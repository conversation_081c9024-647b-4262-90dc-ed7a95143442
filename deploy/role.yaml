apiVersion: v1
kind: Secret
metadata:
  namespace: {{NAMESPACE}}
  name: discovery-token
  annotations:
    kubernetes.io/service-account.name: discovery
type: kubernetes.io/service-account-token
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: {{NAMESPACE}}
  name: discovery
secrets:
  - name: discovery-token
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: {{NAMESPACE}}
  name: discovery-role
rules:
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get","list","watch","patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  namespace: {{NAMESPACE}}
  name: discovery-role-binding
roleRef:
  kind: Role
  name: discovery-role
  apiGroup: rbac.authorization.k8s.io
subjects:
  - kind: ServiceAccount
    name: discovery
    namespace: {{NAMESPACE}}
