server:
  grpc:
    addr: 0.0.0.0:9090
    timeout_seconds: 5
  http:
    addr: 0.0.0.0:8080
    timeout_seconds: 5
data:
  database:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/gold_store?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
  static:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/middle?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
  redis:
    address: testdb-redis.fxeyeinterface.com:6379
    password:
    db: 191
    read_timeout_seconds: 5
    write_timeout_seconds: 5
  ea:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/ea_shop?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
  vps:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/wikifxcloud?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
  vip:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/fxskyeye?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
  report:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/fxskyeye?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
  exhibition:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/expo_ticket?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 0
business:
  feishu_alarm:
    webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/b7145b16-4384-4e99-98e6-1a12d83b2d2e"
  gold_domain: "http://fxeyegold.fxeyeinterface.com"
  oss_domain: "https://img.fx696.com"
  exhibition_domain: "https://expoliveimgs.wikiexpo.com"
  broker_domain: "https://eimgjys.fxeyee.com"
  task_consumer:
    brokers:
      - "************:9092"
    version: "2.8.1"
    auto_commit: false
    offset_oldest: false
  task_producer:
    brokers:
      - "************:9092"
    topic: "dynamic_wiki_gold_store_task"
    version: "2.8.1"
  order_merge:
    ea_consumer:
      brokers:
        - "************:9092"
      version: "2.8.1"
      group_id: "ea.gold_store.order_merge"
      topics:
        - "dynamic_ea_shop_eaorders"
    vps_consumer:
      brokers:
        - "************:9092"
      version: "2.8.1"
      group_id: "vps.gold_store.order_merge"
      topics:
        - "dynamic_wikifxcloud_hostorders"
    vip_consumer:
      brokers:
        - "************:9092"
      version: "2.8.1"
      group_id: "vip.gold_store.order_merge"
      topics:
        - "dynamic_fxskyeye_order"
    report_consumer:
      brokers:
        - "************:9092"
      version: "2.8.1"
      group_id: "report.gold_store.order_merge"
      topics:
        - "dynamic_report_order"
    exhibition_consumer:
      brokers:
        - "************:9092"
      version: "2.8.1"
      group_id: "exhibition.gold_store.order_merge"
      topics:
        - "dynamic_expo_ticket_order"
  notification_rabbit:
    url: "amqp://fxeye:<EMAIL>:5672/MessageCenter"
    exchange: "messagesync"
    routing_key: "WikiFX.LogisticsNotify.Message.Sync"
  virtual_goods:
    vps_domain: "http://cloudvpsapi.fxeyeinterface.com"
    vip_domain: "http://wikiapi.fxeyeinterface.com"
    shadow_domain: "http://shieldcenterapi.fxeyeinterface.com"
    broker_domain: "http://wikibitsearch.fxeyeinterface.com"
    report_domain: "http://bitfxpdfapi.fxeyeinterface.com"
  task_consumer_group:
    post_moment: "dynamic_wikicommunity_posts"
    post_business: "dynamic_wikicommunity_posts"
    comment_post: "dynamic_wikicommunity_reply"
    rate_dealer: "dynamic_wikicommunity_comment_grade"
    join_activity: "dynamic_wikicommunity_posts"
    modify_username: "dynamic_usercenter_user"
    modify_avatar: "dynamic_usercenter_user"
    verify_identity: "dynamic_usercenter_userpassport"
    bind_real_account: "dynamic_wikitrade_business_meta_trader_accounts"
    open_vps: "dynamic_wikifxcloud_hostinfo"
    follow_wikifx: "dynamic_usercenter_user_wikibit_attention"
    view_dealer: "dynamic_wiki_gold_store_task"
    search: "dynamic_wiki_gold_store_task"
  user_center_endpoint: "api-usercenter-rpc.microservice.svc.cluster.local:81"
  express:
    key: "SfEOQeAv1564"
    customer: "6D6260E197AA7DE6365DF983B03EFCAF"
    secret: "4b3d968a9abc44898d438c7912c56cd7"
    base_url: "https://poll.kuaidi100.com"
    callback_url: "https://api-goldstore-test.eeeoeee.com/v1/express/callback"
  ems:
    environment: "production"
    sender_no: "*************"
    authorization: "SPcZVe9REZ6gCHdg"
    secret_key: "U2ZPSGY5VU4weVI2NUtOUw=="
    user_code: ""
    timeout: 30
