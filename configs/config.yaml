server:
  grpc:
    addr: 0.0.0.0:81
    timeout_seconds: 5
  http:
    addr: 0.0.0.0:80
    timeout_seconds: 30
data:
  database:
    driver: mysql
    source: wikifx:Wikifx2023@tcp(testdb-mysql.fxeyeinterface.com:3306)/expo_ticket?charset=utf8mb4&collation=utf8mb4_unicode_ci&parseTime=true&loc=UTC
    level: 4
  redis:
    address: testdb-redis.fxeyeinterface.com:6379
    password:
    db: 191
    read_timeout_seconds: 5
    write_timeout_seconds: 5
business:
  notify_check_spec: "0 * * * * *"
  oss_domain: "https://expoliveimgs.zy223.com"
  survey_domain: "https://eimgreview.souhei.com.cn"
  expo_register_link: "http://apphtml.0067.cc/Mobile/expoSignup"
  upstream:
    trader_domain: "http://wikibitsearch.fxeyeinterface.com"
    contest_domain: "http://tradecontest.fxeyeinterface.com"
    third_domain: "http://thridnew.fxeyeinterface.com"
    wikienterprise_domain: "http://wikienterprise.fxeyeinterface.com"
    google_trans_domain: "http://wikitranslation.fx994.com/google/trans"
  user_center_endpoint: "***********:31699"
  community_endpoint: "***********:32108"
  im_message_endpoint:
    base_url: "http://wikibitchat.fxeyeinterface.com"
  notification_rabbit:
    url: "amqp://fxeye:<EMAIL>:5672/MessageCenter"
    exchange: "messagesync"
    routing_key: "WikiFX.ActivityNotice.Message.Sync"
  tencent_ai:
    secret_id: "AKID6C6rRnvtjNp9LPFsIUUGpALYffXHvU0P"
    secret_key: "TuFXkaIm6FB6qMfyoY5U7btypTvAB0e6"
    region: "ap-guangzhou"
    timeout_sec: 60
    endpoint: "iai.tencentcloudapi.com"
    face_model_version: "3.0"
    search_config:
      max_face_num: 5
      max_person_num: 100
      min_face_size: 80
      default_match_threshold: 70.0
    rate_limit_config:
      user_limit_per_min: 5
      user_limit_per_day: 100
      ip_limit_per_min: 500
      alert_threshold: 80
    cache_config:
      ttl: 300
  email_sender:
    base_url: "http://thridnew.fxeyeinterface.com"
    timeout_sec: 30
  message_email_task:
    enabled: true
    lock_key: "expo:weekly_message_task"
    lock_timeout_minutes: 60
    message_days_range: 7

