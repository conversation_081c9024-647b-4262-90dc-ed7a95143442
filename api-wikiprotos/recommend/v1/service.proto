syntax = "proto3";

package api.recommend.v1;
option go_package = "api/recommend/v1;v1";

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "recommend/v1/models.proto";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "推荐接口";
    version: "1.0";
  };
  schemes: HTTP;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "ApiKeyAuth";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "x-token";
      }
    }
  };
  security: {
    security_requirement: {
      key: "ApiKeyAuth";
      value: {};
    }
  };
  responses: {
    key: "400";
    value: {
      description: "参数错误，检查参数格式跟字段类型是否正确"
      schema: {
        json_schema: {
          type:OBJECT
          ref: ".api.recommend.v1.ErrorReplyCopy"
        }
      }
      examples: {
        key: "application/json"
        value: "{\"code\":400,\"reason\":\"INVALID_SIGN\",\"message\":\"invalid args\",\"time\":**********}"
      }
    }
  };
  responses: {
    key: "404";
    value: {
      description: "资源不存在";
      schema: {
        json_schema: {
          type:OBJECT
          ref: ".api.recommend.v1.ErrorReplyCopy"
        }
      }
      examples: {
        key: "application/json"
        value: "{\"code\":404,\"reason\":\"RESOURCE_NOT_FOUND\",\"message\":\"resource not found\",\"time\":**********}"
      }
    }
  };
  responses: {
    key: "500";
    value: {
      description: "服务内部错误";
      schema: {
        json_schema: {
          type:OBJECT
          ref: ".api.recommend.v1.ErrorReplyCopy"
        }
      }
      examples: {
        key: "application/json"
        value: "{\"code\":500,\"reason\":\"INTERNAL_SERVER_ERROR\",\"message\":\"internal server error\",\"time\":**********}"
      }
    }
  };
};

service Service {
  // 健康检查
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // 推荐
  rpc Recommend(RecommendRequest) returns (RecommendReply) {
    option (google.api.http) = {get: "/v1/recommend"};
  }
  // 推荐v2版本
  rpc RecommendV2(RecommendV2Request) returns (RecommendReply) {
    option (google.api.http) = {get: "/v2/recommend"};
  }
  // 商业按照分类获取推荐
  rpc FindCommerceByCategory(FindCommerceByCategoryRequest) returns (FindCommerceByCategoryReply) {
    option (google.api.http) = {get: "/v1/commerce"};
  }
  // 热门和最新
  rpc FindHotAndNew(FindHotAndNewRequest) returns (FindHotAndNewReply) {
    option (google.api.http) = {get: "/v1/release_type/{release_type}/category/{category_id}"};
  }
  // 热门和最新
  rpc FindHotAndNewV2(FindHotAndNewRequest) returns (FindHotAndNewV2Reply) {
    option (google.api.http) = {get: "/v2/release_type/{release_type}/category/{category_id}"};
  }
  // 关注对象内容列表
  rpc FindFollowPublish(FindFollowPublishRequest) returns (FindFollowPublishReply) {
    option (google.api.http) = {post: "/v1/follow/publish" body:"*"};
  }
  // 交易商主页内容
  rpc TraderHome(TraderHomeRequest) returns (TraderHomeReply) {
    option (google.api.http) = {post: "/v1/trader/home" body:"*"};
  }
  // 交易商帖子总数量
  rpc TraderPostCount(TraderPostCountRequest) returns (TraderPostCountReply) {
    option (google.api.http) = {post: "/v1/trader/post/count" body:"*"};
  }
  // 内容搜索接口
  rpc Search(SearchRequest) returns (SearchReply) {
    option (google.api.http) = {post: "/v1/search" body:"*"};
  }
  // 查找用户搜索标题内容
  rpc FindSearchTitle(FindSearchTitleRequest) returns (FindSearchTitleReply) {
    option (google.api.http) = {get: "/v1/search_title"};
  }
  // 感兴趣的用户
  rpc InterestedUser(InterestedUserRequest) returns (InterestedUserReply) {
    option (google.api.http) = {get: "/v1/user/interested"};
  }
  // 热门内容
  rpc FindHotContent(FindHotContentRequest) returns (FindHotContentReply) {
    option (google.api.http) = {get: "/v1/content/hot"};
  }
  // 推荐用户
  rpc RecommendUser(RecommendUserRequest) returns (RecommendUserReply) {
    option (google.api.http) = {get: "/v1/user/recommend"};
  }
  // 获取排行
  rpc SkyLineActivity(ActivityRequest) returns (ActivityReply) {
    option (google.api.http) = {get: "/v1/activity/rank"};
  }
  // 年度报告
  rpc YearlyReport(YearlyReportRequest) returns (YearlyReportReply) {
    option (google.api.http) = {get: "/v1/yearly_report"};
  }
  // 推荐用户反馈
  rpc RecommendFeedback(RecommendFeedbackRequest) returns (RecommendFeedbackResponse) {
    option (google.api.http) = {post: "/v1/recommend/feedback" body:"*"};
  }
  // 热帖排行
  rpc HotContentRanking(HotContentRankingRequest) returns (HotContentRankingResponse) {
    option (google.api.http) = {get: "/v1/content/hot/ranking"};
  }
  // 排行榜范围
  rpc RankingScope(RankingScopeRequest) returns (RankingScopeResponse) {
    option (google.api.http) = {get: "/v1/ranking/scope"};
  }
  // 用户排行
  rpc CreatorRanking(CreatorRankingRequest) returns (CreatorRankingResponse) {
    option (google.api.http) = {get: "/v1/ranking/creator"};
  }
  // 用户排行通知
  rpc CreatorRankingNotice(CreatorRankingNoticeRequest) returns (CreatorRankingNoticeResponse) {
    option (google.api.http) = {get: "/v1/ranking/creator/notice"};
  }
  // 文章标题搜索自动补全
  rpc SearchTitleAutoComplete(SearchTitleAutoCompleteRequest) returns (SearchTitleAutoCompleteReply) {
    option (google.api.http) = {get: "/v1/search_title/auto_complete"};
  }
  // ------------------------- 下面接口内部自测使用 -----------------------------------------
  rpc RecommendTest(RecommendTestRequest) returns (RecommendTestReply) {
    option (google.api.http) = {get: "/v1/recommend/test"};
  }
  rpc ContentSimilarity(ContentSimilarityRequest) returns (ContentSimilarityReply) {
    option (google.api.http) = {get: "/v1/content_similarity"};
  }
  rpc UserContent(UserContentRequest) returns (RecommendTestReply) {
    option (google.api.http) = {post: "/v1/user/content" body:"*"};
  }
  rpc UserBehavior(UserBehaviorRequest) returns (UserBehaviorReply) {
    option (google.api.http) = {get: "/v1/user/behavior"};
  }
  rpc UserOriginBehavior(UserOriginBehaviorRequest) returns (UserOriginBehaviorReply) {
    option (google.api.http) = {get: "/v1/user/behavior/origin"};
  }
  rpc UserVector(UserVectorRequest) returns (UserVectorReply){
    option (google.api.http) = {get: "/v1/user/vector"};
  }
  rpc UserRealVector(UserVectorRequest) returns (UserVectorReply){
    option (google.api.http) = {get: "/v1/user/vector/real"};
  }
  rpc ResetUserBehavior(ResetUserBehaviorRequest) returns (ResetUserBehaviorReply) {
    option (google.api.http) = {delete: "/v1/user/behavior"};
  }
}

//------
message ErrorReplyCopy{
  int32 code = 1;
  string reason = 2;
  string message = 3;
  int64 time = 4;
}

