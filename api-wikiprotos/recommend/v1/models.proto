syntax = "proto3";

package api.recommend.v1;
option go_package = "api/recommend/v1;v1";

import "protoc-gen-openapiv2/options/annotations.proto";

enum ReleaseType{
  ReleaseTypeUnknown = 0;
  Commerce = 1; // 商业
  Dynamic = 2; // 动态
}

message RecommendRequest {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"},deprecated=true];
  string device_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "设备ID"},deprecated=true];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小,默认20",default: "20"}];
  int64 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数",default: "1"}];
  string type = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐类型:默认所有类型:article:资讯;exposure:曝光;discover:发现;trader:交易商;survey:实勘"},deprecated=true];
  string language_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"},deprecated=true];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"},deprecated=true];
  string project = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "项目:wikifx;wikibit;wikitrade;wikistock"},deprecated=true];
  ReleaseType release_type = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发现页类型：1:商业;2:动态"}];
  string preferred_languages = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏好语言,多个语言之间用逗号分隔"},deprecated=true];
  int64 first_show_page = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "首页显示数据偏移"}];
  UserIdentify user_identify = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份认证"}];
}
enum RecommendCategory {
  RecommendCategoryRecommend = 0; // 全部内容推荐
  RecommendCategoryCommerce = 1;  // 商业
  RecommendCategoryDynamic = 2; // 动态
  RecommendCategoryDiscover = 3; // 发现全部(商业+动态)
}
enum RecommendPosition {
  RecommendPositionDiscoverPage = 0; // 发现页
  RecommendPositionHomePage = 1; // 首页
}
enum UserIdentify {
  UserIdentifyInvestor = 0; // 投资者
  UserIdentifyKOL = 1; // KOL
  UserIdentifyTrader = 2; // 交易商
  UserIdentifyServiceProvider = 3; // 服务商
}
message RecommendV2Request {
  int64 size = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数据量"}];
  int64 page = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页"}];
  RecommendCategory category = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分类：0：推荐；1：商业；2：动态"}];
  int64 first_show_page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "首页显示数据偏移"}];
  RecommendPosition position = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐位置：0：首页；1：发现页"}];
  UserIdentify user_identify = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户身份认证"}];
  int64 pre_load = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否预加载请求:0：非预加载;1:预加载"}];
}
// 推荐类型
enum RecommendItemType {
  RecommendItemTypeUnknown = 0; // 未知类型
  RecommendItemTypeArticle = 1; // 文章
  RecommendItemTypeExposure = 2; // 曝光
  RecommendItemTypeDiscover = 3; // 发现
  RecommendItemTypeTrader = 4; // 交易商
  RecommendItemTypeSurvey = 5; // 实勘
  RecommendItemTypeMediate = 6; // 调解
  RecommendItemTypeFlash = 7; // 快讯
  RecommendItemTypeDisclosure = 8; // 披露
  RecommendItemTypeComment = 9; // 评价
  RecommendItemTypeService = 10; // 服务商
  RecommendItemTypeAdvertise = 11; // 广告
}
// 广告跳转类型
enum AdvertiseJumpType {
  AdvertiseJumpTypeUnknown = 0;
  AdvertiseJumpTypeTradeDetail = 1; // 交易商详情
  AdvertiseJumpTypeOuterLink = 2; // 外部链接
  AdvertiseJumpTypeNewsDetail = 3; // 新闻文章详情
  AdvertiseJumpTypeSurveyDetail = 4; // 实勘详情
  AdvertiseJumpTypeVPS = 6; // VPS
  AdvertiseJumpTypeLiveDetail = 10; // 直播详情
  AdvertiseJumpTypeLiveList = 11; // 直播列表
  AdvertiseJumpTypeCommercial = 13; // 商业
  AdvertiseJumpTypePersonalHomepage = 14; // 个人主页
  AdvertiseJumpTypeServiceProvider = 15; // 服务商详情页
  AdvertiseJumpTypeRank = 16; // 排行榜
  AdvertiseJumpTypeH5 = 17; // H5
  AdvertiseJumpTypeTopic = 55; // 话题
}
// 广告商业-帖子类型
enum AdvertisePostType {
  AdvertisePostTypeNone = 0; // 无
  AdvertisePostTypeArticle = 1; // 文章
  AdvertisePostTypeExposure = 2; // 曝光
  AdvertisePostTypeDiscover = 3; // 发现
  AdvertisePostTypeTrader = 4; // 交易商
  AdvertisePostTypeSurvey = 5; // 实勘
  AdvertisePostTypeMediate = 6; // 调解
  AdvertisePostTypeFlash = 7; // 快讯
  AdvertisePostTypeDisclosure = 8; // 披露
  AdvertisePostTypeComment = 9; // 评价
}
// 广告标签类型
enum AdvertiseTagType {
  AdvertiseTypeUnknown = 0;
  AdvertiseTypeNone = 1; // 不显示
  AdvertiseTypeTop = 2; // 置顶
  AdvertiseTypeRecommended = 3; // 推荐
  AdvertiseTypeHot = 4; // 热门
  AdvertiseTypeAd = 5; // 浏览
}
message AdvertiseItem {
  string title = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  string slogan = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "副标题"}];
  string image = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  string url = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "跳转地址"}];
  string code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象code"}];
  string country_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  string language_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"}];
  string app_jump_sub_type = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "app跳转类型"}];
  int64 width = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片宽"}];
  int64 height = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片高"}];
  string hits_info = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "hits_info"}];
}
message Advertise {
  AdvertiseJumpType jump_type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "0:非法值;1:交易商详情;2:外部链接;3:新闻文章详情;4:实勘详情;6:VPS;10:直播详情;11:直播列表;13:商业;14:个人主页;15:服务商详情页;16:排行榜;17:H5;55:话题"}];
  AdvertiseTagType tag_type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "1:不显示;2:置顶;3:推荐;4:热门;5:浏览"}];
  string tag = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  string title = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  string slogan = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "slogan"}];
  repeated AdvertiseItem items = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "广告项列表"}];
  int64 order = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "广告位置"}];
  AdvertisePostType post_type = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商业-帖子类型"}];
  string id = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "广告ID"}];
  string app_jump_type = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "app跳转类型"}];
}
message RecommendItemLabel {
  string background_color = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字体背景色"}];
  string text = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "字体内容"}];
}
message RecommendItem {
  string type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐类型:默认所有类型:article:资讯(子类型看sub_type);exposure:曝光;discover:发现;trader:交易商;survey:实勘;mediation:调解;news_flash:快讯;regdisclosure:披露;comment:评价;advertise:广告"}];
  string id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
  string trader_code = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  string user_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string from = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据来源，debug使用业务不用关心"}];
  string sub_type = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    title: "推荐类型子类型:article:(zx_jys:交易商新闻;zx_jyshuiping:交易商汇评;zx_jyshuodong:交易商活动;zx_baoguang:曝光新闻;zx_yaowen:行业新闻;ty_yaowen:天眼新闻;provider_news:服务商新闻);discover(commerce:商业;dynamic:动态)"}];
  RecommendItemType item_type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子类型：1:文章;2:曝光;3:商业;4:交易商;5:实勘;6:调解;7:快讯;8:披露;9:评价;10:服务商;11:广告"}];
  Advertise advertise = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "广告数据"}];
  repeated RecommendItemLabel labels = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签"}];
  bool not_show_feedback = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不显示推荐反馈"}];
}
message RecommendReply {
  repeated RecommendItem items = 1;
  int64 total = 2;
  int64 first_show_page = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "首页显示数据偏移"}];
}

message FindCommerceByCategoryRequest {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"},deprecated=true];
  string device_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "设备ID"},deprecated=true];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小,默认20",default: "20"}];
  int64 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数",default: "1"}];
  string language_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"},deprecated=true];
  string country_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"},deprecated=true];
  string project = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "项目:wikifx;wikibit;wikitrade;wikistock"},deprecated=true];
  string preferred_languages = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏好语言,多个语言之间用逗号分隔"},deprecated=true];
  string category_id = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分类ID"}];
  int64 first_show_page = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "首页显示数据偏移"}];
}
message CommerceItem {
  string id = 1;
  RecommendItemType type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型"}];
  Advertise advertise = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "广告数据"}];
}
message FindCommerceByCategoryReply {
  repeated CommerceItem items = 1;
  int64 total = 2;
  int64 first_show_page = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "首页显示数据偏移"}];
}

enum YearlyReportUserTag {
  YearlyReportUserTagUnknown = 0;
  SafetyExerciser = 1; // 安全演练家
  ForeignExchangeEncyclopedia = 2; // 外汇小百科
  CommunityUpFlow = 3; // 社区顶流
  SuperhumanRightsDefender = 4; // 维权超人
  NewsSavvy = 5; // 新闻小灵通
  TruthKeeper = 6; // 真相守护者
  SearchWordSurfer = 7; // 搜词冲浪者
  GoodEatingMelons = 8; // 吃瓜小能手
  ChanceWatchers = 9; // 机会守望者
  SecurityTransactionAmbassador = 10; // 安全交易大使
  SorosOfForex = 11; // 外汇界索罗斯
  WarrenBuffett = 12; // 投资界巴菲特
  ShelteredFlipper = 13; // 被Flipper庇护者
  MostFans = 14; // 粉丝收割机
}

message Quote {
  string image = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  string symbol = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "品类"}];
  float rate = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "上涨下跌率"}];
  float max_price = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高价格"}];
  int64 max_price_timestamp = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高价格的时间"}];
  float min_price = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低价格"}];
  int64 min_price_timestamp = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低价格的时间"}];
}
message YearlyReportRequest {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
}
message YearlyReportGlobal {
  int64 new_trader = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的交易商数量"}];
  int64 new_service_provider = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的服务商数量"}];
  int64 new_license = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的牌照数量"}];
  int64 new_survey = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的实勘数量"}];
  int64 new_article = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增新闻/资讯数量"}];
  int64 new_exposure = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增曝光数量"}];
  int64 new_disclosure = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增披露数量"}];
  int64 new_disclosure_rel_trader = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的披露涉及的交易商数量"}];
  int64 mediate_person = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "调解人数"}];
  float mediate_amount = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "调解解决金额"}];
  int64 quote_count = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "外汇行情数量"}];
  int64 up_quote_count = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "上涨外汇行情数量"}];
  int64 fall_quote_count = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下跌外汇行情数量"}];
  repeated Quote quotes = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行情数量列表"}];
  string interested_search_keyword_user_text = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "全员搜索词用户量"}];
  repeated string keywords = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索词列表"}];
  int64 view_posts_user_count = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看帖子的用户数"}];
  int64 action_user_count = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有过交互行为的用户数"}];
  repeated Quote yearly_inc_top2 = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易品种年涨top2"}];
  Quote yearly_desc_top1 = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易品种年跌top1"}];
  Quote yearly_durable_inc_top1 = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易品种连涨top1"}];
  string community_user_text = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "社区用户总量"}];
}
message YearlyReportUserObject {
  string code = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "code"}];
  int64 view_count = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览次数"}];
}
message YearlyReportUser {
  int64 register_timestamp = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册日期；2024-11-24"}];
  int64 register_days = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册天数"}];
  repeated YearlyReportUserObject view_trader_list = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览交易商列表"}];
  repeated YearlyReportUserObject view_service_provider_list = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览服务商列表"}];
  int64 use_search_count = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用搜索功能次数"}];
  string most_used_search_keyword = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "常用搜索词"}];
  repeated string interested_keywords = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索词列表"}];
  int64 view_article_count = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看过的文章数量"}];
  repeated string interested_area_codes = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的区域code"}];
  int64 posts_count = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发帖数量"}];
  int64 view_posts_count = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览帖子数量"}];
  int64 view_exposure_count = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览的曝光数量"}];
  int64 mediate_count = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发起调解次数"}];
  float mediate_amount = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "调解总金额"}];
  Quote interested_quote = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的外汇行情"}];
  int64 interested_quote_view_count = 16[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的外汇行情查看次数"}];
  int64 mock_trader_count = 17[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易次数"}];
  float mock_trade_amount = 18[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易金额"}];
  float mock_trade_revenue = 19[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易全年营收"}];
  Quote mock_trade_most_quote = 20[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易最多的品种"}];
  string first_post_id = 21[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子ID"}];
  int64 first_post_timestamp = 22[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子发布时间"}];
  int64 first_post_action_count = 23[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子分享，点赞，评论的总数"}];
  repeated YearlyReportUserObject first_post_view_user_list = 24[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子查看用户列表"}];
  YearlyReportUserTag tag = 25[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户最终称号"}];
  int64 view_trader_count = 26[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看交易商数量"}];
  int64 click_trade_count = 27[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "点击交易商次数"}];
  int64 view_service_provider_count  = 28[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看服务商数量"}];
  int64 click_service_provider_count = 29[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "点击交易商数量"}];
}
message YearlyReportReply {
  YearlyReportGlobal global = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "全局信息"}];
  YearlyReportUser user = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户信息"}];
}

message FindHotAndNewRequest {
  ReleaseType release_type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "类型：1：商业；2：动态" required: "true"}];
  string category_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "热门和最新：002:热门;003:最新" required: "true"}];
  int64 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据页"}];
  int64 size = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数据量"}];
  int64 start_timestamp = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间戳：在刷新时固定好一个时间戳，在加载更多时使用这个时间"}];
}
message HotAndNewItem {
  string id = 1;
}
message FindHotAndNewReply {
  repeated HotAndNewItem items = 1;
  int64 total = 2;
}

message HotAndNewV2Item {
  string id = 1;
  RecommendItemType type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最新和热门的类型，v2接口才会返回该字段"}];
  string trader_code = 3;
  string user_id = 4;
  string sub_type = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    title: "推荐类型子类型:article:(zx_jys:交易商新闻;zx_jyshuiping:交易商汇评;zx_jyshuodong:交易商活动;zx_baoguang:曝光新闻;zx_yaowen:行业新闻;ty_yaowen:天眼新闻;provider_news:服务商新闻);discover(commerce:商业;dynamic:动态)"}];
  int64 create_time = 6;
  Advertise advertise = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "广告数据"}];
}
message FindHotAndNewV2Reply {
  repeated HotAndNewV2Item items = 1;
  int64 total = 2;
}

// 官方号类型
enum OfficialNumberType {
  OfficialNumber_Unknown=0; //未知
  Trader = 1; // 交易商号
  WikiFXMediate = 2; // 天眼调解
  WikiFXNews = 3; // WikiFX-新闻
  WikiFXExpress = 4; // WikiFX-快讯
  WikiFXSurvey = 5; // WikiFX-实勘
  ServiceProvider = 6; // 服务商号
  Regulator = 7; // 监管机构号
  User = 8; // 用户号
  WikiFXActivity = 9; // WikiFX-活动
  LemonX = 10; // LemonX官方号
  Expo = 11; // WikiExpo官方号文章
  WikiFx = 12; // WikiFx官方号
  WikiFxEducation = 13; // WikiFxEducation官方号
  WikiFxElitesClub = 14; // WikiFxElitesClub官方号
  WikiFxSkylineGuide=15; // WikiFxSkylineGuide官方号
}

message FollowItem {
  OfficialNumberType type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "账号类型：1：交易商号；2：天眼调解；3：wikifx新闻；4：wikifx-快讯;5：wikifx-实勘；6：服务商号；7：监管机构号；8：用户号"}];
  repeated string ids = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID或者code"}];
}
message FindFollowPublishRequest {
  repeated FollowItem follows = 1;
  int64 page = 2;
  int64 size = 3;
}
message FollowPublishItem {
  RecommendItemType type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子类型：1:文章(有子类型);2:曝光;3:商业;4:交易商;5:实勘;6:调解;7:快讯;8:披露;9:评价;10:服务商;11:广告"}];
  string id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
  string trader_code = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  string user_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string sub_type = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
    title: "推荐类型子类型:article:(zx_jys:交易商新闻;zx_jyshuiping:交易商汇评;zx_jyshuodong:交易商活动;zx_baoguang:曝光新闻;zx_yaowen:行业新闻;ty_yaowen:天眼新闻;provider_news:服务商新闻);discover(commerce:商业;dynamic:动态)"}];
  string language_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code，小写"}];
}
message FindFollowPublishReply {
 repeated FollowPublishItem items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容列表"}];
}

message TraderHomeRequest {
  string code = 1; // code
  ReleaseType release_type = 3; // 商业或者动态
  int64 page = 4; // 页数
  int64 size = 5; // 每页数据大小
  bool is_service = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否是服务商（该字段废弃）"},deprecated=true];
  OfficialNumberType official_number_type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "官方号类型"}];
}
message TraderHomeReply {
  repeated FollowPublishItem items = 1;
}

message TraderPostCountRequest {
  string code = 1; // code
  bool is_service = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否是服务商（该字段废弃）"},deprecated=true];
  OfficialNumberType official_number_type = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "官方号类型"}];
}
message TraderPostCountReply {
  int64 commerce_total = 1; // 商业总数量
  int64 dynamic_total = 2; // 动态总数量
}

message SearchRequest {
  string content = 1; // 搜索内容
  int64 page = 2; // 页数
  int64 size = 3; // 每页数据大小
}
message SearchItem {
  RecommendItemType type = 1; // 搜索结果类型
  string id = 2; // 对象ID
  string trader_code = 3; // 交易商
  string user_id = 4; // 用户ID
  string sub_type = 6; // 子类型:article:(zx_jys:交易商新闻;zx_jyshuiping:交易商汇评;zx_jyshuodong:交易商活动;zx_baoguang:曝光新闻;zx_yaowen:行业新闻;ty_yaowen:天眼新闻;provider_news:服务商新闻);discover(commerce:商业;dynamic:动态)
  string title = 7; // 测试使用
}
message SearchReply {
  repeated SearchItem items = 1;
}

message FindSearchTitleRequest {
  string content = 1; // 搜索内容
  int64 size = 3; // 每页数据大小
}
message SearchTitleItem {
  string title = 1; // 标题
  string highlight = 2; // 高亮标题
}
message FindSearchTitleReply {
  repeated SearchTitleItem items = 1;
}

message InterestedUserRequest {
  repeated string follow_users = 1;
  int64 size = 2;
}
message InterestedUserReply {
  repeated string users = 1;
}

message FindHotContentRequest {}
message FindHotContentReply {
  repeated RecommendItem items = 1;
}

message RecommendUserRequest {
  int64 size = 1;
}
message RecommendUserReply {
  repeated string users = 1;
}

message ActivityRequest {
  string activity = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "活动分类"}];
  string rank = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排行分类"}];
}
message ActivityItem {
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  int64 rank = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户排名"}];
  int64 score = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "人气值"}];
  string home_url =4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户主页URL"}];
  int64 week_score = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "周人气值"}];
}
message ActivityReply {
  repeated ActivityItem items = 1;
}

enum RecommendFeedbackCategory {
  RecommendFeedbackCategoryContent = 0 ; // 不喜欢内容
  RecommendFeedbackCategoryAuthor = 1; // 不喜欢作者
}
message RecommendFeedbackRequest {
  string item_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容ID"}];
  RecommendItemType item_type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容类型:0:未知类型;1:文章;2:曝光:3:发现;4:交易商;5:实勘;6:调解;7:快讯;8:披露;9:评价;10:服务商:11:广告"}];
  string item_user_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "内容用户ID"}];
  RecommendFeedbackCategory category = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不喜欢分类"}];
}
message RecommendFeedbackResponse {}

message HotContentRankingRequest {
  int64 size = 1;
}
enum HotContentLabel {
  HotContentLabelNone = 0; // 无标签
  HotContentLabelNew = 1; // 新上榜
  HotContentLabelHot = 2; // 热
  HotContentLabelUnique = 3; // 独家
}
message HotContentRankingItem {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子ID"}];
  RecommendItemType type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子类型"}];
  int64 score = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子分数"}];
  HotContentLabel label = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子标签"}];
  string user_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发帖用户ID"}];
}
message HotContentRankingResponse {
  repeated HotContentRankingItem items = 1;
}

message RankingScopeRequest {}
message RankingScopeResponse {
  repeated string week = 1; // 周排行列表
  string month_start = 2; // 月排行开始时间
  string month_end = 3; // 月排行结束时间
  string week_start = 4;
  string week_end = 5;
}

enum CreatorRankingCategory {
  CreatorRankingCategoryWeek = 0; // 周榜
  CreatorRankingCategoryMonth = 1; // 月榜
}

message CreatorRankingRequest {
  CreatorRankingCategory category = 1;
  int64 size = 2;
  string id = 3;
}
message CreatorRankingItem {
  string user_id = 1;// 用户ID
  int64 score = 2; // 分数
}
message CreatorRankingResponse {
  repeated CreatorRankingItem items = 1;
  int64 user_rank = 2; // 用户所在排名
  string state = 3; // 第几期
  int64 user_score = 4; // 用户分数
  string id = 5; // 榜单ID
}

message CreatorRankingNoticeRequest {
  CreatorRankingCategory category = 1;
  string id = 2;
}
message CreatorRankingNoticeResponse {}
// ------------------------- 下面接口内部测试使用 -----------------------------------------
message UserContentRequest {
  repeated string user_ids = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小,默认20",default: "20"}];
  int64 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数",default: "1"}];
  string type = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐类型:默认所有类型:article:资讯;exposure:曝光;discover:发现;trader:交易商;survey:实勘"}];
  string language_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  string project = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "项目:wikifx;wikibit;wikitrade;wikistock"}];
  ReleaseType release_type = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发现页类型：1:商业;2:动态"}];
  string scale = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "scale"}];
  string offset = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "offset"}];
  float decay = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "decay"}];
}

message RecommendTestRequest{
  string user_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string device_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "设备ID"}];
  int64 size = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小,默认20",default: "20"}];
  int64 page = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数",default: "1"}];
  string language_code = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"}];
  string country_code = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
  bool real_time = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否只基于实时行为"}];
  string scale = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "scale"}];
  string offset = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "offset"}];
  float decay = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "decay"}];
  string preferred_languages = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "偏好语言,多个语言之间用逗号分隔"}];
  RecommendCategory category = 15[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐分类：0：全推；1：商业；2：动态"}];
}
message RecommendTestItem {
  string type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐类型:默认所有类型:article:资讯;exposure:曝光;discover:发现;trader:交易商;survey:实勘"}];
  string id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
  string language_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"}];
  float score = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分数"}];
  string user_id = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所属用户"}];
  string create_time = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
  string trader_code = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "所属交易商"}];
  repeated float rank_params = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序参数"}];
  string from = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "数据来源：content:内容相似性;hot:热度;relationship:好友关系"}];
  repeated float rank_shp_value = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序出参"}];
  int64 duration_days = 15;
}
message RecommendTestReply {
  repeated RecommendTestItem items = 1;
}

message ContentSimilarityRequest {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
  string id_type = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象类型"}];
  string target_type = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "目标类型"}];
  string language_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言code"}];
  int64 size = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数据大小,默认20",default: "20"}];
  int64 page = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "分页数",default: "1"}];
  string project = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "项目:wikifx;wikibit;wikitrade;wikistock"}];
  string scale = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "scale"}];
  string offset = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "offset"}];
  float decay = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "decay"}];
  string country_code = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code"}];
}
message ContentSimilarityReply {
  repeated RecommendTestItem items = 1;
}

message UserBehaviorRequest {
  string user_id = 1;
  string device_id = 2;
}
message UserBehaviorItem {
  string event_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件ID"}];
  string event = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件类型"}];
  string event_time = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件时间"}];
  string object_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
  string language_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];;
}
message UserBehaviorReply {
  repeated UserBehaviorItem items = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户行为列表"}];;
}

message UserOriginBehaviorRequest {
  string user_id = 1;
  string device_id = 2;
  int64 size = 3;
  int64 page = 4;
}
message UserOriginBehaviorItem {
  string event_name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件类型"}];
  string event_time = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件时间"}];
  string object_id = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
  string language_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];;
}
message UserOriginBehaviorReply {
  repeated UserOriginBehaviorItem items = 1;
}

message UserVectorRequest {
  string user_id = 1;
  string device_id = 2;
}
message UserVectorItem {
  string type = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象类型"}];
  string object_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "对象ID"}];
}
message UserVectorReply {
  string updated_at = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最后一次更新时间"}];
  repeated UserVectorItem items = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户向量列表"}];
}

message ResetUserBehaviorRequest {
  string user_id = 1;
  string device_id = 2;
}
message ResetUserBehaviorReply{}

message SearchTitleAutoCompleteRequest {
  string keyword = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索关键字"}];
  int64 size = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "获取数量"}];
}
message SearchTitleAutoCompleteItem {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
  string title = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标题"}];
  string highlight = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "高亮标题"}];
  RecommendItemType item_type = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子类型：1:文章;2:曝光;3:商业;4:交易商;5:实勘;6:调解;7:快讯;8:披露;9:评价;10:服务商;11:广告"}];
  string user_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string lang = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "语言"}];
  string art_category_id = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "文章分类ID"}];
  string aff_enterprise_code = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "机构代码"}];
}
message SearchTitleAutoCompleteReply {
  repeated SearchTitleAutoCompleteItem items = 2;
}