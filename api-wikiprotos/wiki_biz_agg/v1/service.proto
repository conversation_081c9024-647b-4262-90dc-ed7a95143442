syntax="proto3";

package api.wiki_biz_agg.v1;
option go_package ="api/wiki_biz_agg/v1;v1";

import "protoc-gen-openapiv2/options/annotations.proto";
import "google/api/annotations.proto";
import "common/common.proto";
import "wiki_biz_agg/v1/models.proto";

service Service {
  // 健康检查
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation)={summary:"健康检查",tags:["健康检查"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  }


  //==============================================搜索（search）===========================================================//
  // 搜索智能联想
  rpc GetSearchIntellisenseV2(GetSearchIntellisenseRequestV2) returns (GetSearchIntellisenseReplyV2){
    option (google.api.http) = {get: "/v1/search/intellisense2"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "搜索智能联想",tags: ["搜索"],

      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };

  // 综合搜索
  rpc GetCompoundSearchV2(GetCompoundSearchRequestV2) returns (GetCompoundSearchReplyV2){
    option (google.api.http) = {post: "/v1/search2", body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "综合搜索",tags: ["搜索"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };
  // 综合搜索
  rpc GetCompoundSearch(GetCompoundSearchRequest) returns (GetCompoundSearchReply){
    option (google.api.http) = {get: "/v1/search"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "综合搜索",tags: ["搜索"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };

  // LemonX 内容智能提示
  rpc GetLemonXIntellisense(GetLemonXIntellisenseRequest) returns (GetLemonXIntellisenseReply){
    option (google.api.http) = {get: "/v1/search/lemonx/intellisense"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "智能提示",tags: ["LemonX"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };
  // LemonX 搜索
  rpc GetLemonXCompoundSearch(GetLemonXCompoundSearchRequest) returns (GetLemonXCompoundSearchReply){
    option (google.api.http) = {get: "/v1/search/lemonx/search"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "综合搜索",tags: ["LemonX"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };
  // LemonX 搜索发现
  rpc GetLemonXSearchDiscover(GetLemonXSearchDiscoverRequest) returns (GetLemonXSearchDiscoverReply){
    option (google.api.http) = {get: "/v1/search/lemonx/discover"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "搜索发现",tags: ["LemonX"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };
  // LemonX 热门内容
  rpc GetLemonXSearchHotContent(GetLemonXSearchHotContentRequest) returns (GetLemonXSearchHotContentReply){
    option (google.api.http) = {get: "/v1/search/lemonx/hot"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "热门内容",tags: ["LemonX"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };
  // LemonX 推荐用户
  rpc GetLemonXSearchRecommendUser(GetLemonXSearchRecommendUserRequest) returns (GetLemonXSearchRecommendUserReply){
    option (google.api.http) = {get: "/v1/search/lemonx/recommend"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "推荐用户",tags: ["LemonX"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };

  // 用户&官方号搜索
  rpc GetBackEndSearch(GetBackEndSearchRequest) returns (GetBackEndSearchReply){
    option (google.api.http) = {get: "/v1/search/backend/search"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户&官方号搜索",tags: ["搜索"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
            required:false
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
            required:false
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
            required:false
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:false
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:false
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
            required:false
          }
        ]
      };
    };
  };

  //==============================================社区（community）======================================================//
  // 获取交易商、服务商或个人页数量
  rpc GetCommunityUserHomeCount(GetCommunityUserHomeCountRequest) returns (GetCommunityUserHomeCountReply){
    option (google.api.http) = {get: "/v1/community/posts/count"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "详情页帖子总数",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 获取帖子发布人信息
  rpc GetCommunityPostsUser(GetCommunityPostsUserRequest) returns (UserData) {
    option (google.api.http) = {get: "/v1/community/posts/user"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "帖子发布人信息",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 收藏列表
  rpc GetCommunityCollectList(GetCommunityCollectListRequest) returns (GetCommunityCollectListReply) {
    option (google.api.http) = {get: "/v1/community/collection/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "收藏列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 推荐栏目
  rpc GetCommunityRecommendList(GetCommunityRecommendListRequest) returns (GetCommunityListReply) {
    option (google.api.http) = {get: "/v1/community/recommend/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "推荐列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 用户主页帖子列表
  rpc GetUserHomeCommunity(GetUserHomeCommunityRequest) returns (GetCommunityListReply){
    option (google.api.http) = {get: "/v1/community/user/home"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户主页列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  };
  // 商业列表
  rpc GetCommunityBusinessList(CommunityListRequest) returns (GetCommunityListReply){
    option (google.api.http) = {get: "/v1/community/business/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取商业列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 动态列表
  rpc GetCommunityDynamicList(CommunityListRequest) returns (GetCommunityListReply){
    option (google.api.http) = {get: "/v1/community/dynamic/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取动态列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 用户关注列表
  rpc GetUserFollowList(GetUserFollowListRequest) returns (GetUserFollowListReply) {
    option (google.api.http) = {get: "/v1/community/follow/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取关注列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 用户浏览列表
  rpc GetUserBrowseList(GetCommunityCollectListRequest) returns (GetCommunityCollectListReply) {
    option (google.api.http) = {get: "/v1/community/browse/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户浏览列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 推荐-新闻栏目
  rpc GetCommunityRecommendNewsList(GetCommunityRecommendNewsListRequest) returns (GetCommunityListReply) {
    option (google.api.http) = {get: "/v1/community/recommend/news"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "推荐-原创&交易商新闻列表",tags: ["推荐-原创&交易商新闻"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 话题搜索
  rpc GetCommunityTopicSearch(GetCommunitySearchRequest) returns (GetCommunityTopicSearchReply) {
    option (google.api.http) = {get: "/v1/community/topic/search"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "话题搜索",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  }
  // 用户搜索
  rpc GetCommunityUserSearch(GetCommunitySearchRequest) returns (GetCommunityUserSearchReply) {
    option (google.api.http) = {get: "/v1/community/user/search"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户搜索",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  }

  // 获取推荐话题
  rpc GetTopicRecommend(common.EmptyRequest) returns (GetTopicRecommendReply) {
    option (google.api.http) = {get: "/v1/community/topic/recommend"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取推荐话题",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 话题详情
  rpc GetTopicDetail(GetTopicDetailRequest) returns (GetTopicDetailReply) {
    option (google.api.http) = {post: "/v1/community/topic/detail",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "话题详情",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //话题收藏列表
  rpc GetCommunityTopicCollectList(GetCommunityTopicCollectListRequest) returns (GetCommunityTopicCollectListReply){
    option (google.api.http) = {get: "/v1/community/collection/topics"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "话题收藏列表",tags: ["话题"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  //===========================年度报告=====================================
  // 年度报告
  rpc GetAnnualReport(common.EmptyRequest) returns (YearlyReportReply){
    option (google.api.http) = {get: "/v1/annualreport/info"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取年度报告",tags: ["年度报告"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 年度报告结果
  rpc GetAnnualReportResult(common.EmptyRequest) returns (ReportResultReply){
    option (google.api.http) = {get: "/v1/annualreport/result"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取年度报告结果",tags: ["年度报告"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  rpc GetSts(common.EmptyRequest) returns (GetStsReply) {
    option (google.api.http)={get:"/v1/annualreport/getsts"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取osss上传凭据",tags: ["年度报告"]};
  }
  //===========================社区热帖=====================================
  // 社区热帖
  rpc GetCommunityHot(GetCommunityHotRequest) returns (GetCommunityHotReply){
    option (google.api.http) = {get: "/v1/community/hots"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取社区热帖",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 社区热帖规则
  rpc GetCommunityHotRule(common.EmptyRequest) returns (GetCommunityHotRuleReply){
    option (google.api.http) = {get: "/v1/community/hots/rule"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取社区热帖规则",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  //==============================================WEB端搜索（search）===========================================================//
  // 综合搜索
  rpc GetWebCompoundSearch(GetCompoundSearchRequest) returns (GetCompoundSearchReply){
    option (google.api.http) = {get: "/v1/websearch"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "综合搜索",tags: ["WEB端搜索"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data,案例:【0,1761,3,354,0,c2c8dd80-3779-1114-a791-b32d4967b02e,0】  BasicData文档：https://klteeqb691.feishu.cn/wiki/wikcnCXIfKz384NVm79AnPgMexe?theme=LIGHT&contentTheme=DARK",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          }
        ]
      };
    };
  };
  //==============================================WEB端社区（community）======================================================//
  // WEB端获取交易商、服务商或个人页数量
  rpc GetWebCommunityUserHomeCount(GetCommunityUserHomeCountRequest) returns (GetCommunityUserHomeCountReply){
    option (google.api.http) = {get: "/v1/webcommunity/posts/count"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "详情页帖子总数",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端获取帖子发布人信息
  rpc GetWebCommunityPostsUser(GetCommunityPostsUserRequest) returns (UserData) {
    option (google.api.http) = {get: "/v1/webcommunity/posts/user"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "帖子发布人信息",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端收藏列表
  rpc GetWebCommunityCollectList(GetCommunityCollectListRequest) returns (GetCommunityListReply) {
    option (google.api.http) = {get: "/v1/webcommunity/collection/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "收藏列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端推荐栏目
  rpc GetWebCommunityRecommendList(GetCommunityRecommendListRequest) returns (GetCommunityListReply) {
    option (google.api.http) = {get: "/v1/webcommunity/recommend/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "推荐列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端用户主页帖子列表
  rpc GetWebUserHomeCommunity(GetUserHomeCommunityRequest) returns (GetCommunityListReply){
    option (google.api.http) = {get: "/v1/webcommunity/user/home"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户主页列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  };
  // WEB端商业列表
  rpc GetWebCommunityBusinessList(CommunityListRequest) returns (GetCommunityListReply){
    option (google.api.http) = {get: "/v1/webcommunity/business/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取商业列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端动态列表
  rpc GetWebCommunityDynamicList(CommunityListRequest) returns (GetCommunityListReply){
    option (google.api.http) = {get: "/v1/webcommunity/dynamic/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取动态列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端用户关注列表
  rpc GetWebUserFollowList(GetUserFollowListRequest) returns (GetUserFollowListReply) {
    option (google.api.http) = {get: "/v1/webcommunity/follow/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取关注列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端用户浏览列表
  rpc GetWebUserBrowseList(GetCommunityCollectListRequest) returns (GetCommunityCollectListReply) {
    option (google.api.http) = {get: "/v1/webcommunity/browse/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户浏览列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // WEB端推荐-新闻栏目
  rpc GetWebCommunityRecommendNewsList(GetCommunityRecommendNewsListRequest) returns (GetCommunityListReply) {
    option (google.api.http) = {get: "/v1/webcommunity/recommend/news"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "推荐-原创&交易商新闻列表",tags: ["WEB端商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  //活动列表
  rpc ActivityPageList(ActivityListRequest) returns (ActivityListReply) {
    option (google.api.http) = {get: "/v1/community/activity/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "活动列表",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  //活动详情
  rpc ActivityDetail(ActivityDetailRequest) returns (ActivityDetailReply) {
    option (google.api.http) = {get: "/v1/community/activity/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "活动详情",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  //参与活动
  rpc UserJoinActivity(UserJoinActivityRequest) returns (EmptyResponse) {
    option (google.api.http) = {post: "/v1/community/activity/userjoin",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "参与活动",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  //活动广场
  rpc ActivityPostsPageList(ActivityPostsPageListRequest) returns (GetCommunityListReply) {
    option (google.api.http) = {get: "/v1/community/activity/postspagelist"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "活动广场",tags: ["商业"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }

  // 明星排行榜
  rpc GetPopularCreatorList(GetPopularCreatorRequest) returns (GetPopularCreatorReply) {
    option (google.api.http) = {get: "/v1/community/getpopularcreatorlist"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取明星排行榜列表",tags: [""],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取邀请奖励弹窗数据
  rpc GetInvitationPopupData(GetInvitationPopupDataRequest) returns (GetInvitationPopupDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getinvitationpopupdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请奖励弹窗数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取邀请奖励领取banner数据
  rpc GetInviteRewardBannerData(GetInviteRewardBannerDataRequest) returns (GetInviteRewardBannerDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getinviterewardbannerdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请奖励领取banner数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取分享推广链接数据
  rpc GetShareLinkData(GetShareLinkDataRequest) returns (GetShareLinkDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getsharelinkdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取分享推广链接数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取邀请用户数据
  rpc GetInvitedUserData(GetInvitedUserDataRequest) returns (GetInvitedUserDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getinviteduserdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请用户数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取邀请用户记录数据
  rpc GetInvitedRecordData(GetInvitedRecordDataRequest) returns (GetInvitedRecordDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/invitedrecord"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请用户记录数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
}


