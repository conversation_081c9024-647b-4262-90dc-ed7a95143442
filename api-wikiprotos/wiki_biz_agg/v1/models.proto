syntax="proto3";

package api.wiki_biz_agg.v1;
option go_package ="api/wiki_biz_agg/v1;v1";
import "protoc-gen-openapiv2/options/annotations.proto";
import "google/protobuf/descriptor.proto";

// 社区用户和话题搜索
message GetCommunitySearchRequest {
  string content=1  [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索内容",required:["content"]}];
  int32 size=2  [json_name="size",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"请求数量",default:"10"}];
  int32 index=3  [json_name="index",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码",default:"1"}];
}
message  GetCommunityTopicSearchReply {
  int64 total=1  [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
  repeated CommunityTopicItem items=2  [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结果"}];

}
message CommunityTopicItem {
  string id =1  [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"话题Id"}];
  string topic=2 [json_name="topic",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"话题"}];
  string view=3 [json_name="view",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"浏览量"}];
}
message GetCommunityUserSearchReply {
  int64 total=1  [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
  repeated CommunityUserSearchItem items=2  [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结果"}];
}
message  CommunityUserSearchItem {
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string nickName = 2[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  int32  type=4 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1:用户 2:官方号"}];
  string vipIcon = 5[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string darenIcon = 6[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  string identityIcon=7  [json_name="identityIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份icon"}];
  string avatarFrame=8  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];

}

message  GetCommunityPostsUserRequest {
  string postsId =1  [json_name="postsId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子ID"}];
  int32 dataType =2 [json_name = "dataType", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"数据类型（从广告系统来）, 1301:article(文章) 1302:exposure(曝光) 1303:discover(发现) 1304:trader(交易商) 1305:survey(实勘) 1306:mediate(调解) 1307:flash(快讯) 1308:disclosure(披露) 1309:comment(评价)",required:["dataType"]}];
}

message  GetCommunityCollectListRequest {
  int32 type =1  [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1服务 2动态"}];
  int32 pageIndex=2 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=3 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}

message  GetCommunityCollectListReply {
  message CollectData {
    int32 dataType = 1[json_name = "dataType", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
    string postsId = 2 [json_name = "postsId", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"帖子ID"}];
    string title = 3 [json_name = "title", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"标题"}];
    Images images = 4 [json_name = "images", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"图片"}];
    string nickName = 5 [json_name = "nickName", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"发布人昵称"}];
    int64 collectTime = 6 [json_name = "collectTime", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"收藏时间"}];
    string userId=7  [json_name = "userId", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"用户ID"}];
    string languageCode =8  [json_name = "languageCode", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"语言Code--详情用"}];
    string countryCode =9  [json_name = "countryCode", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"国家Code--文章用"}];
    string contentLanguage=10  [json_name="contentLanguage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"传入详情页语言Code"}];
    bool isApplaud = 11 [json_name="isApplaud",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否点赞"}];
    UserData userInfo=12  [json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户数据"}];
    string vodFileId=13[json_name="vodFileId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"视频地址"}];
    bool hasVideo=14[json_name="hasVideo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有视频"}];
  }
  repeated CollectData list =1  [json_name = "list", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"收藏列表"}];
}

message GetCommunityRecommendListRequest {
  int32 pageIndex=1 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
  int32 firstShowPage=3 [json_name="firstShowPage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐使用"}];
  int32 position=4  [json_name="position",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐位置;0:发现，1:首页"}];
  int32 userIdentify=5  [json_name="userIdentify",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户身份:匿名情况下使用（1:投资者 2:金融从业者）,登录用户会自动获取"}];
  int64 preLoad =6[json_name="preload", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否预加载请求:0：非预加载;1:预加载"}];
}

message GetUserHomeCommunityRequest {
  string  userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  int32 type =2 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1:商业 2:动态"}];
  int32 pageIndex=3 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=4 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}

message GetUserFollowListRequest {
  string  userLoginId =1 [json_name="userLoginId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"登录用户ID"}];
  int32  pageIndex=2  [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32  pageSize=3  [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}

message GetUserFollowListReply {
  repeated UserFollowData list = 1 [json_name="list",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结果集"}];
  int32 total=2 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
}

message GetCommunityListReply {
  repeated CommunityData list = 1 [json_name="list",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结果集"}];
  int32 total=2 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
  int32 firstShowPage=3 [json_name="firstShowPage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐使用"}];
}

message CommunityListRequest {
  int32 pageIndex=1 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
  string serviceType =3 [json_name="serviceType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型"}];
  string userLoginId=4  [json_name="userLoginId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"登录用户UserID"}];
  string businessType=5  [json_name="businessType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"tag类型; 推荐 001 热门002 最新003"}];
  string dateTime=6  [json_name="dateTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐使用"}];
  int32 firstShowPage=7 [json_name="firstShowPage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐使用--从推荐列表取"}];
  int32 position=8  [json_name="position",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐位置;0:发现，1:首页"}];
  int32 userIdentify=9  [json_name="userIdentify",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户身份:匿名情况下使用（1:投资者 2:金融从业者）,登录用户会自动获取"}];
  int64 preLoad = 10[json_name="preload", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否预加载请求:0：非预加载;1:预加载"}];
}

message UserFollowData {
  int32 dataType = 1[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
  SurveyData surveyData = 2[json_name="surveyData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘"}];
  ArticleData articleData=3 [json_name="articleData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文章"}];
  RegdisclosureData regDisclosureData = 4[json_name="regDisclosureData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管披露"}];
  MediateData mediateData = 5[json_name="mediateData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解"}];
  PostsData postsInfo = 6[json_name="postsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子"}];
  UserData userInfo = 7[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  repeated TraderData traderCol=8 [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商/服务商信息"}];
  bool commentFlag =9  [json_name="commentFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"废弃"}];
  int32 commentFlagStatus =10  [json_name="commentFlagStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论开关标识代码：0 none   1Write 2 Read 3readWrite"}];
}

message CommunityData {
  int32 dataType = 1[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
  int32 isAd = 2[json_name="isAd",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否广告"}];
  AdData adInfo = 3[json_name="adInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告数据"}];
  SurveyData surveyData = 4[json_name="surveyData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘"}];
  ArticleData articleData=5 [json_name="articleData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文章"}];
  RegdisclosureData regDisclosureData = 6[json_name="regDisclosureData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管披露"}];
  MediateData mediateData = 7[json_name="mediateData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解"}];
  PostsData postsInfo = 8[json_name="postsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子"}];
  UserData userInfo = 9[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  bool notShowFeedback = 10[json_name="notShowFeedback",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不显示推荐反馈"}];
}

message TraderData {
  string code = 1[json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易Code"}];
  string name = 2[json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商名称"}];
  string icon = 3[json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"icon"}];
  int32  type=4 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1:交易商/服务商"}];
  int32 project=5 [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1:交易商 3:服务商"}];
  string englishShortName=6  [json_name="englishShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文简称"}];
  string localShortName=7  [json_name="localShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言简称"}];
  string logo=8  [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"logo"}];
  bool isWhite=9 [json_name="isWhite",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否白名单"}];
  double score=10 [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评分"}];
  string textScore=11 [json_name="textScore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数文本"}];
  double scoreLevel=12 [json_name="scoreLevel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数对应的星级"}];
  string showName=13 [json_name="showName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"显示名称"}];
  repeated LabelItem labels = 14 [json_name="labels",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"输出标签"}];
  string flag = 15 [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
  string hitsInfo =16  [json_name="hitsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告位统计字段"}];
}

message UserCol {
  string nickName = 1[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string nickNameColor = 2[json_name="nickNameColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称颜色"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string vipIcon = 4[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string userId = 5[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string darenIcon = 6[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  string fansCount = 7[json_name="fansCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"粉丝数"}];
  string applaudCount=8  [json_name="applaudCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量"}];
  string postsCount=9  [json_name="postsCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发帖数量"}];
  string avatarFrame=10  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];
  string hitsInfo =11  [json_name="hitsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告位统计字段"}];
}

message AdData {
  string id = 1[json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"商业置顶id"}];
  int32 tagType = 2[json_name="tagType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"2 置顶 3 推荐 4 热门 5 浏览"}];
  string tagContent = 3[json_name="tagContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签显示内容"}];
  int32 jumpType = 4[json_name="jumpType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1、交易商详情页 2、外部链接 3、新闻文章详情 4、实勘详情  6、VPS 10、直播详情 11、直播列表 13、商业 14、个人主页 15、服务商详情页 16、排行榜 17、H5"}];
  Images image = 5[json_name="image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string countryCode = 6[json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"从原始url提取出的国家code，用于内部跳转"}];
  string languageCode = 7[json_name="languageCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"从原始url提取出的语言code，用于内部跳转"}];
  string slogan = 8[json_name="slogan",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告词内容"}];
  string title = 9[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题内容"}];
  string url = 10[json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"原始链接url"}];
  repeated TraderData traderCol = 11[json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商/服务商"}];
  repeated UserCol userCol = 12[json_name="userCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  string code =13 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"业务Code--老广告接口用"}];
  int32 activityType=14   [json_name="activity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动--老广告接口用"}];
  string appJumpType=15 [json_name="appJumpType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转类型"}];
  string  hitsInfo=16  [json_name="hitsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"统计字段"}];
}

message SurveyData {
  string seal = 1[json_name="seal",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"印章"}];
  string surveyor = 2[json_name="surveyor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘人"}];
  string flag = 3[json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘到访地区旗帜"}];
  string countryName = 4[json_name="countryName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前实勘访问公司所在国家名称"}];
  string evaluate = 5[json_name="evaluate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘评价"}];
  string evaluateColor = 6[json_name="evaluateColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评价颜色"}];
  string baiduImg = 7[json_name="baiduImg",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"百度坐标缩略图"}];
  string googleImg = 8[json_name="googleImg",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"谷歌坐标缩略图"}];
}

message TagData {
  string tagName = 1[json_name="tagName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签名称"}];
  string color = 2[json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"颜色"}];
  string tagCode = 3[json_name="tagCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"code"}];
  string languageCode=4 [json_name="languageCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"使用语言"}];
}

message CategoryData {
  string categoryName = 1[json_name="categoryName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类名称"}];
  string categoryId = 2[json_name="categoryId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类id"}];
}

message SummaryData {
  int32 rule = 1[json_name="rule",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"匹配规则"}];
  string ruleName = 2[json_name="ruleName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"规则名称"}];
  uint64 wikiTimestamp = 3[json_name="wikiTimestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"披露时间戳"}];
  string penaltyAmount = 4[json_name="penaltyAmount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"处罚金额(完整)"}];
  string amountSymbol = 5[json_name="amountSymbol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"处罚金额符号"}];
  string reason = 6[json_name="reason",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"处罚原因"}];
}

message RegdisclosureData {
  repeated TraderData traderCol = 1[json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"披露交易商"}];
  TagData tagData = 2[json_name="tagData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"披露标签"}];
  CategoryData categoryData = 3[json_name="categoryData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"披露分类"}];
  SummaryData summaryData = 4[json_name="summaryData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"摘要信息"}];
  string content = 5[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容"}];
  string seal = 6[json_name="seal",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"章"}];
}

message MediateData {
  TraderData traderData = 1[json_name="traderData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解交易商"}];
  string objectLabel = 2[json_name="objectLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解对象标签"}];
  string categoryLabel = 3[json_name="categoryLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解问题标签"}];
  string category = 4[json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解问题"}];
  string requirementLabel = 5[json_name="requirementLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解要求标签"}];
  string requirement = 6[json_name="requirement",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解要求"}];
  string amountLabel = 7[json_name="amountLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解金额标签"}];
  string amount = 8[json_name="amount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额"}];
  string symbol = 9[json_name="symbol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币符号"}];
  string stamp =10 [json_name="stamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"印章地址"}];
  string annotation =11 [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
  string color =12 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标状态颜色"}];
  string newColor =13 [json_name="newColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"新角标状态颜色"}];
  string threeCode=14  [json_name="threeCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币单位"}];
  string elapsed =15 [json_name="elapsed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间"}];
  string closeLabel =16 [json_name="closeLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间标签"}];
  int32 appendStatus=17 [json_name="appendStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解状态"}];
}

message Sign {
  int32 isShow = 1[json_name="isShow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示"}];
  string bgColor = 2[json_name="bgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"背景颜色"}];
  string word = 3[json_name="word",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"显示文字"}];
  string icon = 4[json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"icon"}];
  int32 isAd = 5[json_name="isAd",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否是广告"}];
}

message Images {
  string list = 1[json_name="expoId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"列表图"}];
  string detail = 2[json_name="detail",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"详情图"}];
  string url = 3[json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"url"}];
  int32 width = 4[json_name="width",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"宽度"}];
  int32 height = 5[json_name="height",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"高度"}];
}

message PostsData {
  Sign sign = 1[json_name="sign",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"右下角角标"}];
  string postsId = 2[json_name="postsId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子ID"}];
  string releaseType = 3[json_name="releaseType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型 1服务 2动态"}];
  string title = 4[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  string titleNew = 5[json_name="titleNew",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题原文"}];
  string content = 6[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容"}];
  string shareUrl = 7[json_name="shareUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享地址"}];
  repeated Images images = 8[json_name="images",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string themeCode = 9[json_name="themeCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型"}];
  bool isApplaud = 10[json_name="isApplaud",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否点赞"}];
  bool isCollect = 11[json_name="isCollect",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否收藏"}];
  int64 publishTime = 12[json_name="publishTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布时间戳"}];
  string serviceTypeName=13 [json_name="serviceTypeName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型名称"}];
  string serviceTypeCode=14  [json_name="serviceTypeCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务类型Code"}];
  string  countryName=15  [json_name="countryName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发帖所在国家"}];
  int32 grade=16  [json_name="grade",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评价等级 1好评 2中评 3 曝光"}];
  string views=17  [json_name="views",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"浏览数"}];
  bool showViewFlag=18  [json_name="showViewFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示浏览数标识"}];
  string contentLanguage=19  [json_name="contentLanguage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"传入详情页语言Code"}];
  int64 applaudNumber = 20[json_name="applaudNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞数量"}];
  string showApplaudNumber = 21[json_name="showApplaudNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞数量"}];
  int64 commentNumber = 22[json_name="commentNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论数量"}];
  string showCommentNumber = 23[json_name="showCommentNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论数量"}];
  int64 collectNumber = 24[json_name="collectNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"收藏数量"}];
  string showCollectNumber = 25[json_name="showCollectNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"收藏数量"}];
  repeated PostsTopicItems postsTopicItems=26[json_name="postsTopicItems",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"@和话题相关数据"}];
  bool isShowApplaudNumber=27[json_name="isShowApplaudNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示点赞"}];
  bool isShowCommentNumber=28[json_name="isShowCommentNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示评论"}];
  bool isShowCollectNumber=29[json_name="isShowCollectNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示收藏"}];
  bool isTop=30[json_name="isTop",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否置顶"}];
  string topContent=31[json_name="topContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"置顶文字"}];
  string topColor=32[json_name="topColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"置顶字体颜色"}];
  string topBgColor=33[json_name="topBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"置顶字体背景色"}];
  string vodFileId=34[json_name="vodFileId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"视频地址"}];
  bool hasVideo=35[json_name="hasVideo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有视频"}];
  string showPlayTimes=36[json_name="showPlayTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示播放次数"}];
  int64 playTimes=37 [json_name="playTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"播放次数"}];
  bool isShowPlayTimes=38  [json_name="isShowPlayTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示播放次数"}];
}


message UserData {
  string nickName = 1[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string nickNameColor = 2[json_name="nickNameColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称颜色"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string vipIcon = 4[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string userId = 5[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string darenIcon = 6[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  int32 rightLabelType = 7[json_name="rightLabelType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户类型 1企业号 2员工 3 个人 4 kol"}];
  bool isFollow=8 [json_name="isFollow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否关注"}];
  bool  isSelf=9  [json_name="isSelf",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否自己"}];
  bool  isShowFlow=10 [json_name="isShowFlow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示关注"}];
  OfficialNumberType officialNumber=11 [json_name="officialNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方号类型"}];
  string official =12  [json_name="official",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方"}];
  string officialColor =13  [json_name="officialColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方颜色"}];
  int64 fansCount=14  [json_name="fansCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"粉丝数量"}];
  string identityIcon=15  [json_name="identityIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"身份icon"}];
  string timeAfterLabel=16  [json_name="timeAfterLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"员工标签"}];
  string  registerLong=17  [json_name="registerLong",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册时长"}];
  string position=18  [json_name="position",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"职位"}];
  int32  enterpriseType=19   [json_name="enterpriseType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"企业类型，1:服务商 2:交易商"}];
  int64 applaudCount=20  [json_name="applaudCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量"}];
  string showFansCount=21  [json_name="showFansCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量展示简写"}];
  string showApplaudCount=22  [json_name="showApplaudCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获赞数量展示简写"}];
  string avatarFrame=23  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];
  string wikiFxNumber=24  [json_name="wikiFxNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"天眼号"}];
  int32 attentionStauts=25  [json_name="attentionStauts",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1未关注 2 已关注 3相互关注   4自己"}];
}


message ArticleData {
  string languageCode=1 [json_name="languageCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文章语言"}];
  string countryCode=2  [json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文章国家"}];
  string category=3  [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文章分类"}];
}

message  GetCommunityUserHomeCountRequest {
  string id =1  [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"主页ID"}];
}

message GetCommunityUserHomeCountReply {
  string dynamicCount=1  [json_name="dynamicCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"动态数量"}];
  string businessCount=2  [json_name="businessCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"商业数量"}];
}

message GetCommunityRecommendNewsListRequest {
  int32 pageIndex=1 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
  string newsType =3  [json_name="newsType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"004原创 005交易商"}];
}

// 官方号类型
enum OfficialNumberType {
  OfficialNumber_Unknown=0; //未知
  Trader=1; // 交易商号
  WikiFXMediate=2; // 天眼调解
  WikiFXNews=3; // WikiFX-新闻
  WikiFXExpress=4; // WikiFX-快讯
  WikiFXSurvey=5; // WikiFX-实勘
  ServiceProvider=6; // 服务商号
  Regulator=7; // 监管机构号
  User=8; // 用户号
  WikiFXActivity=9; // WikiFX-活动
  LemonX=10; // LemonX官方号
  WikiExpo=11; // WikiExpo官方号
  WikiFx=12; // WikiFx官方号
  WikiFxEducation=13; // WikiFxEducation官方号
  WikiFxElitesClub=14; // WikiFxElitesClub官方号
  WikiFxSkylineGuide=15; // WikiFxSkylineGuide官方号
}


//============================================搜索=====================================/
message WikifxSearchCategory {
  // 分类名称
  string name = 1 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类名称"}];
  // 排序号
  int32 order = 2 [json_name="order",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"排序号"}];
  // 0 全部 1 wikifx 2 wikibit
  int32 project = 3 [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"0 全部 1 wikifx 2 wikibit"}];
  // Project=>0,0:全部;Project=>1,1:交易商 2：代理商;Project=>2,10:区块
  int32 type = 4 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"Project=>0,0:全部;Project=>1,1:交易商 2：代理商;Project=>2,10:区块"}];
}

// 搜索联想请求
message GetSearchIntellisenseRequestV2 {
  string content =1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索条件",required:["content"]}];
  int32 scene = 2 [json_name="scene",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"场景 1:首页 2:交易商详情页 3:社区页 4:社区内容详情页 5:服务商详情页 6:企业详情页"}];
  int32 project=4  [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目类型(0:混合搜索,1:wikifx搜索,2:区块搜索,3:服务商搜索,4:内容搜索,5:用户搜索),默认0"}];
  int32 type=5   [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"project==>0(2:综合);project==>1(1:交易商);project==>2(10:交易所&通证);project==>3(1:服务商);project==>4(0:内容);project==>5(1:用户,2:官方号，默认用户),默认2"}];
}

message GetSearchIntellisenseItemV2 {
  // Code
  string code = 1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"Code"}];
  // 名称
  string name = 2 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"名称"}];
  // ico
  string ico = 3 [json_name="ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"ico"}];
  // 项目 1 wikifx 2 wikibit
  int32 project = 4 [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目 1 wikifx 2 wikibit"}];
  // project=>wikifx 1-trader 2-agent;project=>wikibit 1-交易所 2-项目 3-钱包 4-通证
  int32 type = 5 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"project=>wikifx 1-trader 2-agent;project=>wikibit 1-交易所 2-项目 3-钱包 4-通证"}];
  // 角标背景色
  string color = 6 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标背景色"}];
  // 角标
  string annotation = 7 [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
  // 总分
  double score = 8 [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总分"}];
  // 分数文本 9.00 或 AAA
  string textScore = 9 [json_name="textScore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数文本 9.00 或 AAA"}];
  // urlName
  string urlName = 10 [json_name="urlName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"urlName"}];
  // 主域信息
  repeated string host = 11;
  // 口碑评级
  string kbScore = 12 [json_name="kbscore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"口碑评级"}];
  // 数据
  int32 dataType = 13 [json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
}

// 贴子联想结构体
message CommunityIntellisenseItemV2 {
  // 贴子id
  string id = 1 [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"贴子id"}];
  // 数据类型
  int32 dataType = 2 [json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型"}];
  // 标题
  string title = 3 [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  // 命中的关键字
  string keyword = 4 [json_name="keyword",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"命中的关键字"}];
  // 用户id
  string userId = 5 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
}

// 搜索联想响应
message GetSearchIntellisenseReplyV2{
  repeated  GetSearchIntellisenseItemV2 items=1 [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"联想结果"}];
  repeated  CommunityIntellisenseItemV2 content=2   [json_name = "content", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"内容模型"}];
}

// 综合搜索v2版本接口-请求参数
message GetCompoundSearchRequestV2 {
  string  content =1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索条件",required:["content"]}];
  int32 pageIndex=2  [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=3  [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页大小"}];
  int32 project=4  [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目类型(0:混合搜索,1:wikifx搜索,2:区块搜索,3:服务商搜索,4:内容搜索,5:用户搜索),默认0"}];
  int32 type=5   [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"project==>0(2:综合);project==>1(1:交易商);project==>2(10:交易所&通证);project==>3(1:服务商);project==>4(0:内容);project==>5(1:用户,2:官方号，默认用户),默认2"}];
  bool hasAd=6 [json_name="hasAd",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否包含品专广告，默认true"}];
  int32 adPtn=7 [json_name="adPtn",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告模式  0 默认 1 发布模式 2 沙盒模式 4 审核模式"}];
  string url=8 [json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索地址栏url"}];
  // 场景参数
  // 上次栏目的数据
  repeated WikifxSearchCategory lastCategory = 9 [json_name="lastCategory",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"上次栏目的数据"}];
  int32 scene = 10 [json_name="scene",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"场景 1:首页 2:交易商详情页 3:社区页 4:社区内容详情页 5:服务商详情页 6:企业详情页"}];
}

// 综合请求结果v2版本-响应参数
message  GetCompoundSearchReplyV2 {
  int32  total =1 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
  WikiSearchCompoundModel  compound=2 [json_name="compound",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"综合页结果"}];
  WikiSearchTabModel tab =3  [json_name="tab",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"各个tab搜索结果"}];
  string  totalText=4  [json_name="totalText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数展示文本"}];
  int32  type =5 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索类型 0:普通搜索 1:网址精准搜索"}];
  FakeInfo fakeInfo=6  [json_name="fakeInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"假冒数据"}];
  AdvertisementAttr ad=7   [json_name="ad",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品专广告模型"}];
  SurveyInfo surveyInfo=8    [json_name="surveyInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘"}];
  string jumpUrl=9   [json_name="jumpUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"外跳链接，目前支持年度报告"}];
  string tips=10   [json_name="tips",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索结果提示"}];

  // 分类数据-数组
  repeated WikifxSearchCategory categoryData = 11 [json_name="categoryData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类数据"}];
  // 搜索内容排序优先级
  repeated WikifxSearchCategory contentPriority = 12 [json_name="contentPriority",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索内容排序优先级"}];

}
// 综合搜索请求
message GetCompoundSearchRequest {
  string  content =1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索条件",required:["content"]}];
  int32 pageIndex=2  [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=3  [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页大小"}];
  int32 project=4  [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目类型(0:混合搜索,1:wikifx搜索,2:区块搜索,3:服务商搜索,4:内容搜索,5:用户搜索),默认0"}];
  int32 type=5   [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"project==>0(2:综合);project==>1(1:交易商);project==>2(10:交易所&通证);project==>3(1:服务商);project==>4(0:内容);project==>5(1:用户,2:官方号，默认用户),默认2"}];
  bool hasAd=6 [json_name="hasAd",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否包含品专广告，默认true"}];
  int32 adPtn=7 [json_name="adPtn",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告模式  0 默认 1 发布模式 2 沙盒模式 4 审核模式"}];
  string url=8 [json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索地址栏url"}];
}

// 综合请求结果
message  GetCompoundSearchReply {
  int32  total =1 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
  WikiSearchCompoundModel  compound=2 [json_name="compound",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"综合页结果"}];
  WikiSearchTabModel tab =3  [json_name="tab",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"各个tab搜索结果"}];
  string  totalText=4  [json_name="totalText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数展示文本"}];
  int32  type =5 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索类型 0:普通搜索 1:网址精准搜索"}];
  FakeInfo fakeInfo=6  [json_name="fakeInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"假冒数据"}];
  AdvertisementAttr ad=7   [json_name="ad",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品专广告模型"}];
  SurveyInfo surveyInfo=8    [json_name="surveyInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘"}];
  string jumpUrl=9   [json_name="jumpUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"外跳链接，目前支持年度报告"}];
  string tips=10   [json_name="tips",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索结果提示"}];
}

// 用户&官方号搜索请求
message GetBackEndSearchRequest {
  string  content =1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索条件",required:["content"]}];
  string  userId =2 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"userId"}];
}

// 用户&官方号搜索结果
message  GetBackEndSearchReply {
  repeated GetBackEndSearchItem items=1[json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"items"}];
}

message  GetBackEndSearchItem {
  string code =1[json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"code"}];
  string nickName = 2[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  OfficialNumberType officialNumber=4 [json_name="officialNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方号类型"}];
  string avatarFrame=5  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];
}

//假冒信息
message FakeInfo {
  int32 count=1   [json_name="count",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"假冒数量"}];
  string set=2   [json_name="set",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"假冒Code Base64编码"}];
}

// 广告
message    AdvertisementAttr {
  TraderInfo trader=1   [json_name="trader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商信息"}];
  ConfigKit kit=2   [json_name="kit",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"语言包"}];
  repeated  AdMediaModel media=3   [json_name="media",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告数据"}];
}

// 交易商
message  TraderInfo  {
  string  code=1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商Code"}];
  string  color=2  [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"背景色"}];
  string  ico=3  [json_name="ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"ico"}];
  string  defaultIco=4   [json_name="defaultIco",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"默认ico"}];
  string  logo=5   [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"logo"}];
  string  intro=6   [json_name="intro",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"介绍"}];
  float score=7   [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"天眼评分"}];
  BrandLabel label=8   [json_name="label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签"}];
  string  localShortName=9   [json_name="localShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言简称"}];
  string  localFullName=10   [json_name="localFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言全称"}];
  string  englishShortName=11   [json_name="englishShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英语简称"}];
  string englishFullName=12    [json_name="englishFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英语全称"}];
  string webSite=13    [json_name="webSite",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"网址"}];
  repeated BrandRegulator licenses=14    [json_name="licenses",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"牌照信息"}];
  string  country=15    [json_name="country",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册国家"}];
  string flag=16   [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
  string  annotation=17   [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"汇总监管状态"}];
  string  totalColor=18   [json_name="totalColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商状态颜色"}];
  string  detail_HitsInfo=19  [json_name="detail_HitsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品专进入详情页统计数据"}];
  string  official_HitsInfo=20  [json_name="official_HitsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品专进入官网统计数据"}];
}

// 品牌标签
message  BrandLabel {
  string  score=1  [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总分"}];
  string  reg=2  [json_name="reg",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管机构"}];
  string  lic=3  [json_name="lic",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"牌照"}];
  string  year=4  [json_name="year",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"经营时间"}];
  string  licFullName=5  [json_name="licFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"牌照全称"}];
}

//监管信息
message  BrandRegulator {
  string  flag=1  [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
  string  cflag=2  [json_name="cflag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"圆角国旗"}];
  string  shortName=3  [json_name="shortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"简称"}];
  string  licenseShortName=4  [json_name="licenseShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"牌照简称"}];
  string  licenseName=5  [json_name="licenseName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"牌照全称"}];
  string  eShortName=6  [json_name="eShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管机构英文简称"}];
  string  annotation=7  [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管状态"}];
  string  color=8  [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管汇总颜色"}];
}

//配置信息
message  ConfigKit {
  string  color=1  [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品牌背景色"}];
  string  brand=2  [json_name="brand",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品牌语言包"}];
  string  site=3  [json_name="site",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"进入官网语言包"}];
  string  detail=4   [json_name="detail",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"进入详情页语言包"}];
  string  zone=5  [json_name="zone",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品牌专区语言包"}];
  string  year=6  [json_name="year",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"经营年限语言包"}];
  string  img=7  [json_name="img",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品牌图片"}];
}

//广告媒体信息
message  AdMediaModel {
  string  language=1  [json_name="language",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"语言"}];
  string  tCode=2  [json_name="tCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片类型关联的目标（交易商Code），自定义图片类型时该字段值为空字符串"}];
  int32  order=3  [json_name="order",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"排序号"}];
  int32 jumpType=4  [json_name="jumpType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点击广告时的跳转类型（0：无跳转，1：详情页，2：外链接，3：新闻页，4：实勘页，5：书城页，6：云主机）"}];
  string  url=5  [json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转地址"}];
  string  color=6  [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告的角标背景色"}];
  string  image=7  [json_name="image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string  h5img=8  [json_name="h5img",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"H5图片"}];
  string  image1000_530=9  [json_name="image1000_530",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1000*530"}];
  string  image800_530=10  [json_name="image800_530",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"800*530"}];
  string hitsInfo=11  [json_name="hitsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"统计信息"}];
}

message  SurveyInfo {
  string evaluatecode = 1  [json_name="evaluatecode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"code"}];
  int32 imgcount = 2  [json_name="imgcount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片数量"}];
  repeated string images = 3 [json_name="images",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string sid = 4  [json_name="sid",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘ID"}];
  string code = 5 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘Code"}];
  string evaluate = 6  [json_name="evaluate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评价等级"}];
  int32 type = 7   [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型"}];
  string evaluatecolor = 8  [json_name="evaluatecolor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评价色值"}];
  int32 level = 9  [json_name="level",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级"}];
  string address = 10  [json_name="address",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"地址"}];
  string date = 11  [json_name="date",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘日期"}];
  int32 wiki_timestamp = 12  [json_name="wiki_timestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"时间戳"}];
  string craeteddate = 13   [json_name="craeteddate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"创建时间"}];
  string cover = 14  [json_name="cover",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"封面"}];
  string ico = 15   [json_name="ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"ico"}];
  string flag = 16    [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
  string company = 17    [json_name="company",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司名称"}];
  string localcompany = 18   [json_name="localcompany",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言公司名称"}];
  string vr = 19  [json_name="vr",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vr"}];
  string vcc = 20  [json_name="vcc",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘国家"}];
  string country = 21  [json_name="country",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家"}];
  string city = 22   [json_name="city",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"城市"}];
  string title = 23   [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  string summary = 24   [json_name="summary",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘总结"}];
  string exhibition = 25    [json_name="exhibition",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示地址"}];
  string videourl = 26    [json_name="videourl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"视屏URL"}];
  string videocover = 27   [json_name="videocover",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"视屏封面"}];
  bool isvideo = 28    [json_name="isvideo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有视屏"}];
  bool isexhibition = 29   [json_name="isexhibition",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示"}];
}


message CompoundSearchBaseModel {
  string code = 1  [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"code"}];
  float score = 2  [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评分"}];
  string text_score = 3  [json_name="text_score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数文本 9.00 或 AAA"}];
  float scoreLevel = 4  [json_name="scoreLevel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数对应的星级"}];
  string color = 5 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标颜色"}];
  string annotation = 6 [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
  repeated LabelItem labels = 7  [json_name="labels",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"输出标签"}];
  string flag = 8  [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
  string logo = 9 [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"logo"}];
  string ico = 10 [json_name="ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"ico"}];
  string hico = 11 [json_name="hico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"hico"}];
  int32 project = 12 [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目类型 1 Wikifx 2 Wikibit 3 服务商 4 WikiStock"}];
  int32 type = 13 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"Project => 1 交易商 2 代理商；Project => 1-交易所 2-项目 3-钱包 4-通证; Project=>3 1-fx 2-bit"}];
  repeated string regnumber = 14 [json_name="regnumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管号"}];
  string localShortName = 15 [json_name="localShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言简称"}];
  string localFullName = 16 [json_name="localFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言全称"}];
  string englishShortName = 17 [json_name="englishShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文简称"}];
  string englishFullName = 18 [json_name="englishFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文全称"}];
  string showName = 19 [json_name="showName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"前端直接显示名称  显示规则：https://klteeqb691.feishu.cn/wiki/wikcn7tfzG4GXY17auGfhMvSGwe"}];
  string enterpriseName = 20 [json_name="enterpriseName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"企业名片显示名称，规则： https://klteeqb691.feishu.cn/wiki/wikcn7tfzG4GXY17auGfhMvSGwe ==> 企业名片列表展示"}];
  bool isVr = 21 [json_name="isVr",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有VR"}];
  int32 agentMember = 22 [json_name="agentMember",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"代理商会员数"}];
  repeated string agentLabel = 23 [json_name="agentLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"代理商标签"}];
  bool  isEpc=24  [json_name="isEpc",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否是保障"}];
  int32 ultimateType = 25 [json_name="ultimateType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"旗舰店类型"}];
  string urlName = 26 [json_name="urlName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"urlName"}];
  string seal = 27 [json_name="seal",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商印章"}];
  string createdDate = 28 [json_name="createdDate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"录入时间"}];
  bool isFake = 29  [json_name="isFake",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否假冒"}];
  string kbscore = 30  [json_name="kbscore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"口碑评级"}];
  repeated LabelItem serviceProviderLabel = 31 [json_name="serviceProviderLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务商标签"}];
  string forbidExpiredAt = 32 [json_name="forbidExpiredAt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"禁搜过期时间"}];
  string registerCountry = 33 [json_name="registerCountry",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册国家"}];
  int32 status = 34 [json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"汇总状态"}];
  repeated DimensionModel dimension = 35 [json_name="dimension",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"小项分"}];
  repeated WikiInvestment investment = 36 [json_name="investment",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"联系方式"}];
  Category category = 37  [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索结果分类"}];
  int32 staffCount = 38 [json_name="staffCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"企业员工数"}];
  int32 cooperationCount = 39 [json_name="cooperationCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"企业合作数"}];
  repeated string website = 40 [json_name="website",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"网址"}];
  repeated string accountWebsite = 41 [json_name="accountWebsite",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方网址"}];
  repeated MatchedContent matchedContent = 42 [json_name="matchedContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"匹配内容"}];
  string registerCountryCode = 43  [json_name="registerCountryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册国家Code"}];
  string foundDate = 44  [json_name="foundDate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"成立日期"}];
  bool offical = 45 [json_name="offical",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否是官方词匹配"}];
  string officalColor = 46  [json_name="officalColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方词颜色"}];
  string officalTip = 47 [json_name="officalTip",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方词提示"}];
  string officalImage = 48 [json_name="officalImage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方词背景图片"}];
  float esScore = 49 [json_name="esScore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索匹配度"}];
  string telephone = 50 [json_name="telephone",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"电话"}];
  string email = 51 [json_name="email",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邮箱"}];
  string address = 52 [json_name="address",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司地址"}];
  string introduction = 53 [json_name="introduction",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司简介"}];
  repeated TraderSpecialtyLabelModel specialtyLabels=54 [json_name="specialtyLabels",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"特色标签"}];
  string propTraderFeeLabel=55  [json_name="propTraderFeeLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"自营交易商最低费用标签"}];
}

message LabelItem {
  int32 type = 1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签类型"}];
  message  LabelDataItem	{ // 标签具体内容
    string  labelName=1  [json_name="labelName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签内容"}];
  }
  repeated LabelDataItem data = 2 [json_name="data",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签内容"}];
}
message  TraderSpecialtyLabelModel {
  int32 labelType = 1 [json_name="labelType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签类型（1-旗舰店,2-Skyline,3-伊斯兰账户,4-黄金账号,5-原有账户,6-加密货币账户）"}];
  string icon = 2 [json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"icon"}];
  string image = 3 [json_name="image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string content = 4 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签内容"}];
  string backgroundColor = 5 [json_name="backgroundColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"背景色"}];
  string fontColor = 6 [json_name="fontColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"字体颜色"}];
  int32 imageWidth=7  [json_name="imageWidth",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片宽度"}];
  int32 imageHeight=8  [json_name="imageHeight",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片高度"}];
}

message   DimensionModel  {
  string code = 1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类编码"}];
  string category = 2 [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数类型"}];
  float score = 3 [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分数"}];
}

message   WikiInvestment  {
  int32 type = 1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型"}];
  string contact = 2 [json_name="contact",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"联系方式"}];
}

message   Category  {
  string color = 1 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类背景色"}];
  string name = 2 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类名称"}];
}

message   MatchedContent  {
  string type = 1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型"}];
  repeated string words = 2 [json_name="words",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"匹配数组"}];
}


// 查看更多
message ShowMoreModel  {
  bool traderMore = 1 [json_name="traderMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商加载更多"}];
  bool serviceProviderMore = 2 [json_name="serviceProviderMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务商加载更多"}];
  bool contentMore = 3 [json_name="contentMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容加载更多"}];
  bool bitMore = 4 [json_name="bitMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"Bit加载更多"}];
  bool userMore = 5 [json_name="userMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户加载更多"}];
  bool OfficialMore = 6 [json_name="OfficialMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方号加载更多"}];
  bool companyMore=7  [json_name="companyMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司加载更多"}];
  bool propTraderMore=8  [json_name="propTraderMore",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"自营交易商加载更多"}];
}

message WikiSearchCompoundModel  {
  repeated  CompoundSearchBaseModel  data = 1 [json_name = "data", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"交易商"}];
  repeated  CompoundSearchBaseModel  serviceProvider = 2 [json_name = "serviceProvider", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"服务商"}];
  repeated  CompoundSearchBaseModel  bit = 3 [json_name = "bit", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"bit"}];
  repeated  UserData  user = 4 [json_name = "user", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"用户"}];
  repeated  UserData  official = 5 [json_name = "official", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"官方"}];
  repeated  CommunityData content=6  [json_name = "content", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"内容"}];
  ShowMoreModel showMore = 7 [json_name = "showMore", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"交易商Code"}];
  repeated WikiFxCompanyData company=8  [json_name = "company", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"公司"}];
  CompoundTabLanguagePkg categoryLang=9   [json_name = "categoryLang", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"综合页分类语言包"}];
  repeated CompoundSearchBaseModel propTrader=10  [json_name = "propTrader", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"自营交易商"}];
}

message WikiFxCompanyData {
  string code = 1  [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"code"}];
  string color = 2 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标颜色"}];
  string annotation = 3 [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
  repeated LabelItem labels = 4  [json_name="labels",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"输出标签"}];
  string flag = 5  [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
  string logo = 6 [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"logo"}];
  string ico = 7 [json_name="ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"ico"}];
  string hico = 8 [json_name="hico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"hico"}];
  int32 project = 9 [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目类型 1 Wikifx 2 Wikibit 3 服务商 4 WikiStock 6 公司"}];
  int32 type = 10 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"Project => 1 交易商 2 代理商；Project => 1-交易所 2-项目 3-钱包 4-通证; Project=>3 1-fx 2-bit"}];
  string localShortName = 11 [json_name="localShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言简称"}];
  string localFullName = 12 [json_name="localFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言全称"}];
  string englishShortName = 13 [json_name="englishShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文简称"}];
  string englishFullName = 14 [json_name="englishFullName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文全称"}];
  string showName = 15 [json_name="showName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"前端直接显示名称  显示规则：https://klteeqb691.feishu.cn/wiki/wikcn7tfzG4GXY17auGfhMvSGwe"}];
  string registrationNum=16   [json_name="registrationNum",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册编号"}];
  Category category = 17  [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索结果分类"}];
  string foundDate = 18  [json_name="foundDate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"成立日期"}];
  string companyType =19  [json_name="companyType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司类型"}];
  string  registrationStatus =20  [json_name="registrationStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"登记状态"}];
  string judicialArea=21  [json_name="judicialArea",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"司法辖区"}];
  repeated TraderData relation =22   [json_name="relation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关联品牌"}];
  bool isFollow=23 [json_name="isFollow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否关注"}];
  string registrationBorder=24 [json_name="registrationBorder",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"登记状态边框"}];
  string registrationBgColor=25 [json_name="registrationBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"登记状态背景"}];
  string registrationFont=26 [json_name="registrationFont",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"登记状态字体"}];
  string registerCountry=27 [json_name="registerCountry",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册国家"}];
  string showRelationFlag=28 [json_name="showRelationFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示关联品牌"}];
  string flagC=29  [json_name="flagC",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"圆角国旗"}];
}

message CompoundTabLanguagePkg {
  string trader=1  [json_name="trader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商"}];
  string serviceProvider=2  [json_name="serviceProvider",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"服务商"}];
  string company=3  [json_name="company",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司"}];
  string bit=4  [json_name="bit",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"区块"}];
  string official=5  [json_name="official",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方号"}];
  string user=6  [json_name="user",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  string content=7  [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容"}];
  string propTrader=8  [json_name="propTrader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"自营交易商"}];
}

message WikiSearchTabModel {
  repeated  CompoundSearchBaseModel data=1  [json_name = "data", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"交易商和服务商搜索结果模型"}];
  repeated  CommunityData content=2   [json_name = "content", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"内容模型"}];
  repeated  UserData user=3  [json_name = "user", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"用户"}];
  repeated  UserData official=4  [json_name = "official", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"官方号"}];
  repeated  WikiFxCompanyData company=5  [json_name = "company", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"公司"}];
}

//========================================LemonX======================================================/
// 智能提示
message GetLemonXIntellisenseRequest {
  string content=1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关键词",required:["content"]}];
}

message GetLemonXIntellisenseReply {
  repeated LemonXIntellisenseItem items=1  [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索结果"}];
}
message LemonXIntellisenseItem {
  string keyword=1  [json_name="keyword",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关键词"}];
  string highLight=2  [json_name="highLight",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"高亮字段"}];
}

// 搜索
message GetLemonXCompoundSearchRequest {
  string content =1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索条件",required:["content"]}];
  int32 pageIndex=2  [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=3  [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页大小"}];
  int32 project=4  [json_name="project",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"项目类型(0:混合搜索,1:wikifx搜索,2:区块搜索,3:服务商搜索,4:内容搜索,5:用户搜索),默认0"}];
  int32 type=5   [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"project==>0(2:综合);project==>1(1:交易商);project==>2(10:交易所&通证);project==>3(1:服务商);project==>4(0:内容);project==>5(1:用户,2:官方号，默认用户),默认2"}];
}
message GetLemonXCompoundSearchReply {
  int32  total =1 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
  LemonXCompoundModel  compound=2 [json_name="compound",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"综合页结果"}];
  LemonXTabModel tab =3  [json_name="tab",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"各个tab搜索结果"}];
  string  totalText=4  [json_name="totalText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数展示文本"}];
}
message LemonXCompoundModel {
  repeated  UserData  user = 1 [json_name = "user", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"用户"}];
  repeated  UserData  official = 2 [json_name = "official", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"官方"}];
  repeated  CommunityData content=3  [json_name = "content", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"内容"}];
  ShowMoreModel showMore = 4 [json_name = "showMore", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"显示更多模型"}];
}
message LemonXTabModel {
  repeated  CommunityData content=1   [json_name = "content", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"内容模型"}];
  repeated  UserData user=2  [json_name = "user", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"用户"}];
  repeated  UserData official=3  [json_name = "official", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title:"官方号"}];
}

// 发现
message GetLemonXSearchDiscoverRequest {
}
message GetLemonXSearchDiscoverReply {
  repeated LemonXSearchDiscoverContentItem content =1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容"}];
  repeated LemonXSearchDiscoverUserItem user =2 [json_name="user",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
}
message LemonXSearchDiscoverContentItem {
  int32 dataType = 1[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
  string postsId = 2[json_name="postsId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子ID"}];
  string title = 3[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
}
message LemonXSearchDiscoverUserItem {
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string nickName = 2[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string vipIcon = 4[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string darenIcon = 5[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  string avatarFrame=6  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];
}

// 热门内容
message  GetLemonXSearchHotContentRequest {
  int32 size =1 [json_name="size",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数量"}];
}
message GetLemonXSearchHotContentReply {
  repeated GetLemonXSearchHotContentItem items=1 [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结果"}];
}
message GetLemonXSearchHotContentItem {
  int32 dataType = 1[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
  string postsId = 2[json_name="postsId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子ID"}];
  string title = 3[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  string image =4 [json_name="image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string applaudText =5  [json_name="applaudText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞文本"}];
}

//热门用户
message GetLemonXSearchRecommendUserRequest {
  int32 size =1 [json_name="size",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数量"}];
}
message GetLemonXSearchRecommendUserReply {
  repeated GetLemonXSearchRecommendUserItem items=1 [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结果"}];
}
message  GetLemonXSearchRecommendUserItem {
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string nickName = 2[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string vipIcon = 4[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string darenIcon = 5[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  string fansText=6 [json_name="fansText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"粉丝文本"}];
  int64 fans=7  [json_name="fans",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"粉丝数量"}];
  bool followFlag =8  [json_name="followFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关注标识"}];
  string avatarFrame=9  [json_name="avatarFrame",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像框"}];
}


//============================================中间模型=====================================/
// 社区综合模型，供推荐列表和关注列表转换使用
message CommunityConvertModel {
  int32 dataType = 1[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
  int32 isAd = 2[json_name="isAd",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否广告"}];
  AdData adInfo = 3[json_name="adInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告数据"}];
  SurveyData surveyData = 4[json_name="surveyData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实勘"}];
  ArticleData articleData=5 [json_name="articleData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文章"}];
  RegdisclosureData regDisclosureData = 6[json_name="regDisclosureData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"监管披露"}];
  MediateData mediateData = 7[json_name="mediateData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解"}];
  PostsData postsInfo = 8[json_name="postsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子"}];
  UserData userInfo = 9[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  repeated TraderData traderCol=10 [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商/服务商信息"}];
  bool commentFlag =11  [json_name="commentFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"废弃"}];
  int32 commentFlagStatus =12  [json_name="commentFlagStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论开关标识代码：0 none   1Write 2 Read 3readWrite"}];
  bool notShowFeedback = 13[json_name="notShowFeedback",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "不显示推荐反馈"}];
}

//============================================社区热帖=====================================/
message GetCommunityHotRequest {
  int64 size=1  [json_name="size",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"请求数量",default:"10"}];
}
message GetCommunityHotReply {
  message HotSign {
    int32 isShow = 1[json_name="isShow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否展示"}];
    string bgColor = 2[json_name="bgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"背景颜色"}];
    string word = 3[json_name="word",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"显示文字"}];
    string wordColor = 4[json_name="wordColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"字体颜色"}];
  }
  message GetCommunityHot {
    int32 dataType = 1[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
    string postsId = 2[json_name="postsId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子ID"}];
    string releaseType = 3[json_name="releaseType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型 1服务 2动态"}];
    string title = 4[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
    string showHotNumber = 5[json_name="showHotNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热度"}];
    string contentLanguage = 6[json_name="contentLanguage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:""}];
    HotSign hotSign = 7[json_name="hotSign",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热度标签"}];
    string userId = 8[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"userid"}];
  }
  repeated GetCommunityHot hots = 1[json_name="hots",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热帖"}];
  string ruleLink = 2 [json_name="ruleLink",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"规则链接"}];
}
message GetCommunityHotRuleReply {
  message CommunityHotRule {
    string ruleTitle = 1[json_name="ruleTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热帖规则标题"}];
    string ruleContent = 2[json_name="ruleContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热帖规则内容"}];
  }
  repeated  CommunityHotRule communityHotRules= 1[json_name="communityHotRules",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热帖规则"}];
}

//============================================话题=====================================/
message GetTopicRecommendReply{
  repeated TopicRecommend recommendCol=1[json_name="recommendCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "推荐话题"}];
}

message TopicRecommend{
  string prefix=1[json_name="prefix",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "前缀"}];
  string topicId=2[json_name="topicId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题编号"}];
  string content=3[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题内容"}];
  int32 viewCount=4[json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题浏览量"}];
  bool isShowViewCount=5[json_name="isShowViewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否显示话题浏览量"}];
}

message GetTopicDetailRequest{
  string userId=1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户编号"}];
  string topicId=2[json_name="topicId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题编号"}];
  int32 pageIndex=3[json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 pageSize=4[json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  string type=5[json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题帖子列表类型,002:热门,003:最新"}];
}

message GetTopicDetailReply{
  string prefix=1[json_name="prefix",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "前缀"}];
  string topicId=2[json_name="topicId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题编号"}];
  string content=3[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题内容"}];
  string introduction=4[json_name="introduction",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "简介"}];
  string backgroudImage=5[json_name="backgroudImage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "背景"}];
  string viewCount=6[json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览量"}];
  string participantCount=7[json_name="participantCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "参与人数"}];
  int32 postsCount=8[json_name="postsCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "话题帖子数量"}];
  repeated  TopicPostsData postsCol=9[json_name="postsCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "帖子"}];
  bool collected=10[json_name="collected",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否收藏"}];
  bool isShowViewCount=11[json_name="isShowViewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否显示话题浏览量"}];
  bool isShowParticipantCount=12[json_name="isShowParticipantCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否显示话题参与人数"}];

}

message TopicPostsData{
  PostsData postsInfo = 1[json_name="postsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子"}];
  UserData userInfo = 2[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  int32 dataType = 3[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
}
//============================================话题=====================================/

message PostsTopicItems{
  int32 type=1[json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型： 1 @ 、2 话题"}];
  string id=2[json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"type为1时：用户id，为2时：话题id"}];
  string name=3[json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"type为1时：用户昵称，为2时：话题名称"}];
  bool enable=4[json_name="enable",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否启用"}];
}

message GetCommunityTopicCollectListRequest{
  int32 pageIndex=1[json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:""}];
  int32 pageSize=2[json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:""}];
}

message GetCommunityTopicCollectListReply{
  repeated  GetUserTopicCollectListReplyItem list=1[json_name="list",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:""}];
}

message  GetUserTopicCollectListReplyItem{
  string topicId=1[json_name="topicId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"话题Id"}];
  string topicName=2[json_name="topicName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"话题内容"}];
  string  viewCount=3[json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"浏览数"}];
  string  userCount=4[json_name="userCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"参与人数"}];
  bool isShowViewCount=5[json_name="isShowViewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否显示话题浏览量"}];
  bool isShowParticipantCount=6[json_name="isShowParticipantCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否显示话题参与人数"}];
}

//===========================年度报告=====================================
message YearlyReportReply {
  ReportUserRegister userRegister = 1[json_name="userRegister",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户注册信息"}];
  ReportOverallReview overallReview = 2[json_name="overallReview",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总回顾"}];
  ReportQuoteStatistic quoteStatistic = 3[json_name="quoteData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"行情数据统计"}];
  ReportTraderInfo reportTrader = 4[json_name="reportTrader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"查看过交易商页面"}];
  ReportServiceProviderInfo reportServiceProvider = 5[json_name="reportServiceProvider",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"查看过服务商页面"}];
  ReportSearch reportSearch = 6[json_name="reportSearch",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索页面"}];
  ReportNews reportNews = 7[json_name="reportNews",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"专业资讯页面"}];
  ReportCommunity reportCommunity = 8[json_name="reportCommunity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"社区相关页面"}];
  ReportPostInfo reportPostInfo = 9[json_name="reportPostInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子信息页面"}];
  ReportMediate reportMediate = 10[json_name="reportMediate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解页面"}];
  ReportQuoteData reportQuoteData = 11[json_name="reportQuoteData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"行情页面"}];
  ReportMockTrader reportMockTrader = 12[json_name="reportMockTrader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"模拟交易页面"}];
  ReportResultReply reportResult = 13[json_name="reportResult",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"最终结果页面"}];
}
message ReportUserRegister {
  int64 register_date = 1[json_name="register_date",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册日期"}];
  int64 register_days = 2[json_name="register_days",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册天数"}];
  string nickName = 3[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
}
message ReportOverallReview {
  string new_trader = 1[json_name="new_trader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的交易商数量"}];
  string new_service_provider = 2[json_name="new_service_provider",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的服务商数量"}];
  string new_license = 3[json_name="new_license",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的牌照数量"}];
  string new_survey = 4[json_name="new_survey",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的实勘数量"}];
  string new_article = 5[json_name="new_article",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增新闻/资讯数量"}];
  string new_exposure = 6[json_name="new_exposure",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增曝光数量"}];
  string new_disclosure = 7[json_name="new_disclosure",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增披露数量"}];
  int64 new_disclosure_rel_trader = 8[json_name="new_disclosure_rel_trader",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新增的披露涉及的交易商数量"}];
  string mediate_person = 9[json_name="mediate_person",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "调解人数"}];
  float mediate_amount = 10[json_name="mediate_amount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "调解解决金额"}];
}
message ReportQuoteStatistic {
  int64 quote_count = 1[json_name="mediate_amount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "外汇行情数量"}];
  int64 up_quote_count = 2[json_name="up_quote_count",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "上涨外汇行情数量"}];
  int64 fall_quote_count = 3[json_name="fall_quote_count",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下跌外汇行情数量"}];
  repeated Quote quotes = 4[json_name="quotes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "行情数量列表"}];
}

message Quote {
  string image = 1[json_name="image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片"}];
  string symbol = 2[json_name="symbol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "品类"}];
  float rate = 3[json_name="rate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "上涨下跌率"}];
  float max_price = 4[json_name="max_price",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高价格"}];
  int64 max_price_timestamp = 5[json_name="max_price_timestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最高价格的时间"}];
  float min_price = 6[json_name="min_price",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低价格"}];
  int64 min_price_timestamp = 7[json_name="min_price_timestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低价格的时间"}];
}

message ReportTraderInfo {
  int32 showType = 1[json_name="showType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "showType:1、已查看交易商列表；2、交易商天眼排行"}];
  repeated ReportTrader reportTraders = 2[json_name="reportTraders",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商集合"}];
  int32 traderCount = 3[json_name="traderCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看交易商数量"}];
}
message ReportTrader {
  string logo = 1[json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string name = 2[json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商名称"}];
  string englishShortName = 3[json_name="englishShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文简称"}];
  string localShortName = 4[json_name="localShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言简称"}];
  int64 viewCount = 5[json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看次数"}];
  string code = 6[json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易商code"}];
  string score = 7[json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分"}];
  string annotation = 8[json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "附注"}];
  repeated LabelItem labels = 9[json_name="labels",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "labels"}];
  string color = 10[json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "color"}];
  string icon = 11[json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "icon"}];

}
message ReportServiceProviderInfo {
  repeated ReportServiceProvider reportServiceProviders = 1[json_name="reportServiceProviders",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "服务商列表"}];
  int32 serviceProviderCount = 2[json_name="serviceProviderCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看服务商数量"}];
}

message ReportServiceProvider {
  string logo = 1[json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "logo"}];
  string name = 2[json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商名称"}];
  string englishShortName = 3[json_name="englishShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"英文简称"}];
  string localShortName = 4[json_name="localShortName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"当前语言简称"}];
  int64 viewCount = 5[json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看次数"}];
  string code = 6[json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "code"}];
  string score = 7[json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分"}];
  double scoreLevel = 8[json_name="scoreLevel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "评分等级"}];
  string icon = 9[json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "icon"}];

}

message ReportSearch {
  int64 useSearchCount = 1[json_name="useSearchCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "使用搜索功能次数"}];
  string mostUsedSearchKeyword = 2[json_name="mostUsedSearchKeyword",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "常用搜索词"}];
  repeated string interestedKeywords = 3[json_name="interestedKeywords",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "搜索词列表"}];
  string interestedSearchKeywordUserText = 4[json_name="interestedSearchKeywordUserText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "全员搜索词用户量"}];
  repeated string keywords = 5[json_name="keywords",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "全员搜索词列表"}];
}

message ReportNews {
  int64 viewArticleCount = 1[json_name="viewArticleCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看过的文章数量"}];
  repeated ReportCountryInfo reportCountryData = 2[json_name="reportCountryData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的国家经纬度"}];
  string interestedCountry = 3[json_name="interestedCountry",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最感兴趣的国家"}];
}

message ReportCountryInfo {
  string countryCode = 1[json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的国家code"}];
  string longitude = 2[json_name="longitude",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的国家code"}];
  string latitude = 3[json_name="latitude",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的国家code"}];
}

message ReportCommunity {
  int64 postsCount = 1[json_name="postsCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发帖数量"}];
  int64 viewPostsCount = 2[json_name="viewPostsCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览帖子数量"}];
  int64 viewExposureCount = 3[json_name="viewExposureCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览的曝光数量"}];
}

message ReportPostInfo {
  string firstPostId = 1[json_name="firstPostId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子ID"}];
  int64 firstPostTimestamp = 2[json_name="firstPostTimestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子发布时间"}];
  int64 firstPostActionCount = 3[json_name="firstPostActionCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子分享，点赞，评论的总数"}];
  repeated YearlyReportUserObject firstPostViewUserList = 4[json_name="firstPostViewUserList",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第一篇帖子查看用户列表"}];
  string viewPostsUserCount = 5[json_name="viewPostsUserCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看帖子的用户数"}];
  string actionUserCount = 6[json_name="actionUserCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "有过交互行为的用户数"}];
  string communityUserText = 7[json_name="communityUserText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "社区用户总量"}];
}

message YearlyReportUserObject {
  ReportUser user = 1[json_name="user",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户信息"}];
  int64 viewCount = 2[json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览次数"}];
  string fans = 3[json_name="fans",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "粉丝数"}];
}

message ReportUser {
  string nickName = 1[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string nickNameColor = 2[json_name="nickNameColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称颜色"}];
  string avatarAddress = 3[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string vipIcon = 4[json_name="vipIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"VIP Icon"}];
  string userId = 5[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string darenIcon = 6[json_name="darenIcon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"达人Icon"}];
  int32 rightLabelType = 7[json_name="rightLabelType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户类型 1企业号 2员工 3 个人 4 kol"}];
  OfficialNumberType officialNumber=8 [json_name="officialNumber",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"官方号类型1 交易商号 6 服务商号 2 天眼调解   3 WikiFX-新闻  4; WikiFX-快讯    5 WikiFX-实勘 7 监管机构号 8个人"}];
}
message ReportMediate {
  int64 mediateCount = 1[json_name="mediateCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发起调解次数"}];
  float mediateAmount = 2[json_name="mediateAmount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "调解总金额"}];
}

message ReportQuoteData {
  Quote interestedQuote = 1[json_name="interestedQuote",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的外汇行情"}];
  int64 interestedQuoteViewCount = 2[json_name="interestedQuoteViewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "感兴趣的外汇行情查看次数"}];
  repeated Quote yearlyIncTop2 = 3[json_name="yearlyIncTop2",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易品种年涨top2"}];
  Quote yearlyDescTop1 = 4[json_name="yearlyDescTop1",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易品种年跌top1"}];
  Quote yearlyDurableIncTop1 = 5[json_name="yearlyDurableIncTop1",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "交易品种连涨top1"}];
}

message ReportMockTrader {
  int64 mockTraderCount = 1[json_name="mockTraderCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易次数"}];
  float mockTraderAmount = 2[json_name="mockTraderAmount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易金额"}];
  string mockTraderRevenue = 3[json_name="mockTraderRevenue",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易全年营收"}];
  Quote mockTradeMostQuote = 4[json_name="mockTradeMostQuote",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易最多的品种"}];
}

message ReportResultReply {
  ReportUser user = 1[json_name="user",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户信息"}];
  int32 tag = 2[json_name="tag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "标签：1安全演练家、2外汇小百科、3社区顶流、4维权超人、5新闻小灵通、6真相守护者、7搜词冲浪者、8吃瓜小能手、9机会守望者、10安全交易大使、11外汇界索罗斯、12投资界巴菲特、13被Flipper庇护者、14粉丝收割机"}];
  int64 viewTraderCount = 3[json_name="viewTraderCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "浏览交易商列表"}];
  int64 viewArticleCount = 4[json_name="viewArticleCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看过的文章数量"}];
  float mockTradeAmount = 5[json_name="mockTradeAmount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易金额"}];
  Quote mockTradeMostQuote = 6[json_name="mockTradeMostQuote",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "模拟交易最多的品种"}];
  int64 postsCount = 7[json_name="postsCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "发帖数量"}];
  int64 register_days = 8[json_name="register_days",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "注册天数"}];
}

message GetStsReply{
  string accessKeyId=1 [json_name="accessKeyId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"访问key"}];
  string accessKeySecret=2 [json_name="accessKeySecret",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"访问密钥"}];
  string securityToken=3 [json_name="securityToken",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"令牌"}];
  string expiration=4 [json_name="expiration",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"过期时间"}];
}
message ActivityListRequest{
  int32 pageIndex=1 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}
message ActivityListReply{
  repeated ActivityListItemReply list=1[json_name="list",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"列表"}];
}
message ActivityListItemReply{
  string activityId=1 [json_name="activityId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动Id"}];
  string title=2 [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  string intro=3 [json_name="intro",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动简介"}];
  string startTime=4 [json_name="startTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开始时间"}];
  string endTime=5 [json_name="endTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结束时间"}];
  int32 status=6 [json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动状态 0 未开始 1进行中 2 已结束"}];
  string statusContent=7 [json_name="statusContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动状态"}];
  string statusBgColor=8 [json_name="statusBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"状态背景色"}];
  string statusColor=9 [json_name="statusColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"状态字体颜色"}];
  string Image=10 [json_name="Image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
}

// 活动详情
message ActivityDetailRequest{
  string activityId=1[json_name="activityId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动Id"}];
  int32 pageIndex=2[json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:""}];
  int32 pageSize=3[json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:""}];

}

message ActivityDetailReply{
  string activityId=1[json_name="activityId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动Id"}];
  string title=2[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动标题"}];
  string intro=3[json_name="intro",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动简介"}];
  string startTime=4[json_name="startTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开始时间"}];
  string endTime=5[json_name="endTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"结束时间"}];//
  int32 status=6[json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动状态 0 未开始 1进行中 2 已结束"}];
  string statusContent=7[json_name="statusContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动状态"}];
  string statusBgColor=8[json_name="statusBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"状态背景色"}];
  string statusColor=9[json_name="statusColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"状态字体颜色"}];
  string content=10[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动内容"}];
  string joinCount=11[json_name="joinCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"参数人数"}];
  repeated string JoinUserPhoto=12[json_name="JoinUserPhoto",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"参与人数头像"}];
  string Image=13[json_name="Image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
  string TopicId=14[json_name="TopicId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"话题Id"}];
  string TopicName=15[json_name="TopicName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"话题名称"}];
  int32 LinkType=16[json_name="LinkType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转类型  1发布帖子 2外链 3内链 4交易商详情页5直播6排行榜7实盘"}];
  string LinkContent=17[json_name="LinkContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转内容"}];
  bool IsWonderful=18[json_name="IsWonderful",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否是精彩活动"}];
  string LinkAddress=19[json_name="LinkAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转地址"}];
  repeated ActivityPostsData Posts=20[json_name="Posts",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子信息"}];
  string AreaCode=21[json_name="AreaCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"区域code"}];
  string LanguageCode=22[json_name="LanguageCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"语言"}];
  int32 LinkAddressType=23 [json_name="LinkAddressType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1内链 2外链"}];
}
message ActivityPostsData{//精彩活动返回
  PostsData postsInfo = 1[json_name="postsInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子"}];
  UserData userInfo = 2[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
  int32 dataType = 3[json_name="dataType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"数据类型, 1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)"}];
}

//参与活动
message UserJoinActivityRequest{
  string activityId=1[json_name="activityId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动Id"}];
}
message EmptyResponse {}

message ActivityPostsPageListRequest{
  int32 pageIndex=1[json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2[json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}


//============================================明星排行榜=====================================/
message GetPopularCreatorRequest{
  string userId=1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户编号"}];
  string rankId=2[json_name="rankId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"榜单id"}];
  int32 category=3[json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类"}];
}

message GetPopularCreatorReply{
  repeated PopularCreator rankCol=1[json_name="rankCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"排名列表"}];
  PopularCreator profile=2[json_name="profile",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"个人信息"}];
  string rankId=3[json_name="rankId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"榜单id"}];
  int32 category=4[json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类"}];
  string shareTitle=5[json_name="shareTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享标题"}];
  string shareContent=6[json_name="shareContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享内容"}];
  string shareImage=7[json_name="shareImage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享图片"}];
  string shareUrl=8[json_name="shareUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享下载链接"}];
}
message PopularCreator{
  UserData userInfo=1[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  string rank=2[json_name="rank",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"排名"}];
  string icon=3[json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图标"}];
  int32 popularity=4[json_name="popularity",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热度"}];
  int32 gold=5[json_name="gold",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"送金币数量"}];
  int32 follow=6[json_name="follow",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否关注,0:没有关注1:已关注"}];
  int32 attentionStauts=7  [json_name="attentionStauts",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1未关注 2 已关注 3相互关注   4自己"}];
  string popularityDisplay=8[json_name="popularityDisplay",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"热度显示"}];
}
//============================================明星排行榜=====================================/

//============================================Wiki安全大师推广=====================================/
//获取邀请奖励弹窗数据
message GetInvitationPopupDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetInvitationPopupDataReply{
  string title=1[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  UserData userData=2[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  repeated string contentCol = 3[json_name="contentCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容字符串数组"}];
}
//获取邀请奖励领取banner数据
message GetInviteRewardBannerDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetInviteRewardBannerDataReply{
  string title=1[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  UserData userData=2[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  repeated string contentCol = 3[json_name="contentCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容字符串数组"}];
  string vpsVerifyImgUrl=4[json_name="vpsVerifyImgUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps图片验证Url"}];
  string vpsVerifyContent=5 [json_name="vpsVerifyContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps验证按钮多语言内容"}];
  string vpsVerifyBgColor=6  [json_name="vpsVerifyBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps验证按钮背景色"}];
}
//获取分享推广链接数据
message GetShareLinkDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetShareLinkDataReply{
  message ShareLinkData{
    string shareUrl = 1[json_name="shareUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享链接url"}];
    string title = 2[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
    repeated string contentCol = 3[json_name="contentCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容字符串数组"}];
    string imgUrl = 4[json_name="imgUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享图片url"}];
  }
  UserData userData=1[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  ShareLinkData shareLinkData=2[json_name="shareLinkData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享链接数据"}];
}
//获取邀请用户数据
message GetInvitedUserDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  int32 pageIndex=2 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=3 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}
message GetInvitedUserDataReply{
  repeated  UserData userDataCol=1[json_name="userDataCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息集合"}];
}
//获取邀请记录数据
message GetInvitedRecordDataRequest{
  int32 pageIndex=1 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}
message GetInvitedRecordDataReply{
  message InvitedRecordData{
      UserData userData=1[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
      int32 bindingStatus=2[json_name="bindingStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"绑定状态 0 未绑定 1已绑定"}];
      string updateTime=3[json_name="updateTimeupdateTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"更新时间"}];
  }
  //邀请总人数
  int32 invitedCount=1[json_name="invitedCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请总人数"}];
  //升级次数
  int32 upgradedCount=2[json_name="upgradedCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级次数"}];
  repeated InvitedRecordData invitedRecordDataCol=3[json_name="invitedRecordDataCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请记录数据集合"}];
}

//============================================Wiki安全大师推广=====================================/