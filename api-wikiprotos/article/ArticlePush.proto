syntax = "proto3";
package ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts;

option go_package="api/articlepush/v1;v1";
message ArticleBelongToCodeResult { // 文章所属code信息
   string Code = 1; // 所属交易商或服务商code
   RelationTraderType RelationType = 2; // 相关类型，交易商或服务商
   string CateName = 3; // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
}
message ArticleByCodesQuery { // 文章内容查询
   repeated string ArtCodes = 1;
   bool IsRequiredRelationTrader = 2; // 是否需要相关交易商
}
message ArticleByCodesResult { // 文章查询结果
   repeated ArticleList Articles = 1;
}
message ArticleDetail { // 文章详情
   string ArtCode = 1; // 文章Code
   string CategoryId = 2; // 文章分类编码
   string Title = 3; // 标题
   string Summary = 4; // 摘要
   string Content = 5; // 正文
   string LanguageId = 6; // 文章语言
   string Tag = 7; // tag
   string Icon = 8; // icon
   string Share = 9; // 文章分享页地址
   int64 Timestamp = 10; // 显示日期时间戳
   string ArticleBelongToCode = 11; // 文章所属交易商
   repeated ArticleRelation Associations = 12;
   bool IsWhiteList = 13; // 是否白名单
   string ArtKeyword = 14; // 关键词
   string ArtImageUrl = 15; // 头图
   bool IsShow = 16; // 是否显示
}
message ArticleDetailQuery { // 文章详情查询
   string ArtCode = 1; // 文章Code
}
message ArticleList { // 文章列表信息
   string ArtCode = 1; // 文章Code
   string Title = 2; // 标题
   ImageInfo Banner = 3; // 头图
   int64 Timestamp = 4; // 显示日期时间戳
   string LanguageId = 5; // 文章语言
   ImageInfo FirstImage = 6; // 内容里面的第一张图
   string CategoryId = 7; // 文章类型
   string ArticleBelongToCode = 8; // 文章所属交易商Code
   repeated string RelationCodes = 9;
   string Summary = 10; // 摘要
   string TextPart = 11; // 部分正文内容，暂时取120个字，web端使用
   string HitsInfo = 12; // 统计信息
}
message ArticleQuery100Request { // 查询文章100
   string CateName = 1; // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
   bool IsRequiredRelationTrader = 2; // 是否需要相关交易商
}
message ArticleQueryCountRequest { // 查询文章总数
   string CateName = 1; // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
   bool IsHomePage = 2; // 是否首页
}
message ArticleQueryRequest { // 查询文章请求
   string CateName = 1; // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
   int32 Pageindex = 2; // 页数
   int32 PageSize = 3; // 页大小
   bool IsRequiredRelationTrader = 4; // 是否需要相关交易商
   bool IsHomePage = 5; // 是否首页
}
message ArticleRelation { // 文章交易商关系
   RelationType ArtRelRelationType = 1; // 文章关联类型（所属或相关）
   string Code = 2; // 关联交易商或代理商id
   RelationTraderType ArtRelTraderType = 3; // 关联机构类型（平台，交易商，代理商）
}
message ContainerData_ArticleList {
   int64 Total = 1;
   repeated ArticleList Items = 2;
}
message ImageInfo { // 图片信息
   string Url = 1; // 图片url
   int32 Width = 2; // 宽
   int32 Height = 3; // 高
   int32 ImageType = 4; // 图片类型
}
message QueryBelongToCodeRequest { // 查询文章所属Code
   string ArtCode = 1; // 文章Code
}
enum RelationTraderType {
   RelationTraderType_None = 0; // 未知
   RelationTraderType_Trader = 1; // 交易商
   RelationTraderType_Agent = 3; // 代理商
   RelationTraderType_Dealer = 4; // 受评方
   RelationTraderType_ServiceProvider = 5; // 服务商
   RelationTraderType_Stock = 6; // 券商
}
enum RelationType {
   RelationType_None = 0; // 未知
   RelationType_BelongTo = 1; // 文章所属交易商
   RelationType_Related = 2; // 文章关联交易商/代理商
   RelationType_ServiceProvider = 3; // 文章关联服务商
}
message UnityReply_ArticleBelongToCodeResult { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   ArticleBelongToCodeResult Result = 3; // 返回结果
}
message UnityReply_ArticleByCodesResult { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   ArticleByCodesResult Result = 3; // 返回结果
}
message UnityReply_ArticleDetail { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   ArticleDetail Result = 3; // 返回结果
}
message UnityReply_ContainerData_ArticleList { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   ContainerData_ArticleList Result = 3; // 返回结果
}
message UnityReply_Int32 { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   int32 Result = 3; // 返回结果
}
service ArticlePushService {
   rpc QueryArticle (ArticleDetailQuery) returns (UnityReply_ArticleDetail); // 查询文章列表
   rpc QueryArticleByCodes (ArticleByCodesQuery) returns (UnityReply_ArticleByCodesResult); // 多Code查询文章信息
   rpc QueryArticleCount (ArticleQueryCountRequest) returns (UnityReply_Int32); // 查询文章总数
   rpc QueryArticleList (ArticleQueryRequest) returns (UnityReply_ContainerData_ArticleList); // 查询文章列表
   rpc QueryArticleTop100 (ArticleQuery100Request) returns (UnityReply_ArticleByCodesResult); // 查询前100文章，测试用
   rpc QueryBelongToCodeByArtCode (QueryBelongToCodeRequest) returns (UnityReply_ArticleBelongToCodeResult); // 查询文章关联交易商信息
}
