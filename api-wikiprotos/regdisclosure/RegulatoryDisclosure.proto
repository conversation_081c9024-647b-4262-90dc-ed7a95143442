syntax = "proto3";
package RegulatoryDisclosure.Core.Grpc.ProtoContracts;

option go_package="api/regulatorydisclosure/v1;v1";
message CategoryInfo { // 分类信息
   string CategoryName = 1; // 分类名称
   string CategoryId = 2; // 分类id
}
message ContainerData_RegulatoryDisclosureReply {
   int64 Total = 1;
   repeated RegulatoryDisclosureReply Items = 2;
}
message ImageInfo { // 图片信息
   string Url = 1; // url
   int32 ImageWidth = 2; // 图片宽度
   int32 ImageHeight = 3; // 图片高度
}
message RegulatoryDisclosureCountRequest {
   int32 Count = 1; // 数量
}
message RegulatoryDisclosureListByRegulatorCodeRequest {
   string RegulatorCode = 1; // 监管机构Code
   int32 PageIndex = 2; // 页码
   int32 PageSize = 3; // 数量
}
message RegulatoryDisclosureReply {
   string RegId = 1; // 标识
   repeated string Traders = 2; // 披露的交易商信息
   repeated string CopyTraders = 3; // 被克隆的交易商
   string RegulatorCode = 4; // 机构code
   TagInfo Tag = 5; // 标签
   CategoryInfo Category = 6; // 分类
   SummaryInfo Summary = 7; // 摘要信息
   string Title = 8; // 标题
   string Content = 9; // 内容
   string Seal = 10; // 章
   ImageInfo Image = 11; // 图片
   string ContentLanguage = 12; // 内容语言
}
message RegulatoryDisclosureRequest {
   repeated string RegulatoryDisclosureIdList = 1; // 监管披露Id集合
}
message RegulatoryInfoReply { // 监管机构信息
   string RegulatorCode = 1; // 机构code
}
message RegulatoryInfoRequest {
   string RegulatoryDisclosureId = 1; // 监管披露Id
}
message SummaryInfo { // 摘要信息
   int32 Rule = 1; // 匹配规则
   string RuleName = 2; // 规则名称
   int64 wiki_timestamp = 3; // 披露时间戳
   string PenaltyAmount = 4; // 处罚金额(完整)
   string AmountSymbol = 5; // 处罚金额符号
   string Reason = 6; // 处罚原因
}
message TagInfo { // 标签信息
   string TagName = 1; // 标签名称
   string Color = 2; // 颜色
   string TagCode = 3; // code
   string LanguageCode = 4; // 语言code
   string TagLogo = 5; // logo
}
message UnityReply_ContainerData_RegulatoryDisclosureReply { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   ContainerData_RegulatoryDisclosureReply Result = 3; // 返回结果
}
message UnityReply_List_RegulatoryDisclosureReply { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   repeated RegulatoryDisclosureReply Result = 3; // 返回结果
}
message UnityReply_RegulatoryInfoReply { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   RegulatoryInfoReply Result = 3; // 返回结果
}
service RegulatoryDisclosureService {
   rpc GetRegulatoryCode (RegulatoryInfoRequest) returns (UnityReply_RegulatoryInfoReply); // 获取监管机构
   rpc GetRegulatoryDisclosureList (RegulatoryDisclosureRequest) returns (UnityReply_List_RegulatoryDisclosureReply); // 获取监管披露(id集合)
   rpc GetRegulatoryDisclosureListByRegulatorCode (RegulatoryDisclosureListByRegulatorCodeRequest) returns (UnityReply_ContainerData_RegulatoryDisclosureReply); // 根据监管机构获取监管披露
   rpc GetRegulatoryDisclosureListTop100 (RegulatoryDisclosureCountRequest) returns (UnityReply_List_RegulatoryDisclosureReply); // 获取监管披露前100
}
