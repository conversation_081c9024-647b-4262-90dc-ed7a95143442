syntax = "proto3";

package api.news.v1;

option go_package = "api/news/v1;v1";

message GetNewsByIdRequest {
  string id = 1;
}
message NewsInfo {
  string id = 1; // 新闻ID
  int64 action = 3; // 0 --- 新增   1 --- 修改
  int64 important = 4; // 重要性  1 --- 低   2 --- 中  3 --- 高
  int64 news_time = 5; // 中文（快讯时间 北京时间减去8小时=格林尼治时间 推送的时间戳也是格林尼治时间）；英文（注意：这里是东八区时间，需要加8小时才是北京时间）
  string news_title = 6; // title
  int64 update_time = 7; // 更新时间
  int64 new_type = 8; // 中文特有字段（类型：1=快讯 2=财经日历）
  string scountry = 9; // 英文特有字段（国家）
  string scategory = 10; // 英文特有字段
  string news_description = 11; // 英文特有字段
  string image = 12; // 图片地址
  int64 image_width = 13; // 图片宽
  int64 image_height = 14; // 图片高
  bool label_is_show = 15; // 标签是否显示
  bool label = 16; // 标签
  bool label_bg_color = 17; // 标签背景色
}

message FindNewsRequest {
  int64 size = 1;
  int64 page = 2;
}
message FindNewsReply {
  repeated NewsInfo items = 1;
  int64 total = 2;
}

message FindNewsByIdsRequest {
  repeated string ids = 1;
}
message FindNewsByIdsReply {
  repeated NewsInfo items = 1;
}
