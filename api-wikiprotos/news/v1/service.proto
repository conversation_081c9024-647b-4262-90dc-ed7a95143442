syntax = "proto3";

package api.news.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "news/v1/models.proto";

option go_package = "api/news/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  // 获取快讯详情
  rpc GetNewsById(GetNewsByIdRequest) returns (NewsInfo){
    option (google.api.http) = {get: "/v1/news"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取快讯详情",tags: ["快讯"]};
  }
  // 获取快讯列表
  rpc FindNews(FindNewsRequest) returns (FindNewsReply) {
    option (google.api.http) = {get: "/v1/news/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取快讯列表",tags: ["快讯"]};
  }
  // 批量获取快讯列表
  rpc FindNewsByIds(FindNewsByIdsRequest) returns (FindNewsByIdsReply) {
    option (google.api.http) = {post: "/v1/news/batch" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "批量获取快讯列表",tags: ["快讯"]};
  }
}
