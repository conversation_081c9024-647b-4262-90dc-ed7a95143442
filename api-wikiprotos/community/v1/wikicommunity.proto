syntax = "proto3";

package api.community.v1;
import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "google/protobuf/any.proto";

option go_package = "api-community/api/community/v1;v1";

service WikiCommunity {
  //弃用
  rpc GetPostsInfo (GetPostsRequest) returns (GetPostsReply){
    option (google.api.http)={post:"/v1/app/community/getpostsinfo",body:"*"};
  }
  // 帖子收藏
  rpc GetPostsApplaud (GetPostsApplaudRequest) returns (GetPostsApplaudReply){
    option (google.api.http)={post:"/v1/app/community/GetPostsApplaud",body:"*"};
  }

  rpc GetSinglePosts (GetSinglePostsRequest) returns (GetPostsReplyItem){
    option (google.api.http)={get:"/v1/app/community/GetSinglePosts"};
  }
  //获取商业或者评论数据
  rpc GetPostsOrCommentGradesInfo (GetPostsOrCommentGradesInfoRequest) returns (GetPostsReply){
    option (google.api.http)={post:"/v1/app/community/getpostsorcommentgradesinfo",body:"*"};
  }

  //获取帖子收藏和点赞
  rpc GetPostsApplaudAndCollect (GetPostsApplaudRequest) returns (GetPostsApplaudAndCollectReply){
    option (google.api.http)={post:"/v1/app/community/getpostsapplaudandcollect",body:"*"};
  }
  rpc GetUserCollectPosts (GetUserCollectPostsRequest) returns (GetUserCollectPostsReply){
    option (google.api.http)={get:"/v1/app/community/getusercollectposts"};
  }

  //根据帖子Id获取用户
  rpc GetUserIdByPostsId (GetUserIdByPostsIdRequest) returns (GetUserIdByPostsIdReply){
    option (google.api.http)={get:"/v1/app/community/getuseridbypostsid"};
  }
  // 获取用户帖子数量
  rpc GetPostsNumber (GetPostsNumberRequest) returns (GetPostsNumberReply){
    option (google.api.http)={get:"/v1/app/community/getpostsnumber"};
    
  }

  // 用户用户个人
  rpc  GetPersonHomePostsPageList (GetPersonHomePostsPageListRequest) returns ( GetPersonHomePostsPageListReply){
    option (google.api.http)={get:"/v1/app/community/getpersonhomepostspagelist"};
  }


  //用户浏览历史
  rpc GetUserViewHistoryPosts (GetUserViewHistoryPostsRequest) returns (GetUserViewHistoryPostsReply){
    option (google.api.http)={get:"/v1/app/community/getuserviewhistoryposts"};
  }

  //获取帖子用户点赞数据
  rpc GetPostsUserApplaud (GetUserPostsApplaudRequest) returns (GetUserPostsApplaudReply){
    option (google.api.http)={post:"/v1/app/community/getpostsuserapplaud",body:"*"};
  }

  rpc GetPostsReplyStatus (GetPostsReplyStatusRequest) returns (GetPostsReplyStatusReply){
    option (google.api.http)={post:"/v1/app/community/getpostsreplystatus",body:"*"};
  }

  rpc GetTopicRecommend (GetTopicRecommendRequest) returns (GetTopicRecommendReply){
    option (google.api.http)={get:"/v1/app/community/gettopicrecommend"};
  }

  rpc GetTopicDetail (GetTopicDetailRequest) returns (GetTopicDetailReply){
    option (google.api.http)={get:"/v1/app/community/gettopicdetail"};
  }
  //GetUserTopicCollectList 用户收藏话题
  rpc GetUserTopicCollectList (GetUserTopicCollectListRequest) returns (GetUserTopicCollectListReply){
    option (google.api.http)={get:"/v1/app/community/getusercollectlist"};
  }
  // 获取用户帖子数量和获赞数量
  rpc GetUserBusinessCountOrApplaudCount (GetUserBusinessCountOrApplaudCountRequest) returns (GetUserBusinessCountOrApplaudCountReply){
    option (google.api.http)={post:"/v1/app/community/getuserbusinesscountorapplaudcount",body:"*"};
  }

  //活动列表
  rpc ActivityPageList (ActivityListRequest) returns (ActivityListReply){
    option (google.api.http)={get:"/v1/app/community/activitypagelist"};
  }

  //活动详情
  rpc ActivityDetail (ActivityDetailRequest) returns (ActivityDetailReply){
    option (google.api.http)={get:"/v1/app/community/ActivityDetail"};
  }


  //活动广场
  rpc ActivityPostsPageList (ActivityPostsPageListRequest) returns (ActivityPostsPageListReply){
    option (google.api.http)={get:"/v1/app/community/activitypostspagelist"};
  }

  ///参数活动
  rpc UserJoinActivity (UserJoinActivityRequest) returns(EmptyResponse){
    option (google.api.http)={post:"/v1/app/community/userjoinactivity",body:"*"};
  }
  ///获取话题
  rpc GetTopicName (UserTopicNameRequest) returns(UserTopicNameReply){
    option (google.api.http)={get:"/v1/app/community/gettopicname"};
  }

  // 获取单个用户获赞数量
  rpc GetSingleUserApplaudCount (GetSingleUserApplaudCountRequest) returns (GetSingleUserApplaudCountReply){
    option (google.api.http)={get:"/v1/app/community/getsingleuserapplaudcount"};
  }




}

message GetPostsReplyStatusRequest{
  repeated string dataUserId=1;
}
message GetPostsReplyStatusReply{
  repeated GetPostsReplyStatusReplyItem list=1;
}
message GetPostsReplyStatusReplyItem{
  string dataUserId=1;
  int32 status=2; // 0 none   1Write 2 Read 3readWrite
}


message GetUserPostsApplaudRequest{
  repeated  string postsIds=1;
}
message  GetUserPostsApplaudReply{
  repeated  GetUserPostsApplaudReplyItem list=1;
}

message  GetUserPostsApplaudReplyItem{
  string postsId=1;
  string Number=2;
}

message  GetUserViewHistoryPostsRequest{
  string userLoginId=1;
  int32 releaseType=2;//1 商业 2 动态
  int32 pageIndex=3;
  int32 pageSize=4;
}


message  GetUserViewHistoryPostsReply{
  repeated  GetUserViewHistoryPostsItem list=1;
}


message GetUserViewHistoryPostsItem{
  int32 dataType=1; //    1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
  string postsId=2;
  int64 collectTime=3;//收藏时间

}


message GetPostsNumberRequest{
  string userId=1;//查看用户的userid
  string  userLoginId=2;//用户登录Id 可以为空
}

message GetPostsNumberReply{
  string businessNumber=1;//商业数量
  string dailyNumber=2;//动态数量
}



//通过帖子Id获取用户
message GetUserIdByPostsIdRequest{
  string postsId=1;
}
message GetUserIdByPostsIdReply{
  string userId=1;
}


message GetPersonHomePostsPageListRequest{//个人用户主页用户发布的动态和商业请求
  int32 pageIndex=1;
  int32 pageSize=2;
  int32 releaseType=3;
  string userLoginId=4;
  string  userId=5;
}
message GetPersonHomePostsPageListReply{//个人用户主页用户发布的动态和商业请求
  repeated  GetPostsReplyItem List=1;
}

//    1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)

// 用户收藏帖子请求
message GetUserCollectPostsRequest{
  string userLoginId=1;
  int32 releaseType=2;//1 商业 2 动态
  int32 pageIndex=3;
  int32 pageSize=4;
}
message GetUserCollectPostsReplyItem{
  int32 dataType=1; //    1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
  string postsId=2;
  int64 collectTime=3;//收藏时间
  string publicUserId=4;//发布用户userid

}
// 用户收藏帖子返回
message GetUserCollectPostsReply{

  repeated  GetUserCollectPostsReplyItem List =1;//返回结果
  int32 Total=2;
}


//获取商业或者评论数据
message  GetPostsOrCommentGradesInfoRequest{
  repeated string PostsIds=1; //商业Id
  repeated string CommentGradeIds=2; //评价Id
  string   userLoginId=3;//登录用户Id
  bool IsDoTitle=4;//是否处理title
}

message  GetSinglePostsRequest{
  string postsId=1;
  string userLoginId=2;
  string postsUserId=3;
}

message  GetPostsApplaudRequest{
  string userLoginId=1; //登录用户Id
  repeated  string postsIds=2;//帖子集合

}
message  GetPostsApplaudReply{
  repeated  string postsId=1;

}
message  GetPostsApplaudAndCollectReply{
  repeated  string Applauds=1;
  repeated  string Collects=2;
  repeated  GetPostsApplaudAndCollectReplyCount  ApplaudsCounts=3;
}
message GetPostsApplaudAndCollectReplyCount{
  string PostsId=1;
  int32 ApplaudNumber=2; //点赞数量
  int32 CommentNumber=3; //评论数量
  string ShowApplaudNumber=4; //展示点赞数量
  string ShowCommentNumber=5;//展示评论数量
  int32 CollectNumber=6; //收藏数量
  string ShowCollectNumber=7;//展示收藏数量
  bool isShowApplaudNumber=8; //是否展示点赞
  bool isShowCommentNumber=9; //是否展示评论
  bool isShowCollectNumber=10; //是否展示点赞
  bool isShowPlayTimes=11;//是否展示视频播放数量
  int32 playTimes=12;
  string showPlayTimes=13;

}

message GetPostsReply{
  repeated  GetPostsReplyItem Items=1;
}

message GetPostsRequest{
  //登录用户id
  string   userLoginId=1;
  repeated  string postsIds=2;
  bool isShowLoginUserNoAudit=3;//是否显示未审核的用户数据
}
message Image{
  string list=1;
  string detail=2;
  string url=3;
  int32 width=4;
  int32 height=5;
}
message PostsSign{ //角标
  int32  IsShow=1;
  string BgColor=2;
  string Word=3;
  string Icon=4;
  string Color=5;
}
message PostsTopicItems{
  int32 type=1;//// 1 @ 2 话题
  string id=2;
  string name=3;
  bool enable=4;
}
message GetPostsReplyItem{
  string postsId=1;
  int32 releaseType=2;//类型 1服务 2动态
  int64 publicTime=3;//展示时间
  string  title=4;
  string  titleNew=5;//没有处理的标题
  string content=6; //
  string  contentNew =7;// 内容新 逻辑处理没有处理的内容
  string refusalReason=8;//拒绝理由
  repeated Image images=9;//
  string postsCode=10;//动态code
  int32 applaudCount =11;//点赞数量
  string showApplaudCount=12;//展示点赞数量
  int32 collectCount=13;//收藏数量
  bool isCollect=14;//是否收藏
  int32 forwardCount=15; //分享数量;
  string showForwardCount=16;//显示分享数量
  int32 replyCount=17;//原始头像
  string showReplyCount=18;//显示评论数量
  bool isApplaud=19;//是否点赞
  string theme=20;// 服务类型
  string themeCode=21;//服务code
  string themeColor=22;//服务颜色
  string shareUrl=23;//分享url
  PostsSign Sign=24;
  string userId=25;
  string CountryName=26; //国家名称
  string ViewCount=27;//查看数量
  bool IsShowViewCount=28;//是否显示访问数量
  string enterpriseCode=29;//管理企业code
  int32 Grade=30;//评价等级 1好评 2中评 3 曝光
  int32 dataType=41;//  1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
  string contentLanguage=42;
  repeated PostsTopicItems postsTopicItems=43;
  bool isTop=44;//是否置顶
  string topContent=45; //置顶文字
  string topColor=46;//置顶颜色 置顶background: #FFFFFF;
  string topBgColor=47;//置顶背景颜色 background: #4E5969 80;
  string vodFileId=48;//视频地址
  bool hasVideo=49;//是否有视频

}


message GetTopicRecommendRequest{

}

message GetTopicRecommendReply{
  repeated TopicRecommend recommendCol=1;
}

message TopicRecommend{
  string Prefix=1;
  string TopicId=2;
  string Content=3;
  int32 ViewCount=4;
}

message GetTopicDetailRequest{
  string UserId=1;
  string TopicId=2;
  int32 pageIndex=3;
  int32 pageSize=4;
  int32 type=5; // 1:热门,2:最新
}

message GetTopicDetailReply{
  string Prefix=1;
  string TopicId=2;
  string Content=3;
  string Introduction=4;
  string BackgroudImage=5;
  int32 ViewCount=6;
  int32 ParticipantCount=7;
  int32 PostsCount=8;
  repeated  GetPostsReplyItem postsCol=9;
  bool Collected=10;
}


message GetUserTopicCollectListRequest{
  string userId=1;
  int32 pageIndex=2;
  int32 pageSize=3;
}

message GetUserTopicCollectListReply{
  repeated  GetUserTopicCollectListReplyItem list=1;
}

message  GetUserTopicCollectListReplyItem{
  string topicId=1;//话题Id
  string topicName=2;//话题内容
  string ViewCount=3;//浏览数
  string UserCount=4;//参与人数
  bool IsShowViewCount=5;//是否展示浏览数
  bool IsShowUserCount=6; //是否展示参与数量
}

message   GetUserBusinessCountOrApplaudCountRequest{
     repeated string userIds=1;//用户Id
}

message GetUserBusinessCountOrApplaudCountReplyItem{
  int32 postsCount=1;//商业数量
  int32 applaudCount=2;//获赞数量
  string userId=3;
}

message  GetUserBusinessCountOrApplaudCountReply{
  repeated  GetUserBusinessCountOrApplaudCountReplyItem  list=1;
}

//活动列表
message ActivityListRequest{
     int32 pageIndex=1;
     int32 pageSize=2;
}

message ActivityListReply{
  repeated ActivityListItemReply list=1;
}
message ActivityListItemReply{
  string activityId=1; //活动Id
  string title=2;
  string intro=3;//活动简介
  string startTime=4;//开始时间
  string endTime=5;//结束时间
  int32 status=6;//活动状态 0 未开始 1进行中 2 已结束
  string statusContent=7;//活动状态
  string statusBgColor=8; //状态背景色
  string statusColor=9; //状态字体颜色
  string Image=10;//图片
}


//活动详情
message ActivityDetailRequest{
  string activityId=1;//活动Id
  int32 pageIndex=2;
  int32 pageSize=3;
}

message ActivityDetailReply{
  string activityId=1; //活动Id
  string title=2;
  string intro=3;//活动简介
  string startTime=4;//开始时间
  string endTime=5;//结束时间
  int32 status=6;//活动状态 0 未开始 1进行中 2 已结束
  string statusContent=7;//活动状态
  string statusBgColor=8; //状态背景色
  string statusColor=9; //状态字体颜色
  string content=10;//活动内容
  string joinCount=11;//参数人数
  repeated string JoinUserPhoto=12; //参与人数头像
  string Image=13;//图片
  string TopicId=14;// 话题Id
  string TopicName=15;//话题名称
  int32 LinkType=16; //跳转类型  1发布帖子 2外链 3内链 4交易商详情页5直播6排行榜7实盘
  string LinkContent=17;//跳转内容,
  bool IsWonderful=18;//是否是精彩活动
  string LinkAddress=19;//跳转地址
  repeated  GetPostsReplyItem postsCol=20;
  string AreaCode=21;//区域Code
  string LanguageCode=22; //语言
  int32 LinkAddressType=23; //1内链 2外链
}

message ActivityPostsPageListRequest{
   int32 pageIndex=1;
   int32 pageSize=2;
}

message ActivityPostsPageListReply{
  repeated  GetPostsReplyItem Items=1;
}

message UserJoinActivityRequest{
  string activityId=1; //活动Id
  string AvatarAddress=2;//参与用户的头像
}
message EmptyResponse {}


message UserTopicNameRequest{
  string topicId=1;
}

message UserTopicNameReply{
  string topicName=1;
}

message GetSingleUserApplaudCountRequest{
  string UserId=1;
}
message GetSingleUserApplaudCountReply{
  int64 ApplaudNumber=1;
}