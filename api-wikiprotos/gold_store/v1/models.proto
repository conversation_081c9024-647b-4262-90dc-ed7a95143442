syntax = "proto3";

package api.gold_store.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";

option go_package = "api/gold_store/v1;v1";

enum PaymentMethod {
  GOLD = 0; // 金币
  GIFT_CARD = 1; // 礼品卡
  TASK = 2; // 用户任务
  ALI = 3; // 支付宝
  WECHAT = 4; // 微信
  APPLE_IAP = 5; // 苹果内购
  GOOGLE_IAP = 6; // google 内购
  UNION_PAY = 7; // 银联
  APPLE_PAY = 8; // 苹果支付
  GOOGLE_PAY = 9; // google支付
  PAYPAL = 10; // Paypal
  WECHAT_H5 = 11; // 微信H5
  ALI_WAP = 12; // 支付宝WAP
  FOREX_PAY = 13; // ForexPay
  POINTS = 14; // 积分
  PAYMENT_METHOD_UNKNOWN = 99;// 未知
}

enum OrderStatus {
  UNKNOWN = 0;
  UNPAY = 1; // 待支付
  PAID = 2; // 已支付
  CANCEL = 3; // 已取消
  DELIVERY = 4; // 待收货
  COMPLETE = 5; // 已完成
}

enum PaymentStatus {
  PaymentStatusUNPAY = 0; // 未支付
  PaymentStatusPAID = 1; // 已支付
  PaymentStatusCANCELED = 2;  // 已取消
  PaymentStatusTOREFUND = 3; // 待退款
  PaymentStatusREFUNDED = 4; // 已退款
}

enum Platform {
  IOS = 0; // 苹果
  ANDROID = 1; // 安卓
  PC = 2; // pc
  WEB = 3; // web
  PlatformUNKNOWN = 999; // 未知
}

enum GoodsCategory {
  GOODS_CATEGORY_PHYSICAL = 0; // 实物商品
  GOODS_CATEGORY_VIP = 1; // VIP
  GOODS_CATEGORY_VPS = 2; // VPS
  GOODS_CATEGORY_REPORT = 3; // 报告
  GOODS_CATEGORY_EXHIBITION = 4; // 展会
  GOODS_CATEGORY_EA = 5; // EA
}

enum GoodsStatus {
  GoodsStatusOff = 0; // 下架
  GoodsStatusOn = 1; // 上架
}

enum OrderSource {
  STORE = 0; // 金币商城
  VIP = 1; // vip
  VPS = 2; // VPS
  REPORT = 3; // 报告
  EXHIBITION = 4; // 展会
  EA = 5; // ea商城
}

enum LogisticStepStatus {
  LogisticStepStatusORDERED = 0; // 已下单
  LogisticStepStatusPROCESSING = 1; // 仓库处理中
  LogisticStepStatusSHIPPED = 2; // 已发货
  LogisticStepStatusPICKED = 3; // 已揽件
  LogisticStepStatusTRANSIT = 4; // 运输中
  LogisticStepStatusDELIVERY = 5; // 派送中
  LogisticStepStatusSIGNED = 6; // 已签收
}
message LogisticStep {
  string step_name = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "步骤名称"}];
  string step_desc = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "步骤描述"}];
  int64 step_time = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "步骤时间"}];
  LogisticStepStatus status = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "物流状态"}];
}

message Address {
  int32 id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "地址ID，新建时不用传递"}];
  string country_code_iso = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家code,聚合层会在body中增加countryCode字段会导致冲突"}];
  string user_name = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户名称"}];
  string phone_country_code = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号国家code"}];
  string phone_area_code = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号地区code"}];
  string phone = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "手机号"}];
  string province_name = 7[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "省份code"}];
  string city_name = 8[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "城市名称"}];
  string street_address = 9[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "街道地址"}];
  string building_unit_address = 10[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "单元地址"}];
  string postal_code = 11[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "邮编"}];
  bool is_default = 12[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否默认地址"}];
  string address_show = 13[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "显示名称，返回的时候展示使用"}];
  string country_name = 14[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国家名称"}];
}

message Image {
  string url = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片地址"}];
  int32 width = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片宽"}];
  int32 height = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "图片高"}];
}
message GoodsSpecValue {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值"}];
  bool selected = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否选中"}];
  Image image = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值图片"}];
  string spec_id = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格ID,app端需要"}];
  string unit_id = 6[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格单位：单位id"}];
}
message GoodsSpec {
  string id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格ID"}];
  string name = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格名称"}];
  repeated GoodsSpecValue values = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值"}];
}

message GoodsSkuSpec {
  string spec_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格ID"}];
  string value_id = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格值ID"}];
  string unit_id = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "规格单位ID"}];
}
message GoodsSku {
  string sku_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "sku_id"}];
  repeated GoodsSkuSpec specs = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "sku规格组合"}];
  float price = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "sku价格"}];
  bool disable = 4[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否禁用"}];
  int32 stock = 5[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "库存"}];
}
