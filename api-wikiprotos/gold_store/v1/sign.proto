syntax = "proto3";

package api.gold_store.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";

option go_package = "api/gold_store/v1;v1";

// 奖励内容
message Reward {
  string reward_type = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型，gold_coins"}];
  int32 gold_coins = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "金币奖励数量，reward_type=gold_coins时填写"}];
//  GoodsReward goods = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "实物奖励配置，reward_type=2时填写"}];
//  VirtualGoodsReward virtual_goods = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "虚拟奖励配置，reward_type=3时填写"}];
}

// 实物奖励配置
//message GoodsReward {
//  string goods_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品id"}];
//  string goods_name = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
//  bool free_shipping = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否免运费"}];
//}
//
//// 虚拟奖励配置
//message VirtualGoodsReward {
//  string virtual_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "虚拟商品id"}];
//}

// 签到奖励配置
message SignRewardConfig {
  int32 day = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到天数"}];
  Reward reward = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "普通奖励"}];
  Reward consecutive_reward = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "连续签到奖励"}];
  bool has_consecutive_reward = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否有连续签到奖励"}];
}

// 签到配置信息
message SignConfig {
  int32 cycle_days = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到周期天数"}];
  repeated SignRewardConfig reward = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励配置"}];
  string next_consecutive_sign_description = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下次连续签到说明文案"}];
}

// 用户签到信息
message UserSignInfo {
  bool has_signed_today = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "今日是否已签到"}];
  int32 consecutive_days = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前连续签到天数"}];
  int32 remaining_days_in_cycle = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "本周期剩余天数"}];
  repeated Reward remaining_reward_in_cycle = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "本周期剩余奖励"}];
  string signed_desc = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "今日签到描述"}];
}

// 签到历史
message SignHistory {
  string sign_date = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到日期"}];
  int32 day = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "第几天"}];
  Reward reward = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "普通奖励"}];
  Reward consecutive_reward = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "连续签到奖励"}];
}

// 实时动态
message SignLog {
  string description = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "动态描述"}];
  string name = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户昵称"}];
  string avatar = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户头像"}];
}

// ========== 前台接口 ==========
// 签到信息聚合接口
message GetSignAggregateInfoRequest {
  string timezone = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户时区"}];
}
message GetSignAggregateInfoReply {
  SignConfig config = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到配置"}];
  UserSignInfo user_sign_info = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户签到信息"}];
  repeated SignHistory sign_history = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到历史"}];
  repeated SignLog other_sign_log = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "实时动态"}];
}

// 执行签到接口
message SignInRequest {
  string timezone = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户时区"}];
}
message SignInReply {
  string sign_date = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到日期"}];
  repeated Reward reward = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "普通奖励"}];
  repeated Reward consecutive_reward = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "连续签到奖励"}];
  bool has_signed_today = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "今日是否已签到"}];
  int32 consecutive_days = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前连续签到天数"}];
  bool is_cycle_end = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否周期结束"}];
  repeated Reward next_reward = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "下次奖励"}];
  string sign_description = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到说明文案"}];
  string sign_status = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到状态，success 表示成功，duplicate 表示已签到 failed 表示失败"}];
}

// ========== 后台管理接口 ==========
// 获取签到配置列表
message ListSignConfigRequest {}
message ListSignConfigReply {
  repeated SignRewardConfig configs = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到奖励配置列表"}];
}
// 新增签到配置
message CreateSignConfigRequest {
  int32 day = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到天数"}];
  Reward reward = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "普通奖励"}];
  bool has_consecutive_reward = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否有连续签到奖励"}];
  Reward consecutive_reward = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "连续签到奖励"}];
}
message CreateSignConfigReply {
  SignRewardConfig config = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到奖励配置"}];
}
// 修改签到配置
message UpdateSignConfigRequest {
  int32 day = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到天数"}];
  Reward reward = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "普通奖励"}];
  bool has_consecutive_reward = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否有连续签到奖励"}];
  Reward consecutive_reward = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "连续签到奖励"}];
}
message UpdateSignConfigReply {
  SignRewardConfig config = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到奖励配置"}];
}
// 删除签到配置
message DeleteSignConfigRequest {
  int32 day = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "签到天数"}];
}
message DeleteSignConfigReply {
  string message = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "提示信息"}];
}


