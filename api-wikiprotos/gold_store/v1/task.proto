syntax = "proto3";

package api.gold_store.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";

option go_package = "api/gold_store/v1;v1";


// 获取任务列表
message GetTaskListRequest {
  string user_timezone = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户时区"}];
}
message GetTaskListReply {
  repeated TaskInfo daily = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "日常任务列表"}];
  repeated TaskInfo new_comer = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "新手任务列表"}];
}

// 领取奖励
message ReceiveTaskRewardRequest {
  int64 task_id = 1[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  string user_timezone = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户时区"}];
}
message ReceiveTaskRewardReply {
  int32 reward_type = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型"}];
  RewardInfo reward_info = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励详情"}];
  int64 task_id = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 reward_status = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励发放状态"}];
  int64 reward_issue_id = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务奖励发放ID"}];
}

// 领取任务
message ClaimTaskRequest {
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  string user_timezone = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户时区"}];
}
message ClaimTaskReply {
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 status = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "领取状态"}];
  string expire_time = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "过期时间"}];
}

// 查询奖励记录
message ListRewardRecordsRequest {
  int32 page = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 page_size = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int32 reward_type = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型"}];
  string start_time = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "开始时间"}];
  string end_time = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "结束时间"}];
}
message ListRewardRecordsReply {
  repeated RewardRecord records = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励记录列表"}];
  int32 total = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
}

message RewardRecord {
  int64 id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励记录ID"}];
  int64 task_id = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 reward_type = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型"}];
  RewardInfo reward_info = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励详情"}];
  int32 status = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励发放状态"}];
  string created_at = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "创建时间"}];
}

message RewardInfo {
  int32 gold_coins = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "金币数量"}];
  GoodsReward goods = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "实物奖励信息"}];
  VirtualGoodsReward virtual_goods = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "虚拟商品奖励信息"}];
}
message GoodsReward {
  string goods_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  string goods_name = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  bool free_shipping = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否免运费"}];
  string goods_icon = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品图标"}];
}

message VirtualGoodsReward {
  string goods_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "虚拟商品ID"}];
  string goods_name = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品名称"}];
  string goods_icon = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品图标"}];
}

message TaskInfo {
  int64 id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 user_progress = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户任务进度 (0=未开始, 1=进行中, 2=已完成未领取 3=已领取奖励)"}];
  int32 task_type = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务类型（1=新手任务，2=日常任务）"}];
  string task_desc = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务描述"}];
  string task_condition = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务完成条件"}];
  string show_project = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示项目"}];
  string min_version = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低版本号"}];
  string visible_users = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "可见用户范围"}];
  int32 status = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务状态:0 未领取, 1 进行中, 2 已完成未领取, 3 已领取奖励, 4 已过期"}];
  string task_icon = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务图标"}];
  int32 complete_times = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "需要完成次数"}];
  RewardInfo reward_info = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励详情"}];
  string reward_icon = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励图标"}];
  string task_sub_type = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务子类型"}];
  int32 reward_type = 15 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型 1=金币, 2=实物奖励, 3=虚拟商品奖励"}];
  string reward_desc = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务奖励描述"}];
  string target_code = 17 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务目标ID"}];
  int32 time_limit = 18 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看时间"}];
  string task_title = 19 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务标题"}];
}
// =========================== 后台 ===========================
message AdminListTaskRequest {
  int32 page = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 page_size = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int32 status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务状态筛选"}];
  int32 task_type = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务类型筛选"}];
}

// 后台查询用户任务进度列表请求
message AdminListTaskProgressRequest {
  int32 page = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
  int32 page_size = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "每页数量"}];
  int64 task_progress_id = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务进度id"}];
  string task_name = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务名称"}];
  int32 reward_status = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励状态: 0=全部; 1=已领取; 2=未领取"}];
  string start_time = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "完成时间开始"}];
  string end_time = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "完成时间结束"}];
}

// 用户任务进度记录
message TaskProgressRecord {
  int64 task_progress_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务进度id"}];
  string task_title = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务名称"}];
  string user_nickname = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户昵称"}];
  string user_id = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "天眼ID"}];
  int32 reward_type = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型: 1=金币, 2=实物奖励, 3=虚拟商品奖励"}];
  string reward_name = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务奖品名称"}];
  int32 task_status = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务状态: 0=未领取, 1=进行中, 2=已完成未领取, 3=已领取奖励, 4=已过期"}];
  int32 reward_status = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励状态: 1=已领取, 2=未领取"}];
  string order_no = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单号"}];
  string completed_at = 10 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "完成时间"}];
  int64 task_id = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int64 task_reward_id = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务奖励发放ID"}];
}

// 后台查询用户任务进度列表响应
message AdminListTaskProgressReply {
  repeated TaskProgressRecord records = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务进度记录列表"}];
  int32 total = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
  int32 page = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "当前页码"}];
}

message AdminTaskInfo{
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 status = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务状态"}];
  string task_title = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务标题"}];
  string task_desc = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务描述"}];
  string task_condition = 5 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务完成条件"}];
  string show_project = 6 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "展示项目"}];
  string min_version = 7 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "最低版本号"}];
  string visible_users = 8 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "可见用户范围"}];
  string task_icon = 9 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务图标"}];
  int32 complete_times = 11 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "需要完成次数"}];
  RewardInfo reward_info = 12 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励详情"}];
  string reward_icon = 13 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励图标"}];
  string modifier = 14 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "修改人"}];
  map<string, I18nConfig> i18n = 15 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "国际化语言配置"}];
  int32 sort_order = 16 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "排序id:高到底"}];
  string target_code = 17 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务目标ID"}];
  int32 time_limit = 18 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "查看时间"}];
  string config_version = 19 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "配置版本"}];
  string update_time = 20 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "更新时间"}];
  int32 reward_type = 21 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "奖励类型"}];
  int32 task_type = 22 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务类型（1=新手任务，2=日常任务）"}];
}

message AdminListTaskReply {
  repeated AdminTaskInfo taskList = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务列表"}];
  string total = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "总数"}];
  string page = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "页码"}];
}

message AdminUpdateTaskStatusRequest {
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 status = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务状态"}];
  string modifier = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "修改人"}];
}
message AdminUpdateTaskConfigRequest {
    AdminTaskInfo task_info = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务信息"}];
}
message AdminDeleteTaskRequest {
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  string modifier = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "修改人"}];
}
message AdminCreateTaskRequest {
    AdminTaskInfo task_info = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务信息"}];
}

message I18nConfig {
  string task_desc = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务描述"}];
  string task_title = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务标题"}];
}

message AdminCreateTaskReply {
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
  int32 status = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务状态"}];
  string modifier = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "修改人"}];
}

// 任务事件消息
message TaskEventRequest {
  string user_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "用户ID"}];
  string event = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件类型", description: "view_dealer 查看经销商, search 搜索"}];
  int32 status = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "事件状态", description: "0=未开始, 1=已完成"}];
}

message TaskEventReply {
  bool success = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否成功"}];
}


// 任务类型信息
message TaskTypeInfo {
  string enum_code = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务枚举代码"}];
  string title = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务标题"}];
  string description = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务描述"}];
  string condition = 4 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "完成条件"}];
}

// 获取任务类型列表请求
message GetTaskTypesRequest {
}

// 获取任务类型列表响应
message GetTaskTypesResponse {
  repeated TaskTypeInfo tasks = 3 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务列表"}];
}

// 获取任务详情请求
message AdminGetTaskDetailRequest {
  int64 task_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
}

// 获取任务详情响应
message AdminGetTaskDetailReply {
  AdminTaskInfo task_info = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务详细信息"}];
}

message AdminTaskGoods{
  string goods_id = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "商品ID"}];
  int32 task_id = 2 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务ID"}];
}

message AdminListTaskGoodsReply {
  repeated AdminTaskGoods task_goods = 1 [(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "任务商品列表"}];
}

