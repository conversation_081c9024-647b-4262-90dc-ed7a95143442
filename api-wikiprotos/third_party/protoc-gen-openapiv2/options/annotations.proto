syntax = "proto3";

package grpc.gateway.protoc_gen_openapiv2.options;

import "google/protobuf/descriptor.proto";
import "protoc-gen-openapiv2/options/openapiv2.proto";

option go_package = "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options";

extend google.protobuf.FileOptions {
  // ID <NAME_EMAIL> for gRPC-Gateway project.
  //
  // All IDs are the same, as assigned. It is okay that they are the same, as they extend
  // different descriptor messages.
  Swagger openapiv2_swagger = 1042;
}
extend google.protobuf.MethodOptions {
  // ID <NAME_EMAIL> for gRPC-Gateway project.
  //
  // All IDs are the same, as assigned. It is okay that they are the same, as they extend
  // different descriptor messages.
  Operation openapiv2_operation = 1042;
}
extend google.protobuf.MessageOptions {
  // ID <NAME_EMAIL> for gRPC-Gateway project.
  //
  // All IDs are the same, as assigned. It is okay that they are the same, as they extend
  // different descriptor messages.
  Schema openapiv2_schema = 1042;
}
extend google.protobuf.ServiceOptions {
  // ID <NAME_EMAIL> for gRPC-Gateway project.
  //
  // All IDs are the same, as assigned. It is okay that they are the same, as they extend
  // different descriptor messages.
  Tag openapiv2_tag = 1042;
}
extend google.protobuf.FieldOptions {
  // ID <NAME_EMAIL> for gRPC-Gateway project.
  //
  // All IDs are the same, as assigned. It is okay that they are the same, as they extend
  // different descriptor messages.
  JSONSchema openapiv2_field = 1042;
}

extend google.protobuf.EnumValueOptions {
  JSONSchema openapiv2_value = 1042;
}