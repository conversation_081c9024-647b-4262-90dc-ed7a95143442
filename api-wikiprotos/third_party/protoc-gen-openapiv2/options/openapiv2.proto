syntax = "proto3";

package grpc.gateway.protoc_gen_openapiv2.options;

import "google/protobuf/struct.proto";

option go_package = "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options";

// Scheme describes the schemes supported by the OpenAPI Swagger
// and Operation objects.
enum Scheme {
  UNKNOWN = 0;
  HTTP = 1;
  HTTPS = 2;
  WS = 3;
  WSS = 4;
}

// `Swagger` is a representation of OpenAPI v2 specification's Swagger object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#swaggerObject
//
// Example:
//
//  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
//    info: {
//      title: "Echo API";
//      version: "1.0";
//      description: "";
//      contact: {
//        name: "gRPC-Gateway project";
//        url: "https://github.com/grpc-ecosystem/grpc-gateway";
//        email: "<EMAIL>";
//      };
//      license: {
//        name: "BSD 3-Clause License";
//        url: "https://github.com/grpc-ecosystem/grpc-gateway/blob/main/LICENSE";
//      };
//    };
//    schemes: HTTPS;
//    consumes: "application/json";
//    produces: "application/json";
//  };
//
message Swagger {
  // Specifies the OpenAPI Specification version being used. It can be
  // used by the OpenAPI UI and other clients to interpret the API listing. The
  // value MUST be "2.0".
  string swagger = 1;
  // Provides metadata about the API. The metadata can be used by the
  // clients if needed.
  Info info = 2;
  // The host (name or ip) serving the API. This MUST be the host only and does
  // not include the scheme nor sub-paths. It MAY include a port. If the host is
  // not included, the host serving the documentation is to be used (including
  // the port). The host does not support path templating.
  string host = 3;
  // The base path on which the API is served, which is relative to the host. If
  // it is not included, the API is served directly under the host. The value
  // MUST start with a leading slash (/). The basePath does not support path
  // templating.
  // Note that using `base_path` does not change the endpoint paths that are
  // generated in the resulting OpenAPI file. If you wish to use `base_path`
  // with relatively generated OpenAPI paths, the `base_path` prefix must be
  // manually removed from your `google.api.http` paths and your code changed to
  // serve the API from the `base_path`.
  string base_path = 4;
  // The transfer protocol of the API. Values MUST be from the list: "http",
  // "https", "ws", "wss". If the schemes is not included, the default scheme to
  // be used is the one used to access the OpenAPI definition itself.
  repeated Scheme schemes = 5;
  // A list of MIME types the APIs can consume. This is global to all APIs but
  // can be overridden on specific API calls. Value MUST be as described under
  // Mime Types.
  repeated string consumes = 6;
  // A list of MIME types the APIs can produce. This is global to all APIs but
  // can be overridden on specific API calls. Value MUST be as described under
  // Mime Types.
  repeated string produces = 7;
  // field 8 is reserved for 'paths'.
  reserved 8;
  // field 9 is reserved for 'definitions', which at this time are already
  // exposed as and customizable as proto messages.
  reserved 9;
  // An object to hold responses that can be used across operations. This
  // property does not define global responses for all operations.
  map<string, Response> responses = 10;
  // Security scheme definitions that can be used across the specification.
  SecurityDefinitions security_definitions = 11;
  // A declaration of which security schemes are applied for the API as a whole.
  // The list of values describes alternative security schemes that can be used
  // (that is, there is a logical OR between the security requirements).
  // Individual operations can override this definition.
  repeated SecurityRequirement security = 12;
  // A list of tags for API documentation control. Tags can be used for logical
  // grouping of operations by resources or any other qualifier.
  repeated Tag tags = 13;
  // Additional external documentation.
  ExternalDocumentation external_docs = 14;
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 15;
}

// `Operation` is a representation of OpenAPI v2 specification's Operation object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#operationObject
//
// Example:
//
//  service EchoService {
//    rpc Echo(SimpleMessage) returns (SimpleMessage) {
//      option (google.api.http) = {
//        get: "/v1/example/echo/{id}"
//      };
//
//      option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
//        summary: "Get a message.";
//        operation_id: "getMessage";
//        tags: "echo";
//        responses: {
//          key: "200"
//            value: {
//            description: "OK";
//          }
//        }
//      };
//    }
//  }
message Operation {
  // A list of tags for API documentation control. Tags can be used for logical
  // grouping of operations by resources or any other qualifier.
  repeated string tags = 1;
  // A short summary of what the operation does. For maximum readability in the
  // swagger-ui, this field SHOULD be less than 120 characters.
  string summary = 2;
  // A verbose explanation of the operation behavior. GFM syntax can be used for
  // rich text representation.
  string description = 3;
  // Additional external documentation for this operation.
  ExternalDocumentation external_docs = 4;
  // Unique string used to identify the operation. The id MUST be unique among
  // all operations described in the API. Tools and libraries MAY use the
  // operationId to uniquely identify an operation, therefore, it is recommended
  // to follow common programming naming conventions.
  string operation_id = 5;
  // A list of MIME types the operation can consume. This overrides the consumes
  // definition at the OpenAPI Object. An empty value MAY be used to clear the
  // global definition. Value MUST be as described under Mime Types.
  repeated string consumes = 6;
  // A list of MIME types the operation can produce. This overrides the produces
  // definition at the OpenAPI Object. An empty value MAY be used to clear the
  // global definition. Value MUST be as described under Mime Types.
  repeated string produces = 7;
  // field 8 is reserved for 'parameters'.
  reserved 8;
  // The list of possible responses as they are returned from executing this
  // operation.
  map<string, Response> responses = 9;
  // The transfer protocol for the operation. Values MUST be from the list:
  // "http", "https", "ws", "wss". The value overrides the OpenAPI Object
  // schemes definition.
  repeated Scheme schemes = 10;
  // Declares this operation to be deprecated. Usage of the declared operation
  // should be refrained. Default value is false.
  bool deprecated = 11;
  // A declaration of which security schemes are applied for this operation. The
  // list of values describes alternative security schemes that can be used
  // (that is, there is a logical OR between the security requirements). This
  // definition overrides any declared top-level security. To remove a top-level
  // security declaration, an empty array can be used.
  repeated SecurityRequirement security = 12;
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 13;
  // Custom parameters such as HTTP request headers.
  // See: https://swagger.io/docs/specification/2-0/describing-parameters/
  // and https://swagger.io/specification/v2/#parameter-object.
  Parameters parameters = 14;
}

// `Parameters` is a representation of OpenAPI v2 specification's parameters object.
// Note: This technically breaks compatibility with the OpenAPI 2 definition structure as we only
// allow header parameters to be set here since we do not want users specifying custom non-header
// parameters beyond those inferred from the Protobuf schema.
// See: https://swagger.io/specification/v2/#parameter-object
message Parameters {
  // `Headers` is one or more HTTP header parameter.
  // See: https://swagger.io/docs/specification/2-0/describing-parameters/#header-parameters
  repeated HeaderParameter headers = 1;
}

// `HeaderParameter` a HTTP header parameter.
// See: https://swagger.io/specification/v2/#parameter-object
message HeaderParameter {
  // `Type` is a a supported HTTP header type.
  // See https://swagger.io/specification/v2/#parameterType.
  enum Type {
    UNKNOWN = 0;
    STRING = 1;
    NUMBER = 2;
    INTEGER = 3;
    BOOLEAN = 4;
  }

  // `Name` is the header name.
  string name = 1;
  // `Description` is a short description of the header.
  string description = 2;
  // `Type` is the type of the object. The value MUST be one of "string", "number", "integer", or "boolean". The "array" type is not supported.
  // See: https://swagger.io/specification/v2/#parameterType.
  Type type = 3;
  // `Format` The extending format for the previously mentioned type.
  string format = 4;
  // `Required` indicates if the header is optional
  bool required = 5;
  // field 6 is reserved for 'items', but in OpenAPI-specific way.
  reserved 6;
  // field 7 is reserved `Collection Format`. Determines the format of the array if type array is used.
  reserved 7;
}

// `Header` is a representation of OpenAPI v2 specification's Header object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#headerObject
//
message Header {
  // `Description` is a short description of the header.
  string description = 1;
  // The type of the object. The value MUST be one of "string", "number", "integer", or "boolean". The "array" type is not supported.
  string type = 2;
  // `Format` The extending format for the previously mentioned type.
  string format = 3;
  // field 4 is reserved for 'items', but in OpenAPI-specific way.
  reserved 4;
  // field 5 is reserved `Collection Format` Determines the format of the array if type array is used.
  reserved 5;
  // `Default` Declares the value of the header that the server will use if none is provided.
  // See: https://tools.ietf.org/html/draft-fge-json-schema-validation-00#section-6.2.
  // Unlike JSON Schema this value MUST conform to the defined type for the header.
  string default = 6;
  // field 7 is reserved for 'maximum'.
  reserved 7;
  // field 8 is reserved for 'exclusiveMaximum'.
  reserved 8;
  // field 9 is reserved for 'minimum'.
  reserved 9;
  // field 10 is reserved for 'exclusiveMinimum'.
  reserved 10;
  // field 11 is reserved for 'maxLength'.
  reserved 11;
  // field 12 is reserved for 'minLength'.
  reserved 12;
  // 'Pattern' See https://tools.ietf.org/html/draft-fge-json-schema-validation-00#section-5.2.3.
  string pattern = 13;
  // field 14 is reserved for 'maxItems'.
  reserved 14;
  // field 15 is reserved for 'minItems'.
  reserved 15;
  // field 16 is reserved for 'uniqueItems'.
  reserved 16;
  // field 17 is reserved for 'enum'.
  reserved 17;
  // field 18 is reserved for 'multipleOf'.
  reserved 18;
}

// `Response` is a representation of OpenAPI v2 specification's Response object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#responseObject
//
message Response {
  // `Description` is a short description of the response.
  // GFM syntax can be used for rich text representation.
  string description = 1;
  // `Schema` optionally defines the structure of the response.
  // If `Schema` is not provided, it means there is no content to the response.
  Schema schema = 2;
  // `Headers` A list of headers that are sent with the response.
  // `Header` name is expected to be a string in the canonical format of the MIME header key
  // See: https://golang.org/pkg/net/textproto/#CanonicalMIMEHeaderKey
  map<string, Header> headers = 3;
  // `Examples` gives per-mimetype response examples.
  // See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#example-object
  map<string, string> examples = 4;
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 5;
}

// `Info` is a representation of OpenAPI v2 specification's Info object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#infoObject
//
// Example:
//
//  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
//    info: {
//      title: "Echo API";
//      version: "1.0";
//      description: "";
//      contact: {
//        name: "gRPC-Gateway project";
//        url: "https://github.com/grpc-ecosystem/grpc-gateway";
//        email: "<EMAIL>";
//      };
//      license: {
//        name: "BSD 3-Clause License";
//        url: "https://github.com/grpc-ecosystem/grpc-gateway/blob/main/LICENSE";
//      };
//    };
//    ...
//  };
//
message Info {
  // The title of the application.
  string title = 1;
  // A short description of the application. GFM syntax can be used for rich
  // text representation.
  string description = 2;
  // The Terms of Service for the API.
  string terms_of_service = 3;
  // The contact information for the exposed API.
  Contact contact = 4;
  // The license information for the exposed API.
  License license = 5;
  // Provides the version of the application API (not to be confused
  // with the specification version).
  string version = 6;
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 7;
}

// `Contact` is a representation of OpenAPI v2 specification's Contact object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#contactObject
//
// Example:
//
//  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
//    info: {
//      ...
//      contact: {
//        name: "gRPC-Gateway project";
//        url: "https://github.com/grpc-ecosystem/grpc-gateway";
//        email: "<EMAIL>";
//      };
//      ...
//    };
//    ...
//  };
//
message Contact {
  // The identifying name of the contact person/organization.
  string name = 1;
  // The URL pointing to the contact information. MUST be in the format of a
  // URL.
  string url = 2;
  // The email address of the contact person/organization. MUST be in the format
  // of an email address.
  string email = 3;
}

// `License` is a representation of OpenAPI v2 specification's License object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#licenseObject
//
// Example:
//
//  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
//    info: {
//      ...
//      license: {
//        name: "BSD 3-Clause License";
//        url: "https://github.com/grpc-ecosystem/grpc-gateway/blob/main/LICENSE";
//      };
//      ...
//    };
//    ...
//  };
//
message License {
  // The license name used for the API.
  string name = 1;
  // A URL to the license used for the API. MUST be in the format of a URL.
  string url = 2;
}

// `ExternalDocumentation` is a representation of OpenAPI v2 specification's
// ExternalDocumentation object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#externalDocumentationObject
//
// Example:
//
//  option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
//    ...
//    external_docs: {
//      description: "More about gRPC-Gateway";
//      url: "https://github.com/grpc-ecosystem/grpc-gateway";
//    }
//    ...
//  };
//
message ExternalDocumentation {
  // A short description of the target documentation. GFM syntax can be used for
  // rich text representation.
  string description = 1;
  // The URL for the target documentation. Value MUST be in the format
  // of a URL.
  string url = 2;
}

// `Schema` is a representation of OpenAPI v2 specification's Schema object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#schemaObject
//
message Schema {
  JSONSchema json_schema = 1;
  // Adds support for polymorphism. The discriminator is the schema property
  // name that is used to differentiate between other schema that inherit this
  // schema. The property name used MUST be defined at this schema and it MUST
  // be in the required property list. When used, the value MUST be the name of
  // this schema or any schema that inherits it.
  string discriminator = 2;
  // Relevant only for Schema "properties" definitions. Declares the property as
  // "read only". This means that it MAY be sent as part of a response but MUST
  // NOT be sent as part of the request. Properties marked as readOnly being
  // true SHOULD NOT be in the required list of the defined schema. Default
  // value is false.
  bool read_only = 3;
  // field 4 is reserved for 'xml'.
  reserved 4;
  // Additional external documentation for this schema.
  ExternalDocumentation external_docs = 5;
  // A free-form property to include an example of an instance for this schema in JSON.
  // This is copied verbatim to the output.
  string example = 6;
}

// `JSONSchema` represents properties from JSON Schema taken, and as used, in
// the OpenAPI v2 spec.
//
// This includes changes made by OpenAPI v2.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#schemaObject
//
// See also: https://cswr.github.io/JsonSchema/spec/basic_types/,
// https://github.com/json-schema-org/json-schema-spec/blob/master/schema.json
//
// Example:
//
//  message SimpleMessage {
//    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_schema) = {
//      json_schema: {
//        title: "SimpleMessage"
//        description: "A simple message."
//        required: ["id"]
//      }
//    };
//
//    // Id represents the message identifier.
//    string id = 1; [
//        (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {
//          description: "The unique identifier of the simple message."
//        }];
//  }
//
message JSONSchema {
  // field 1 is reserved for '$id', omitted from OpenAPI v2.
  reserved 1;
  // field 2 is reserved for '$schema', omitted from OpenAPI v2.
  reserved 2;
  // Ref is used to define an external reference to include in the message.
  // This could be a fully qualified proto message reference, and that type must
  // be imported into the protofile. If no message is identified, the Ref will
  // be used verbatim in the output.
  // For example:
  //  `ref: ".google.protobuf.Timestamp"`.
  string ref = 3;
  // field 4 is reserved for '$comment', omitted from OpenAPI v2.
  reserved 4;
  // The title of the schema.
  string title = 5;
  // A short description of the schema.
  string description = 6;
  string default = 7;
  bool read_only = 8;
  // A free-form property to include a JSON example of this field. This is copied
  // verbatim to the output swagger.json. Quotes must be escaped.
  // This property is the same for 2.0 and 3.0.0 https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/3.0.0.md#schemaObject  https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#schemaObject
  string example = 9;
  double multiple_of = 10;
  // Maximum represents an inclusive upper limit for a numeric instance. The
  // value of MUST be a number,
  double maximum = 11;
  bool exclusive_maximum = 12;
  // minimum represents an inclusive lower limit for a numeric instance. The
  // value of MUST be a number,
  double minimum = 13;
  bool exclusive_minimum = 14;
  uint64 max_length = 15;
  uint64 min_length = 16;
  string pattern = 17;
  // field 18 is reserved for 'additionalItems', omitted from OpenAPI v2.
  reserved 18;
  // field 19 is reserved for 'items', but in OpenAPI-specific way.
  // TODO(ivucica): add 'items'?
  reserved 19;
  uint64 max_items = 20;
  uint64 min_items = 21;
  bool unique_items = 22;
  // field 23 is reserved for 'contains', omitted from OpenAPI v2.
  reserved 23;
  uint64 max_properties = 24;
  uint64 min_properties = 25;
  repeated string required = 26;
  // field 27 is reserved for 'additionalProperties', but in OpenAPI-specific
  // way. TODO(ivucica): add 'additionalProperties'?
  reserved 27;
  // field 28 is reserved for 'definitions', omitted from OpenAPI v2.
  reserved 28;
  // field 29 is reserved for 'properties', but in OpenAPI-specific way.
  // TODO(ivucica): add 'additionalProperties'?
  reserved 29;
  // following fields are reserved, as the properties have been omitted from
  // OpenAPI v2:
  // patternProperties, dependencies, propertyNames, const
  reserved 30 to 33;
  // Items in 'array' must be unique.
  repeated string array = 34;

  enum JSONSchemaSimpleTypes {
    UNKNOWN = 0;
    ARRAY = 1;
    BOOLEAN = 2;
    INTEGER = 3;
    NULL = 4;
    NUMBER = 5;
    OBJECT = 6;
    STRING = 7;
  }

  repeated JSONSchemaSimpleTypes type = 35;
  // `Format`
  string format = 36;
  // following fields are reserved, as the properties have been omitted from
  // OpenAPI v2: contentMediaType, contentEncoding, if, then, else
  reserved 37 to 41;
  // field 42 is reserved for 'allOf', but in OpenAPI-specific way.
  // TODO(ivucica): add 'allOf'?
  reserved 42;
  // following fields are reserved, as the properties have been omitted from
  // OpenAPI v2:
  // anyOf, oneOf, not
  reserved 43 to 45;
  // Items in `enum` must be unique https://tools.ietf.org/html/draft-fge-json-schema-validation-00#section-5.5.1
  repeated string enum = 46;

  // Additional field level properties used when generating the OpenAPI v2 file.
  FieldConfiguration field_configuration = 1001;

  // 'FieldConfiguration' provides additional field level properties used when generating the OpenAPI v2 file.
  // These properties are not defined by OpenAPIv2, but they are used to control the generation.
  message FieldConfiguration {
    // Alternative parameter name when used as path parameter. If set, this will
    // be used as the complete parameter name when this field is used as a path
    // parameter. Use this to avoid having auto generated path parameter names
    // for overlapping paths.
    string path_param_name = 47;
  }
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 48;
}

// `Tag` is a representation of OpenAPI v2 specification's Tag object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#tagObject
//
message Tag {
  // The name of the tag. Use it to allow override of the name of a
  // global Tag object, then use that name to reference the tag throughout the
  // OpenAPI file.
  string name = 1;
  // A short description for the tag. GFM syntax can be used for rich text
  // representation.
  string description = 2;
  // Additional external documentation for this tag.
  ExternalDocumentation external_docs = 3;
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 4;
}

// `SecurityDefinitions` is a representation of OpenAPI v2 specification's
// Security Definitions object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#securityDefinitionsObject
//
// A declaration of the security schemes available to be used in the
// specification. This does not enforce the security schemes on the operations
// and only serves to provide the relevant details for each scheme.
message SecurityDefinitions {
  // A single security scheme definition, mapping a "name" to the scheme it
  // defines.
  map<string, SecurityScheme> security = 1;
}

// `SecurityScheme` is a representation of OpenAPI v2 specification's
// Security Scheme object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#securitySchemeObject
//
// Allows the definition of a security scheme that can be used by the
// operations. Supported schemes are basic authentication, an API key (either as
// a header or as a query parameter) and OAuth2's common flows (implicit,
// password, application and access code).
message SecurityScheme {
  // The type of the security scheme. Valid values are "basic",
  // "apiKey" or "oauth2".
  enum Type {
    TYPE_INVALID = 0;
    TYPE_BASIC = 1;
    TYPE_API_KEY = 2;
    TYPE_OAUTH2 = 3;
  }

  // The location of the API key. Valid values are "query" or "header".
  enum In {
    IN_INVALID = 0;
    IN_QUERY = 1;
    IN_HEADER = 2;
  }

  // The flow used by the OAuth2 security scheme. Valid values are
  // "implicit", "password", "application" or "accessCode".
  enum Flow {
    FLOW_INVALID = 0;
    FLOW_IMPLICIT = 1;
    FLOW_PASSWORD = 2;
    FLOW_APPLICATION = 3;
    FLOW_ACCESS_CODE = 4;
  }

  // The type of the security scheme. Valid values are "basic",
  // "apiKey" or "oauth2".
  Type type = 1;
  // A short description for security scheme.
  string description = 2;
  // The name of the header or query parameter to be used.
  // Valid for apiKey.
  string name = 3;
  // The location of the API key. Valid values are "query" or
  // "header".
  // Valid for apiKey.
  In in = 4;
  // The flow used by the OAuth2 security scheme. Valid values are
  // "implicit", "password", "application" or "accessCode".
  // Valid for oauth2.
  Flow flow = 5;
  // The authorization URL to be used for this flow. This SHOULD be in
  // the form of a URL.
  // Valid for oauth2/implicit and oauth2/accessCode.
  string authorization_url = 6;
  // The token URL to be used for this flow. This SHOULD be in the
  // form of a URL.
  // Valid for oauth2/password, oauth2/application and oauth2/accessCode.
  string token_url = 7;
  // The available scopes for the OAuth2 security scheme.
  // Valid for oauth2.
  Scopes scopes = 8;
  // Custom properties that start with "x-" such as "x-foo" used to describe
  // extra functionality that is not covered by the standard OpenAPI Specification.
  // See: https://swagger.io/docs/specification/2-0/swagger-extensions/
  map<string, google.protobuf.Value> extensions = 9;
}

// `SecurityRequirement` is a representation of OpenAPI v2 specification's
// Security Requirement object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#securityRequirementObject
//
// Lists the required security schemes to execute this operation. The object can
// have multiple security schemes declared in it which are all required (that
// is, there is a logical AND between the schemes).
//
// The name used for each property MUST correspond to a security scheme
// declared in the Security Definitions.
message SecurityRequirement {
  // If the security scheme is of type "oauth2", then the value is a list of
  // scope names required for the execution. For other security scheme types,
  // the array MUST be empty.
  message SecurityRequirementValue {
    repeated string scope = 1;
  }
  // Each name must correspond to a security scheme which is declared in
  // the Security Definitions. If the security scheme is of type "oauth2",
  // then the value is a list of scope names required for the execution.
  // For other security scheme types, the array MUST be empty.
  map<string, SecurityRequirementValue> security_requirement = 1;
}

// `Scopes` is a representation of OpenAPI v2 specification's Scopes object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#scopesObject
//
// Lists the available scopes for an OAuth2 security scheme.
message Scopes {
  // Maps between a name of a scope to a short description of it (as the value
  // of the property).
  map<string, string> scope = 1;
}