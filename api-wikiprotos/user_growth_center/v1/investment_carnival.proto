syntax = "proto3";

package api.user_growth_center.v1;
import "protoc-gen-openapiv2/options/annotations.proto";
option go_package = "api/user_growth_center/v1;v1";

message GetActivityMainRequest{
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}

message GetActivityMainReply{
	string banner=1 [json_name="banner",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"Banner图"}];
	int64 activityStatus =2 [json_name="activityStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动状态 0:未开始 1:开始 2:已结束",default:"0"}];
	int64 activityTimeStamp=3 [json_name="activityTimeStamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动开始时间",default:"0"}];
	repeated TraderBaseInfo popularTraderCol =4 [json_name="popularTraderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐交易商"}];
	repeated  CategoryTrader bestTraderCol=5 [json_name="bestTraderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐交易商"}];
	repeated Recommender recommenderCol=6 [json_name="recommenderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"王牌推荐官"}];
}

message CategoryTrader {
	string category=1 [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类别"}];
	repeated TraderBaseInfo traderCol=2 [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商列表"}];
}
message  TraderBaseInfo {
	string traderCode =1 [json_name="traderCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商Code"}];
	string logo=2 [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商logo"}];
	string ico=3 [json_name="ico",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商ico"}];
	string traderName=4 [json_name="traderName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商简称"}];
	repeated string welfareCol=5 [json_name="welfareCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"福利"}];
	bool  adFlag =6 [json_name="adFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否广告",default:"false"}];
	string annotation=7 [json_name="annotation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商角标"}];
	int64  participant =8 [json_name="participant",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"参与人数"}];
	string score =9  [json_name="score",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"天眼评分"}];
	string countryFlag =10  [json_name="countryFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"注册国家国旗"}];
	repeated LabelItem  labelCol=11  [json_name="labelCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商标签列表"}];
	bool vrFlag=12   [json_name="vrFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有VR",default:"false"}];
	string color=13  [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标颜色"}];
}

message Recommender {
	string avatar =1 [json_name="avatar",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像"}];
	string recommenderName=2 [json_name="recommenderName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"姓名"}];
	repeated string recommenderTagCol =3 [json_name="recommenderTagCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签,例如:从业时间,职位"}];
	string liveTitle=4  [json_name="liveTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"直播标题"}];
	int64 liveTimeStamp=5  [json_name="liveTimeStamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"直播开始时间"}];
	int32 liveStatus=6  [json_name="liveStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"直播状态,0:未开始 1 直播中 2 直播结束",default:"0"}];
	string reviewUrl=7 [json_name="reviewUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"直播回放地址"}];
	string traderName=8 [json_name="traderName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"所属交易商"}];
	string liveEndTimeStamp=9 [json_name="liveEndTimeStamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"直播结束时间"}];
}

message  GetContentRequest {
	int32  type =1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"1 规则,2 说明",default:"1"}];
}
message GetContentReply {
	string content=1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容"}];
}

message GetRecommendTraderRequest {
	int32 number=1  [json_name="number",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"条数",default:"10"}];
}
message GetRecommendTraderReply {
  repeated TraderBaseInfo traderCol=1 [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐交易商列表"}];
}

message  GetGlobalTraderRequest {}
message GetGlobalTraderReply {
repeated CategoryTrader traderCol=1  [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类交易商列表"}];
}

message  GetTraderActivityPageRequest{
	string traderCode =1 [json_name="traderCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商Code",required:["traderCode"]}];
}
message  GetTraderActivityPageReply{
	string traderCode =1 [json_name="traderCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商Code"}];
	bool  cooperationFlag=2 [json_name="cooperationFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否合作",default:"false"}];
	repeated AdvertisementInfo advertisementCol =3  [json_name="advertisementCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告"}];
	TraderBaseInfo traderData=4  [json_name="traderData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商信息"}];
	TraderContactInfo contactData=5  [json_name="contactData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商联系信息"}];
	TraderEventDiscount  eventDiscountData=6 [json_name="eventDiscountData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动优惠"}];
	TraderBrandDisplay brandDisplayData=7 [json_name="brandDisplayData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品牌展示"}];
	string traderProfile=8  [json_name="traderProfile",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"公司简介"}];
	bool  liveFlag=9  [json_name="liveFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有直播",default:"false"}];
	string  liveUrl=10  [json_name="liveUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"直播地址"}];
	string openAccountUrl=11  [json_name="openAccountUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户链接"}];
}

message TraderContactInfo {
	repeated string site =1  [json_name="site",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"网址信息"}];
	string phone=2  [json_name="phone",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"电话"}];
	string email=3  [json_name="email",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邮箱"}];
}

message AdvertisementInfo {
	 string imageUrl =1 [json_name="imageUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片地址"}];
	 string jumpAddress=2  [json_name="jumpAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"跳转地址"}];
}
message TraderEventDiscount {
	repeated string imageUrl =1 [json_name="imageUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片地址"}];
}
message TraderBrandDisplay {
	string  content=1 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"文案"}];
	repeated string imageCol =2 [json_name="imageCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"品牌资质图片"}];
	string descriptionImg=3 [json_name="descriptionImg",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"长图介绍"}];
}

message GetRecommenderListRequest{
	int32 number=1  [json_name="number",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"条数",default:"10"}];
}

message GetRecommenderListReply {
	repeated Recommender recommenderCol=1  [json_name="recommenderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐官列表"}];
}

message 	GetRewordPoolDetailRequest {
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}
message  GetRewordPoolDetailReply {
	string banner=1  [json_name="banner",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告位背景图"}];
	repeated  AdvertisementInfo advertisementCol=2  [json_name="advertisementCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告logo"}];
	repeated 	CheckInInfo  checkInCol =3  [json_name="checkInCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"签到列表"}];
	repeated 	RewordPoolSummaryInfo  rewordPoolCol =4  [json_name="rewordPoolCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"签到列表"}];
}
message CheckInInfo{
	string  checkInTitle =1 [json_name="checkInTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"签到标题,例如：day1、day2、day3"}];
	int32  checkInStatus=2 [json_name="checkInStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"签到状态,0:未签到 1:已签到",default:"0"}];
}
message RewordPoolSummaryInfo {
  string poolTitle =1 [json_name="poolTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池标题"}];
	string poolContent =2 [json_name="poolContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池内容"}];
	int64  participant=3 [json_name="participant",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"参与人数"}];
	int64 finished=4 [json_name="finished",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"完成人数"}];
	int32 progress=5 [json_name="progress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"完成任务数量"}];
	int32 total=6 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总任务数量"}];
}
message  RewordPoolInfo {
	string poolTitle =1 [json_name="poolTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池标题"}];
	repeated  TaskDetail taskCol=2  [json_name="taskCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池任务"}];
}
message TaskDetail {
	string taskTitle=1  [json_name="taskTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务标题"}];
	string taskContent=2  [json_name="taskContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务内容"}];
	string taskIco=3  [json_name="taskIco",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务Ico"}];
	bool finishFlag=4 [json_name="finishFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否完成",default:"false"}];
	string actionName=5  [json_name="actionName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"按钮名称"}];
}

message 		UserCheckInRequest {
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}
message 	UserCheckInReply {
	bool  success =1  [json_name="success",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"签到是否成功",default:"false"}];
}

message  GetRewordDetailRequest{
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}

message GetRewordDetailReply {
	float reword=1  [json_name="reword",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖励金额"}];
	repeated 	SharePrizePoolSummary poolSummaryCol=2  [json_name="poolSummaryCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池总览"}];
		DepositCashBack depositCashBackData =3  [json_name="depositCashBackData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"现金提取"}];
		repeated GrandLuckyDraw luckyDrawCol=4  [json_name="luckyDrawCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"幸运大抽奖"}];
}
message SharePrizePoolSummary {
	string poolTitle =1 [json_name="poolTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池标题"}];
	string poolSchedule=2  [json_name="poolSchedule",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖池进度"}];
}
message DepositCashBack {
 bool  depositFlag=1  [json_name="depositFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否返现",default:"false"}];
	float cashBack=2  [json_name="cashBack",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"返现金额"}];
}
message GrandLuckyDraw {
  string award =1  [json_name="award",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖品"}];
	int32  type=2   [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型，1:实物,2:奖金,3:VPS"}];
	string redemptionCodes=3   [json_name="redemptionCodes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"兑换码"}];
}

message GrandLuckyDrawRequest {
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}
message GrandLuckyDrawReply {
	int32 chances=1  [json_name="chances",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"剩余抽奖次数",default:"0"}];
	repeated AwardInfo awardCol=2  [json_name="awardCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖品列表"}];
	repeated TraderBaseInfo traderCol=3  [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"合作交易商列表"}];
	repeated  RewordRecords record=4  [json_name="record",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获奖记录"}];
	repeated TaskDetail task=5   [json_name="task",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获取抽奖次数任务列表"}];

}
message AwardInfo {
  string name =1 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖品名称"}];
	string logo=2  [json_name="logo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖品logo"}];
}

message  RewordRecords {
	string nickName=1 [json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户昵称"}];
	string award=2  [json_name="award",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖品"}];
	int64  rewordTimeStamp=3  [json_name="rewordTimeStamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获奖时间"}];
}

message  StartDrawReply {
	bool successFlag =1 [json_name="successFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否抽奖成功",default:"false"}];
	string tips=2 [json_name="tips",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"抽奖提示词"}];
	 	AwardInfo awardData=3  [json_name="awardData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"中奖奖品"}];
		 string description=4 [json_name="description",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"抽奖结果描述"}];
	string actionName=5 [json_name="actionName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"中奖跳转按钮"}];
	string exchangeDesc=6 [json_name="exchangeDesc",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖品兑换描述"}];
}

message  GetDepositDetailRequest {
}
message  GetDepositDetailReply {
	int64 participant =1  [json_name="participant",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"参与人数"}];
}
message  SearchTraderRequest{
	string content=1   [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索内容",required:["content"]}];
}
message SearchTraderReply {
	repeated TraderBaseInfo traderCol=1  [json_name="traderCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"搜索结果"}];
}

message GetActivityShareRequest {}
message GetActivityShareReply {
	string url =1  [json_name="url",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享地址"}];
}

message GetBannerRequest {}
message GetBannerReply {
	string banner=1  [json_name="banner",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告位背景图"}];
	repeated  AdvertisementInfo advertisementCol=2  [json_name="advertisementCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"广告logo"}];
}

message AssistRequest {
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
	string assistedUserId=2 [json_name="assistedUserId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"助力用户ID",required:["assistedUserId"]}];
}
message AssistReply {}

message WatchLiveRequest {
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}
message WatchLiveReply {}

message FriendAssistanceRequest{
	string userId=1 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID",required:["userId"]}];
}

message FriendAssistanceReply{
	int32 progress=1 [json_name="progress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请人数"}];
	int32 total=2 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总人数"}];
	repeated string invitations=3 [json_name="invitations",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请明细"}];
}

message  LabelItem {
	int64 type =1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"类型"}];
	string labelName=2 [json_name="labelName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标签名称"}];
}

