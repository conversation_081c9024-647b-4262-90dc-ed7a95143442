syntax = "proto3";

package api.user_growth_center.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "user_growth_center/v1/growth_center.proto";
import "user_growth_center/v1/investment_carnival.proto";
import "common/common.proto";
import "user_growth_center/v1/models.proto";

option go_package = "api/user_growth_center/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  rpc GetUserInfo(GetUserInfoRequest) returns (GetUserInfoReply){
    option (google.api.http) = {get: "/v1/user_info"};
  }
  rpc StringReply(StringReplyRequest) returns (common.StringReply){
    option (google.api.http) = {get: "/v1/string_reply"};
  }
  /*=============================用户身份成长中心=================================*/
  // 身份升级规则
  rpc GetIdentityRule(GetIdentityRuleRequest) returns (GetIdentityRuleReply){
    option (google.api.http)= {get:"/v1/app/identity/rule"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "身份升级规则",tags: ["身份成长中心"]};
  }
 // 身份成长中心详情页
  rpc GetUserGrowthDetail(GetUserGrowthDetailRequest) returns (GetUserGrowthDetailReply){
    option (google.api.http)={get:"/v1/app/identity/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户成长中心详情页",tags: ["身份成长中心"]};
  }
 // 成长中心入展示开关
  rpc GetGrowthCenterEntry(GetGrowthCenterEntryRequest) returns (GetGrowthCenterEntryReply){
    option (google.api.http)={get:"/v1/app/identity/switch"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "成长中心展示开关",tags: ["身份成长中心"]};
  }

  // 获取我的页面banner位置轮播图（有屏蔽点位）
  rpc GetIdentityCarousel(GetIdentityCarouselRequest) returns (GetIdentityCarouselReply){
    option (google.api.http)={get:"/v1/app/identity/carousel"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "轮播图banner",tags: ["身份成长中心"]};
  }

  // 获取身份分享图
  rpc GetIdentityShare(GetIdentityShareRequest) returns (GetIdentityShareReply){
    option (google.api.http)={get:"/v1/app/identity/share"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "分享身份",tags: ["身份成长中心"]};
  }

  // 升级身份入口
  rpc GetUpgradeIdentity(GetUpgradeIdentityRequest) returns (GetUpgradeIdentityReply){
    option (google.api.http)={get:"/v1/app/identity/entry"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "升级身份入口",tags: ["身份成长中心"]};
  }
  // APP 切换身份
  rpc PostUserIdentitySwitch(PostUserIdentitySwitchRequest)  returns (PostUserIdentitySwitchReply){
    option (google.api.http)={post:"/v1/app/identity" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "APP切换身份",tags: ["身份成长中心"]};
  }


  /*=============================投资狂欢节================================*/
  // 活动主页
  rpc GetActivityMain(GetActivityMainRequest) returns (GetActivityMainReply){
    option (google.api.http)={get:"/v1/app/investment/main"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "活动主页",tags: ["投资狂欢节"]};
  }

  // 活动主页分享
  rpc GetActivityShare(GetActivityShareRequest) returns (GetActivityShareReply){
    option (google.api.http)={get:"/v1/app/investment/share"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "活动主页分享",tags: ["投资狂欢节"]};
  }

  // 规则和说明
  rpc GetContent(GetContentRequest) returns (GetContentReply){
    option (google.api.http)={get:"/v1/app/content"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "规则和说明",tags: ["投资狂欢节"]};
  }
  // 搜索推荐交易商
  rpc GetRecommendTrader(GetRecommendTraderRequest) returns (GetRecommendTraderReply){
    option (google.api.http)={get:"/v1/app/search/recommend"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "搜索推荐交易商",tags: ["投资狂欢节"]};
  }
  // 全球交易商列表
  rpc GetGlobalTrader(GetGlobalTraderRequest) returns (GetGlobalTraderReply){
    option (google.api.http)={get:"/v1/app/global/trader"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "全球交易商列表",tags: ["投资狂欢节"]};
  }
// 交易商活动详情页
  rpc GetTraderActivityPage(GetTraderActivityPageRequest) returns (GetTraderActivityPageReply){
    option (google.api.http)={get:"/v1/app/trader/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "交易商活动详情页",tags: ["投资狂欢节"]};
  }

  // 获取推荐官列表
  rpc GetRecommenderList(GetRecommenderListRequest) returns (GetRecommenderListReply){
    option (google.api.http)={get:"/v1/app/recommender/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "推荐官列表",tags: ["投资狂欢节"]};
  }

  // 瓜分现金奖池
  rpc GetRewordPoolDetail(GetRewordPoolDetailRequest) returns (GetRewordPoolDetailReply){
    option (google.api.http)={get:"/v1/app/reword/pool"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "瓜分现金奖池详情页",tags: ["投资狂欢节"]};
  }

  // 用户签到
  rpc UserCheckIn(UserCheckInRequest) returns (UserCheckInReply){
    option (google.api.http)={post:"/v1/app/user/checkin" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户签到",tags: ["投资狂欢节"]};
  }


  // 我的奖励
  rpc GetRewordDetail(GetRewordDetailRequest) returns (GetRewordDetailReply){
    option (google.api.http)={get:"/v1/app/rewords/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "我的奖励",tags: ["投资狂欢节"]};
  }

  // 幸运大抽奖详情页
  rpc GrandLuckyDraw(GrandLuckyDrawRequest) returns (GrandLuckyDrawReply){
    option (google.api.http)={get:"/v1/app/luckydraw/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "幸运大抽奖详情页",tags: ["投资狂欢节"]};
  }

  // 开始抽奖
  rpc StartDraw(GrandLuckyDrawRequest) returns (StartDrawReply){
    option (google.api.http)={post:"/v1/app/startdraw",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "开始抽奖",tags: ["投资狂欢节"]};
  }

  // 入金一手
  rpc GetDepositDetail(GetDepositDetailRequest) returns (GetDepositDetailReply){
    option (google.api.http)={get:"/v1/app/deposit/detail"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "入金一手",tags: ["投资狂欢节"]};
  }

  // 搜索交易商
  rpc SearchTrader(SearchTraderRequest) returns (SearchTraderReply) {
    option (google.api.http)={get:"/v1/app/search/trader"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "搜索交易商",tags: ["投资狂欢节"]};
  }

  // 轮播图和广告
  rpc GetBanner(GetBannerRequest) returns (GetBannerReply) {
    option (google.api.http)={get:"/v1/app/investment/banner"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "轮播图和广告",tags: ["投资狂欢节"]};
  }

  // 给用户助力
  rpc Assist(AssistRequest) returns (AssistReply) {
    option (google.api.http)={get:"/v1/app/investment/assist"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "给用户助力",tags: ["投资狂欢节"]};
  }

  // 观看直播任务完成
    rpc WatchLiveCompleted(WatchLiveRequest) returns (WatchLiveReply) {
      option (google.api.http)={get:"/v1/app/live/completed"};
      option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "观看直播任务完成",tags: ["投资狂欢节"]};
    }

  // 好友助力
  rpc FriendAssistance(FriendAssistanceRequest) returns (FriendAssistanceReply) {
    option (google.api.http)={get:"/v1/app/investment/friendassistance"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "好友助力",tags: ["投资狂欢节"]};
  }
  //获取邀请奖励弹窗数据
  rpc GetInvitationPopupData(GetInvitationPopupDataRequest) returns (GetInvitationPopupDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getinvitationpopupdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请奖励弹窗数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取邀请奖励领取banner数据
  rpc GetInviteRewardBannerData(GetInviteRewardBannerDataRequest) returns (GetInviteRewardBannerDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getinviterewardbannerdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请奖励领取banner数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取分享推广链接数据
  rpc GetShareLinkData(GetShareLinkDataRequest) returns (GetShareLinkDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/getsharelinkdata"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取分享推广链接数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取邀请用户记录数据
  rpc GetInvitedRecordData(GetInvitedRecordDataRequest) returns (GetInvitedRecordDataReply) {
    option (google.api.http) = {get: "/v1/userdivision/invitedrecord"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请用户记录数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取用户vps等级
  rpc GetVpsLevel(common.EmptyRequest) returns (GetVpsLevelReply) {
    option (google.api.http) = {get: "/v1/userdivision/getuservpslevel"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户vps等级",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //----------------------------------答题模块----------------------------------//
  //获取试题数据
  rpc GetQuizInfo(common.EmptyRequest) returns (GetQuizInfoReply) {
    option (google.api.http) = {get: "/v1/userdivision/getquizinfo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取试题数据",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //提交试卷
  rpc SubmitQuiz(SubmitQuizRequest) returns (SubmitQuizReply) {
    option (google.api.http) = {post: "/v1/userdivision/submitquiz",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "提交试卷",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //获取试卷记录
  rpc GetQuizRecord(common.EmptyRequest) returns (GetQuizRecordReply) {
    option (google.api.http) = {get: "/v1/userdivision/getquizrecord"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取试卷记录",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //----------------------------------答题模块----------------------------------//
  //----------------------------------奖励配置----------------------------------//
  rpc GetInviterActivityTime(common.EmptyRequest) returns (GetInviterActivityTimeReply) {
    option (google.api.http) = {get: "/v1/userdivision/activity/time"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请活动时间",tags: ["用户分裂"]};
  }
  rpc UpdateInviterActivityTime(UpdateInviterActivityTimeRequest) returns (UpdateInviterActivityTimeReply) {
    option (google.api.http) = {put: "/v1/userdivision/activity/time",body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "更新邀请活动时间",tags: ["用户分裂"]};
  }
  rpc GetUserDivisionRewardLevelInfo(common.EmptyRequest) returns ( GetUserDivisionRewardLevelInfoReply) {
    option (google.api.http) = {get: "/v1/userdivision/reward"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户分裂奖励层级配置",tags: ["用户分裂"]};
  }
  // 新增用户分裂奖励配置
  rpc CreateUserDivisionRewardLevel (CreateUserDivisionRewardLevelRequest) returns (CreateUserDivisionRewardLevelReply) {
    option (google.api.http) = { post: "/v1/userdivision/reward",body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = { summary: "新增用户分裂奖励层级配置",tags: ["用户分裂"]};
  }
  // 修改用户分裂奖励配置
  rpc UpdateUserDivisionRewardLevel (UpdateUserDivisionRewardLevelRequest) returns (common.EmptyReply) {
    option (google.api.http) = { put: "/v1/userdivision/reward/{id}",body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = { summary: "修改用户分裂奖励层级配置",tags: ["用户分裂"]};
  }
  // 删除用户分裂奖励配置
  rpc DeleteUserDivisionRewardLevel (DeleteUserDivisionRewardLevelRequest) returns (common.EmptyReply) {
    option (google.api.http) = { delete: "/v1/userdivision/reward/{id}" };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = { summary: "删除用户分裂奖励层级配置",tags: ["用户分裂"]};
  }
  //----------------------------------奖励配置----------------------------------//
  //----------------------------------邀请----------------------------------//
  // 新增用户分裂邀请记录
  rpc CreateUserDivisionInvitation (CreateUserDivisionInvitationRequest) returns (CreateUserDivisionInvitationReply) {
    option (google.api.http) = { post: "/v1/userdivision/invitation",body: "*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = { summary: "新增用户分裂邀请记录",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For",
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id",
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id",
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode",
            type: STRING,
            description: "三位国家code",
            required: true
          },
          {
            name: "LanguageCode",
            type: STRING,
            description: "当前语言code",
            required: true
          },
          {
            name: "BasicData",
            type: STRING,
            description: "basic data",
            required: true
          },
          {
            name: "PreferredLanguageCode",
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id",
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      }
    };
  }
  // 获取用户分裂邀请人统计数据列表
  rpc GetUserDivisionInviterStatisticsInfo(GetUserDivisionInviterStatisticsInfoRequest) returns ( GetUserDivisionInviterStatisticsInfoReply) {
    option (google.api.http) = {get: "/v1/userdivision/inviterInfo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户分裂邀请人统计数据列表",tags: ["用户分裂"]};
  }
  // 获取用户分裂被邀请人数据列表
  rpc GetUserDivisionInviteeInfo(GetUserDivisionInviteeInfoRequest) returns ( GetUserDivisionInviteeInfoReply) {
    option (google.api.http) = {get: "/v1/userdivision/inviteeInfo"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取用户分裂被邀请人数据列表",tags: ["用户分裂"]};
  }
  // 获取邀请活动VPS升级记录列表
  rpc GetUserDivisionActivityList(GetUserDivisionActivityListRequest) returns ( GetUserDivisionActivityListReply) {
    option (google.api.http) = {get: "/v1/userdivision/activities"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取邀请活动VPS升级记录列表",tags: ["用户分裂"]};
  }

  //----------------------------------邀请----------------------------------//
  //----------------------------------裂变流程----------------------------------//
  // 裂变活动详情
  rpc GetUserDivisionActivityInfo(common.EmptyRequest) returns ( GetUserDivisionActivityInfoReply) {
    option (google.api.http) = {get: "/v1/userdivision/activity"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "裂变活动详情",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 裂变活动入口
  rpc GetUserDivisionEntry(common.EmptyRequest) returns (GetUserDivisionEntryReply) {
    option (google.api.http) = {get: "/v1/userdivision/entry"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "裂变活动入口",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // 升级VPS
  rpc UpgradeVPS(UpgradeVPSRequest) returns (UpgradeVPSReply) {
    option (google.api.http) = {get: "/v1/userdivision/upgrade/vps"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "升级VPS",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  // VPS升级回调接口
  rpc PostUpgradeVPSStatus(PostUpgradeVPSStatusRequest) returns (PostUpgradeVPSStatusReply) {
    option (google.api.http) = {get: "/v1/userdivision/upgrade/callback"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "升级VPS回调",tags: ["用户分裂"],
      parameters: {
        headers: [
          {
            name: "X-Forwarded-For"
            type: STRING,
            description: "客户端IP"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "CountryCode"
            type: STRING,
            description: "三位国家code",
            required:true
          },
          {
            name: "LanguageCode"
            type: STRING,
            description: "当前语言code",
            required:true
          },
          {
            name: "BasicData"
            type: STRING,
            description: "basic data",
            required:true
          },
          {
            name: "PreferredLanguageCode"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  //----------------------------------裂变流程----------------------------------//
}

