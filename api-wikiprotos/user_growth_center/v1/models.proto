syntax = "proto3";

package api.user_growth_center.v1;
import "protoc-gen-openapiv2/options/annotations.proto";
import "google/protobuf/descriptor.proto";

option go_package = "api/user_growth_center/v1;v1";

message GetUserInfoRequest {}
message GetUserInfoReply {
  string user_id = 1;
}

message UpdateUserRequest {}
message UpdateUserReply {}

message StringReplyRequest {}
message StringReplyReply {}
//获取邀请奖励弹窗数据
message GetInvitationPopupDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetInvitationPopupDataReply{
  string title=1[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  UserData userData=2[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  repeated string contentCol = 3[json_name="contentCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容字符串数组"}];
}
//获取邀请奖励领取banner数据
message GetInviteRewardBannerDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetInviteRewardBannerDataReply{
  string title=1[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
  UserData userData=2[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  repeated string contentCol = 3[json_name="contentCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容字符串数组"}];
  string vpsVerifyImgUrl=4[json_name="vpsVerifyImgUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps图片验证Url"}];
  string vpsVerifyContent=5 [json_name="vpsVerifyContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps验证按钮多语言内容"}];
  string vpsVerifyBgColor=6  [json_name="vpsVerifyBgColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps验证按钮背景色"}];
}
//获取分享推广链接数据
message GetShareLinkDataRequest{
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
}
message GetShareLinkDataReply{
  message ShareLinkData{
    string shareUrl = 1[json_name="shareUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享链接url"}];
    string title = 2[json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
    repeated string contentCol = 3[json_name="contentCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"内容字符串数组"}];
    string imgUrl = 4[json_name="imgUrl",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享图片url"}];
  }
  UserData userData=1[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
  ShareLinkData shareLinkData=2[json_name="shareLinkData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享链接数据"}];
}
//获取邀请记录数据
message GetInvitedRecordDataRequest{
  int32 pageIndex=1 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=2 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"每页大小"}];
}
message GetInvitedRecordDataReply{
  message InvitedRecordData{
    UserData userData=1[json_name="userData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
    int32 bindingStatus=2[json_name="bindingStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"绑定状态 0 未绑定 1已绑定"}];
    string updateTime=3[json_name="updateTimeupdateTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"更新时间"}];
  }
  //邀请总人数
  int32 invitedCount=1[json_name="invitedCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请总人数"}];
  //升级次数
  int32 upgradedCount=2[json_name="upgradedCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级次数"}];
  repeated InvitedRecordData invitedRecordDataCol=3[json_name="invitedRecordDataCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请记录数据集合"}];
}
message GetVpsLevelReply{
  int32 level = 1[json_name="level",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps等级"}];
}
//----------------------------------答题模块----------------------------------//
message UserData {
  string userId = 1[json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string avatarAddress = 2[json_name="avatarAddress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"头像地址"}];
  string nickName = 3[json_name="nickName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"昵称"}];
  string wikiId = 4[json_name="wikiId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"天眼id"}];
}
// 题目类型枚举
enum QuestionType {
  UNSPECIFIED = 0; // 未指定
  SINGLE_CHOICE = 1; // 单选
  MULTIPLE_CHOICE = 2; // 多选
}
// 获取试卷信息
message QuizInfo {
  message Option {
    string option = 1[json_name="option",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"选项标识"}];
    string content = 2[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"选项内容"}];
  }
  string questionId = 1[json_name="questionId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"题目id"}];
  QuestionType questionType = 2[json_name="questionType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"题目类型"}];
  string content = 3[json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"题目内容"}];
  repeated Option options = 4[json_name="options",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"选项列表"}];
}
message GetQuizInfoReply {
  repeated QuizInfo quizInfo = 1[json_name="quizInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"试题数据"}];
}

//用户提交试卷
message SubmitQuizRequest {
  message UserAnswer {
    string questionId = 1[json_name="questionId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"题目id"}];
    repeated string selectedOptions = 2[json_name="selectedOptions",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户选项"}];
  }
  repeated UserAnswer answers = 1[json_name="answers",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户选择内容"}];
}
message SubmitQuizReply {
  bool isPass = 1[json_name="isPass",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否通过"}];
  repeated string incorrectQuestionIds = 2[json_name="incorrectQuestionIds",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"正确选项id集合"}];
}

//获取试卷记录
message GetQuizRecordReply {
  message QuizResult {
    string questionId = 1[json_name="questionId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"层级ID"}];
    repeated string selectedOptions = 2[json_name="selectedOptions",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"层级ID"}];
    bool isCorrect = 3[json_name="isCorrect",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"层级ID"}];
  }
  bool isPass = 1[json_name="isPass",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"层级ID"}];
  repeated QuizResult results = 2[json_name="results",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"层级ID"}];
}
//----------------------------------答题模块----------------------------------//
//----------------------------------配置奖励层级----------------------------------//
//活动时间
 message   InviterActivityTime{
  int64 EffectiveDate=1[json_name="EffectiveDate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动开始时间"}];
  int64 ExpireDate=2[json_name="ExpireDate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动结束时间"}];
 }
//获取活动时间
message GetInviterActivityTimeReply{
  InviterActivityTime InviterActivityTime=1[json_name="InviterActivityTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动时间"}];
}
//修改活动时间
message UpdateInviterActivityTimeRequest{
  InviterActivityTime InviterActivityTime=1[json_name="InviterActivityTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动时间"}];
}
message UpdateInviterActivityTimeReply{
  InviterActivityTime InviterActivityTime=1[json_name="InviterActivityTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"活动时间"}];
}
//获取奖励层级
message GetUserDivisionRewardLevelInfoReply{
  message RewardLevel{
    string id=1[json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"id"}];
    int32 threshold=2[json_name="threshold",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"梯度人数阈值"}];
    int32 level=3[json_name="level",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级"}];
    string reward=4[json_name="reward",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖励名称"}];
    string rewardDetails=5[json_name="rewardDetails",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖励详情描述"}];
  }
  repeated RewardLevel RewardLevelCol=1[json_name="rewardLevelCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖励层级数据"}];
}
// 新增用户分裂奖励配置
message CreateUserDivisionRewardLevelRequest {
  int32 threshold = 1 [json_name = "threshold", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "梯度人数阈值"}];
  int32 level=2[json_name="level",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级"}];
}
message CreateUserDivisionRewardLevelReply {
  string id = 1 [json_name = "id", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
}

// 修改用户分裂奖励配置
message UpdateUserDivisionRewardLevelRequest {
  string id = 1 [json_name = "id", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
  int32 threshold = 2 [json_name = "threshold", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "梯度人数阈值"}];
  int32 level=3[json_name="level",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"等级"}];
}

// 删除用户分裂奖励配置
message DeleteUserDivisionRewardLevelRequest {
  string id = 1 [json_name = "id", (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "id"}];
}

// 新增邀请记录
message CreateUserDivisionInvitationRequest {
  string inviteeId = 1[json_name="inviteeId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"受邀人id"}];
  string inviteeDeviceId = 3[json_name="inviteeDeviceId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"受邀人设备id"}];
  string inviterId = 4[json_name="inviterId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请人id"}];
}
message CreateUserDivisionInvitationReply {
  string id = 1 [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请记录id"}];
}
//获取邀请人统计列表
message GetUserDivisionInviterStatisticsInfoRequest {
  string keywords = 1[json_name="keywords",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关键词"}];
  int32 pageIndex=3  [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=4  [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页大小"}];
}
message GetUserDivisionInviterStatisticsInfoReply {
  message  InviterStatistics{
    UserData userInfo=1[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
    int32 invitedCount=2[json_name="invitedCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请人数"}];
  }
  repeated InviterStatistics inviterStatisticsCol = 1 [json_name="inviterStatisticsCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请人数据集合"}];
}
//获取受邀人信息列表
message GetUserDivisionInviteeInfoRequest {
  string inviterId=1[json_name="inviterId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"邀请人id"}];
  int32 bindingStatus =2[json_name="bindingStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实盘状态"}];
  int32 pageIndex=3[json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=4[json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页大小"}];
}
message GetUserDivisionInviteeInfoReply {
  message InviteeExtraInfo{
    string inviteeIp=1[json_name="inviteeIp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"打开邀请时的ip"}];
    string inviteeRegisterIp=2[json_name="inviteeRegisterIp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"新用户注册ip"}];
    string inviteeDeviceId=3[json_name="inviteeDeviceId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"新用户设备id"}];
    int32 bindingStatus=4[json_name="bindingStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"实盘状态"}];
  }
  message  InviterInfo{
    UserData userInfo=1[json_name="userInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"受邀人信息"}];
    InviteeExtraInfo inviteeExtraInfo=2[json_name="inviteeExtraInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"受邀人额外信息"}];
  }
  repeated InviterInfo inviterInfoCol = 1 [json_name="inviterInfoCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"受邀人数据集合"}];
}

// 获取用户分裂邀请活动VPS列表
message GetUserDivisionActivityListRequest{
  string keywords=1[json_name="keywords",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关键词"}];
  int32  status=2[json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"状态 0=未完成 1=未领取 2=升级中 3=已领取"}];
  int32 pageIndex=3[json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
  int32 pageSize=4[json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页大小"}];
}
message GetUserDivisionActivityListReply{
  message  VPSUpgradeRecord{
    UserData userInfo=1[json_name="keywords",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户信息"}];
    int32  threshold=2[json_name="threshold",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"所需实盘人数"}];
    string  reward=3[json_name="reward",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级配置点击"}];
    int32  status=4[json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"状态 0=未完成 1=未领取 2=升级中 3=已领取"}];
    string  awardTime=5[json_name="awardTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"获得奖励时间"}];
    string  updateBy=6[json_name="updateBy",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"修改人"}];
    string  updateAt=7[json_name="updateAt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"修改时间"}];
  }
  repeated  VPSUpgradeRecord vpsUpgradeRecordCol=1[json_name="vpsUpgradeRecordCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"vps升级记录集合"}];
}

//----------------------------------配置奖励层级----------------------------------//
// 裂变活动详情
message GetUserDivisionActivityInfoReply {
  message AwardTaskItem {
    string taskId=1 [json_name="taskId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务ID"}];
    int64 recommendUser=2 [json_name="recommendUser",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐人数"}];
    string taskContent=3 [json_name="taskContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务内容"}];
    int32 taskStatus=4  [json_name="taskStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务状态 0 未完成 1 待领取 2 审核中 3 审核失败 4 已领取"}];
    string taskProgress=5  [json_name="taskProgress",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务进度"}];
    int32 taskLevel=6  [json_name="taskLevel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务等级"}];
  }
  message AwardData {
    int32 awardType =1[json_name="awardType",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖励方式，1:VPS升级 2:签约奖励"}];
    int64 recommendUser=2[json_name="recommendUser",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"推荐用户数"}];
    int64 upgradeTimes=3[json_name="upgradeTimes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级次数"}];
    repeated AwardTaskItem taskCol=4 [json_name="taskCol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级任务列表"}];
  }
  int32 status=1  [json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"安全大使状态 0:未成为安全大使 1:已成为安全大使"}];
  AwardData awardData=2 [json_name="awardData",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"奖励方式"}];
}

// 裂变活动入口
message GetUserDivisionEntryReply {
  bool showEntryFlag =1  [json_name="showEntryFlag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示入口标识"}];
  string image=2  [json_name="image",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"入口图片"}];
  string address=3  [json_name="address",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"入口链接地址"}];
  string title=4  [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"展示标题,例如:安全大使计划"}];
  string slogon=5 [json_name="slogon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"入口宣传语,例如:Wiki专属招募"}];
  repeated string color=6 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"入口宣传语底色"}];
}

// 升级VPS
 message  UpgradeVPSRequest {
   string taskId =1 [json_name="taskId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务ID"}];
   int32 taskLevel =2 [json_name="taskLevel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务等级"}];
 }
 // 升级VPS返回
 message  UpgradeVPSReply {
   bool  upgradeSucceed=1  [json_name="upgradeSucceed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"更新成功"}];
 }

 // VPS升级回调接口
 message PostUpgradeVPSStatusRequest{
  string userId=1  [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户ID"}];
  string taskId =2 [json_name="taskId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"任务ID"}];
  bool upgradeSucceed=3  [json_name="upgradeSucceed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"升级成功"}];
 }
// VPS升级回调接口返回
 message PostUpgradeVPSStatusReply {
  bool updateSucceed =1   [json_name="updateSucceed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"更新成功状态"}];
 }

//----------------------------------MQ消息模型----------------------------------//
// 用户出入金(账户余额)
message TraderDepositWithdrawMsg {
  message TraderDepositWithdrawData {
    int64 id=1;
    int64  mt_user_number=2;
    string mt_type=3;
    int64 amount=4;
    string  currency=5;
    int64 operation_time=6;
    int64 create_timestamp=7;
  }
  string database=1;
  string  table=2;
  string  type=3;
  TraderDepositWithdrawData data=4;
  TraderDepositWithdrawData old=5;
}
// 账户映射（实盘绑定）
message TraderAccountMsg {
  message TraderAccountData {
    int64 id=1;
    string user_id=2;
    string account_id=3;
    int64 service_id=4;
    int64 mt_user_number=5;
    string password=6;
    string currency=7;
    string mt_type=8;
    int64 real_type=9;
    int64 read_type=10;
    int64 leverage=11;
    int32 is_delete=12;
    int64 create_timestamp=13;
  }
  string database=1;
  string  table=2;
  string  type=3;
  TraderAccountData data=4;
  TraderAccountData old=5;
}
// 订单（交易记录）
message TraderOrdersMsg {
  message TraderOrdersData {
      int32 id = 1;
      int64 mt_user_number = 2;
      string mt_type = 3;
      int64 ticket = 4;
      string symbol = 5;
      int32 operation = 6;
      double slippage = 7;
      double stop_loss = 8;
      double take_profit = 9;
      double swap = 10;
      double volume = 11;
      double close_price = 12;
      int64 close_time = 13;
      double commission = 14;
      int64 expiration = 15;
      double lots = 16;
      int64 magic_number = 17;
      double open_price = 18;
      int64 open_time = 19;
      double profit = 20;
      double rate_close = 21;
      double rate_margin = 22;
      double rate_open = 23;
      double fee = 24;
      double close_lots = 25;
      string close_comment = 26;
      int32 deal_type = 27;
      int64 create_timestamp = 28;
    }

  string database=1;
  string  table=2;
  string  type=3;
  TraderOrdersData data=4;
  TraderOrdersData old=5;
}
// 用户变更（用户注册）
message UserMsg {
  message UserData {
    string UserId = 1;
    string RegistrationIp = 2;
    string RegistrationTime=3;
    string DeviceCode=4;
    /*
    string userId = 1;
    string nickName = 2;
    int32 sex = 3;
    int32 identityType = 4;
    optional string userName = 5;
    string password = 6;
    string phoneNumber = 7;
    string encryptedPhone = 8;
    string md5Phone = 9;
    optional string email = 10;
    string avatarAddress = 11;
    int32 isDefaultAvatar = 12;
    int32 isDefaultNick = 13;
    string registrationTime = 14;
    int32 registrationType = 15;
    string registrationIp = 16;
    string ipLocation = 17;
    int32 status = 18;
    int32 level = 19;
    int32 score = 20;
    string realName = 21;
    string idNumber = 22;
    string certificationTime = 23;
    double balance = 24;
    double spentAmount = 25;
    string vipExpiredTime = 26;
    int32 remainViews = 27;
    string encryptedIdNumber = 28;
    string md5_id_number = 29;
    int32 application_type = 30;
    string last_name = 31;
    string first_name = 32;
    string birthday = 33;
    string certification_platform = 34;
    string contact_number = 35;
    string encrypted_contact = 36;
    string md5_contact = 37;
    int32 is_email_confirmed = 38;
    string registration_country = 39;
    string registration_language = 40;
    string registration_version = 41;
    string qq = 42;
    string weibo = 43;
    string wechat = 44;
    string area_code = 45;
    int32 is_phone_confirmed = 46;
    int32 certification_type = 47;
    int32 registration_platform = 48;
    string device_code = 49;
    string device_information = 50;
    string area_flag = 51;
    string salutation = 52;
    string md5_phone32 = 53;
    string phone_location = 54;
    string middle_name = 55;
    string user_first_name = 56;
    string wiki_fx_number = 57;
    int32 lock = 58;
    int32 lock_bit = 59;
    int32 is_vest_account = 60;
    string encrypted_area_code_phone = 61;
    string wikibit_intro = 62;
    int32 wikibit_is_kol = 63;
    string wikifx_intro = 64;
     */
  }

  string database=1;
  string  table=2;
  string  type=3;
  UserData data=4;
  UserData old=5;
}
