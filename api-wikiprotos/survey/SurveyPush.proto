syntax = "proto3";
package Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts;

option go_package="api/surveypush/v1;v1";
message ContainerData_SurveyList {
   int64 Total = 1;
   repeated SurveyList Items = 2;
}
message ExhibitionSurvey { // 展会信息
   string ExhibitionId = 1; // 展会id
   int32 Level = 2; // 展会等级
   string Position = 3; // 展会位置
   string ExpoLogo = 4; // logo
   string ExpomapColor = 5; // 颜色
   string ExpoBgColor = 6; // 背景色
   string ExpoId = 7; // id
   string ExpoName = 8; // 名称
   int64 SurveyDateTimestamp = 9; // 实勘时间时间戳
}
message ImageInfo { // 图片信息
   string Url = 1; // 图片url
   int32 Width = 2; // 宽
   int32 Height = 3; // 高
}
message QuerySurveyListRequest { // 查询实勘列表请求
   int32 Pageindex = 1; // 页数
   int32 PageSize = 2; // 页大小
}
message SurveyDetail { // 实勘详情
   string SurveyId = 1; // 实勘id
   string TraderCode = 2; // 实勘交易商、券商Code
   string Seal = 3; // 印章
   string Surveyor = 4; // 实勘人
   string Flag = 5; // 实勘到访地区旗帜
   string CountryName = 6; // 当前实勘访问公司所在国家名称
   string Evaluate = 7; // 实勘评价
   string EvaluateColor = 8; // 评价颜色
   string BaiduImg = 9; // 百度坐标缩略图
   string GoogleImg = 10; // 谷歌坐标缩略图
   string Content = 11; // 实勘正文
   string VideoCover = 12; // 实勘视频封面图片地址
   string Cover = 13; // 实勘的封面图片地址
   int32 Type = 14; // 实勘类型
}
message SurveyDetailQueryRequest { // 查询实勘详情
   string Sid = 1; // 实勘id
}
message SurveyList { // 实勘列表
   string SurveyId = 1; // 实勘id
   string TraderCode = 2; // 实勘交易商、服务商code
   string Evaluate = 3; // 实勘评价
   int32 Type = 4; // 实勘类型
   string EvaluateColor = 5; // 评价颜色
   int32 Level = 6; // 实勘评价的等级，数字由小到大对应等级依次递减。
   string Address = 7; // 展会地址
   int64 SurveyDateTimestamp = 8; // 实勘时间时间戳
   repeated string Images = 10;
   string Cover = 12; // 实勘的封面图片地址
   string Ico = 13; // 交易商ICO图标地址
   string Flag = 14; // 实勘到访地区旗帜
   string Company = 15; // 当前实勘交易商的名称
   string LocalCompany = 16; // 当前实勘交易商、券商的名称
   string VRCode = 17;
   string VisitCountryCode = 18; // 实勘到访地区代码
   SurveyVR VR = 19;
   string CountryName = 20; // 当前实勘访问公司所在国家名称
   string CityName = 21; // 当前实勘访问公司所在城市名称
   string Title = 22; // 实勘标题
   string Summary = 23; // 实勘摘要
   ExhibitionSurvey ExhibitionSurvey = 24; // 展会信息
   ImageInfo Banner = 25; // 头图
   ImageInfo FirstImage = 26; // 内容里面的第一张图
   string Seal = 27; // 印章
   string Surveyor = 28; // 实勘人
   string BaiduImg = 29; // 百度坐标缩略图
   string GoogleImg = 30; // 谷歌坐标缩略图
   bool IsServiceProvider = 31; // 是否服务商
   string TextPart = 32; // 部分正文内容，暂时取120个字，web端使用
   repeated ImageInfo AllImage = 33;
}
message SurveyQueryByIdsRequest { // 查询实勘通过Ids
   repeated string Sids = 1;
}
message SurveyVR { // vr信息
   string Code = 1; // VR唯一标识Code
   string Cover = 2; // VR封面图
   string Loading = 3; // VR加载图
   string Area = 4; // 实勘所在区域
   string Flag = 5; // 勘察地区旗帜
   int32 Time = 6; // 实勘时间
   string Url = 9; // VR详情地址
}
message UnityReply_ContainerData_SurveyList { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   ContainerData_SurveyList Result = 3; // 返回结果
}
message UnityReply_List_SurveyList { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   repeated SurveyList Result = 3; // 返回结果
}
message UnityReply_SurveyDetail { // 统一返回模型
   bool IsSuccess = 1; // 是否成功
   string Message = 2; // 错误信息
   SurveyDetail Result = 3; // 返回结果
}
service SurveyPushService {
   rpc QuerySurveyDetail (SurveyDetailQueryRequest) returns (UnityReply_SurveyDetail); // 查询实勘详情
   rpc QuerySurveyDetailByIds (SurveyQueryByIdsRequest) returns (UnityReply_List_SurveyList); // 通过ids查询实勘
   rpc QuerySurveyList (QuerySurveyListRequest) returns (UnityReply_ContainerData_SurveyList); // 查询实勘列表
   rpc QuerySurveyTop100 (QuerySurveyListRequest) returns (UnityReply_List_SurveyList); // 查询Top100数据，调试用
}
