syntax = "proto3";

package api.forum.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "api/forum/v1;v1";

service MyService{
    rpc GetMediateList(GetMediateInfoRequest)returns(GetMediateInfoReply){
        option (google.api.http) = {post: "/v1/app/forum/getmediatelist",body:"*"};
        option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取调解列表",tags: ["调解"]};
    }
    rpc GetMediateDetail(GetMediateDetailRequest)returns(GetMediateDetailReply){
        option (google.api.http) = {post: "/v1/app/forum/getmediatedetail",body:"*"};
        option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取调解详情",tags: ["调解"]};
    }
    rpc GetLatest(Request)returns(GetMediateListReply){
        option (google.api.http) = {get: "/v1/app/forum/getlatest"};
        option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取最新的调解列表",tags: ["调解"]};
    }
    rpc GetMyMediateList(GetMyMediateListRequest)returns(GetMediateListReply){
        option (google.api.http) = {post: "/v1/app/forum/getmymediatelist",body:"*"};
        option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取调解列表",tags: ["调解"]};
    }
    rpc GetMediates(GetMyMediateListRequest)returns(GetMediateListReply){
        option (google.api.http) = {post: "/v1/app/forum/GetMediates",body:"*"};
        option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取调解列表",tags: ["调解"]};
    }
}

message Request{

}

message GetMyMediateListRequest{
    MediateStatus auditStatus =35 [json_name="auditStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问审核状态"}];
    string countryCode =2 [json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家编码"}];
    string uid =3 [json_name="uid",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
    Platform platform =4 [json_name="platform",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"wikifx传1，wikibit传2"}];
    int32 pageIndex =5 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分页页码"}];
    int32 pageSize =6 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分页数"}];
    bool isApp =7 [json_name="isApp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否app调用"}];
}

message GetMediateInfoRequest{
    repeated string codes =1 [json_name="codes",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解编码"}];
    int32 pageIndex =2 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"页码"}];
    int32 pageSize =3 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分页大小"}];
}

message GetMediateInfoReply{
    int32 total =1 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
    repeated MediateInfo items =2 [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解数据"}];
}

message MediateInfo{
    string code =1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解编码"}];
    string languageCode =2 [json_name="languageCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子语言"}];
    string originalTitle =3 [json_name="originalTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子标题"}];
    string originalContent =4 [json_name="originalContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子内容"}];
    string title =5 [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子翻译标题"}];
    string content =6 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子翻译内容"}];
    repeated Image images =7 [json_name="images",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
    int64 closedTimestamp =8 [json_name="closedTimestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关闭时间戳"}];
    string amount =9 [json_name="amount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额"}];
    string objectLabel =10 [json_name="objectLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权对象标签"}];
    string amountLabel =11 [json_name="amountLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额标签"}];
    string categoryLabel =12 [json_name="categoryLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权问题标签"}];
    string requirementLabel =13 [json_name="requirementLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权要求标签"}];
    string requirement =14 [json_name="requirement",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权要求"}];
    string category =15 [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权问题"}];
    string symbol =17 [json_name="symbol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币符号"}];
    string traderCode =18 [json_name="traderCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解对象"}];
    string userId =19 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
    int64 createdAt =20 [json_name="createdAt",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布时间戳"}];
    string annoation =21 [json_name="annoation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
    string color =22 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标状态颜色"}];
    string newColor =23 [json_name="newColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"新角标状态颜色"}];
    string stamp =24 [json_name="stamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"印章地址"}];
    MediateStatus appendAuditStatus =25 [json_name="appendAuditStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问审核状态"}];
    string id =26 [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解id"}];
    string currencyCode =27 [json_name="currencyCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币"}];
    string elapsed =28 [json_name="elapsed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间"}];
    string closeLabel =29 [json_name="closeLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间标签"}];
    int64 createdAtMili =30 [json_name="createdAtMili",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布时间戳毫秒"}];
}

message GetMediateListReply{
    int32 total =1 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"总数"}];
    repeated MediateList items =2 [json_name="items",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解数据"}];
}

message MediateList{
    string code =1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子编码"}];
    repeated Category categories =2 [json_name="categories",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"曝光分类"}];
    string language =3 [json_name="language",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子语言"}];
    string translanguage =4 [json_name="translanguage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子语言"}];
    bool isChinese =5 [json_name="isChinese",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否中国"}];
    string originalTitle =6 [json_name="originalTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子标题"}];
    string originalContent =7 [json_name="originalContent",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子内容"}];
    string title =8 [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子翻译标题"}];
    string content =9 [json_name="content",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子翻译内容"}];
    string countryCode =10 [json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子发布国家编码"}];
    string countryName =11 [json_name="countryName",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子发布国家"}];
    string flag =12 [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布国家国旗"}];
    string time =13 [json_name="time",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"创建时间"}];
    string closetime =14 [json_name="closetime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"关闭时间"}];
    string ip =15 [json_name="ip",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户发帖IP地址"}];
    repeated Image images =16 [json_name="images",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片"}];
    string topicImage =17 [json_name="topicImage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"曝光第一张图片"}];
    int32 forwardCount =18 [json_name="forwardCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"转发数"}];
    int32 commentCount =19 [json_name="commentCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论数"}];
    int32 applaudCount =20 [json_name="applaudCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞数量"}];
    int32 viewCount =21 [json_name="viewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"查看数量"}];
    bool forwardCountVisible =22 [json_name="forwardCountVisible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"转发数是否可见"}];
    bool commentCountVisible =23 [json_name="commentCountVisible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论数是否可以就"}];
    bool applaudCountVisible =24 [json_name="applaudCountVisible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"点赞数量是否可见"}];
    bool viewCountVisible =25 [json_name="viewCountVisible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"查看数是否可见"}];
    bool hasImage =26 [json_name="hasImage",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有图片"}];
    bool isResolved =27 [json_name="isResolved",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否解决"}];
    bool isResolvedByUser =28 [json_name="isResolvedByUser",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否由用户点击已解决"}];
    bool isApplaud =29 [json_name="isApplaud",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否点赞"}];
    AuditStatus status =30 [json_name="status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"审批状态"}];
    bool IsCommentClosed =31 [json_name="IsCommentClosed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"评论是否关闭"}];
    string feedback =32 [json_name="feedback",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"反馈"}];
    string share =33 [json_name="share",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享页"}];
    bool hasNewAppend =34 [json_name="hasNewAppend",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否有最新回复"}];
    MediateStatus appendAuditStatus =35 [json_name="appendAuditStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问审核状态"}];
    int64 wikiTimestamp =36 [json_name="wikiTimestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布时间戳"}];
    int64 wikiTimestampms =37 [json_name="wikiTimestampms",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"发布时间戳（精确到毫秒"}];
    int32 topicViewCount =38 [json_name="topicViewCount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"帖子浏览数"}];
    bool isBoutique =39 [json_name="isBoutique",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否精品"}];
    string countDownText =40 [json_name="countDownText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"倒计时文本"}];
    string ExpireTime =41 [json_name="expireTime",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"过期时间"}];
    string amount =42 [json_name="amount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额"}];
    string objectLabel =43 [json_name="objectLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权对象标签"}];
    string amountLabel =44 [json_name="amountLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额标签"}];
    string categoryLabel =45 [json_name="categoryLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权问题标签"}];
    string requirementLabel =46 [json_name="requirementLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权要求标签"}];
    string requirement =47 [json_name="requirement",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权要求"}];
    string annoation =48 [json_name="annoation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
    string newColor =49 [json_name="newColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标状态颜色"}];
    string elapsed =50 [json_name="elapsed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间"}];
    string closeLabel =51 [json_name="closeLabel",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间标签"}];
    MediateStatus appendStatus =52 [json_name="appendStatus",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权状态"}];
    string stamp =53 [json_name="stamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"印章地址"}];
    string currency_symbol =54 [json_name="currency_symbol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币符号"}];
    string currency_code =55 [json_name="currency_code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币"}];
    AggInfo aggInfo =56 [json_name="aggInfo",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"聚合层约定模型 由下游接口填写需要补出数据的主键"}];
    string traderCode =57 [json_name="traderCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解对象"}];
    string userId =58 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
    string id =59 [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解id"}];
}

message GetMediateDetailRequest{
    string code =1 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权编码"}];
    string countryCode =2 [json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家编码"}];
    string uid =3 [json_name="uid",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户"}];
    Platform platform =4 [json_name="platform",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"wikifx传1，wikibit传2"}];
    int32 pageIndex =5 [json_name="pageIndex",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分页页码"}];
    int32 pageSize =6 [json_name="pageSize",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分页数"}];
    bool isApp =7 [json_name="isApp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否app调用"}];
}

message GetMediateDetailReply{
    string code =1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"编号"}];
    MediateStatus append_status =2 [json_name="append_status",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权状态"}];
    string country_name =3 [json_name="country_name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家名称"}];
    string flag =4 [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
    string feedback =5 [json_name="feedback",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"审核反馈"}];
    string originTitle =6 [json_name="originTitle",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"原标题"}];
    string title =7 [json_name="title",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"标题"}];
    string object_label =8 [json_name="object_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权对象标签"}];
    string category_label =9 [json_name="category_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权问题标签"}];
    string requirement_label =10 [json_name="requirement_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权要求标签"}];
    string amount_label =11 [json_name="amount_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额标签"}];
    string close_label =12 [json_name="close_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间标签"}];
    string account_label =13 [json_name="account_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易账号标签"}];
    string name_label =14 [json_name="name_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户姓名标签"}];
    string phone_label =15 [json_name="phone_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户手机号标签"}];
    string email_label =16 [json_name="email_label",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户邮箱标签"}];
    string category =17 [json_name="category",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权问题"}];
    string requirement =18 [json_name="requirement",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权要求"}];
    string amount =19 [json_name="amount",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权金额"}];
    string symbol =20 [json_name="symbol",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"货币符号"}];
    string threecode =21 [json_name="threecode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家三位code"}];
    string account =22 [json_name="account",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易账号"}];
    string name =23 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户姓名"}];
    string phone =24 [json_name="phone",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户手机号"}];
    string email =25 [json_name="email",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"开户邮箱"}];
    string stamp =26 [json_name="stamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"印章地址"}];
    string share =27 [json_name="share",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分享页"}];
    repeated MediateReply appends =28 [json_name="appends",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"回复"}];
    int32 total =29 [json_name="total",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"回复总数"}];
    MediateReply banner =30 [json_name="banner",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"已解决和放弃解决banner"}];
    string statement =31 [json_name="statement",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"申明"}];
    string time =32 [json_name="time",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"创建时间"}];
    string wiki_timestamp =33 [json_name="wiki_timestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"创建时间戳"}];
    string is_append_visible =34 [json_name="is_append_visible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"回复是否可见"}];
    string is_extrainfo_visible =35 [json_name="is_extrainfo_visible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"补充资料是否可见"}];
    string elapsed =36 [json_name="elapsed",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"维权时间"}];
    string annoation =37 [json_name="annoation",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标"}];
    string color =38 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"角标状态颜色"}];
    string newColor =39 [json_name="newColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"新角标状态颜色"}];
    string countDownText =40 [json_name="countDownText",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"倒计时文本"}];
    string traderCode =41 [json_name="traderCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解对象"}];
    string userId =42 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
    string id =43 [json_name="id",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"调解id"}];
}

message Category{
    string cid =1 [json_name="cid",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类编号"}];
    string name =2 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类名称"}];
    string icon =3 [json_name="icon",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类图标"}];
    string iconwikibit =4 [json_name="iconwikibit",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"bit图标"}];
    string color =5 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"分类颜色"}];
    string newColor =6 [json_name="newColor",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"新颜色"}];
}

message Image{
    string abbr =1 [json_name="abbr",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"缩略图"}];
    string detail =2 [json_name="detail",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"详细"}];
    string width =3 [json_name="width",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片宽度"}];
    string height =4 [json_name="height",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"图片高度"}];
}

message AggInfo{
    string code =1 [json_name="code",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"交易商、服务商、受评方Code"}];
    string userId =2 [json_name="userId",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"用户id"}];
    string countryCode =3 [json_name="countryCode",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家3位code 用于补全国家名称和国旗"}];
}

message MediateReply{
    Owner type =1 [json_name="type",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问回复所属,0-用户追问，1-wikifx官方回复,2-关联交易商"}];
    string name =2 [json_name="name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问用户名称"}];
    string avatar =3 [json_name="avatar",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问头像"}];
    string color =4 [json_name="color",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问标签颜色"}];
    string tip =5 [json_name="tip",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问标签内容"}];
    string origin =6 [json_name="origin",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问内容"}];
    string translate =7 [json_name="translate",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问翻译内容"}];
    repeated Image images =8 [json_name="images",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问图片"}];
    int64 createtimestamp =9 [json_name="createtimestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问时间(时间戳)"}];
    int64 wiki_timestamp =10 [json_name="wiki_timestamp",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"追问时间(时间戳)"}];
    string flag =11 [json_name="flag",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国旗"}];
    string country_name =12 [json_name="country_name",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"国家名称"}];
    bool is_visible =13 [json_name="is_visible",(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field)={title:"是否显示"}];
}

enum AuditStatus{
    Topic_All=0;//全部
    Topic_Pending=100;//未审核
    Topic_Success=200;//审核通过
    Topic_Fail=401;//审核未通过
    Topic_Private=407;//隐藏
    Topic_Hidden=408;//后台隐藏
}

enum MediateStatus{
    Mediate_All=0;//全部
    Mediate_Pending=100;//待审核
    Mediate_Handling=110;//处理中
    Mediate_Reply=112;//处理中
    Mediate_Finish=200;//已解决
    Mediate_Abandon=300;//已放弃
    Mediate_Fail=401;//未通过
}

enum Owner{
    Reply_User=0;//用户追问
    Reply_Wikifx=1;//官方回复
    Reply_Trader=2;//交易商回复
}

enum Platform{
    P_Fxeye=0;//国内版
    P_WikiFx=1;//国际版
    P_Wikibit=2;//wikibit
}