syntax = "proto3";

package api.user_profile.v1;

import "google/api/annotations.proto";
import "common/common.proto";
import "user_profile/v1/models.proto";
import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "api/user_profile/v1;v1";

service Service{
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "健康检查",tags: ["安全"]};
  }
  // 推送ID上报
  rpc PushIdReport(PushIdReportRequest) returns (PushIdReportReply) {
    option (google.api.http) = {post: "/v1/push_report" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "上报用户推送ID",tags: ["用户"]};
  }
  // 获取筛选标签
  rpc FilterLabels(FilterLabelsRequest) returns (FilterLabelsReply) {
    option (google.api.http) = {get: "/v1/push_labels"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取预定义标签列表",tags: ["预定义标签"]};
  }
  // 按照标签筛选
  rpc CustomFilter(CustomFilterRequest) returns (CustomFilterReply) {
    option (google.api.http) = {post: "/v1/push_filter" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据预定义标签筛选",tags: ["预定义标签"]};
  }
  // =============== 用户画像 =========================
  // 用户画像支持的字段列表
  rpc UserProfileField(UserProfileFieldRequest) returns (UserProfileFieldReply) {
    option (google.api.http) = {get: "/v1/profile/fields"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "用户画像支持的字段",tags: ["用户画像"]};
  }
  // 根据用户ID查看用户画像
  rpc GetByUserId(GetByUserIdRequest) returns (UserProfile) {
    option (google.api.http) = {get: "/v1/user/{user_id}/profile"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据用户ID获取用户画像",tags: ["用户画像"]};
  }
  // 根据用户ID或者设备ID查看用户画像
  rpc GetUserProfile(GetUserprofileRequest) returns (UserProfile) {
    option (google.api.http) = {get: "/v1/profile"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据用户ID或者设备ID获取用户画像",tags: ["用户画像"]};
  }
  // =============== 自定义标签 =========================
  // 新增自定义标签
  rpc AddLabel(Label) returns (AddLabelReply) {
    option (google.api.http) = {post: "/v1/label" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加自定义标签",tags: ["自定义标签"]};
  }
  // 获取自定义标签详情
  rpc GetLabel(GetLabelRequest) returns (Label) {
    option (google.api.http) = {get: "/v1/label/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取自定义标签详情",tags: ["自定义标签"]};
  }
  // 更新自定义标签
  rpc UpdateLabel(Label) returns (UpdateLabelReply) {
    option (google.api.http) = {put: "/v1/label/{id}" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "更新自定义标签",tags: ["自定义标签"]};
  }
  // 更新自定义标签状态
  rpc UpdateLabelStatus(UpdateLabelStatusRequest) returns (UpdateLabelStatusReply) {
    option (google.api.http) = {put: "/v1/label/{id}/status" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "更新自定义标签状态",tags: ["自定义标签"]};
  }
  // 删除自定义标签
  rpc DeleteLabel(DeleteLabelRequest) returns (DeleteLabelReply) {
    option (google.api.http) = {delete: "/v1/label/{id}"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除自定义标签",tags: ["自定义标签"]};
  }
  // 获取自定义标签列表
  rpc ListLabel(ListLabelRequest) returns (ListLabelReply) {
    option (google.api.http) = {get: "/v1/label"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取自定义标签列表",tags: ["自定义标签"]};
  }
  // 根据标签获取用户数量
  rpc CountByLabel(CountByLabelRequest) returns (CountByLabelReply) {
    option (google.api.http) = {get: "/v1/label/{id}/count"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据预定义标签获取数量",tags: ["自定义标签"]};
  }
  // 根据标签筛选用户
  rpc FilterByLabel(FilterByLabelRequest) returns (FilterByLabelReply) {
    option (google.api.http) = {get: "/v1/label/{id}/filter"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "根据预定义标签获取数量",tags: ["自定义标签"]};
  }
  // 编辑人列表
  rpc EditorList(EditorListRequest) returns (EditorListReply) {
    option (google.api.http) = {get: "/v1/label/editor/list"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取编辑人列表",tags: ["自定义标签"]};
  }
  // 标签国家
  rpc LabelCountry(LabelCountryRequest) returns (LabelCountryReply) {
    option (google.api.http) = {get: "/v1/label/{id}/countries"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取标签语言列表",tags: ["自定义标签"]};
  }
  // 标签语言
  rpc LabelLanguage(LabelLanguageRequest) returns (LabelLanguageReply) {
    option (google.api.http) = {get: "/v1/label/{id}/languages"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取标签语言列表",tags: ["自定义标签"]};
  }
  // =============== 召回任务 =========================
  // 新增召回
  rpc AddRecall(Recall) returns (AddRecallReply) {
    option (google.api.http) = {post: "/v1/recall" body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "添加召回任务",tags: ["召回"]};
  }
  // 获取召回列表
  rpc GetRecalls(GetRecallsRequest) returns (GetRecallsReply) {
    option (google.api.http) = {get: "/v1/recalls"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取召回列表",tags: ["召回"]};
  }
  //  // 获取召回详情
  //  rpc GetRecall(GetRecallRequest) returns (Recall) {
  //    option (google.api.http) = {get: "/v1/recall"};
  //    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取召回详情",tags: ["召回"]};
  //  }
  // 获取召回任务执行记录
  rpc GetRecallExecRecords(GetRecallExecRecordsRequest) returns (GetRecallExecRecordsReply) {
    option (google.api.http) = {get: "/v1/recall/exec_records"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取召回任务执行记录",tags: ["召回"]};
  }
  // 删除召回任务
  rpc DeleteRecall(DeleteRecallRequest) returns (DeleteRecallReply) {
    option (google.api.http) = {delete: "/v1/recall"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "删除召回任务",tags: ["召回"]};
  }

  // 设置召回任务状态
  rpc SetRecall(SetRecallRequest) returns (SetRecallReply) {
    option (google.api.http) = {post: "/v1/recall/set_status", body:"*"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "设置召回任务状态",tags: ["召回"]};
  }
  // 召回任务触达率
  rpc GetRecallTouch(GetRecallTouchRequest) returns (GetRecallTouchReply) {
    option (google.api.http) = {get: "/v1/recall/touch"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "召回任务触达率",tags: ["召回"]};
  }
  // 召回任务覆盖用户列表
  rpc GetRecallCoverUsers(GetRecallCoverUsersRequest) returns (GetRecallCoverUsersReply) {
    option (google.api.http) = {get: "/v1/recall/cover_users"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "召回任务覆盖用户列表",tags: ["召回"]};
  }
}