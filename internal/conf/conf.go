package conf

import (
	"api-expo/api/common"
	"api-expo/pkg/emailsender"
	"api-expo/pkg/immessagecenter"
	"api-expo/pkg/rabbit"
	"api-expo/pkg/tencentAi"
	"api-expo/pkg/upstream"
)

type Bootstrap struct {
	Server   *common.ServerConfig `json:"server"`
	Data     *common.DataConfig   `json:"data"`
	Business *Business            `json:"business"`
}

type Business struct {
	NotifyCheckSpec    string                  `json:"notify_check_spec"`
	OssDomain          string                  `json:"oss_domain"`
	SurveyDomain       string                  `json:"survey_domain"`
	ExpoRegisterLink   string                  `json:"expo_register_link"`
	UserCenterEndpoint string                  `json:"user_center_endpoint"`
	CommunityEndpoint  string                  `json:"community_endpoint"`
	ImMessageCenter    *immessagecenter.Config `json:"im_message_endpoint"`
	TencentAI          *tencentAi.Config       `json:"tencent_ai"`
	Upstream           *upstream.Config        `json:"upstream"`
	NotificationRabbit *rabbit.Config          `json:"notification_rabbit"`
	EmailSender        *emailsender.Config     `json:"email_sender"`
	MessageEmailTask   *MessageEmailTaskConfig `json:"message_email_task"`
}

type MessageEmailTaskConfig struct {
	Enabled            bool   `json:"enabled"`              // 是否启用
	LockKey            string `json:"lock_key"`             // 分布式锁key
	LockTimeoutMinutes int    `json:"lock_timeout_minutes"` // 锁超时时间(分钟)
	MessageDaysRange   int    `json:"message_days_range"`   // 消息查询天数范围
}
