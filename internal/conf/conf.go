package conf

import (
	"github.com/airunny/wiki-go-tools/alarm"
	common "gold_store/api/common"
	"gold_store/pkg/ikafka"
	"gold_store/pkg/rabbit"
	"gold_store/pkg/vgoods"
)

type Data struct {
	*common.DataConfig
	Static     *common.DataConfig_Database `json:"static"`     // 静态数据
	VPS        *common.DataConfig_Database `json:"vps"`        // VPS
	EA         *common.DataConfig_Database `json:"ea"`         // EA
	VIP        *common.DataConfig_Database `json:"vip"`        // vip
	Report     *common.DataConfig_Database `json:"report"`     // 报告
	Exhibition *common.DataConfig_Database `json:"exhibition"` // 展会
}

// EMSConfig EMS配置
type EMSConfig struct {
	Environment   string `json:"environment"`   // production | sandbox
	SenderNo      string `json:"sender_no"`     // 客户代码
	Authorization string `json:"authorization"` // 授权码
	SecretKey     string `json:"secret_key"`    // SM4加密密钥
	UserCode      string `json:"user_code"`     // 用户编码
	Timeout       int    `json:"timeout"`       // 超时时间
}

type Bootstrap struct {
	Server   *common.ServerConfig `json:"server"`
	Data     *Data                `json:"data"`
	Business *Business            `json:"business"`
}

type Business struct {
	FeiShuAlarm        *alarm.Config          `json:"feishu_alarm"`
	OssDomain          string                 `json:"oss_domain"`
	ExhibitionDomain   string                 `json:"exhibition_domain"`
	BrokerDomain       string                 `json:"broker_domain"`
	NotificationRabbit *rabbit.Config         `json:"notification_rabbit"`
	TaskConsumer       *ikafka.ConsumerConfig `json:"task_consumer"`
	TaskProducer       *ikafka.ProducerConfig `json:"task_producer"`
	TaskConsumerGroup  *TaskConsumerGroup     `json:"task_consumer_group"`
	EMS                *EMSConfig             `json:"ems"`
	GoldDomain         string                 `json:"gold_domain"`
	VirtualGoods       *vgoods.Config         `json:"virtual_goods"`
	UserCenterEndpoint string                 `json:"user_center_endpoint"`
	Express            *ExpressConfig         `json:"express"`
	OrderMerge         struct {
		EAConsumer         *ikafka.ConsumerConfig `json:"ea_consumer"`
		VPSConsumer        *ikafka.ConsumerConfig `json:"vps_consumer"`
		VIPConsumer        *ikafka.ConsumerConfig `json:"vip_consumer"`
		ReportConsumer     *ikafka.ConsumerConfig `json:"report_consumer"`
		ExhibitionConsumer *ikafka.ConsumerConfig `json:"exhibition_consumer"`
	} `json:"order_merge"`
}

// TaskConsumerGroup 任务消费组配置
type TaskConsumerGroup struct {
	PostMoment      string `json:"post_moment"`
	PostBusiness    string `json:"post_business"`
	CommentPost     string `json:"comment_post"`
	RateDealer      string `json:"rate_dealer"`
	JoinActivity    string `json:"join_activity"`
	ModifyUsername  string `json:"modify_username"`
	ModifyAvatar    string `json:"modify_avatar"`
	VerifyIdentity  string `json:"verify_identity"`
	BindRealAccount string `json:"bind_real_account"`
	OpenVPS         string `json:"open_vps"`
	FollowWikiFX    string `json:"follow_wikifx"`
	ViewDealer      string `json:"view_dealer"`
	Search          string `json:"search"`
}

// ExpressConfig 快递100配置
type ExpressConfig struct {
	Key         string `json:"key"`      // 快递100授权key
	Customer    string `json:"customer"` // 快递100客户编号
	Secret      string `json:"secret"`   // 快递100签名密钥
	BaseURL     string `json:"base_url"` // 快递100 API基础URL
	CallbackUrl string `json:"callback_url"`
}
