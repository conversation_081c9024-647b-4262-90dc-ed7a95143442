package util

import (
	"fmt"
	"strings"
)

// FullName 拼接生成名字
func FullName(lastName, firstName string) string {
	lastName = strings.TrimSpace(lastName)
	firstName = strings.TrimSpace(firstName)

	if lastName == "" && firstName == "" {
		return ""
	}
	if lastName == "" {
		return firstName
	}
	if firstName == "" {
		return lastName
	}
	return lastName + " " + firstName
}

// ValidatePagination 验证分页参数
func ValidatePagination(page, size, DefaultPageSize int32) (int32, int32) {
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = DefaultPageSize
	}
	return page, size
}

func Format(template string, args ...interface{}) string {
	for i, arg := range args {
		placeholder := fmt.Sprintf("{%d}", i)
		template = strings.ReplaceAll(template, placeholder, fmt.Sprintf("%v", arg))
	}
	return template
}
