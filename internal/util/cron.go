package util

import (
	"context"
	"log"

	"github.com/robfig/cron/v3"
)

// MessageEmailSender 发送私聊邮件的接口 (方便测试)
type MessageEmailSender interface {
	SendMessageEmails(ctx context.Context) error
}

// EmailCron 私信邮件任务
type EmailCron struct {
	sender MessageEmailSender
	cron   *cron.Cron
}

// NewEmailCron 创建私信邮件任务
func NewEmailCron(sender MessageEmailSender) *EmailCron {
	c := cron.New(cron.WithLogger(cron.VerbosePrintfLogger(log.New(log.Writer(), "CRON: ", log.LstdFlags))))

	return &EmailCron{
		sender: sender,
		cron:   c,
	}
}

// Start 启动定时任务 (每周一上午10点执行)
func (c *EmailCron) Start() {
	// 添加定时任务：每周一上午10点执行
	// 格式：分 时 日 月 周 (5字段格式)
	// 0 10 * * 1 = 每周一10点0分
	_, err := c.cron.AddFunc("0 10 * * 1", func() {
		c.execute()
	})

	if err != nil {
		log.Printf("添加定时任务失败: %v", err)
		return
	}

	// 启动cron调度器
	c.cron.Start()
	log.Println("私信邮件任务已启动 (每周一上午10点执行)")
}

// Stop 停止定时任务
func (c *EmailCron) Stop() {
	if c.cron != nil {
		c.cron.Stop()
		log.Println("私信邮件任务已停止")
	}
}

// execute 执行发送任务
func (c *EmailCron) execute() {
	log.Println("开始执行私聊邮件发送任务")
	ctx := context.Background()

	err := c.sender.SendMessageEmails(ctx)
	if err != nil {
		log.Printf("私聊邮件发送失败: %v", err)
	} else {
		log.Println("私聊邮件发送完成")
	}
}

// TriggerNow 立即触发执行 (用于测试)
func (c *EmailCron) TriggerNow() {
	log.Println("手动触发私聊邮件发送任务")
	c.execute()
}

// GetNextSchedule 获取下次执行时间 (调试用)
func (c *EmailCron) GetNextSchedule() string {
	entries := c.cron.Entries()
	if len(entries) > 0 {
		return entries[0].Next.Format("2006-01-02 15:04:05")
	}
	return "未设置"
}
