package util

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"math"
	mr "math/rand"
	"strconv"
	"strings"
)

func GenerateCode(expoId int) (out string) {
	out = fmt.Sprintf("%04d%s",
		expoId%10000, Generate(6, false, false))
	return
}
func Generate(len int, includeAlpha bool, ignoreCase bool) string {
	num := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	var max float64 = 10
	if includeAlpha {
		if ignoreCase {
			max = 36
		} else {
			max = 62
		}
	}
	var seed int64
	binary.Read(rand.Reader, binary.BigEndian, &seed)
	var result strings.Builder
	r := mr.New(mr.<PERSON>ource(seed))
	for i := 1; i <= len; i++ {
		idx := int(math.Floor(max * float64(r.Float64())))
		result.WriteString(string(num[idx]))
	}
	return result.String()
}
func GetTimeZone(src string) (out int) {
	// 移除最开始的utc
	temp, _ := strings.CutPrefix(strings.ToLower(src), "utc")
	// 移除加号
	temp, ok := strings.CutPrefix(temp, "+")
	if ok {
		out, _ = strconv.Atoi(temp)
	} else {
		// utc-08
		out, _ = strconv.Atoi(temp)
	}
	return
}
