<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WikiEXPO 粉丝提问邮件</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f4f4f4;
    }

    .container {
      width: 600px;
      margin: 20px auto;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }

    .header {
      background-image: url('{{.BackgroundImageURL}}');
      background-size: cover;
      background-position: center;
      background-color: #007cba;
      height: 150px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      padding: 0 20px;
    }

    .header h1 {
      color: #fff;
      font-size: 24px;
      margin: 0 0 10px 0;
    }

    .header .expo-title {
      color: #fff;
      margin: 0;
      font-size: 18px;
      font-weight: bold;
    }

    .header .expo-info {
      color: #fff;
      margin: 5px 0 0 0;
      font-size: 16px;
    }

    .content {
      padding: 20px;
    }

    .content h2 {
      color: #333;
      margin: 0 0 20px 0;
    }

    .content p {
      margin: 10px 0;
      line-height: 1.6;
      color: #666;
    }

    .avatar-list {
      display: flex;
      gap: 10px;
      margin: 20px 0;
      flex-wrap: wrap;
    }

    .avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid #ddd;
    }

    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .btn {
      display: inline-block;
      margin-top: 20px;
      padding: 12px 24px;
      background-color: #f7ca33;
      color: #000;
      text-decoration: none;
      border-radius: 5px;
      font-weight: bold;
    }

    .btn:hover {
      background-color: #e6b82d;
    }

    .footer {
      padding: 20px;
      background-color: #f9f9f9;
      border-top: 1px solid #ddd;
      color: #666;
      font-size: 12px;
      text-align: center;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>WikiEXPO</h1>
      <p class="expo-title">{{.ExpoTitle}}</p>
      <p class="expo-info">{{.ExpoTime}} {{.ExpoLocation}}</p>
    </div>
    <div class="content">
      <h2>{{.FanCount}}位粉丝正在期盼您的回复</h2>
      <p>Hello {{.UserName}}</p>
      <p>感谢您在百忙之中成为WikiEXPO {{.ExpoTitle}}的演讲嘉宾，您的粉丝有一些问题想得到您的解答。</p>
      <div class="avatar-list">
        {{range .FanAvatars}}
        <div class="avatar"><img src="{{.}}" alt="粉丝头像"></div>
        {{end}}
      </div>
      <a href="{{.ViewMessagesURL}}" class="btn">查看消息</a>
    </div>
    <div class="footer">
      <p>此邮件由 WikiFX 系统自动发送，请勿回复。</p>
    </div>
  </div>
</body>

</html>
