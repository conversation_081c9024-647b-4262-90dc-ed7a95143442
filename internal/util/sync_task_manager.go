package util

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	v1 "api-expo/api/expo/v1"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

const (
	redisTaskKeyPrefix = "sync_task_result:"
	redisExpoMapPrefix = "expo_task_result_map:"
	redisTaskTTL       = 365 * 24 * time.Hour
)

// SkippableError 表示应该跳过的错误
type SkippableError struct {
	Reason string
}

func (e *SkippableError) Error() string {
	return e.Reason
}

// NewSkippableError 创建跳过错误
func NewSkippableError(reason string) *SkippableError {
	return &SkippableError{Reason: reason}
}

// SyncTaskItem 同步任务项
type SyncTaskItem struct {
	ImageURL     string             `json:"image_url"`
	Action       v1.SyncAction      `json:"action"`
	Status       v1.ImageSyncStatus `json:"status"`
	ErrorMessage string             `json:"error_message"`
	FaceCount    int32              `json:"face_count"`
	ProcessedAt  *time.Time         `json:"processed_at"`
	RetryCount   int32              `json:"retry_count"`
}

// SyncTask 同步任务
type SyncTask struct {
	// 基本信息
	TaskID  string        `json:"task_id"`
	ExpoID  int64         `json:"expo_id"`
	GroupID string        `json:"group_id"`
	Status  v1.SyncStatus `json:"status"`
	Message string        `json:"message"`

	// 时间信息
	StartedAt   time.Time  `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`

	// 进度统计
	TotalImages     int32 `json:"total_images"`
	ProcessedImages int32 `json:"processed_images"`
	SuccessCount    int32 `json:"success_count"`
	FailedCount     int32 `json:"failed_count"`
	SkippedCount    int32 `json:"skipped_count"`
	DeletedCount    int32 `json:"deleted_count"`
	NoFaceCount     int32 `json:"no_face_count"`

	// 运行时状态
	CurrentImage string                   `json:"current_image"`
	Items        map[string]*SyncTaskItem `json:"items"`

	mu sync.RWMutex `json:"-"`
}

// SyncTaskManager 同步任务管理器
type SyncTaskManager struct {
	tasks       map[string]*SyncTask
	expoMap     map[int64]string
	mu          sync.RWMutex
	redisClient *redis.Client
}

// 全局单例
var (
	globalManager *SyncTaskManager
	once          sync.Once
	globalMu      sync.RWMutex
)

// NewSyncTaskManager 创建任务管理器
func NewSyncTaskManager(redisClient ...*redis.Client) *SyncTaskManager {
	manager := &SyncTaskManager{
		tasks:   make(map[string]*SyncTask),
		expoMap: make(map[int64]string),
	}
	if len(redisClient) > 0 && redisClient[0] != nil {
		manager.redisClient = redisClient[0]
	}
	return manager
}

// CreateTask 创建新任务
func (m *SyncTaskManager) CreateTask(expoID int64, groupID string) (*SyncTask, error) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 检查是否已有运行中的任务
	if existingTaskID, exists := m.expoMap[expoID]; exists {
		if existingTask, ok := m.tasks[existingTaskID]; ok {
			if existingTask.Status == v1.SyncStatus_SYNC_STATUS_RUNNING {
				return nil, fmt.Errorf("展会 %d 已有正在运行的同步任务: %s", expoID, existingTaskID)
			}
		}
	}

	task := &SyncTask{
		TaskID:    uuid.New().String(),
		ExpoID:    expoID,
		GroupID:   groupID,
		Status:    v1.SyncStatus_SYNC_STATUS_NOT_STARTED,
		StartedAt: time.Now(),
		Message:   "任务已创建，等待开始执行",
		Items:     make(map[string]*SyncTaskItem),
	}

	m.tasks[task.TaskID] = task
	m.expoMap[expoID] = task.TaskID
	return task, nil
}

// GetTask 获取任务
func (m *SyncTaskManager) GetTask(taskID string) (*SyncTask, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	task, exists := m.tasks[taskID]
	return task, exists
}

// GetTaskByExpoID 根据展会ID获取任务
func (m *SyncTaskManager) GetTaskByExpoID(expoID int64) (*SyncTask, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 先查内存
	if taskID, exists := m.expoMap[expoID]; exists {
		if task, ok := m.tasks[taskID]; ok {
			return task, true
		}
	}

	// 再查Redis
	if m.redisClient != nil {
		if task := m.getTaskFromRedis(context.Background(), expoID); task != nil {
			return task, true
		}
	}

	return nil, false
}

// UpdateTaskStatus 更新任务状态
func (m *SyncTaskManager) UpdateTaskStatus(taskID string, status v1.SyncStatus, message string) error {
	task, exists := m.GetTask(taskID)
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	task.Status = status
	if message != "" {
		task.Message = message
	}

	// 任务完成时保存到Redis
	if isTaskCompleted(status) {
		now := time.Now()
		task.CompletedAt = &now
		if m.redisClient != nil {
			go m.saveTaskToRedis(context.Background(), task)
		}
	}

	return nil
}

// isTaskCompleted 判断任务是否已完成
func isTaskCompleted(status v1.SyncStatus) bool {
	return status == v1.SyncStatus_SYNC_STATUS_COMPLETED ||
		status == v1.SyncStatus_SYNC_STATUS_FAILED ||
		status == v1.SyncStatus_SYNC_STATUS_CANCELLED
}

// AddTaskItem 添加任务项
func (m *SyncTaskManager) AddTaskItem(taskID string, item *SyncTaskItem) error {
	task, exists := m.GetTask(taskID)
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	task.Items[item.ImageURL] = item
	task.TotalImages = int32(len(task.Items))
	return nil
}

// UpdateTaskItem 更新任务项状态
func (m *SyncTaskManager) UpdateTaskItem(taskID, imageURL string, status v1.ImageSyncStatus, errorMessage string, faceCount int32) error {
	task, exists := m.GetTask(taskID)
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	task.mu.Lock()
	defer task.mu.Unlock()

	item, itemExists := task.Items[imageURL]
	if !itemExists {
		return fmt.Errorf("任务项不存在: %s", imageURL)
	}

	// 更新项状态
	oldStatus := item.Status
	item.Status = status
	item.ErrorMessage = errorMessage
	item.FaceCount = faceCount
	now := time.Now()
	item.ProcessedAt = &now

	// 更新处理计数
	if shouldIncreaseProcessed(oldStatus) {
		task.ProcessedImages++
	}

	// 重新计算统计
	task.recalculateStats()
	return nil
}

// shouldIncreaseProcessed 判断是否应该增加处理计数
func shouldIncreaseProcessed(oldStatus v1.ImageSyncStatus) bool {
	return oldStatus == v1.ImageSyncStatus_IMAGE_SYNC_STATUS_PENDING ||
		oldStatus == v1.ImageSyncStatus_IMAGE_SYNC_STATUS_PROCESSING
}

// recalculateStats 重新计算统计数据
func (task *SyncTask) recalculateStats() {
	task.SuccessCount = 0
	task.FailedCount = 0
	task.SkippedCount = 0
	task.DeletedCount = 0
	task.NoFaceCount = 0

	for _, item := range task.Items {
		switch item.Status {
		case v1.ImageSyncStatus_IMAGE_SYNC_STATUS_SUCCESS:
			task.SuccessCount++
		case v1.ImageSyncStatus_IMAGE_SYNC_STATUS_FAILED:
			task.FailedCount++
		case v1.ImageSyncStatus_IMAGE_SYNC_STATUS_SKIPPED:
			task.SkippedCount++
		case v1.ImageSyncStatus_IMAGE_SYNC_STATUS_DELETED:
			task.DeletedCount++
		case v1.ImageSyncStatus_IMAGE_SYNC_STATUS_NO_FACE:
			task.NoFaceCount++
		}
	}
}

// SetCurrentImage 设置当前处理的图片
func (m *SyncTaskManager) SetCurrentImage(taskID, imageURL string) error {
	task, exists := m.GetTask(taskID)
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	task.mu.Lock()
	task.CurrentImage = imageURL
	task.mu.Unlock()
	return nil
}

// SyncTask 方法

// GetProgress 获取任务进度百分比
func (t *SyncTask) GetProgress() float32 {
	t.mu.RLock()
	defer t.mu.RUnlock()
	if t.TotalImages == 0 {
		return 0
	}
	return float32(t.ProcessedImages) / float32(t.TotalImages) * 100
}

// GetDurationMs 获取任务执行时长（毫秒）
func (t *SyncTask) GetDurationMs() int64 {
	t.mu.RLock()
	defer t.mu.RUnlock()
	endTime := time.Now()
	if t.CompletedAt != nil {
		endTime = *t.CompletedAt
	}
	return endTime.Sub(t.StartedAt).Milliseconds()
}

// GetTaskItems 获取任务项列表
func (t *SyncTask) GetTaskItems() []*SyncTaskItem {
	t.mu.RLock()
	defer t.mu.RUnlock()
	items := make([]*SyncTaskItem, 0, len(t.Items))
	for _, item := range t.Items {
		items = append(items, item)
	}
	return items
}

// GetItemCount 获取任务项数量
func (t *SyncTask) GetItemCount() int {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return len(t.Items)
}

// GetPendingItems 获取待处理的任务项
func (t *SyncTask) GetPendingItems() map[string]*SyncTaskItem {
	t.mu.RLock()
	defer t.mu.RUnlock()
	pendingItems := make(map[string]*SyncTaskItem)
	for imageURL, item := range t.Items {
		if item.Status == v1.ImageSyncStatus_IMAGE_SYNC_STATUS_PENDING {
			itemCopy := *item
			pendingItems[imageURL] = &itemCopy
		}
	}
	return pendingItems
}

// GetFinalCounts 获取最终统计数据
func (t *SyncTask) GetFinalCounts() (successCount, failedCount, skippedCount, deletedCount, noFaceCount int32) {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.SuccessCount, t.FailedCount, t.SkippedCount, t.DeletedCount, t.NoFaceCount
}

// 全局单例管理

// GetGlobalSyncTaskManager 获取全局任务管理器实例
func GetGlobalSyncTaskManager() *SyncTaskManager {
	globalMu.RLock()
	if globalManager != nil {
		defer globalMu.RUnlock()
		return globalManager
	}
	globalMu.RUnlock()

	globalMu.Lock()
	defer globalMu.Unlock()
	if globalManager == nil {
		globalManager = NewSyncTaskManager()
	}
	return globalManager
}

// SetGlobalSyncTaskManagerWithRedis 设置带Redis的全局任务管理器
func SetGlobalSyncTaskManagerWithRedis(redisClient *redis.Client) {
	globalMu.Lock()
	defer globalMu.Unlock()
	globalManager = NewSyncTaskManager(redisClient)
}

// Redis 持久化方法

// saveTaskToRedis 保存任务结果到Redis
func (m *SyncTaskManager) saveTaskToRedis(ctx context.Context, task *SyncTask) {
	if m.redisClient == nil {
		return
	}

	// 创建任务副本避免并发问题
	taskCopy := m.copyTask(task)

	// 序列化并保存
	taskData, err := json.Marshal(taskCopy)
	if err != nil {
		fmt.Printf("序列化任务数据失败: %v\n", err)
		return
	}

	pipe := m.redisClient.Pipeline()
	pipe.Set(ctx, m.getRedisTaskKey(task.TaskID), taskData, redisTaskTTL)
	pipe.Set(ctx, m.getRedisExpoMapKey(task.ExpoID), task.TaskID, redisTaskTTL)

	if _, err := pipe.Exec(ctx); err != nil {
		fmt.Printf("保存任务到Redis失败: %v\n", err)
	}
}

// copyTask 创建任务副本
func (m *SyncTaskManager) copyTask(task *SyncTask) *SyncTask {
	taskCopy := &SyncTask{
		TaskID:          task.TaskID,
		ExpoID:          task.ExpoID,
		GroupID:         task.GroupID,
		Status:          task.Status,
		TotalImages:     task.TotalImages,
		ProcessedImages: task.ProcessedImages,
		SuccessCount:    task.SuccessCount,
		FailedCount:     task.FailedCount,
		SkippedCount:    task.SkippedCount,
		DeletedCount:    task.DeletedCount,
		NoFaceCount:     task.NoFaceCount,
		StartedAt:       task.StartedAt,
		CompletedAt:     task.CompletedAt,
		Message:         task.Message,
		CurrentImage:    task.CurrentImage,
		Items:           make(map[string]*SyncTaskItem),
	}

	// 复制任务项
	for k, v := range task.Items {
		taskCopy.Items[k] = &SyncTaskItem{
			ImageURL:     v.ImageURL,
			Action:       v.Action,
			Status:       v.Status,
			ErrorMessage: v.ErrorMessage,
			FaceCount:    v.FaceCount,
			ProcessedAt:  v.ProcessedAt,
			RetryCount:   v.RetryCount,
		}
	}

	return taskCopy
}

// getTaskFromRedis 从Redis获取任务结果
func (m *SyncTaskManager) getTaskFromRedis(ctx context.Context, expoID int64) *SyncTask {
	if m.redisClient == nil {
		return nil
	}

	// 获取任务ID
	taskID, err := m.redisClient.Get(ctx, m.getRedisExpoMapKey(expoID)).Result()
	if err != nil {
		return nil
	}

	// 获取任务数据
	taskData, err := m.redisClient.Get(ctx, m.getRedisTaskKey(taskID)).Result()
	if err != nil {
		return nil
	}

	// 反序列化
	var task SyncTask
	if err := json.Unmarshal([]byte(taskData), &task); err != nil {
		return nil
	}

	// 初始化
	task.mu = sync.RWMutex{}
	if task.Items == nil {
		task.Items = make(map[string]*SyncTaskItem)
	}

	return &task
}

// getRedisTaskKey 获取任务的Redis key
func (m *SyncTaskManager) getRedisTaskKey(taskID string) string {
	return redisTaskKeyPrefix + taskID
}

// getRedisExpoMapKey 获取展会映射的Redis key
func (m *SyncTaskManager) getRedisExpoMapKey(expoID int64) string {
	return redisExpoMapPrefix + strconv.FormatInt(expoID, 10)
}
