package util

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// GetCurrentTime 只支持 "2006-01-02 15:04:05" 格式
func GetCurrentTime(timeStr, timezone string) time.Time {
	if timezone == "" {
		timezone = "UTC"
	}
	loc, err := ParseTimezone(timezone)
	if err != nil {
		// 如果时区解析失败，返回当前时间的UTC时间
		loc, _ = time.LoadLocation("UTC")
	}
	if timeStr == "" {
		return time.Now().In(loc)
	}
	// 只解析精确到秒的格式
	t, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, loc)
	if err == nil {
		return t
	}
	// 解析失败返回当前时间
	return time.Now().In(loc)
}

func GetCurrentTimeWithEnv() time.Time {
	timeStr := os.Getenv("CURRENT_TIME")
	timezone := os.Getenv("TIMEZONE")
	return GetCurrentTime(timeStr, timezone)
}

func GetCurrentTimeWithEnvByTimezone(timezone string) time.Time {
	timeStr := os.Getenv("CURRENT_TIME")
	return GetCurrentTime(timeStr, timezone)
}

func ParseTimezone(timezone string) (*time.Location, error) {
	if timezone == "" {
		return time.UTC, nil
	}

	// 处理常见格式如 "UTC+8", "UTC-5", "UTC+08"
	if len(timezone) >= 4 && timezone[:3] == "UTC" {
		offsetStr := timezone[3:]
		// 处理 +8, -5, +08, -05 等格式
		var offset int
		var err error

		if len(offsetStr) > 0 {
			// 去掉可能的 + 号
			if offsetStr[0] == '+' {
				offsetStr = offsetStr[1:]
			}
			offset, err = strconv.Atoi(offsetStr)
			if err != nil {
				return nil, fmt.Errorf("invalid UTC offset format: %s", timezone)
			}
			// 验证偏移量范围 [-12, +14]
			if offset < -12 || offset > 14 {
				return nil, fmt.Errorf("invalid UTC offset range: %d, must be between -12 and +14", offset)
			}
			return time.FixedZone(timezone, offset*60*60), nil
		}
	}

	// 尝试加载标准时区
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("failed to parse timezone %s: %w", timezone, err)
	}
	return loc, nil
}
