package models

import "gorm.io/gorm"

type Address struct {
	gorm.Model
	UserID              string `gorm:"column:user_id"`
	CountryCode         string `gorm:"column:country_code"`
	CountryName         string `gorm:"column:country_name"`
	UserName            string `gorm:"column:user_name"`
	PhoneCountryCode    string `gorm:"column:phone_country_code"`
	PhoneAreaCode       string `gorm:"column:phone_area_code"`
	Phone               string `gorm:"column:phone"`
	ProvinceName        string `gorm:"column:province_name"`
	CityName            string `gorm:"column:city_name"`
	StreetAddress       string `gorm:"column:street_address"`
	BuildingUnitAddress string `gorm:"column:building_unit_address"`
	PostalCode          string `gorm:"column:postal_code"`
	IsDefault           bool   `gorm:"column:is_default"`
}

func (Address) TableName() string {
	return "user_address"
}
