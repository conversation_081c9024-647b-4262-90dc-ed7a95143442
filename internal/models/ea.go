package models

import "time"

type EAInfo struct {
	Id               int       `gorm:"column:id"`
	Name             string    `gorm:"column:name"`
	NameEN           string    `gorm:"column:name_en"`
	NameHK           string    `gorm:"colum:name_hk"`
	Price            float32   `gorm:"column:price"`
	PriceUSD         float32   `gorm:"column:price_USD"`
	PriceOriginal    float32   `json:"price_original"`
	PriceOriginalUSD float32   `gorm:"column:price_original_USD"`
	MTType           int       `gorm:"column:mttype"`         // 适用mt平台类型 0:MT4 1:MT5
	Code             string    `gorm:"column:code"`           // 适用品种
	Picture          string    `gorm:"column:picture"`        // 图片位置
	Summary          string    `gorm:"column:summary"`        // 概要
	SummaryEN        string    `gorm:"column:summary_en"`     // 概要_en
	SummaryHK        string    `gorm:"column:summary_hk"`     // 概要_hk
	Description      string    `gorm:"column:description"`    // 说明
	DescriptionEN    string    `gorm:"column:description_en"` // 说明_en
	DescriptionHK    string    `gorm:"column:description_hk"` // 说明_hk
	Data             string    `gorm:"column:data"`           // 回测报告位置
	EA               string    `gorm:"column:ea"`             // ea文件位置
	State            int       `gorm:"column:state"`          // 状态 0禁用 1启用
	CreateTime       time.Time `gorm:"column:createtime"`     // 创建时间
	UpdateTime       time.Time `gorm:"column:updatetime"`     // 更新时间
	ClassifyId       int       `gorm:"column:classify_id"`    // 所属EA类型，对应eatype
	ProfitRate       float32   `gorm:"column:profit_rate"`    // 年化收益率
	HomePateSite     int       `gorm:"column:homepagesite"`   // 首页显示位置 选择>0的按顺序排
	Dot              int       `gorm:"column:dot"`
	PurchaseQuantify string    `gorm:"column:purchaseQuantify"`
}

func (EAInfo) TableName() string {
	return "eainfo"
}

type EAType struct {
	Id          int       `gorm:"column:id"`
	Type        string    `gorm:"column:type"`       // EA类型
	TypeEN      string    `gorm:"column:type_en"`    // EA类型_en
	TypeHK      string    `gorm:"column:type_hk"`    // EA类型_hk
	Color       string    `gorm:"column:color"`      // 颜色
	CreateTime  time.Time `gorm:"column:createtime"` // 创建时间
	EANum       int       `gorm:"column:eaNum"`
	TranslateId string    // 翻译id
}

func (EAType) TableName() string {
	return "eatype"
}
