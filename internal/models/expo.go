package models

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/airunny/wiki-go-tools/igorm"
)

type Expo struct {
	ID            int64     `gorm:"column:id"`             //
	Name          string    `gorm:"column:name"`           // 展会名称
	Location      string    `gorm:"column:location"`       // 展会地点
	CountryCode   string    `gorm:"column:countryCode"`    // 展会国家
	Type          int       `gorm:"column:type"`           // 展会类型，0：展会，1：酒会
	SignUpURL     string    `gorm:"column:signUpURL"`      // 报名链接
	Start         time.Time `gorm:"column:start"`          // 开始时间
	End           time.Time `gorm:"column:end"`            // 结束时间
	Award         int       `gorm:"column:award"`          // 展会奖品
	Price         float32   `gorm:"column:price"`          // 展会门票价格
	PriceUnit     int       `gorm:"column:price_unit"`     // 展会门票单位
	Langs         string    `gorm:"column:langs"`          // 多语言
	Longitude     string    `gorm:"column:longitude"`      // 经度
	Latitude      string    `gorm:"column:latitude"`       // 纬度
	CreatedAt     time.Time `gorm:"column:created_at"`     // 创建时间
	Creator       string    `gorm:"column:creator"`        // 创建人
	ModifiedAt    time.Time `gorm:"column:modified_at"`    // 修改时间
	Modifier      string    `gorm:"column:modifier"`       // 修改人
	LocationName  string    `gorm:"column:location_name"`  // 展会显示名称
	Current       int       `gorm:"column:current"`        // 当前直播展会0：不是，1：是
	HomeLongitude string    `gorm:"column:home_longitude"` // 国内经度
	HomeLatitude  string    `gorm:"column:home_latitude"`  // 国内纬度
	TimeZone      string    `gorm:"column:time_zone"`      // 时区
	TicketType    int       `gorm:"column:ticket_type"`    // 展会门票类型
	SendTicket    int       `gorm:"column:send_ticket"`    // 1:人工发放,2:自动发放
	Registrants   int       `gorm:"column:registrants"`    // 报名人数
	BaiduMap      string    `gorm:"column:baidu_map"`      // 百度地图
	GoogleMap     string    `gorm:"column:google_map"`     // google地图
}

func (*Expo) TableName() string {
	return "expo"
}

func (s *Expo) FromLangs() []*ExpoLang {
	var out []*ExpoLang
	_ = json.Unmarshal([]byte(s.Langs), &out)
	return out
}

func (s *Expo) GetZoneDiff() time.Duration {
	var (
		timeZone    = strings.TrimPrefix(strings.ToLower(s.TimeZone), "utc")
		zoneDiff, _ = strconv.Atoi(timeZone)
	)
	return time.Duration(zoneDiff) * time.Hour
}

func (s *Expo) GetNow() time.Time {
	return time.Now().UTC().Add(s.GetZoneDiff())
}

//func (s *Expo) GetStartUTC() time.Time {
//	return s.getTime(s.Start)
//}
//
//func (s *Expo) GetEndUTC() time.Time {
//	return s.getTime(s.End)
//}
//
//func (s *Expo) getTime(in time.Time) time.Time {
//	var (
//		timeZone    = strings.TrimPrefix(strings.ToLower(s.TimeZone), "utc")
//		zoneDiff, _ = strconv.Atoi(timeZone)
//	)
//	zoneDiff -= zoneDiff
//	return in.Add(time.Duration(zoneDiff) * time.Hour)
//}

type ExpoLang struct {
	WebCover           string `json:"webCover"`
	AppCover           string `json:"appCover"`
	AppDetailCover     string `json:"appDetailCover"`
	WebBackgroundImage string `json:"webBackgroundImage"`
	AppBackgroundImage string `json:"appBackgroundImage"`
	WebShareBackground string `json:"webShareBackground"`
	AppShareBackground string `json:"appShareBackground"`
	Lang               string `json:"lang"`
	CustomerEmail      string `json:"customerEmail"`
	AdmissionNotice    string `json:"admissionNotice"`
	Description        string `json:"description"`
}

type ExpoUnion struct {
	Expo
	Extra *igorm.CustomValue[*ExpoCommunityExtra] `gorm:"column:extra"`
}
