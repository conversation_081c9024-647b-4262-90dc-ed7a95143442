package models

import (
	"time"
)

// TaskProgress 用户任务进度表
type TaskProgress struct {
	ID           uint               `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID       string             `gorm:"column:user_id;not null" json:"user_id"`                       // 用户Id
	TaskConfigID uint               `gorm:"column:task_config_id;not null" json:"task_config_id"`         // 任务配置Id
	UserProgress int                `gorm:"column:user_progress;default:0;not null" json:"user_progress"` // 用户完成进度
	Status       UserProgressStatus `gorm:"column:status;default:1;not null" json:"status"`               // 任务状态
	ExpireTime   time.Time          `gorm:"column:expire_time;not null" json:"expire_time"`               // 过期时间
	ReceiveTime  *time.Time         `gorm:"column:receive_time" json:"receive_time"`                      // 奖励领取时间
	UserTimezone string             `gorm:"column:user_timezone;default:UTC" json:"user_timezone"`        // 用户所在时区
	CreatedAt    time.Time          `gorm:"column:created_at;not null" json:"created_at"`                 // 创建时间
	UpdatedAt    time.Time          `gorm:"column:updated_at;not null" json:"updated_at"`                 // 更新时间

	// 关联模型
	TaskConfig *TaskConfig `gorm:"foreignKey:TaskConfigID" json:"task_config,omitempty"`
}

// TableName 返回表名
func (TaskProgress) TableName() string {
	return "task_progress"
}

// IsExpired 判断任务是否已过期
func (tp *TaskProgress) IsExpired() bool {
	return time.Now().UTC().After(tp.ExpireTime)
}

// IsCompleted 判断任务是否已完成
func (tp *TaskProgress) IsCompleted(taskConfig *TaskConfig) bool {
	if taskConfig == nil {
		return false
	}
	return tp.UserProgress >= taskConfig.CompleteTimes
}

// CanReceiveReward 判断是否可以领取奖励
func (tp *TaskProgress) CanReceiveReward(taskConfig *TaskConfig) bool {
	return tp.Status == UserProgressStatusCompleted && !tp.IsExpired() && tp.IsCompleted(taskConfig)
}
