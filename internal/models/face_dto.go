package models

// FaceSearchResult 人脸搜索结果
type FaceSearchResult struct {
	SearchID           string                  `json:"search_id"`            // 搜索批次ID
	TotalSearchedFaces int                     `json:"total_searched_faces"` // 搜索到的总人脸数
	SearchDurationMs   int64                   `json:"search_duration_ms"`   // 搜索耗时（毫秒）
	SearchFaceRect     []*FaceRect             `json:"search_face_rect"`     // 搜索图片中检测到的所有人脸坐标
	Results            []*FaceSearchResultItem `json:"results"`              // 搜索结果列表
	TotalResults       int                     `json:"total_results"`        // 总结果数
	Page               int                     `json:"page"`                 // 当前页码
	PageSize           int                     `json:"page_size"`            // 每页大小
	FromCache          bool                    `json:"from_cache"`           // 是否来自缓存
	AllResults         []*FaceSearchResultItem `json:"-"`                    // 完整结果列表（不序列化到JSON，仅用于分页）
	ImageHeight        int                     `json:"image_height"`         // 搜索图片高度
	ImageWidth         int                     `json:"image_width"`          // 搜索图片宽度
}

// FaceSearchResultItem 人脸搜索结果项
type FaceSearchResultItem struct {
	PhotoURL          string          `json:"photo_urls"`          // 匹配到的展会照片URL
	MaxSimilarity     float64         `json:"max_similarity"`      // 最高相似度
	MatchedFacesCount int             `json:"matched_faces_count"` // 匹配到的人脸数量
	ExhibitionInfo    *ExhibitionInfo `json:"exhibition_info"`     // 展会信息
}

// ExhibitionInfo 展会信息
type ExhibitionInfo struct {
	ExhibitionID   int64  `json:"exhibition_id"`   // 展会ID
	ExhibitionName string `json:"exhibition_name"` // 展会名称
	ExhibitionDate string `json:"exhibition_date"` // 展会日期
}

// FaceUploadResult 人脸上传结果
type FaceUploadResult struct {
	UploadID         string                  `json:"upload_id"`          // 上传批次ID
	OriginalPhotoURL string                  `json:"original_photo_url"` // 原始照片URL
	TotalFaces       int                     `json:"total_faces"`        // 检测到的总人脸数
	SuccessFaces     int                     `json:"success_faces"`      // 成功入库的人脸数
	FailedFaces      int                     `json:"failed_faces"`       // 失败的人脸数
	Faces            []*FaceUploadResultItem `json:"faces"`              // 人脸处理结果列表
}

// FaceUploadResultItem 人脸上传结果项
type FaceUploadResultItem struct {
	FaceID       string    `json:"face_id"`                 // 业务人脸ID
	FaceURL      string    `json:"face_url"`                // 人脸截图URL
	QualityScore float64   `json:"quality_score"`           // 质量分数（0-100）
	Rect         *FaceRect `json:"rect"`                    // 人脸在原图中的坐标
	Status       string    `json:"status"`                  // 入库状态：success/failed
	ErrorMessage string    `json:"error_message,omitempty"` // 错误信息（失败时）
}

// FaceDetectionInfo 人脸检测信息
type FaceDetectionInfo struct {
	FaceRect     *FaceRect  `json:"face_rect"`     // 人脸矩形坐标
	QualityScore float64    `json:"quality_score"` // 质量分数
	Gender       FaceGender `json:"gender"`        // 性别
	Age          int8       `json:"age"`           // 年龄
	Beauty       float64    `json:"beauty"`        // 颜值分数
}

// FaceGroupInfo 人员库信息
type FaceGroupInfo struct {
	GroupID            string `json:"group_id"`
	Name               string `json:"name"`
	Description        string `json:"description"`
	FaceModelVersion   string `json:"face_model_version"`
	MaxFaces           int    `json:"max_faces"`
	EstimatedFaceCount int    `json:"estimated_face_count"`
	Status             int    `json:"status"`
	CreatedAt          string `json:"created_at"`
}

// FaceGroupCreateRequest 创建人员库请求
type FaceGroupCreateRequest struct {
	Name        string `json:"name" validate:"required,max=60"`  // 人员库名称
	Description string `json:"description" validate:"max=200"`   // 描述
	ExpoID      int64  `json:"expo_id" validate:"max=64"`        // 场景ID
	MaxFaceNum  int    `json:"max_face_num" validate:"min=1000"` // 最大人脸数
}

// FacePhotoUploadRequest 人脸照片上传请求
type FacePhotoUploadRequest struct {
	GroupID              string `json:"group_id" validate:"required"`            // 人员库ID
	PhotoURL             string `json:"photo_url" validate:"required,url"`       // 展会照片URL
	ExpoID               int64  `json:"expo_id" validate:"max=64"`               // 展会场景ID
	MaxFaceNum           int    `json:"max_face_num" validate:"min=1,max=50"`    // 最大检测人脸数
	MinFaceSize          int    `json:"min_face_size" validate:"min=30,max=200"` // 最小人脸尺寸像素
	NeedQualityDetection bool   `json:"need_quality_detection"`                  // 是否需要质量检测
	Operator             string `json:"operator" validate:"max=64"`              // 操作员ID
}

// FaceSearchRequest 人脸搜索请求
type FaceSearchRequest struct {
	SearchPhotoURL     string   `json:"search_photo_url" validate:"required,url"` // 用户自拍照URL
	GroupIDs           []string `json:"group_ids" validate:"required,min=1"`      // 搜索的展会人员库列表
	NeedFaceRect       bool     `json:"need_face_rect"`                           // 是否返回人脸坐标
	FaceMatchThreshold float64  `json:"face_match_threshold"`                     // 人脸匹配阈值，0-1之间，可选
	Page               int      `json:"page"`                                     // 页码
	PageSize           int      `json:"page_size"`                                // 每页大小
}

// FaceGroupQueryRequest 人员库查询请求
type FaceGroupQueryRequest struct {
	GroupIDs []string        `json:"group_ids"` // 人员库ID列表
	Name     string          `json:"name"`      // 人员库名称（模糊搜索）
	Status   FaceGroupStatus `json:"status"`    // 状态筛选
	Page     int             `json:"page"`      // 页码
	PageSize int             `json:"page_size"` // 每页大小
}

// FacePhotoQueryRequest 人脸照片查询请求
type FacePhotoQueryRequest struct {
	GroupID          string          `json:"group_id"`           // 人员库ID
	OriginalPhotoURL string          `json:"original_photo_url"` // 原始照片URL
	UploadUserID     string          `json:"upload_user_id"`     // 上传用户ID
	Status           FacePhotoStatus `json:"status"`             // 状态筛选
	MinQualityScore  float64         `json:"min_quality_score"`  // 最小质量分数
	Page             int             `json:"page"`               // 页码
	PageSize         int             `json:"page_size"`          // 每页大小
}

// FaceGroupListRequest 人员库列表请求
type FaceGroupListRequest struct {
	Page int `json:"page"`
	Size int `json:"size"`
}

// FaceGroupListResponse 人员库列表响应
type FaceGroupListResponse struct {
	Groups []*FaceGroupInfo `json:"groups"`
	Total  int              `json:"total"`
	Page   int              `json:"page"`
	Size   int              `json:"size"`
}

// FaceGroupQueryParams 人员库查询参数
type FaceGroupQueryParams struct {
	ExpoType string
	Status   int
	Page     int
	Size     int
}

// FaceSearchLimitConfig 人脸搜索限流配置
type FaceSearchLimitConfig struct {
	UserLimitPerMin int `json:"user_limit_per_min"` // 用户每分钟限制次数
	UserLimitPerDay int `json:"user_limit_per_day"` // 用户每日限制次数
	IPLimitPerMin   int `json:"ip_limit_per_min"`   // IP每分钟限制次数
	AlertThreshold  int `json:"alert_threshold"`    // 报警阈值（百分比）
	CacheTTL        int `json:"cache_ttl"`          // 缓存TTL（秒）
}

// FaceSearchUsageStats 人脸搜索使用统计
type FaceSearchUsageStats struct {
	UserID             string  `json:"user_id"`              // 用户ID
	MinuteUsed         int     `json:"minute_used"`          // 本分钟已使用次数
	MinuteLimit        int     `json:"minute_limit"`         // 每分钟限制次数
	DayUsed            int     `json:"day_used"`             // 今日已使用次数
	DayLimit           int     `json:"day_limit"`            // 每日限制次数
	MinuteRemaining    int     `json:"minute_remaining"`     // 本分钟剩余次数
	DayRemaining       int     `json:"day_remaining"`        // 今日剩余次数
	MinuteUsagePercent float64 `json:"minute_usage_percent"` // 本分钟使用率
	DayUsagePercent    float64 `json:"day_usage_percent"`    // 今日使用率
}

// FaceSearchCacheStats 人脸搜索缓存统计
type FaceSearchCacheStats struct {
	TotalKeys    int    `json:"total_keys"`    // 总缓存键数量
	TotalSizeKB  int64  `json:"total_size_kb"` // 总缓存大小（KB）
	CacheTTL     int    `json:"cache_ttl"`     // 缓存TTL（秒）
	CachePattern string `json:"cache_pattern"` // 缓存键模式
}

// PhotoFacesResponse 照片人脸列表响应
type PhotoFacesResponse struct {
	PhotoURL     string                  `json:"photo_url"`
	TotalFaces   int                     `json:"total_faces"`
	SuccessFaces int                     `json:"success_faces"`
	FailedFaces  int                     `json:"failed_faces"`
	Faces        []*FaceUploadResultItem `json:"faces"`
}

// GeetestValidationRequest 极验验证请求
type GeetestValidationRequest struct {
	LotNumber     string `json:"lot_number" validate:"required"`     // 验证流水号
	CaptchaOutput string `json:"captcha_output" validate:"required"` // 验证输出信息
	PassToken     string `json:"pass_token" validate:"required"`     // 验证通过标识
	GenTime       string `json:"gen_time" validate:"required"`       // 验证生成时间
}

// GeetestValidationResponse 极验验证响应
type GeetestValidationResponse struct {
	Success bool   `json:"success"`         // 验证是否成功
	Message string `json:"message"`         // 响应消息
	Score   int    `json:"score,omitempty"` // 验证分数（可选）
}
