package models

import (
	"time"

	"gorm.io/gorm"
)

// FacePhotoRelation 人脸照片关联表-存储人脸与照片的关联关系
type FacePhotoRelation struct {
	ID               uint64 `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                                                       // 主键ID
	FaceBusinessID   string `gorm:"column:face_business_id;size:64;not null;uniqueIndex" json:"face_business_id" validate:"required"`   // 业务人脸ID（自定义UUID）
	TencentPersonID  string `gorm:"column:tencent_person_id;size:64;not null;uniqueIndex" json:"tencent_person_id" validate:"required"` // 腾讯云PersonId（每个人脸对应一个独立Person）
	GroupID          string `gorm:"column:group_id;size:64;not null;index:idx_group_status" json:"group_id" validate:"required"`        // 人员库ID
	OriginalPhotoURL string `gorm:"column:original_photo_url;type:text;not null" json:"original_photo_url" validate:"required,url"`     // 原始照片URL

	// 人脸坐标信息 (用于在原图中定位人脸位置)
	FaceRectX      int `gorm:"column:face_rect_x;not null;default:0" json:"face_rect_x"`           // 人脸矩形左上角X坐标
	FaceRectY      int `gorm:"column:face_rect_y;not null;default:0" json:"face_rect_y"`           // 人脸矩形左上角Y坐标
	FaceRectWidth  int `gorm:"column:face_rect_width;not null;default:0" json:"face_rect_width"`   // 人脸矩形宽度
	FaceRectHeight int `gorm:"column:face_rect_height;not null;default:0" json:"face_rect_height"` // 人脸矩形高度

	// 人脸质量和属性信息 (腾讯云API返回的分析结果)
	QualityScore float64    `gorm:"column:quality_score;type:decimal(5,2);default:0.00" json:"quality_score"` // 人脸质量分数（0-100）
	Gender       FaceGender `gorm:"column:gender;default:0" json:"gender"`                                    // 性别：0=未知 1=男 2=女
	Age          int8       `gorm:"column:age;default:0" json:"age"`                                          // 估算年龄
	Beauty       float64    `gorm:"column:beauty;type:decimal(5,2);default:0.00" json:"beauty"`               // 颜值分数（0-100）

	// 腾讯云相关字段
	TencentFaceID string `gorm:"column:tencent_face_id;size:64;default:''" json:"tencent_face_id"` // 腾讯云返回的FaceId

	// 业务字段
	UploadUserID string          `gorm:"column:upload_user_id;size:64;default:'';index" json:"upload_user_id"`           // 上传用户ID
	UploadTime   time.Time       `gorm:"column:upload_time;not null;default:CURRENT_TIMESTAMP;index" json:"upload_time"` // 上传时间
	Status       FacePhotoStatus `gorm:"column:status;not null;default:1;index:idx_group_status" json:"status"`          // 状态：1=正常 2=已删除
	CreatedAt    time.Time       `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`         // 创建时间
	UpdatedAt    time.Time       `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`         // 更新时间

	// 关联字段
	FaceGroup *FaceGroup `gorm:"foreignKey:GroupID;references:ID" json:"face_group,omitempty"` // 关联人员库
}

// TableName 指定表名
func (FacePhotoRelation) TableName() string {
	return "face_photo_relations"
}

// BeforeCreate 创建前钩子
func (fpr *FacePhotoRelation) BeforeCreate(tx *gorm.DB) error {
	if fpr.Status == 0 {
		fpr.Status = FacePhotoStatusNormal
	}
	if fpr.UploadTime.IsZero() {
		fpr.UploadTime = time.Now()
	}
	return nil
}

// IsNormal 判断人脸照片是否为正常状态
func (fpr *FacePhotoRelation) IsNormal() bool {
	return fpr.Status == FacePhotoStatusNormal
}

// IsDeleted 判断人脸照片是否已删除
func (fpr *FacePhotoRelation) IsDeleted() bool {
	return fpr.Status == FacePhotoStatusDeleted
}

// CanDelete 判断人脸照片是否可以删除
func (fpr *FacePhotoRelation) CanDelete() bool {
	return fpr.Status == FacePhotoStatusNormal
}

// MarkDeleted 标记为已删除状态
func (fpr *FacePhotoRelation) MarkDeleted() {
	fpr.Status = FacePhotoStatusDeleted
}

// GetFaceRect 获取人脸矩形坐标信息
func (fpr *FacePhotoRelation) GetFaceRect() FaceRect {
	return FaceRect{
		X:      fpr.FaceRectX,
		Y:      fpr.FaceRectY,
		Width:  fpr.FaceRectWidth,
		Height: fpr.FaceRectHeight,
	}
}

// SetFaceRect 设置人脸矩形坐标信息
func (fpr *FacePhotoRelation) SetFaceRect(rect FaceRect) {
	fpr.FaceRectX = rect.X
	fpr.FaceRectY = rect.Y
	fpr.FaceRectWidth = rect.Width
	fpr.FaceRectHeight = rect.Height
}

// IsHighQuality 判断是否为高质量人脸（质量分数>=60）
func (fpr *FacePhotoRelation) IsHighQuality() bool {
	return fpr.QualityScore >= 60.0
}

// GetGenderString 获取性别字符串表示
func (fpr *FacePhotoRelation) GetGenderString() string {
	return fpr.Gender.String()
}

// FaceRect 人脸矩形坐标
type FaceRect struct {
	X      int `json:"x"`      // 左上角X坐标
	Y      int `json:"y"`      // 左上角Y坐标
	Width  int `json:"width"`  // 宽度
	Height int `json:"height"` // 高度
}

// IsValid 判断矩形坐标是否有效
func (fr *FaceRect) IsValid() bool {
	return fr.X >= 0 && fr.Y >= 0 && fr.Width > 0 && fr.Height > 0
}

// Area 计算矩形面积
func (fr *FaceRect) Area() int {
	if !fr.IsValid() {
		return 0
	}
	return fr.Width * fr.Height
}
