// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameGiftCardSetting = "gift_card_setting"

// GiftCardSetting 礼品卡配置表
type GiftCardSetting struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	GoodsID          string    `gorm:"column:goods_id;not null;comment:产品Id" json:"goods_id"`                                   // 产品Id
	IsPinkage        int32     `gorm:"column:is_pinkage;not null;comment:是否包邮 0不包邮 1包邮" json:"is_pinkage"`                      // 是否包邮 0不包邮 1包邮
	InDate           int32     `gorm:"column:in_date;not null;comment:有效期类型 0固定 1相对" json:"in_date"`                            // 有效期类型 0固定 1相对
	IndateDays       int32     `gorm:"column:in_date_days;not null;comment:有效期天数 相对时有值" json:"in_date_days"`                    // 有效期天数 相对时有值
	StartTime        time.Time `gorm:"column:start_time;comment:有效期开始时间" json:"start_time"`                                     // 有效期开始时间
	EndTime          time.Time `gorm:"column:end_time;comment:有效期结束时间" json:"end_time"`                                         // 有效期结束时间
	UseTimes         int32     `gorm:"column:use_times;not null;comment:使用次数" json:"use_times"`                                 // 使用次数
	PromotionChannel string    `gorm:"column:promotion_channel;not null;comment:推广渠道" json:"promotion_channel"`                 // 推广渠道
	Proposer         string    `gorm:"column:proposer;not null;comment:申请人" json:"proposer"`                                    // 申请人
	CreatedUser      string    `gorm:"column:created_user;not null;comment:申请人" json:"created_user"`                            // 申请人
	CreatedAt        time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
	Image            string    `gorm:"column:image;not null;comment:弹框图片" json:"image"`                                         // 弹框图片

}

// TableName GiftCardSetting's table name
func (*GiftCardSetting) TableName() string {
	return TableNameGiftCardSetting
}
