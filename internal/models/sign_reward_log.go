package models

import (
	"time"
)

// SignRewardLog 奖励流水表模型
type SignRewardLog struct {
	ID          uint      `gorm:"primaryKey;column:id;type:int unsigned;auto_increment" json:"id"`
	UserID      string    `gorm:"column:user_id;type:varchar(64);not null" json:"user_id"`               // 用户ID（唯一标识）
	RecordId    uint      `gorm:"column:record_id;type:int unsigned;not null" json:"record_id"`          // 任务ID
	SignID      uint      `gorm:"column:sign_id;type:int unsigned;not null" json:"sign_id"`              // 来源关联ID（签到记录ID）
	SourceType  int8      `gorm:"column:source_type;type:tinyint;not null;default:1" json:"source_type"` // 来源类型（1=签到送，2=签到满送）
	GoldCoins   int32     `gorm:"column:gold_coins;type:int;not null;default:0" json:"gold_coins"`       // 奖励数量（如金币数量）
	Status      int8      `gorm:"column:status;type:tinyint;not null;default:0" json:"status"`           // 0=处理中，1=成功，2=失败
	ErrorMsg    string    `gorm:"column:error_msg;type:varchar(255)" json:"error_msg"`                   // 失败原因
	OperationID string    `gorm:"column:operation_id;type:varchar(64);not null" json:"operation_id"`     // 操作唯一标识（防重复）
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime;not null;" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 返回表名
func (SignRewardLog) TableName() string {
	return "sign_reward_log"
}

// SourceTypeEnum 来源类型枚举
const (
	SourceTypeRegularSign     int8 = 1 // 普通签到奖励
	SourceTypeConsecutiveSign int8 = 2 // 连续签到奖励
)

// StatusEnum 奖励状态枚举
const (
	StatusProcessing int8 = 0 // 处理中
	StatusSuccess    int8 = 1 // 成功
	StatusFailed     int8 = 2 // 失败
)
