package models

import (
	"time"
)

// TaskRewardIssue 奖励发放记录表
type TaskRewardIssue struct {
	ID             uint                  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID         string                `gorm:"column:user_id;not null" json:"user_id"`                   // 用户Id
	TaskConfigID   uint                  `gorm:"column:task_config_id;not null" json:"task_config_id"`     // 任务配置Id
	TaskProgressID uint                  `gorm:"column:task_progress_id;not null" json:"task_progress_id"` // 任务进度Id
	RewardType     RewardType            `gorm:"column:reward_type;not null" json:"reward_type"`           // 奖励类型
	RewardConfig   string                `gorm:"column:reward_config;type:json;not null" json:"-"`         // 奖励配置内容（JSON格式）
	Status         TaskRewardIssueStatus `gorm:"column:status;default:0;not null" json:"status"`           // 发放状态
	FailReason     string                `gorm:"column:fail_reason" json:"fail_reason"`                    // 失败原因
	OperationID    string                `gorm:"column:operation_id;not null" json:"operation_id"`         // 操作唯一标识
	CreatedAt      time.Time             `gorm:"column:created_at;not null" json:"created_at"`             // 创建时间
	UpdatedAt      time.Time             `gorm:"column:updated_at;not null" json:"updated_at"`             // 更新时间

	// 关联模型
	TaskConfig   *TaskConfig   `gorm:"foreignKey:TaskConfigID" json:"task_config,omitempty"`
	TaskProgress *TaskProgress `gorm:"foreignKey:TaskProgressID" json:"task_progress,omitempty"`
}

// TableName 返回表名
func (TaskRewardIssue) TableName() string {
	return "task_reward_issue"
}

// IsSuccess 判断奖励是否发放成功
func (r *TaskRewardIssue) IsSuccess() bool {
	return r.Status == TaskRewardIssueIssued
}

// IsFailed 判断奖励发放是否失败
func (r *TaskRewardIssue) IsFailed() bool {
	return r.Status == TaskRewardIssueFailed
}

// IsProcessing 判断奖励是否正在发放中
func (r *TaskRewardIssue) IsProcessing() bool {
	return r.Status == TaskRewardIssueIssuing
}

// CanRetry 判断是否可以重试发放
func (r *TaskRewardIssue) CanRetry() bool {
	return r.Status == TaskRewardIssueFailed
}
