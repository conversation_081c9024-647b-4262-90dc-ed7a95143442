package models

import "gorm.io/gorm"

type ExpoScheduleGuest struct {
	gorm.Model
	ExpoId     int64 `gorm:"column:expo_id"`     // 展会ID
	ScheduleId int64 `gorm:"column:schedule_id"` // 议程ID
	Role       int   `gorm:"column:role"`        // 角色：0：演讲嘉宾；1：主持人
	GuestId    int64 `gorm:"column:guest_id"`    // 演讲嘉宾ID(guest表主键)
}

func (*ExpoScheduleGuest) TableName() string {
	return "expo_schedule_guest"
}

type ExpoScheduleGuestGroup struct {
	GuestId int64 `gorm:"column:guest_id"`
	Count   int64 `gorm:"column:count"`
}
