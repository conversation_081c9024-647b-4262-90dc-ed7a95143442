package models

import "gorm.io/gorm"

type ExpoScheduleReserve struct {
	gorm.Model
	ExpoId          int64  `gorm:"column:expo_id"`
	UserId          string `gorm:"column:user_id"`
	ScheduleGuestId int64  `gorm:"column:schedule_guest_id"`
	Notify          int    `gorm:"column:notify"` // 是否通知
	LanguageCode    string `gorm:"column:language_code"`
}

func (*ExpoScheduleReserve) TableName() string {
	return "expo_schedule_reserve"
}
