package models

import "time"

type Config struct {
	Id         int32     `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	Type       string    `json:"type" gorm:"column:config_type"`
	Value      string    `json:"value" gorm:"column:config_value"`
	CreatedAt  time.Time `json:"createdAt" gorm:"column:created_at"`
	Creator    string    `json:"creator" gorm:"column:creator"`
	ModifiedAt time.Time `json:"modifiedAt" gorm:"column:modified_at"`
	Modifier   string    `json:"modifier" gorm:"column:modifier"`
}

func (*Config) TableName() string {
	return "config"
}
