package models

type VPSImage struct {
	Id                int     `gorm:"column:PrimaryId"`         // 主键
	IndexId           int     `gorm:"column:IndexId"`           // 列表索引位置
	ImageId           string  `gorm:"column:ImageId"`           // 镜像ID
	Language          int     `gorm:"column:Language"`          // 系统语言 1中文 2英文
	SystemType        string  `gorm:"column:SystemType"`        // 系统类型
	Zone              string  `gorm:"column:Zone"`              // 所属可用区
	ZonaName          string  `gorm:"column:ZonaName"`          // 所属可用区名称
	Region            string  `gorm:"column:Region"`            // 所属区域
	RegionName        string  `gorm:"column:RegionName"`        // 所属区域名称
	RegionRoute       string  `gorm:"column:RegionRoute"`       // 区域路由（后端ucloudapi使用）
	RegionEnglishName string  `gorm:"column:RegionEnglishName"` // 区域英文名称
	Zhou              string  `gorm:"column:Zhou"`              // 所属洲
	ZhouEnglish       string  `gorm:"column:ZhouEnglish"`       // 洲英文
	Amount            float32 `gorm:"column:Amount"`            // 费用（月）
	Enable            int     `gorm:"column:Enable"`            // 是否启用 0禁用 1启用
	EnableGlobal      int     `gorm:"column:EnableGlobal"`      // 是否启用 国际
	Config            int     `gorm:"column:Config"`            // 1 低配 2 高配
	GoodsId           string  `gorm:"column:GoodsId"`           // 商品ID
	DefaultSelect     int     `gorm:"column:DefaultSelect"`     // 默认选择 0非选中 1选中
	BrokerId          string  `gorm:"column:BrokerId"`          // 定制经纪商ID
	Tips              string  `gorm:"column:Tips"`              // 选择后提示
	RegionNameKey     string  `gorm:"column:RegionNameKey"`     //
	HostType          int     `gorm:"column:HostType"`          // 云主机类型
	ResourceEnough    int     `gorm:"column:ResourceEnough"`    // 资源充足 0 不足 1充足
	ZhuanShu          int     `gorm:"column:zhuanshu"`          // 专属vps专用镜像
	Orders            int     `gorm:"column:orders"`            // 顺序
}

func (VPSImage) TableName() string {
	return "images"
}
