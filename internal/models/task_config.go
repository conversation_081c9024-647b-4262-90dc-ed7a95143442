package models

import (
	"encoding/json"
	"gorm.io/gorm"
	"time"
)

// TaskConfig 任务配置表
type TaskConfig struct {
	ID            uint       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TaskType      TaskType   `gorm:"column:task_type;not null" json:"task_type"`           // 任务类型（1=新手任务，2=日常任务）
	TaskTitle     string     `gorm:"column:task_title;not null" json:"task_title"`         // 任务标题
	TaskEnumCode  string     `gorm:"column:task_enum_code;not null" json:"task_enum_code"` // 任务枚举表Id
	CompleteTimes int        `gorm:"column:complete_times;not null" json:"complete_times"` // 完成次数
	TaskIcon      string     `gorm:"column:task_icon" json:"task_icon"`                    // 任务图标路径
	RewardIcon    string     `gorm:"column:reward_icon" json:"reward_icon"`                // 奖励图标路径
	MinVersion    string     `gorm:"column:min_version" json:"min_version"`                // 最低版本号
	ShowProject   string     `gorm:"column:show_project;not null" json:"show_project"`     // 展示项目
	RewardType    RewardType `gorm:"column:reward_type;not null" json:"reward_type"`       // 奖励类型
	RewardConfig  string     `gorm:"column:reward_config;type:json;not null" json:"-"`     // 奖励配置内容（JSON格式）
	TaskConfig    string     `gorm:"column:task_config;type:json;not null" json:"-"`       // 任务额外配置（JSON格式）
	I18n          string     `gorm:"column:i18n;type:json;not null" json:"-"`              // 多语言配置（JSON格式）
	SortOrder     int        `gorm:"column:sort_order;default:0" json:"sort_order"`        // 排序值
	ConfigVersion string     `gorm:"column:config_version" json:"config_version"`          // 配置版本号
	VisibleUsers  string     `gorm:"column:visible_users" json:"visible_users"`            // 可见用户范围
	Modifier      string     `gorm:"column:modifier" json:"modifier"`                      // 最后修改人
	Status        int8       `gorm:"column:status;default:0;not null" json:"status"`       // 状态（1=开启，2=关闭）
	CreatedAt     time.Time  `gorm:"column:created_at;not null" json:"created_at"`         // 记录时间
	UpdatedAt     time.Time  `gorm:"column:updated_at;not null" json:"updated_at"`         // 更新时间

	// 非数据库字段，用于处理JSON数据
	RewardConfigObj RewardDetail   `gorm:"-" json:"reward_config"` // 解析后的奖励配置对象
	TaskConfigObj   TaskConfigData `gorm:"-" json:"task_config"`   // 解析后的任务配置对象
	I18nObj         I18nData       `gorm:"-" json:"i18n"`          // 解析后的多语言配置对象
}

// TableName 返回表名
func (TaskConfig) TableName() string {
	return "task_config"
}

// TaskStatusEnum 任务状态枚举
const (
	TaskStatusEnumNone    = 0 // 未发布
	TaskStatusEnumOngoing = 1 // 进行中
	TaskStatusEnumClose   = 2 // 关闭
)

// RewardDetail 奖励详情结构体
type RewardDetail struct {
	RewardType   RewardType    `json:"reward_type"`
	GoldCoins    int           `json:"gold_coins"`
	Goods        *GoodsDetail  `json:"goods,omitempty"`
	VirtualGoods *VirtualGoods `json:"virtual_goods,omitempty"`
}

// GoodsDetail 实物商品详情
type GoodsDetail struct {
	GoodsID      string `json:"goods_id"`
	FreeShipping bool   `json:"free_shipping"`
	GoodsIcon    string `json:"goods_icon"`
}

// VirtualGoods 虚拟商品详情
type VirtualGoods struct {
	GoodsID   string `json:"goods_id"`
	GoodsIcon string `json:"goods_icon"`
}

// TaskConfigData 任务额外配置结构体
type TaskConfigData struct {
	TargetCode string `json:"target_code,omitempty"` // 目标
	TimeLimit  int32  `json:"time_limit,omitempty"`  // 时间限制(秒)
}

// I18nData 多语言配置结构体
type LanguageInfo struct {
	TaskTitle string `json:"task_title"` // 任务名称的多语言配置
	TaskDesc  string `json:"task_desc"`  // 任务描述的多语言配置
}

type I18nData map[string]LanguageInfo

// AfterFind 从数据库加载后，解析JSON
func (t *TaskConfig) AfterFind(_ *gorm.DB) error {
	var err error

	if t.RewardConfig != "" {
		if err = json.Unmarshal([]byte(t.RewardConfig), &t.RewardConfigObj); err != nil {
			return err
		}
	}

	if t.TaskConfig != "" {
		if err = json.Unmarshal([]byte(t.TaskConfig), &t.TaskConfigObj); err != nil {
			return err
		}
	}

	if t.I18n != "" {
		if err = json.Unmarshal([]byte(t.I18n), &t.I18nObj); err != nil {
			return err
		}
	}

	return nil
}

// BeforeSave 保存到数据库前，序列化JSON
func (t *TaskConfig) BeforeSave(_ *gorm.DB) error {
	data, err := json.Marshal(t.RewardConfigObj)
	if err != nil {
		return err
	}
	t.RewardConfig = string(data)

	data, err = json.Marshal(t.TaskConfigObj)
	if err != nil {
		return err
	}
	t.TaskConfig = string(data)

	data, err = json.Marshal(t.I18nObj)
	if err != nil {
		return err
	}
	t.I18n = string(data)

	return nil
}
