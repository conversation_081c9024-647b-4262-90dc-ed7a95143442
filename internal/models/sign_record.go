package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gold_store/internal/util"
	"time"
)

// Value 实现driver.Valuer接口
func (r RewardJSON) Value() (driver.Value, error) {
	return json.Marshal(r)
}

// Scan 实现sql.Scanner接口
func (r *RewardJSON) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &r)
}

// RewardJSONMap 用于存储JSON格式的奖励详情
type RewardJSONMap struct {
	Regular     RewardJSON `json:"reward"`             // 普通奖励
	Consecutive RewardJSON `json:"consecutive_reward"` // 连续签到奖励
}

// Value 实现driver.Valuer接口
func (r RewardJSONMap) Value() (driver.Value, error) {
	return json.Marshal(r)
}

// Scan 实现sql.Scanner接口
func (r *RewardJSONMap) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, &r)
}

// SignRecord 用户签到记录表模型
type SignRecord struct {
	ID              uint          `gorm:"primaryKey;column:id;type:int unsigned;auto_increment" json:"id"`
	UserID          string        `gorm:"column:user_id;type:varchar(64);not null" json:"user_id"`                     // 用户ID（唯一标识）
	TaskID          uint          `gorm:"column:task_id;type:int unsigned;not null" json:"task_id"`                    // 任务ID（关联任务配置表）
	SignDate        time.Time     `gorm:"column:sign_date;type:timestamp;not null" json:"sign_date"`                   // 签到日期 utc 时间
	ConsecutiveDays int           `gorm:"column:consecutive_days;type:int;not null;default:1" json:"consecutive_days"` // 当前连续签到天数
	RewardJSON      RewardJSONMap `gorm:"column:reward_json;type:json;not null" json:"reward_json"`                    // 奖励详情JSON
	UserTimezone    string        `gorm:"column:user_timezone;type:varchar(50);default:UTC" json:"user_timezone"`      // 用户所在时区
	CreatedAt       time.Time     `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       time.Time     `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 返回表名
func (SignRecord) TableName() string {
	return "sign_record"
}

// IsToday 判断签到记录是否为今天（考虑时区）
func (s *SignRecord) IsToday(timezone string) bool {
	loc, err := util.ParseTimezone(timezone)
	if err != nil {
		return false
	}

	// 获取现在时间
	now := util.GetCurrentTimeWithEnvByTimezone(timezone).In(loc)

	// 将数据库中的UTC时间签到日期转换为用户时区
	signDateInUserTz := s.SignDate.In(loc)

	// 比较年月日
	nowYear, nowMonth, nowDay := now.Date()
	signYear, signMonth, signDay := signDateInUserTz.Date()

	return nowYear == signYear && nowMonth == signMonth && nowDay == signDay
}

// IsYesterday 判断签到记录是否为昨天（考虑时区）
func (s *SignRecord) IsYesterday(timezone string) bool {
	loc, err := util.ParseTimezone(timezone)
	if err != nil {
		return false
	}

	// 获取用户时区的"现在"时间
	now := util.GetCurrentTimeWithEnvByTimezone(timezone).In(loc)

	// 计算用户时区的"当天"日期
	yesterday := now.AddDate(0, 0, -1)

	// 将数据库中的UTC时间签到日期转换为用户时区
	signDateInUserTz := s.SignDate.In(loc)

	// 比较年月日
	yesterdayYear, yesterdayMonth, yesterdayDay := yesterday.Date()
	signYear, signMonth, signDay := signDateInUserTz.Date()

	return yesterdayYear == signYear && yesterdayMonth == signMonth && yesterdayDay == signDay
}
