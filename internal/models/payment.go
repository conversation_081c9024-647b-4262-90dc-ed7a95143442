package models

import (
	"github.com/airunny/wiki-go-tools/igorm"
	v1 "gold_store/api/gold_store/v1"
	"time"

	"gorm.io/gorm"
)

type Payment struct {
	gorm.Model
	PaymentNo   string                            `gorm:"column:payment_no" json:"payment_no"`
	OrderNo     string                            `gorm:"column:order_no" json:"order_no"`
	OperationNo string                            `gorm:"column:operation_no" json:"operation_no"`
	TotalAmount float32                           `gorm:"column:total_amount" json:"total_amount"`
	PaidTotal   float32                           `gorm:"column:paid_total" json:"paid_total"`
	PaidMethod  v1.PaymentMethod                  `gorm:"column:paid_method" json:"paid_method"`
	Status      v1.PaymentStatus                  `gorm:"column:status" json:"status"`
	PaidTime    time.Time                         `gorm:"column:paid_time" json:"paid_time"`
	Extra       *igorm.CustomValue[*PaymentExtra] `gorm:"column:extra" json:"extra"` // 扩展字段
}

func (Payment) TableName() string {
	return "payment"
}

type PaymentExtra struct {
	PayInfo interface{} `json:"pay_info"`
}
