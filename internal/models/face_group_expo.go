package models

import (
	"time"

	"gorm.io/gorm"
)

// FaceGroupExpo 人员库场景关联表-关联人员库与业务场景
type FaceGroupExpo struct {
	ID        uint64              `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                                                                  // 主键ID
	GroupID   string              `gorm:"column:group_id;size:64;not null;uniqueIndex:uk_group_scene" json:"group_id" validate:"required"`               // 人员库ID
	ExpoID    int64               `gorm:"column:expo_id;size:64;not null;uniqueIndex:uk_group_scene;index:idx_scene" json:"expo_id" validate:"required"` // 场景ID（如展会ID、活动ID）
	ExpoName  string              `gorm:"-" json:"scene_name,omitempty" validate:"max=100"`                                                              // 场景名称 (暂时忽略数据库映射)
	Status    FaceGroupExpoStatus `gorm:"column:status;not null;default:1;index" json:"status"`                                                          // 状态：1=正常 2=已删除
	CreatedAt time.Time           `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`                                        // 创建时间
	UpdatedAt time.Time           `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`                                        // 更新时间

	// 关联字段
	FaceGroup *FaceGroup `gorm:"foreignKey:GroupID;references:ID" json:"face_group,omitempty"` // 关联人员库
}

// TableName 指定表名
func (FaceGroupExpo) TableName() string {
	return "face_group_expo"
}

// BeforeCreate 创建前钩子
func (fgs *FaceGroupExpo) BeforeCreate(tx *gorm.DB) error {
	if fgs.Status == 0 {
		fgs.Status = FaceGroupExpoStatusNormal
	}
	return nil
}

// IsNormal 判断场景关联是否为正常状态
func (fgs *FaceGroupExpo) IsNormal() bool {
	return fgs.Status == FaceGroupExpoStatusNormal
}

// IsDeleted 判断场景关联是否已删除
func (fgs *FaceGroupExpo) IsDeleted() bool {
	return fgs.Status == FaceGroupExpoStatusDeleted
}

// CanDelete 判断场景关联是否可以删除
func (fgs *FaceGroupExpo) CanDelete() bool {
	return fgs.Status == FaceGroupExpoStatusNormal
}

// MarkDeleted 标记为已删除状态
func (fgs *FaceGroupExpo) MarkDeleted() {
	fgs.Status = FaceGroupExpoStatusDeleted
}

// GetStatusString 获取状态字符串表示
func (fgs *FaceGroupExpo) GetStatusString() string {
	return fgs.Status.String()
}

// UpdateExpoName 更新场景名称
func (fgs *FaceGroupExpo) UpdateExpoName(name string) {
	fgs.ExpoName = name
}
