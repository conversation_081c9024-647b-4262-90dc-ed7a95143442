package models

import (
	v1 "api-expo/api/expo/v1"
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoCommunity struct {
	gorm.Model
	ExpoId      int64                                   `gorm:"column:expo_id"`     // 展会ID
	Description string                                  `gorm:"column:description"` // 展会介绍
	TopicId     string                                  `gorm:"column:topic_id"`    // 话题ID
	Enable      bool                                    `gorm:"column:enable"`      // 是否启用
	Creator     string                                  `gorm:"column:creator"`     // 创建者
	Extra       *igorm.CustomValue[*ExpoCommunityExtra] `gorm:"column:extra"`
}

func (*ExpoCommunity) TableName() string {
	return "expo_community"
}

type ExpoCommunityExtra struct {
	Languages map[string]*v1.ExpoCommunityLanguage `json:"languages"`
}
