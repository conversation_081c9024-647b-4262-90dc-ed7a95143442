package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"
)

// RewardJSON 奖励信息JSON结构 本期只实现金币奖励
type RewardJSON struct {
	RewardType string `json:"reward_type"` // 奖励类型(coin金币)
	GoldCoins  int32  `json:"gold_coins"`  // 金币数量
}

// ConfigReward 签到奖励配置项
type ConfigReward struct {
	Day                  int         `json:"day"`                    // 天数
	Reward               *RewardJSON `json:"reward"`                 // 普通奖励，使用指针类型以便处理null值
	ConsecutiveReward    *RewardJSON `json:"consecutive_reward"`     // 连续签到奖励，可为null
	HasConsecutiveReward bool        `json:"has_consecutive_reward"` // 是否有连续签到奖励
}

// MarshalJSON 自定义JSON序列化，解决reward字段为null的情况
func (c ConfigReward) MarshalJSON() ([]byte, error) {
	type Alias ConfigReward

	// 确保Reward字段不为nil
	if c.Reward == nil {
		c.Reward = &RewardJSON{RewardType: "", GoldCoins: 0}
	}

	return json.Marshal(&struct {
		Alias
	}{
		Alias: Alias(c),
	})
}

// UnmarshalJSON 自定义JSON反序列化，处理reward字段为null的情况
func (c *ConfigReward) UnmarshalJSON(data []byte) error {
	type Alias ConfigReward
	aux := &struct {
		*Alias
	}{
		Alias: (*Alias)(c),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 如果reward为nil，设置一个默认值
	if c.Reward == nil {
		c.Reward = &RewardJSON{RewardType: "", GoldCoins: 0}
	}

	return nil
}

// ConfigValue 签到配置值
type ConfigValue struct {
	CycleDays int            `json:"cycle_days"` // 周期天数
	Rewards   []ConfigReward `json:"reward"`     // 奖励配置列表
}

// Value 实现driver.Valuer接口
func (c ConfigValue) Value() (driver.Value, error) {
	return json.Marshal(c)
}

// Scan 实现sql.Scanner接口，增强错误处理
func (c *ConfigValue) Scan(value interface{}) error {
	if value == nil {
		return errors.New("ConfigValue.Scan: scan value is nil")
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("ConfigValue.Scan: type assertion to []byte failed, got %T", value)
	}

	if len(bytes) == 0 {
		return errors.New("ConfigValue.Scan: empty JSON data")
	}

	err := json.Unmarshal(bytes, &c)
	if err != nil {
		return fmt.Errorf("ConfigValue.Scan: JSON unmarshal error: %w, data: %s", err, string(bytes))
	}

	// 验证反序列化后的数据
	if c.CycleDays <= 0 {
		return errors.New("ConfigValue.Scan: invalid cycle_days value")
	}

	if len(c.Rewards) == 0 {
		return errors.New("ConfigValue.Scan: empty rewards")
	}

	return nil
}

// SignConfig 任务配置表模型
type SignConfig struct {
	ID          uint        `gorm:"primaryKey;column:id;type:int unsigned;auto_increment" json:"id"`
	ConfigKey   string      `gorm:"column:config_key;type:varchar(50);not null" json:"config_key"`
	ConfigValue ConfigValue `gorm:"column:config_value;type:json;not null" json:"config_value"`
	Status      int8        `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`         // 1=生效，2=失效
	CycleDays   int8        `gorm:"column:cycle_days;type:tinyint;not null;default:7" json:"cycle_days"` // 周期天数
	UpdatedBy   string      `gorm:"column:updated_by;type:varchar(50);default:''" json:"updated_by"`     // 更新人
	CreatedAt   time.Time   `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   time.Time   `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 返回表名
func (SignConfig) TableName() string {
	return "sign_config"
}

// StatusEnum 配置状态枚举
const (
	ConfigStatusActive   int8 = 1 // 生效
	ConfigStatusInactive int8 = 2 // 失效
)

// 默认配置键
const (
	DefaultSignInConfigKey = "sign_in_config" // 默认签到配置键
)

// GetRewardByDay 根据天数获取奖励配置
func (c *SignConfig) GetRewardByDay(day int) *ConfigReward {
	for _, reward := range c.ConfigValue.Rewards {
		if reward.Day == day {
			return &reward
		}
	}
	return nil
}

// IsActive 判断配置是否有效
func (c *SignConfig) IsActive() bool {
	return c.Status == ConfigStatusActive
}
