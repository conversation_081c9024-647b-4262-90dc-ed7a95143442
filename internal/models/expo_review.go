package models

import (
	v1 "api-expo/api/expo/v1"
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoReview struct {
	gorm.Model
	ExpoId  int64                                `gorm:"column:expo_id"` // 展会ID
	Type    v1.ExpoReviewType                    `gorm:"column:type"`    // 类型：0:视频；1：图片
	URL     string                               `gorm:"column:url"`     // 视频地址；图片地址在extra中
	Cover   string                               `gorm:"column:cover"`   //  封面图片地址
	Enable  bool                                 `gorm:"column:enable"`  // 是否启用：0-不启用；1-启用
	Count   int64                                `gorm:"column:count"`   // 图片数量
	Creator string                               `gorm:"column:creator"` // 创建者
	Extra   *igorm.CustomValue[*ExpoReviewExtra] `gorm:"column:extra"`   // 扩展字段
}

func (*ExpoReview) TableName() string {
	return "expo_review"
}

type ExpoReviewExtra struct {
	Languages map[string]*v1.ExpoReviewLanguage `json:"languages"`
	Images    []string                          `json:"images"`
}
