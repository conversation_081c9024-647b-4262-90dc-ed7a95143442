package models

import "gorm.io/gorm"

type ExpoLive struct {
	gorm.Model
	ExpoId  int64  `gorm:"column:expo_id"`
	Url     string `gorm:"column:url"`     // 直播URL
	AppId   string `gorm:"column:app_id"`  // app_id
	RoomId  string `gorm:"column:room_id"` // 房间号
	UserId  string `gorm:"column:user_id"` // 用户ID
	Cover   string `gorm:"column:cover"`   // 封面
	Level   int32  `gorm:"column:level"`   // 直播等级
	Enable  bool   `gorm:"column:enable"`  // 是否启用：0-不启用；1-启用
	Creator string `gorm:"column:creator"` // 创建者
}

func (s *ExpoLive) TableName() string {
	return "expo_live"
}
