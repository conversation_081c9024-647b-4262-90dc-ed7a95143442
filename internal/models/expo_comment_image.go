// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameExpoCommentImage = "expo_comment_image"

// ExpoCommentImage 评价图片
type ExpoCommentImage struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	CommentID string    `gorm:"column:comment_id;not null;comment:评论ID" json:"comment_id"`                               // 评论ID
	ImageURL  string    `gorm:"column:image_url;not null;comment:图片地址" json:"image_url"`                                 // 图片地址
	Sort      int32     `gorm:"column:sort;not null;comment:排序" json:"sort"`                                             // 排序
	CreatedAt time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
	DeletedAt time.Time `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`                                        // 删除时间
}

// TableName ExpoCommentImage's table name
func (*ExpoCommentImage) TableName() string {
	return TableNameExpoCommentImage
}
