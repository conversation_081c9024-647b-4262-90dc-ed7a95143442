// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameExpoCommentTran = "expo_comment_trans"

// ExpoCommentTran 翻译表
type ExpoCommentTran struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	CommentID    string    `gorm:"column:comment_id;not null;comment:评论ID" json:"comment_id"`                               // 评论ID
	LanguageCode string    `gorm:"column:language_code;not null;comment:语言" json:"language_code"`                           // 语言
	Content      string    `gorm:"column:content;not null;comment:评论内容" json:"content"`                                     // 评论内容
	CreatedAt    time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
	TransUser      string    `gorm:"column:trans_user;not null;comment:翻译用户" json:"trans_user"`                                     // 翻译用户

}

// TableName ExpoCommentTran's table name
func (*ExpoCommentTran) TableName() string {
	return TableNameExpoCommentTran
}
