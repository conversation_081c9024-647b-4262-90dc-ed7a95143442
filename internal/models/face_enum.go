package models

// FaceGroupStatus 人员库状态枚举
type FaceGroupStatus int8

const (
	FaceGroupStatusNormal   FaceGroupStatus = 1 // 正常
	FaceGroupStatusDeleting FaceGroupStatus = 2 // 删除中
	FaceGroupStatusDeleted  FaceGroupStatus = 3 // 已删除
)

// String 返回人员库状态的字符串表示
func (s FaceGroupStatus) String() string {
	switch s {
	case FaceGroupStatusNormal:
		return "正常"
	case FaceGroupStatusDeleting:
		return "删除中"
	case FaceGroupStatusDeleted:
		return "已删除"
	default:
		return "未知状态"
	}
}

// FacePhotoStatus 人脸照片状态枚举
type FacePhotoStatus int8

const (
	FacePhotoStatusNormal  FacePhotoStatus = 1 // 正常
	FacePhotoStatusDeleted FacePhotoStatus = 2 // 已删除
)

// String 返回人脸照片状态的字符串表示
func (s FacePhotoStatus) String() string {
	switch s {
	case FacePhotoStatusNormal:
		return "正常"
	case FacePhotoStatusDeleted:
		return "已删除"
	default:
		return "未知状态"
	}
}

// FaceGender 人脸性别枚举
type FaceGender int8

const (
	FaceGenderUnknown FaceGender = 0 // 未知
	FaceGenderMale    FaceGender = 1 // 男
	FaceGenderFemale  FaceGender = 2 // 女
)

// String 返回人脸性别的字符串表示
func (g FaceGender) String() string {
	switch g {
	case FaceGenderMale:
		return "男"
	case FaceGenderFemale:
		return "女"
	default:
		return "未知"
	}
}

// FaceGroupExpoStatus 人员库场景关联状态枚举
type FaceGroupExpoStatus int8

const (
	FaceGroupExpoStatusNormal  FaceGroupExpoStatus = 1 // 正常
	FaceGroupExpoStatusDeleted FaceGroupExpoStatus = 2 // 已删除
)

// String 返回人员库场景关联状态的字符串表示
func (s FaceGroupExpoStatus) String() string {
	switch s {
	case FaceGroupExpoStatusNormal:
		return "正常"
	case FaceGroupExpoStatusDeleted:
		return "已删除"
	default:
		return "未知状态"
	}
}
