// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameExpoCommentLike = "expo_comment_like"

// ExpoCommentLike 评价点赞表
type ExpoCommentLike struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	CommentID string    `gorm:"column:comment_id;not null;comment:评论ID" json:"comment_id"`      // 评论ID
	UserID    string    `gorm:"column:user_id;not null" json:"user_id"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
}

// TableName ExpoCommentLike's table name
func (*ExpoCommentLike) TableName() string {
	return TableNameExpoCommentLike
}
