package models

import (
	v1 "api-expo/api/expo/v1"
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type Guest struct {
	gorm.Model
	Avatar        string                          `gorm:"column:avatar"`          // 嘉宾头像
	Name          string                          `gorm:"column:name"`            // 嘉宾名称
	UserId        string                          `gorm:"column:user_id"`         // 用户ID
	WikiNumber    string                          `gorm:"column:wiki_number"`     // Wiki ID
	PhoneAreaCode string                          `gorm:"column:phone_area_code"` // 手机号区号
	Phone         string                          `gorm:"column:phone"`           // 手机号
	Email         string                          `gorm:"column:email"`           // 邮箱
	Wechat        string                          `gorm:"column:wechat"`          // 微信
	WhatsApp      string                          `gorm:"column:whats_app"`       // WhatsApp号
	Facebook      string                          `gorm:"column:facebook"`        // facebook
	Twitter       string                          `gorm:"column:twitter"`         // twitter
	Linkedin      string                          `gorm:"column:linkedin"`        // linkedin
	Instagram     string                          `gorm:"column:instagram"`       // Instagram
	Reddit        string                          `gorm:"column:reddit"`          // Reddit
	Youtube       string                          `gorm:"column:youtube"`         // Youtube
	Telegram      string                          `gorm:"column:telegram"`        // Telegram
	TikTok        string                          `gorm:"column:tiktok"`          // tiktok
	IsRegister    bool                            `gorm:"column:is_register"`     // 是否注册
	Enable        bool                            `gorm:"column:enable"`          // 是否启用
	Creator       string                          `gorm:"column:creator"`         // 创建者
	Extra         *igorm.CustomValue[*GuestExtra] `gorm:"column:extra"`           // 扩展字段
	CountryFlag   string                          `gorm:"column:country_flag"`    // 国家国旗
}

func (s *Guest) TableName() string {
	return "guest"
}

type GuestExtra struct {
	Languages map[string]*v1.GuestLanguage `json:"languages"`
}

type ExpoItem struct {
	Id   int64  `json:"id"`
	Logo string `json:"logo"`
	Name string `json:"name"`
}
