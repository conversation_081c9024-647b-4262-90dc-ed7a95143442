package models

import (
	v1 "api-expo/api/expo/v1"

	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoHall struct {
	gorm.Model
	ExpoId  int64                             `gorm:"column:expo_id"` // 展会ID
	Type    v1.ExpoHallType                   `gorm:"column:type"`    // 会场类型：0-主会场;1-分会场
	Enable  bool                              `gorm:"column:enable"`  // 是否启用：0-不启用；1-启用
	Creator string                            `gorm:"column:creator"` // 创建者
	Extra   *igorm.CustomValue[ExpoHallExtra] `gorm:"column:extra"`   // 扩展字段
	Name    string                            `gorm:"column:name"`    // 名称
}

func (s *ExpoHall) TableName() string {
	return "expo_hall"
}

type ExpoHallExtra struct {
	Languages map[string]*v1.ExpoHallLanguage `json:"languages"`
}
