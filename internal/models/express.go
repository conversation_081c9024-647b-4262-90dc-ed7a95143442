package models

import (
	"time"

	v1 "gold_store/api/gold_store/v1"

	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

// ExpressQueryLog 快递查询日志
type ExpressQueryLog struct {
	gorm.Model
	TrackingNo  string                                    `gorm:"column:tracking_no"`  // 快递单号
	CarrierCode string                                    `gorm:"column:carrier_code"` // 快递公司编码
	CarrierName string                                    `gorm:"column:carrier_name"` // 快递公司名称
	Status      v1.LogisticStepStatus                     `gorm:"column:status"`       // 快递状态
	QueryTime   time.Time                                 `gorm:"column:query_time"`   // 查询时间
	LastUpdate  time.Time                                 `gorm:"column:last_update"`  // 最后更新时间
	IsCompleted bool                                      `gorm:"column:is_completed"` // 是否已完成
	RawResponse *igorm.CustomValue[*ExpressQueryResponse] `gorm:"column:raw_response"` // 原始响应数据
	Phone       string                                    `gorm:"column:phone"`        // 查询时使用的手机号
}

func (ExpressQueryLog) TableName() string {
	return "express_query_log"
}

// ExpressQueryResponse 快递查询原始响应
type ExpressQueryResponse struct {
	Message   string                `json:"message"`   // 消息
	Nu        string                `json:"nu"`        // 单号
	IsCheck   string                `json:"ischeck"`   // 是否签收标记
	Condition string                `json:"condition"` // 快递单当前状态
	Com       string                `json:"com"`       // 快递公司编码
	Status    string                `json:"status"`    // 查询结果状态
	State     string                `json:"state"`     // 快递单当前签收状态
	Data      []ExpressDataResponse `json:"data"`      // 物流信息
	IsLoop    bool                  `json:"isLoop"`    // 是否循环查询
}

// ExpressDataResponse 物流信息响应
type ExpressDataResponse struct {
	Time     string `json:"time"`     // 时间
	Ftime    string `json:"ftime"`    // 格式化时间
	Context  string `json:"context"`  // 物流信息描述
	Location string `json:"location"` // 地点
	Status   string `json:"status"`   // 状态
	AreaCode string `json:"areaCode"` // 区域编码
	AreaName string `json:"areaName"` // 区域名称
}

// ExpressRouteInfo 快递路由信息
type ExpressRouteInfo struct {
	From []ExpressAreaInfo `json:"from"` // 出发地
	Cur  []ExpressAreaInfo `json:"cur"`  // 当前地点
	To   []ExpressAreaInfo `json:"to"`   // 目的地
}

// ExpressAreaInfo 快递区域信息
type ExpressAreaInfo struct {
	Number   string  `json:"number"`   // 区域编号
	Name     string  `json:"name"`     // 区域名称
	Location string  `json:"location"` // 位置
	Lat      float64 `json:"lat"`      // 纬度
	Lng      float64 `json:"lng"`      // 经度
}
