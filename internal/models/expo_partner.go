package models

import (
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoPartner struct {
	gorm.Model
	ExpoId  int64                                 `gorm:"column:expo_id"` // 展会ID
	Name    string                                `gorm:"column:name"`    // 名称
	Logo    string                                `gorm:"column:logo"`    // logo
	Website string                                `gorm:"column:website"` // 网站
	Enable  bool                                  `gorm:"column:enable"`  // 是否启用：0-不启用；1-启用
	Type    int                                   `gorm:"column:type"`    // 类型：0:区块链和数字资产合作伙伴;1:WEB3.0、METAVERSE和游戏合作伙伴;2:金融科技、人工智能、外汇和支付合作伙伴;3:VCS,金融服务合作伙伴;4:社区合作伙伴(加密货币和外汇',
	Rank    int                                   `gorm:"column:rank"`    // 排序
	Creator string                                `gorm:"column:creator"` // 创建者
	Extra   *igorm.CustomValue[*ExpoPartnerExtra] `gorm:"column:extra"`   // 扩展字段
}

func (*ExpoPartner) TableName() string {
	return "expo_partner"
}

type ExpoPartnerExtra struct {
}
