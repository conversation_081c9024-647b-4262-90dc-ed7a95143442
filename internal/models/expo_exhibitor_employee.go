package models

import (
	"gorm.io/gorm"
)

type ExpoExhibitorEmployeeType int

const (
	ExpoExhibitorEmployeeTypeEmployee = 0 // 员工
	ExpoExhibitorEmployeeTypeMember   = 1 // 团队成员
)

type ExpoExhibitorEmployee struct {
	gorm.Model
	ExpoId      int64                     `gorm:"column:expo_id"`
	ExhibitorId int64                     `gorm:"column:exhibitor_id"` // 展商ID(expo_exhibitor主键)
	EmployeeId  int64                     `gorm:"column:employee_id"`  // 员工ID(票夹ID)
	Type        ExpoExhibitorEmployeeType `gorm:"column:type"`         // 员工类型：0-报名员工；1-团队成员
	UserId      string                    `gorm:"column:user_id"`      // 团队成员用户ID
	Enable      bool                      `gorm:"column:enable"`       // 是否启用：0-不启用；1-启用
}

func (*ExpoExhibitorEmployee) TableName() string {
	return "expo_exhibitor_employee"
}
