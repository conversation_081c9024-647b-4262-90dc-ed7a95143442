// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"database/sql"
	"gorm.io/gorm"
	"time"
)

const TableNameExpoComment = "expo_comment"

// ExpoComment 展会评论
type ExpoComment struct {
	ID            string    `gorm:"column:id;primaryKey;comment:主键ID" json:"id"`         // 主键ID
	ExpoID        int64     `gorm:"column:expo_id;not null;comment:展会ID" json:"expo_id"` // 展会ID
	UserID        string    `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`
	Status        int32     `gorm:"column:status;not null;comment:Status" json:"100 未审核 200 通过 401 拒绝"`                  // 展会ID
	Content       string    `gorm:"column:content;not null;comment:评论内容" json:"content"`                                 // 评论内容
	ContentType   int32     `gorm:"column:content_type;not null;comment:评价类型 1普通 2绑定实盘 3固定评价 4预约演讲" json:"content_type"` // 评价类型 1普通 2绑定实盘 3固定评价 4预约演讲
	ContentDesc   string    `gorm:"column:content_desc;not null;default:0;comment:补充字段" json:"content_desc"`             // 补充字段
	RootID        string    `gorm:"column:root_id;not null;comment:根Id" json:"root_id"`                                  // 根Id
	ParentID      string    `gorm:"column:parent_id;not null;comment:父id" json:"parent_id"`                              // 父id
	NickName      string    `gorm:"column:nickname;not null;comment:发帖昵称" json:"nickname"`
	ReplyNickName string    `gorm:"column:reply_nickname;not null;comment:回复了谁的用户昵称" json:"reply_nickname"`                  //
	LanguageCode  string    `gorm:"column:language_code;not null;comment:语言" json:"language_code"`                           // 语言
	CountryCode  string    `gorm:"column:country_code;not null;comment:国家Code" json:"country_code"`                           // 国家Code
	Ip  string    `gorm:"column:ip;not null;comment:ip" json:"ip"`                           // ip
	CreatedAt     time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
	LikeCount     int32     `gorm:"column:like_count;not null;comment:点赞总数" json:"like_count"`                               // 点赞总数
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`                                        // 删除时间
	AuditAt     sql.NullTime `gorm:"column:audit_at;comment:审核时间" json:"audit_at"`                                        // 审核时间
	AuditName  string    `gorm:"column:audit_name;not null;comment:审核人" json:"audit_name"`                           // 审核人
	UpdateName  string    `gorm:"column:update_name;not null;comment:修改人" json:"update_name"`                           // 修改人
	RefuseReason  string    `gorm:"column:refuse_reason;not null;comment:拒绝理由" json:"refuse_reason"`                           // 修改人


}
type ExpoCommentView struct {
	RootID string `gorm:"column:root_id;not null;comment:根Id" json:"root_id"`
	Count  int32  `gorm:"column:count;not null;comment:总数" json:"count"`
}

// TableName ExpoComment's table name
func (*ExpoComment) TableName() string {
	return TableNameExpoComment
}
