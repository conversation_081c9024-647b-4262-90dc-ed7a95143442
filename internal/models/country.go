package models

type Country struct {
	CountryCode string `gorm:"column:CountryCode"`
	IsEnabled   bool   `gorm:"column:IsEnabled"`
}

func (Country) TableName() string {
	return "fxeyeinternational_country"
}

type CountryTranslation struct {
	CountryCode  string `gorm:"column:CountryCode"`
	LanguageCode string `gorm:"column:LanguageCode"`
	CountryName  string `gorm:"column:CountryName"`
	IsEnabled    bool   `gorm:"column:IsEnabled"`
}

func (CountryTranslation) TableName() string {
	return "fxeyeinternational_gj_countrytranslate"
}
