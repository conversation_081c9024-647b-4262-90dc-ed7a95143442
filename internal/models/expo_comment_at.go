// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameExpoCommentAt = "expo_comment_at"

// ExpoCommentAt 评价@表
type ExpoCommentAt struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"` // 主键ID
	CommentID string    `gorm:"column:comment_id;not null;comment:评论ID" json:"comment_id"`      // 评论ID
	UserID    string    `gorm:"column:user_id;not null" json:"user_id"`
	Nickname  string    `gorm:"column:nickname;not null" json:"nickname"`
	CreatedAt time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
	DeletedAt time.Time `gorm:"column:deleted_at;comment:删除时间" json:"deleted_at"`                                        // 删除时间
}

// TableName ExpoCommentAt's table name
func (*ExpoCommentAt) TableName() string {
	return TableNameExpoCommentAt
}
