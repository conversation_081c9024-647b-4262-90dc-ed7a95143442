package models

import (
	"github.com/airunny/wiki-go-tools/igorm"
	v1 "gold_store/api/gold_store/v1"

	"gorm.io/gorm"
)

/**
TODO 特此说明：为了兼容老订单和并；老订单的商品和总价是按照小数存储的，所以所有的价格都需要使用float
*/

type Goods struct {
	gorm.Model
	GoodsId       string                                            `gorm:"goods_id"`              // 商品ID
	Category      v1.GoodsCategory                                  `gorm:"column:category"`       // 商品分类
	Name          string                                            `gorm:"column:name"`           // 商品名称
	BasePrice     float32                                           `gorm:"column:base_price"`     // 基础价格
	UseBasePrice  bool                                              `gorm:"column:use_base_price"` // 是否使用基础价格
	SelectedPrice float32                                           `gorm:"column:selected_price"` // 选中的sku价格
	Description   string                                            `gorm:"column:description"`    // 商品描述
	Status        v1.GoodsStatus                                    `gorm:"column:status"`         // 商品状态
	FreeShipping  bool                                              `gorm:"column:free_shipping"`  // 是否免运费
	Image         *igorm.CustomValue[*v1.Image]                     `gorm:"column:image"`          // 商品主图
	Labels        *igorm.CustomValue[[]*GoodsLabel]                 `gorm:"column:labels"`         // 商品标签
	Carousels     *igorm.CustomValue[[]*v1.Image]                   `gorm:"column:carousels"`      // 商品轮播图
	Details       *igorm.CustomValue[[]*v1.Image]                   `gorm:"column:details"`        // 商品详情
	Specs         *igorm.CustomValue[[]*v1.GoodsSpec]               `gorm:"column:specs"`          // 商品规格
	Translate     *igorm.CustomValue[map[string]*v1.GoodsTranslate] `gorm:"column:translate"`      // 多语言
	Extra         *igorm.CustomValue[*GoodsExtra]                   `gorm:"column:extra"`          // 扩展字段
}

func (Goods) TableName() string {
	return "goods"
}

type GoodsLabel struct {
	Id string `json:"id"`
}

type GoodsExtra struct{}

type GoodsSnapshot struct {
	gorm.Model
	GoodsId  string                        `gorm:"column:goods_id" json:"goods_id"`
	Version  int64                         `gorm:"column:version" json:"version"`
	Snapshot *igorm.CustomValue[*Snapshot] `gorm:"column:snapshot" json:"snapshot"`
}

func (GoodsSnapshot) TableName() string {
	return "goods_snapshot"
}

type Snapshot struct {
	Goods *Goods `json:"goods"`
	Skus  []*SKU `json:"skus"`
}

type GoodsStatistics struct {
	gorm.Model
	GoodsId      string         `gorm:"goods_id"`             // 商品ID
	Status       v1.GoodsStatus `gorm:"column:status"`        // 商品状态
	Sales        int32          `gorm:"column:sales"`         // 实际销量
	VirtualSales int32          `gorm:"column:virtual_sales"` // 虚拟销量
	TotalSales   int32          `gorm:"column:total_sales"`   // 总销量
	Views        int32          `gorm:"column:views"`         // 浏览数
}

func (GoodsStatistics) TableName() string {
	return "goods_statistics"
}
