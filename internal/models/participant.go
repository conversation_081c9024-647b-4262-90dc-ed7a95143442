package models

import (
	v1 "api-expo/api/expo/v1"
	"time"
)

type Participant struct {
	ID                uint                 `gorm:"column:id"`
	UserId            string               `gorm:"column:user_id"`            // 用户ID
	ExpoId            int64                `gorm:"column:expo_id"`            // 展会ID
	WikiNumber        string               `gorm:"column:wiki_number"`        // 天眼号
	FirstName         string               `gorm:"column:first_name"`         // 名
	LastName          string               `gorm:"column:last_name"`          // 姓
	Phone             string               `gorm:"column:phone"`              // 手机号
	AreaCode          string               `gorm:"column:area_code"`          // 手机区域
	Email             string               `gorm:"column:email"`              // 邮件
	IdentityCode      v1.SubIdentity       `gorm:"column:identity_code"`      // 身份编码1:交易商,2:投资者,3:服务商,4:KOL
	IndustryCode      v1.Industry          `gorm:"column:industry_code"`      // 行业编码1:Stock,2:Forex,3:Crypto,4:Fintech
	Product           int                  `gorm:"column:product"`            // 产品
	CompanyName       string               `gorm:"column:company_name"`       // 公司名称
	Position          string               `gorm:"column:position"`           // 职位
	Recommender       string               `gorm:"column:recommender"`        // 推荐人
	Channel           string               `gorm:"column:channel"`            // 渠道
	Mode              int                  `gorm:"column:mode"`               // 报名方式，1自主报名，2后台发放，3现场补录
	TicketType        int                  `gorm:"column:ticket_type"`        // 门票类型,1:普通,2:VIP,3:SVIP
	TicketStatus      int                  `gorm:"column:ticket_status"`      // 门票状态,1:待领取,2:已领取,3:已入场,4:已过期
	CreatedAt         time.Time            `gorm:"column:created_at"`         // 创建时间
	Checker           string               `gorm:"column:checkor"`            // 核销人
	CheckedAt         time.Time            `gorm:"column:checked_at"`         // 核销时间
	InvitedAt         time.Time            `gorm:"column:invited_at"`         // 邀约时间
	Inviter           string               `gorm:"column:inviter"`            // 邀约人
	InvitedStatus     int                  `gorm:"column:invited_status"`     // 邀约状态,1:暂不确定,2:确认到场,3:不参会,4暂未联系
	Recorder          string               `gorm:"column:recorder"`           // 补录人
	RecordedAt        time.Time            `gorm:"column:recorded_at"`        // 补录时间
	Creator           string               `gorm:"column:creator"`            // 创建人
	ModifiedAt        time.Time            `gorm:"column:modified_at"`        // 修改时间
	Modifier          string               `gorm:"column:modifier"`           // 修改人
	ShowMedal         int                  `gorm:"column:show_medal"`         // 是否展示勋章1：展示0：不展示
	Code              string               `gorm:"column:code"`               //
	HomePop           int                  `gorm:"column:home_pop"`           // app 首页弹窗
	EmailStatus       int                  `gorm:"column:email_status"`       // 邮件发送状态
	SubIdentityCode   v1.SubIdentity       `gorm:"column:sub_identity_code"`  // 30001:Service Provider（Press/Media/Cloud/Bank/Wealth management/CRM）\r\n30002:Fintech（Payment/Al/Liquidity/Trading platform）\r\n30003:Crypto / Digital Assets"\r\n30004:IB / Affiliate\r\n30005:Investor / VC\r\n30006:Trader\r\n30007:Other\r\n10001:forex Broker\r\n40001:KOL
	CheckMode         int                  `gorm:"column:check_mode"`         // 0：未核销，1：邮件二维码核销，2：app二维码核销，3：后台发放，4：现场补录
	CooperationMethod int                  `gorm:"column:cooperation_method"` // 合作方式
	CheckDevice       int                  `gorm:"column:check_device"`       // 核销设备，1：扫描枪，2：管理后台扫码，3：后台，4现场补录
	OrderNo           string               `gorm:"column:order_no"`           // 订单号
	ApplyStatus       v1.ApplyAudit        `gorm:"column:apply_status"`       // 报名状态
	RefuseReason      v1.ApplyRefuseReason `gorm:"column:refuse_reason"`      // 拒绝理由
	ApplyUserid       string               `gorm:"column:apply_userid"`       // 报名人
	AuditAt           time.Time            `gorm:"column:audit_at"`           // 审核时间
	AuditUser         string               `gorm:"column:audit_user"`         //审核人
	IsResister        bool                 `gorm:"column:is_resister"`        // 是否注册 1:已注册 0:未注册
	IsCommentNotice   int                  `gorm:"column:is_comment_notice"`  //是否评论通知
	IsEmployee        int                  `gorm:"column:is_employee"`        // 是否是企业员工：0-否;1-是
	ForOther          bool                 `gorm:"column:for_other"`          // 是否提别人报名
	CountryCode       string               `gorm:"column:country_code"`       //国家Code
	Ip                string               `gorm:"column:ip"`                 //ip地址
	LanguageCode      string               `gorm:"column:language_code"`      //语言Code
}

// 定义ticket_status的常量
const (
	TicketStatusDel = 500 // 已过期

	//是否是企业员工
	IsEmployeeNo  = 0 // 否
	IsEmployeeYes = 1 // 是
)

func (s *Participant) TableName() string {
	return "participant"
}
