package models

import (
	v1 "gold_store/api/gold_store/v1"

	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type SKU struct {
	gorm.Model
	GoodsId      string                                 `gorm:"column:goods_id"`      // 商品ID
	SkuID        string                                 `gorm:"column:sku_id"`        // SKUid
	Specs        *igorm.CustomValue[[]*v1.GoodsSkuSpec] `gorm:"column:specs"`         // 商品规格
	Price        float32                                `gorm:"column:price"`         // 单价
	Stock        int32                                  `gorm:"column:stock"`         // 库存
	Sales        int32                                  `gorm:"column:sales"`         // 销量
	VirtualSales int32                                  `gorm:"column:virtual_sales"` // 虚拟销量
	Disable      bool                                   `gorm:"column:disable"`       // 状态：0：禁用；1：启用
}

func (SKU) TableName() string {
	return "sku"
}
