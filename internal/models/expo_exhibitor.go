package models

import (
	v1 "api-expo/api/expo/v1"
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoExhibitor struct {
	gorm.Model
	ExpoId        int64                                   `gorm:"column:expo_id"`         // 展会ID
	HallId        int64                                   `gorm:"column:hall_id"`         // 会场ID
	TraderCode    string                                  `gorm:"column:trader_code"`     // 交易商code
	TraderName    string                                  `gorm:"column:trader_name"`     // 交易商名称
	Logo          string                                  `gorm:"column:logo"`            // logo图片地址
	MinLogo       string                                  `gorm:"column:min_logo"`        // logo缩略图地址
	SponsorLevel  v1.SponsorLevel                         `gorm:"column:sponsor_level"`   // 赞助等级：0-未赞助；1-白银；2-黄金；3-铂金；4-钻石；5-全球
	ExhibitorType v1.ExhibitorType                        `gorm:"column:exhibitor_type"`  // 参展商类型：0；1-交易商；3-服务商
	BoothLength   string                                  `gorm:"column:booth_length"`    // 展位长度
	BoothWidth    string                                  `gorm:"column:booth_width"`     // 展位宽度
	BoothHeight   string                                  `gorm:"column:booth_height"`    // 展位高度
	Booth         string                                  `gorm:"column:booth"`           // 展位
	Contact       string                                  `gorm:"column:contact"`         // 联系人
	PhoneAreaCode string                                  `gorm:"column:phone_area_code"` // 电话区域
	Phone         string                                  `gorm:"column:phone"`           // 电话
	Email         string                                  `gorm:"column:email"`           // 邮箱
	Rank          int32                                   `gorm:"column:rank"`            // 排序
	Enable        bool                                    `gorm:"column:enable"`          // 是否启用：0-不启用；1-启用
	Creator       string                                  `gorm:"column:creator"`         // 创建者
	Extra         *igorm.CustomValue[*ExpoExhibitorExtra] `gorm:"column:extra"`           // 额外信息
}

func (*ExpoExhibitor) TableName() string {
	return "expo_exhibitor"
}

type ExpoExhibitorExtra struct {
	Languages map[string]*v1.ExpoExhibitorLanguage `json:"languages"`
}
