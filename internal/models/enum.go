package models

// TaskType 任务类型枚举
type TaskType int8

const (
	TaskTypeNewComer TaskType = 1 // 新手任务
	TaskTypeDaily    TaskType = 2 // 日常任务
	// 3-99 预留
)

// String 返回任务类型的字符串表示
func (t TaskType) String() string {
	switch t {
	case TaskTypeNewComer:
		return "新手任务"
	case TaskTypeDaily:
		return "日常任务"
	default:
		return "未知任务类型"
	}
}

// RewardType 奖励类型枚举
type RewardType int8

const (
	RewardTypeGoldCoin RewardType = 1 // 积分
	RewardTypePhysical RewardType = 2 // 实物
	RewardTypeVirtual  RewardType = 3 // 虚拟商品
)

// String 返回奖励类型的字符串表示
func (r RewardType) String() string {
	switch r {
	case RewardTypeGoldCoin:
		return "金币"
	case RewardTypePhysical:
		return "实物"
	case RewardTypeVirtual:
		return "虚拟商品"
	default:
		return "未知奖励类型"
	}
}

// UserProgressStatus 任务状态枚举
type UserProgressStatus int8

const (
	UserProgressStatusNone      UserProgressStatus = 0 // 未开始
	UserProgressStatusOngoing   UserProgressStatus = 1 // 进行中
	UserProgressStatusCompleted UserProgressStatus = 2 // 已完成未领取
	UserProgressStatusReceived  UserProgressStatus = 3 // 已完成已领取
	UserProgressStatusExpired   UserProgressStatus = 4 // 已过期
)

// String 返回任务状态的字符串表示
func (s UserProgressStatus) String() string {
	switch s {
	case UserProgressStatusNone:
		return "未开始"
	case UserProgressStatusOngoing:
		return "领取任务进行中"
	case UserProgressStatusCompleted:
		return "已完成未领取"
	case UserProgressStatusReceived:
		return "已完成已领取"
	case UserProgressStatusExpired:
		return "已过期"
	default:
		return "未知状态"
	}
}

// 奖励发放状态枚举
type TaskRewardIssueStatus int8

const (
	TaskRewardIssuePending TaskRewardIssueStatus = 0 // 待领取
	TaskRewardIssueIssued  TaskRewardIssueStatus = 1 // 已发放
	TaskRewardIssueExpired TaskRewardIssueStatus = 2 // 已过期
	TaskRewardIssueIssuing TaskRewardIssueStatus = 3 // 发放中
	TaskRewardIssueFailed  TaskRewardIssueStatus = 4 // 发放失败
)

// String 返回奖励发放状态的字符串表示
func (s TaskRewardIssueStatus) String() string {
	switch s {
	case TaskRewardIssuePending:
		return "待领取"
	case TaskRewardIssueIssued:
		return "已发放"
	case TaskRewardIssueExpired:
		return "已过期"
	case TaskRewardIssueIssuing:
		return "发放中"
	case TaskRewardIssueFailed:
		return "发放失败"
	default:
		return "未知状态"
	}
}

// 具体任务类型枚举
type TaskSubType string

const (
	// 日常任务
	TaskSubTypePostMoment   TaskSubType = "post_moment"
	TaskSubTypePostBusiness TaskSubType = "post_business"
	TaskSubTypeCommentPost  TaskSubType = "comment_post"
	TaskSubTypeRateDealer   TaskSubType = "rate_dealer"
	TaskSubTypeJoinActivity TaskSubType = "join_activity"
	// 新手任务
	TaskSubTypeModifyUsername  TaskSubType = "modify_username"
	TaskSubTypeModifyAvatar    TaskSubType = "modify_avatar"
	TaskSubTypeVerifyIdentity  TaskSubType = "verify_identity"
	TaskSubTypeBindRealAccount TaskSubType = "bind_real_account"
	TaskSubTypeOpenVPS         TaskSubType = "open_vps"
	TaskSubTypeFollowWikiFX    TaskSubType = "follow_wikifx"
	TaskSubTypeViewDealer      TaskSubType = "view_dealer"
	TaskSubTypeSearch          TaskSubType = "search"
)

// GetTaskCondition 返回具体任务类型的字符串表示
func (t TaskSubType) GetTaskCondition() string {
	switch t {
	// 日常任务
	case TaskSubTypePostMoment:
		return "发布动态"
	case TaskSubTypePostBusiness:
		return "发布商业"
	case TaskSubTypeCommentPost:
		return "评价商业或动态"
	case TaskSubTypeRateDealer:
		return "评价交易商"
	case TaskSubTypeJoinActivity:
		return "参与活动"
	// 新手任务
	case TaskSubTypeModifyUsername:
		return "修改用户名称"
	case TaskSubTypeModifyAvatar:
		return "修改用户图像"
	case TaskSubTypeVerifyIdentity:
		return "实名认证"
	case TaskSubTypeBindRealAccount:
		return "绑定实盘"
	case TaskSubTypeOpenVPS:
		return "开通VPS"
	case TaskSubTypeFollowWikiFX:
		return "关注用户"
	case TaskSubTypeViewDealer:
		return "浏览交易商详情页"
	case TaskSubTypeSearch:
		return "搜索"
	default:
		return "未知任务子类型"
	}
}

// GetTaskDesc 获取任务的完成条件描述
func (t TaskSubType) GetTaskDesc() string {
	switch t {
	case TaskSubTypePostMoment:
		return "动态审核通过"
	case TaskSubTypePostBusiness:
		return "商业审核通过"
	case TaskSubTypeCommentPost:
		return "评价通过"
	case TaskSubTypeRateDealer:
		return "评价审核通过"
	case TaskSubTypeJoinActivity:
		return "发布带活动话题的动态，审核通过"
	case TaskSubTypeModifyUsername:
		return "修改通过"
	case TaskSubTypeModifyAvatar:
		return "修改通过"
	case TaskSubTypeVerifyIdentity:
		return "通过"
	case TaskSubTypeBindRealAccount:
		return "绑定成功，且为wikifx中没有的实盘"
	case TaskSubTypeOpenVPS:
		return "开通成功"
	case TaskSubTypeFollowWikiFX:
		return "关注成功"
	case TaskSubTypeViewDealer:
		return "进入交易商页面浏览时长超过{x}秒"
	case TaskSubTypeSearch:
		return "进入搜索结果页"
	default:
		return ""
	}
}

// GetTaskEnumCodeByTitle 根据任务标题获取对应的任务枚举代码
func GetTaskEnumCodeByTitle(title string) TaskSubType {
	titleMap := map[string]TaskSubType{
		"发布动态":     TaskSubTypePostMoment,
		"发布商业":     TaskSubTypePostBusiness,
		"评价商业或动态":  TaskSubTypeCommentPost,
		"评价交易商":    TaskSubTypeRateDealer,
		"参与活动":     TaskSubTypeJoinActivity,
		"修改用户名称":   TaskSubTypeModifyUsername,
		"修改用户图像":   TaskSubTypeModifyAvatar,
		"实名认证":     TaskSubTypeVerifyIdentity,
		"绑定实盘":     TaskSubTypeBindRealAccount,
		"开通VPS":    TaskSubTypeOpenVPS,
		"关注用户":     TaskSubTypeFollowWikiFX,
		"浏览交易商详情页": TaskSubTypeViewDealer,
		"搜索":       TaskSubTypeSearch,
	}

	// 尝试直接匹配
	if enumCode, ok := titleMap[title]; ok {
		return enumCode
	}

	// 尝试匹配子字符串
	for t, code := range titleMap {
		if title == t || title == string(code) {
			return code
		}
	}

	// 未匹配到，返回空字符串
	return ""
}

var AllTaskSubTypes = []TaskSubType{
	TaskSubTypePostMoment,
	TaskSubTypePostBusiness,
	TaskSubTypeCommentPost,
	TaskSubTypeRateDealer,
	TaskSubTypeJoinActivity,
	TaskSubTypeModifyUsername,
	TaskSubTypeModifyAvatar,
	TaskSubTypeVerifyIdentity,
	TaskSubTypeBindRealAccount,
	TaskSubTypeOpenVPS,
	TaskSubTypeFollowWikiFX,
	TaskSubTypeViewDealer,
	TaskSubTypeSearch,
}

// 任务信息结构体
type TaskInfo struct {
	SubType     TaskSubType `json:"sub_type"`
	Description string      `json:"description"`
	Condition   string      `json:"condition"`
}

// ToTaskInfo 枚举转任务信息
func (t TaskSubType) ToTaskInfo() TaskInfo {
	return TaskInfo{
		SubType:     t,
		Description: t.GetTaskDesc(),
		Condition:   t.GetTaskCondition(),
	}
}

// AllTaskInfoList 获取所有任务信息
func AllTaskInfoList() []TaskInfo {
	infos := make([]TaskInfo, 0, len(AllTaskSubTypes))
	for _, t := range AllTaskSubTypes {
		infos = append(infos, t.ToTaskInfo())
	}
	return infos
}

// AllTaskInfoMap 获取所有任务信息的映射
func AllTaskInfoMap() map[TaskSubType]TaskInfo {
	m := make(map[TaskSubType]TaskInfo, len(AllTaskSubTypes))
	for _, t := range AllTaskSubTypes {
		m[t] = t.ToTaskInfo()
	}
	return m
}
