package models

import (
	"time"

	v1 "api-expo/api/expo/v1"

	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoSchedule struct {
	gorm.Model
	ExpoId  int64                                  `gorm:"column:expo_id"` // 展会ID
	HallId  int64                                  `gorm:"column:hall_id"` // 会场ID
	Type    v1.ScheduleType                        `gorm:"column:type"`    // 类型
	Theme   string                                 `gorm:"column:theme"`   // 演讲主题
	HostId  int64                                  `gorm:"column:host_id"` // 主持人ID(guest表主键)
	Date    string                                 `gorm:"column:date"`    // 演讲日期(2025-07-07)
	Start   time.Time                              `gorm:"column:start"`   // 演讲开始时间
	End     time.Time                              `gorm:"column:end"`     // 演讲结束时间
	Enable  bool                                   `gorm:"column:enable"`  // 是否启用：0-不启用；1-启用
	Creator string                                 `gorm:"column:creator"` // 创建者
	Extra   *igorm.CustomValue[*ExpoScheduleExtra] `gorm:"column:extra"`   // 扩展字段
}

func (s *ExpoSchedule) TableName() string {
	return "expo_schedule"
}

func (s *ExpoSchedule) GetTypeName(languageCode string) string {
	switch s.Type {
	case v1.ScheduleType_ScheduleType_Lecture: // 围炉谈话
		return i18n.GetWithDefaultEnglish("63198", languageCode) //  围炉谈话
	case v1.ScheduleType_ScheduleType_RoundTable:
		return i18n.GetWithDefaultEnglish("63199", languageCode) // 圆桌会议
	default:
		return i18n.GetWithDefaultEnglish("63197", languageCode) // 主题演讲
	}
}

type ExpoScheduleExtra struct {
	Languages map[string]*v1.ExpoScheduleLanguage `json:"languages"`
}
