package models

import "time"

type Expo struct {
	Id            int64     `gorm:"column:id"`
	Name          string    `gorm:"column:name"`           // 展会名称
	Location      string    `gorm:"column:location"`       // 展会地点
	CountryCode   string    `gorm:"column:country_code"`   // 展会国家
	Status        int       `gorm:"column:status"`         // 展会状态，0：未开始，1：进行中，2：已结束
	Type          int       `gorm:"column:type"`           // 展会类型，0：展会，1：酒会
	SignURL       string    `gorm:"column:signUpUrl"`      //
	Start         time.Time `gorm:"column:start"`          // 展会开始时间
	End           time.Time `gorm:"column:end"`            // 展会结束时间
	Award         int       `gorm:"column:award"`          // 展会奖品
	Price         float32   `gorm:"column:price"`          // 展会门票价格
	PriceUnit     int       `gorm:"column:price_unit"`     // 展会门票单位
	Langs         string    `gorm:"column:langs"`          // 语言设置
	Longitude     string    `gorm:"column:longitude"`      // 经度
	Latitude      string    `gorm:"column:latitude"`       // 纬度
	CreatedAt     string    `gorm:"column:created_at"`     // 创建时间
	Creator       string    `gorm:"column:creator"`        // 创建人
	ModifiedAt    string    `gorm:"column:modified_at"`    // 修改时间
	Modifier      string    `gorm:"column:modifier"`       // 修改人
	LocationName  string    `gorm:"column:location_name"`  // 展会显示名称
	Current       int       `gorm:"column:current"`        // 当前直播展会0：不是，1：是
	HomeLongitude string    `gorm:"column:home_longitude"` // 国内经度
	HomeLatitude  string    `gorm:"column:home_latitude"`  // 国家纬度
	TimeZone      string    `gorm:"column:time_zone"`      // 时区
	TicketType    int       `gorm:"column:ticket_type"`    // 展会门票类型
	SendTicket    int       `gorm:"column:send_ticket"`    // 1:人工发放,2:自动发放
}

func (Expo) TableName() string {
	return "expo"
}

type ExpoLang struct {
	WebCover           string `json:"webCover"`
	AppCover           string `json:"appCover"`
	WebBackgroundImage string `json:"webBackgroundImage"`
	AppBackgroundImage string `json:"appBackgroundImage"`
	WebShareBackground string `json:"webShareBackground"`
	AppShareBackground string `json:"appShareBackground"`
	Lang               string `json:"lang"`
	CustomerEmail      string `json:"customerEmail"`
	AdmissionNotice    string `json:"admissionNotice"`
}

type TicketType struct {
	Id         int64   `gorm:"column:id"`          // 主键
	ExpoId     int64   `gorm:"column:expo_id"`     // 展会编号
	Name       string  `gorm:"column:name"`        // 门票名称
	Price      float32 `gorm:"column:price"`       // 门票价格
	Currency   string  `gorm:"column:currency"`    // 转赠方式
	Desc       string  `gorm:"column:desc"`        // 门票描述
	CreatedAt  string  `gorm:"column:created_at"`  // 创建时间
	Creator    string  `gorm:"column:creator"`     // 创建人
	ModifiedAt string  `gorm:"column:modified_at"` // 修改时间
	Modifier   string  `gorm:"column:modifier"`    // 修改人
}

func (TicketType) TableName() string {
	return "ticket_type"
}

type Participant struct {
	Id                int64  `gorm:"column:id"`
	UserId            string `gorm:"column:user_id"`            // 用户ID
	ExpoId            int64  `gorm:"column:expo_id"`            // 展会ID
	WikiNumber        string `gorm:"column:wiki_number"`        // 天眼号
	FirstName         string `gorm:"column:first_name"`         // 名
	LastName          string `gorm:"column:last_name"`          // 姓
	Phone             string `gorm:"column:phone"`              // 手机号
	AreaCode          string `gorm:"column:area_code"`          // 手机区域
	Email             string `gorm:"column:email"`              // 邮件
	IdentityCode      int64  `gorm:"column:identity_code"`      // 身份编码1:交易商,2:投资者,3:服务商,4:KOL
	IndustryCode      int64  `gorm:"column:industry_code"`      // 行业编码1:Stock,2:Forex,3:Crypto,4:Fintech
	ProductCode       int64  `gorm:"column:product_code"`       // 产品
	CompanyName       string `gorm:"column:company_name"`       // 公司名称
	Position          string `gorm:"column:position"`           // 职位
	Recommender       string `gorm:"column:recommender"`        // 推荐人
	Channel           string `gorm:"column:channel"`            // 渠道
	Mode              int64  `gorm:"column:mode"`               // 报名方式，1自主报名，2后台发放，3现场补录
	TicketType        int64  `gorm:"column:ticket_type"`        // 门票类型,1:普通,2:VIP,3:SVIP
	TicketStatus      int64  `gorm:"column:ticket_status"`      // 门票状态,1:待领取,2:已领取,3:已过期
	CreatedAt         string `gorm:"column:created_at"`         // 创建时间
	Checkor           string `gorm:"column:checkor"`            // 核销人
	CheckedAt         string `gorm:"column:checked_at"`         // 核销时间
	InvitedAt         string `gorm:"column:invited_at"`         // 邀约时间
	Inviter           string `gorm:"column:inviter"`            // 邀约人
	InvitedStatus     int64  `gorm:"column:invited_status"`     // 邀约状态,1:暂不确定,2:确认到场,3:不参会,4暂未联系
	Recorder          string `gorm:"column:recorder"`           // 补录人
	RecordedAt        string `gorm:"column:recorded_at"`        // 补录时间
	Creator           string `gorm:"column:creator"`            // 创建人
	ModifiedAt        string `gorm:"column:modified_at"`        // 修改时间
	Modifier          string `gorm:"column:modifier"`           // 修改人
	ShowMedal         int64  `gorm:"column:show_medal"`         // 是否展示勋章1：展示0：不展示
	Code              string `gorm:"column:code"`               //
	HomePop           int64  `gorm:"column:home_pop"`           // app 首页弹窗
	EmailStatus       int64  `gorm:"column:email_status"`       // 邮件发送状态
	SubIdentityCode   int64  `gorm:"column:sub_identity_code"`  // 30001:Service Provider（Press/Media/Cloud/Bank/Wealth management/CRM）\r\n30002:Fintech（Payment/Al/Liquidity/Trading platform）\r\n30003:Crypto / Digital Assets"\r\n30004:IB / Affiliate\r\n30005:Investor / VC\r\n30006:Trader\r\n30007:Other\r\n10001:forex Broker\r\n40001:KOL
	CheckMode         int64  `gorm:"column:check_mode"`         // 0：未核销，1：邮件二维码核销，2：app二维码核销，3：后台发放，4：现场补录
	CooperationMethod int64  `gorm:"column:cooperation_method"` // 合作方式
	CheckDevice       int64  `gorm:"column:check_device"`       // 核销设备，1：扫描枪，2：管理后台扫码，3：后台，4现场补录
	OrderNo           string `gorm:"order_no"`                  // 订单号
}
