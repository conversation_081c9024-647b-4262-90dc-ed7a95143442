// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameGiftCardUserRemind = "gift_card_user_remind"

// GiftCardUserRemind 礼品卡提醒表
type GiftCardUserRemind struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	UserGiftCardID int64     `gorm:"column:user_gift_card_id;not null;comment:礼品卡Id" json:"user_gift_card_id"`                // 礼品卡Id
	UserID         string    `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                                     // 用户id
	CreatedAt      time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt      time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
}

// TableName GiftCardUserRemind's table name
func (*GiftCardUserRemind) TableName() string {
	return TableNameGiftCardUserRemind
}
