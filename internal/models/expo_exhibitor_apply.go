package models

import (
	v1 "api-expo/api/expo/v1"

	"gorm.io/gorm"
)

type ExpoExhibitorApply struct {
	gorm.Model
	Booth                 string                  `gorm:"column:booth"`                   // 展位大小
	Company               string                  `gorm:"column:company"`                 // 公司名称
	Website               string                  `gorm:"column:website"`                 // 官方网址
	Contact               string                  `gorm:"column:contact"`                 // 联系人
	PhoneAreaCode         string                  `gorm:"column:phone_area_code"`         // 手机号区号
	Phone                 string                  `gorm:"column:phone"`                   // 手机号
	Email                 string                  `gorm:"column:email"`                   // 邮箱
	Status                v1.ExhibitorApplyStatus `gorm:"column:status"`                  // 状态：0-未知状态;1-待审核;2-审核通过;3-审核未通过
	Reason                string                  `gorm:"column:reason"`                  // 审核未通过原因
	ClientIP              string                  `gorm:"column:client_ip"`               // 报名时的IP地址
	LanguageCode          string                  `gorm:"column:language_code"`           // 报名时的语言
	PreferredLanguageCode string                  `gorm:"column:preferred_language_code"` // 报名时的偏好语言
	CountryCode           string                  `gorm:"column:country_code"`            // 报名时的国家代码
	BasicData             string                  `gorm:"column:basic_data"`              // 报名时的基础数据
	ExpoId                int64                   `gorm:"column:expo_id"`                 //展会Id
	UserId                string                  `gorm:"column:user_id"`                 //提交人
}

func (*ExpoExhibitorApply) TableName() string {
	return "expo_exhibitor_apply"
}

type ExpoExhibitorApplySearch struct {
	Company string
	Start   int64
	End     int64
	Status  v1.ExhibitorApplyStatus
	Page    int32
	Size    int32
}
