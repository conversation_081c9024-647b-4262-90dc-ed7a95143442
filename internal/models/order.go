package models

import (
	"time"

	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/igorm"
	v1 "gold_store/api/gold_store/v1"
	"gorm.io/gorm"
)

type Order struct {
	gorm.Model
	OrderNo               string                          `gorm:"column:order_no" json:"order_no"`                               // 用户单号
	Source                v1.OrderSource                  `gorm:"column:source" json:"source"`                                   // 订单来源：0：金币商城；1：VIP；2：VPS；3：报告；4：展会；5：EA
	PaymentMethod         v1.PaymentMethod                `gorm:"column:payment_method" json:"payment_method"`                   // 下单时选择的支付方式：0：金币；1：礼品卡；2：用户任务；3：支付宝；4：微信；5：苹果内购；6：google内购
	Platform              v1.Platform                     `gorm:"column:platform" json:"platform"`                               // 下单平台:0:苹果；1：安卓；2：PC；3：web
	UserId                string                          `gorm:"column:user_id" json:"user_id"`                                 // 用户ID
	ClientIP              string                          `gorm:"column:client_ip" json:"client_ip"`                             // 客户端ip
	LanguageCode          string                          `gorm:"column:language_code" json:"language_code"`                     // 用户语言
	PreferredLanguageCode string                          `gorm:"column:preferred_language_code" json:"preferred_language_code"` // 用户偏好语言
	CountryCode           string                          `gorm:"column:country_code" json:"country_code"`                       // 用户国家code
	CurrencySymbol        string                          `gorm:"column:currency_symbol" json:"currency_symbol"`                 // 下单时的货币单位
	BasicData             string                          `gorm:"column:basic_data" json:"basic_data"`                           // 用户basic_data
	AddressId             int32                           `gorm:"column:address_id" json:"address_id"`                           // 用户地址ID
	Address               *igorm.CustomValue[*Address]    `gorm:"column:address" json:"address"`                                 // 用户地址快照
	Quantity              int32                           `gorm:"column:quantity" json:"quantity"`                               // 商品总数量
	EstimatedShippingTime time.Time                       `gorm:"column:estimated_shipping_time" json:"estimated_shipping_time"` // 预计发货时间
	TotalAmount           float32                         `gorm:"column:total_amount" json:"total_amount"`                       // 总金额
	ShippingFee           float32                         `gorm:"column:shipping_fee" json:"shipping_fee"`                       // 运费
	Status                v1.OrderStatus                  `gorm:"column:status" json:"status"`                                   // 状态：0：待支付；1：已支付；2：已取消；3：待收货；4：已完成
	GoodsName             string                          `gorm:"column:goods_name" json:"goods_name"`                           // 商品名称，冗余字段
	TrackingNo            string                          `gorm:"column:tracking_no" json:"tracking_no"`                         // 物流单号
	Extra                 *igorm.CustomValue[*OrderExtra] `gorm:"column:extra" json:"extra"`                                     // 扩展字段
}

func (Order) TableName() string {
	return "order"
}

type OrderItem struct {
	gorm.Model
	OrderNo         string                                 `gorm:"column:order_no" json:"order_no"`                   // 订单号
	GoodsId         string                                 `gorm:"column:goods_id" json:"goods_id"`                   // 商品ID
	SkuId           string                                 `gorm:"column:sku_id" json:"sku_id"`                       // sku_id
	Price           float32                                `gorm:"column:price" json:"price"`                         // 商品价格
	PriceUnit       string                                 `gorm:"column:price_unit" json:"price_unit"`               // 价格单位,如果为空则为金币
	GoodsSnapshotId uint                                   `gorm:"column:goods_snapshot_id" json:"goods_snapshot_id"` // 快照ID
	Quantity        int32                                  `gorm:"column:quantity" json:"quantity"`                   // 购买的商品数量
	TotalAmount     float32                                `gorm:"column:total_amount" json:"total_amount"`           // 总价格
	ShippingFee     float32                                `gorm:"column:shipping_fee" json:"shipping_fee"`           // 运费
	GoodsName       string                                 `gorm:"column:goods_name" json:"goods_name"`               // 下单时的商品名称（冗余字段）
	SpecDesc        string                                 `gorm:"column:spec_desc" json:"spec_desc"`                 // 规格描述（冗余字段）
	Specs           *igorm.CustomValue[[]*v1.SpecSelected] `gorm:"column:specs" json:"specs"`                         // 规格值
}

func (OrderItem) TableName() string {
	return "order_item"
}

type FindOrderParams struct {
	UserId  string
	Offset  string
	Size    int
	Status  v1.OrderStatus
	Sources []v1.OrderSource
}

type ExhibitionUser struct {
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Email     string `json:"email,omitempty"`
	Phone     string `json:"phone,omitempty"`
}

type OrderExtra struct {
	Email                  string            `json:"email,omitempty"`
	DisplayAmount          string            `json:"display_amount,omitempty"`
	SpecDesc               map[string]string `json:"spec_desc,omitempty"`
	VPSStart               string            `json:"vps_start,omitempty"`
	VPSEnd                 string            `json:"vps_end,omitempty"`
	VPSStatus              int               `json:"vps_status,omitempty"`
	VPSLanguage            string            `json:"vps_language,omitempty"`
	VPSCountry             string            `json:"vps_country,omitempty"`
	VPSUser                string            `json:"vps_user,omitempty"`
	VPSBrokerId            string            `json:"vps_broker_id,omitempty"`
	ExhibitionTicketType   string            `json:"exhibition_ticket_type,omitempty"`
	ExhibitionCouponCode   string            `json:"exhibition_coupon_code,omitempty"`
	ExhibitionUsers        []ExhibitionUser  `json:"exhibition_users,omitempty"`
	ExhibitionLocationName string            `json:"exhibition_location_name,omitempty"`
	ExhibitionLocation     string            `json:"exhibition_location,omitempty"`
	ReportTraderCode       string            `json:"report_trader_code,omitempty"`
	ReportLanguage         string            `json:"report_language,omitempty"`
	ReportType             int32             `json:"report_type,omitempty"`
	TraderName             string            `json:"trader_name,omitempty"`
}

func (o *OrderExtra) ToVPSExtra(languageCode string) *v1.VPSExtra {
	if o == nil {
		return nil
	}

	var key string
	switch o.VPSStatus {
	case 1: // 待支付
		key = "59923"
	case 2: // 支付失败
		key = "62128"
	case 3: // 支付成功，未开通
		key = "62129"
	case 4: // 主机创建成功
		key = "62130"
	case 5: // 主机已删除
		key = "62131"
	case 6: // 主机7天未绑定下单
		key = "62132"
	case 7: // 主机已过期1月
		key = "62133"
	case 8: // 支付成功，未开通（待重新选地区）
		key = "62134"
	}

	vpsExtra := &v1.VPSExtra{
		EffectiveTime:  o.VPSStart,
		ExpirationTime: o.VPSEnd,
		OpenState:      i18n.GetWithDefaultEnglish(key, languageCode),
		ServerLanguage: o.VPSLanguage,
		ServerCity:     i18n.GetWithDefaultEnglish(o.VPSCountry, languageCode),
		ServerAccount:  o.VPSUser,
	}
	return vpsExtra
}

func (o *OrderExtra) ToReportExtra(languageCode string) *v1.ReportExtra {
	if o == nil {
		return nil
	}
	return &v1.ReportExtra{}
}

func (o *OrderExtra) ToExhibitionExtra(languageCode string) *v1.ExhibitionExtra {
	if o == nil {
		return nil
	}

	users := make([]*v1.ExhibitionUser, 0, len(o.ExhibitionUsers))
	for _, user := range o.ExhibitionUsers {
		users = append(users, &v1.ExhibitionUser{
			UserName:    user.FirstName + " " + user.LastName,
			Email:       user.Email,
			PhoneNumber: user.Phone,
		})
	}

	return &v1.ExhibitionExtra{
		Title:       o.ExhibitionLocationName,
		Description: o.ExhibitionLocation,
		TicketType:  o.ExhibitionTicketType,
		UseExchange: o.ExhibitionCouponCode != "",
		Users:       users,
	}
}

type FilterOrderParams struct {
	OrderNo       string
	GoodsName     string
	PaymentMethod v1.PaymentMethod
	Status        v1.OrderStatus
	Size          int32
	Page          int32
}
