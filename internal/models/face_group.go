package models

import (
	"time"

	"gorm.io/gorm"
)

// FaceGroup 人员库表-存储腾讯云人员库信息
type FaceGroup struct {
	ID                 string          `gorm:"column:id;primaryKey" json:"id" validate:"required"`                                 // 人员库ID（对应腾讯云GroupId）
	Name               string          `gorm:"column:name;size:60;not null" json:"name" validate:"required,max=60"`                // 人员库名称
	Description        string          `gorm:"column:description;size:200;default:''" json:"description" validate:"max=200"`       // 人员库描述
	Tag                string          `gorm:"column:tag;size:100;default:''" json:"tag" validate:"max=100"`                       // 人员库标签（对应腾讯云Tag字段）
	FaceModelVersion   string          `gorm:"column:face_model_version;size:10;not null;default:'3.0'" json:"face_model_version"` // 算法模型版本
	MaxFaces           int             `gorm:"column:max_faces;not null;default:50000" json:"max_faces"`                           // 最大人脸数量
	EstimatedFaceCount int             `gorm:"column:estimated_face_count;not null;default:0" json:"estimated_face_count"`         // 预估当前人脸数量
	CreationTimestamp  int64           `gorm:"column:creation_timestamp;default:0" json:"creation_timestamp"`                      // 腾讯云返回的创建时间戳
	Status             FaceGroupStatus `gorm:"column:status;not null;default:1" json:"status"`                                     // 状态：1=正常 2=删除中 3=已删除
	CreatedAt          time.Time       `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`             // 创建时间
	UpdatedAt          time.Time       `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`             // 更新时间
}

// TableName 指定表名
func (FaceGroup) TableName() string {
	return "face_groups"
}

// BeforeCreate 创建前钩子
func (fg *FaceGroup) BeforeCreate(tx *gorm.DB) error {
	if fg.Status == 0 {
		fg.Status = FaceGroupStatusNormal
	}
	return nil
}

// IsNormal 判断人员库是否为正常状态
func (fg *FaceGroup) IsNormal() bool {
	return fg.Status == FaceGroupStatusNormal
}

// IsDeleted 判断人员库是否已删除
func (fg *FaceGroup) IsDeleted() bool {
	return fg.Status == FaceGroupStatusDeleted
}

// CanDelete 判断人员库是否可以删除
func (fg *FaceGroup) CanDelete() bool {
	return fg.Status == FaceGroupStatusNormal
}

// MarkDeleting 标记为删除中状态
func (fg *FaceGroup) MarkDeleting() {
	fg.Status = FaceGroupStatusDeleting
}

// MarkDeleted 标记为已删除状态
func (fg *FaceGroup) MarkDeleted() {
	fg.Status = FaceGroupStatusDeleted
}

// UpdateFaceCount 更新人脸数量
func (fg *FaceGroup) UpdateFaceCount(count int) {
	fg.EstimatedFaceCount = count
}
