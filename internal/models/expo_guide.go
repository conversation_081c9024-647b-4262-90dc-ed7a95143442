package models

import (
	"github.com/airunny/wiki-go-tools/igorm"
	"gorm.io/gorm"
)

type ExpoGuide struct {
	gorm.Model
	ExpoId  int64                               `gorm:"column:expo_id"` // 展会ID
	MapURL  string                              `gorm:"column:map_url"` // 展会指南地图
	Enable  bool                                `gorm:"column:enable"`  // 是否启用：0-不启用；1-启用
	Creator string                              `gorm:"column:creator"` // 创建者
	Extra   *igorm.CustomValue[*ExpoGuideExtra] `json:"extra"`
}

func (*ExpoGuide) TableName() string {
	return "expo_guide"
}

type ExpoGuideExtra struct {
}
