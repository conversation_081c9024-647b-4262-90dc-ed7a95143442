package models

import (
	"database/sql/driver"

	"github.com/airunny/wiki-go-tools/igorm"
)

type Product struct {
	OrderProductId string         `gorm:"column:OrderProductId"` // 商品ID
	OrderId        string         `gorm:"column:OrderId"`        // 订单ID
	ProductType    int            `gorm:"column:ProductType"`    // 商品类型
	PlatformType   int            `gorm:"column:PlatformType"`   // 设备类型
	Coins          int            `gorm:"column:Coins"`          // 商品金币价格
	ProductContent ProductContent `gorm:"column:ProductContent"` // 商品详情
}

func (Product) TableName() string {
	return "orderproduct"
}

type ProductContent struct {
	VipCode      string `json:"VipCode"`
	IsUnShelve   bool   `json:"IsUnShelve"`
	ProjectType  int    `json:"ProjectType"`
	VipDuration  int    `json:"VipDuration"`
	DurationUnit int    `json:"DurationUnit"`
	UserIdentity int    `json:"UserIdentity"`
}

func (s *ProductContent) Value() (driver.Value, error) {
	return igorm.GormCustomValue(s)
}

func (s *ProductContent) Scan(value interface{}) error {
	return igorm.GormCustomScan(s, value)
}

type ProductTranslate struct {
	OrderProductId   string           `gorm:"column:OrderProductId"` // 商品ID
	LanguageCode     string           `gorm:"column:LanguageCode"`
	TranslateContent TranslateContent `gorm:"column:TranslateContent"`
}

func (ProductTranslate) TableName() string {
	return "orderproducttranslate"
}

type TranslateContent struct {
	Tips        string `json:"Tips"`
	Slogan      string `json:"Slogan"`
	DurationTag string `json:"DurationTag"`
}

func (s *TranslateContent) Value() (driver.Value, error) {
	return igorm.GormCustomValue(s)
}

func (s *TranslateContent) Scan(value interface{}) error {
	return igorm.GormCustomScan(s, value)
}
