package models

import (
	"time"

	v1 "gold_store/api/gold_store/v1"

	"gorm.io/gorm"

	"github.com/airunny/wiki-go-tools/igorm"
)

type Logistics struct {
	gorm.Model
	OrderNo       string                `gorm:"column:order_no"`       // 订单号
	TrackingNo    string                `gorm:"column:tracking_no"`    // 物流单号
	ShippingFee   float32               `gorm:"column:shipping_fee"`   // 运费
	Status        v1.LogisticStepStatus `gorm:"column:status"`         // 物流状态
	ShippingNotes string                `gorm:"column:shipping_notes"` // 物流备注
	ShippingTime  time.Time             `gorm:"column:shipping_time"`  // 发货时间
	DeliveryTime  time.Time             `gorm:"column:delivery_time"`  // 送达时间
	SignTime      time.Time             `gorm:"column:sign_time"`      // 签收时间
	CarrierCode   string                `gorm:"column:carrier_code"`   // 物流公司编号
	CarrierName   string                `gorm:"column:carrier_name"`   // 物流公司名称
}

func (Logistics) TableName() string {
	return "logistics"
}

type LogisticsDetail struct {
	gorm.Model
	OrderNo    string                                    `gorm:"column:order_no"`    // 订单号
	TrackingNo string                                    `gorm:"column:tracking_no"` // 物流单号
	DeliveryId string                                    `gorm:"column:delivery_id"` // 物流流水ID
	Status     v1.LogisticStepStatus                     `gorm:"column:status"`      // 物流状态
	Detail     *igorm.CustomValue[*LogisticsDetailExtra] `gorm:"column:detail"`      // 详情
	EventTime  time.Time                                 `gorm:"column:event_time"`  // 物流时间
}

func (LogisticsDetail) TableName() string {
	return "logistics_detail"
}

type LogisticsDetailExtra struct {
	EstimatedShippingTime int64  `json:"estimated_shipping_time"` // 预计发货时间戳，单位秒
	City                  string `json:"city"`                    // 城市
	Phone                 string `json:"phone"`                   // 手机号
	UserName              string `json:"user_name"`               // 快递员手机号
	Context               string `json:"context"`                 // 物流描述文本
}
