// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameUserGiftCard = "user_gift_card"

// UserGiftCard 用户兑换
type UserGiftCard struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                          // 主键ID
	SettingID  int64     `gorm:"column:setting_id;not null;comment:礼品卡配置Id" json:"setting_id"`                            // 礼品卡配置Id
	IsPinkage  int32     `gorm:"column:is_pinkage;not null;comment:是否包邮：0：不包邮；1：包邮" json:"is_pinkage"`                    // 是否包邮
	UserID     string    `gorm:"column:user_id;not null;comment:用户id" json:"user_id"`                                     // 用户id
	GoodsID    string    `gorm:"column:goods_id;not null;comment:产品Id" json:"goods_id"`                                   // 产品Id
	OrderId    string    `gorm:"column:order_id;not null;comment:订单号" json:"order_id"`                                    // 订单号
	Status     int32     `gorm:"column:status;not null;comment:商品状态：0：未使用；1：已使用" json:"status"`                           // 商品状态：0：未使用；1：已使用
	Remark     string    `gorm:"column:goods_id;not null;comment:修改备注" json:"remark"`                                     //修改备注
	CreatedAt  time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;comment:最后一次更新时间" json:"updated_at"` // 最后一次更新时间
	StartTime  time.Time `gorm:"column:start_time;not null;comment:有效期开始时间" json:"start_time"`                            // 有效期开始时间
	EndTime    time.Time `gorm:"column:end_time;not null;comment:有效期结束时间" json:"end_time"`                                // 有效期结束时间
	UsedTime   time.Time `gorm:"column:used_time;comment:使用时间" json:"used_time"`                                          // 使用时间
	ShowNotice int32     `gorm:"column:show_notice;not null;comment:商品状态：0：未使用；1：已使用" json:"show_notice"`                 // 通知状态：0：已通知；1：未通知
}

type UserGiftCardGroupby struct {
	SettingID int64
	Count     int
}

// TableName UserGiftCard's table name
func (*UserGiftCard) TableName() string {
	return TableNameUserGiftCard
}
