package service

import (
	"encoding/json"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
)

type ExhibitionOrder struct {
	Database string `json:"database"`
	Table    string `json:"table"`
	Type     string `json:"type"`
	Ts       int64  `json:"ts"`
	Xid      int64  `json:"xid"`
	Commit   bool   `json:"commit"`
	Data     struct {
		Id             int64   `json:"id"`
		OrderNo        string  `json:"order_no"`         // 订单编号
		Status         int     `json:"status"`           // 状态
		ExpoId         int     `json:"expo_id"`          // 展会编号
		TicketType     int     `json:"ticket_type"`      // 门票类型
		TicketTypeName string  `json:"ticket_type_name"` // 门票类型名称
		Price          float32 `json:"price"`            // 价格
		Quantity       int32   `json:"quantity"`         // 门票数量
		Amount         float32 `json:"amount"`           // 金额
		CouponCode     string  `json:"coupon_code"`      // 兑换码
		PaymentNo      string  `json:"payment_no"`       // 支付编号
		Payment        int     `json:"payment"`          // 支付方式
		UserId         string  `json:"user_id"`          // 用户ID
		PaidAt         string  `json:"paid_at"`          // 支付时间
		CreatedAt      string  `json:"created_at"`       // 创建时间
		Creator        string  `json:"creator"`          // 创建人
		ModifiedAt     string  `json:"modified_at"`      // 修改时间
		Modifier       string  `json:"modifier"`         // 修改人
		SalePrice      float32 `json:"sale_price"`       // 销售价
	} `json:"data"`
	Old map[string]interface{} `json:"old"`
}

type ReportOrder struct {
	Database string `json:"database"`
	Table    string `json:"table"`
	Type     string `json:"type"`
	Ts       int64  `json:"ts"`
	Xid      int64  `json:"xid"`
	Commit   bool   `json:"commit"`
	Data     struct {
	} `json:"data"`
	Old map[string]interface{} `json:"old"`
}

type VIPOrder struct {
	Database string `json:"database"`
	Table    string `json:"table"`
	Type     string `json:"type"`
	Ts       int64  `json:"ts"`
	Xid      int64  `json:"xid"`
	Commit   bool   `json:"commit"`
	Data     struct {
		OrderId         string  `json:"OrderId"`
		GoldOrderId     string  `json:"GoldOrderId"`     // 金币订单ID
		GoldOrderNumber string  `json:"GoldOrderNumber"` // 金币订单号
		GoldOrderToken  string  `json:"GoldOrderToken"`  // 金币系统支付凭证token
		UserId          string  `json:"UserId"`          // 用户ID
		OrderStatus     int     `json:"OrderStatus"`     // 订单状态 0待付款，1开通中，2成功，3失败
		OrderType       int     `json:"OrderType"`       // 订单类型
		ActualCoins     int     `json:"ActualCoins"`     // 订单实际付款金额
		LastUpdatedDate string  `json:"LastUpdatedDate"` // 订单上次更新时间
		CreatedDate     string  `json:"CreatedDate"`     // 订单创建时间
		FinishedTime    string  `json:"FinishedTime"`    // 订单完成时间
		CostType        int     `json:"CostType"`        // 兼容老订单-消费类型：0金币，1货币
		CostPrice       float32 `json:"CostPrice"`       // 兼容老订单-消费金额
		CurrencyCode    string  `json:"CurrencyCode"`    // 兼容老订单-货币Code
		CurrencySymbol  string  `json:"CurrencySymbol"`  // 兼容老订单-货币符号
		Placeholder     string  `json:"Placeholder"`     // 金额描述占位符
	} `json:"data"`
	Old map[string]interface{} `json:"old"`
}

type EAOrder struct {
	Database string `json:"database"`
	Table    string `json:"table"`
	Type     string `json:"type"`
	Ts       int64  `json:"ts"`
	Xid      int64  `json:"xid"`
	Commit   bool   `json:"commit"`
	Data     struct {
		OrderId       string  `json:"order_id"`      // 订单号
		ApiType       int     `json:"apiType"`       // 1 国内版 2 国际版
		Source        string  `json:"source"`        // 平台
		Platform      string  `json:"platform"`      // 支付平台，ALI：支付宝，WECHAT：微信，APPLEIAP：苹果内购，GOOGLEIAP：谷歌内购用户ID
		AppId         string  `json:"app_id"`        // 应用ID
		PaymId        string  `json:"paym_id"`       // 支付流水号
		Amount        float32 `json:"amount"`        // 订单金额
		DisplayAmount string  `json:"diplay_amount"` // 展示金额
		TradeStatus   string  `json:"trade_status"`  // 支付状态，TRADE_SUCCESS：支付成功，其它均为失败
		FinishedAt    string  `json:"finished_at"`   // 完成时间
		ReturnCode    string  `json:"return_code"`   // 返回状态
		UserId        string  `json:"UserId"`        // 用户ID
		GoodsId       int     `json:"GoodsId"`       // 商品ID
		CreateTime    string  `json:"CreateTime"`    // 创建时间
		UpdateTime    string  `json:"UpdateTime"`    // 最后一次更新时间
		GoldCoin      int     `json:"GoldCoin"`      // 金币数
		Countrycode   string  `json:"countrycode"`   // 国家代码
	} `json:"data"`
	Old map[string]interface{} `json:"old"`
}

type VPSOrder struct {
	Database string `json:"database"`
	Table    string `json:"table"`
	Type     string `json:"type"`
	Ts       int64  `json:"ts"`
	Xid      int64  `json:"xid"`
	Commit   bool   `json:"commit"`
	Data     struct {
		OrderId        string  `json:"order_id"`       // 订单号
		APIType        int     `json:"apiType"`        // 1 国内版 2 国际版
		OrderType      int     `json:"order_type"`     // 1 申请主机 2 续费
		Source         string  `json:"source"`         // 平台
		Platform       string  `json:"platform"`       // 支付平台，ALI：支付宝，WECHAT：微信，APPLEIAP：苹果内购，GOOGLEIAP：谷歌内购用户ID
		AppId          string  `json:"app_id"`         // 应用ID
		PaymentId      string  `json:"payment_id"`     // 支付流水号
		Amount         float32 `json:"amount"`         // 订单金额
		DisplayAmount  string  `json:"display_amount"` // 订单金额
		TradeStatus    string  `json:"trade_status"`   // 支付状态，TRADE_SUCCESS：支付成功，其它均为失败
		FinishedAt     string  `json:"finished_at"`    // 完成时间
		ReturnCode     string  `json:"return_code"`    // 返回状态
		UserId         string  `json:"UserId"`         // 用户ID
		Zone           string  `json:"Zone"`           // 所在区
		Language       int     `json:"Language"`       // 系统语言 1中文 2英文
		UHostPwd       string  `json:"UHostPwd"`       // 主机远程密码
		Config         int     `json:"Config"`         // 主机配置
		Months         int     `json:"Months"`         // 购买月份
		UpdateAt       string  `json:"updateAt"`       // 更新时间
		UHostId        string  `json:"UHostId"`        // 主机资源ID
		IPWan          string  `json:"IP_WAN"`         // 外网IP
		OrderStatus    int     `json:"Order_Status"`   // 开通状态 \r\n1：待支付\r\n2：支付失败\r\n3：支付成功，未开通\r\n4：主机创建成功\r\n5：主机已删除\r\n6：主机7天未绑定下单\r\n7：主机已过期1月\r\n8：支付成功，未开通（待重新选地区）
		CreateTime     string  `json:"CreateTime"`     // 创建时间
		ExpireTime     string  `json:"ExpireTime"`     // 到期时间
		PhoneZoon      string  `json:"PhoneZoon"`      // 手机区号
		PhoneNum       string  `json:"PhoneNum"`       // 手机号
		APILanguage    string  `json:"apiLanguage"`    //
		ApiCountryCode string  `json:"apiCountryCode"` //
		ExtensionId    int     `json:"extensionid"`    // 推广来源id
		HostType       int     `json:"hostType"`       // 1
		OrderTime      string  `json:"orderTime"`      // 订单创建时间
		GoldCoin       int     `json:"goldCoin"`       // 金币数
		BlackUser      int     `json:"blackUser"`      // 黑名单 0:默认 1 黑名单
		DeleteReason   int     `json:"deleteReason"`   // 删除原因:1 自己删除 2 到期回收 3 开通后7日内不满足条件回收
		IsFree         int     `json:"isFree"`         // 是否免费:1 免费开通
		IsZero         int     `json:"isZero"`         // 是否0元购:是0元购
		BrokerId       string  `json:"brokerid"`       // 交易商id
		Level          string  `json:"level"`          // 等级
	} `json:"data"`
	Old map[string]interface{} `json:"old"`
}

// ======================================

type orderCreateResult struct {
	OrderNo     string
	PaymentNo   string
	TotalAmount float32
}

type orderPayParams struct {
	UserId             string
	Method             v1.PaymentMethod
	GoodsId            string
	GoodsName          string
	GoodsImageURL      string
	Category           v1.GoodsCategory
	OperationTicket    string
	OrderNo            string
	PaymentNo          string
	TotalAmount        float32
	VpsLevel           string
	VpsZone            string
	VpsLanguage        int
	VipYear            int
	VipMonth           int
	VipDay             int
	GoodsNameTranslate map[string]string // 商品名称多语言
}

type orderPayResult struct {
	Release     func() error
	TotalAmount float32
	OperationId string
	PaidTime    time.Time
}

type orderPreCheckResult struct {
	Request       *v1.CreateOrderRequest
	OriginGoods   *models.Goods
	OriginSkus    []*models.SKU
	UserAddress   *models.Address
	GoodsDetail   *v1.GoodsDetail
	SelectedSkuId string
	TotalAmount   float32
	ShippingFee   float32
	GoodsNameCh   string
}

type ShippingFeeParams struct {
	UserId          string // 用户ID
	OperationTicket string // 票据
	GoodsId         string // 商品ID
}

type ShippingFeeResult struct {
	FreeShipping bool // 是否免运费
}

type VerifyParams struct {
	UserId          string  // 用户ID
	OperationTicket string  // 票据
	TotalAmount     float32 // 支付金额
	GoodsId         string  // 商品ID
	NotCheck        bool
}

type VerifyResult struct {
	FreeShipping bool // 是否免运费
}

type PayParams struct {
	UserId             string            // 用户ID
	OrderNo            string            // 订单号
	PaymentNo          string            // 支付单号
	OperationTicket    string            // 票据
	TotalAmount        float32           // 支付金额
	ImageURL           string            // 商品图片
	GoodsName          string            // 商品名称
	GoodsId            string            // 商品ID
	GoodsNameTranslate map[string]string // 商品名称多语言
}

type PayResult struct {
	OperationId string  // 业务ID（必须要返回）
	PaidTotal   float32 // 实际支付金额数量（金币消费需要返回)
}

type RollbackParams struct {
	OperationId        string            // 支付时返回的业务ID
	Total              float32           // 需要回退的数量
	GoodsNameTranslate map[string]string // 商品名称多语言
}

type NoticeParams struct {
	OrderNo         string            // 订单号
	UserId          string            // 用户ID
	ImageURL        string            // 商品图片
	LogisticsStatus int               // 物流状态
	LanguageCode    string            // 语言code
	TitleMapping    map[string]string // 标题多语言
	ContentMapping  map[string]string // 内容多语言
}

type Notification struct {
	BusinessId        string                 `json:"BusinessId"`
	BusinessCategory  string                 `json:"BusinessCategory"`
	Title             map[string]string      `json:"Title"`
	Content           map[string]string      `json:"Content"`
	CustomInformation map[string]string      `json:"CustomInformation"`
	UserId            string                 `json:"UserId"`
	CountryCodes      []string               `json:"CountryCodes"`
	LanguageCodes     []string               `json:"LanguageCodes"`
	AreaCodes         []string               `json:"AreaCodes"`
	PushTimestamp     int64                  `json:"PushTimestamp"`
	Snapshot          map[string]interface{} `json:"Snapshot"`
	AggInfo           map[string]string      `json:"AggInfo"`
}

func (n *Notification) String() []byte {
	str, _ := json.Marshal(n)
	return str
}

type UnionOrder struct {
	Order         *models.Order
	OrderItems    []*models.OrderItem
	GoodsSnapshot *models.GoodsSnapshot
	Payment       *models.Payment
}
