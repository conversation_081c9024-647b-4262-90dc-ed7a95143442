package service

import (
	"time"

	"gold_store/internal/conf"
	"gold_store/internal/models"
	"gold_store/pkg/ikafka"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-kratos/kratos/v2/log"
)

type TaskConsumer struct {
	consumers []*ikafka.ConfluentConsumer
	service   *Service
	business  *conf.Business
}

func NewTaskConsumer(business *conf.Business, svc *Service, _ interface{}) *TaskConsumer {
	return &TaskConsumer{
		consumers: make([]*ikafka.ConfluentConsumer, 0),
		service:   svc,
		business:  business,
	}
}

func (s *TaskConsumer) Start() error {
	consumerConfigs := s.generateKafkaConsumerConfigs()
	for taskType, config := range consumerConfigs {
		if config == nil || config.GroupId == "" {
			continue
		}
		func(tt models.TaskSubType, cfg *ikafka.ConsumerConfig) {
			consumer, err := ikafka.NewConfluentConsumer(cfg)
			if err != nil {
				log.Errorf("Failed to create TaskConsumer consumer for task %s: %v", tt, err)
				return
			}
			s.consumers = append(s.consumers, consumer)

			callback := func(c *kafka.Consumer, message *kafka.Message) {
				if err := s.service.HandleTaskMessage(tt, c, message); err != nil {
					log.Errorf("Failed to process message for task %s: %v", tt, err)
					return
				}
				// 使用 confluent-kafka-go 的 Commit 方法标记消息已处理
				_, err := c.Commit()
				if err != nil {
					log.Errorf("Failed to commit message: %v", err)
				}
			}
			consumer.Start(callback)
		}(taskType, config)
	}
	return nil
}

func (s *TaskConsumer) Stop() {
	log.Info("TaskConsumer server stopping")
	for _, consumer := range s.consumers {
		if err := consumer.Close(); err != nil {
			log.Errorf("Failed to close TaskConsumer consumer: %v", err)
		}
	}
	log.Info("All TaskConsumer consumers closed")
}

func (s *TaskConsumer) generateKafkaConsumerConfigs() map[models.TaskSubType]*ikafka.ConsumerConfig {
	result := make(map[models.TaskSubType]*ikafka.ConsumerConfig)
	// 获取各个任务类型的配置
	tcg := s.business.TaskConsumerGroup
	validTopics := make(map[models.TaskSubType]string)
	if tcg == nil {
		panic("nil task consumer group")
	}
	if tcg.PostMoment != "" {
		validTopics[models.TaskSubTypePostMoment] = tcg.PostMoment
	}
	if tcg.PostBusiness != "" {
		validTopics[models.TaskSubTypePostBusiness] = tcg.PostBusiness
	}
	if tcg.CommentPost != "" {
		validTopics[models.TaskSubTypeCommentPost] = tcg.CommentPost
	}
	if tcg.RateDealer != "" {
		validTopics[models.TaskSubTypeRateDealer] = tcg.RateDealer
	}
	if tcg.JoinActivity != "" {
		validTopics[models.TaskSubTypeJoinActivity] = tcg.JoinActivity
	}
	//if tcg.InviteFriend != "" {
	//	validTopics[models.TaskSubTypeInviteFriend] = tcg.InviteFriend
	//}
	if tcg.ModifyUsername != "" {
		validTopics[models.TaskSubTypeModifyUsername] = tcg.ModifyUsername
	}
	if tcg.ModifyAvatar != "" {
		validTopics[models.TaskSubTypeModifyAvatar] = tcg.ModifyAvatar
	}
	if tcg.VerifyIdentity != "" {
		validTopics[models.TaskSubTypeVerifyIdentity] = tcg.VerifyIdentity
	}
	if tcg.BindRealAccount != "" {
		validTopics[models.TaskSubTypeBindRealAccount] = tcg.BindRealAccount
	}
	if tcg.OpenVPS != "" {
		validTopics[models.TaskSubTypeOpenVPS] = tcg.OpenVPS
	}
	if tcg.FollowWikiFX != "" {
		validTopics[models.TaskSubTypeFollowWikiFX] = tcg.FollowWikiFX
	}
	if tcg.ViewDealer != "" {
		validTopics[models.TaskSubTypeViewDealer] = tcg.ViewDealer
	}
	if tcg.Search != "" {
		validTopics[models.TaskSubTypeSearch] = tcg.Search
	}
	for taskType, topic := range validTopics {
		config := &ikafka.ConsumerConfig{
			Brokers:           s.business.TaskConsumer.Brokers,
			Version:           s.business.TaskConsumer.Version,
			Topics:            []string{topic},
			GroupId:           "gold_store_task_1",
			OffsetOldest:      s.business.TaskConsumer.OffsetOldest,
			AutoCommit:        s.business.TaskConsumer.AutoCommit,
			MaxProcessingTime: 600 * time.Second,
			KeepAlive:         120 * time.Second,
			ConnectionsRetry:  true,
			RetryBackoff:      60 * time.Second,
		}
		result[taskType] = config
	}
	return result
}
