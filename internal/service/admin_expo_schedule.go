package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	userCenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

func (s *Service) checkExpoSchedule(ctx context.Context, in *v1.ExpoScheduleInfo) error {
	l := log.Context(ctx)
	if in.ExpoId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.HallId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "会场ID不能为空")
	}

	//if in.Host == nil || in.Host.Id <= 0 {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "主持人不能为空")
	//}

	if len(in.Guests) < 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "演讲嘉宾不能为空")
	}

	guestMapping := make(map[int64]struct{}, len(in.Guests))
	for _, guest := range in.Guests {
		if guest.Id <= 0 {
			return innErr.WithMessage(innErr.ErrBadRequest, "演讲嘉宾不能为空")
		}

		if _, ok := guestMapping[guest.Id]; ok {
			return innErr.WithMessage(innErr.ErrBadRequest, "演讲嘉宾不能重复")
		}
		guestMapping[guest.Id] = struct{}{}
	}

	if in.Host != nil && in.Host.Id > 0 {
		if _, ok := guestMapping[in.Host.Id]; ok {
			return innErr.WithMessage(innErr.ErrBadRequest, "主持人不能是演讲嘉宾")
		}
	}

	if len(in.Languages) <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "多语言不能为空")
	}

	var (
		enTheme string
		theme   string
	)

	for key, language := range in.Languages {
		if strings.ToLower(key) == "en" {
			enTheme = language.Theme
		}

		if strings.ToLower(key) == "zh-cn" {
			theme = language.Theme
		}
	}

	if enTheme == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "多语言英语不能为空")
	}
	in.Theme = theme

	if in.Start >= in.End {
		return innErr.WithMessage(innErr.ErrBadRequest, "开始时间不能大于结束时间")
	}

	var (
		startTime = time.Unix(in.Start, 0)
		endTime   = time.Unix(in.End, 0)
	)

	expo, err := s.expo.Get(ctx, in.ExpoId)
	if err != nil {
		return err
	}
	zoneDiff := expo.GetZoneDiff()

	if startTime.UTC().Add(zoneDiff).Before(expo.Start) {
		return innErr.WithMessage(innErr.ErrBadRequest, "开始时间不能早于展会开始时间")
	}

	if endTime.UTC().Add(zoneDiff).After(expo.End) {
		return innErr.WithMessage(innErr.ErrBadRequest, "结束时间不能晚于展会结束时间")
	}

	schedule, err := s.expoSchedule.GetByStartAndEnd(ctx, in.ExpoId, in.HallId, startTime, endTime)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoSchedule.ExistsByUserId Err:%v", err)
		return err
	}

	if err == nil && int64(schedule.ID) != in.Id {
		return innErr.WithMessage(innErr.ErrBadRequest, "当前时间段已经存在")
	}
	return nil
}

func (s *Service) AddExpoSchedule(ctx context.Context, in *v1.ExpoScheduleInfo) (*v1.AddExpoScheduleReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoSchedule(ctx, in)
	if err != nil {
		l.Errorf("checkExpoSchedule Err:%v", err)
		return nil, err
	}

	var (
		startTime = time.Unix(in.Start, 0).Local()
		endTime   = time.Unix(in.End, 0).Local()
	)

	tx := s.expoSchedule.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()
	extra := &igorm.CustomValue[*models.ExpoScheduleExtra]{
		V: &models.ExpoScheduleExtra{
			Languages: in.Languages,
		},
	}
	var newId uint
	newId, err = s.expoSchedule.Add(ctx, &models.ExpoSchedule{
		ExpoId:  in.ExpoId,
		HallId:  in.HallId,
		Type:    in.Type,
		Theme:   in.Theme,
		HostId:  in.Host.Id,
		Date:    time.Unix(in.Start, 0).UTC().Format(time.DateOnly),
		Start:   startTime,
		End:     endTime,
		Enable:  in.Enable,
		Creator: in.Creator,
		Extra:   extra,
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoSchedule.Add Err:%v", err)
		return nil, err
	}

	guests := make([]*models.ExpoScheduleGuest, 0, len(in.Guests))
	for _, guest := range in.Guests {
		guests = append(guests, &models.ExpoScheduleGuest{
			ExpoId:     in.ExpoId,
			ScheduleId: int64(newId),
			GuestId:    guest.Id,
		})
	}

	if in.Host != nil && in.Host.Id > 0 {
		guests = append(guests, &models.ExpoScheduleGuest{
			ExpoId:     in.ExpoId,
			ScheduleId: int64(newId),
			GuestId:    in.Host.Id,
			Role:       1,
		})
	}

	err = s.expoScheduleGuest.BatchAdd(ctx, guests, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoScheduleGuest.BatchAdd Err:%v", err)
		return nil, err
	}

	return &v1.AddExpoScheduleReply{
		Id: int64(newId),
	}, nil
}

func (s *Service) GetExpoSchedule(ctx context.Context, in *v1.GetExpoScheduleRequest) (*v1.ExpoScheduleInfo, error) {
	l := log.Context(ctx)

	schedule, err := s.expoSchedule.Get(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoSchedule.Get Err:%v", err)
		return nil, err
	}

	scheduleGuests, err := s.expoScheduleGuest.FindByScheduleIds(ctx, []int64{in.Id})
	if err != nil {
		l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
		return nil, err
	}

	guestIds := make([]int64, 0, len(scheduleGuests))
	for _, scheduleGuest := range scheduleGuests {
		guestIds = append(guestIds, scheduleGuest.GuestId)
	}

	guestIds = append(guestIds, schedule.HostId)
	guests, err := s.guest.FindByIdsWithUnscoped(ctx, guestIds)
	if err != nil {
		l.Errorf("guest.FindByIds Err:%v", err)
		return nil, err
	}

	guestsMapping := make(map[int64]*models.Guest, len(guests))
	for _, guest := range guests {
		guestsMapping[int64(guest.ID)] = guest
	}

	var hostItem *v1.ExpoScheduleGuest
	if guest, ok := guestsMapping[schedule.HostId]; ok {
		hostItem = &v1.ExpoScheduleGuest{
			Id:         int64(guest.ID),
			Name:       guest.Name,
			WikiNumber: guest.WikiNumber,
			Phone:      guest.Phone,
			Email:      guest.Email,
			Avatar:     guest.Avatar,
		}
	}

	guestItems := make([]*v1.ExpoScheduleGuest, 0, len(scheduleGuests))
	// 用于记录已经添加过的嘉宾 ID
	guestIdsAdded := make(map[int64]struct{})
	for _, scheduleGuest := range scheduleGuests {
		if scheduleGuest.Role == 1 {
			continue
		}
		guest := guestsMapping[scheduleGuest.GuestId]
		if guest == nil {
			continue
		}

		// 检查嘉宾 ID 是否已经添加过
		if _, exists := guestIdsAdded[int64(guest.ID)]; exists {
			continue
		}

		// 标记该嘉宾 ID 已添加
		guestIdsAdded[int64(guest.ID)] = struct{}{}
		guestItems = append(guestItems, &v1.ExpoScheduleGuest{
			Id:         int64(guest.ID),
			Name:       guest.Name,
			WikiNumber: guest.WikiNumber,
			Phone:      guest.Phone,
			Email:      guest.Email,
			Avatar:     guest.Avatar,
		})
	}

	hall, err := s.expoHall.GetHallById(ctx, schedule.HallId)
	if err != nil {
		l.Errorf("expoHall.GetHallById Err:%v", err)
		return nil, err
	}

	return &v1.ExpoScheduleInfo{
		Id:     int64(schedule.ID),
		ExpoId: schedule.ExpoId,
		HallId: schedule.HallId,
		Hall: &v1.ExpoHall{
			Id:   int64(hall.ID),
			Type: hall.Type,
			Name: hallTypeMap[hall.Type] + "-" + hall.Name,
		},
		Type:      schedule.Type,
		TypeName:  scheduleTypeMap[schedule.Type],
		Theme:     schedule.Theme,
		Host:      hostItem,
		Guests:    guestItems,
		Start:     schedule.Start.Unix(),
		End:       schedule.End.Unix(),
		Enable:    schedule.Enable,
		CreatedAt: schedule.CreatedAt.Unix(),
		Creator:   schedule.Creator,
		Languages: schedule.Extra.V.Languages,
	}, nil
}

func (s *Service) UpdateExpoSchedule(ctx context.Context, in *v1.ExpoScheduleInfo) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoSchedule(ctx, in)
	if err != nil {
		l.Errorf("checkExpoSchedule Err:%v	", err)
		return nil, err
	}

	var (
		startTime = time.Unix(in.Start, 0).Local()
		endTime   = time.Unix(in.End, 0).Local()
	)

	tx := s.expoSchedule.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.expoScheduleGuest.DeleteByScheduleId(ctx, in.Id, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoScheduleGuest.DeleteByScheduleId Err:%v", err)
		return nil, err
	}

	err = s.expoSchedule.Update(ctx, &models.ExpoSchedule{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId: in.ExpoId,
		HallId: in.HallId,
		Type:   in.Type,
		Theme:  in.Theme,
		HostId: in.Host.Id,
		Date:   time.Unix(in.Start, 0).UTC().Format(time.DateOnly),
		Start:  startTime,
		End:    endTime,
		Enable: in.Enable,
		Extra: &igorm.CustomValue[*models.ExpoScheduleExtra]{
			V: &models.ExpoScheduleExtra{
				Languages: in.Languages,
			},
		},
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoSchedule.Update Err:%v", err)
		return nil, err
	}

	guests := make([]*models.ExpoScheduleGuest, 0, len(in.Guests))
	for _, guest := range in.Guests {
		guests = append(guests, &models.ExpoScheduleGuest{
			ExpoId:     in.ExpoId,
			ScheduleId: in.Id,
			GuestId:    guest.Id,
		})
	}

	if in.Host != nil && in.Host.Id > 0 {
		guests = append(guests, &models.ExpoScheduleGuest{
			ExpoId:     in.ExpoId,
			ScheduleId: in.Id,
			GuestId:    in.Host.Id,
			Role:       1,
		})
	}

	err = s.expoScheduleGuest.BatchAdd(ctx, guests, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoScheduleGuest.BatchAdd Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoScheduleEnable(ctx context.Context, in *v1.SetExpoScheduleEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoSchedule.UpdateEnable(ctx, in.ExpoId, in.Id, in.Enable)
	if err != nil {
		l.Errorf("expoSchedule.UpdateEnable Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListExpoSchedule(ctx context.Context, in *v1.ListExpoScheduleRequest) (*v1.ListExpoScheduleReply, error) {
	l := log.Context(ctx)
	if in.Page <= 0 {
		in.Page = 1
	}

	if in.Size <= 0 {
		in.Size = 10
	}

	schedules, total, err := s.expoSchedule.FindByPageCount(ctx, in.ExpoId, in.Type, in.Name, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoSchedule.FindByPageCount Err:%v", err)
		return nil, err
	}

	//获取所有的会场id
	var (
		hallIds     = make([]int64, 0, len(schedules))
		scheduleIds = make([]int64, 0, len(schedules))
	)

	for _, schedule := range schedules {
		hallIds = append(hallIds, schedule.HallId)
		scheduleIds = append(scheduleIds, int64(schedule.ID))
	}

	halls, err := s.expoHall.FindByIds(ctx, hallIds)
	if err != nil {
		l.Errorf("expoHall.FindByIds Err:%v", err)
		return nil, err
	}

	hallMapping := make(map[int64]*models.ExpoHall, len(halls))
	for _, hall := range halls {
		hallMapping[int64(hall.ID)] = hall
	}

	// 一次性获取所有议程的嘉宾信息
	scheduleGuests, err := s.expoScheduleGuest.FindByScheduleIds(ctx, scheduleIds)
	if err != nil {
		l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
		return nil, err
	}

	// 构建每个议程对应的嘉宾映射
	scheduleGuestMap := make(map[int64][]*models.ExpoScheduleGuest, len(scheduleGuests))
	for _, scheduleGuest := range scheduleGuests {
		scheduleGuestMap[scheduleGuest.ScheduleId] = append(scheduleGuestMap[scheduleGuest.ScheduleId], scheduleGuest)
	}

	// 收集所有嘉宾 ID 和主持人 ID
	guestIds := make([]int64, 0, len(schedules))
	for _, schedule := range schedules {
		guestIds = append(guestIds, schedule.HostId)
		for _, scheduleGuest := range scheduleGuestMap[int64(schedule.ID)] {
			guestIds = append(guestIds, scheduleGuest.GuestId)
		}
	}

	// 一次性查询所有嘉宾信息
	guests, err := s.guest.FindByIdsWithUnscoped(ctx, guestIds)
	if err != nil {
		l.Errorf("guest.FindByIds Err:%v", err)
		return nil, err
	}

	// 构建嘉宾信息映射
	guestsMapping := make(map[int64]*models.Guest, len(guests))
	for _, guest := range guests {
		guestsMapping[int64(guest.ID)] = guest
	}

	items := make([]*v1.ExpoScheduleInfo, 0, len(schedules))
	for _, schedule := range schedules {
		var (
			hostItem          *v1.ExpoScheduleGuest
			curScheduleGuests = scheduleGuestMap[int64(schedule.ID)]
		)

		if guest, ok := guestsMapping[schedule.HostId]; ok {
			hostItem = &v1.ExpoScheduleGuest{
				Id:         int64(guest.ID),
				Name:       guest.Name,
				WikiNumber: guest.WikiNumber,
				Phone:      guest.Phone,
				Email:      guest.Email,
				Avatar:     guest.Avatar,
			}
		}

		var (
			guestItems = make([]*v1.ExpoScheduleGuest, 0, len(curScheduleGuests))
			// 用于记录已经添加过的嘉宾 ID
			guestIdsAdded = make(map[int64]struct{})
		)

		for _, scheduleGuest := range curScheduleGuests {
			if scheduleGuest.Role == 1 {
				continue
			}

			guest, ok := guestsMapping[scheduleGuest.GuestId]
			if !ok {
				continue
			}

			// 检查嘉宾 ID 是否已经添加过
			if _, exists := guestIdsAdded[int64(guest.ID)]; exists {
				continue
			}

			// 标记该嘉宾 ID 已添加
			guestIdsAdded[int64(guest.ID)] = struct{}{}
			guestItems = append(guestItems, &v1.ExpoScheduleGuest{
				Id:         int64(guest.ID),
				Name:       guest.Name,
				WikiNumber: guest.WikiNumber,
				Phone:      guest.Phone,
				Email:      guest.Email,
				Avatar:     guest.Avatar,
			})
		}

		var hallInfo *v1.ExpoHall
		if hall, ok := hallMapping[schedule.HallId]; ok {
			hallInfo = &v1.ExpoHall{
				Id:   int64(hall.ID),
				Name: hallTypeMap[hall.Type] + "-" + hall.Name,
				Type: hall.Type,
			}
		}

		var enTheme string
		if schedule.Extra != nil && schedule.Extra.V != nil && schedule.Extra.V.Languages != nil {
			if lang, exists := schedule.Extra.V.Languages["en"]; exists && lang != nil {
				enTheme = lang.Theme
			}
		}

		items = append(items, &v1.ExpoScheduleInfo{
			Id:        int64(schedule.ID),
			ExpoId:    schedule.ExpoId,
			HallId:    schedule.HallId,
			Hall:      hallInfo,
			Type:      schedule.Type,
			TypeName:  scheduleTypeMap[schedule.Type],
			Theme:     enTheme,
			Host:      hostItem,
			Guests:    guestItems,
			Start:     schedule.Start.Unix(),
			End:       schedule.End.Unix(),
			Enable:    schedule.Enable,
			CreatedAt: schedule.CreatedAt.Unix(),
			Creator:   schedule.Creator,
			Languages: schedule.Extra.V.Languages,
		})
	}

	return &v1.ListExpoScheduleReply{
		Schedules: items,
		Total:     total,
	}, nil
}

func (s *Service) DeleteExpoSchedule(ctx context.Context, in *v1.DeleteExpoScheduleRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	tx := s.expoSchedule.Begin()
	var err error
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.expoSchedule.Delete(ctx, in.ExpoId, in.Id, igorm.WithTransaction(tx)) // Add transaction option
	if err != nil {
		l.Errorf("expoSchedule.Delete Err:%v", err)
		return nil, err
	}
	err = s.expoScheduleGuest.DeleteByScheduleId(ctx, in.Id, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoScheduleGuest.DeleteByScheduleId Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) getExpoScheduleGuests(ctx context.Context, schedules []*models.ExpoSchedule) (
	map[int64][]*models.ExpoScheduleGuest,
	map[int64]*models.Participant,
	map[string]*userCenterv1.UserInfo, error) {
	l := log.Context(ctx)

	var (
		scheduleIds        = make([]int64, 0, len(schedules))
		guestIds           = make([]int64, 0, len(scheduleIds)+1)
		participants       []*models.Participant
		userIds            = make([]string, 0, len(guestIds))
		userMap            = make(map[string]*userCenterv1.UserInfo, len(participants))
		participantMapping = make(map[int64]*models.Participant, len(participants))
		scheduleGuests     = make(map[int64][]*models.ExpoScheduleGuest, len(schedules))
	)

	for _, schedule := range schedules {
		scheduleIds = append(scheduleIds, int64(schedule.ID))
		guestIds = append(guestIds, schedule.HostId)
	}

	guests, err := s.expoScheduleGuest.FindByScheduleIds(ctx, scheduleIds)
	if err != nil {
		l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
		return nil, nil, nil, err
	}

	for _, guest := range guests {
		guestIds = append(guestIds, guest.GuestId)
		scheduleGuests[guest.ScheduleId] = append(scheduleGuests[guest.ScheduleId], guest)
	}

	if len(guestIds) > 0 {
		participants, err = s.participant.FindByIds(ctx, guestIds)
		if err != nil {
			l.Errorf("participant.FindByIds Err:%v", err)
			return nil, nil, nil, err
		}
	}

	for _, participant := range participants {
		participantMapping[int64(participant.ID)] = participant
		if participant.UserId != "" {
			userIds = append(userIds, participant.UserId)
		}
	}

	if len(userIds) > 0 {
		var userRes *userCenterv1.GetUsersReply
		userRes, err = s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
			UserIds: userIds,
		})
		if err != nil {
			l.Errorf("user.GetUsersInfo Err:%v", err)
			return nil, nil, nil, err
		}

		for _, user := range userRes.Message {
			userMap[user.UserId] = user
		}
	}
	return scheduleGuests, participantMapping, userMap, nil
}

func (s *Service) ImportExpoSchedule(ctx context.Context, in *v1.ImportExpoScheduleRequest) (*common.EmptyReply, error) {
	if in.ExpoId < 0 {
		return nil, innErr.ErrBadRequest
	}

	return &common.EmptyReply{}, nil
}

func (s *Service) ExportExpoSchedule(ctx context.Context, in *v1.ExportExpoScheduleRequest) (*v1.ExportExpoScheduleReply, error) {
	var (
		l    = log.Context(ctx)
		page = 1
		size = 10
	)

	var (
		filePath = path.Join(os.TempDir(), fmt.Sprintf("expo_schedule_%d.xlsx", time.Now().UnixMicro()))
		excel    = excelize.NewFile()
	)

	writer, err := excel.NewStreamWriter("Sheet1")
	if err != nil {
		l.Errorf("excel.NewStreamWriter Err:%v", err)
		return nil, err
	}

	styleID, err := excel.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Color: "777777",
			Size:  14,
			Bold:  true,
		},
	})

	err = writer.SetRow("A1", []interface{}{"会场类型", "议程类型", "演讲题目", "议程日期", "议程时间", "角色", "主持人头像", "主持人姓名", "嘉宾id", "天眼号", "手机号", "邮箱"}, excelize.RowOpts{
		Height:       16,
		StyleID:      styleID,
		OutlineLevel: 0,
	})
	if err != nil {
		l.Errorf("writer.SetRow Err:%v", err)
		return nil, err
	}

	total := 1
	for {
		var res *v1.ListExpoScheduleReply
		res, err = s.ListExpoSchedule(ctx, &v1.ListExpoScheduleRequest{
			ExpoId: in.ExpoId,
			Name:   in.Name,
			Type:   in.Type,
			Page:   int32(page),
			Size:   int32(size),
		})
		if err != nil {
			l.Errorf("ListExpoSchedule Err:%v", err)
			return nil, err
		}

		for _, schedule := range res.Schedules {
			for _, guest := range schedule.Guests {
				err = writer.SetRow(fmt.Sprintf("A%d", total+1), []interface{}{
					schedule.Hall.Name,
					hallTypeMap[schedule.Hall.Type],
					schedule.Theme,
					time.Unix(schedule.Start, 0).Format("2006-01-02"),
					fmt.Sprintf("%s-%s", time.Unix(schedule.Start, 0).Format("15:04"), time.Unix(schedule.End, 0).Format("15:04")),
					"主持人",
					schedule.Host.Avatar,
					schedule.Host.Name,
					guest.Id,
					guest.WikiNumber,
					guest.Phone,
					guest.Email,
				})
				if err != nil {
					return nil, err
				}
				total++
			}

		}

		if len(res.Schedules) <= 0 {
			break
		}
		page++
	}
	err = writer.Flush()
	if err != nil {
		l.Errorf("writer.Flush Err:%v", err)
		return nil, err
	}

	err = excel.SaveAs(filePath)
	if err != nil {
		l.Errorf("excel.SaveAs Err:%v", err)
		return nil, err
	}

	content := bytes.NewBuffer(nil)
	_, err = excel.WriteTo(content)
	if err != nil {
		l.Errorf("file.WriteTo Err:%v", err)
		return nil, err
	}

	return &v1.ExportExpoScheduleReply{
		Data: base64.StdEncoding.EncodeToString(content.Bytes()),
	}, nil
}
