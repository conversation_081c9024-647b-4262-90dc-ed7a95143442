package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/express"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
)

// SubscribeExpress 订阅快递推送
func (s *Service) SubscribeExpress(ctx context.Context, trackingNo, carrierCode, phone, callbackURL string) error {
	// 创建快递100客户端
	expressClient := express.NewClient(&express.Config{
		Key:      s.business.Express.Key,
		Customer: s.business.Express.Customer,
		Secret:   s.business.Express.Secret,
		BaseURL:  s.business.Express.BaseURL,
	})

	// 构建订阅请求
	pollReq := &express.PollRequest{
		Company: carrierCode,
		Number:  trackingNo,
		Key:     s.business.Express.Key,
		Parameters: express.PollParameters{
			Callbackurl: callbackURL,
			Salt:        s.business.Express.Secret, // 使用secret作为salt
			Phone:       phone,
			Resultv2:    "4", // 开启高级行政区域解析
		},
	}

	log.Context(ctx).Infof("订阅 request callbackURL : %v", callbackURL)
	// 调用订阅接口
	response, err := expressClient.SubscribeExpress(ctx, pollReq)
	if err != nil {
		log.Context(ctx).Errorf("订阅快递推送失败: %v", err)
		return err
	}
	log.Context(ctx).Infof("订阅response : %v", response)

	if !response.Result {
		log.Context(ctx).Errorf("快递推送订阅失败: 代码=%s, 消息=%s", response.ReturnCode, response.Message)
		return errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("订阅失败: %s", response.Message))
	}

	log.Context(ctx).Infof("成功订阅快递推送: 单号=%s, 快递公司=%s", trackingNo, carrierCode)
	return nil
}

// sendLogisticsNotification 发送物流状态变更通知给用户
func (s *Service) sendLogisticsNotification(ctx context.Context, orderNo string, logisticStatus v1.LogisticStepStatus, logisticsDetail *models.LogisticsDetail) {
	l := log.Context(ctx)
	// 获取订单信息
	order, err := s.order.GetByOrderNo(ctx, orderNo)
	if err != nil {
		l.Errorf("查询订单信息失败，无法发送物流通知: orderNo=%s, error=%v", orderNo, err)
		return
	}

	var (
		languageCode   = order.LanguageCode
		titleMapping   = make(map[string]string, len(supportedLanguageCodes))
		contentMapping = make(map[string]string, len(supportedLanguageCodes))
	)

	for _, language := range supportedLanguageCodes {
		var (
			stepValue = s.getLocalizedLogisticStepName(logisticStatus, language)
			content   = s.getLocalizedLogisticStepDesc(logisticsDetail, language)
		)

		if stepValue == "" || content == "" {
			l.Warnf("getLocalizedLogisticStepName 多语言内容为空:, language=%s", language)
			continue
		}
		titleMapping[language] = stepValue
		contentMapping[language] = content
	}

	str, _ := json.Marshal(contentMapping)
	notice := &Notification{
		BusinessId:       orderNo,
		BusinessCategory: "Logistics",
		Title:            titleMapping,
		Content:          contentMapping,
		CustomInformation: map[string]string{
			"Code":             orderNo,
			"type":             "11",
			"largeIcon":        s.getOrderGoodsImage(ctx, orderNo),
			"languageCode":     languageCode,
			"alert":            string(str),
			"userId":           order.UserId,
			"logistics_status": strconv.Itoa(int(logisticStatus)),
		},
		UserId:        order.UserId,
		PushTimestamp: time.Now().Add(5 * time.Second).UnixMilli(),
	}

	log.Context(ctx).Infof("sendLogisticsNotification notice : %v", notice.String())
	err = s.notification.Publish(notice.String())
	// 发送通知
	if err != nil {
		log.Context(ctx).Errorf("发送物流通知失败: orderNo=%s, error=%v", orderNo, err)
	} else {
		log.Context(ctx).Infof("成功发送物流通知: orderNo=%s, status=%s, userId=%s",
			orderNo, logisticStatus.String(), order.UserId)
	}
}

// getOrderGoodsImage 获取订单商品图片URL
func (s *Service) getOrderGoodsImage(ctx context.Context, orderNo string) string {
	// 获取订单项
	orderItems, err := s.orderItem.FindByOrderNos(ctx, []string{orderNo})
	if err != nil || len(orderItems) == 0 {
		log.Context(ctx).Warnf("获取订单项失败或为空: orderNo=%s, error=%v", orderNo, err)
		return ""
	}

	// 获取第一个商品的快照
	goodsSnapshot, err := s.goodsSnapshot.Get(ctx, orderItems[0].GoodsSnapshotId)
	if err != nil {
		log.Context(ctx).Warnf("获取商品快照失败: orderNo=%s, snapshotId=%d, error=%v", orderNo, orderItems[0].GoodsSnapshotId, err)
		return ""
	}

	// 提取商品图片URL
	if goodsSnapshot != nil &&
		goodsSnapshot.Snapshot.V != nil &&
		goodsSnapshot.Snapshot.V.Goods != nil &&
		goodsSnapshot.Snapshot.V.Goods.Image != nil &&
		goodsSnapshot.Snapshot.V.Goods.Image.V != nil {
		return goodsSnapshot.Snapshot.V.Goods.Image.V.Url
	}

	return ""
}
