package service

import (
	"context"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) ExhibitorApplyList(ctx context.Context, in *v1.ExhibitorApplyListRequest) (*v1.ExhibitorApplyListReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	l := log.Context(ctx)
	applies, total, err := s.expoApply.Search(ctx, &models.ExpoExhibitorApplySearch{
		Company: in.Company,
		Start:   in.Start,
		End:     in.End,
		Status:  in.Status,
		Page:    in.Page,
		Size:    in.Size,
	})
	if err != nil {
		l.<PERSON>rf("expoApply.Search Err:%v", err)
		return nil, err
	}

	items := make([]*v1.ExhibitorApply, 0, len(applies))
	for _, apply := range applies {
		items = append(items, &v1.ExhibitorApply{
			Id:        int64(apply.ID),
			Status:    apply.Status,
			Company:   apply.Company,
			Website:   apply.Website,
			Contact:   apply.Contact,
			Phone:     apply.Phone,
			Email:     apply.Email,
			BoothSize: apply.Booth,
			CreatedAt: apply.CreatedAt.Unix(),
		})
	}

	return &v1.ExhibitorApplyListReply{
		Items: items,
		Total: total,
	}, nil
}

func (s *Service) SetExhibitorApplyStatus(ctx context.Context, in *v1.SetExhibitorApplyStatusRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoApply.SetStatus(ctx, in.Id, in.Status)
	if err != nil {
		l.Errorf("expoApply.SetStatus Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}
