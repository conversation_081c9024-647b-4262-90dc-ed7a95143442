package service

import (
	"context"

	userCenterv1 "api-expo/api/user_center/v1"

	"github.com/go-kratos/kratos/v2/log"
)

type UserInfo struct {
	UserID              string
	Nickname            string
	WikiNo              string
	AvatarAddress       string
	VipIcon             string
	DarenIcon           string
	UserIdentity        string
	userStatus          int32
	nickNameColor       string
	userIdentityNewIcon string
	IsFirmoffer         bool // 是否是审核
}

// fetchUserInfoBatch 批量获取用户信息
func (s *Service) fetchUserInfoBatch(ctx context.Context, UserIds []string) (map[string]*UserInfo, error) {
	if len(UserIds) == 0 {
		return make(map[string]*UserInfo), nil
	}
	userInfo, err := s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
		UserIds: UserIds,
	})
	if err != nil {
		log.Context(ctx).Errorf("获取用户信息批量查询失败: %v", err)
		return make(map[string]*UserInfo), nil
	}

	result := make(map[string]*UserInfo)
	if userInfo == nil || userInfo.Message == nil {
		log.Context(ctx).Warnf("用户中心返回的数据为空")
		return result, nil
	}

	for _, user := range userInfo.Message {
		if user == nil || user.UserId == "" {
			continue
		}
		result[user.UserId] = &UserInfo{
			UserID:              user.UserId,
			Nickname:            user.NickName,
			WikiNo:              user.WikiFxNumber,
			AvatarAddress:       user.AvatarAddress,
			VipIcon:             user.EnterpriseVIcon2,
			DarenIcon:           user.DarenIcon,
			UserIdentity:        user.UserIdentityNewIcon,
			userStatus:          user.UserStatus,
			userIdentityNewIcon: user.UserIdentityNewIcon,
			IsFirmoffer:         user.IsFirmoffer,
		}
	}
	return result, nil
}
