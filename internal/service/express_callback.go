package service

import (
	"context"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/pkg/express"

	"github.com/go-kratos/kratos/v2/log"
)

// ExpressPushCallback 处理快递100推送回调
func (s *Service) ExpressPushCallback(ctx context.Context, req *v1.ExpressPushCallbackRequest) (*v1.ExpressPushCallbackReply, error) {
	l := log.Context(ctx)
	l.Infof("ExpressPushCallback 收到快递推送回调请求 ExpressPushCallbackRequest %v ", req)

	if req.Sign == "" {
		return &v1.ExpressPushCallbackReply{
			Result:     false,
			ReturnCode: "400",
			Message:    "签名验证不能为空",
		}, nil
	}

	// 创建快递客户端进行签名验证
	callbackData, err := s.expressClient.ValidateCallback(&express.CallbackRequest{
		Sign:  req.Sign,
		Param: req.Param,
	})
	if err != nil {
		l.Errorf("验证回调数据失败: %v %v", err, req.Param)
		return &v1.ExpressPushCallbackReply{
			Result:     false,
			ReturnCode: "400",
			Message:    "签名验证失败",
		}, nil
	}

	if err = s.SyncExpressInfoIfNeeded(ctx, callbackData.LastResult.Nu, "push", "", true); err != nil {
		l.Errorf("保存快递推送结果失败: %s", callbackData.LastResult.Nu)
		return &v1.ExpressPushCallbackReply{
			Result:     false,
			ReturnCode: "500",
			Message:    "处理失败",
		}, nil
	}

	l.Infof("成功处理快递推送回调")
	return &v1.ExpressPushCallbackReply{
		Result:     true,
		ReturnCode: "200",
		Message:    "成功",
	}, nil
}
