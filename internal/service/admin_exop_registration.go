package service

import (
	"api-expo/internal/util"
	"cmp"
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"slices"
	"strconv"
	"strings"
	"time"

	v1 "api-expo/api/expo/v1"
	usercenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"
	"api-expo/pkg/id"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
)

const (
	commentContent = "我报名了{0}"
	urlimage       = "https://expoliveimgs.zy223.com"
)

var langs = []string{"ar", "de", "en", "es", "fil", "fr", "hi", "id", "it", "ja", "ko", "ms", "pt", "ru", "th", "tr", "vi", "zh-cn", "zh-hk"}

var refuseReason = map[int]string{
	1: "电话号码填写有误",
	2: "E-Mail填写有误",
	3: "行业填写有误",
	4: "身份填写有误",
	5: "公司填写有误",
	6: "职位填写有误",
}

type ApplySuccessPushMessage struct {
	BusinessId        string
	BusinessCategory  string
	Title             map[string]string
	Content           map[string]string
	UserId            string
	PushTimestamp     int64
	CustomInformation CustomerInfo
}
type CustomerInfo struct {
	TicketId  string `json:"TicketId"`
	ExpoImage string `json:"ExpoImage"`
	Language  string `json:"Language"`
	ExpoId    string `json:"ExpoId"`
	JumpType  string `json:"JumpType"`
	Href      string `json:"Href"`
}
type ExpoExtraContent struct {
	WebCover           string `protobuf:"bytes,1,opt,name=webCover,json=webCover,proto3" json:"webCover"`
	AppCover           string `protobuf:"bytes,2,opt,name=appCover,json=appCover,proto3" json:"appCover"`
	WebBackgroundImage string `protobuf:"bytes,3,opt,name=webBackgroundImage,json=webBackgroundImage,proto3" json:"webBackgroundImage"`
	AppBackgroundImage string `protobuf:"bytes,4,opt,name=appBackgroundImage,json=appBackgroundImage,proto3" json:"appBackgroundImage"`
	WebShareBackground string `protobuf:"bytes,5,opt,name=webShareBackground,json=webShareBackground,proto3" json:"webShareBackground"`
	AppShareBackground string `protobuf:"bytes,6,opt,name=appShareBackground,json=appShareBackground,proto3" json:"appShareBackground"`
	Lang               string `protobuf:"bytes,7,opt,name=lang,json=lang,proto3" json:"lang"`
	CustomerEmail      string `protobuf:"bytes,10,opt,name=customerEmail,json=customerEmail,proto3" json:"customerEmail"`
	AdmissionNotice    string `protobuf:"bytes,11,opt,name=admissionNotice,json=admissionNotice,proto3" json:"admissionNotice"`
}

func (s *Service) ApplyAudit(ctx context.Context, in *v1.ApplyAuditRequest) (*v1.ApplyAuditReply, error) {
	l := log.Context(ctx)
	single, err := s.participant.GetByTicketId(ctx, in.ApplyId)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	userinfo, err := s.user.GetBasicUserInfo(ctx, &usercenterv1.GetUserWikiNumbersRequest{
		UserIds: []string{single.UserId},
	})
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	expoinfo, err := s.expo.Get(ctx, single.ExpoId)
	if err != nil {
		l.Errorf("user.CreateExpoPreUser Err:%v", err)
		return nil, innErr.ErrBadRequest
	}
	isRegister := single.IsResister

	if in.Status == v1.ApplyAudit_APPLY_AUDIT_Pass && len(single.ApplyUserid) == 0 {
		userId := single.ApplyUserid
		searchUser, err := s.user.GetUserByPhoneNumber(ctx, &usercenterv1.GetUserByPhoneNumberRequest{
			AreaCode:    single.AreaCode,
			PhoneNumber: single.Phone,
		})
		if err != nil {
			l.Errorf("GetUserByPhoneNumber Err:%v", err)
		}

		if len(searchUser.UserId) > 0 {
			isRegister = true
			userId = searchUser.UserId
		} else {
			createUserId, err := s.user.CreateExpoPreUser(ctx, &usercenterv1.CreateExpoPreUserRequest{
				ExpoId:      single.ExpoId,
				ReleaseId:   int64(single.ID),
				AreaCode:    single.AreaCode,
				PhoneNumber: single.Phone,
				Email:       single.Email,
			})
			if err != nil {
				l.Errorf("user.CreateExpoPreUser Err:%v", err)
			} else {
				userId = createUserId.UserId
			}
		}
		ticketCode := util.GenerateCode(int(single.ExpoId))
		err = s.participant.ApplyAudit(ctx, in.ApplyId, in.Status, in.Creator, userId, isRegister, in.RefuseReason, ticketCode)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}

	} else {
		err = s.participant.ApplyAudit(ctx, in.ApplyId, in.Status, in.Creator, single.ApplyUserid, isRegister, in.RefuseReason, "")
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
	}
	nickname := ""
	if len(userinfo.List) > 0 {
		nickname = userinfo.List[0].NickName
	}

	now := time.Now().UTC()
	if single.IsCommentNotice == 1 && in.Status == v1.ApplyAudit_APPLY_AUDIT_Pass {
		comment := &models.ExpoComment{
			ID:           id.CreatePrimaryId("COM"),
			ExpoID:       single.ExpoId,
			UserID:       single.UserId,
			Content:      commentContent,
			ContentType:  int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_REGISTRATION),
			Status:       2,
			LanguageCode: "zh-cn",
			CreatedAt:    now,
			UpdatedAt:    now,
			NickName:     nickname,
			CountryCode:  single.CountryCode,
			Ip:           single.Ip,
		}
		err = s.comment.Add(ctx, comment)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
	}

	if in.Status == v1.ApplyAudit_APPLY_AUDIT_Pass {
		if len(single.UserId) > 0 {
			user, err := s.user.GetUserEnterpriseCode(ctx, &usercenterv1.GetUserEnterpriseCodeRequest{
				UserId: single.UserId,
			})

			if err != nil {
				l.Errorf("expo.GetUserEnterpriseCode Err:%v", err)
			}
			if len(user.EnterpriseCode) > 0 {
				expoExhibitor, err := s.expoExhibitor.GetByExpoIdAndTraderCode(ctx, single.ExpoId, user.EnterpriseCode)
				if err != nil {
					l.Errorf("expo.GetByExpoIdAndTraderCode Err:%v", err)
				}
				if expoExhibitor.ExpoId > 0 {
					_, err := s.expoExhibitorEmployee.Add(ctx, &models.ExpoExhibitorEmployee{
						Model:       gorm.Model{},
						ExpoId:      single.ExpoId,
						ExhibitorId: int64(expoExhibitor.ID),
						EmployeeId:  int64(single.ID),
						Type:        0,
						UserId:      single.UserId,
						Enable:      true,
					})
					if err != nil {
						l.Errorf("expoExhibitorEmployee.Add Err:%v", err)
					}
					err = s.participant.UpdateIsEmployee(ctx, in.ApplyId)
					if err != nil {
						l.Errorf("UpdateIsEmployee.Add Err:%v", err)
					}
				}

			}
		}

		err = s.expo.RegistrantsIncr(ctx, single.ExpoId)
		if err != nil {
			l.Errorf("expo.RegistrantsIncr Err:%v", err)
		}
		go s.PushMessageMessage(single.LanguageCode, expoinfo.Langs, single.UserId, expoinfo.Name, single.ExpoId, int64(single.ID))
	}

	return &v1.ApplyAuditReply{}, nil

}
func (s *Service) PushMessageMessage(languageCode, expoLangs, userId, expoName string, expoId, applyId int64) {

	title := "展会报名成功"
	content := "恭喜您，已经成功报名{0}.请在入场前不要卸载APP,以便参会入场使用"
	titleMap := make(map[string]string)
	contentMap := make(map[string]string)
	for _, lang := range langs {
		titleMap[lang] = title                            //i18n.GetWithChineseValueDefaultEnglish(title, lang)
		contentMap[lang] = util.Format(content, expoName) //fmt.Sprintf(strings.ReplaceAll(i18n.GetWithChineseValueDefaultEnglish(content, lang), "{0}", "%s"), expoName)
	}
	if len(languageCode) == 0 {
		languageCode = "en"
	}
	CustomInformation := CustomerInfo{
		TicketId:  strconv.FormatInt(int64(applyId), 10),
		ExpoImage: GetPushImg(languageCode, expoLangs),
		Language:  "zh-cn",
		ExpoId:    strconv.FormatInt(expoId, 10),
		JumpType:  "3",
		Href:      "",
	}
	message := ApplySuccessPushMessage{
		BusinessId:        strconv.FormatInt(applyId, 10),
		BusinessCategory:  "Tickets",
		Title:             titleMap,
		Content:           contentMap,
		UserId:            userId,
		PushTimestamp:     time.Now().UTC().UnixMilli(),
		CustomInformation: CustomInformation,
	}
	data, err := json.Marshal(message)
	fmt.Println(string(data))
	if err != nil {
		log.Fatal(err)
	}
	err = s.notification.Publish(data)
	if err != nil {
		log.Errorf("notification.Publish Err:%v", err)
	}
}
func GetPushImg(lang string, content string) string {
	var langCol []ExpoExtraContent
	err := json.Unmarshal([]byte(content), &langCol)
	if err != nil {
		return ""
	}
	result := ""
	for _, val := range langCol {
		if strings.EqualFold(val.Lang, lang) {
			result = urlimage + val.WebCover
			break
		}
	}
	if len(result) == 0 && lang != "en" {
		result = GetPushImg("en", content)
	}
	return result
}

func (s *Service) ApplyRefuseInfo(ctx context.Context, in *v1.ApplyRefuseInfoRequest) (*v1.ApplyRefuseInfoReply, error) {
	l := log.Context(ctx)
	single, err := s.participant.GetByTicketId(ctx, in.ApplyId)
	if err != nil {
		l.Errorf("expoCommunity.GetByExpoId Err:%v", err)
		return nil, innErr.ErrBadRequest
	}
	reasonRefuse := ""
	if reason, ok := refuseReason[int(single.RefuseReason)]; ok {
		reasonRefuse = reason
	}
	return &v1.ApplyRefuseInfoReply{
		Creator:      single.AuditUser,
		CreatedAt:    single.AuditAt.Unix(),
		RefuseReason: reasonRefuse,
	}, nil
}

func (s *Service) RefuseSetting(ctx context.Context, in *v1.RefuseSettingRequest) (*v1.RefuseSettingReply, error) {
	list := make([]*v1.RefuseSetting, 0, 6)
	for k, v := range refuseReason {
		list = append(list, &v1.RefuseSetting{
			Code: int32(k),
			Name: v,
		})
	}
	slices.SortFunc(list, func(i, j *v1.RefuseSetting) int {
		return cmp.Compare(i.Code, j.Code)
	})

	return &v1.RefuseSettingReply{
		List: list,
	}, nil
}
