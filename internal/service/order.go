package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
	userCenterv1 "gold_store/api/user_center/v1"
	"gold_store/internal/models"
	"gold_store/pkg/express"
	"gold_store/pkg/id"
	"gold_store/pkg/vgoods"

	"github.com/airunny/wiki-go-tools/alarm"
	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/recovery"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) OrderFilter(ctx context.Context, _ *v1.OrderFilterRequest) (*v1.OrderFilterReply, error) {
	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	return &v1.OrderFilterReply{
		Groups: []*v1.OrderFilterGroup{
			{
				Name: i18n.GetWithDefaultEnglish("60004", languageCode), // 类目
				Id:   "1000",
				Values: []*v1.OrderFilterValue{
					{
						Id:   "-1",
						Name: i18n.GetWithDefaultEnglish("59922", languageCode), // 全部
					},
					{
						Id:   strconv.Itoa(int(v1.OrderSource_VIP)),
						Name: i18n.GetWithDefaultEnglish("59863", languageCode), // VIP
					},
					{
						Id:   strconv.Itoa(int(v1.OrderSource_VPS)),
						Name: i18n.GetWithDefaultEnglish("60002", languageCode), // VPS
					},
					{
						Id:   strconv.Itoa(int(v1.OrderSource_REPORT)),
						Name: i18n.GetWithDefaultEnglish("60003", languageCode), // 报告
					},
					{
						Id:   strconv.Itoa(int(v1.OrderSource_EA)),
						Name: i18n.GetWithDefaultEnglish("60005", languageCode), // EA
					},
					{
						Id:   strconv.Itoa(int(v1.OrderSource_EXHIBITION)),
						Name: i18n.GetWithDefaultEnglish("60006", languageCode), // 展会
					},
					{
						Id:   strconv.Itoa(int(v1.OrderSource_STORE)),
						Name: i18n.GetWithDefaultEnglish("60007", languageCode), // 商城
					},
				},
			},
		},
	}, nil
}

func (s *Service) preCheck(ctx context.Context, in *v1.CreateOrderRequest) (*orderPreCheckResult, error) {
	if in.GoodsId == "" {
		return nil, innErr.ErrBadRequest
	}

	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	if in.Quantity <= 0 {
		return nil, innErr.ErrBadRequest
	}

	goods, err := s.goods.Get(ctx, in.GoodsId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("goods.Get Err:%v", err)
		return nil, err
	}
	// 如果没找到，报参数错误即可
	if err != nil {
		return nil, innErr.WithMessage(innErr.ErrGoodsOff, i18n.GetWithDefaultEnglish("61905", languageCode)) // 商品已下架
	}

	if goods.Status == v1.GoodsStatus_GoodsStatusOff {
		return nil, innErr.WithMessage(innErr.ErrGoodsOff, i18n.GetWithDefaultEnglish("61905", languageCode)) // 商品已下架
	}

	skus, err := s.sku.FindByGoodsId(ctx, in.GoodsId)
	if err != nil {
		l.Errorf("sku.FindByGoodsId Err:%v", err)
		return nil, err
	}

	var userAddress *models.Address
	if in.AddressId > 0 {
		userAddress, err = s.address.Get(ctx, uint(in.AddressId), userId)
		if err != nil {
			l.Errorf("address.Get Err:%v", err)
			return nil, err
		}
	}

	var (
		selectedSpec = make(map[string]string, len(in.Specs))
		matchedSku   *models.SKU
	)

	for _, spec := range in.Specs {
		selectedSpec[spec.SpecId] = spec.ValueId
	}

	// 定位sku
	for _, sku := range skus {
		match := true
		for _, spec := range sku.Specs.V {
			valueId, ok := selectedSpec[spec.SpecId]
			if !ok {
				return nil, innErr.WithMessage(innErr.ErrPriceChanged, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
			}

			if valueId != spec.ValueId {
				match = false
				break
			}
		}

		if match {
			matchedSku = sku
			break
		}
	}

	// 没有匹配到SKU
	if matchedSku != nil {
		// 不可用
		if matchedSku.Disable {
			return nil, innErr.WithMessage(innErr.ErrPriceChanged, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
		}

		if matchedSku.Stock == 0 {
			return nil, innErr.WithMessage(innErr.ErrSellOut, i18n.GetWithDefaultEnglish("62036", languageCode)) // 售罄
		}

		// 库存不足
		if matchedSku.Stock < in.Quantity {
			return nil, innErr.WithMessage(innErr.ErrOutOfStock, i18n.GetWithDefaultEnglish("62035", languageCode)) // 库存不足
		}
	}

	// 计算价格
	var (
		goodsDetail = s.getGoodsDetail(ctx, goods, skus, selectedSpec)
		totalAmount = goodsDetail.Price * float32(in.Quantity)
		shippingFee float32
	)

	if in.Method != v1.PaymentMethod_POINTS && in.OperationTicket == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61916", languageCode)) // 当前不可兑换
	}

	payInstance, ok := getPayment(in.Method)
	if !ok {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61906", languageCode)) // 支付方式不正确
	}

	if goodsDetail.BuyLimit > 0 && in.Quantity > goodsDetail.BuyLimit {
		return nil, innErr.WithMessage(innErr.ErrBuyLimit, i18n.GetWithDefaultEnglish("62572", languageCode)) // 限购数为{0}
	}

	verifyRes, err := payInstance.Verify(ctx, &VerifyParams{
		UserId:          userId,
		OperationTicket: in.OperationTicket,
		TotalAmount:     in.TotalAmount,
		GoodsId:         in.GoodsId,
	})
	if err != nil {
		l.Errorf("payInstance.Verify Err:%v", err)
		return nil, err
	}

	freeShipping := goods.FreeShipping
	if goods.Category != v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL {
		freeShipping = verifyRes.FreeShipping
	}

	if in.Method != v1.PaymentMethod_POINTS {
		totalAmount = 0
	}

	if !freeShipping {
		// TODO 计算运费
	}

	if !freeShipping {
		totalAmount += shippingFee
	}

	if in.TotalAmount > 0 && in.TotalAmount != totalAmount {
		return nil, innErr.WithMessage(innErr.ErrPriceChanged, i18n.GetWithDefaultEnglish("61917", languageCode)) // 商品价格发生变化，请重新购买
	}

	return &orderPreCheckResult{
		Request:       in,
		OriginGoods:   goods,
		OriginSkus:    skus,
		UserAddress:   userAddress,
		GoodsDetail:   goodsDetail,
		SelectedSkuId: goodsDetail.SelectedSkuId,
		TotalAmount:   totalAmount,
		ShippingFee:   shippingFee,
		GoodsNameCh:   goods.Name,
	}, nil
}

func (s *Service) OrderTotalAmount(ctx context.Context, in *v1.OrderTotalAmountRequest) (*v1.OrderTotalAmountReply, error) {
	if in.GoodsId == "" {
		return nil, innErr.ErrBadRequest
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	goods, err := s.goods.Get(ctx, in.GoodsId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("goods.Get Err:%v", err)
		return nil, err
	}

	// 如果没找到，报商品下架
	if err != nil {
		return nil, innErr.WithMessage(innErr.ErrGoodsOff, i18n.GetWithDefaultEnglish("61905", languageCode)) // 商品已下架
	}

	var (
		userAddress *models.Address
		userId, _   = icontext.UserIdFrom(ctx)
	)

	if in.AddressId > 0 {
		userAddress, err = s.address.Get(ctx, uint(in.AddressId), userId)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("address.Get Err:%v", err)
			return nil, err
		}
	}

	if goods.Category == v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL && userAddress == nil {
		userAddress, err = s.address.GetDefault(ctx, userId)
		// 这里发生错误不影响主流程
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("address.GetDefault Err:%v", err)
		}

		// 如果没有默认的，获取最后一条
		if userAddress == nil {
			userAddress, err = s.address.GetUserLast(ctx, userId)
			if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
				l.Errorf("address.GetUserLast Err:%v", err)
			}
		}
	}

	// 计算价格
	var (
		shippingFee float32
	)

	payInstance, ok := getPayment(in.Method)
	if !ok {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61906", languageCode)) // 支付方式不正确
	}

	verifyRes, err := payInstance.Verify(ctx, &VerifyParams{
		UserId:          userId,
		OperationTicket: in.OperationTicket,
		GoodsId:         in.GoodsId,
		NotCheck:        true,
	})
	if err != nil {
		l.Errorf("payInstance.Verify Err:%v", err)
		return nil, err
	}

	freeShipping := goods.FreeShipping
	if goods.Category != v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL {
		freeShipping = verifyRes.FreeShipping
	}

	if !freeShipping {
		// TODO 计算运费
	}

	// 如果不支持派送，返回code为409
	return &v1.OrderTotalAmountReply{
		ShippingFee: shippingFee,
		Address:     s.addressToGRPC(userAddress),
		Code:        "",
	}, nil
}

func (s *Service) PreCheck(ctx context.Context, in *v1.CreateOrderRequest) (*v1.PreCheckReply, error) {
	preRes, err := s.preCheck(ctx, in)
	if err != nil {
		return nil, err
	}

	return &v1.PreCheckReply{
		TotalAmount: preRes.TotalAmount,
		ShippingFee: preRes.ShippingFee,
		Address:     s.addressToGRPC(preRes.UserAddress),
		GoodsDetail: preRes.GoodsDetail,
	}, nil
}

func (s *Service) CreateOrder(ctx context.Context, in *v1.CreateOrderRequest) (*v1.CreateOrderReply, error) {
	checkRes, err := s.preCheck(ctx, in)
	if err != nil {
		return nil, err
	}

	var (
		userId, _       = icontext.UserIdFrom(ctx)
		goods           = checkRes.OriginGoods
		goodsDetail     = checkRes.GoodsDetail
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	// 实物商品需要地址
	if goodsDetail.Category == v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL && in.AddressId <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "address is required")
	}

	if len(goodsDetail.Specs) != len(in.Specs) {
		return nil, innErr.ErrBadRequest
	}

	// 如果没有匹配到sku_id
	if goodsDetail.SelectedSkuId == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
	}

	// 如果不是实物商品，这里需要将规格值拿出来，匹配成虚拟商品参数;TODO 这块逻辑可以单独拆出去
	var selectedSku *v1.GoodsSku
	for _, sku := range goodsDetail.Skus {
		if goodsDetail.SelectedSkuId == sku.SkuId {
			selectedSku = sku
			break
		}
	}

	// 这里多做一步校验
	if selectedSku == nil {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
	}

	var (
		vpsLevel    string
		vpsZone     string
		vpsLanguage int
		vipYear     int
		vipMonth    int
		vipDay      int
	)

	if goodsDetail.Category != v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL {
		goodsSpecValueMapping := make(map[string]map[string]*v1.GoodsSpecValue, len(selectedSku.Specs))
		for _, spec := range checkRes.OriginGoods.Specs.V {
			valueMapping := make(map[string]*v1.GoodsSpecValue, len(spec.Values))
			for _, value := range spec.Values {
				valueMapping[value.Id] = value
			}
			goodsSpecValueMapping[spec.Id] = valueMapping
		}

		for _, spec := range selectedSku.Specs {
			switch spec.SpecId {
			case "SP00004": // VIP 时间
				specValueMapping, ok := goodsSpecValueMapping[spec.SpecId]
				if !ok {
					return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
				}

				originSpecValue, ok := specValueMapping[spec.ValueId]
				if !ok {
					return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
				}

				var intValue int
				intValue, err = strconv.Atoi(originSpecValue.Name)
				if err != nil {
					return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
				}

				switch spec.UnitId {
				case "60019": // 天
					vipDay = intValue * int(in.Quantity)
				case "60021": // 月
					vipMonth = intValue * int(in.Quantity)
				case "60023": // 年
					vipYear = intValue * int(in.Quantity)
				}
			case "SP00005": // VPS 配置
				vpsLevel = spec.ValueId
			case "SP00006": // VPS 区域
				vpsZone = spec.ValueId
			case "SP00007": // VPS 语言
				vpsLanguage, err = strconv.Atoi(spec.ValueId)
				if err != nil {
					return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62037", languageCode)) // 商品配置发生变化，请重新购买
				}
			}
		}
	}

	// 这里需要分布式锁
	key := createOrderKey(userId)
	release, err := s.locker.TryLock(ctx, key, time.Minute*5)
	if err != nil {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "wait a moment.")
	}
	defer release()

	var (
		l        = log.Context(ctx)
		reqId, _ = icontext.RequestIdFrom(ctx)
	)

	// 1、先扣库存，这里不和下面的库操作放在一起，防止长事务影响库性能
	var rowsAffected int64
	rowsAffected, err = s.sku.DecreaseStock(ctx, checkRes.SelectedSkuId, in.Quantity)
	if err != nil {
		l.Errorf("DecreaseStock Err:%v", err)
		return nil, err
	}

	if rowsAffected == 0 {
		return nil, innErr.WithMessage(innErr.ErrOutOfStock, i18n.GetWithDefaultEnglish("62035", languageCode)) // 库存不足
	}
	// 如果后续逻辑有异常，这里做事务补偿
	defer func() {
		rcvErr := recover()
		if err != nil || rcvErr != nil {
			_, defErr := s.sku.IncreaseStock(ctx, checkRes.SelectedSkuId, in.Quantity)
			if defErr != nil {
				l.Errorf("sku.IncreaseStock Err:%v", defErr)
				alarm.FeiShuAlarm(reqId, fmt.Sprintf("库存回退失败：sku:%s，数量：%d", checkRes.SelectedSkuId, in.Quantity))
			}
		}
	}()

	// 2、创建订单
	var orderRes *orderCreateResult
	orderRes, err = s.orderCreate(ctx, checkRes)
	if err != nil {
		l.Errorf("orderCreate Err:%v", err)
		return nil, err
	}

	goodsNameTranslate := make(map[string]string)
	if goods.Translate != nil && goods.Translate.V != nil {
		for k, v := range goods.Translate.V {
			if v.Name == "" {
				continue
			}
			goodsNameTranslate[k] = v.Name
		}
	}

	// 3、支付订单
	var (
		payReq = &orderPayParams{
			UserId:             userId,
			Method:             in.Method,
			GoodsId:            in.GoodsId,
			GoodsName:          goodsDetail.Name,
			GoodsImageURL:      goodsDetail.Image.Url,
			Category:           goodsDetail.Category,
			OperationTicket:    in.OperationTicket,
			OrderNo:            orderRes.OrderNo,
			PaymentNo:          orderRes.PaymentNo,
			TotalAmount:        orderRes.TotalAmount,
			VpsLevel:           vpsLevel,
			VpsZone:            vpsZone,
			VpsLanguage:        vpsLanguage,
			VipYear:            vipYear,
			VipMonth:           vipMonth,
			VipDay:             vipDay,
			GoodsNameTranslate: goodsNameTranslate,
		}
		payRes *orderPayResult
	)

	payRes, err = s.orderPay(ctx, payReq)
	if err != nil {
		l.Errorf("orderPay Err:%v", err)
		return nil, err
	}
	defer func() {
		rcvErr := recover()
		if err != nil || rcvErr != nil {
			defErr := payRes.Release()
			if defErr != nil {
				l.Errorf("payRes.Release Err:%v", defErr)
				alarm.FeiShuAlarm(reqId, fmt.Sprintf("支付回退失败：operation_ticket:%s", in.OperationTicket))
			}
		}
	}()

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	var (
		afterDate             = time.Now().AddDate(0, 0, 2)
		estimatedShippingTime = time.Date(afterDate.Year(), afterDate.Month(), afterDate.Day(), 0, 0, 0, 0, time.UTC)
	)

	// 4、更新支付单信息
	err = s.payment.UpdatePayment(ctx, &models.Payment{
		PaymentNo:   orderRes.PaymentNo,
		OperationNo: payRes.OperationId,
		PaidTotal:   payRes.TotalAmount,
		PaidTime:    payRes.PaidTime,
		Status:      v1.PaymentStatus_PaymentStatusPAID,
		Extra: &igorm.CustomValue[*models.PaymentExtra]{
			V: &models.PaymentExtra{
				PayInfo: payReq,
			},
		},
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("payment.UpdatePayment Err:%v", err)
		return nil, err
	}

	orderStatus := v1.OrderStatus_PAID
	if goodsDetail.Category != v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL {
		orderStatus = v1.OrderStatus_COMPLETE
	}

	// 5、更新支付订单
	err = s.order.UpdateStatus(ctx, orderRes.OrderNo, orderStatus, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("order.UpdateStatus Err:%v", err)
		return nil, err
	}

	if goodsDetail.Category == v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL && orderStatus == v1.OrderStatus_PAID {
		// 已下单
		err = s.logisticsDetail.Add(ctx, &models.LogisticsDetail{
			OrderNo:   orderRes.OrderNo,
			Status:    v1.LogisticStepStatus_LogisticStepStatusORDERED,
			Detail:    &igorm.CustomValue[*models.LogisticsDetailExtra]{},
			EventTime: time.Now(),
		}, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("logisticsDetail.Add Err:%v", err)
			return nil, err
		}

		// 订单进入仓库、准备出库
		err = s.logisticsDetail.Add(ctx, &models.LogisticsDetail{
			OrderNo:   orderRes.OrderNo,
			Status:    v1.LogisticStepStatus_LogisticStepStatusPROCESSING,
			Detail:    &igorm.CustomValue[*models.LogisticsDetailExtra]{},
			EventTime: time.Now().Add(time.Second), // 这里加1秒
		}, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("logisticsDetail.Add Err:%v", err)
			return nil, err
		}

		// 预计时间发货
		err = s.logisticsDetail.Add(ctx, &models.LogisticsDetail{
			OrderNo: orderRes.OrderNo,
			Status:  v1.LogisticStepStatus_LogisticStepStatusPROCESSING,
			Detail: &igorm.CustomValue[*models.LogisticsDetailExtra]{
				V: &models.LogisticsDetailExtra{
					EstimatedShippingTime: estimatedShippingTime.Unix(),
				},
			},
			EventTime: time.Now().Add(time.Second), // 这里加1秒
		}, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("logisticsDetail.Add Err:%v", err)
			return nil, err
		}
	}
	return &v1.CreateOrderReply{
		OrderNo: orderRes.OrderNo,
	}, nil
}

func (s *Service) GetOrderCount(ctx context.Context, _ *common.EmptyRequest) (*v1.OrderCountReply, error) {
	var (
		l         = log.Context(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	deliveryCount, err := s.order.GetCountByStatus(ctx, userId, v1.OrderStatus_DELIVERY)
	if err != nil {
		l.Errorf("order.GetCountByStatus Err:%v", err)
		return nil, err
	}

	completeCount, err := s.order.GetCountByStatus(ctx, userId, v1.OrderStatus_COMPLETE)
	if err != nil {
		return nil, err
	}
	return &v1.OrderCountReply{
		Delivery: int32(deliveryCount),
		Complete: int32(completeCount),
	}, nil
}

func (s *Service) GetOrderTab(ctx context.Context, in *v1.GetOrderTabRequest) (*v1.OrderTabReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}
	languageCode, _ := icontext.LanguageCodeFrom(ctx)

	return &v1.OrderTabReply{
		Tabs: []*v1.OrderTab{
			{
				Status: v1.OrderStatus_UNKNOWN,
				Name:   i18n.GetWithDefaultEnglish("59922", languageCode), // 全部
			},
			{
				Status: v1.OrderStatus_UNPAY,
				Name:   i18n.GetWithDefaultEnglish("59923", languageCode), // 待支付
			},
			{
				Status: v1.OrderStatus_DELIVERY,
				Name:   i18n.GetWithDefaultEnglish("59924", languageCode), // 待收货
			},
			{
				Status: v1.OrderStatus_CANCEL,
				Name:   i18n.GetWithDefaultEnglish("59925", languageCode), // 已取消
			},
			{
				Status: v1.OrderStatus_COMPLETE,
				Name:   i18n.GetWithDefaultEnglish("59926", languageCode), // 已完成
			},
		},
	}, nil
}

func (s *Service) GetOrderList(ctx context.Context, in *v1.OrderListRequest) (*v1.OrderListReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	var (
		l         = log.Context(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	sources := make([]v1.OrderSource, 0, len(in.Filters))
	for _, filter := range in.Filters {
		for _, value := range filter.Values {
			if value.Id == "-1" {
				continue
			}

			intSource, err := strconv.ParseInt(value.Id, 10, 64)
			if err != nil {
				return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid filter")
			}
			sources = append(sources, v1.OrderSource(intSource))
		}
	}

	orders, nextOffset, err := s.order.FindOrderByUserId(ctx, &models.FindOrderParams{
		UserId:  userId,
		Offset:  in.Offset,
		Size:    int(in.Size),
		Status:  in.Status,
		Sources: sources,
	})
	if err != nil {
		l.Errorf("order.FindOrderByUserId Err:%v", err)
		return nil, err
	}

	outOrders, err := s.orderListToOrderBase(ctx, orders)
	if err != nil {
		l.Errorf("orderListToOrderBase Err:%v", err)
		return nil, err
	}

	return &v1.OrderListReply{
		Orders: outOrders,
		Offset: nextOffset,
	}, nil
}

func (s *Service) GetOrderDetail(ctx context.Context, in *v1.OrderDetailRequest) (*v1.OrderDetail, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	order, err := s.order.Get(ctx, in.OrderNo, userId)
	if err != nil {
		l.Errorf("order.Get Err:%v", err)
		return nil, err
	}

	orderItems, err := s.orderItem.FindByOrderNo(ctx, order.OrderNo)
	if err != nil {
		l.Errorf("orderItem.FindByOrderNo Err:%v", err)
		return nil, err
	}

	if len(orderItems) <= 0 {
		l.Errorf("orderItems is empty")
		return nil, innErr.ErrInternalServer
	}

	orderItem := orderItems[0]
	goodsSnapshot, err := s.goodsSnapshot.Get(ctx, orderItem.GoodsSnapshotId)
	if err != nil {
		l.Errorf("goods.Get Err:%v", err)
		return nil, err
	}
	goods := goodsSnapshot.Snapshot.V.Goods

	payment, err := s.payment.GetByOrderNo(ctx, order.OrderNo)
	if err != nil {
		l.Errorf("payment.GetByOrderNo Err:%v", err)
		return nil, err
	}

	var (
		deliveryMethod    string
		currentDelivery   *v1.LogisticStep
		email             string
		goodsName         = orderItem.GoodsName
		specDesc          = orderItem.SpecDesc
		priceShow         = priceToMessage(orderItem.PriceUnit, orderItem.Price)
		totalAmountShow   = orderTotalAmountToMessage(languageCode, order.CurrencySymbol, orderItem.TotalAmount, order.PaymentMethod)
		vpsExtra          *v1.VPSExtra
		reportExtra       *v1.ReportExtra
		exhibitionExtra   *v1.ExhibitionExtra
		brokerId          string
		traderName        string
		language          string
		paymentMethodIcon = v1.OrderPaymentMethodIcon_OrderPaymentMethodIcon_UNKNOWN
	)

	switch order.PaymentMethod {
	case v1.PaymentMethod_GOLD:
		paymentMethodIcon = v1.OrderPaymentMethodIcon_OrderPaymentMethodIcon_GOLD
	case v1.PaymentMethod_POINTS, v1.PaymentMethod_TASK, v1.PaymentMethod_GIFT_CARD:
		paymentMethodIcon = v1.OrderPaymentMethodIcon_OrderPaymentMethodIcon_POINTS
	}

	// 获取最新物流信息
	if goods.Category == v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL {
		// 获取物流详情
		var logisticsDetails []*models.LogisticsDetail
		logisticsDetails, err = s.logisticsDetail.FindByOrderNo(ctx, order.OrderNo)
		if err != nil {
			l.Errorf("GetByOrderNoWithDetails Err:%v", err)
			// 不影响主流程
		}

		if len(logisticsDetails) > 0 {
			latestDetail := logisticsDetails[0]
			currentDelivery = &v1.LogisticStep{
				StepName: s.getLocalizedLogisticStepName(latestDetail.Status, languageCode),
				StepDesc: s.getLocalizedLogisticStepDesc(latestDetail, languageCode),
				StepTime: latestDetail.EventTime.Unix(),
				Status:   latestDetail.Status,
			}
		}
		deliveryMethod = i18n.GetWithDefaultEnglish("60027", languageCode) // 快递运输
	}

	if order.Source != v1.OrderSource_STORE && goodsSnapshot.Snapshot.V != nil &&
		goodsSnapshot.Snapshot.V.Goods.Translate.V != nil {
		translate, ok := goodsSnapshot.Snapshot.V.Goods.Translate.V[languageCode]
		if !ok {
			translate, ok = goodsSnapshot.Snapshot.V.Goods.Translate.V["en"]
		}

		if ok && translate.Name != "" {
			goodsName = translate.Name
		}
	}

	if order.Extra != nil && order.Extra.V != nil {
		translateSpecName, ok := order.Extra.V.SpecDesc[languageCode]
		if !ok {
			translateSpecName, ok = order.Extra.V.SpecDesc["en"]
		}

		if ok && translateSpecName != "" {
			specDesc = translateSpecName
		}

		if order.Extra.V.DisplayAmount != "" && order.Extra.V.DisplayAmount != "0" {
			totalAmountShow = order.Extra.V.DisplayAmount
		}
		brokerId = order.Extra.V.VPSBrokerId
		if order.Source == v1.OrderSource_REPORT {
			brokerId = order.Extra.V.ReportTraderCode
		}
		traderName = order.Extra.V.TraderName
		language = order.Extra.V.ReportLanguage
	}

	switch order.Source {
	case v1.OrderSource_VPS:
		vpsExtra = order.Extra.V.ToVPSExtra(languageCode)
	case v1.OrderSource_REPORT:
		reportExtra = order.Extra.V.ToReportExtra(languageCode)
	case v1.OrderSource_EXHIBITION:
		exhibitionExtra = order.Extra.V.ToExhibitionExtra(languageCode)
	}

	// 这里如果是报告订单，需要获取报告详情
	if order.Source == v1.OrderSource_REPORT {
		reportDetail, err := s.virtualGoods.GetReportDetail(ctx, &vgoods.GetReportDetailRequest{
			OrderNo: order.OrderNo,
		})
		if err != nil {
			l.Errorf("virtualGoods.GetReportDetail<%s> Err:%v", order.OrderNo, err)
			return nil, err
		}

		goodsDetails := true
		if order.Extra != nil && order.Extra.V != nil && order.Extra.V.ReportType == 2 {
			goodsDetails = reportDetail.Result.IsClick
		}

		reportExtra = &v1.ReportExtra{
			SendEmail:     reportDetail.Result.Email,
			Language:      reportDetail.Result.Language,
			SendState:     reportDetail.Result.SengEmailStatus,
			GenerateState: reportDetail.Result.CreateStatus,
			GoDetails:     goodsDetails,
		}
	}

	if order.Source == v1.OrderSource_REPORT {
		if traderName != "" {
			goodsName = i18n.GetWithTemplateDataDefaultEnglish(goodsName, languageCode, []string{traderName})
			specDesc = fmt.Sprintf("%s;%s", i18n.GetWithTemplateDataDefaultEnglish(specDesc, languageCode, []string{traderName}), language)
		} else {
			goodsName = i18n.GetWithTemplateDataDefaultEnglish(specDesc, languageCode, []string{""})
			specDesc = fmt.Sprintf("%s;%s", i18n.GetWithTemplateDataDefaultEnglish(specDesc, languageCode, []string{""}), language)
		}
	}

	return &v1.OrderDetail{
		OrderNo:           order.OrderNo,
		GoodsId:           orderItem.GoodsId,
		SkuId:             orderItem.SkuId,
		GoodsName:         goodsName,
		Image:             fullOrderImage(goods.Image.V, order.Source, brokerId),
		Category:          goods.Category,
		Email:             email,
		Symbol:            order.CurrencySymbol,
		Source:            order.Source,
		SourceShow:        orderSourceToMessage(languageCode, order.Source),
		SpecDesc:          specDesc,
		Price:             orderItem.Price,
		PriceShow:         priceShow,
		Quantity:          order.Quantity,
		ShippingFee:       order.ShippingFee,
		TotalAmount:       order.TotalAmount,
		TotalAmountShow:   totalAmountShow,
		TotalPay:          payment.PaidTotal,
		TotalPayShow:      totalAmountShow,
		Status:            order.Status,
		StatusShow:        orderStatusToMessage(languageCode, order.Status),
		PaymentMethod:     order.PaymentMethod,
		OrderTime:         order.CreatedAt.Unix(),
		PayTime:           payment.PaidTime.Unix(),
		DeliveryMethod:    deliveryMethod, // 快递运输
		Address:           s.addressToGRPC(order.Address.V),
		CurrentDelivery:   currentDelivery,
		VpsExtra:          vpsExtra,
		ReportExtra:       reportExtra,
		ExhibitionExtra:   exhibitionExtra,
		SelectedSpecs:     orderItem.Specs.V,
		PaymentMethodIcon: paymentMethodIcon,
	}, nil
}

func (s *Service) GetOrderLogistics(ctx context.Context, in *v1.OrderLogisticsRequest) (*v1.GetOrderLogisticsReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	order, err := s.order.Get(ctx, in.OrderNo, userId)
	if err != nil {
		l.Errorf("order.Get Err:%v", err)
		return nil, err
	}

	if order.Status != v1.OrderStatus_PAID && order.Status != v1.OrderStatus_DELIVERY && order.Status != v1.OrderStatus_COMPLETE {
		return nil, innErr.ErrBadRequest
	}

	var (
		carrierName string
		carrierIcon = emsIcon
	)

	// 如果订单有快递单号，尝试自动同步物流信息
	if order.TrackingNo != "" {
		// 查询物流单是否存在
		logistics, err := s.logistics.GetByTrackingNo(ctx, order.TrackingNo)
		if err != nil {
			l.Errorf("GetByOrderNo Err:%v", err)
			return nil, err
		}
		carrierName = logistics.CarrierName
	}

	logisticsDetails, err := s.logisticsDetail.FindByOrderNo(ctx, in.OrderNo)
	if err != nil {
		l.Errorf("FindByTrackingNo Err:%v", err)
		return nil, err
	}

	// 获取物流信息
	var (
		steps       = make([]*v1.LogisticStep, 0, len(logisticsDetails))
		preStepName string
		stepName    string
	)

	for _, detail := range logisticsDetails {
		stepName = s.getLocalizedLogisticStepName(detail.Status, languageCode)
		currentStepName := stepName
		if stepName == preStepName {
			stepName = ""
		}
		preStepName = currentStepName
		steps = append(steps, &v1.LogisticStep{
			StepName: stepName,
			StepDesc: s.getLocalizedLogisticStepDesc(detail, languageCode),
			StepTime: detail.EventTime.Unix(),
			Status:   detail.Status,
		})
	}

	return &v1.GetOrderLogisticsReply{
		CarrierName: carrierName,
		CarrierIcon: carrierIcon,
		TrackingNo:  order.TrackingNo,
		Address:     s.addressToGRPC(order.Address.V),
		Steps:       steps,
	}, nil
}

func (s *Service) GetUserOrderCount(ctx context.Context, in *v1.GetUserOrderCountRequest) (*v1.GetUserOrderCountReply, error) {
	var (
		l      = log.Context(ctx)
		userId = in.UserId
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	count, err := s.order.GetCount(ctx, userId)
	if err != nil {
		l.Errorf("order.GetCount Err:%v", err)
		return nil, err
	}

	storeCount, err := s.order.GetCountBySource(ctx, userId, v1.OrderSource_STORE)
	if err != nil {
		l.Errorf("GetCountBySource Err:%v", err)
		return nil, err
	}

	return &v1.GetUserOrderCountReply{
		Total:      int32(count),
		StoreTotal: int32(storeCount),
	}, nil
}

func (s *Service) orderCreate(ctx context.Context, checkRes *orderPreCheckResult) (*orderCreateResult, error) {
	var (
		l                        = log.Context(ctx)
		userId, _                = icontext.UserIdFrom(ctx)
		in                       = checkRes.Request
		now                      = time.Now()
		goods                    = checkRes.OriginGoods
		goodsDetail              = checkRes.GoodsDetail
		skus                     = checkRes.OriginSkus
		goodsVersion             = goods.UpdatedAt.UTC().Unix()
		clientIP, _              = icontext.ClientIPFrom(ctx)
		languageCode, _          = icontext.LanguageCodeFrom(ctx)
		preferredLanguageCode, _ = icontext.PreferredLanguageCodeFrom(ctx)
		countryCode, _           = icontext.CountryCodeFrom(ctx)
		basicData, _             = icontext.BasicDataFrom(ctx)
		orderMethod              = strconv.Itoa(int(in.Method))
		orderNo                  = id.GenerateId(id.BusinessOrder, id.WithChannel(orderMethod))
		paymentNo                = id.GenerateId(id.BusinessPayment, id.WithChannel(orderMethod))
		platform, _              = icontext.PlatformFrom(ctx)
		orderPlatorm             v1.Platform
		err                      error
	)

	switch platform {
	case icontext.IOS:
		orderPlatorm = v1.Platform_IOS
	case icontext.Android:
		orderPlatorm = v1.Platform_ANDROID
	case icontext.PC:
		orderPlatorm = v1.Platform_PC
	case icontext.Web:
		orderPlatorm = v1.Platform_WEB
	}

	// 1、获取商品快照
	var goodsSnapshot *models.GoodsSnapshot
	goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goods.GoodsId, goodsVersion)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("goodsSnapshot.GetByGoodsId<%s,%d> Err:%v", goods.GoodsId, goodsVersion, err)
		return nil, err
	}

	// 没有找到，这里新增
	if err != nil {
		goodsSnapshot = &models.GoodsSnapshot{
			GoodsId: goods.GoodsId,
			Version: goodsVersion,
			Snapshot: &igorm.CustomValue[*models.Snapshot]{
				V: &models.Snapshot{
					Goods: goods,
					Skus:  skus,
				},
			},
		}

		err = s.goodsSnapshot.Add(ctx, goodsSnapshot)
		if err != nil && errors.Is(err, ormhelper.ErrDuplicateKey) {
			l.Errorf("goodsSnapshot.Add Err:%v", err)
			return nil, err
		}

		// 冲突，重新获取
		if err != nil {
			goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goods.GoodsId, goodsVersion)
			if err != nil {
				l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
				return nil, err
			}
		}
	}

	var (
		newOrder = &models.Order{
			OrderNo:               orderNo,
			Source:                v1.OrderSource_STORE,
			PaymentMethod:         in.Method,
			Platform:              orderPlatorm,
			UserId:                userId,
			ClientIP:              clientIP,
			LanguageCode:          languageCode,
			PreferredLanguageCode: preferredLanguageCode,
			CountryCode:           countryCode,
			BasicData:             basicData,
			AddressId:             in.AddressId,
			Address: &igorm.CustomValue[*models.Address]{
				V: checkRes.UserAddress,
			},
			Quantity:              in.Quantity,
			EstimatedShippingTime: time.Date(now.Year(), now.Month(), now.Day()+2, 0, 0, 0, 0, time.UTC),
			TotalAmount:           checkRes.TotalAmount,
			ShippingFee:           checkRes.ShippingFee,
			Status:                v1.OrderStatus_UNPAY,
			GoodsName:             checkRes.GoodsNameCh,
			Extra:                 &igorm.CustomValue[*models.OrderExtra]{},
		}

		newOrderItems = []*models.OrderItem{
			{
				OrderNo:         orderNo,
				GoodsId:         in.GoodsId,
				SkuId:           checkRes.SelectedSkuId,
				Price:           goodsDetail.Price,
				GoodsSnapshotId: goodsSnapshot.ID,
				Quantity:        in.Quantity,
				TotalAmount:     checkRes.TotalAmount,
				ShippingFee:     checkRes.ShippingFee,
				GoodsName:       goodsDetail.Name,
				SpecDesc:        goodsDetail.SelectedSpecDesc,
				Specs: &igorm.CustomValue[[]*v1.SpecSelected]{
					V: in.Specs,
				},
			},
		}

		newPayment = &models.Payment{
			PaymentNo:   paymentNo,
			OrderNo:     orderNo,
			TotalAmount: checkRes.TotalAmount,
			PaidMethod:  in.Method,
			Status:      v1.PaymentStatus_PaymentStatusUNPAY,
			Extra:       &igorm.CustomValue[*models.PaymentExtra]{},
		}
	)

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// 1、添加订单
	err = s.order.Add(ctx, newOrder, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("order.Add Err:%v", err)
		return nil, err
	}

	// 3、添加订单项
	err = s.orderItem.BatchAdd(ctx, newOrderItems, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("orderItem.BatchAdd Err:%v", err)
		return nil, err
	}

	// 4、添加支付
	err = s.payment.Add(ctx, newPayment, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("payment.Add Err:%v", err)
		return nil, err
	}

	// 5、为整个商品增加销量信息 TODO ，这里使用消息队列异步处理
	err = s.goodsStatistics.UpdatesSales(ctx, in.GoodsId, in.Quantity, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goodsStatistics.UpdatesSales Err:%v", err)
		return nil, err
	}
	return &orderCreateResult{
		OrderNo:     orderNo,
		PaymentNo:   paymentNo,
		TotalAmount: newPayment.TotalAmount,
	}, nil
}

func (s *Service) orderPay(ctx context.Context, in *orderPayParams) (*orderPayResult, error) {
	var (
		l        = log.Context(ctx)
		reqId, _ = icontext.RequestIdFrom(ctx)
		err      error
	)

	payInstance, ok := getPayment(in.Method)
	if !ok {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid method")
	}

	// 1、核销用户资产
	var payResult *PayResult
	payResult, err = payInstance.Pay(ctx, &PayParams{
		UserId:             in.UserId,
		OrderNo:            in.OrderNo,
		PaymentNo:          in.PaymentNo,
		OperationTicket:    in.OperationTicket,
		TotalAmount:        in.TotalAmount,
		ImageURL:           in.GoodsImageURL,
		GoodsName:          in.GoodsName,
		GoodsId:            in.GoodsId,
		GoodsNameTranslate: in.GoodsNameTranslate,
	})
	if err != nil {
		l.Errorf("payInstance.Verify Err:%v", err)
		return nil, err
	}

	var (
		paidTotal = payResult.PaidTotal
		paidTime  = time.Now()
	)

	// 3、如果是需要商品，这里需要先发放虚拟物品
	switch in.Category {
	case v1.GoodsCategory_GOODS_CATEGORY_VPS: //  VPS
		l.Infof("VPS发放：%s-%s-%d-%s", in.UserId, in.VpsZone, in.VpsLanguage, in.VpsLevel)
		var vpsRes *vgoods.GiveVPSReply
		vpsRes, err = s.virtualGoods.GiveVPS(ctx, &vgoods.GiveVPSRequest{
			UserId:   in.UserId,
			Zone:     in.VpsZone,
			Language: in.VpsLanguage,
			Config:   1,
			Level:    in.VpsLevel,
		})
		if err != nil {
			l.Errorf("virtualGoods.GiveVPS Err:%v", err)
			return nil, err
		}
		l.Infof("give_vps:【%s】【%s】", vpsRes.Data, vpsRes.RequestId)

	case v1.GoodsCategory_GOODS_CATEGORY_VIP: //  VIP
		var vipRes *vgoods.GiveVIPReply
		l.Infof("VIP发放：%s-%d-%d-%d-%v", in.UserId, in.VipYear, in.VipMonth, in.VipDay, paidTotal)
		vipRes, err = s.virtualGoods.GiveVIP(ctx, &vgoods.GiveVIPRequest{
			OrderNo:    in.OrderNo,
			UserId:     in.UserId,
			Year:       in.VipYear,
			Month:      in.VipMonth,
			Day:        in.VipDay,
			PaidAt:     paidTime.Format(time.RFC3339),
			PaidAmount: int(paidTotal),
		})
		if err != nil {
			l.Errorf("virtualGoods.GiveVIP Err:%v", err)
			return nil, err
		}
		l.Infof("give_vip:【%s】", vipRes.Message)
	}

	return &orderPayResult{
		OperationId: payResult.OperationId,
		PaidTime:    paidTime,
		Release: func() error {
			defErr := payInstance.Rollback(ctx, &RollbackParams{
				OperationId: payResult.OperationId,
				Total:       payResult.PaidTotal,
			})
			if err != nil {
				l.Errorf("payInstance.Rollback<%s> err:%v", payResult.OperationId, defErr)
				alarm.FeiShuAlarm(reqId, fmt.Sprintf("支付功能事务补偿失败：operationId:%s", payResult.OperationId))
			}
			return nil
		},
		TotalAmount: paidTotal,
	}, nil
}

func (s *Service) orderListToOrderBase(ctx context.Context, orders []*models.Order) ([]*v1.OrderBase, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		orderNos        = make([]string, 0, len(orders))
		orderItems      []*models.OrderItem
		goodsSnapshots  []*models.GoodsSnapshot
		err             error
	)

	for _, order := range orders {
		orderNos = append(orderNos, order.OrderNo)
	}

	if len(orderNos) > 0 {
		orderItems, err = s.orderItem.FindByOrderNos(ctx, orderNos)
		if err != nil {
			l.Errorf("orderItem.FindByOrderNos Err:%v", err)
			return nil, err
		}
	}

	snapshotIds := make([]uint, 0, len(orderItems))
	for _, orderItem := range orderItems {
		snapshotIds = append(snapshotIds, orderItem.GoodsSnapshotId)
	}

	if len(snapshotIds) > 0 {
		goodsSnapshots, err = s.goodsSnapshot.FindByIds(ctx, snapshotIds)
		if err != nil {
			l.Errorf("goodsSnapshot.FindByIds Err:%v", err)
			return nil, err
		}
	}

	var (
		snapshotMap  = make(map[uint]*models.GoodsSnapshot, len(goodsSnapshots))
		orderItemMap = make(map[string][]*models.OrderItem, len(orderItems))
		out          = make([]*v1.OrderBase, 0, len(orders))
	)

	for _, snapshot := range goodsSnapshots {
		snapshotMap[snapshot.ID] = snapshot
	}

	for _, orderItem := range orderItems {
		items := orderItemMap[orderItem.OrderNo]
		items = append(items, orderItem)
		orderItemMap[orderItem.OrderNo] = items
	}

	for _, order := range orders {
		curOrderItems, ok := orderItemMap[order.OrderNo]
		if !ok {
			continue
		}

		if len(curOrderItems) <= 0 {
			continue
		}

		orderItem := curOrderItems[0]
		goodsSnapshot, ok := snapshotMap[orderItem.GoodsSnapshotId]
		if !ok {
			continue
		}

		var (
			goods             = goodsSnapshot.Snapshot.V.Goods
			goodsName         = orderItem.GoodsName
			specDesc          = orderItem.SpecDesc
			priceShow         = priceToMessage(orderItem.PriceUnit, orderItem.Price)
			totalAmountShow   = orderTotalAmountToMessage(languageCode, order.CurrencySymbol, orderItem.TotalAmount, order.PaymentMethod)
			brokerId          string
			traderName        string
			language          string
			paymentMethodIcon = v1.OrderPaymentMethodIcon_OrderPaymentMethodIcon_UNKNOWN
		)

		switch order.PaymentMethod {
		case v1.PaymentMethod_GOLD:
			paymentMethodIcon = v1.OrderPaymentMethodIcon_OrderPaymentMethodIcon_GOLD
		case v1.PaymentMethod_POINTS, v1.PaymentMethod_TASK, v1.PaymentMethod_GIFT_CARD:
			paymentMethodIcon = v1.OrderPaymentMethodIcon_OrderPaymentMethodIcon_POINTS
		}

		if order.Source != v1.OrderSource_STORE && goodsSnapshot.Snapshot.V != nil &&
			goodsSnapshot.Snapshot.V.Goods.Translate.V != nil {
			translate, ok := goodsSnapshot.Snapshot.V.Goods.Translate.V[languageCode]
			if !ok {
				translate, ok = goodsSnapshot.Snapshot.V.Goods.Translate.V["en"]
			}

			if ok && translate.Name != "" {
				goodsName = translate.Name
			}
		}

		if order.Extra != nil && order.Extra.V != nil {
			translateSpecName, ok := order.Extra.V.SpecDesc[languageCode]
			if !ok {
				translateSpecName, ok = order.Extra.V.SpecDesc["en"]
			}

			if ok && translateSpecName != "" {
				specDesc = translateSpecName
			}
			totalAmountShow = order.Extra.V.DisplayAmount
			brokerId = order.Extra.V.VPSBrokerId
			if order.Source == v1.OrderSource_REPORT {
				brokerId = order.Extra.V.ReportTraderCode
			}
			traderName = order.Extra.V.TraderName
			language = order.Extra.V.ReportLanguage
		}

		if order.Source == v1.OrderSource_REPORT {
			if traderName != "" {
				goodsName = i18n.GetWithTemplateDataDefaultEnglish(goodsName, languageCode, []string{traderName})
				specDesc = fmt.Sprintf("%s;%s", i18n.GetWithTemplateDataDefaultEnglish(specDesc, languageCode, []string{traderName}), language)
			} else {
				goodsName = i18n.GetWithTemplateDataDefaultEnglish(specDesc, languageCode, []string{""})
				specDesc = fmt.Sprintf("%s;%s", i18n.GetWithTemplateDataDefaultEnglish(specDesc, languageCode, []string{""}), language)
			}
		}

		out = append(out, &v1.OrderBase{
			OrderNo:           order.OrderNo,
			GoodsId:           orderItem.GoodsId,
			SkuId:             orderItem.SkuId,
			GoodsName:         goodsName,
			Image:             fullOrderImage(goods.Image.V, order.Source, brokerId),
			Source:            order.Source,
			SourceShow:        orderSourceToMessage(languageCode, order.Source),
			PaymentMethod:     order.PaymentMethod,
			TotalAmountShow:   totalAmountShow,
			SpecDesc:          specDesc,
			Price:             orderItem.Price,
			PriceShow:         priceShow,
			Quantity:          orderItem.Quantity,
			TotalAmount:       order.TotalAmount,
			Status:            order.Status,
			StatusShow:        orderStatusToMessage(languageCode, order.Status),
			PaymentMethodIcon: paymentMethodIcon,
		})
	}
	return out, nil
}

func priceToMessage(currencySymbol string, totalAmount float32) string {
	out := fmt.Sprintf("%s%.2f", currencySymbol, totalAmount)
	// 金币
	if strings.TrimSpace(currencySymbol) == "" {
		out = fmt.Sprintf("%.0f", totalAmount)
	}
	return out
}

func orderTotalAmountToMessage(languageCode, symbol string, totalAmount float32, paymentMethod v1.PaymentMethod) string {
	if totalAmount <= 0.00001 {
		return i18n.GetWithDefaultEnglish("59938", languageCode)
	}

	out := ""
	if paymentMethod == v1.PaymentMethod_GOLD || paymentMethod == v1.PaymentMethod_POINTS {
		out = fmt.Sprintf("%.0f", totalAmount)
	} else {
		out = fmt.Sprintf("%s%.2f", symbol, totalAmount)
	}
	return out
}

func orderSourceToMessage(languageCode string, source v1.OrderSource) string {
	var (
		message = ""
		key     = ""
	)

	switch source {
	case v1.OrderSource_STORE:
		key = "60007" // 商城
	case v1.OrderSource_VIP:
		key = "59863" // vip
	case v1.OrderSource_VPS:
		key = "60002" // VPS
	case v1.OrderSource_REPORT:
		key = "60003" // 报告
	case v1.OrderSource_EXHIBITION:
		key = "60006" // 展会
	case v1.OrderSource_EA:
		key = "60005" // ea商城
	}

	if key != "" {
		message = i18n.GetWithDefaultEnglish(key, languageCode)
	}
	return message
}

func orderStatusToMessage(languageCode string, status v1.OrderStatus) string {
	var (
		message = ""
		key     = ""
	)

	switch status {
	case v1.OrderStatus_UNPAY:
		key = "59923" // 待支付
	case v1.OrderStatus_PAID:
		key = "60024" // 已支付
	case v1.OrderStatus_CANCEL:
		key = "59925" // 已取消
	case v1.OrderStatus_DELIVERY:
		key = "59933" // 已发货
	case v1.OrderStatus_COMPLETE:
		key = "59926" // 已完成
	}

	if key != "" {
		message = i18n.GetWithDefaultEnglish(key, languageCode)
	}
	return message
}

func createOrderKey(userId string) string {
	return fmt.Sprintf("create_order:%s", userId)
}

// ================================================ 后台 ===================================================

func (s *Service) AdminOrderList(ctx context.Context, in *v1.AdminOrderListRequest) (*v1.AdminOrderListReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	orders, totalCount, err := s.order.FilterOrder(ctx, &models.FilterOrderParams{
		OrderNo:       in.OrderNo,
		GoodsName:     in.GoodsName,
		PaymentMethod: in.PaymentMethod,
		Status:        in.Status,
		Page:          in.Page,
		Size:          in.Size,
	})
	if err != nil {
		l.Errorf("order.FilterOrder Err:%v", err)
		return nil, err
	}

	var (
		userIds           = make([]string, 0, len(orders))
		userWikiNoMapping = make(map[string]string, len(orders))
	)

	for _, order := range orders {
		userIds = append(userIds, order.UserId)
	}

	if len(userIds) > 0 {
		var userInfo *userCenterv1.GetUserWikiNumbersReply
		userInfo, err = s.user.GetUserWikiNumbers(ctx, &userCenterv1.GetUserWikiNumbersRequest{
			UserIds: userIds,
		})
		if err != nil {
			l.Errorf("GetUserWikiNumbers Err:%v", err)
			userInfo = &userCenterv1.GetUserWikiNumbersReply{List: nil}
		}

		for _, user := range userInfo.List {
			userWikiNoMapping[user.UserId] = user.WikiNumber
		}
	}

	unionOrders, err := s.fullOrders(ctx, orders)
	if err != nil {
		l.Errorf("s.fullOrders Err:%v", err)
		return nil, err
	}

	outOrders := make([]*v1.AdminOrder, 0, len(unionOrders))
	for _, unionOrder := range unionOrders {
		var (
			order         = unionOrder.Order
			orderItems    = unionOrder.OrderItems
			goodsSnapshot = unionOrder.GoodsSnapshot
			payment       = unionOrder.Payment
		)

		if order == nil || len(orderItems) <= 0 || goodsSnapshot == nil || payment == nil {
			continue
		}

		var (
			orderItem = orderItems[0]
			wikifxNo  = userWikiNoMapping[order.UserId]
		)

		outOrders = append(outOrders, &v1.AdminOrder{
			OrderNo:       order.OrderNo,
			GoodsId:       orderItem.GoodsId,
			UserId:        order.UserId,
			GoodsName:     orderItem.GoodsName,
			SkuId:         orderItem.SkuId,
			SpecDesc:      orderItem.SpecDesc,
			Price:         orderItem.Price,
			Quantity:      orderItem.Quantity,
			Address:       s.addressToGRPC(order.Address.V),
			TotalAmount:   order.TotalAmount,
			PaymentTotal:  payment.PaidTotal,
			ShippingFee:   order.ShippingFee,
			PaymentMethod: order.PaymentMethod,
			Status:        order.Status,
			CreatedAt:     order.CreatedAt.Unix(),
			WikiNo:        wikifxNo,
		})
	}
	return &v1.AdminOrderListReply{
		Orders: outOrders,
		Total:  int32(totalCount),
	}, nil
}

func (s *Service) AdminOrderDetail(ctx context.Context, in *v1.AdminOrderDetailRequest) (*v1.AdminOrderDetailReply, error) {
	if in.OrderNo == "" {
		return nil, innErr.ErrBadRequest
	}
	l := log.Context(ctx)

	order, err := s.order.GetByOrderNo(ctx, in.OrderNo)
	if err != nil {
		l.Errorf("order.GetByOrderNo Err:%v", err)
		return nil, err
	}

	fullOrders, err := s.fullOrders(ctx, []*models.Order{order})
	if err != nil {
		l.Errorf("fullOrders Err:%v", err)
		return nil, err
	}

	if len(fullOrders) <= 0 {
		l.Error("empty fullOrders")
		return nil, innErr.ErrBadRequest
	}

	userInfo, err := s.user.GetUserWikiNumbers(ctx, &userCenterv1.GetUserWikiNumbersRequest{
		UserIds: []string{order.UserId},
	})
	if err != nil {
		l.Errorf("GetUserWikiNumbers Err:%v", err)
		userInfo = &userCenterv1.GetUserWikiNumbersReply{List: nil}
	}

	var (
		unionOrder    = fullOrders[0]
		orderItems    = unionOrder.OrderItems
		goodsSnapshot = unionOrder.GoodsSnapshot
		payment       = unionOrder.Payment
		userWikiNo    string
	)

	if len(orderItems) <= 0 || goodsSnapshot == nil || payment == nil {
		l.Error("empty order attach")
		return nil, innErr.ErrBadRequest
	}
	// 这里保护好自己
	if userInfo != nil && len(userInfo.List) > 0 {
		userWikiNo = userInfo.List[0].WikiNumber
	}
	orderItem := orderItems[0]

	var (
		logistics                            *models.Logistics
		logisticsDetails                     []*models.LogisticsDetail
		carrierName, carrierIcon, trackingNo string
	)

	if order.TrackingNo != "" {
		logistics, err = s.logistics.GetByOrderNo(ctx, in.OrderNo)
		if err != nil {
			l.Errorf("GetByOrderNo Err:%v", err)
			return nil, err
		}

		carrierName = logistics.CarrierName
		carrierIcon = emsIcon
		trackingNo = logistics.TrackingNo
	}

	logisticsDetails, err = s.logisticsDetail.FindByTrackingNo(ctx, order.TrackingNo)
	if err != nil {
		l.Errorf("FindByOrderNo Err:%v", err)
		return nil, err
	}

	// 获取物流信息
	steps := make([]*v1.LogisticStep, 0, len(logisticsDetails))
	for _, detail := range logisticsDetails {
		stepName := s.getLocalizedLogisticStepName(detail.Status, "zh-cn")
		stepDesc := s.getLocalizedLogisticStepDesc(detail, "zh-cn")

		steps = append(steps, &v1.LogisticStep{
			StepName: stepName,
			StepDesc: stepDesc,
			StepTime: detail.EventTime.Unix(),
			Status:   detail.Status,
		})
	}

	return &v1.AdminOrderDetailReply{
		OrderNo:       order.OrderNo,
		GoodsId:       orderItem.GoodsId,
		UserId:        order.UserId,
		GoodsName:     orderItem.GoodsName,
		SkuId:         orderItem.SkuId,
		SpecDesc:      orderItem.SpecDesc,
		Price:         orderItem.Price,
		Quantity:      orderItem.Quantity,
		Address:       s.addressToGRPC(order.Address.V),
		TotalAmount:   order.TotalAmount,
		PaymentTotal:  payment.PaidTotal,
		ShippingFee:   order.ShippingFee,
		PaymentMethod: order.PaymentMethod,
		Status:        order.Status,
		CreatedAt:     order.CreatedAt.Unix(),
		WikiNo:        userWikiNo,
		PaidAt:        payment.PaidTime.Unix(),
		CarrierName:   carrierName,
		CarrierIcon:   carrierIcon,
		TrackingNo:    trackingNo,
		Steps:         steps,
	}, nil
}

func (s *Service) OrderDeliver(ctx context.Context, in *v1.OrderDeliverRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	order, err := s.order.GetByOrderNo(ctx, in.OrderNo)
	if err != nil {
		l.Errorf("order.GetByOrderNo Err:%v", err)
		return nil, err
	}

	if order.TrackingNo != "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前订单已经发货")
	}

	if order.Status != v1.OrderStatus_PAID {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前订单不可发货")
	}

	// 检查是否存在物流单
	logisticCount, err := s.logistics.CountByTrackingNo(ctx, in.TrackingNo)
	if err != nil {
		l.Errorf("logistics.CountByTrackingNo Err:%v", err)
		return nil, err
	}

	if logisticCount > 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前物流单已经存在")
	}

	tx := s.order.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// 更新订单状态
	var rowUpdated int64
	rowUpdated, err = s.order.SetTrackingNo(ctx, in.OrderNo, in.TrackingNo, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("order.SetTrackingNo Err:%v", err)
		return nil, err
	}

	// 这里需要判断影响行数，避免并发操作
	if rowUpdated <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前订单已经发货")
	}

	carrierName := "EMS"
	carrierCode := "ems"
	//获得快递公司CarrierName
	if in.CarrierName != "" {
		carrierCode = express.GetCompanyCode(in.CarrierName)
		carrierName = in.CarrierName
	}

	// 添加物流信息
	err = s.logistics.Add(ctx, &models.Logistics{
		OrderNo:      in.OrderNo,
		TrackingNo:   in.TrackingNo,
		CarrierName:  carrierName,
		CarrierCode:  carrierCode,
		ShippingFee:  order.ShippingFee,
		ShippingTime: time.Now(),
		SignTime:     time.Now(),
		Status:       v1.LogisticStepStatus_LogisticStepStatusSHIPPED,
		DeliveryTime: time.Now(),
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("logistics.Add Err:%v", err)
		return nil, err
	}

	// 物流详情绑定物流单号
	err = s.logisticsDetail.SetTrackingNoByOrderNo(ctx, in.OrderNo, in.TrackingNo, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("SetTrackingNoByOrderNo Err:%v", err)
		return nil, err
	}

	// 发货后立即查询一次物流信息，并尝试订阅推送
	go func() {
		// 使用新的context避免事务影响
		syncCtx := context.Background()
		ctx = icontext.WithRequestId(ctx, reqid.GenRequestID())
		defer recovery.CatchGoroutinePanicWithContext(syncCtx)

		// 尝试使用推送模式同步物流信息
		if err := s.SyncExpressWithBothModes(syncCtx, in.TrackingNo, order.LanguageCode, ""); err != nil {
			log.Context(syncCtx).Warnf("发货后同步物流信息失败: %v", err)
		}
	}()

	return &common.EmptyReply{}, nil
}

// SyncExpressWithBothModes 同时支持推送和拉取模式的快递同步
func (s *Service) SyncExpressWithBothModes(ctx context.Context, trackingNo, LanguageCode, phone string) error {
	// 从数据库获取快递信息
	logistics, err := s.logistics.GetByTrackingNo(ctx, trackingNo)
	if err != nil {
		log.Context(ctx).Errorf("查询物流信息失败: %v", err)
		return err
	}

	if logistics == nil {
		log.Context(ctx).Warnf("未找到快递单号的物流信息: %s", trackingNo)
		return nil
	}

	// 如果启用推送模式且提供了回调地址，先尝试订阅推送
	if err = s.SubscribeExpress(ctx, trackingNo, logistics.CarrierCode, phone, s.business.Express.CallbackUrl); err != nil {
		log.Context(ctx).Errorf("订阅快递推送失败，将使用拉取模式: %v trackingNo：%s", err, trackingNo)
		// 订阅失败不影响拉取模式，继续执行
	} else {
		log.Context(ctx).Infof("成功订阅快递推送: %s", trackingNo)
	}
	// 使用拉取模式
	return s.SyncExpressInfoIfNeeded(ctx, trackingNo, "sync", phone, true)
}

func (s *Service) fullOrders(ctx context.Context, orders []*models.Order) ([]*UnionOrder, error) {
	var (
		l              = log.Context(ctx)
		orderNos       = make([]string, 0, len(orders))
		orderItems     []*models.OrderItem
		payments       []*models.Payment
		goodsSnapshots []*models.GoodsSnapshot
		err            error
	)

	for _, order := range orders {
		orderNos = append(orderNos, order.OrderNo)
	}

	if len(orderNos) > 0 {
		orderItems, err = s.orderItem.FindByOrderNos(ctx, orderNos)
		if err != nil {
			l.Errorf("orderItem.FindByOrderNos Err:%v", err)
			return nil, err
		}

		payments, err = s.payment.FindByOrderNos(ctx, orderNos)
		if err != nil {
			l.Errorf("payment.FindByOrderNos Err:%v", err)
			return nil, err
		}
	}

	snapshotIds := make([]uint, 0, len(orderItems))
	for _, orderItem := range orderItems {
		snapshotIds = append(snapshotIds, orderItem.GoodsSnapshotId)
	}

	if len(snapshotIds) > 0 {
		goodsSnapshots, err = s.goodsSnapshot.FindByIds(ctx, snapshotIds)
		if err != nil {
			l.Errorf("goodsSnapshot.FindByIds Err:%v", err)
			return nil, err
		}
	}

	var (
		snapshotMap  = make(map[uint]*models.GoodsSnapshot, len(goodsSnapshots))
		orderItemMap = make(map[string][]*models.OrderItem, len(orderItems))
		paymentMap   = make(map[string]*models.Payment, len(payments))
		outOrders    = make([]*UnionOrder, 0, len(orders))
	)

	for _, snapshot := range goodsSnapshots {
		snapshotMap[snapshot.ID] = snapshot
	}

	for _, orderItem := range orderItems {
		items := orderItemMap[orderItem.OrderNo]
		items = append(items, orderItem)
		orderItemMap[orderItem.OrderNo] = items
	}

	for _, payment := range payments {
		paymentMap[payment.OrderNo] = payment
	}

	for _, order := range orders {
		orderItem, ok := orderItemMap[order.OrderNo]
		if !ok {
			continue
		}

		goodsSnapshot, ok := snapshotMap[orderItem[0].GoodsSnapshotId]
		if !ok {
			continue
		}

		payment, ok := paymentMap[order.OrderNo]
		if !ok {
			continue
		}

		outOrders = append(outOrders, &UnionOrder{
			Order:         order,
			OrderItems:    orderItem,
			GoodsSnapshot: goodsSnapshot,
			Payment:       payment,
		})
	}
	return outOrders, nil
}

func (s *Service) shadow(ctx context.Context) (isShadow bool, hasUhost bool) {
	var (
		l = log.Context(ctx)
		//basicData, _ = icontext.BasicDataFrom(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
	)

	if userId != "" {
		userInfo, err := s.virtualGoods.GetUserVPSInfo(ctx, &vgoods.GetUserVPSInfoRequest{
			UserId: userId,
		})
		if err != nil {
			l.Errorf("virtualGoods.GetUserVPSInfo Err:%v", err)
			return true, false
		}

		str, _ := json.Marshal(userInfo)
		l.Infof("UserVPS:%s", string(str))

		if userInfo.Data.HasUhost || userInfo.Data.IsBlackuser {
			return true, userInfo.Data.HasUhost
		}
	}

	//splits := strings.Split(basicData, ",")
	//if len(splits) < 7 {
	//	l.Infof("basicdata1:%s", basicData)
	//	return false
	//}
	//
	//if splits[6] != "1" {
	//	l.Infof("basicdata2:%s", basicData)
	//	return false
	//}

	res, err := s.virtualGoods.CheckShadow(ctx, &vgoods.CheckShadowRequest{
		Point: "12005",
	})
	if err != nil {
		l.Errorf("virtualGoods.CheckShadow Err:%v", err)
		return false, false
	}

	if res.Result.Value == 1 {
		return true, false
	}
	return false, false
}

// getLocalizedLogisticStepName 根据物流状态获取对应的多语言步骤名称
func (s *Service) getLocalizedLogisticStepName(status v1.LogisticStepStatus, languageCode string) string {
	switch status {
	case v1.LogisticStepStatus_LogisticStepStatusORDERED:
		return i18n.GetWithDefaultEnglish("59931", languageCode) // 已下单
	case v1.LogisticStepStatus_LogisticStepStatusPROCESSING:
		return i18n.GetWithDefaultEnglish("59932", languageCode) // 仓库处理中
	case v1.LogisticStepStatus_LogisticStepStatusPICKED:
		return i18n.GetWithDefaultEnglish("59934", languageCode) // 已揽件
	case v1.LogisticStepStatus_LogisticStepStatusTRANSIT:
		return i18n.GetWithDefaultEnglish("59935", languageCode) // 运输中
	case v1.LogisticStepStatus_LogisticStepStatusDELIVERY:
		return i18n.GetWithDefaultEnglish("59936", languageCode) // 派送中
	case v1.LogisticStepStatus_LogisticStepStatusSIGNED:
		return i18n.GetWithDefaultEnglish("59937", languageCode) // 已签收
	default:
		return i18n.GetWithDefaultEnglish("59932", languageCode) // 默认：仓库处理中
	}
}

// getLocalizedLogisticStepDesc 根据物流详情获取多语言步骤描述
func (s *Service) getLocalizedLogisticStepDesc(detail *models.LogisticsDetail, languageCode string) string {
	switch detail.Status {
	case v1.LogisticStepStatus_LogisticStepStatusORDERED:
		return i18n.GetWithDefaultEnglish("60044", languageCode) // 您提交了订单，等待系统确认
	case v1.LogisticStepStatus_LogisticStepStatusPROCESSING:
		out := i18n.GetWithDefaultEnglish("60039", languageCode) // 您的订单已进入仓库，准备出库
		if detail.Detail != nil && detail.Detail.V != nil && detail.Detail.V.EstimatedShippingTime > 0 {
			// 预计{0}月{1}日{2}前发货
			var (
				estimatedShippingTime = time.Unix(detail.Detail.V.EstimatedShippingTime, 0)
				month                 = estimatedShippingTime.Format("01")
				day                   = estimatedShippingTime.Format("02")
				hour                  = estimatedShippingTime.Format("15:04")
			)
			out = i18n.GetWithTemplateDataDefaultEnglish("60040", languageCode, []string{month, day, hour})
		}
		return out
	case v1.LogisticStepStatus_LogisticStepStatusPICKED:
		return i18n.GetWithDefaultEnglish("60042", languageCode) // 包裹已揽收完成
	case v1.LogisticStepStatus_LogisticStepStatusTRANSIT:
		if detail.Detail != nil && detail.Detail.V != nil && detail.Detail.V.City != "" {
			return i18n.GetWithTemplateDataDefaultEnglish("60045", languageCode, []string{detail.Detail.V.City}) // 您的包裹已到达【城市】
		}
		return i18n.GetWithDefaultEnglish("60045", languageCode) // 包裹运输中
	case v1.LogisticStepStatus_LogisticStepStatusDELIVERY:
		if detail.Detail != nil && detail.Detail.V != nil && detail.Detail.V.City != "" {
			city := detail.Detail.V.City
			userName := detail.Detail.V.UserName
			return i18n.GetWithTemplateDataDefaultEnglish("60046", languageCode, []string{city, userName}) // 【城市】快递员正在为您派件
		}
		return i18n.GetWithDefaultEnglish("60046", languageCode) // 正在为您派件
	case v1.LogisticStepStatus_LogisticStepStatusSIGNED:
		if detail.Detail != nil && detail.Detail.V != nil && detail.Detail.V.City != "" {
			return i18n.GetWithTemplateDataDefaultEnglish("60047", languageCode, []string{detail.Detail.V.City}) // 【城市】您的包裹已签收
		}
		return i18n.GetWithTemplateDataDefaultEnglish("60047", languageCode, []string{}) // 您的包裹已签收
	default:
		return i18n.GetWithDefaultEnglish("60039", languageCode) // 处理中
	}
}

// getDefaultLogisticSteps 获取默认的物流步骤
func (s *Service) getDefaultLogisticSteps(languageCode string) []*v1.LogisticStep {
	return []*v1.LogisticStep{
		{
			StepName: i18n.GetWithDefaultEnglish("59931", languageCode), // 已下单
			StepDesc: i18n.GetWithDefaultEnglish("60044", languageCode), // 您提交了订单，等待系统确认
			StepTime: time.Now().Unix(),
			Status:   v1.LogisticStepStatus_LogisticStepStatusORDERED,
		},
		{
			StepName: "",
			StepDesc: i18n.GetWithDefaultEnglish("60039", languageCode), // 您的订单已进入仓库，准备出库
			StepTime: time.Now().Unix(),
			Status:   v1.LogisticStepStatus_LogisticStepStatusORDERED,
		},
	}
}
