package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"path"
	"strconv"
	"time"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	userCenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"
	"api-expo/pkg/upstream"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

func (s *Service) checkExhibitor(ctx context.Context, in *v1.ExpoExhibitorInfo, new bool) error {
	l := log.Context(ctx)
	if in.ExpoId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	//if in.HallId <= 0 {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "会场ID不能为空")
	//}

	//exits, err := s.expoHall.ExistsByExpoId(ctx, in.ExpoId, in.HallId)
	//if err != nil {
	//	return err
	//}
	//
	//if !exits {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "该会场不存在")
	//}

	if in.TraderCode != "" {
		if new {
			exists, err := s.expoExhibitor.ExistsByTraderCode(ctx, in.ExpoId, in.TraderCode)
			if err != nil {
				l.Errorf("expoExhibitor.ExistsByTraderCode Err:%v", err)
				return err
			}

			if exists {
				return innErr.WithMessage(innErr.ErrBadRequest, "该交易商已存在")
			}
		}

		res, err := s.upstream.GetTraders(ctx, &upstream.GetTraderRequest{
			Codes: []string{in.TraderCode},
		})
		if err != nil {
			l.Errorf("upstream.GetTraders Err:%v", err)
			return err
		}

		if len(res.Result) <= 0 {
			return innErr.WithMessage(innErr.ErrBadRequest, "交易商不存在")
		}

		traderInfo := res.Result[0]
		in.TraderName = traderInfo.ShowName
		in.TraderLogo = traderInfo.Logo
		in.TraderMinLogo = traderInfo.Icon
		in.ExhibitorType = v1.ExhibitorType(int32(traderInfo.Project))
	} else {
		if in.TraderName == "" {
			return innErr.WithMessage(innErr.ErrBadRequest, "交易商名称不能为空")
		}

		if in.TraderLogo == "" {
			return innErr.WithMessage(innErr.ErrBadRequest, "交易商logo不能为空")
		}

		if in.TraderMinLogo == "" {
			return innErr.WithMessage(innErr.ErrBadRequest, "交易商小logo不能为空")
		}

		if in.ExhibitorType != v1.ExhibitorType_EXHIBITOR_TYPE_SPONSOR && in.ExhibitorType != v1.ExhibitorType_EXHIBITOR_TYPE_BROKER {
			return innErr.WithMessage(innErr.ErrBadRequest, "交易商类型不正确")
		}
	}

	//userIds := make([]string, 0, len(in.Members))
	//for _, member := range in.Members {
	//	if member.UserId == "" {
	//		return innErr.WithMessage(innErr.ErrBadRequest, "团队成员用户ID不能为空")
	//	}
	//	userIds = append(userIds, member.UserId)
	//}
	//
	//if len(userIds) > 0 {
	//	users, err := s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
	//		UserIds: userIds,
	//	})
	//	if err != nil {
	//		return err
	//	}
	//
	//	if len(users.Message) != len(userIds) {
	//		return innErr.WithMessage(innErr.ErrBadRequest, "用户不存在")
	//	}
	//}

	//if in.BoothLength == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "展位长度不能为空")
	//}
	//
	//if in.BoothWidth == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "展位宽度不能为空")
	//}
	//
	//if in.BoothHeight == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "展位高度不能为空")
	//}

	if in.Booth == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "展位不能为空")
	}

	//if in.Contact == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "联系人不能为空")
	//}
	//
	//if in.PhoneAreaCode == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "电话区域不能为空")
	//}
	//
	//if in.Phone == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "电话不能为空")
	//}
	//
	//if in.Email == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "邮箱不能为空")
	//}
	return nil
}

func (s *Service) GetSponsorLevel(ctx context.Context, in *common.EmptyRequest) (*v1.GetSponsorLevelReply, error) {
	return &v1.GetSponsorLevelReply{
		Items: sponsorLevelItems,
	}, nil
}

func (s *Service) SetExpoExhibitorEmployeeEnable(ctx context.Context, in *v1.SetExpoExhibitorEmployeeEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoExhibitorEmployee.SetEnable(ctx, in.ExhibitorId, in.EmployeeId, in.Enable)
	if err != nil {
		l.Errorf("expoExhibitorEmployee.SetEnable Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) AddExpoExhibitor(ctx context.Context, in *v1.ExpoExhibitorInfo) (*v1.AddExpoExhibitorReply, error) {
	l := log.Context(ctx)
	err := s.checkExhibitor(ctx, in, true)
	if err != nil {
		return nil, err
	}

	id, err := s.expoExhibitor.Add(ctx, &models.ExpoExhibitor{
		ExpoId:        in.ExpoId,
		HallId:        in.HallId,
		TraderCode:    in.TraderCode,
		TraderName:    in.TraderName,
		Logo:          in.TraderLogo,
		MinLogo:       in.TraderMinLogo,
		SponsorLevel:  in.SponsorLevel,
		ExhibitorType: in.ExhibitorType,
		BoothLength:   in.BoothLength,
		BoothWidth:    in.BoothWidth,
		BoothHeight:   in.BoothHeight,
		Booth:         in.Booth,
		Contact:       in.Contact,
		PhoneAreaCode: in.PhoneAreaCode,
		Phone:         in.Phone,
		Email:         in.Email,
		Rank:          in.Rank,
		Enable:        in.Enable,
		Creator:       in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoExhibitorExtra]{
			V: &models.ExpoExhibitorExtra{
				Languages: map[string]*v1.ExpoExhibitorLanguage{},
			},
		},
	})
	if err != nil {
		l.Errorf("expoExhibitor.Add Err:%v", err)
		return nil, err
	}
	return &v1.AddExpoExhibitorReply{
		Id: int64(id),
	}, nil
}

func (s *Service) GetExpoExhibitor(ctx context.Context, in *v1.GetExpoExhibitorRequest) (*v1.ExpoExhibitorInfo, error) {
	l := log.Context(ctx)
	exhibitor, err := s.expoExhibitor.Get(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoExhibitor.Get Err:%v", err)
		return nil, err
	}

	exhibitorEmployeesMap, participantMap, userMap, err := s.getExhibitorEmployees(ctx, []int64{int64(exhibitor.ID)})
	if err != nil {
		l.Errorf("s.getExhibitorEmployees Err:%v", err)
		return nil, err
	}
	return s.expoExhibitorToGRPC(exhibitor, exhibitorEmployeesMap, participantMap, userMap), nil
}

func (s *Service) UpdateExpoExhibitor(ctx context.Context, in *v1.ExpoExhibitorInfo) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.checkExhibitor(ctx, in, false)
	if err != nil {
		return nil, err
	}

	err = s.expoExhibitor.Update(ctx, &models.ExpoExhibitor{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId:        in.ExpoId,
		HallId:        in.HallId,
		TraderCode:    in.TraderCode,
		TraderName:    in.TraderName,
		Logo:          in.TraderLogo,
		MinLogo:       in.TraderMinLogo,
		SponsorLevel:  in.SponsorLevel,
		ExhibitorType: in.ExhibitorType,
		BoothLength:   in.BoothLength,
		BoothWidth:    in.BoothWidth,
		BoothHeight:   in.BoothHeight,
		Booth:         in.Booth,
		Contact:       in.Contact,
		PhoneAreaCode: in.PhoneAreaCode,
		Phone:         in.Phone,
		Email:         in.Email,
		Rank:          in.Rank,
		Enable:        in.Enable,
		Creator:       in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoExhibitorExtra]{
			V: &models.ExpoExhibitorExtra{
				Languages: map[string]*v1.ExpoExhibitorLanguage{},
			},
		},
	})
	if err != nil {
		l.Errorf("expoExhibitor.Add Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoExhibitorEnable(ctx context.Context, in *v1.SetExpoExhibitorEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoExhibitor.UpdateEnable(ctx, in.ExpoId, in.Id, in.Enable)
	if err != nil {
		l.Errorf("expoExhibitor.UpdateEnable Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListExpoExhibitor(ctx context.Context, in *v1.ListExpoExhibitorRequest) (*v1.ListExpoExhibitorReply, error) {
	l := log.Context(ctx)

	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	exhibitors, err := s.expoExhibitor.FindByPage(ctx, in.ExpoId, in.Name, in.Level, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoExhibitor.FindByPage Err:%v", err)
		return nil, err
	}

	total, err := s.expoExhibitor.CountByExpoId(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoExhibitor.CountByExpoId Err:%v", err)
		return nil, err
	}

	exhibitorIds := make([]int64, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		exhibitorIds = append(exhibitorIds, int64(exhibitor.ID))
	}

	exhibitorEmployeesMap, participantMap, userMap, err := s.getExhibitorEmployees(ctx, exhibitorIds)
	if err != nil {
		l.Errorf("s.getExhibitorEmployees Err:%v", err)
		return nil, err
	}

	exhibitorItems := make([]*v1.ExpoExhibitorInfo, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		exhibitorItems = append(exhibitorItems, s.expoExhibitorToGRPC(exhibitor, exhibitorEmployeesMap, participantMap, userMap))
	}

	return &v1.ListExpoExhibitorReply{
		Exhibitors: exhibitorItems,
		Total:      total,
	}, nil
}

func (s *Service) DeleteExpoExhibitor(ctx context.Context, in *v1.DeleteExpoExhibitorRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoExhibitor.Delete(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoExhibitor.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ExpoExhibitorImport(ctx context.Context, in *v1.ExpoExhibitorImportRequest) (*common.EmptyReply, error) {
	if in.ExpoId < 0 {
		return nil, innErr.ErrBadRequest
	}

	l := log.Context(ctx)
	content, err := base64.StdEncoding.DecodeString(in.Data)
	if err != nil {
		return nil, err
	}

	fInfo, err := excelize.OpenReader(bytes.NewReader(content))
	if err != nil {
		l.Errorf("OpenReader Err:%v", err)
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "文件格式不正确!")
	}

	sheetName := fInfo.GetSheetName(0)
	rows, err := fInfo.GetRows(sheetName)
	if err != nil {
		l.Errorf("fInfo.GetRows Err:%v", err)
		return nil, err
	}

	if len(rows) <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "文件格式不正确!")
	}

	headers := rows[0]
	if len(headers) < 7 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "文件至少包含7列!")
	}

	var (
		traderCodes []string
		exhibitors  []*models.ExpoExhibitor
	)

	for index, row := range rows {
		if index == 0 {
			continue
		}

		if len(row) < 7 {
			return nil, innErr.ErrBadRequest
		}

		var (
			traderCode = row[0]
			traderName = row[1]
			logo       = row[2]
			minLogo    = row[3]
			level      v1.SponsorLevel
			booth      = row[5]
			rank       int
		)

		switch row[4] {
		case "全球":
			level = v1.SponsorLevel_SponsorLevel_GOLOBAL
		case "钻石":
			level = v1.SponsorLevel_SponsorLevel_DIAMOND
		case "铂金":
			level = v1.SponsorLevel_SponsorLevel_PLATINUM
		case "黄金":
			level = v1.SponsorLevel_SponsorLevel_GOLD
		case "白银":
			level = v1.SponsorLevel_SponsorLevel_SILVER
		}

		rank, err = strconv.Atoi(row[6])
		if err != nil {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "请输入正确的排序!")
		}

		exhibitors = append(exhibitors, &models.ExpoExhibitor{
			ExpoId:       in.ExpoId,
			HallId:       0,
			TraderCode:   traderCode,
			TraderName:   traderName,
			Logo:         logo,
			MinLogo:      minLogo,
			SponsorLevel: level,
			Booth:        booth,
			Rank:         int32(rank),
			Enable:       true,
			Creator:      in.Creator,
			Extra: &igorm.CustomValue[*models.ExpoExhibitorExtra]{
				V: &models.ExpoExhibitorExtra{
					Languages: map[string]*v1.ExpoExhibitorLanguage{},
				},
			},
		})
		traderCodes = append(traderCodes, traderCode)
	}

	traderMapping := make(map[string]*upstream.TraderInfo, len(traderCodes))
	if len(traderCodes) > 0 {
		var res *upstream.GetTraderReply
		res, err = s.upstream.GetTraders(ctx, &upstream.GetTraderRequest{
			Codes: traderCodes,
		})
		if err != nil {
			l.Errorf("upstream.GetTraders Err:%v", err)
			return nil, err
		}

		if len(res.Message) <= 0 {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "不存在的交易商")
		}

		for _, traderInfo := range res.Result {
			traderMapping[traderInfo.Code] = traderInfo
		}
	}

	for _, exhibitor := range exhibitors {
		if exhibitor.TraderCode == "" {
			continue
		}

		trader, ok := traderMapping[exhibitor.TraderCode]
		if !ok {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, fmt.Sprintf("不存在的交易商[%s]", exhibitor.TraderCode))
		}
		exhibitor.TraderName = trader.ShowName
		exhibitor.Logo = trader.Logo
		exhibitor.MinLogo = trader.Icon
		exhibitor.ExhibitorType = v1.ExhibitorType(int32(trader.Project))
	}

	err = s.expoExhibitor.BatchAdd(ctx, exhibitors)
	if err != nil {
		l.Errorf("expoExhibitor.BatchAdd Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ExpoExhibitorExport(ctx context.Context, in *v1.ExpoExhibitorExportRequest) (*v1.ExpoExhibitorExportReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	var (
		filePath = path.Join(os.TempDir(), fmt.Sprintf("expo_exhibitor_%d.xlsx", time.Now().UnixMicro()))
		excel    = excelize.NewFile()
	)

	writer, err := excel.NewStreamWriter("Sheet1")
	if err != nil {
		l.Errorf("excel.NewStreamWriter Err:%v", err)
		return nil, err
	}

	styleID, err := excel.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Color: "777777",
			Size:  14,
			Bold:  true,
		},
	})

	err = writer.SetRow("A1", []interface{}{"参展商code", "参展商名称", "参展商logo", "参展商小logo", "赞助等级", "展位", "排序"}, excelize.RowOpts{
		Height:       16,
		StyleID:      styleID,
		OutlineLevel: 0,
	})
	if err != nil {
		l.Errorf("writer.SetRow Err:%v", err)
		return nil, err
	}

	total := 1
	for {
		var exhibitors []*models.ExpoExhibitor
		exhibitors, err = s.expoExhibitor.FindByPage(ctx, in.ExpoId, in.Name, in.Level, int(in.Page), int(in.Size))
		if err != nil {
			l.Errorf("expoExhibitor.FindByPage Err:%v", err)
			return nil, err
		}

		for _, exhibitor := range exhibitors {
			var level string
			switch exhibitor.SponsorLevel {
			case v1.SponsorLevel_SponsorLevel_SILVER:
				level = "白银"
			case v1.SponsorLevel_SponsorLevel_GOLD:
				level = "黄金"
			case v1.SponsorLevel_SponsorLevel_PLATINUM:
				level = "铂金"
			case v1.SponsorLevel_SponsorLevel_DIAMOND:
				level = "钻石"
			case v1.SponsorLevel_SponsorLevel_GOLOBAL:
				level = "全球"
			default:
				level = "无"
			}

			err = writer.SetRow(fmt.Sprintf("A%d", total+1), []interface{}{
				exhibitor.TraderCode,
				exhibitor.TraderName,
				urlformat.FullPath(exhibitor.Logo, urlTemplate),
				urlformat.FullPath(exhibitor.MinLogo, urlTemplate),
				level,
				exhibitor.Booth,
				exhibitor.Rank,
			})
			if err != nil {
				return nil, err
			}
			total++
		}

		if len(exhibitors) <= 0 {
			break
		}
		in.Page++
	}
	err = writer.Flush()
	if err != nil {
		l.Errorf("writer.Flush Err:%v", err)
		return nil, err
	}

	err = excel.SaveAs(filePath)
	if err != nil {
		l.Errorf("excel.SaveAs Err:%v", err)
		return nil, err
	}

	content := bytes.NewBuffer(nil)
	_, err = excel.WriteTo(content)
	if err != nil {
		l.Errorf("file.WriteTo Err:%v", err)
		return nil, err
	}

	return &v1.ExpoExhibitorExportReply{
		Data: base64.StdEncoding.EncodeToString(content.Bytes()),
	}, nil
}

func (s *Service) SearchExhibitorMember(ctx context.Context, in *v1.SearchExhibitorMemberRequest) (*v1.SearchExhibitorMemberReply, error) {
	if in.Key == "" {
		return &v1.SearchExhibitorMemberReply{}, nil
	}

	l := log.Context(ctx)
	user, err := s.user.GetUserUserIdByWikiNumber(ctx, &userCenterv1.GetUserWikiNumbersRequest{
		UserIds: []string{in.Key},
	})
	if err != nil {
		l.Errorf("user.GetUserUserIdByWikiNumber Err:%v", err)
		return nil, err
	}

	if len(user.List) <= 0 {
		return &v1.SearchExhibitorMemberReply{}, nil
	}

	userId := user.List[0].UserId
	userRes, err := s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
		UserIds: []string{userId},
	})
	if err != nil {
		l.Errorf("user.GetUsersInfo Err:%v", err)
		return nil, err
	}

	if len(userRes.Message) <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "用户不存在")
	}

	exists, err := s.expoExhibitorEmployee.Exists(ctx, in.ExpoId, in.Id, userId)
	if err != nil {
		l.Errorf("expoExhibitorEmployee.ExistsByUserId Err:%v", err)
		return nil, err
	}

	userInfo := userRes.Message[0]
	return &v1.SearchExhibitorMemberReply{
		Avatar:     userInfo.AvatarAddress,
		Name:       userInfo.NickName,
		UserId:     userInfo.UserId,
		WikiNumber: userInfo.WikiFxNumber,
		Exists:     exists,
	}, nil
}

func (s *Service) AddExpoExhibitorMember(ctx context.Context, in *v1.ExpoExhibitorMemberRequest) (*v1.AddExpoExhibitorMemberReply, error) {
	l := log.Context(ctx)

	exists, err := s.expoExhibitorEmployee.Exists(ctx, in.ExpoId, in.Id, in.UserId)
	if err != nil {
		l.Errorf("expoExhibitorEmployee.ExistsByUserId Err:%v", err)
		return nil, err
	}

	if exists {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "用户已存在")
	}

	user, err := s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
		UserIds: []string{in.UserId},
	})
	if err != nil {
		l.Errorf("user.GetUsersInfo Err:%v", err)
		return nil, err
	}

	if len(user.Message) <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "用户不存在")
	}

	newId, err := s.expoExhibitorEmployee.Add(ctx, &models.ExpoExhibitorEmployee{
		ExpoId:      in.ExpoId,
		ExhibitorId: in.Id,
		Type:        models.ExpoExhibitorEmployeeTypeMember,
		UserId:      in.UserId,
		Enable:      true,
	})
	if err != nil {
		l.Errorf("expoExhibitorEmployee.Add Err:%v", err)
		return nil, err
	}
	return &v1.AddExpoExhibitorMemberReply{
		EmployeeId: int64(newId),
	}, nil
}

func (s *Service) DeleteExhibitorMember(ctx context.Context, in *v1.DeleteExhibitorMemberRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.expoExhibitorEmployee.DeleteByUserId(ctx, in.ExpoId, in.Id, in.UserId)
	if err != nil {
		l.Errorf("expoExhibitorEmployee.DeleteByUserId Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) getExhibitorEmployees(ctx context.Context, exhibitorIds []int64) (
	map[int64][]*models.ExpoExhibitorEmployee,
	map[int64]*models.Participant,
	map[string]*userCenterv1.UserInfo,
	error) {
	l := log.Context(ctx)
	employeesValues, err := s.expoExhibitorEmployee.FindByExhibitorIds(ctx, exhibitorIds)
	if err != nil {
		l.Errorf("expoExhibitorEmployee.FindByExhibitorIds Err:%v", err)
		return nil, nil, nil, err
	}

	var (
		employeeIds           = make([]int64, 0, len(employeesValues))
		exhibitorEmployeesMap = make(map[int64][]*models.ExpoExhibitorEmployee, len(employeesValues))
	)

	userIds := make([]string, 0, len(employeesValues))
	for _, employee := range employeesValues {
		employeeIds = append(employeeIds, employee.EmployeeId)
		exhibitorEmployeesMap[employee.ExhibitorId] = append(exhibitorEmployeesMap[employee.ExhibitorId], employee)
		if employee.UserId != "" {
			userIds = append(userIds, employee.UserId)
		}
	}

	var (
		participants   []*models.Participant
		participantMap = make(map[int64]*models.Participant, len(participants))
		userMap        = make(map[string]*userCenterv1.UserInfo, len(participants))
	)

	if len(employeeIds) > 0 {
		participants, err = s.participant.FindByIds(ctx, employeeIds)
		if err != nil {
			l.Errorf("participant.FindByIds Err:%v", err)
			return nil, nil, nil, err
		}

		for _, participant := range participants {
			participantMap[int64(participant.ID)] = participant
			if participant.UserId != "" {
				userIds = append(userIds, participant.UserId)
			}
		}
	}

	if len(userIds) > 0 {
		var userRes *userCenterv1.GetUsersReply
		userRes, err = s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
			UserIds: userIds,
		})
		if err != nil {
			l.Errorf("user.GetUsersInfo Err:%v", err)
			return nil, nil, nil, err
		}

		for _, user := range userRes.Message {
			userMap[user.UserId] = user
		}
	}
	return exhibitorEmployeesMap, participantMap, userMap, nil
}

func (s *Service) expoExhibitorToGRPC(
	in *models.ExpoExhibitor,
	employees map[int64][]*models.ExpoExhibitorEmployee,
	participants map[int64]*models.Participant,
	users map[string]*userCenterv1.UserInfo,
) *v1.ExpoExhibitorInfo {
	var (
		outEmployees = make([]*v1.ExhibitorEmployeeInfo, 0, len(employees))
		members      = make([]*v1.ExhibitorEmployeeInfo, 0, len(employees))
		curEmployees = employees[int64(in.ID)]
	)

	for _, employee := range curEmployees {
		var (
			name       string
			avatar     string
			wikiNumber string
		)

		if employee.Type == models.ExpoExhibitorEmployeeTypeEmployee {
			participant, ok := participants[employee.EmployeeId]
			if !ok {
				continue
			}
			name = fmt.Sprintf("%s%s", participant.FirstName, participant.LastName)

			user, ok := users[participant.UserId]
			if ok {
				name = user.NickName
				avatar = user.AvatarAddress
				wikiNumber = user.WikiFxNumber
			}
			if avatar == "" {
				avatar = getLetterAvatar(name)
			}

		} else {
			user, ok := users[employee.UserId]
			if !ok {
				continue
			}
			name = user.NickName
			avatar = user.AvatarAddress
			wikiNumber = user.WikiFxNumber
			if avatar == "" {
				avatar = getLetterAvatar(name)
			}
			members = append(members, &v1.ExhibitorEmployeeInfo{
				EmployeeId: int64(employee.ID),
				Avatar:     avatar,
				Name:       name,
				WikiNumber: wikiNumber,
				Enable:     employee.Enable,
			})
		}
		outEmployees = append(outEmployees, &v1.ExhibitorEmployeeInfo{
			EmployeeId: int64(employee.ID),
			Avatar:     avatar,
			Name:       name,
			WikiNumber: wikiNumber,
			Enable:     employee.Enable,
		})
	}

	return &v1.ExpoExhibitorInfo{
		Id:            int64(in.ID),
		ExpoId:        in.ExpoId,
		HallId:        in.HallId,
		TraderCode:    in.TraderCode,
		TraderName:    in.TraderName,
		TraderLogo:    in.Logo,
		TraderMinLogo: in.MinLogo,
		SponsorLevel:  in.SponsorLevel,
		ExhibitorType: in.ExhibitorType,
		BoothLength:   in.BoothLength,
		BoothWidth:    in.BoothWidth,
		BoothHeight:   in.BoothHeight,
		Booth:         in.Booth,
		Contact:       in.Contact,
		PhoneAreaCode: in.PhoneAreaCode,
		Phone:         in.Phone,
		Email:         in.Email,
		Enable:        in.Enable,
		CreatedAt:     in.CreatedAt.Unix(),
		Creator:       in.Creator,
		Employees:     outEmployees,
		Rank:          in.Rank,
		Members:       members,
	}
}
