package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/express"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-kratos/kratos/v2/log"
)

// ExpressDataUnified 统一的快递数据结构，兼容推送和同步两种模式
type ExpressDataUnified struct {
	Time       string `json:"time"`       // 时间（原始格式）
	FTime      string `json:"ftime"`      // 格式化时间
	Context    string `json:"context"`    // 物流信息描述
	Location   string `json:"location"`   // 当前地点
	Status     string `json:"status"`     // 物流状态名称
	StatusCode string `json:"statusCode"` // 高级物流状态值
	AreaCode   string `json:"areaCode"`   // 行政区域编码
	AreaName   string `json:"areaName"`   // 行政区域名称
	AreaCenter string `json:"areaCenter"` // 行政区域经纬度
	AreaPinYin string `json:"areaPinYin"` // 行政区域拼音
}

// ExpressResultUnified 统一的快递查询结果结构
type ExpressResultUnified struct {
	Message    string               `json:"message"`    // 消息体
	Nu         string               `json:"nu"`         // 单号
	IsCheck    string               `json:"ischeck"`    // 是否签收标记
	Condition  string               `json:"condition"`  // 快递单明细状态标记
	Com        string               `json:"com"`        // 快递公司编码
	Status     string               `json:"status"`     // 通讯状态
	State      string               `json:"state"`      // 快递单当前状态
	Data       []ExpressDataUnified `json:"data"`       // 物流信息
	IsLoop     bool                 `json:"isLoop"`     // 是否循环查询
	ReturnCode string               `json:"returnCode"` // 返回码
}

// ExpressSaveOptions 保存选项，用于区分不同的保存模式和特殊处理
type ExpressSaveOptions struct {
	Mode       string // "sync" 或 "push"
	Phone      string // 手机号码
	PushStatus string // 推送状态（仅推送模式使用）
}

// saveExpressResultUnified 统一的快递结果保存方法
func (s *Service) saveExpressResultUnified(ctx context.Context, logistics *models.Logistics, result *ExpressResultUnified, options *ExpressSaveOptions) error {
	// 转换状态
	status := s.convertExpressSyncStatus(ctx, result.State)
	isCompleted := status == v1.LogisticStepStatus_LogisticStepStatusSIGNED

	// 推送模式的特殊状态处理
	//if options.Mode == "push" && options.PushStatus != "" {
	//	switch options.PushStatus {
	//	case "shutdown":
	//		// 快递已完成，强制设置为已签收
	//		status = v1.LogisticStepStatus_LogisticStepStatusSIGNED
	//		isCompleted = true
	//	case "abort":
	//		// 快递异常终止，记录但不更改为已签收
	//		log.Context(ctx).Warnf("快递异常终止: 单号=%s, 消息=%s", result.Nu, result.Message)
	//	}
	//}

	// 获取最后更新时间
	var lastUpdate time.Time
	if len(result.Data) > 0 && result.Data[0].Time != "" {
		cstLocation, _ := time.LoadLocation("Asia/Shanghai")
		if t, err := time.ParseInLocation("2006-01-02 15:04:05", result.Data[0].Time, cstLocation); err == nil {
			lastUpdate = t
		}
	}

	// 构建 phone 字段，区分同步和推送模式
	phoneField := options.Phone
	if options.Mode == "push" {
		phoneField = fmt.Sprintf("push:%s", options.PushStatus)
	} else if options.Mode == "sync" {
		phoneField = fmt.Sprintf("sync:%s", options.Phone)
	}

	// 保存查询日志
	queryLog := &models.ExpressQueryLog{
		TrackingNo:  logistics.TrackingNo,
		CarrierCode: logistics.CarrierCode,
		CarrierName: logistics.CarrierName,
		Status:      status,
		QueryTime:   time.Now(),
		LastUpdate:  lastUpdate,
		IsCompleted: isCompleted,
		Phone:       phoneField,
		RawResponse: &igorm.CustomValue[*models.ExpressQueryResponse]{
			V: &models.ExpressQueryResponse{
				Message:   result.Message,
				Nu:        result.Nu,
				IsCheck:   result.IsCheck,
				Condition: result.Condition,
				Com:       result.Com,
				Status:    result.Status,
				State:     result.State,
				IsLoop:    result.IsLoop,
				Data:      s.convertUnifiedDataToResponseData(result.Data),
			},
		},
	}

	if err := s.expressQuery.SaveQueryLog(ctx, queryLog); err != nil {
		return err
	}

	// 更新物流主表状态和时间
	logisticsUpdates := map[string]interface{}{
		"status": status,
	}

	// 根据状态更新对应的时间字段
	switch status {
	case v1.LogisticStepStatus_LogisticStepStatusPICKED:
		if !lastUpdate.IsZero() {
			logisticsUpdates["shipping_time"] = lastUpdate
		}
	case v1.LogisticStepStatus_LogisticStepStatusDELIVERY:
		if !lastUpdate.IsZero() {
			logisticsUpdates["delivery_time"] = lastUpdate
		}
	case v1.LogisticStepStatus_LogisticStepStatusSIGNED:
		if !lastUpdate.IsZero() {
			logisticsUpdates["sign_time"] = lastUpdate
		}
	}

	if err := s.logistics.Update(ctx, logistics.OrderNo, logisticsUpdates); err != nil {
		return err
	}

	// 保存物流详情到logistics_detail表
	var latestDetail *models.LogisticsDetail // 保存最新的物流详情用于通知
	for _, data := range result.Data {
		cstLocation, _ := time.LoadLocation("Asia/Shanghai")
		eventTime, _ := time.ParseInLocation("2006-01-02 15:04:05", data.Time, cstLocation)

		detail := &models.LogisticsDetail{
			OrderNo:    logistics.OrderNo,
			TrackingNo: result.Nu,
			DeliveryId: fmt.Sprintf("%s_%s", result.Nu, data.Time), // 生成唯一ID
			Status:     s.convertExpressContextSyncStatus(ctx, data.StatusCode),
			Detail: &igorm.CustomValue[*models.LogisticsDetailExtra]{
				V: &models.LogisticsDetailExtra{
					City:     data.Location,
					Phone:    options.Phone,
					UserName: "",
					Context:  data.Context, // 保存完整的物流描述
				},
			},
			EventTime: eventTime,
		}

		// 保存到logistics_detail表
		if err := s.logisticsDetail.AddIfNotExists(ctx, detail); err != nil {
			log.Context(ctx).Errorf("保存物流详情失败: %v", err)
		}

		// 记录最新的物流详情（时间最晚的）
		if latestDetail == nil || eventTime.After(latestDetail.EventTime) {
			latestDetail = detail
		}
	}

	// 自动更新订单状态
	statusChanged, err := s.updateOrderStatusFromLogisticsSync(ctx, logistics.OrderNo, status)
	if err != nil {
		log.Context(ctx).Errorf("自动更新订单状态失败: %v", err)
	}

	// 只有订单状态发生变更时才发送物流状态通知给用户
	if statusChanged {
		s.sendLogisticsNotification(ctx, logistics.OrderNo, status, latestDetail)
	}

	return nil
}

// convertQueryResponseToUnified 将同步查询结果转换为统一格式
func (s *Service) convertQueryResponseToUnified(_ context.Context, result *express.QueryResponse) *ExpressResultUnified {
	unified := &ExpressResultUnified{
		Message:    result.Message,
		Nu:         result.Nu,
		IsCheck:    result.IsCheck,
		Condition:  result.Condition,
		Com:        result.Com,
		Status:     result.Status,
		State:      result.State,
		IsLoop:     result.IsLoop,
		ReturnCode: result.ReturnCode,
		Data:       make([]ExpressDataUnified, 0, len(result.Data)),
	}

	// 转换数据数组
	for _, data := range result.Data {
		unified.Data = append(unified.Data, ExpressDataUnified{
			Time:       data.Time,
			FTime:      data.Ftime,
			Context:    data.Context,
			Location:   data.Location,
			Status:     data.Status,
			StatusCode: data.StatusCode,
			AreaCode:   data.AreaCode,
			AreaName:   data.AreaName,
			AreaCenter: data.AreaCenter,
			AreaPinYin: data.AreaPinYin,
		})
	}

	return unified
}

// convertCallbackDataToUnified 将推送回调数据转换为统一格式
func (s *Service) convertCallbackDataToUnified(_ context.Context, data *express.CallbackData) *ExpressResultUnified {
	if data.LastResult == nil {
		return &ExpressResultUnified{
			Message:    data.Message,
			Nu:         "",
			ReturnCode: "200",
			State:      "0", // 默认在途状态
			Data:       []ExpressDataUnified{},
		}
	}

	unified := &ExpressResultUnified{
		Message:    data.LastResult.Message,
		Nu:         data.LastResult.Nu,
		IsCheck:    data.LastResult.IsCheck,
		Condition:  data.LastResult.Condition,
		Com:        data.LastResult.Com,
		Status:     data.LastResult.Status,
		State:      data.LastResult.State,
		IsLoop:     data.LastResult.IsLoop,
		ReturnCode: "200",
		Data:       make([]ExpressDataUnified, 0, len(data.LastResult.Data)),
	}

	// 转换TrackData为ExpressDataUnified
	for _, trackData := range data.LastResult.Data {
		unified.Data = append(unified.Data, ExpressDataUnified{
			Time:       trackData.Time,
			FTime:      trackData.FTime,
			Context:    trackData.Context,
			Location:   trackData.Location,
			Status:     trackData.Status,
			StatusCode: trackData.StatusCode,
			AreaCode:   trackData.AreaCode,
			AreaName:   trackData.AreaName,
			AreaCenter: trackData.AreaCenter,
			AreaPinYin: trackData.AreaPinYin,
		})
	}

	return unified
}

// convertExpressContextSyncStatus 状态码推断状态
func (s *Service) convertExpressContextSyncStatus(ctx context.Context, statusCode string) v1.LogisticStepStatus {
	if statusCode == "" {
		return v1.LogisticStepStatus_LogisticStepStatusTRANSIT
	}
	return s.convertExpressSyncStatus(ctx, statusCode)
}

// convertExpressSyncStatus 转换快递状态 - 支持基础状态和高级状态
func (s *Service) convertExpressSyncStatus(_ context.Context, state string) v1.LogisticStepStatus {
	// 快递状态映射
	expressStatusMap := map[string]v1.LogisticStepStatus{
		"0":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 在途 -> 运输中
		"1":  v1.LogisticStepStatus_LogisticStepStatusPICKED,   // 揽收 -> 已揽件
		"2":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 疑难 -> 运输中（需要特殊处理）
		"3":  v1.LogisticStepStatus_LogisticStepStatusSIGNED,   // 签收 -> 已签收
		"4":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 退签 -> 运输中（异常状态）
		"5":  v1.LogisticStepStatus_LogisticStepStatusDELIVERY, // 派件 -> 派送中
		"6":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 退回 -> 运输中（退回中）
		"7":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 转投 -> 运输中
		"8":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 清关 -> 运输中
		"14": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 拒签 -> 运输中（异常状态）
	}

	// 高级状态映射（当使用 resultv2=4 时的详细状态）
	expressAdvancedStatusMap := map[string]v1.LogisticStepStatus{
		"101":  v1.LogisticStepStatus_LogisticStepStatusORDERED,    // 已下单
		"102":  v1.LogisticStepStatus_LogisticStepStatusPROCESSING, // 待揽收 -> 仓库处理中
		"103":  v1.LogisticStepStatus_LogisticStepStatusPICKED,     // 已揽收
		"1001": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,    // 到达派件城市
		"1002": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,    // 干线运输
		"1003": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,    // 转递
		"501":  v1.LogisticStepStatus_LogisticStepStatusDELIVERY,   // 投柜或驿站
		"301":  v1.LogisticStepStatus_LogisticStepStatusSIGNED,     // 本人签收
		"302":  v1.LogisticStepStatus_LogisticStepStatusSIGNED,     // 派件异常后签收
		"303":  v1.LogisticStepStatus_LogisticStepStatusSIGNED,     // 代签
		"304":  v1.LogisticStepStatus_LogisticStepStatusSIGNED,     // 投柜或站签收
		"401":  v1.LogisticStepStatus_LogisticStepStatusTRANSIT,    // 已销单（异常）
		// 疑难状态
		"201": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 超时未签收
		"202": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 超时未更新
		"203": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 拒收
		"204": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 派件异常
		"205": v1.LogisticStepStatus_LogisticStepStatusDELIVERY, // 柜或驿站超时未取
		"206": v1.LogisticStepStatus_LogisticStepStatusDELIVERY, // 无法联系
		"207": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 超区
		"208": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 滞留
		"209": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 破损
		"210": v1.LogisticStepStatus_LogisticStepStatusTRANSIT,  // 销单
		// 清关状态
		"10": v1.LogisticStepStatus_LogisticStepStatusTRANSIT, // 待清关
		"11": v1.LogisticStepStatus_LogisticStepStatusTRANSIT, // 清关中
		"12": v1.LogisticStepStatus_LogisticStepStatusTRANSIT, // 已清关
		"13": v1.LogisticStepStatus_LogisticStepStatusTRANSIT, // 清关异常
	}

	// 优先检查高级状态映射（更精确）
	if status, exists := expressAdvancedStatusMap[state]; exists {
		return status
	}

	// 如果没有高级状态，使用基础状态映射
	if status, exists := expressStatusMap[state]; exists {
		return status
	}

	// 默认状态：运输中
	return v1.LogisticStepStatus_LogisticStepStatusTRANSIT
}

// updateOrderStatusFromLogisticsSync 根据物流状态自动更新订单状态（用于快递同步）
// 返回值：是否有状态变更, 错误信息
func (s *Service) updateOrderStatusFromLogisticsSync(ctx context.Context, orderNo string, logisticStatus v1.LogisticStepStatus) (bool, error) {
	// 获取当前订单状态
	order, err := s.order.GetByOrderNo(ctx, orderNo)
	if err != nil {
		log.Context(ctx).Errorf("获取订单信息失败: %v", err)
		return false, err
	}

	// 只有已支付的订单才能更新为配送状态
	if order.Status != v1.OrderStatus_PAID && order.Status != v1.OrderStatus_DELIVERY {
		log.Context(ctx).Infof("订单状态不允许更新物流状态: 订单号=%s, 当前状态=%s", orderNo, order.Status.String())
		return false, nil
	}

	var targetOrderStatus v1.OrderStatus
	needUpdate := false

	// 根据物流状态决定订单状态
	switch logisticStatus {
	case v1.LogisticStepStatus_LogisticStepStatusPICKED,
		v1.LogisticStepStatus_LogisticStepStatusTRANSIT,
		v1.LogisticStepStatus_LogisticStepStatusDELIVERY:
		// 已揽件、运输中、派送中 -> 待收货
		if order.Status == v1.OrderStatus_PAID {
			targetOrderStatus = v1.OrderStatus_DELIVERY
			needUpdate = true
		}
	case v1.LogisticStepStatus_LogisticStepStatusSIGNED:
		// 已签收 -> 已完成
		if order.Status == v1.OrderStatus_DELIVERY {
			targetOrderStatus = v1.OrderStatus_COMPLETE
			needUpdate = true
		}
	}

	// 执行订单状态更新
	if needUpdate {
		log.Context(ctx).Infof("自动更新订单状态: 订单号=%s, %s -> %s",
			orderNo, order.Status.String(), targetOrderStatus.String())

		if err := s.order.UpdateStatus(ctx, orderNo, targetOrderStatus); err != nil {
			log.Context(ctx).Errorf("更新订单状态失败: %v", err)
			return false, err
		}
		return true, nil // 状态已更新
	}

	return false, nil // 状态未变更
}

// convertUnifiedDataToResponseData 将统一格式的物流数据转换为ExpressDataResponse格式
func (s *Service) convertUnifiedDataToResponseData(unifiedData []ExpressDataUnified) []models.ExpressDataResponse {
	responseData := make([]models.ExpressDataResponse, 0, len(unifiedData))

	for _, data := range unifiedData {
		responseData = append(responseData, models.ExpressDataResponse{
			Time:     data.Time,
			Ftime:    data.FTime,
			Context:  data.Context,
			Location: data.Location,
			Status:   data.Status,
			AreaCode: data.AreaCode,
			AreaName: data.AreaName,
		})
	}
	return responseData
}

// SyncExpressInfoIfNeeded 同步快递信息
func (s *Service) SyncExpressInfoIfNeeded(ctx context.Context, trackingNo, action, phone string, forceSync bool) error {
	l := log.Context(ctx)
	// 1. 从数据库获取快递公司信息
	logistics, err := s.logistics.GetByTrackingNo(ctx, trackingNo)
	if err != nil {
		l.Errorf("查询物流信息失败: %v", err)
		return err
	}

	// 2. 检查是否需要查询
	if !forceSync {
		queryLog, err := s.expressQuery.GetQueryLogByTrackingNo(ctx, trackingNo)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("获取查询日志失败: %v", err)
			return err
		}

		// 到这里说明有查询到信息
		if err == nil {
			// 如果最近1小时已查询过且未完成，则跳过查询
			if time.Since(queryLog.QueryTime) < 30*time.Minute && !queryLog.IsCompleted {
				l.Infof("快递信息最近已查询，跳过同步: %s", trackingNo)
				return nil
			}
		}
	}

	order, err := s.order.GetByOrderNo(ctx, logistics.OrderNo)
	if err != nil {
		l.Errorf("订单不存在: %v", err)
		return err
	}

	lang := "zh"
	if order.LanguageCode != "zh-cn" {
		lang = "en"
	}

	result, err := s.expressClient.Query(ctx, &express.QueryRequest{
		Num:      trackingNo,
		Com:      logistics.CarrierCode,
		Phone:    phone,
		ResultV2: "4",    // 开启高级行政区域解析（推荐）
		Show:     "0",    // json格式
		Order:    "desc", // 降序排列
		Lang:     lang,
	})
	if err != nil {
		l.Errorf("调用快递100 API失败: %v", err)
		return err
	}
	if result.ReturnCode == "500" {
		l.Infof("调用失败 %s,%v ", trackingNo, result.Message)
	}

	// 转换为统一格式
	unifiedResult := s.convertQueryResponseToUnified(ctx, result)
	// 保存查询结果到数据库
	if err = s.saveExpressResultUnified(ctx, logistics, unifiedResult, &ExpressSaveOptions{
		Mode:  action,
		Phone: phone,
	}); err != nil {
		l.Errorf("保存快递查询结果失败: %v", err)
		return err
	}

	l.Infof("成功同步快递信息: %s", trackingNo)
	return nil
}
