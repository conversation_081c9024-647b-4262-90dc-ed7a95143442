package service

import (
	"context"
	stderrors "errors"
	"fmt"
	"strconv"
	"time"

	"api-expo/internal/models"
	"api-expo/internal/util"
	"api-expo/pkg/id"
	"api-expo/pkg/tencentAi"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

const (
	// Redis键前缀
	faceSearchUserLimitPrefix = "face_search:user_limit:"
	faceSearchIPLimitPrefix   = "face_search:ip_limit:"
	faceSearchCachePrefix     = "face_search:cache:"
	faceSearchAlertPrefix     = "face_search:alert:"
)

// CreateFaceGroup 创建人员库
func (s *Service) CreateFaceGroup(ctx context.Context, req *models.FaceGroupCreateRequest) (*models.FaceGroupInfo, error) {
	// 参数验证
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.Name == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库名称不能为空")
	}

	var (
		l           = log.Context(ctx)
		groupID     = s.generateFaceGroupID(req.ExpoID)
		maxFaceNum  = req.MaxFaceNum
		description = req.Description
	)

	// 设置默认值
	if maxFaceNum <= 0 {
		maxFaceNum = 10000
	}
	if description == "" {
		description = fmt.Sprintf("%s人员库", req.Name)
	}

	l.Infof("开始创建人员库: name=%s, expo_id=%s", req.Name, req.ExpoID)

	// 检查人员库名称是否已存在
	exists, err := s.faceGroup.ExistsByName(ctx, req.Name, "")
	if err != nil {
		l.Errorf("检查人员库名称失败: %v", err)
		return nil, err
	}
	if exists {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库名称已存在")
	}

	// 使用事务创建人员库及场景关联
	var result *models.FaceGroupInfo
	err = s.faceGroup.WithTx(ctx, func(txCtx context.Context) error {
		// 创建人员库实体
		group := &models.FaceGroup{
			ID:                 groupID,
			Name:               req.Name,
			Description:        description,
			Tag:                "",
			FaceModelVersion:   "3.0",
			MaxFaces:           maxFaceNum,
			EstimatedFaceCount: 0,
			Status:             models.FaceGroupStatusNormal,
			CreatedAt:          time.Now(),
			UpdatedAt:          time.Now(),
		}

		// 创建人员库
		err := s.faceGroup.Create(txCtx, group)
		if err != nil {
			l.Errorf("创建人员库失败: %v", err)
			return err
		}

		// 创建场景关联
		if req.ExpoID != 0 {
			scene := &models.FaceGroupExpo{
				GroupID:   groupID,
				ExpoID:    req.ExpoID,
				ExpoName:  req.Name,
				Status:    models.FaceGroupExpoStatusNormal,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			}

			err = s.faceGroupExpo.Create(txCtx, scene)
			if err != nil {
				l.Errorf("创建场景关联失败: %v", err)
				return err
			}
		}

		// 调用腾讯云API创建人员库
		tencentReq := &tencentAi.CreateGroupRequest{
			GroupId:          groupID,
			GroupName:        req.Name,
			Tag:              "",
			FaceModelVersion: "3.0",
		}

		_, err = s.tencentAI.CreateGroup(ctx, tencentReq)
		if err != nil {
			l.Errorf("腾讯云创建人员库失败: %v", err)
			return errors.WithMessage(errors.ErrInternalServer, "创建人员库失败")
		}

		// 构建返回结果
		result = &models.FaceGroupInfo{
			GroupID:            groupID,
			Name:               req.Name,
			Description:        description,
			FaceModelVersion:   "3.0",
			MaxFaces:           maxFaceNum,
			EstimatedFaceCount: 0,
			Status:             int(models.FaceGroupStatusNormal),
			CreatedAt:          group.CreatedAt.Format(time.RFC3339),
		}

		return nil
	})

	if err != nil {
		l.Errorf("创建人员库事务失败: %v", err)
		return nil, err
	}

	l.Infof("成功创建人员库: group_id=%s", groupID)
	return result, nil
}

// UploadFacePhotos 照片上传与人脸检测入库
func (s *Service) UploadFacePhotos(ctx context.Context, req *models.FacePhotoUploadRequest) (*models.FaceUploadResult, error) {
	// 参数验证
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.GroupID == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库ID不能为空")
	}
	if req.PhotoURL == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "照片URL不能为空")
	}

	var (
		l           = log.Context(ctx)
		uploadID    = id.NewUploadID()
		maxFaceNum  = req.MaxFaceNum
		minFaceSize = req.MinFaceSize
	)

	// 设置默认值
	if maxFaceNum <= 0 {
		maxFaceNum = 20
	}
	if minFaceSize <= 0 {
		minFaceSize = 60
	}

	l.Infof("开始处理人脸照片上传: upload_id=%s, group_id=%s, photo_url=%s", uploadID, req.GroupID, req.PhotoURL)

	// 预检查阶段 - 验证人员库是否存在且状态正常
	checkRes, err := s.preCheckFaceGroup(ctx, req.GroupID)
	if err != nil {
		l.Errorf("人员库不存在: %v", err)
		return nil, err
	}

	// 调用腾讯云API检测人脸
	detectRes, err := s.detectFacesFromPhoto(ctx, req.PhotoURL)
	if err != nil {
		l.Errorf("人脸检测失败: %v", err)
		return nil, err
	}

	if len(detectRes.FaceInfos) == 0 {
		l.Warnf("照片中未检测到人脸: photo_url=%s", req.PhotoURL)
		return &models.FaceUploadResult{
			UploadID:         uploadID,
			OriginalPhotoURL: req.PhotoURL,
			TotalFaces:       0,
			SuccessFaces:     0,
			FailedFaces:      0,
			Faces:            []*models.FaceUploadResultItem{},
		}, nil
	}

	// 分布式锁保护人员库操作
	lockKey := s.createFaceGroupLockKey(req.GroupID)
	release, err := s.locker.TryLock(ctx, lockKey, time.Minute*3)
	if err != nil {
		l.Errorf("获取人员库锁失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "系统繁忙，请稍后重试")
	}
	defer func() {
		if releaseErr := release(); releaseErr != nil {
			l.Errorf("释放锁失败: key=%s, error=%v", lockKey, releaseErr)
		}
	}()

	// 检查人员库容量
	if checkRes.EstimatedFaceCount+len(detectRes.FaceInfos) > checkRes.MaxFaces {
		return nil, errors.WithMessage(errors.ErrBadRequest,
			fmt.Sprintf("人员库容量不足，当前：%d，最大：%d，本次添加：%d",
				checkRes.EstimatedFaceCount, checkRes.MaxFaces, len(detectRes.FaceInfos)))
	}

	// 批量处理人脸入库
	result, err := s.batchProcessFacePhotos(ctx, req, uploadID, detectRes.FaceInfos)
	if err != nil {
		l.Errorf("批量处理人脸失败: %v", err)
		return nil, err
	}

	l.Infof("人脸照片处理完成: upload_id=%s, total=%d, success=%d, failed=%d",
		uploadID, result.TotalFaces, result.SuccessFaces, result.FailedFaces)

	return result, nil
}

// SearchFacePhotos 用户人脸搜索
func (s *Service) SearchFacePhotos(ctx context.Context, req *models.FaceSearchRequest) (*models.FaceSearchResult, error) {
	var (
		l        = log.Context(ctx)
		searchID = id.NewSearchID()
	)

	l.Infof("开始人脸搜索: search_id=%s, photo_url=%s, groups=%v, page=%d, page_size=%d",
		searchID, req.SearchPhotoURL, req.GroupIDs, req.Page, req.PageSize)

	// 获取用户ID和客户端IP进行频率限制检查
	userID, _ := icontext.UserIdFrom(ctx)
	clientIP, _ := icontext.ClientIPFrom(ctx)

	// 频率限制检查
	if err := s.CheckFaceSearchRateLimit(ctx, userID, clientIP); err != nil {
		l.Warnf("频率限制检查失败: user_id=%s, client_ip=%s, error=%v", userID, clientIP, err)
		return nil, err
	}

	// 检查缓存
	if cachedResult, err := s.GetFaceSearchCachedResult(ctx, req.SearchPhotoURL); err != nil {
		l.Warnf("获取缓存失败: %v", err)
	} else if cachedResult != nil {
		l.Infof("从缓存返回结果: search_id=%s, photo_url=%s, total_results=%d",
			searchID, req.SearchPhotoURL, len(cachedResult.Results))

		// 对缓存结果进行分页处理
		paginatedResult := s.paginateFaceSearchResult(cachedResult, req.Page, req.PageSize)
		paginatedResult.SearchID = searchID
		paginatedResult.FromCache = true

		return paginatedResult, nil
	}

	// 验证人员库状态
	activeGroups, err := s.validateFaceGroups(ctx, req.GroupIDs)
	if err != nil {
		return nil, err
	}

	if len(activeGroups) == 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "没有可用的人员库")
	}

	// 提取有效的人员库ID
	validGroupIDs := make([]string, len(activeGroups))
	for i, group := range activeGroups {
		validGroupIDs[i] = group.ID
	}

	// 先调用人脸检测获取用户上传图片中的人脸坐标
	var detectRes *tencentAi.DetectFaceResponse
	if req.NeedFaceRect {
		detectRes, err = s.detectFacesFromPhoto(ctx, req.SearchPhotoURL)
		if err != nil {
			l.Warnf("腾讯云人脸检测失败，将跳过人脸坐标返回: %v", err)
			// 人脸检测失败不影响搜索功能，设置为nil继续执行
			detectRes = nil
		}
	}

	// 调用腾讯云人脸搜索API
	searchRes, err := s.searchFacesWithTencent(ctx, req.SearchPhotoURL, validGroupIDs)
	// fmt.Printf("searchRes: %+v\n", searchRes)
	if err != nil {
		l.Errorf("腾讯云人脸搜索失败: %v", err)
		return nil, err
	}

	// 处理搜索结果
	result, err := s.processFaceSearchResults(ctx, searchID, req, searchRes, detectRes)
	if err != nil {
		l.Errorf("处理搜索结果失败: %v", err)
		return nil, err
	}

	// 缓存完整搜索结果（不分页）
	if err := s.SetFaceSearchCachedResult(ctx, req.SearchPhotoURL, result); err != nil {
		l.Warnf("缓存搜索结果失败: %v", err)
	}

	// 对结果进行分页处理
	paginatedResult := s.paginateFaceSearchResult(result, req.Page, req.PageSize)
	paginatedResult.FromCache = false

	l.Infof("人脸搜索完成: search_id=%s, total_results=%d, current_page_results=%d",
		searchID, paginatedResult.TotalResults, len(paginatedResult.Results))

	return paginatedResult, nil
}

// GetFaceGroupsByExpo 根据场景获取人员库信息
func (s *Service) GetFaceGroupsByExpo(ctx context.Context, expoID int64) ([]*models.FaceGroupInfo, error) {
	// 参数验证
	if expoID == 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "场景ID不能为空")
	}

	l := log.Context(ctx)

	// 获取场景关联的人员库ID列表
	groupIDs, err := s.faceGroupExpo.GetGroupIDsByExpo(ctx, expoID)
	if err != nil {
		l.Errorf("获取场景人员库ID失败: %v", err)
		return nil, err
	}

	if len(groupIDs) == 0 {
		l.Infof("场景下没有关联的人员库:  expo_id=%s", expoID)
		return []*models.FaceGroupInfo{}, nil
	}

	// 获取人员库详细信息
	groups, err := s.faceGroup.GetActiveByIDs(ctx, groupIDs)
	if err != nil {
		l.Errorf("获取人员库详情失败: %v", err)
		return nil, err
	}

	// 转换为DTO格式
	result := make([]*models.FaceGroupInfo, 0, len(groups))
	for _, group := range groups {
		if group.IsNormal() {
			info := &models.FaceGroupInfo{
				GroupID:            group.ID,
				Name:               group.Name,
				Description:        group.Description,
				FaceModelVersion:   group.FaceModelVersion,
				MaxFaces:           group.MaxFaces,
				EstimatedFaceCount: group.EstimatedFaceCount,
				Status:             int(group.Status),
				CreatedAt:          group.CreatedAt.Format(time.RFC3339),
			}
			result = append(result, info)
		}
	}

	l.Infof("查询场景人员库完成: expo_id=%s, count=%d", expoID, len(result))
	return result, nil
}

// GetPhotoFaces 获取照片中的人脸列表
func (s *Service) GetPhotoFaces(ctx context.Context, photoURL string) (*models.FaceUploadResult, error) {
	// 参数验证
	if photoURL == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "照片URL不能为空")
	}

	l := log.Context(ctx)
	l.Infof("获取照片人脸列表: photo_url=%s", photoURL)

	// 查询数据库中已存储的人脸数据
	relations, err := s.facePhotoRelation.GetActiveByOriginalPhotoURL(ctx, photoURL)
	if err != nil {
		l.Errorf("查询照片人脸数据失败: %v", err)
		return nil, err
	}

	// 转换为返回格式
	faces := make([]*models.FaceUploadResultItem, 0, len(relations))
	for _, relation := range relations {
		if relation.IsNormal() {
			faceRect := relation.GetFaceRect()
			face := &models.FaceUploadResultItem{
				FaceID:       relation.FaceBusinessID,
				QualityScore: relation.QualityScore,
				Rect:         &faceRect,
				Status:       "success",
			}
			faces = append(faces, face)
		}
	}

	result := &models.FaceUploadResult{
		OriginalPhotoURL: photoURL,
		TotalFaces:       len(faces),
		SuccessFaces:     len(faces),
		FailedFaces:      0,
		Faces:            faces,
	}

	l.Infof("获取照片人脸列表完成: photo_url=%s, total=%d", photoURL, len(faces))
	return result, nil
}

// preCheckFaceGroup 预检查人员库状态
func (s *Service) preCheckFaceGroup(ctx context.Context, groupID string) (*models.FaceGroup, error) {
	group, err := s.faceGroup.GetActiveByID(ctx, groupID)
	if err != nil {
		if stderrors.Is(err, ormhelper.ErrNotFound) {
			return nil, errors.WithMessage(errors.ErrBadRequest, "人员库不存在")
		}
		return nil, err
	}

	if !group.IsNormal() {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库状态异常")
	}

	return group, nil
}

// detectFacesFromPhoto 从照片中检测人脸
func (s *Service) detectFacesFromPhoto(ctx context.Context, photoURL string) (*tencentAi.DetectFaceResponse, error) {
	// 确保传递给腾讯AI的URL是绝对地址
	absolutePhotoURL := urlformat.FullPath(photoURL)

	detectReq := &tencentAi.DetectFaceRequest{
		Url:                  absolutePhotoURL,
		MaxFaceNum:           s.business.TencentAI.SearchConfig.MaxFaceNum,
		MinFaceSize:          s.business.TencentAI.SearchConfig.MinFaceSize,
		NeedQualityDetection: true,
		NeedFaceAttributes:   true,
		NeedRotateDetection:  true,
		FaceModelVersion:     s.business.TencentAI.FaceModelVersion,
	}

	return s.tencentAI.DetectFace(ctx, detectReq)
}

// batchProcessFacePhotos 批量处理人脸照片入库
func (s *Service) batchProcessFacePhotos(ctx context.Context, req *models.FacePhotoUploadRequest, uploadID string, faceInfos []tencentAi.FaceInfo) (*models.FaceUploadResult, error) {
	var (
		l              = log.Context(ctx)
		successFaces   = 0
		failedFaces    = 0
		resultFaces    = make([]*models.FaceUploadResultItem, 0, len(faceInfos))
		relationsBatch = make([]*models.FacePhotoRelation, 0, len(faceInfos))
	)

	// 处理每个检测到的人脸
	for i, faceInfo := range faceInfos {
		faceBusinessID := id.NewFaceID()
		tencentPersonID := id.NewTencentPersonID()

		// 创建人脸照片关联记录
		relation := &models.FacePhotoRelation{
			FaceBusinessID:   faceBusinessID,
			TencentPersonID:  tencentPersonID,
			GroupID:          req.GroupID,
			OriginalPhotoURL: req.PhotoURL,
			QualityScore:     0, // 默认值，如果有质量信息会设置
			UploadUserID:     req.Operator,
			UploadTime:       time.Now(),
			Status:           models.FacePhotoStatusNormal,
		}

		// 设置质量分数
		if faceInfo.Quality != nil {
			relation.QualityScore = faceInfo.Quality.Score
		}

		// 设置人脸坐标
		relation.SetFaceRect(models.FaceRect{
			X:      faceInfo.FaceRect.X,
			Y:      faceInfo.FaceRect.Y,
			Width:  faceInfo.FaceRect.Width,
			Height: faceInfo.FaceRect.Height,
		})

		// 设置人脸属性
		if faceInfo.Attributes != nil {
			if faceInfo.Attributes.Gender == 1 {
				relation.Gender = models.FaceGenderMale
			} else if faceInfo.Attributes.Gender == 2 {
				relation.Gender = models.FaceGenderFemale
			}
			relation.Age = int8(faceInfo.Attributes.Age)
			relation.Beauty = faceInfo.Attributes.Beauty
		}

		relationsBatch = append(relationsBatch, relation)

		// 构建返回结果项
		resultItem := &models.FaceUploadResultItem{
			FaceID:       faceBusinessID,
			QualityScore: relation.QualityScore,
			Status:       "success",
		}

		resultItem.Rect = &models.FaceRect{
			X:      faceInfo.FaceRect.X,
			Y:      faceInfo.FaceRect.Y,
			Width:  faceInfo.FaceRect.Width,
			Height: faceInfo.FaceRect.Height,
		}

		resultFaces = append(resultFaces, resultItem)
		l.Debugf("处理人脸 %d: face_id=%s, quality=%.2f", i+1, faceBusinessID, relation.QualityScore)
	}

	// 使用事务批量入库
	var err error
	err = s.faceGroup.WithTx(ctx, func(txCtx context.Context) error {
		// 先向腾讯云批量添加人脸
		for _, relation := range relationsBatch {
			// 确保传递给腾讯AI的URL是绝对地址
			absolutePhotoURL := urlformat.FullPath(relation.OriginalPhotoURL)

			createPersonReq := &tencentAi.CreatePersonRequest{
				GroupId:    relation.GroupID,
				PersonId:   relation.TencentPersonID,
				PersonName: relation.FaceBusinessID, // 使用业务ID作为人员名称
				Url:        absolutePhotoURL,
			}

			_, err := s.tencentAI.CreatePerson(ctx, createPersonReq)
			if err != nil {
				// 详细记录腾讯云创建人员失败的错误信息
				l.Errorf("腾讯云创建人员失败: group_id=%s, person_id=%s, person_name=%s, photo_url=%s, error=%v",
					relation.GroupID, relation.TencentPersonID, relation.FaceBusinessID, relation.OriginalPhotoURL, err)

				// 记录失败但不中断事务
				failedFaces++
				// 更新对应结果项状态，包含具体错误信息
				errorMsg := fmt.Sprintf("入库失败: %v", err)
				for _, item := range resultFaces {
					if item.FaceID == relation.FaceBusinessID {
						item.Status = "failed"
						item.ErrorMessage = errorMsg
						break
					}
				}
				continue
			}
			successFaces++
		}

		// 过滤出成功的记录进行数据库入库
		successRelations := make([]*models.FacePhotoRelation, 0)
		for _, relation := range relationsBatch {
			// 检查对应的结果项是否成功
			for _, item := range resultFaces {
				if item.FaceID == relation.FaceBusinessID && item.Status == "success" {
					successRelations = append(successRelations, relation)
					break
				}
			}
		}

		// 批量创建数据库记录
		if len(successRelations) > 0 {
			err := s.facePhotoRelation.BatchCreate(txCtx, successRelations)
			if err != nil {
				l.Errorf("批量创建人脸照片关联失败: %v", err)
				return err
			}

			// 更新人员库统计
			err = s.faceGroup.UpdateFaceCount(txCtx, req.GroupID, len(successRelations))
			if err != nil {
				l.Errorf("更新人员库统计失败: %v", err)
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 返回处理结果
	result := &models.FaceUploadResult{
		UploadID:         uploadID,
		OriginalPhotoURL: req.PhotoURL,
		TotalFaces:       len(faceInfos),
		SuccessFaces:     successFaces,
		FailedFaces:      failedFaces,
		Faces:            resultFaces,
	}

	return result, nil
}

// validateFaceGroups 验证人员库状态
func (s *Service) validateFaceGroups(ctx context.Context, groupIDs []string) ([]*models.FaceGroup, error) {
	groups, err := s.faceGroup.GetActiveByIDs(ctx, groupIDs)
	if err != nil {
		return nil, err
	}

	var activeGroups []*models.FaceGroup
	for _, group := range groups {
		if group.IsNormal() {
			activeGroups = append(activeGroups, group)
		}
	}

	return activeGroups, nil
}

// searchFacesWithTencent 调用腾讯云人脸搜索
func (s *Service) searchFacesWithTencent(ctx context.Context, photoURL string, groupIDs []string) (*tencentAi.SearchFacesResponse, error) {
	// 确保传递给腾讯AI的URL是绝对地址
	absolutePhotoURL := urlformat.FullPath(photoURL)

	searchReq := &tencentAi.SearchFacesRequest{
		Url:                 absolutePhotoURL,
		GroupIds:            groupIDs,
		NeedPersonInfo:      false,
		FaceMatchThreshold:  s.business.TencentAI.SearchConfig.DefaultMatchThreshold,
		NeedRotateDetection: true,
	}

	return s.tencentAI.SearchFaces(ctx, searchReq)
}

// processFaceSearchResults 处理人脸搜索结果
func (s *Service) processFaceSearchResults(ctx context.Context, searchID string, req *models.FaceSearchRequest, searchRes *tencentAi.SearchFacesResponse, detectRes *tencentAi.DetectFaceResponse) (*models.FaceSearchResult, error) {
	l := log.Context(ctx)
	startTime := time.Now()

	var resultItems []*models.FaceSearchResultItem

	// 提取腾讯云返回的PersonID列表
	tencentPersonIDs := make([]string, 0)
	for _, result := range searchRes.Results {
		for _, candidate := range result.Candidates {
			tencentPersonIDs = append(tencentPersonIDs, candidate.PersonId)
		}
	}

	// 批量查询人脸数据
	personMap, err := s.getFaceDataByTencentPersonIDs(ctx, tencentPersonIDs)
	if err != nil {
		l.Errorf("查询人脸数据失败: %v", err)
		return nil, err
	}

	// 处理每个搜索结果
	for _, result := range searchRes.Results {
		for _, candidate := range result.Candidates {
			relation, exists := personMap[candidate.PersonId]
			if !exists {
				continue
			}

			// 根据原始照片URL分组
			photoURL := relation.OriginalPhotoURL
			var resultItem *models.FaceSearchResultItem

			// 查找是否已存在该照片的结果项
			for _, item := range resultItems {
				if item.PhotoURL != "" && item.PhotoURL == photoURL {
					resultItem = item
					break
				}
			}

			// 如果不存在，创建新的结果项
			if resultItem == nil {
				// 获取展会信息（如果是展会场景）
				var exhibitionInfo *models.ExhibitionInfo
				if relation.FaceGroup != nil {
					scenes, err := s.faceGroupExpo.GetByGroupID(ctx, relation.GroupID)
					if err == nil && len(scenes) > 0 {
						scene := scenes[0]
						exhibitionInfo = &models.ExhibitionInfo{
							ExhibitionID:   scene.ExpoID,
							ExhibitionName: scene.ExpoName,
							ExhibitionDate: scene.CreatedAt.Format("2006-01-02"),
						}
					}
				}

				resultItem = &models.FaceSearchResultItem{
					PhotoURL:          photoURL,
					MaxSimilarity:     candidate.Score,
					MatchedFacesCount: 1,
					ExhibitionInfo:    exhibitionInfo,
				}

				resultItems = append(resultItems, resultItem)
			} else {
				// 更新现有结果项
				if candidate.Score > resultItem.MaxSimilarity {
					resultItem.MaxSimilarity = candidate.Score
				}
				resultItem.MatchedFacesCount++
				resultItem.PhotoURL = photoURL
			}
		}
	}

	// 按相似度排序
	for i := 0; i < len(resultItems)-1; i++ {
		for j := i + 1; j < len(resultItems); j++ {
			if resultItems[i].MaxSimilarity < resultItems[j].MaxSimilarity {
				resultItems[i], resultItems[j] = resultItems[j], resultItems[i]
			}
		}
	}

	searchDuration := time.Since(startTime).Milliseconds()

	// 获取搜索图片中所有检测到的人脸坐标
	var searchFaceRects []*models.FaceRect
	if req.NeedFaceRect && detectRes != nil && len(detectRes.FaceInfos) > 0 {
		// 遍历人脸检测结果中的所有人脸，返回用户上传图片中的人脸坐标
		for _, faceInfo := range detectRes.FaceInfos {
			faceRect := &models.FaceRect{
				X:      faceInfo.FaceRect.X,
				Y:      faceInfo.FaceRect.Y,
				Width:  faceInfo.FaceRect.Width,
				Height: faceInfo.FaceRect.Height,
			}
			searchFaceRects = append(searchFaceRects, faceRect)
		}
	}

	result := &models.FaceSearchResult{
		SearchID:           searchID,
		TotalSearchedFaces: len(tencentPersonIDs),
		SearchDurationMs:   searchDuration,
		SearchFaceRect:     searchFaceRects,
		Results:            resultItems,
	}

	// 安全地设置图片尺寸
	if detectRes != nil {
		result.ImageHeight = detectRes.ImageHeight
		result.ImageWidth = detectRes.ImageWidth
	}
	return result, nil
}

// getFaceDataByTencentPersonIDs 根据腾讯PersonID批量获取人脸数据
func (s *Service) getFaceDataByTencentPersonIDs(ctx context.Context, tencentPersonIDs []string) (map[string]*models.FacePhotoRelation, error) {
	if len(tencentPersonIDs) == 0 {
		return make(map[string]*models.FacePhotoRelation), nil
	}

	relations, err := s.facePhotoRelation.GetActiveByTencentPersonIDs(ctx, tencentPersonIDs)
	if err != nil {
		return nil, err
	}

	result := make(map[string]*models.FacePhotoRelation)
	for _, relation := range relations {
		result[relation.TencentPersonID] = relation
	}

	return result, nil
}

// generateFaceGroupID 生成人员库ID
func (s *Service) generateFaceGroupID(expoID int64) string {
	if expoID != 0 {
		return fmt.Sprintf("wikiglobal_%d", expoID)
	}
	return fmt.Sprintf("wikiglobal_%s", id.NewFaceID())
}

// createFaceGroupLockKey 创建人员库锁键
func (s *Service) createFaceGroupLockKey(groupID string) string {
	return fmt.Sprintf("face:group:lock:%s", groupID)
}

// GetFaceGroupInfo 获取人员库信息
func (s *Service) getFaceGroupInfo(ctx context.Context, groupID string) (*models.FaceGroupInfo, error) {
	// 参数验证
	if groupID == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库ID不能为空")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("获取人员库信息: group_id=%s", groupID)

	// 从数据库获取人员库信息
	faceGroup, err := s.faceGroup.GetByID(ctx, groupID)
	if err != nil {
		if stderrors.Is(err, ormhelper.ErrNotFound) {
			return nil, errors.WithMessage(errors.ErrBadRequest, "人员库不存在")
		}
		l.Errorf("查询人员库失败: %v", err)
		return nil, err
	}

	return &models.FaceGroupInfo{
		GroupID:            faceGroup.ID,
		Name:               faceGroup.Name,
		Description:        faceGroup.Description,
		FaceModelVersion:   faceGroup.FaceModelVersion,
		MaxFaces:           faceGroup.MaxFaces,
		EstimatedFaceCount: faceGroup.EstimatedFaceCount,
		Status:             int(faceGroup.Status),
		CreatedAt:          faceGroup.CreatedAt.Format("2006-01-02T15:04:05Z"),
	}, nil
}

// getFaceGroupList 获取人员库列表
func (s *Service) getFaceGroupList(ctx context.Context, req *models.FaceGroupListRequest) (*models.FaceGroupListResponse, error) {
	// 参数验证
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}

	var (
		l    = log.Context(ctx)
		page = req.Page
		size = req.Size
	)

	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 20
	}

	l.Infof("获取人员库列表:  page=%d, size=%d", page, size)

	// 构建查询条件
	params := &models.FaceGroupQueryRequest{
		Status:   1, // 只查询正常状态的
		Page:     page,
		PageSize: size,
	}

	// 查询人员库列表
	faceGroups, total, err := s.faceGroup.List(ctx, params)
	if err != nil {
		l.Errorf("查询人员库列表失败: %v", err)
		return nil, err
	}

	// 转换为业务模型
	groups := make([]*models.FaceGroupInfo, 0, len(faceGroups))
	for _, group := range faceGroups {
		groupInfo := &models.FaceGroupInfo{
			GroupID:            group.ID,
			Name:               group.Name,
			Description:        group.Description,
			FaceModelVersion:   group.FaceModelVersion,
			MaxFaces:           group.MaxFaces,
			EstimatedFaceCount: group.EstimatedFaceCount,
			Status:             int(group.Status),
			CreatedAt:          group.CreatedAt.Format("2006-01-02T15:04:05Z"),
		}
		groups = append(groups, groupInfo)
	}

	return &models.FaceGroupListResponse{
		Groups: groups,
		Total:  int(total),
		Page:   page,
		Size:   size,
	}, nil
}

// ensureExpoFaceGroup 确保展会有对应的人员库，如果没有则创建
func (s *Service) ensureExpoFaceGroup(ctx context.Context, expo *models.Expo) (string, bool, error) {
	// 生成人员库ID（基于展会ID）
	groupID := fmt.Sprintf("expo_%d", expo.ID)

	// 1. 检查是否已有关联关系
	scenes, err := s.faceGroupExpo.GetActiveByExpo(ctx, strconv.FormatInt(expo.ID, 10))
	if err != nil {
		return "", false, fmt.Errorf("查询人员库场景关联失败: %w", err)
	}

	// 如果已存在关联关系，直接返回第一个
	if len(scenes) > 0 {
		return scenes[0].GroupID, false, nil
	}

	// 2. 检查人员库是否存在
	existingGroup, err := s.faceGroup.GetByID(ctx, groupID)
	if err != nil && !stderrors.Is(err, ormhelper.ErrNotFound) {
		return "", false, fmt.Errorf("查询人员库失败: %w", err)
	}

	var createdGroup bool

	// 3. 如果人员库不存在，创建新的人员库
	if stderrors.Is(err, ormhelper.ErrNotFound) || existingGroup == nil {
		// 在腾讯云创建人员库
		createGroupReq := &tencentAi.CreateGroupRequest{
			GroupId:          groupID,
			GroupName:        fmt.Sprintf("%s_人员库", expo.Name),
			Tag:              fmt.Sprintf("expo_%d", expo.ID),
			FaceModelVersion: "3.0",
		}

		_, err = s.tencentAI.CreateGroup(ctx, createGroupReq)
		if err != nil {
			return "", false, fmt.Errorf("创建腾讯云人员库失败: %w", err)
		}

		// 在本地数据库创建人员库记录
		faceGroup := &models.FaceGroup{
			ID:                 groupID,
			Name:               createGroupReq.GroupName,
			Description:        fmt.Sprintf("展会【%s】的人员库，用于人脸识别搜索", expo.Name),
			Tag:                createGroupReq.Tag,
			FaceModelVersion:   createGroupReq.FaceModelVersion,
			MaxFaces:           50000,
			EstimatedFaceCount: 0,
			CreationTimestamp:  time.Now().Unix(),
			Status:             models.FaceGroupStatusNormal,
		}

		err = s.faceGroup.Create(ctx, faceGroup)
		if err != nil {
			return "", false, fmt.Errorf("创建本地人员库记录失败: %w", err)
		}

		createdGroup = true
		log.Infof("为展会 %d 创建了新的人员库: %s", expo.ID, groupID)
	}

	// 4. 创建人员库场景关联
	newExpo := &models.FaceGroupExpo{
		GroupID:  groupID,
		ExpoID:   expo.ID,
		ExpoName: expo.Name,
		Status:   models.FaceGroupExpoStatusNormal,
	}

	err = s.faceGroupExpo.Create(ctx, newExpo)
	if err != nil {
		return "", false, fmt.Errorf("创建人员库场景关联失败: %w", err)
	}

	return groupID, createdGroup, nil
}

// syncImageToFaceGroup 同步图片到人员库
func (s *Service) syncImageToFaceGroup(ctx context.Context, groupID, imageURL string) (int32, error) {
	// 确保传递给腾讯AI的URL是绝对地址
	absoluteImageURL := urlformat.FullPath(imageURL)

	detectResp, err := s.detectFacesFromPhoto(ctx, absoluteImageURL)
	if err != nil {
		// 检查是否为应该跳过的错误
		if tencentAi.IsSkippableError(err) {
			log.Infof("图片应被跳过: %s, 原因: %v", imageURL, err)
			return 0, util.NewSkippableError(err.Error())
		}
		return 0, fmt.Errorf("检测人脸失败: %w", err)
	}

	if len(detectResp.FaceInfos) == 0 {
		log.Infof("图片中未检测到人脸: %s", imageURL)
		return 0, util.NewSkippableError("图片中未检测到人脸")
	}

	faceCount := int32(len(detectResp.FaceInfos))

	// 2. 为每个检测到的人脸创建Person并添加到人员库
	for _, faceInfo := range detectResp.FaceInfos {
		// 生成唯一的业务ID和PersonID
		faceBusinessID := id.NewFaceID()
		tencentPersonID := id.NewTencentPersonID()

		// 创建Person
		createPersonReq := &tencentAi.CreatePersonRequest{
			GroupId:    groupID,
			PersonId:   tencentPersonID,
			PersonName: fmt.Sprintf("person_%d", time.Now().Unix()),
			Url:        absoluteImageURL,
		}

		createPersonResp, err := s.tencentAI.CreatePerson(ctx, createPersonReq)
		if err != nil {
			log.Errorf("创建Person失败: %v", err)
			continue // 单个人脸失败不影响其他人脸
		}

		// 3. 保存人脸照片关联记录
		var qualityScore float64
		var gender models.FaceGender
		var age int8
		var beauty float64

		if faceInfo.Quality != nil {
			qualityScore = faceInfo.Quality.Score
		}
		if faceInfo.Attributes != nil {
			gender = models.FaceGender(faceInfo.Attributes.Gender)
			age = int8(faceInfo.Attributes.Age)
			beauty = faceInfo.Attributes.Beauty
		}

		relation := &models.FacePhotoRelation{
			FaceBusinessID:   faceBusinessID,
			TencentPersonID:  tencentPersonID,
			GroupID:          groupID,
			OriginalPhotoURL: imageURL,
			FaceRectX:        faceInfo.FaceRect.X,
			FaceRectY:        faceInfo.FaceRect.Y,
			FaceRectWidth:    faceInfo.FaceRect.Width,
			FaceRectHeight:   faceInfo.FaceRect.Height,
			QualityScore:     qualityScore,
			Gender:           gender,
			Age:              age,
			Beauty:           beauty,
			TencentFaceID:    createPersonResp.FaceId,
			UploadTime:       time.Now(),
			Status:           models.FacePhotoStatusNormal,
		}

		err = s.facePhotoRelation.Create(ctx, relation)
		if err != nil {
			log.Errorf("保存人脸照片关联失败: %v", err)
			// 如果保存失败，尝试删除已创建的Person
			deleteReq := &tencentAi.DeletePersonRequest{
				GroupId:  groupID,
				PersonId: tencentPersonID,
			}
			_, _ = s.tencentAI.DeletePerson(ctx, deleteReq)
			continue
		}

		log.Infof("成功同步人脸: %s -> %s", faceBusinessID, tencentPersonID)
	}

	return faceCount, nil
}

// deleteImageFromFaceGroup 从人员库删除图片
func (s *Service) deleteImageFromFaceGroup(ctx context.Context, groupID, imageURL string) error {
	// 1. 查找该图片对应的所有人脸记录
	relations, err := s.facePhotoRelation.GetByOriginalPhotoURL(ctx, imageURL)
	if err != nil {
		return fmt.Errorf("查找人脸记录失败: %w", err)
	}

	if len(relations) == 0 {
		log.Infof("图片无对应人脸记录，跳过删除: %s", imageURL)
		return nil
	}

	// 2. 删除腾讯云中的Person和本地记录
	for _, relation := range relations {
		if relation.GroupID != groupID {
			continue // 只处理指定人员库的记录
		}

		// 删除腾讯云Person
		deleteReq := &tencentAi.DeletePersonRequest{
			GroupId:  groupID,
			PersonId: relation.TencentPersonID,
		}

		_, err = s.tencentAI.DeletePerson(ctx, deleteReq)
		if err != nil {
			log.Errorf("删除腾讯云Person失败: %s, 错误: %v", relation.TencentPersonID, err)
			// 继续处理，不因为腾讯云删除失败而中断
		}

		// 删除本地记录
		err = s.facePhotoRelation.Delete(ctx, relation.ID)
		if err != nil {
			log.Errorf("删除本地人脸记录失败: %d, 错误: %v", relation.ID, err)
			return fmt.Errorf("删除本地人脸记录失败: %w", err)
		}

		log.Infof("成功删除人脸: %s -> %s", relation.FaceBusinessID, relation.TencentPersonID)
	}

	return nil
}

// getSyncedPhotoURLsByGroup 获取指定人员库中已同步的图片URL列表
func (s *Service) getSyncedPhotoURLsByGroup(ctx context.Context, groupID string) (map[string]bool, error) {
	relations, err := s.facePhotoRelation.GetByGroupID(ctx, groupID, 0, 0)
	if err != nil {
		return nil, err
	}

	syncedURLs := make(map[string]bool)
	for _, relation := range relations {
		if relation.IsNormal() {
			syncedURLs[relation.OriginalPhotoURL] = true
		}
	}

	return syncedURLs, nil
}

// verifyGeetestAndClearLimitInternal 验证极验并清空限制
func (s *Service) verifyGeetestAndClearLimitInternal(ctx context.Context, req *models.GeetestValidationRequest) (bool, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return false, errors.WithMessage(errors.ErrAccessTokenExpired, "用户未登录")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("用户极验验证请求: user_id=%s, lot_number=%s", userID, req.LotNumber)

	// 参数校验
	if req.LotNumber == "" || req.CaptchaOutput == "" || req.PassToken == "" || req.GenTime == "" {
		return false, errors.WithMessage(errors.ErrBadRequest, "验证参数不完整")
	}

	// 调用极验API验证（这里需要实现具体的验证逻辑）
	verified, err := s.validateGeetest(ctx, req)
	if err != nil {
		l.Errorf("极验验证失败: user_id=%s, error=%v", userID, err)
		return false, errors.WithMessage(errors.ErrInternalServer, "验证服务异常")
	}

	if !verified {
		l.Warnf("极验验证失败: user_id=%s, lot_number=%s", userID, req.LotNumber)
		return false, nil
	}

	err = s.clearFaceSearchLimits(ctx, userID)
	if err != nil {
		l.Errorf("清空用户限制失败: user_id=%s, error=%v", userID, err)
		return false, errors.WithMessage(errors.ErrInternalServer, "清空限制失败")
	}

	l.Infof("极验验证成功并清空限制: user_id=%s, lot_number=%s", userID, req.LotNumber)
	return true, nil
}

// validateGeetest 调用极验API进行验证
func (s *Service) validateGeetest(ctx context.Context, req *models.GeetestValidationRequest) (bool, error) {
	var (
		l = log.Context(ctx)
	)

	l.Infof("调用极验API验证: lot_number=%s, captcha_output=%s, pass_token=%s, gen_time=%s",
		req.LotNumber, req.CaptchaOutput, req.PassToken, req.GenTime)

	// 调用上游服务进行极验验证
	result, err := s.upstream.GeetTestVerify(ctx, req.LotNumber, req.CaptchaOutput, req.PassToken, req.GenTime)
	if err != nil {
		l.Errorf("极验API调用失败: %v", err)
		return false, fmt.Errorf("极验API调用失败: %v", err)
	}

	return result, nil
}

// clearFaceSearchLimits 清空用户的人脸搜索限制
func (s *Service) clearFaceSearchLimits(ctx context.Context, userID string) error {
	now := time.Now()
	minuteKey, dayKey := s.generateUserLimitKeys(userID, now)

	// 使用批量删除提高效率
	err := s.redisCli.Del(ctx, minuteKey, dayKey).Err()
	if err != nil {
		return fmt.Errorf("清空用户人脸搜索限制失败: %v", err)
	}

	log.Context(ctx).Infof("成功清空用户人脸搜索限制: user_id=%s", userID)
	return nil
}

// generateUserLimitKeys 生成用户限制相关的Redis键
func (s *Service) generateUserLimitKeys(userID string, now time.Time) (minuteKey, dayKey string) {
	minuteKey = fmt.Sprintf("%s%s:%s", faceSearchUserLimitPrefix, userID, now.Format("2006-01-02:15:04"))
	dayKey = fmt.Sprintf("%s%s:%s", faceSearchUserLimitPrefix, userID, now.Format("2006-01-02"))
	return
}
