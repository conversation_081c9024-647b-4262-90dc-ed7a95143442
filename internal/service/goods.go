package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"strings"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/id"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) checkGoods(in *v1.GoodsInfo) error {
	if in.Name == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "请添加商品名称")
	}

	for _, labelId := range in.LabelId {
		if _, ok := s.ExistsLabel(labelId); !ok {
			return innErr.WithMessage(innErr.ErrBadRequest, "不存在的标签")
		}
	}

	if in.Image == nil {
		return innErr.WithMessage(innErr.ErrBadRequest, "请添加商品图片")
	}

	if len(in.Carousels) <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "请添加商品轮播图")
	}

	if len(in.Details) <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "请添加商品详情图")
	}

	if len(in.Specs) <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "请添加商品规格")
	}

	if len(in.Skus) <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "商品sku错误")
	}

	if in.UseBasePrice && in.BasePrice <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "请设置商品基础价格")
	}

	// 检查规格
	var (
		specIds  = make(map[string]struct{}, len(in.Specs))
		skuCount = 0
	)

	staticSpecMap, staticSpecValueMap, staticSpecUnitMap, ok := getStaticSpecByCategory(in.Category)
	if !ok {
		return innErr.WithMessage(innErr.ErrBadRequest, "商品分类不正确")
	}

	var (
		hasImageSpec = make(map[string]struct{}, len(in.Specs))
		goodsSpecs   = make(map[string]struct{}, len(in.Specs))
	)

	for index, spec := range in.Specs {
		if spec.Id == "" {
			return innErr.WithMessage(innErr.ErrBadRequest, "规格ID错误")
		}

		if len(spec.Values) <= 0 {
			return innErr.WithMessage(innErr.ErrBadRequest, "规格值不能为空")
		}

		// 获取静态的spec
		_, ok = staticSpecMap[spec.Id]
		if !ok {
			return innErr.WithMessage(innErr.ErrBadRequest, "不正确的规格值")
		}

		// 重复的spec_id
		if _, ok = specIds[spec.Id]; ok {
			return innErr.WithMessage(innErr.ErrBadRequest, "规格ID不能重复")
		}
		specIds[spec.Id] = struct{}{}

		if index <= 0 {
			skuCount = len(spec.Values)
		} else {
			skuCount = skuCount * len(spec.Values)
		}
		goodsSpecs[spec.Id] = struct{}{}

		var (
			valueIds        = make(map[string]struct{}, len(spec.Values))
			selectedCount   = 0
			staticSpecValue map[string]*v1.GoodsStaticSpecValue
			staticSpecUnit  map[string]*v1.GoodsStaticSpecUnit
		)

		staticSpecValue, _ = staticSpecValueMap[spec.Id]
		staticSpecUnit, _ = staticSpecUnitMap[spec.Id]

		for _, value := range spec.Values {
			if value.Id == "" {
				return innErr.WithMessage(innErr.ErrBadRequest, "规格值ID不能为空")
			}

			if value.Image != nil {
				hasImageSpec[spec.Id] = struct{}{}
			}

			// VPS的规格值是固定的
			if in.Category != v1.GoodsCategory_GOODS_CATEGORY_VPS && value.Name == "" {
				return innErr.WithMessage(innErr.ErrBadRequest, "请填写规格值")
			}

			// 如果是VPS，这里需要检测规格值
			if in.Category == v1.GoodsCategory_GOODS_CATEGORY_VPS {
				_, ok = staticSpecValue[value.Id]
				if !ok {
					return innErr.WithMessage(innErr.ErrBadRequest, "不存在的规格值ID")
				}
			}

			if in.Category == v1.GoodsCategory_GOODS_CATEGORY_VIP {
				if value.UnitId == "" {
					return innErr.WithMessage(innErr.ErrBadRequest, "请选择单位")
				}

				_, ok = staticSpecUnit[value.UnitId]
				if !ok {
					return innErr.WithMessage(innErr.ErrBadRequest, "不存在的单位ID")
				}
			}

			if _, ok = valueIds[value.Id]; ok {
				return innErr.WithMessage(innErr.ErrBadRequest, "规格值ID重复")
			}
			if value.Selected {
				selectedCount++
			}
			valueIds[value.Id] = struct{}{}
		}

		if selectedCount != 1 {
			return innErr.WithMessage(innErr.ErrBadRequest, "没有默认的规格值")
		}
	}

	if in.Category != v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL {
		for _, staticSpec := range staticSpecMap {
			if _, ok = goodsSpecs[staticSpec.Id]; !ok {
				return innErr.WithMessage(innErr.ErrBadRequest, fmt.Sprintf("缺少规格 %s", staticSpec.Name))
			}
		}
	}

	if len(hasImageSpec) > 1 {
		return innErr.WithMessage(innErr.ErrBadRequest, "只能有一个规格添加图片")
	}

	// 检查SKU
	// sku的数量应该等于规格的乘积
	if len(in.Skus) != skuCount {
		return innErr.WithMessage(innErr.ErrBadRequest, "sku数量不正确")
	}

	for _, sku := range in.Skus {
		if !in.UseBasePrice && sku.Price <= 0 {
			return innErr.WithMessage(innErr.ErrBadRequest, "请配置sku价格")
		}
	}

	for key, value := range in.Translate {
		in.Translate[strings.ToLower(key)] = value
	}

	return nil
}

func (s *Service) checkGoodsRefTaskOffline(ctx context.Context, goodsId string) (bool, error) {
	res, err := s.ListTaskGoods(ctx, &common.EmptyRequest{})
	if err != nil {
		return false, err
	}

	// 任务
	for _, value := range res.TaskGoods {
		if value.GoodsId == goodsId {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) GetStaticSpec(_ context.Context, in *v1.GetStaticSpecRequest) (*v1.GetStaticSpecReply, error) {
	return &v1.GetStaticSpecReply{
		Categories: goodsStaticSpecs,
	}, nil
}

func (s *Service) AddGoods(ctx context.Context, in *v1.GoodsInfo) (*v1.AddGoodsReply, error) {
	err := s.checkGoods(in)
	if err != nil {
		return nil, err
	}

	var (
		l          = log.Context(ctx)
		newLabels  = make([]*models.GoodsLabel, 0, len(in.LabelId))
		newGoodsId = id.GenerateId(id.BusinessGoods)
	)

	for _, labelId := range in.LabelId {
		newLabels = append(newLabels, &models.GoodsLabel{
			Id: labelId,
		})
	}

	var (
		skus              = make([]*models.SKU, 0, len(in.Skus))
		selectedSpecValue = make(map[string]string, len(in.Specs)) // 选中的规格值
		selectedSkuPrice  = float32(0)                             // 选中的sku价格
		salesTotal        = int32(0)                               // 总销量
	)

	for _, spec := range in.Specs {
		for _, value := range spec.Values {
			if !value.Selected {
				continue
			}
			selectedSpecValue[spec.Id] = value.Id
			break
		}
	}

	for _, sku := range in.Skus {
		var (
			selected     = true
			virtualSales = rand.Int31n(1000)
		)
		salesTotal += virtualSales

		for _, spec := range sku.Specs {
			selectedValueId, ok := selectedSpecValue[spec.SpecId]
			if !ok {
				return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid sku spec")
			}

			if selectedValueId != spec.ValueId {
				selected = false
				break
			}

		}
		if selected {
			selectedSkuPrice = sku.Price
		}

		skus = append(skus, &models.SKU{
			GoodsId: newGoodsId,
			SkuID:   id.GenerateId(id.BusinessSKU),
			Specs: &igorm.CustomValue[[]*v1.GoodsSkuSpec]{
				V: sku.Specs,
			},
			Price:        sku.Price,
			Stock:        sku.Stock,
			VirtualSales: virtualSales,
			Disable:      sku.Disable,
		})
	}

	newGoods := &models.Goods{
		GoodsId:       newGoodsId,
		Category:      in.Category,
		Name:          in.Name,
		BasePrice:     in.BasePrice,
		UseBasePrice:  in.UseBasePrice,
		SelectedPrice: selectedSkuPrice,
		Description:   in.Description,
		Status:        in.Status,
		FreeShipping:  in.FreeShipping,
		Image: &igorm.CustomValue[*v1.Image]{
			V: in.Image,
		},
		Labels: &igorm.CustomValue[[]*models.GoodsLabel]{
			V: newLabels,
		},
		Carousels: &igorm.CustomValue[[]*v1.Image]{
			V: in.Carousels,
		},
		Details: &igorm.CustomValue[[]*v1.Image]{
			V: in.Details,
		},
		Specs: &igorm.CustomValue[[]*v1.GoodsSpec]{
			V: in.Specs,
		},
		Translate: &igorm.CustomValue[map[string]*v1.GoodsTranslate]{
			V: in.Translate,
		},
		Extra: &igorm.CustomValue[*models.GoodsExtra]{
			V: &models.GoodsExtra{},
		},
	}

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.goods.Add(ctx, newGoods, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goods.Add Err:%v", err)
		return nil, err
	}

	err = s.goodsStatistics.Add(ctx, &models.GoodsStatistics{
		GoodsId:      newGoodsId,
		Status:       in.Status,
		VirtualSales: salesTotal,
		TotalSales:   salesTotal,
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goodsStatistics.Add Err:%v", err)
		return nil, err
	}

	err = s.sku.BatchAdd(ctx, skus, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("sku.BatchAdd Err:%v", err)
		return nil, err
	}
	return &v1.AddGoodsReply{
		Id: newGoodsId,
	}, nil
}

func (s *Service) UpdateGoods(ctx context.Context, in *v1.GoodsInfo) (*common.EmptyReply, error) {
	if in.Id == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid id")
	}

	err := s.checkGoods(in)
	if err != nil {
		return nil, err
	}

	if in.Status == v1.GoodsStatus_GoodsStatusOff {
		var ref bool
		ref, err = s.checkGoodsRefTaskOffline(ctx, in.Id)
		if err != nil {
			return nil, err
		}

		if ref {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前商品关联任务，不可下架！")
		}

		ref, err = s.GetGoodsIsConfigGiftCard(ctx, in.Id)
		if err != nil {
			return nil, err
		}

		if ref {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前商品关联礼品卡，不可下架！")
		}
	}

	var (
		l         = log.Context(ctx)
		newLabels = make([]*models.GoodsLabel, 0, len(in.LabelId))
	)

	for _, labelId := range in.LabelId {
		newLabels = append(newLabels, &models.GoodsLabel{
			Id: labelId,
		})
	}

	var (
		selectedSpecValue = make(map[string]string, len(in.Specs))
		selectedSkuPrice  = float32(0)
		newSkuMap         = make(map[string]struct{}, len(in.Skus))
	)

	// 选中的规格值
	for _, spec := range in.Specs {
		for _, value := range spec.Values {
			if !value.Selected {
				continue
			}
			selectedSpecValue[spec.Id] = value.Id
			break
		}
	}

	// 匹配选中的sku价格
	for _, sku := range in.Skus {
		selected := true
		for _, spec := range sku.Specs {
			selectedValueId, ok := selectedSpecValue[spec.SpecId]
			if !ok || selectedValueId != spec.ValueId {
				selected = false
				break
			}
		}
		if selected {
			selectedSkuPrice = sku.Price
		}
		newSkuMap[sku.SkuId] = struct{}{}
	}

	// 获取sku列表
	oldSkus, err := s.sku.FindByGoodsId(ctx, in.Id)
	if err != nil {
		l.Errorf("FindByGoodsId Err:%v", err)
		return nil, err
	}

	oldSkuMap := make(map[string]struct{}, len(oldSkus))
	for _, sku := range oldSkus {
		// 已经存在的sku不允许删除
		if _, ok := newSkuMap[sku.SkuID]; !ok {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "delete sku is not allowed.")
		}
		oldSkuMap[sku.SkuID] = struct{}{}
	}

	updateGoods := &models.Goods{
		GoodsId:       in.Id,
		Category:      in.Category,
		Name:          in.Name,
		BasePrice:     in.BasePrice,
		UseBasePrice:  in.UseBasePrice,
		SelectedPrice: selectedSkuPrice,
		Description:   in.Description,
		Status:        in.Status,
		FreeShipping:  in.FreeShipping,
		Image: &igorm.CustomValue[*v1.Image]{
			V: in.Image,
		},
		Labels: &igorm.CustomValue[[]*models.GoodsLabel]{
			V: newLabels,
		},
		Carousels: &igorm.CustomValue[[]*v1.Image]{
			V: in.Carousels,
		},
		Details: &igorm.CustomValue[[]*v1.Image]{
			V: in.Details,
		},
		Specs: &igorm.CustomValue[[]*v1.GoodsSpec]{
			V: in.Specs,
		},
		Translate: &igorm.CustomValue[map[string]*v1.GoodsTranslate]{
			V: in.Translate,
		},
		Extra: &igorm.CustomValue[*models.GoodsExtra]{
			V: &models.GoodsExtra{},
		},
	}

	var (
		newSkus    = make([]*models.SKU, 0, len(in.Skus))
		updateSkus = make([]*models.SKU, 0, len(in.Skus))
	)

	for _, sku := range in.Skus {
		if _, ok := oldSkuMap[sku.SkuId]; !ok {
			newSkus = append(newSkus, &models.SKU{
				GoodsId: in.Id,
				SkuID:   id.GenerateId(id.BusinessSKU),
				Specs: &igorm.CustomValue[[]*v1.GoodsSkuSpec]{
					V: sku.Specs,
				},
				Price:   sku.Price,
				Stock:   sku.Stock,
				Sales:   rand.Int31n(1000),
				Disable: sku.Disable,
			})
			continue
		}
		updateSkus = append(updateSkus, &models.SKU{
			GoodsId: in.Id,
			SkuID:   sku.SkuId,
			Specs: &igorm.CustomValue[[]*v1.GoodsSkuSpec]{
				V: sku.Specs,
			},
			Price:   sku.Price,
			Stock:   sku.Stock,
			Disable: sku.Disable,
		})
	}

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.goods.Update(ctx, updateGoods, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goods.Update Err:%v", err)
		return nil, err
	}

	err = s.goodsStatistics.UpdateStatus(ctx, in.Id, in.Status, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goodsStatistics.UpdateStatus Err:%v", err)
		return nil, err
	}

	if len(newSkus) > 0 {
		err = s.sku.BatchAdd(ctx, newSkus, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("sku.BatchAdd Err:%v", err)
			return nil, err
		}
	}

	for _, updateSku := range updateSkus {
		var rowsUpdated int64
		rowsUpdated, err = s.sku.Update(ctx, updateSku, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("sku.Update Err:%v", err)
			return nil, err
		}

		if rowsUpdated <= 0 {
			err = innErr.WithMessage(innErr.ErrBadRequest, "不存在的SKU")
			return nil, err
		}
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) GetGoodsInfo(ctx context.Context, in *v1.GetGoodsInfoRequest) (*v1.GoodsInfo, error) {
	if in.Id == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid id")
	}

	l := log.Context(ctx)

	goods, err := s.goods.Get(ctx, in.Id)
	if err != nil {
		l.Errorf("goods.Get Err:%v", err)
		return nil, err
	}

	skus, err := s.sku.FindByGoodsId(ctx, in.Id)
	if err != nil {
		l.Errorf("FindByGoodsId Err:%v", err)
		return nil, err
	}
	return s.goodsToGoodsInfo(goods, skus), nil
}

func (s *Service) DeleteGoods(ctx context.Context, in *v1.DeleteGoodsRequest) (*common.EmptyReply, error) {
	if in.Id == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid id")
	}

	var (
		l   = log.Context(ctx)
		err error
	)

	var ref bool
	ref, err = s.checkGoodsRefTaskOffline(ctx, in.Id)
	if err != nil {
		return nil, err
	}

	if ref {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前商品关联任务，不可删除！")
	}

	ref, err = s.GetGoodsIsConfigGiftCard(ctx, in.Id)
	if err != nil {
		return nil, err
	}

	if ref {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前商品关联礼品卡，不可删除！")
	}

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.goods.Delete(ctx, in.Id, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goods.Delete Err:%v", err)
		return nil, err
	}

	err = s.goodsStatistics.Delete(ctx, in.Id, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goodsStatistics.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) GetGoodsList(ctx context.Context, in *v1.GetGoodsListRequest) (*v1.GetGoodsListReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	goodsList, total, err := s.goods.List(ctx, in.Keyword, v1.GoodsStatus(in.Status), int(in.Size), int(in.Page))
	if err != nil {
		l.Errorf("goods.List Err:%v", err)
		return nil, err
	}

	goodsIds := make([]string, 0, len(goodsList))
	for _, goods := range goodsList {
		goodsIds = append(goodsIds, goods.GoodsId)
	}

	var skus []*models.SKU
	if len(goodsIds) > 0 {
		skus, err = s.sku.FindByGoodsIds(ctx, goodsIds)
		if err != nil {
			l.Errorf("FindByGoodsIds Err:%v", err)
			return nil, err
		}
	}

	goodsSkuMap := make(map[string][]*models.SKU, len(goodsList))
	for _, sku := range skus {
		goodsSkus := goodsSkuMap[sku.GoodsId]
		goodsSkus = append(goodsSkus, sku)
		goodsSkuMap[sku.GoodsId] = goodsSkus
	}

	goodsItems := make([]*v1.GoodsInfo, 0, len(goodsList))
	for _, goods := range goodsList {
		goodsItems = append(goodsItems, s.goodsToGoodsInfo(goods, goodsSkuMap[goods.GoodsId]))
	}

	return &v1.GetGoodsListReply{
		Total: int32(total),
		Items: goodsItems,
	}, nil
}

func (s *Service) SetGoodsStatus(ctx context.Context, in *v1.GoodsStatusRequest) (*common.EmptyReply, error) {
	if in.Id == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid id")
	}

	var (
		l   = log.Context(ctx)
		err error
	)

	if in.Status == v1.GoodsStatus_GoodsStatusOff {
		var ref bool
		ref, err = s.checkGoodsRefTaskOffline(ctx, in.Id)
		if err != nil {
			return nil, err
		}

		if ref {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前商品关联任务，不可删除！")
		}

		ref, err = s.GetGoodsIsConfigGiftCard(ctx, in.Id)
		if err != nil {
			return nil, err
		}

		if ref {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前商品关联礼品卡，不可删除！")
		}
	}

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.goods.UpdateStatus(ctx, in.Id, in.Status, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goods.UpdateStatus Err:%v", err)
		return nil, err
	}

	err = s.goodsStatistics.UpdateStatus(ctx, in.Id, in.Status, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("goodsStatistics.UpdateStatus Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) goodsToGoodsInfo(in *models.Goods, skus []*models.SKU) *v1.GoodsInfo {
	labelIds := make([]string, 0, len(in.Labels.V))
	for _, label := range in.Labels.V {
		labelIds = append(labelIds, label.Id)
	}

	skuItems := make([]*v1.GoodsSku, 0, len(skus))
	for _, sku := range skus {
		skuItems = append(skuItems, &v1.GoodsSku{
			SkuId:   sku.SkuID,
			Specs:   sku.Specs.V,
			Price:   sku.Price,
			Disable: sku.Disable,
			Stock:   sku.Stock,
		})
	}

	return &v1.GoodsInfo{
		Id:            in.GoodsId,
		Category:      in.Category,
		Name:          in.Name,
		Description:   in.Description,
		LabelId:       labelIds,
		Image:         in.Image.V,
		Status:        in.Status,
		FreeShipping:  in.FreeShipping,
		BasePrice:     in.BasePrice,
		UseBasePrice:  in.UseBasePrice,
		SelectedPrice: in.SelectedPrice,
		Carousels:     in.Carousels.V,
		Details:       in.Details.V,
		Specs:         in.Specs.V,
		Skus:          skuItems,
		Translate:     in.Translate.V,
	}
}

// ============================= APP =============================

func (s *Service) GoodsTab(ctx context.Context, _ *v1.GoodsTabRequest) (*v1.GoodsTabReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	goodsRes, err := s.GoodsList(ctx, &v1.GoodsListRequest{
		TabId: "GT1000",
		Size:  10,
		Page:  1,
	})
	if err != nil {
		l.Errorf("GoodsList Err:%v", err)
		return nil, err
	}

	return &v1.GoodsTabReply{
		Tabs: []*v1.GoodsTab{
			{
				Id:       "GT1000",
				Name:     i18n.GetWithDefaultEnglish("60025", languageCode), // 积分商城
				Selected: true,
			},
			//{
			//	Id:   "GT2000",
			//	Name: i18n.GetWithDefaultEnglish("59862", languageCode), // 文化衫
			//},
			//{
			//	Id:   "GT3000",
			//	Name: i18n.GetWithDefaultEnglish("59863", languageCode), // VIP
			//},
			//{
			//	Id:   "GT4000",
			//	Name: i18n.GetWithDefaultEnglish("59864", languageCode), // 书籍
			//},
		},
		Goods: goodsRes.Goods,
	}, nil
}

func (s *Service) GoodsList(ctx context.Context, in *v1.GoodsListRequest) (*v1.GoodsListReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	goodsStatistics, err := s.goodsStatistics.FindBySales(ctx, int(in.Size), int(in.Page))
	if err != nil {
		l.Errorf("goodsStatistics.FindBySales Err:%v", err)
		return nil, err
	}

	goodsIds := make([]string, 0, len(goodsStatistics))
	for _, value := range goodsStatistics {
		goodsIds = append(goodsIds, value.GoodsId)
	}

	var goodsList []*models.Goods
	if len(goodsIds) > 0 {
		goodsList, err = s.goods.FindByGoodsIds(ctx, goodsIds)
		if err != nil {
			l.Errorf("FindByGoodsIds Err:%v", err)
			return nil, err
		}
	}

	var (
		goodsLabelsMapping = s.getGoodsTextLabels(languageCode, goodsStatistics)
		orderGoods         = make([]*models.Goods, 0, len(goodsIds))
		goodsMapping       = make(map[string]*models.Goods, len(goodsIds))
	)

	for _, value := range goodsList {
		goodsMapping[value.GoodsId] = value
	}

	for _, goodsId := range goodsIds {
		goods, ok := goodsMapping[goodsId]
		if !ok {
			continue
		}
		orderGoods = append(orderGoods, goods)
	}

	return &v1.GoodsListReply{
		Goods: s.goodsToGoodsListItem(languageCode, orderGoods, goodsLabelsMapping),
	}, nil
}

func (s *Service) GetGoodsDetail(ctx context.Context, in *v1.GoodsDetailRequest) (*v1.GoodsDetail, error) {
	if in.GoodsId == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "invalid goods_id")
	}

	l := log.Context(ctx)

	goods, err := s.goods.Get(ctx, in.GoodsId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("goods.Get Err:%v", err)
		return nil, err
	}

	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	skus, err := s.sku.FindByGoodsId(ctx, in.GoodsId)
	if err != nil {
		l.Errorf("sku.FindByGoodsId Err:%v", err)
		return nil, err
	}

	//err = s.goodsStatistics.IncreaseGoodsViews(ctx, in.GoodsId)
	//if err != nil {
	//	l.Errorf("IncreaseGoodsViews Err:%v", err)
	//	// 这里发生错误不影响主流程
	//}
	return s.getGoodsDetail(ctx, goods, skus, nil), nil
}

func (s *Service) BestGoods(ctx context.Context, _ *v1.BestGoodsRequest) (*v1.BestGoodsReply, error) {
	l := log.Context(ctx)
	goodsRes, err := s.GoodsList(ctx, &v1.GoodsListRequest{
		TabId: "GT1000",
		Size:  5,
		Page:  1,
	})
	if err != nil {
		l.Errorf("GoodsList Err:%v", err)
		return nil, err
	}

	return &v1.BestGoodsReply{
		Goods: goodsRes.Goods,
	}, nil
}

func (s *Service) getGoodsDetail(ctx context.Context, goods *models.Goods, skus []*models.SKU, selectedSpecs map[string]string) *v1.GoodsDetail {
	if goods == nil {
		return &v1.GoodsDetail{}
	}
	l := log.Context(ctx)

	var (
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		price           = goods.BasePrice
		goodsName       = goods.Name
		goodsLabels     []*v1.GoodsLabel
		details         = goods.Details.V
		specs           = goods.Specs.V
		translateSpecs  map[string]*v1.GoodsSpecTranslate
		sales           = int32(0)
	)

	if !goods.UseBasePrice {
		price = goods.SelectedPrice
	}

	goodsStatistics, err := s.goodsStatistics.GetByGoodsId(ctx, goods.GoodsId)
	if err != nil {
		l.Errorf("goods.Get Err:%v", err)
	} else {
		sales = goodsStatistics.Sales + goodsStatistics.VirtualSales
	}

	translate, ok := goods.Translate.V[languageCode]
	if !ok {
		translate, ok = goods.Translate.V["en"]
	}

	if ok {
		if translate.Name != "" {
			goodsName = translate.Name
		}

		if len(translate.Details) > 0 {
			details = translate.Details
		}

		if len(translate.Specs) > 0 {
			translateSpecs = translate.Specs
		}
	}

	for _, label := range goods.Labels.V {
		labelInfo, labelExists := s.ExistsLabel(label.Id)
		if !labelExists {
			continue
		}

		imageURL, translateExists := labelInfo.Values[languageCode]
		// 使用英文兜底
		if !translateExists {
			imageURL = labelInfo.Values["en"]
		}

		goodsLabels = append(goodsLabels, &v1.GoodsLabel{
			Type:  v1.GoodsLabelType_GoodsLabelImage,
			Image: urlformat.FullPath(imageURL, urlTemplate),
			Id:    labelInfo.Id,
		})
	}

	var (
		outSpecs                                = make([]*v1.GoodsSpec, 0, len(goods.Specs.V))
		defaultSelectedSpecs                    = make(map[string]string, len(goods.Specs.V))
		staticSpecMap, staticSpecValueMap, _, _ = getStaticSpecByCategory(goods.Category)
		goodsSkus                               = make([]*v1.GoodsSku, 0, len(skus))
		specValueUnitMap                        = make(map[string]map[string]string, len(goods.Specs.V))
	)

	for _, spec := range specs {
		var (
			name                                    = spec.Name
			specValues                              = make([]*v1.GoodsSpecValue, 0, len(spec.Values))
			staticSpec                              *v1.GoodsStaticSpec
			specTranslate, specTranslateExists      = translateSpecs[spec.Id]
			staticSpecValues, staticSpecValueExists = staticSpecValueMap[spec.Id]
			valueUnitMap                            = make(map[string]string, len(spec.Values))
		)

		staticSpec, ok = staticSpecMap[spec.Id]
		// 规格名称多语言
		if ok && staticSpec.Key != "" {
			name = i18n.GetWithDefaultEnglish(staticSpec.Key, languageCode)
		}

		for _, value := range spec.Values {
			valueName := value.Name
			switch goods.Category {
			case v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL: // 实物商品
				if specTranslateExists {
					transValueName, exists := specTranslate.Values[value.Id]
					if !exists {
						transValueName, exists = specTranslate.Values["en"]
					}

					if exists && transValueName != "" {
						valueName = transValueName
					}
				}
			case v1.GoodsCategory_GOODS_CATEGORY_VIP: // VIP
				valueName = i18n.GetWithTemplateDataDefaultEnglish(value.UnitId, languageCode, []string{value.Name})
			case v1.GoodsCategory_GOODS_CATEGORY_VPS: // VPS
				if staticSpecValueExists {
					var staticSpecValue *v1.GoodsStaticSpecValue
					staticSpecValue, ok = staticSpecValues[value.Id]
					if !ok {
						break
					}

					if staticSpecValue.Name != "" {
						valueName = staticSpecValue.Name
					}

					valueTransName, valueTransNameExists := staticSpecValue.Translate[languageCode]
					if !valueTransNameExists {
						valueTransName, valueTransNameExists = staticSpecValue.Translate["en"]
					}

					if valueTransNameExists && valueTransName != "" {
						valueName = valueTransName
					}
				}
			}

			specValues = append(specValues, &v1.GoodsSpecValue{
				Id:       value.Id,
				Name:     valueName,
				Selected: value.Selected,
				Image:    fullImage(value.Image, v1.OrderSource_STORE),
				SpecId:   spec.Id,
			})
			valueUnitMap[value.Id] = value.UnitId

			if value.Selected {
				defaultSelectedSpecs[spec.Id] = value.Id
			}
		}

		outSpecs = append(outSpecs, &v1.GoodsSpec{
			Id:     spec.Id,
			Name:   name,
			Values: specValues,
		})
		specValueUnitMap[spec.Id] = valueUnitMap
	}

	// 这里赋值单位ID
	for _, sku := range skus {
		if goods.UseBasePrice {
			sku.Price = goods.BasePrice
		}

		for _, spec := range sku.Specs.V {
			if spec.ValueId == "" {
				continue
			}

			var valueUnitMap map[string]string
			valueUnitMap, ok = specValueUnitMap[spec.SpecId]
			if !ok {
				continue
			}
			spec.UnitId = valueUnitMap[spec.ValueId]
		}

		goodsSkus = append(goodsSkus, &v1.GoodsSku{
			SkuId:   sku.SkuID,
			Specs:   sku.Specs.V,
			Price:   sku.Price,
			Disable: sku.Disable,
			Stock:   sku.Stock,
		})
	}

	if len(selectedSpecs) <= 0 {
		// 检查默认选中的sku是否还有库存
		var selectedSku *v1.GoodsSku
		// 定位sku
		for _, sku := range goodsSkus {
			match := true
			for _, spec := range sku.Specs {
				var valueId string
				valueId, ok = defaultSelectedSpecs[spec.SpecId]
				if !ok || valueId != spec.ValueId {
					match = false
					break
				}
			}

			if match {
				selectedSku = sku
				break
			}
		}

		// 如果默认选中的sku是禁用状态或者没有库存
		if selectedSku != nil && (selectedSku.Disable || selectedSku.Stock <= 0) {
			// 获取一个可用、有库存且价格最低的一个SKU
			var autoSelectedSku *v1.GoodsSku
			for _, sku := range goodsSkus {
				if sku.Disable {
					continue
				}

				if sku.Stock <= 0 {
					continue
				}

				if autoSelectedSku == nil {
					autoSelectedSku = sku
					continue
				}

				if sku.Price < autoSelectedSku.Price {
					autoSelectedSku = sku
				}
			}

			if autoSelectedSku != nil {
				selectedSku = autoSelectedSku
				selectedSpecs = make(map[string]string, len(autoSelectedSku.Specs))
				for _, spec := range autoSelectedSku.Specs {
					selectedSpecs[spec.SpecId] = spec.ValueId
				}
			}
		}

		if selectedSku != nil && !goods.UseBasePrice {
			price = selectedSku.Price
		}
	}

	selectedSpecDesc := make([]string, 0, len(outSpecs))
	for _, spec := range outSpecs {
		selectedValueId := ""
		selectedValueId, ok = selectedSpecs[spec.Id]
		if !ok {
			continue
		}

		for _, value := range spec.Values {
			if value.Id != selectedValueId {
				value.Selected = false
				continue
			}
			value.Selected = true
			selectedSpecDesc = append(selectedSpecDesc, fmt.Sprintf("%s %s", spec.Name, value.Name))
		}
	}

	selectedSkuId := ""
	// 这里再统一监测一次是否有匹配的SKU
	for _, sku := range goodsSkus {
		match := true
		for _, spec := range sku.Specs {
			var valueId string
			valueId, ok = selectedSpecs[spec.SpecId]
			if !ok || valueId != spec.ValueId {
				match = false
				break
			}
		}

		if match {
			selectedSkuId = sku.SkuId
			price = sku.Price
			break
		}
	}

	var (
		carousels = make([]*v1.Image, 0, len(goods.Carousels.V)+1)
		disable   = false
	)

	if goods.Category == v1.GoodsCategory_GOODS_CATEGORY_VPS {
		disable, _ = s.shadow(ctx)
	}

	if goods.Image != nil {
		carousels = append(carousels, goods.Image.V)
	}
	carousels = append(carousels, goods.Carousels.V...)

	buyLimit := int32(0)
	if goods.Category == v1.GoodsCategory_GOODS_CATEGORY_VPS {
		buyLimit = 1
	}
	return &v1.GoodsDetail{
		GoodsId:          goods.GoodsId,
		Name:             goodsName,
		Description:      goods.Description,
		Price:            price,
		Sales:            sales,
		FreeShipping:     goods.FreeShipping,
		SelectedSkuId:    selectedSkuId,
		SelectedSpecDesc: strings.Join(selectedSpecDesc, ";"),
		Category:         goods.Category,
		Image:            fullImage(goods.Image.V, v1.OrderSource_STORE),
		Disable:          disable,
		Status:           goods.Status,
		Labels:           goodsLabels,
		Carousels:        fullImages(carousels, v1.OrderSource_STORE),
		Details:          fullImages(details, v1.OrderSource_STORE),
		Specs:            outSpecs,
		Skus:             goodsSkus,
		BuyLimit:         buyLimit,
	}
}

func (s *Service) getGoodsTextLabels(languageCode string, goodsList []*models.GoodsStatistics) map[string][]*v1.GoodsLabel {
	goodsLabelsMapping := make(map[string][]*v1.GoodsLabel, len(goodsList))
	for _, goods := range goodsList {
		var goodsLabels []*v1.GoodsLabel
		if goods.Sales+goods.VirtualSales > 0 {
			goodsLabels = append(goodsLabels, &v1.GoodsLabel{
				Type:  v1.GoodsLabelType_GoodsLabelText,
				Text:  i18n.GetWithTemplateDataDefaultEnglish("59870", languageCode, []string{toPlusNotation(int(goods.TotalSales))}), // 已兑{0}
				Color: goodsLabelColor,
			})
		}

		//if goods.Views > 0 {
		//	goodsLabels = append(goodsLabels, &v1.GoodsLabel{
		//		Type:  v1.GoodsLabelType_GoodsLabelText,
		//		Text:  i18n.GetWithTemplateDataDefaultEnglish("59871", languageCode, []string{strconv.Itoa(int(goods.Views))}), // 浏览{0}
		//		Color: goodsLabelColor,
		//	})
		//}
		goodsLabelsMapping[goods.GoodsId] = goodsLabels
	}
	return goodsLabelsMapping
}

func (s *Service) goodsToGoodsListItem(languageCode string, goodsList []*models.Goods, goodsLabelMapping map[string][]*v1.GoodsLabel) []*v1.GoodsListItem {
	items := make([]*v1.GoodsListItem, 0, len(goodsList))
	for _, goods := range goodsList {
		var (
			price       = goods.BasePrice
			name        = goods.Name
			goodsLabels []*v1.GoodsLabel
		)

		translate, ok := goods.Translate.V[languageCode]
		// 如果不存在，使用英文兜底
		if !ok {
			translate, ok = goods.Translate.V["en"]
		}

		if ok && translate.Name != "" {
			name = translate.Name
		}

		if !goods.UseBasePrice {
			price = goods.SelectedPrice
		}

		for _, label := range goods.Labels.V {
			labelInfo, labelExists := s.ExistsLabel(label.Id)
			if !labelExists {
				continue
			}

			imageURL, translateExists := labelInfo.Values[languageCode]
			// 使用英文兜底
			if !translateExists {
				imageURL = labelInfo.Values["en"]
			}

			goodsLabels = append(goodsLabels, &v1.GoodsLabel{
				Type:  v1.GoodsLabelType_GoodsLabelImage,
				Image: urlformat.FullPath(imageURL, urlTemplate),
				Id:    labelInfo.Id,
			})
		}

		// 包邮
		if goods.FreeShipping {
			goodsLabels = append(goodsLabels, &v1.GoodsLabel{
				Type:  v1.GoodsLabelType_GoodsLabelText,
				Text:  i18n.GetWithDefaultEnglish("59869", languageCode), // 包邮
				Color: goodsLabelColor,
				Id:    "free_shipping",
			})
		}

		textGoodsLabels, ok := goodsLabelMapping[goods.GoodsId]
		if ok {
			goodsLabels = append(goodsLabels, textGoodsLabels...)
		}

		items = append(items, &v1.GoodsListItem{
			GoodsId:   goods.GoodsId,
			Image:     fullImage(goods.Image.V, v1.OrderSource_STORE),
			Name:      name,
			Price:     price,
			PriceShow: strconv.FormatFloat(float64(price), 'f', -1, 32),
			Labels:    goodsLabels,
			Status:    goods.Status,
			Category:  goods.Category,
		})
	}
	return items
}

// ============================= 内部 =============================

func (s *Service) findGoodsInfo(ctx context.Context, goodsIds []string) ([]*v1.GoodsListItem, error) {
	if len(goodsIds) <= 0 {
		return []*v1.GoodsListItem{}, nil
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	goodsList, err := s.goods.FindByGoodsIds(ctx, goodsIds)
	if err != nil {
		l.Errorf("goods.List Err:%v", err)
		return nil, err
	}
	return s.goodsToGoodsListItem(languageCode, goodsList, nil), nil
}

func toPlusNotation(num int) string {
	if num == 0 {
		return "0+"
	}
	// 计算数字的位数
	digits := len(strconv.Itoa(num))
	// 计算最高位的单位（如 100 → 10^2 = 100）
	magnitude := int(math.Pow10(digits - 1))
	// 向下取整到最高位（如 222 / 100 = 2 → 2 * 100 = 200）
	rounded := (num / magnitude) * magnitude
	return fmt.Sprintf("%d+", rounded)
}

func fullImages(images []*v1.Image, source v1.OrderSource) []*v1.Image {
	for _, image := range images {
		fullImage(image, source)
	}
	return images
}

func fullOrderImage(image *v1.Image, source v1.OrderSource, brokerId string) *v1.Image {
	if image == nil {
		return nil
	}

	switch source {
	case v1.OrderSource_EXHIBITION:
		image.Url = exhibitionFormat.FullPath(image.Url, urlTemplate)
	case v1.OrderSource_VPS, v1.OrderSource_REPORT:
		if brokerId == "" {
			image.Url = urlformat.FullPath(image.Url, urlTemplate)
		} else {
			image.Url = brokerFormat.FullPath(image.Url, urlTemplate)
		}
	default:
		image.Url = urlformat.FullPath(image.Url, urlTemplate)
	}
	return image
}

func fullImage(image *v1.Image, source v1.OrderSource) *v1.Image {
	if image == nil {
		return nil
	}

	if source == v1.OrderSource_EXHIBITION {
		image.Url = exhibitionFormat.FullPath(image.Url, urlTemplate)
	} else {
		image.Url = urlformat.FullPath(image.Url, urlTemplate)
	}
	return image
}
