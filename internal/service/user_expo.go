package service

import (
	"context"
	"encoding/base64"
	"os"
	"strconv"
	"strings"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"api-expo/pkg/qrcode"
	"api-expo/pkg/upstream"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/geo"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	goQRCode "github.com/skip2/go-qrcode"
)

func (s *Service) GetOpenExpo(ctx context.Context, in *common.EmptyRequest) (*v1.OpenExpoReply, error) {
	var (
		l                    = log.Context(ctx)
		clientIp, _          = icontext.ClientIPFrom(ctx)
		languageCode, _      = icontext.LanguageCodeFrom(ctx)
		userId, _            = icontext.UserIdFrom(ctx)
		headerCountryCode, _ = icontext.CountryCodeFrom(ctx)
		env                  = os.Getenv("ENVIRONMENT")
		// basicData, _    = icontext.BasicDataFrom(ctx)
	)

	countryCode, err := geo.GetCountryISOCode(clientIp)
	if countryCode == "" {
		l.Info("ip %s 未查询出数据", clientIp)
		if strings.ToLower(env) == "dev" {
			countryCode = headerCountryCode //由于测试环境获取不到外网ip，测试使用请求头国家
		}
	}
	if err != nil {
		l.Errorf("geo.GetCountryISOCode Err:%v", err)
		return nil, err
	}

	expos, err := s.expo.GetExpoByCountryCode(ctx, countryCode, v1.ExpoStatus_ExpoStatus_PROCESSING)
	if err != nil {
		l.Errorf("expo.GetExpoByCountryCode Err:%v", err)
		return nil, err
	}

	if len(expos) <= 0 {
		return &v1.OpenExpoReply{}, nil
	}

	var (
		expo   = expos[0]
		status = v1.ExpoStatus_ExpoStatus_UNKNOWN
		logo   string
		now    = expo.GetNow()
	)

	if now.After(expo.End) {
		status = v1.ExpoStatus_ExpoStatus_END
	} else if now.After(expo.Start) {
		status = v1.ExpoStatus_ExpoStatus_PROCESSING
	} else {
		status = v1.ExpoStatus_ExpoStatus_NOT_START
	}

	if expo.Extra != nil {
		lang, ok := expo.Extra.V.Languages[languageCode]
		if !ok {
			lang, ok = expo.Extra.V.Languages["en"]
		}

		if ok {
			logo = urlformat.FullPath(lang.Logo, urlTemplate)
		}
	}

	var (
		ticketCode   string
		ticketStatus v1.TicketStatus
		payment      string
		createdAt    int64
	)

	if userId != "" {
		var tickets []*models.Participant
		tickets, err = s.participant.FindByUserId(ctx, userId, expo.ID)
		if err != nil {
			l.Errorf("participant.ExistsByUserId Err:%v", err)
		}

		if len(tickets) > 0 {
			ticket := tickets[0]
			ticketCode = ticket.Code
			switch ticket.ApplyStatus {
			case v1.ApplyAudit_APPLY_AUDIT_Wait: // 审核中
				ticketStatus = v1.TicketStatus_TICKET_STATUS_REVIEW
			case v1.ApplyAudit_APPLY_AUDIT_NoPass: // 审核未通过
			default:
				switch ticket.TicketStatus {
				case 100: // 待领取
					ticketStatus = v1.TicketStatus_TICKET_STATUS_PASS
				case 110: // 已领取
					ticketStatus = v1.TicketStatus_TICKET_STATUS_PASS
					if !now.Before(expo.End) {
						ticketStatus = v1.TicketStatus_TICKET_STATUS_EXPIRED
					}
				case 200: // 已核销
					ticketStatus = v1.TicketStatus_TICKET_STATUS_USED
				case 401: // 已过期
					ticketStatus = v1.TicketStatus_TICKET_STATUS_EXPIRED
				}
			}
			createdAt = ticket.CreatedAt.Unix()
		}
	}

	// key := fmt.Sprintf("expo_open::%s:%d:%s", now.Format(time.DateOnly), expo.ID, basicData)
	// exists, err := s.redisCli.SetNX(ctx, key, expo.ID, time.Hour*24).Result()
	// if err != nil {
	// 	l.Errorf("redisCli.SetNX Err:%v", err)
	// 	return &v1.OpenExpoReply{}, nil
	// }

	// if !exists {
	// 	return &v1.OpenExpoReply{}, nil
	// }

	qrCode, err := qrcode.GenerateQRCodeWithLogo(ticketCode, goQRCode.High, 184)
	if err != nil {
		l.Errorf("qrcode.GenerateQRCodeWithLogo Err:%v", err)
		return nil, err
	}

	return &v1.OpenExpoReply{
		ExpoId:         strconv.FormatInt(expo.ID, 10),
		Status:         status,
		Logo:           logo,
		Name:           expo.Name,
		CountryCode:    expo.CountryCode,
		StartTime:      expo.Start.Unix(),
		TicketCode:     ticketCode,
		TicketStatus:   ticketStatus,
		PaymentTotal:   payment,
		CreatedAt:      createdAt,
		CurrencySymbol: "$",
		QrCodeContent:  base64.StdEncoding.EncodeToString(qrCode),
		QrCodeImage:    "https://h8imgs.ruiyin999.cn/QrCodeLogo.png",
	}, nil
}

func (s *Service) UserExpoRight(ctx context.Context, in *v1.UserExpoRightRequest) (*v1.UserExpoRightReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		clientIP, _     = icontext.ClientIPFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		activate        = false
		lock            = i18n.GetWithDefaultEnglish("62634", languageCode) //未解锁
		jumptitle       = i18n.GetWithDefaultEnglish("63182", languageCode) //"绑定实盘解锁权益"
	)

	// 参数校验
	if userId == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "Users need to log in.")
	}

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	unAuthenticatedService, err := s.upstream.IsUnAuthenticatedService(ctx, &upstream.UserStatusRequest{UserId: userId})
	if err != nil {
		l.Errorf("upstream.IsUnAuthenticatedService Err:%v ,userId: %s", err, userId)
		return nil, err
	}

	hasRealAccount, err := s.upstream.HasRealAccount(ctx, &upstream.UserRealAccountRequest{UserId: userId})
	if err != nil {
		l.Errorf("upstream.HasRealAccount Err:%v ,userId: %s", err, userId)
		return nil, err
	}

	city, err := geo.GetCity(clientIP)
	if err != nil {
		l.Errorf("geo.GetCity Err:%v", err)
		return nil, err
	}

	if unAuthenticatedService {
		//只要该用户是未认证的服务商，就没有自由聊天的权益
		activate = false
	} else if hasRealAccount || (city != nil && city.CityName == expo.Location) {
		//满足任一条件都可以自由聊天: 1. 用户已绑定实盘  2. 用户所在地 和 展会举办地一致
		activate = true
	}

	//是否解锁 只和绑定实盘的任务有关
	if hasRealAccount {
		lock = i18n.GetWithDefaultEnglish("62635", languageCode) //已解锁
		jumptitle = ""
	}

	expoChat := v1.ExpoRight{
		Type:  v1.ExpoRightType_ExpoRightType_Chat,
		Title: i18n.GetWithDefaultEnglish("62638", languageCode), //自由聊天
		Lock:  lock,
		Description: []string{
			i18n.GetWithDefaultEnglish("62639", languageCode), //可以与其他报名用户自由私信，认识志同道合的朋友。
			i18n.GetWithDefaultEnglish("62640", languageCode), //可以与参展大咖线上互动，获取更多行业知识。
			i18n.GetWithDefaultEnglish("62641", languageCode), //可以与您感兴趣的交易商实时沟通。
		},
		Icon:      "https://expoliveimgs.zy223.com/expologo/expo_chat.png_wiki-template-global",
		JumpTitle: jumptitle,
		Activate:  activate,
	}

	//展会互动的数据是固定的，默认已解锁
	expoInteraction := v1.ExpoRight{
		Type:  v1.ExpoRightType_ExpoRightType_Interaction,
		Title: i18n.GetWithDefaultEnglish("62633", languageCode), //展会互动
		Lock:  i18n.GetWithDefaultEnglish("62635", languageCode), //已解锁
		Description: []string{
			i18n.GetWithDefaultEnglish("62636", languageCode), //您可以进入展会互动区，与其他参会者共同讨论展会
			i18n.GetWithDefaultEnglish("62637", languageCode), //您可以进入展会话题，自由发帖，将自己的展会经历分享在WikiFX社区。
		},
		Icon:     "https://expoliveimgs.zy223.com/expologo/expo_interaction.png_wiki-template-global",
		Activate: true,
	}

	return &v1.UserExpoRightReply{
		Rights: []*v1.ExpoRight{&expoInteraction, &expoChat},
	}, nil
}

func (s *Service) UserExpoChat(ctx context.Context, in *common.EmptyRequest) (*v1.UserExpoChatReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	// 用户未登录，默认不显示
	if userId == "" {
		return &v1.UserExpoChatReply{Activate: false}, nil
	}

	hasRealAccount, err := s.upstream.HasRealAccount(ctx, &upstream.UserRealAccountRequest{UserId: userId})
	if err != nil {
		l.Errorf("upstream.HasRealAccount Err:%v ,userId: %s", err, userId)
		return nil, err
	}

	if hasRealAccount {
		//用户已绑定实盘 app底部不显示
		return &v1.UserExpoChatReply{Activate: false}, nil
	} else {
		//未绑定实盘
		return &v1.UserExpoChatReply{
			Icon:     "https://expoliveimgs.zy223.com/expologo/chat.png_wiki-template-global",
			Task:     i18n.GetWithDefaultEnglish("63180", languageCode), //完成任务，立刻与大咖沟通！
			Chat:     i18n.GetWithDefaultEnglish("63181", languageCode), //自由聊天 · 未解锁
			Activate: true,                                              //app 底部需要显示
		}, nil
	}
}

func (s *Service) ChatPermission(ctx context.Context, in *v1.ChatRequest) (*v1.ChatReply, error) {
	var (
		l           = log.Context(ctx)
		userId, _   = icontext.UserIdFrom(ctx)
		clientIP, _ = icontext.ClientIPFrom(ctx)
		activate    = false
		message     = ""
	)

	// 参数校验
	if userId == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "Users need to log in.")
	}

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	registration, err := s.participant.ExistsByUserId(ctx, expoId, userId)
	if err != nil {
		l.Errorf("participant.ExistsByUserId Err:%v", err)
		// 默认未报名
	}

	if !registration {
		return &v1.ChatReply{
			Activate: false,
			Message:  "请报名当前展会",
		}, nil
	}

	unAuthenticatedService, err := s.upstream.IsUnAuthenticatedService(ctx, &upstream.UserStatusRequest{UserId: userId})
	if err != nil {
		l.Errorf("upstream.IsUnAuthenticatedService Err:%v ,userId: %s", err, userId)
		return nil, err
	}

	hasRealAccount, err := s.upstream.HasRealAccount(ctx, &upstream.UserRealAccountRequest{UserId: userId})
	if err != nil {
		l.Errorf("upstream.HasRealAccount Err:%v ,userId: %s", err, userId)
		return nil, err
	}

	city, err := geo.GetCity(clientIP)
	if err != nil {
		l.Errorf("geo.GetCity Err:%v", err)
		return nil, err
	}

	if unAuthenticatedService {
		//只要该用户是未认证的服务商，就没有自由聊天的权益
		activate = false
		message = "身份未验证，无法私聊"
	} else if hasRealAccount || (city != nil && city.CityName == expo.Location) {
		//满足任一条件都可以自由聊天: 1. 用户已绑定实盘  2. 用户所在地 和 展会举办地一致
		activate = true
		message = ""
	} else {
		activate = false
		message = "请完成绑定实盘任务"
	}

	return &v1.ChatReply{
		Activate: activate,
		Message:  message,
	}, nil
}
