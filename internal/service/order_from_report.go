package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"gold_store/api/common"
	"gold_store/pkg/id"
	"gold_store/pkg/vgoods"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/go-kratos/kratos/v2/log"
	"golang.org/x/sync/errgroup"
	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gorm.io/gorm"
)

func (s *Service) ReportOrderPush(ctx context.Context, in *v1.ReportOrderPushRequest) (*v1.ReportOrderPushReply, error) {
	var (
		l        = log.Context(ctx)
		reqId, _ = icontext.RequestIdFrom(ctx)
	)

	trader, err := s.virtualGoods.GetBroker(ctx, &vgoods.GetBrokerRequest{
		Codes:    []string{in.TraderCode},
		Language: "en",
	})
	if err != nil {
		l.Errorf("virtualGoods.GetBroker<%s,%s> Err:%v", in.OrderId, in.TraderCode, err)
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("报告订单【%s】获取交易商错误：【%s】Err:%v", in.OrderId, in.TraderCode, err))
		return nil, err
	}

	var (
		traderLogo string
		traderName string
	)

	if len(trader.Result) > 0 {
		traderLogo = trader.Result[0].Logo
		traderName = trader.Result[0].EnglishShortName
	} else {
		traderLogo = statisticReportImage
	}

	orderTime, err := strconv.ParseInt(in.OrderTime, 10, 64)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("报告订单【%s】OrderTime【%d】Err:%v", in.OrderId, in.OrderTime, err))
		return nil, err
	}

	var (
		goodsName = "62266" // {0}-企业信用报告
		specDesc  = "62268" // {0}企业信用报告
	)

	if in.ReportType == 2 {
		goodsName = "62267" // {0}-研究报告
		specDesc = "62269"  // {0}研究报告
	}

	var (
		goodsId    = id.GenerateId(id.BusinessGoods, id.WithChannel("R"))
		goodsImage = &v1.Image{
			Url: traderLogo,
		}

		reportLanguage = ""
		// 这里没有更新时间；所以只能每次添加新的
		goodsVersion  = time.Now().Unix()
		goodsSnapshot = &models.GoodsSnapshot{
			GoodsId: goodsId,
			Version: goodsVersion,
			Snapshot: &igorm.CustomValue[*models.Snapshot]{
				V: &models.Snapshot{
					Goods: &models.Goods{
						Category:      v1.GoodsCategory_GOODS_CATEGORY_REPORT,
						GoodsId:       goodsId,
						Name:          goodsName,
						BasePrice:     in.Price,
						UseBasePrice:  true,
						SelectedPrice: in.Price,
						Status:        v1.GoodsStatus_GoodsStatusOn,
						FreeShipping:  true,
						Image: &igorm.CustomValue[*v1.Image]{
							V: goodsImage,
						},
						Translate: &igorm.CustomValue[map[string]*v1.GoodsTranslate]{},
					},
				},
			},
		}
	)

	// 1:中文简体；2:英语；3：中文繁体
	switch in.ReportLanguage {
	case 1:
		reportLanguage = "中文(简体)"
	case 2:
		reportLanguage = "English"
	case 3:
		reportLanguage = "中文(繁体)"
	default:
		reportLanguage = "中文(简体)"
	}

	err = s.goodsSnapshot.Add(ctx, goodsSnapshot)
	if err != nil {
		l.Errorf("goodsSnapshot.Add Err:%v", err)
		return nil, err
	}

	var (
		displayAmount  string
		paymentMethod  = v1.PaymentMethod_PAYMENT_METHOD_UNKNOWN
		currencySymbol string
	)

	if in.PayType == 1 {
		paymentMethod = v1.PaymentMethod_GOLD
	}

	// 美元
	if in.SymbolType == 2 {
		currencySymbol = "$"
		displayAmount = fmt.Sprintf("$%.2f", in.Price)
	}

	var (
		orderNo       = in.OrderId
		operationNo   = ""
		paymentNo     = id.GenerateId(id.BusinessPayment, id.WithChannel(strconv.Itoa(int(paymentMethod))))
		paymentStatus = v1.PaymentStatus_PaymentStatusPAID
		orderStatus   = v1.OrderStatus_COMPLETE
		price         = in.Price
		totalAmount   = in.Price
		paidTotal     = in.Price
		quantity      = int32(1)
		orderExtra    = &igorm.CustomValue[*models.OrderExtra]{
			V: &models.OrderExtra{
				DisplayAmount:    displayAmount,
				SpecDesc:         map[string]string{},
				ReportTraderCode: in.TraderCode,
				ReportLanguage:   reportLanguage,
				ReportType:       in.ReportType,
				TraderName:       traderName,
			},
		}
		paidTime = time.Unix(orderTime, 0).UTC()
	)

	_, err = s.order.GetByOrderNo(ctx, orderNo)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("order.GetByOrderNo<%s> Err:%v", orderNo, err)
		return nil, err
	}

	// 说明存在
	if err == nil {
		var payment *models.Payment
		payment, err = s.payment.GetByOrderNo(ctx, orderNo)
		if err != nil {
			l.Errorf("payment.GetByOrderNo<%s> Err:%v", orderNo, err)
			return nil, err
		}

		var orderExtraValue any
		orderExtraValue, err = orderExtra.Value()
		if err != nil {
			l.Errorf("orderExtra.Value Err:%v", err)
			return nil, err
		}

		tx := s.goods.Begin()
		defer func() {
			if err == nil {
				tx.Commit()
			} else {
				tx.Rollback()
			}
		}()

		// 4、更新支付单信息
		err = s.payment.UpdatePayment(ctx, &models.Payment{
			PaymentNo:   payment.PaymentNo,
			OperationNo: operationNo,
			PaidTotal:   totalAmount,
			PaidTime:    paidTime,
			Status:      paymentStatus,
		}, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("payment.UpdatePayment Err:%v", err)
			return nil, err
		}

		// 5、更新支付订单
		err = s.order.Update(ctx, orderNo, map[string]interface{}{
			"status":          orderStatus,
			"total_amount":    totalAmount,
			"extra":           orderExtraValue,
			"currency_symbol": currencySymbol,
		}, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("order.UpdateStatus Err:%v", err)
			return nil, err
		}
		return &v1.ReportOrderPushReply{}, nil
	}

	var (
		newOrder = &models.Order{
			OrderNo:        in.OrderId,
			Source:         v1.OrderSource_REPORT,
			PaymentMethod:  paymentMethod,
			Platform:       v1.Platform_PlatformUNKNOWN,
			UserId:         in.UserId,
			Address:        &igorm.CustomValue[*models.Address]{},
			Quantity:       quantity,
			CurrencySymbol: currencySymbol,
			TotalAmount:    totalAmount,
			Status:         orderStatus,
			GoodsName:      goodsName,
			Extra:          orderExtra,
			Model: gorm.Model{
				CreatedAt: paidTime,
				UpdatedAt: paidTime,
			},
		}

		newOrderItems = []*models.OrderItem{
			{
				OrderNo:         orderNo,
				GoodsId:         goodsId,
				Price:           price,
				PriceUnit:       currencySymbol,
				GoodsSnapshotId: goodsSnapshot.ID,
				Quantity:        quantity,
				TotalAmount:     totalAmount,
				GoodsName:       goodsName,
				SpecDesc:        specDesc,
				Model: gorm.Model{
					CreatedAt: paidTime,
					UpdatedAt: paidTime,
				},
			},
		}

		newPayment = &models.Payment{
			PaymentNo:   paymentNo,
			OrderNo:     orderNo,
			OperationNo: operationNo,
			TotalAmount: totalAmount,
			PaidTotal:   paidTotal,
			PaidMethod:  paymentMethod,
			Status:      paymentStatus,
			PaidTime:    paidTime,
			Model: gorm.Model{
				CreatedAt: paidTime,
				UpdatedAt: paidTime,
			},
		}
	)

	tx := s.goods.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	// 1、添加订单
	err = s.order.Add(ctx, newOrder, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("order.Add Err:%v", err)
		return nil, err
	}

	// 3、添加订单项
	err = s.orderItem.BatchAdd(ctx, newOrderItems, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("orderItem.BatchAdd Err:%v", err)
		return nil, err
	}

	// 4、添加支付
	err = s.payment.Add(ctx, newPayment, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("payment.Add Err:%v", err)
		return nil, err
	}
	return &v1.ReportOrderPushReply{}, nil
}

func (s *Service) FetchReportOrders(ctx context.Context, in *common.EmptyRequest) (*common.EmptyReply, error) {
	go s.fetchHistoryReportOrders()
	return &common.EmptyReply{}, nil
}

func (s *Service) fetchHistoryReportOrders() {
	var (
		ctx        = icontext.WithRequestId(context.Background(), reqid.GenRequestID())
		l          = log.Context(ctx)
		total      = 0
		createTime string
		eg, _      = errgroup.WithContext(ctx)
	)
	eg.SetLimit(100)

	for {
		res, err := s.virtualGoods.FindReportList(ctx, &vgoods.FindReportListRequest{
			PageSize:   200,
			CreateTime: createTime,
		})
		if err != nil {
			l.Errorf("FindReportList Err:%v", err)
			return
		}
		if len(res.Result.Items) <= 0 {
			break
		}

		total += len(res.Result.Items)
		for _, item := range res.Result.Items {
			eg.Go(func() error {
				_, err = s.ReportOrderPush(ctx, &v1.ReportOrderPushRequest{
					UserId:         item.UserId,
					OrderId:        item.OrderId,
					TraderCode:     item.TraderCode,
					ReportType:     item.ReportType,
					OrderTime:      item.OrderTime,
					Price:          item.Price,
					SymbolType:     item.SymbolType,
					ReportLanguage: item.ReportLanguage,
					PayType:        item.PayType,
				})
				if err != nil {
					l.Errorf("ReportOrderPush Err:%v", err)
				}
				return nil
			})

		}
		createTime = res.Result.NextTime
	}
	_ = eg.Wait()
	fmt.Println("结束", total)
}
