package service

import (
	"context"
	"errors"
	"fmt"
	"sync"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"api-expo/internal/util"

	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) ListLiveImage(ctx context.Context, request *v1.ListLiveImageRequest) (*v1.ListLiveImageReply, error) {
	if request.ExpoId == 0 {
		return nil, errors.New("展会ID不能为空")
	}

	page := request.Page
	if page <= 0 {
		page = 1
	}

	size := request.Size
	if size <= 0 || size > 100 {
		size = 20
	}

	images, total, err := s.expoImage.GetPageByExpoID(ctx, request.ExpoId, int(page), int(size))
	if err != nil {
		log.Context(ctx).Errorf("GetPageByExpoID failed: %v", err)
		return nil, err
	}

	var urls []string
	for _, img := range images {
		urls = append(urls, img.ImageUrl)
	}

	var adminImages []*v1.AdminLiveImage
	if len(urls) > 0 {
		adminImages = append(adminImages, &v1.AdminLiveImage{
			ExpoId: request.ExpoId,
			Urls:   urls,
		})
	}

	log.Context(ctx).Infof("ListLiveImage completed. ExpoId: %d, Page: %d, Size: %d, Total: %d, ImageCount: %d",
		request.ExpoId, page, size, total, len(urls))

	return &v1.ListLiveImageReply{
		List:  adminImages,
		Page:  page,
		Size:  size,
		Total: int32(total),
	}, nil
}

// SyncLiveImage 同步展会图片（批量）
func (s *Service) SyncLiveImage(ctx context.Context, req *v1.SyncLiveImageRequest) (*v1.SyncLiveImageReply, error) {
	// 参数验证
	if req.ExpoId == 0 {
		return nil, errors.New("展会ID不能为空")
	}

	// 验证展会是否存在
	_, err := s.expo.Get(ctx, req.ExpoId)
	if err != nil {
		log.Context(ctx).Errorf("Get expo failed: %v", err)
		return nil, errors.New("展会不存在")
	}

	// 过滤空的图片URL，保存原始地址到数据库
	var validImageUrls []string
	for _, imageUrl := range req.ImageUrlList {
		if imageUrl != "" {
			validImageUrls = append(validImageUrls, imageUrl)
		}
	}

	log.Context(ctx).Infof("开始同步展会图片，展会ID: %d，提交图片数: %d", req.ExpoId, len(validImageUrls))

	// ========== 第一阶段：同步图片到数据库（增删操作）==========

	// 1. 获取数据库中已存在的图片
	existingImages, err := s.expoImage.GetByExpoID(ctx, uint64(req.ExpoId))
	if err != nil {
		log.Context(ctx).Errorf("Get existing expo images failed: %v", err)
		return nil, errors.New("获取已存在图片失败")
	}

	// 构建已存在图片的映射
	existingImageMap := make(map[string]bool)
	for _, img := range existingImages {
		existingImageMap[img.ImageUrl] = true
	}

	// 构建新图片的映射
	newImageMap := make(map[string]bool)
	for _, url := range validImageUrls {
		newImageMap[url] = true
	}

	// 2. 找出需要新增的图片
	var imagesToAdd []string
	for _, url := range validImageUrls {
		if !existingImageMap[url] {
			imagesToAdd = append(imagesToAdd, url)
		}
	}

	// 3. 找出需要删除的图片
	var imagesToDelete []uint64
	for _, img := range existingImages {
		if !newImageMap[img.ImageUrl] {
			imagesToDelete = append(imagesToDelete, uint64(img.ID))
		}
	}

	addedCount := 0
	deletedCount := 0

	// 4. 执行新增操作
	if len(imagesToAdd) > 0 {
		var images []*models.ExpoImage
		for _, imageUrl := range imagesToAdd {
			images = append(images, &models.ExpoImage{
				ExpoId:   req.ExpoId,
				ImageUrl: imageUrl,
			})
		}

		err = s.expoImage.BatchAdd(ctx, images)
		if err != nil {
			log.Context(ctx).Errorf("Batch add expo images failed: %v", err)
			return nil, errors.New("批量添加图片失败")
		}
		addedCount = len(imagesToAdd)
		log.Context(ctx).Infof("成功添加图片数量: %d", addedCount)
	}

	// 5. 执行删除操作
	if len(imagesToDelete) > 0 {
		for _, imageId := range imagesToDelete {
			err = s.expoImage.Delete(ctx, imageId)
			if err != nil {
				log.Context(ctx).Errorf("Delete expo image failed: %v, imageId: %d", err, imageId)
				// 删除失败不中断整个流程，继续删除其他图片
				continue
			}
			deletedCount++
		}
		log.Context(ctx).Infof("成功删除图片数量: %d", deletedCount)
	}

	totalSubmitted := len(validImageUrls)
	existingCount := len(validImageUrls) - addedCount

	log.Context(ctx).Infof("数据库图片同步完成. 提交: %d, 新增: %d, 删除: %d, 已存在: %d",
		totalSubmitted, addedCount, deletedCount, existingCount)

	// ========== 第二阶段：同步图片到人才库 ==========
	var taskId string
	syncReq := &v1.SyncFaceRequest{
		ExpoId: req.ExpoId,
	}
	syncReply, syncErr := s.SyncFace(ctx, syncReq)
	if syncErr != nil {
		log.Context(ctx).Errorf("SyncFace failed: %v", syncErr)
		// 数据库操作成功，但人才库同步失败时的处理
		if addedCount > 0 || deletedCount > 0 {
			return nil, errors.New("图片数据库同步成功，但人才库同步失败，请稍后手动同步")
		} else {
			return nil, errors.New("人才库同步失败，请稍后重试")
		}
	} else {
		taskId = syncReply.TaskId
		log.Context(ctx).Infof("人才库同步任务已启动. TaskId: %s", taskId)
	}

	return &v1.SyncLiveImageReply{
		ExpoId:    req.ExpoId,
		SubmitNum: int64(totalSubmitted),
	}, nil
}

// SyncFace 异步触发展会图片同步
func (s *Service) SyncFace(ctx context.Context, req *v1.SyncFaceRequest) (*v1.SyncFaceReply, error) {
	var (
		l = log.Context(ctx)
	)

	l.Infof("开始创建异步同步任务，展会ID: %d", req.ExpoId)

	// 1. 验证展会是否存在
	expo, err := s.expo.Get(ctx, req.ExpoId)
	if err != nil {
		return &v1.SyncFaceReply{
			Started: false,
			Message: fmt.Sprintf("获取展会信息失败: %v", err),
			ExpoId:  req.ExpoId,
		}, nil
	}

	// 2. 检查或创建展会对应的人员库
	groupID, _, err := s.ensureExpoFaceGroup(ctx, expo)
	if err != nil {
		return &v1.SyncFaceReply{
			Started: false,
			Message: fmt.Sprintf("确保展会人员库失败: %v", err),
			ExpoId:  req.ExpoId,
		}, nil
	}

	// 3. 创建同步任务
	task, err := util.GetGlobalSyncTaskManager().CreateTask(req.ExpoId, groupID)
	if err != nil {
		return &v1.SyncFaceReply{
			Started: false,
			Message: fmt.Sprintf("创建同步任务失败: %v", err),
			ExpoId:  req.ExpoId,
			GroupId: groupID,
		}, nil
	}

	// 4. 启动异步同步任务
	go s.executeAsyncSyncTask(context.Background(), task)

	log.Infof("异步同步任务已启动，任务ID: %s, 展会ID: %d", task.TaskID, req.ExpoId)

	return &v1.SyncFaceReply{
		Started: true,
		Message: "同步任务已启动，正在后台执行",
		TaskId:  task.TaskID,
		ExpoId:  req.ExpoId,
		GroupId: groupID,
	}, nil
}

// GetSyncStatus  查询同步状态
func (s *Service) GetSyncStatus(ctx context.Context, req *v1.GetSyncStatusRequest) (*v1.GetSyncStatusReply, error) {
	// 根据展会ID获取任务
	task, exists := util.GetGlobalSyncTaskManager().GetTaskByExpoID(req.ExpoId)
	if !exists {
		return &v1.GetSyncStatusReply{
			ExpoId:  req.ExpoId,
			Status:  v1.SyncStatus_SYNC_STATUS_NOT_STARTED,
			Message: "未找到该展会的同步任务",
		}, nil
	}

	// 构建响应
	reply := &v1.GetSyncStatusReply{
		ExpoId:          task.ExpoID,
		GroupId:         task.GroupID,
		Status:          task.Status,
		TaskId:          task.TaskID,
		TotalImages:     task.TotalImages,
		ProcessedImages: task.ProcessedImages,
		SuccessCount:    task.SuccessCount,
		FailedCount:     task.FailedCount,
		SkippedCount:    task.SkippedCount,
		DeletedCount:    task.DeletedCount,
		NoFaceCount:     task.NoFaceCount,
		StartedAt:       task.StartedAt.Unix(),
		DurationMs:      task.GetDurationMs(),
		Message:         task.Message,
		ProgressPercent: task.GetProgress(),
		CurrentImage:    task.CurrentImage,
	}

	if task.CompletedAt != nil {
		reply.CompletedAt = task.CompletedAt.Unix()
	}

	// 构建图片详情列表
	taskItems := task.GetTaskItems()
	for _, item := range taskItems {
		replyItem := &v1.ImageSyncItem{
			ImageUrl:     urlformat.FullPath(item.ImageURL),
			Action:       item.Action,
			Status:       item.Status,
			ErrorMessage: item.ErrorMessage,
			FaceCount:    item.FaceCount,
			RetryCount:   item.RetryCount,
		}
		if item.ProcessedAt != nil {
			replyItem.ProcessedAt = item.ProcessedAt.Unix()
		}
		reply.Items = append(reply.Items, replyItem)
	}

	return reply, nil
}

// executeAsyncSyncTask 执行异步同步任务
func (s *Service) executeAsyncSyncTask(ctx context.Context, task *util.SyncTask) {
	log.Infof("开始执行异步同步任务: %s, 展会ID: %d", task.TaskID, task.ExpoID)

	// 更新任务状态为运行中
	err := util.GetGlobalSyncTaskManager().UpdateTaskStatus(task.TaskID, v1.SyncStatus_SYNC_STATUS_RUNNING, "正在同步展会图片...")
	if err != nil {
		log.Errorf("更新任务状态失败: %v", err)
		return
	}

	defer func() {
		if r := recover(); r != nil {
			log.Errorf("异步同步任务出现异常: %v", r)
			util.GetGlobalSyncTaskManager().UpdateTaskStatus(task.TaskID, v1.SyncStatus_SYNC_STATUS_FAILED, fmt.Sprintf("任务异常: %v", r))
		}
	}()

	// 1. 获取展会的所有图片
	expoImages, err := s.expoImage.GetByExpoID(ctx, uint64(task.ExpoID))
	if err != nil {
		log.Errorf("获取展会图片失败: %v", err)
		util.GetGlobalSyncTaskManager().UpdateTaskStatus(task.TaskID, v1.SyncStatus_SYNC_STATUS_FAILED, fmt.Sprintf("获取展会图片失败: %v", err))
		return
	}

	// 2. 获取已同步的图片（通过 face_photo_relations 表查询）
	syncedPhotoURLs, err := s.getSyncedPhotoURLsByGroup(ctx, task.GroupID)
	if err != nil {
		log.Errorf("获取已同步图片列表失败: %v", err)
		util.GetGlobalSyncTaskManager().UpdateTaskStatus(task.TaskID, v1.SyncStatus_SYNC_STATUS_FAILED, fmt.Sprintf("获取已同步图片列表失败: %v", err))
		return
	}

	// 3. 初始化任务项（需要新增的图片）
	for _, expoImage := range expoImages {
		item := &util.SyncTaskItem{
			ImageURL: expoImage.ImageUrl,
			Action:   v1.SyncAction_SYNC_ACTION_ADD,
			Status:   v1.ImageSyncStatus_IMAGE_SYNC_STATUS_PENDING,
		}

		// 如果已同步，标记为跳过
		if _, exists := syncedPhotoURLs[expoImage.ImageUrl]; exists {
			item.Action = v1.SyncAction_SYNC_ACTION_SKIP
			item.Status = v1.ImageSyncStatus_IMAGE_SYNC_STATUS_SKIPPED
		}

		util.GetGlobalSyncTaskManager().AddTaskItem(task.TaskID, item)
	}

	// 4. 查找需要删除的图片
	expoImageURLs := make(map[string]bool)
	for _, expoImage := range expoImages {
		expoImageURLs[expoImage.ImageUrl] = true
	}

	for syncedURL := range syncedPhotoURLs {
		if _, exists := expoImageURLs[syncedURL]; !exists {
			item := &util.SyncTaskItem{
				ImageURL: syncedURL,
				Action:   v1.SyncAction_SYNC_ACTION_DELETE,
				Status:   v1.ImageSyncStatus_IMAGE_SYNC_STATUS_PENDING,
			}
			util.GetGlobalSyncTaskManager().AddTaskItem(task.TaskID, item)
		}
	}

	totalItems := task.GetItemCount()

	if totalItems == 0 {
		util.GetGlobalSyncTaskManager().UpdateTaskStatus(task.TaskID, v1.SyncStatus_SYNC_STATUS_COMPLETED, "没有需要同步的图片")
		return
	}

	log.Infof("开始处理 %d 个图片项，任务ID: %s", totalItems, task.TaskID)

	// 并发处理图片（限制并发数）
	const maxConcurrency = 3
	semaphore := make(chan struct{}, maxConcurrency)
	var wg sync.WaitGroup

	pendingItems := task.GetPendingItems()
	for imageURL, item := range pendingItems {
		wg.Add(1)
		go func(url string, taskItem *util.SyncTaskItem) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			s.processSyncTaskItem(ctx, task.TaskID, task.GroupID, url, taskItem)
		}(imageURL, item)
	}

	wg.Wait()

	// 6. 检查最终结果并更新任务状态
	finalSuccessCount, finalFailedCount, finalSkippedCount, finalDeletedCount, finalNoFaceCount := task.GetFinalCounts()

	var finalStatus v1.SyncStatus
	var finalMessage string

	if finalFailedCount == 0 {
		finalStatus = v1.SyncStatus_SYNC_STATUS_COMPLETED
		finalMessage = fmt.Sprintf("同步完成: 成功 %d, 跳过 %d, 删除 %d, 无人脸 %d", finalSuccessCount, finalSkippedCount, finalDeletedCount, finalNoFaceCount)
	} else {
		finalStatus = v1.SyncStatus_SYNC_STATUS_COMPLETED // 部分失败也算完成
		finalMessage = fmt.Sprintf("同步完成(有失败): 成功 %d, 失败 %d, 跳过 %d, 删除 %d, 无人脸 %d", finalSuccessCount, finalFailedCount, finalSkippedCount, finalDeletedCount, finalNoFaceCount)
	}

	util.GetGlobalSyncTaskManager().UpdateTaskStatus(task.TaskID, finalStatus, finalMessage)
	log.Infof("异步同步任务完成: %s, %s", task.TaskID, finalMessage)
}

// processSyncTaskItem 处理单个同步任务项
func (s *Service) processSyncTaskItem(ctx context.Context, taskID, groupID, imageURL string, item *util.SyncTaskItem) {
	log.Infof("开始处理图片: %s, 操作: %v", imageURL, item.Action)

	// 设置当前处理的图片
	util.GetGlobalSyncTaskManager().SetCurrentImage(taskID, imageURL)

	// 更新状态为处理中
	util.GetGlobalSyncTaskManager().UpdateTaskItem(taskID, imageURL, v1.ImageSyncStatus_IMAGE_SYNC_STATUS_PROCESSING, "", 0)

	var err error
	var faceCount int32

	switch item.Action {
	case v1.SyncAction_SYNC_ACTION_ADD:
		faceCount, err = s.syncImageToFaceGroup(ctx, groupID, imageURL)
	case v1.SyncAction_SYNC_ACTION_DELETE:
		err = s.deleteImageFromFaceGroup(ctx, groupID, imageURL)
	case v1.SyncAction_SYNC_ACTION_SKIP:
		// 跳过的图片直接标记为成功
		util.GetGlobalSyncTaskManager().UpdateTaskItem(taskID, imageURL, v1.ImageSyncStatus_IMAGE_SYNC_STATUS_SKIPPED, "", 0)
		return
	}

	if err != nil {
		// 检查是否为应该跳过的错误
		if _, isSkippable := err.(*util.SkippableError); isSkippable {
			log.Infof("图片被过滤（无人脸）: %s, 原因: %v", imageURL, err)
			util.GetGlobalSyncTaskManager().UpdateTaskItem(taskID, imageURL, v1.ImageSyncStatus_IMAGE_SYNC_STATUS_NO_FACE, err.Error(), 0)
		} else {
			log.Errorf("处理图片失败: %s, 错误: %v", imageURL, err)
			util.GetGlobalSyncTaskManager().UpdateTaskItem(taskID, imageURL, v1.ImageSyncStatus_IMAGE_SYNC_STATUS_FAILED, err.Error(), 0)
		}
	} else {
		var status v1.ImageSyncStatus
		if item.Action == v1.SyncAction_SYNC_ACTION_DELETE {
			status = v1.ImageSyncStatus_IMAGE_SYNC_STATUS_DELETED
		} else {
			status = v1.ImageSyncStatus_IMAGE_SYNC_STATUS_SUCCESS
		}
		util.GetGlobalSyncTaskManager().UpdateTaskItem(taskID, imageURL, status, "", faceCount)
		log.Infof("成功处理图片: %s", imageURL)
	}
}
