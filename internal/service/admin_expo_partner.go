package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"net/url"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

func (s *Service) checkExpoPartner(in *v1.ExpoPartnerInfo) error {
	if in.ExpoId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.Name == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "合作伙伴名称不能为空")
	}

	if in.Logo == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "合作伙伴logo不能为空")
	}

	_, err := url.Parse(in.Website)
	if err != nil {
		return innErr.WithMessage(innErr.ErrBadRequest, "合作伙伴网站格式错误")
	}
	return nil
}

func (s *Service) AddExpoPartner(ctx context.Context, in *v1.ExpoPartnerInfo) (*v1.AddExpoPartnerReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoPartner(in)
	if err != nil {
		return nil, err
	}

	ntype, err := strconv.Atoi(in.Type)
	if err != nil {
		return nil, err
	}

	id, err := s.expoPartner.Add(ctx, &models.ExpoPartner{
		ExpoId:  in.ExpoId,
		Name:    in.Name,
		Logo:    in.Logo,
		Website: in.Website,
		Enable:  in.Enable,
		Type:    ntype,
		Rank:    int(in.Rank),
		Creator: in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoPartnerExtra]{
			V: &models.ExpoPartnerExtra{},
		},
	})
	if err != nil {
		l.Errorf("expoPartner.Add Err:%v", err)
		return nil, err
	}
	return &v1.AddExpoPartnerReply{
		Id: int64(id),
	}, nil
}

func (s *Service) GetExpoPartnerType(_ context.Context, _ *v1.GetExpoPartnerTypeRequest) (*v1.GetExpoPartnerTypeReply, error) {
	return &v1.GetExpoPartnerTypeReply{
		Items: partnerItems,
	}, nil
}

func (s *Service) GetExpoPartner(ctx context.Context, in *v1.GetExpoPartnerRequest) (*v1.ExpoPartnerInfo, error) {
	l := log.Context(ctx)

	partner, err := s.expoPartner.GetById(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoPartner.GetById Err:%v", err)
		return nil, err
	}
	return s.expoPartnerToGRPC(partner), nil
}

func (s *Service) UpdateExpoPartner(ctx context.Context, in *v1.ExpoPartnerInfo) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoPartner(in)
	if err != nil {
		return nil, err
	}

	ntype, err := strconv.Atoi(in.Type)
	if err != nil {
		return nil, err
	}

	err = s.expoPartner.Update(ctx, &models.ExpoPartner{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId:  in.ExpoId,
		Name:    in.Name,
		Logo:    in.Logo,
		Website: in.Website,
		Enable:  in.Enable,
		Type:    ntype,
		Rank:    int(in.Rank),
		Creator: in.Creator,
	})
	if err != nil {
		l.Errorf("Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoPartnerEnable(ctx context.Context, in *v1.SetExpoPartnerEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.expoPartner.UpdateEnable(ctx, &models.ExpoPartner{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId: in.ExpoId,
		Enable: in.Enable,
	})
	if err != nil {
		l.Errorf("Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListExpoPartner(ctx context.Context, in *v1.ListExpoPartnerRequest) (*v1.ListExpoPartnerReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	conditions := map[string]interface{}{"expo_id": in.ExpoId}
	if in.Type != 0 {
		conditions["type"] = int(in.Type)
	}

	if in.Keyword != "" {
		conditions["name"] = in.Keyword
	}

	count, err := s.expoPartner.CountByCondition(ctx, conditions)
	if err != nil {
		l.Errorf("expoPartner.CountByCondition Err:%v", err)
		return nil, err
	}

	partners, err := s.expoPartner.FindByPage(ctx, conditions, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoPartner.FindByPage Err:%v", err)
		return nil, err
	}

	values := make([]*v1.ExpoPartnerInfo, 0, len(partners))
	for _, partner := range partners {
		values = append(values, s.expoPartnerToGRPC(partner))
	}

	return &v1.ListExpoPartnerReply{
		Partners: values,
		Total:    count,
	}, nil
}

func (s *Service) DeleteExpoPartner(ctx context.Context, in *v1.DeleteExpoPartnerRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoPartner.Delete(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoPartner.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ImportExpoPartner(ctx context.Context, in *v1.ImportExpoPartnerRequest) (*common.EmptyReply, error) {
	if in.ExpoId < 0 {
		return nil, innErr.ErrBadRequest
	}

	l := log.Context(ctx)
	content, err := base64.StdEncoding.DecodeString(in.Data)
	if err != nil {
		return nil, err
	}

	fInfo, err := excelize.OpenReader(bytes.NewReader(content))
	if err != nil {
		l.Errorf("OpenReader Err:%v", err)
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "文件格式不正确!")
	}

	sheetName := fInfo.GetSheetName(0)
	rows, err := fInfo.GetRows(sheetName)
	if err != nil {
		l.Errorf("fInfo.GetRows Err:%v", err)
		return nil, err
	}

	if len(rows) <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "文件格式不正确!")
	}

	headers := rows[0]
	if len(headers) < 5 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "文件至少包含5列!")
	}

	var partners []*models.ExpoPartner
	for index, row := range rows {
		if index == 0 {
			continue
		}

		if len(row) < 5 {
			return nil, innErr.ErrBadRequest
		}

		var (
			pType   int
			rank    int
			name    = strings.TrimSpace(row[1])
			logo    = strings.TrimSpace(row[2])
			website = strings.TrimSpace(row[3])
		)

		if name == "" {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "请输入合作伙伴名称!")
		}

		if logo == "" {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "请输入合作伙伴logo!")
		}

		if website == "" {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "请输入合作伙伴网址!")
		}

		pType, err = strconv.Atoi(row[0])
		if err != nil {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "请输入正确的合作伙伴类型!")
		}

		rank, err = strconv.Atoi(row[4])
		if err != nil {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "请输入正确的排序!")
		}

		partners = append(partners, &models.ExpoPartner{
			ExpoId:  in.ExpoId,
			Name:    name,
			Logo:    logo,
			Website: website,
			Enable:  true,
			Type:    pType,
			Rank:    rank,
			Creator: in.Creator,
			Extra: &igorm.CustomValue[*models.ExpoPartnerExtra]{
				V: &models.ExpoPartnerExtra{},
			},
		})
	}

	if len(partners) > 0 {
		err = s.expoPartner.BatchAdd(ctx, partners)
		if err != nil {
			l.Errorf("expoPartner.BatchAdd Err:%v", err)
			return nil, err
		}
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ExportExpoPartner(ctx context.Context, in *v1.ExportExpoPartnerRequest) (*v1.ExportExpoPartnerReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	var (
		filePath   = path.Join(os.TempDir(), fmt.Sprintf("expo_partner_%d.xlsx", time.Now().UnixMicro()))
		excel      = excelize.NewFile()
		conditions = map[string]interface{}{"expo_id": in.ExpoId}
	)

	if in.Type != 0 {
		conditions["type"] = int(in.Type)
	}

	if in.Keyword != "" {
		conditions["name"] = in.Keyword
	}

	writer, err := excel.NewStreamWriter("Sheet1")
	if err != nil {
		l.Errorf("excel.NewStreamWriter Err:%v", err)
		return nil, err
	}

	styleID, err := excel.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Color: "777777",
			Size:  14,
			Bold:  true,
		},
	})

	err = writer.SetRow("A1", []interface{}{"合作伙伴类型", "合作伙伴名称", "合作伙伴logo", "外跳链接", "排序"}, excelize.RowOpts{
		Height:       16,
		StyleID:      styleID,
		OutlineLevel: 0,
	})
	if err != nil {
		l.Errorf("writer.SetRow Err:%v", err)
		return nil, err
	}

	total := 1
	for {
		var partners []*models.ExpoPartner
		partners, err = s.expoPartner.FindByPage(ctx, conditions, int(in.Page), int(in.Size))
		if err != nil {
			l.Errorf("expoPartner.FindByPage Err:%v", err)
			return nil, err
		}

		for _, partner := range partners {
			var pType string
			item, ok := getPartnerItem(int32(partner.Type))
			if ok {
				pType = item.Name
			}

			err = writer.SetRow(fmt.Sprintf("A%d", total+1), []interface{}{
				pType,
				partner.Name,
				urlformat.FullPath(partner.Logo, urlTemplate),
				partner.Website,
				partner.Rank,
			})
			if err != nil {
				return nil, err
			}
			total++
		}

		if len(partners) <= 0 {
			break
		}
		in.Page++
	}
	err = writer.Flush()
	if err != nil {
		l.Errorf("writer.Flush Err:%v", err)
		return nil, err
	}

	err = excel.SaveAs(filePath)
	if err != nil {
		l.Errorf("excel.SaveAs Err:%v", err)
		return nil, err
	}

	content := bytes.NewBuffer(nil)
	_, err = excel.WriteTo(content)
	if err != nil {
		l.Errorf("file.WriteTo Err:%v", err)
		return nil, err
	}

	return &v1.ExportExpoPartnerReply{
		Data: base64.StdEncoding.EncodeToString(content.Bytes()),
	}, nil
}

func (s *Service) expoPartnerToGRPC(in *models.ExpoPartner) *v1.ExpoPartnerInfo {
	return &v1.ExpoPartnerInfo{
		Id:        int64(in.ID),
		ExpoId:    in.ExpoId,
		Name:      in.Name,
		Logo:      in.Logo,
		Website:   in.Website,
		Rank:      int32(in.Rank),
		Enable:    in.Enable,
		Type:      strconv.Itoa(in.Type),
		CreatedAt: in.CreatedAt.Unix(),
		Creator:   in.Creator,
	}
}
