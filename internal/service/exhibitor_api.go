package service

import (
	"context"
	"strconv"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	userCenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"
	"api-expo/internal/util"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

const (
	// 临时员工标识
	temporaryEmployeeFlag = 1
)

// ExhibitorList 获取参展商列表
func (s *Service) ExhibitorList(ctx context.Context, in *v1.GetExhibitorListRequest) (*v1.GetExhibitorListReply, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid expo ID")
	}

	in.Page, in.Size = util.ValidatePagination(in.Page, in.Size, defaultPageSize)

	// 分页查询参展商列表
	exhibitors, _, err := s.expoExhibitor.FindByExpoIdPage(ctx, expoId, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoExhibitor.FindByExpoIdPage failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query exhibitor list")
	}

	if len(exhibitors) == 0 {
		return &v1.GetExhibitorListReply{Items: []*v1.Exhibitor{}}, nil
	}

	// 获取参展商ID列表
	exhibitorIds := make([]int64, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		exhibitorIds = append(exhibitorIds, int64(exhibitor.ID))
	}

	// 查询每个参展商的员工信息
	employees, err := s.expoExhibitorEmployee.FindByExhibitorIds(ctx, exhibitorIds)
	// err := s.expoExhibitorEmployee.FindByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoExhibitorEmployee.FindByExhibitorIds failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query exhibitor employees")
	}

	// 按参展商ID分组员工
	employeesByExhibitor := make(map[uint][]*models.ExpoExhibitorEmployee)
	employeeIds := make([]int64, 0, len(employees))
	for _, emp := range employees {
		employeesByExhibitor[uint(emp.ExhibitorId)] = append(employeesByExhibitor[uint(emp.ExhibitorId)], emp)
		employeeIds = append(employeeIds, emp.EmployeeId)
	}

	userInfoList, err := s.fetchEmployeeUserInfo(ctx, employeeIds)
	if err != nil {
		l.Errorf("fetchEmployeeUserInfo Err:%v", err)
		// 这里不返回错误，只是员工信息可能显示不全
		userInfoList = &EmployeeUserInfo{
			ParticipantMap: make(map[int64]*models.Participant),
			UserMapping:    make(map[string]*userCenterv1.UserInfo),
		}
	}

	// 构建响应数据
	result := make([]*v1.Exhibitor, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		// 获取赞助等级图标
		sponsorIcon := getSponsorLevelIcon(exhibitor.SponsorLevel, languageCode)

		// 构建员工信息
		exhibitorEmployees := employeesByExhibitor[exhibitor.ID]
		var simpleEmployees []*v1.SimpleExhibitorEmployee

		for _, emp := range exhibitorEmployees {
			participant := userInfoList.ParticipantMap[emp.EmployeeId]
			// 检查员工对应的参与者是否存在且有效
			if participant == nil {
				// 参与者不存在或已被删除，跳过该员工
				l.Warnf("员工对应的参与者不存在或已被删除: employeeId=%d", emp.EmployeeId)
				continue
			}

			var userInfo *userCenterv1.UserInfo
			if participant.ApplyUserid != "" {
				userInfo = userInfoList.UserMapping[participant.ApplyUserid]
			}

			empInfo := buildEmployeeInfo(participant, userInfo, languageCode)

			simpleEmployees = append(simpleEmployees, &v1.SimpleExhibitorEmployee{
				EmployeeId:       strconv.FormatInt(emp.EmployeeId, 10),
				Avatar:           empInfo.Avatar,
				Name:             empInfo.Name,
				JobTitle:         empInfo.JobTitle,
				UserId:           empInfo.UserID,
				UserIdentityIcon: empInfo.UserIdentityIcon,
				CountryCode:      empInfo.CountryCode,
				Check:            empInfo.IsCheck,
				Identity:         empInfo.Identity,
				Label:            empInfo.Label,
				IsTemporary:      empInfo.IsTemporary,
			})
		}

		result = append(result, &v1.Exhibitor{
			Id:               strconv.FormatUint(uint64(exhibitor.ID), 10),
			Name:             exhibitor.TraderName,
			Logo:             urlformat.FullPath(exhibitor.Logo, urlTemplate),
			SponsorLevel:     int32(exhibitor.SponsorLevel),
			SponsorLevelIcon: urlformat.FullPath(sponsorIcon, urlTemplate),
			Booth:            exhibitor.Booth,
			TraderCode:       exhibitor.TraderCode,
			ExhibitorType:    exhibitor.ExhibitorType,
			Employees:        simpleEmployees,
		})
	}

	l.Infof("ExhibitorList success: userID=%s, expoId=%d, page=%d, size=%d, results=%d",
		userID, expoId, in.Page, in.Size, len(result))

	return &v1.GetExhibitorListReply{
		Items: result,
	}, nil
}

// ExhibitorEmployeeList 获取参展商员工列表
func (s *Service) ExhibitorEmployeeList(ctx context.Context, in *v1.GetExhibitorEmployeeListRequest) (*v1.GetExhibitorEmployeeListReply, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid expo ID")
	}

	in.Page, in.Size = util.ValidatePagination(in.Page, in.Size, defaultPageSize)

	employees, err := s.expoExhibitorEmployee.FindByExpoIdPage(ctx, expoId, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoExhibitorEmployee.FindByExpoIdPage failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query exhibitor employees")
	}

	if len(employees) == 0 {
		return &v1.GetExhibitorEmployeeListReply{Employees: []*v1.ExhibitorEmployee{}}, nil
	}

	// 获取员工ID和参展商ID
	var employeeIds []int64
	var exhibitorIds []int64
	for _, emp := range employees {
		employeeIds = append(employeeIds, emp.EmployeeId)
		exhibitorIds = append(exhibitorIds, emp.ExhibitorId)
	}

	// 查询参展商信息
	exhibitorMap := make(map[int64]*models.ExpoExhibitor)
	if len(exhibitorIds) > 0 {
		exhibitorList, err := s.expoExhibitor.FindByIds(ctx, exhibitorIds)
		if err != nil {
			l.Errorf("expoExhibitor.FindByIds failed: %v", err)
			return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query exhibitor information")
		}

		for _, exhibitor := range exhibitorList {
			exhibitorMap[int64(exhibitor.ID)] = exhibitor
		}
	}

	// 获取员工用户信息
	userInfo, err := s.fetchEmployeeUserInfo(ctx, employeeIds)
	if err != nil {
		l.Errorf("fetchEmployeeUserInfo failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to fetch employee user information")
	}

	result := make([]*v1.ExhibitorEmployee, 0, len(employees))
	for _, emp := range employees {
		// 检查员工对应的参与者是否存在且有效
		participant := userInfo.ParticipantMap[emp.EmployeeId]
		if participant == nil {
			// 参与者不存在或已被删除，跳过该员工
			l.Warnf("员工对应的参与者不存在或已被删除: employeeId=%d", emp.EmployeeId)
			continue
		}

		exhibitor := exhibitorMap[emp.ExhibitorId]

		var exhibitorLogo, exhibitorName, sponsorIcon, booth, exhibitorCode string
		var exhibitorType v1.ExhibitorType
		if exhibitor != nil {
			exhibitorLogo = urlformat.FullPath(exhibitor.Logo, urlTemplate)
			exhibitorName = exhibitor.TraderName
			exhibitorCode = exhibitor.TraderCode
			exhibitorType = exhibitor.ExhibitorType
			sponsorIcon = getSponsorLevelIcon(exhibitor.SponsorLevel, languageCode)
			booth = exhibitor.Booth
		}

		// 构建员工信息
		var userInfoData *userCenterv1.UserInfo
		if participant.ApplyUserid != "" {
			userInfoData = userInfo.UserMapping[participant.ApplyUserid]
		}

		empInfo := buildEmployeeInfo(participant, userInfoData, languageCode)

		result = append(result, &v1.ExhibitorEmployee{
			Id:               strconv.FormatInt(emp.EmployeeId, 10),
			Avatar:           empInfo.Avatar,
			Name:             empInfo.Name,
			Title:            empInfo.JobTitle,
			ExhibitorLogo:    exhibitorLogo,
			ExhibitorName:    exhibitorName,
			SponsorLevelIcon: sponsorIcon,
			Booth:            booth,
			UserId:           empInfo.UserID,
			UserIdentityIcon: empInfo.UserIdentityIcon,
			CountryCode:      empInfo.CountryCode,
			Identity:         empInfo.Identity,
			Label:            empInfo.Label,
			IsTemporary:      empInfo.IsTemporary,
			TraderCode:       exhibitorCode,
			ExhibitorType:    exhibitorType,
		})
	}

	l.Infof("ExhibitorEmployeeList success: userID=%s, expoId=%d, page=%d, size=%d, results=%d",
		userID, expoId, in.Page, in.Size, len(result))

	return &v1.GetExhibitorEmployeeListReply{
		Employees: result,
	}, nil
}

// GetExhibitorBoothList 获取展位列表
func (s *Service) GetExhibitorBoothList(ctx context.Context, in *v1.GetExhibitorBoothListRequest) (*v1.GetExhibitorBoothListReply, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid expo ID")
	}

	in.Page, in.Size = util.ValidatePagination(in.Page, in.Size, defaultPageSize)

	// 分页查询参展商列表，使用与交易商列表相同的排序规则
	// 排序规则：rank ASC, sponsor_level DESC, created_at ASC
	exhibitors, total, err := s.expoExhibitor.FindByExpoIdPage(ctx, expoId, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoExhibitor.FindByExpoIdPage failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query exhibitor booth list")
	}

	if len(exhibitors) == 0 {
		return &v1.GetExhibitorBoothListReply{Booths: []*v1.ExhibitorBooth{}}, nil
	}

	// 构建展位信息列表
	booths := make([]*v1.ExhibitorBooth, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		sponsorIcon := getSponsorLevelIcon(exhibitor.SponsorLevel, languageCode)

		booth := &v1.ExhibitorBooth{
			Id:               strconv.FormatUint(uint64(exhibitor.ID), 10),
			Booth:            exhibitor.Booth,
			Name:             exhibitor.TraderName,
			Logo:             urlformat.FullPath(exhibitor.MinLogo, urlTemplate),
			SponsorLevelIcon: urlformat.FullPath(sponsorIcon, urlTemplate),
			TraderCode:       exhibitor.TraderCode,
			ExhibitorType:    exhibitor.ExhibitorType,
		}
		booths = append(booths, booth)
	}

	l.Infof("GetExhibitorBoothList success: userID=%s, expoId=%d, page=%d, size=%d, results=%d",
		userID, expoId, in.Page, in.Size, len(booths))

	return &v1.GetExhibitorBoothListReply{
		Booths: booths,
		Total:  total,
	}, nil
}

// EmployeeUserInfo 员工用户信息数据结构
type EmployeeUserInfo struct {
	ParticipantMap map[int64]*models.Participant
	UserMapping    map[string]*userCenterv1.UserInfo
}

// fetchEmployeeUserInfo 批量获取员工用户信息
func (s *Service) fetchEmployeeUserInfo(ctx context.Context, employeeIds []int64) (*EmployeeUserInfo, error) {
	var l = log.Context(ctx)

	result := &EmployeeUserInfo{
		ParticipantMap: make(map[int64]*models.Participant),
		UserMapping:    make(map[string]*userCenterv1.UserInfo),
	}

	if len(employeeIds) == 0 {
		return result, nil
	}

	// 批量查询participant信息
	participants, err := s.participant.FindByTicketIds(ctx, employeeIds)
	if err != nil {
		l.Errorf("participant.FindByTicketIds failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query participant information")
	}

	userIds := make([]string, 0, len(participants))
	for _, p := range participants {
		result.ParticipantMap[int64(p.ID)] = p
		if p.ApplyUserid != "" {
			userIds = append(userIds, p.ApplyUserid)
		}
	}

	if len(userIds) > 0 {
		userRes, err := s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
			UserIds: userIds,
		})
		if err != nil {
			l.Errorf("GetUsersInfo Err:%v", err)
		} else if userRes != nil && userRes.Message != nil {
			for _, user := range userRes.Message {
				if user != nil && user.UserId != "" {
					result.UserMapping[user.UserId] = user
				}
			}
		}
	}

	return result, nil
}

// GetExhibitorBadge 获取参展商赞助徽章说明
func (s *Service) GetExhibitorBadge(_ context.Context, _ *common.EmptyRequest) (*v1.GetExhibitorBadgeReply, error) {
	// sponsorLevelItems 降序处理，按照赞助等级从高到低排序
	sortedItems := make([]*v1.SponsorLevelItem, len(sponsorLevelItems))
	copy(sortedItems, sponsorLevelItems)

	// 按Level降序排序：GOLOBAL(5) -> DIAMOND(4) -> PLATINUM(3) -> GOLD(2) -> SILVER(1)
	for i := 0; i < len(sortedItems)-1; i++ {
		for j := i + 1; j < len(sortedItems); j++ {
			if sortedItems[i].Level < sortedItems[j].Level {
				sortedItems[i], sortedItems[j] = sortedItems[j], sortedItems[i]
			}
		}
	}

	return &v1.GetExhibitorBadgeReply{
		SponsorLevels: sortedItems,
	}, nil
}

// EmployeeInfo 员工信息结构
type EmployeeInfo struct {
	Avatar           string
	Name             string
	JobTitle         string
	UserID           string
	UserIdentityIcon string
	CountryCode      string
	Identity         v1.Identity
	Label            []string
	IsCheck          bool
	IsTemporary      bool
}

// buildEmployeeInfo 构建员工信息的辅助函数
func buildEmployeeInfo(participant *models.Participant, userInfo *userCenterv1.UserInfo, languageCode string) *EmployeeInfo {
	info := &EmployeeInfo{}

	if participant == nil {
		return info
	}

	// 构建基本信息
	info.JobTitle = participant.Position
	info.CountryCode = participant.AreaCode
	info.Identity = v1.Identity(participant.IdentityCode)
	info.IsCheck = participant.ApplyStatus == v1.ApplyAudit_APPLY_AUDIT_Pass
	info.IsTemporary = !participant.IsResister

	// 构建用户信息，有 ApplyUserid 则使用用户信息，否则使用参与者的姓名和头像
	if participant.ApplyUserid != "" && userInfo != nil {
		if userInfo.AvatarAddress != "" {
			info.Avatar = userInfo.AvatarAddress
		}
		info.UserID = userInfo.UserId
		info.UserIdentityIcon = userInfo.UserIdentityNewIcon
		info.Name = userInfo.NickName
	} else {
		info.Name = util.FullName(participant.LastName, participant.FirstName)
		info.Avatar = urlformat.FullPath(getLetterAvatar(participant.FirstName), urlTemplate)
	}

	// 构建标签，确保不重复
	labelSet := make(map[string]bool)

	if industryDesc := GetIdentityDesc(participant.IndustryCode, languageCode); industryDesc != "" {
		labelSet[industryDesc] = true
	}
	if subIdentityDesc := GetSubIdentityDesc(participant.SubIdentityCode, languageCode); subIdentityDesc != "" {
		labelSet[subIdentityDesc] = true
	}

	info.Label = make([]string, 0, len(labelSet))
	for label := range labelSet {
		info.Label = append(info.Label, label)
	}

	return info
}
