package service

import (
	"context"
	"encoding/json"
	"strconv"
	"sync"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/gold"

	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-kratos/kratos/v2/log"
	innErr "github.com/airunny/wiki-go-tools/errors"
)

type Payment interface {
	// Verify 验证是否可以核销，不真正的做核销
	// 1、校验用户ID跟operation_ticket的关系
	// 2、校验商品ID跟operation_ticket的关系
	// 3、operation_ticket的核销不能重复核销
	Verify(ctx context.Context, in *VerifyParams) (*VerifyResult, error)
	// Pay 这里才会真正的做核销
	// 1、校验用户ID跟operation_ticket的关系
	// 2、校验商品ID跟operation_ticket的关系
	// 3、operation_ticket的核销不能重复核销
	// 4、核销时一定要做好数据库影响行数的判断
	Pay(ctx context.Context, in *PayParams) (*PayResult, error)
	// Rollback 这里需要做回退操作，这里只针对业务ID做回退
	// 1、这里需要做好幂等操作
	Rollback(ctx context.Context, in *RollbackParams) error
}

var (
	payRegister = map[v1.PaymentMethod]Payment{}
	payLocker   sync.Mutex
)

func registerPayment(method v1.PaymentMethod, payment Payment) {
	payLocker.Lock()
	defer payLocker.Unlock()

	if _, ok := payRegister[method]; ok {
		panic("payment method already registered")
	}
	payRegister[method] = payment
}

func getPayment(method v1.PaymentMethod) (Payment, bool) {
	pay, ok := payRegister[method]
	return pay, ok
}

// registerPointsPay 积分支付
func registerPointsPay(goldClient *gold.Client) {
	registerPayment(v1.PaymentMethod_POINTS, newPointsPay(goldClient))
}

// registerGiftCard 礼品卡兑换
func registerGiftCard(service *Service) {
	registerPayment(v1.PaymentMethod_GIFT_CARD, newGiftCardPay(service))
}

// registerTask 任务兑换
func registerTask(service *Service) {
	registerPayment(v1.PaymentMethod_TASK, newTaskPayment(service))
}

// =================================================== 积分支付 ===================================================
// pointsPay 积分支付
type pointsPay struct {
	goldClient *gold.Client
}

func newPointsPay(goldClient *gold.Client) *pointsPay {
	return &pointsPay{
		goldClient: goldClient,
	}
}

func (g *pointsPay) Verify(ctx context.Context, in *VerifyParams) (*VerifyResult, error) {
	if !in.NotCheck {
		var (
			l               = log.Context(ctx)
			languageCode, _ = icontext.LanguageCodeFrom(ctx)
		)

		verifyRes, err := g.goldClient.UserPoints(ctx, &gold.UserPointsRequest{
			UserId: in.UserId,
		})
		if err != nil {
			l.Errorf("goldClient.UserBalance Err:%v", err)
			return nil, err
		}

		if !verifyRes.IsSuccess {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, verifyRes.Message)
		}

		if verifyRes.Data.Points < int(in.TotalAmount) {
			return nil, innErr.WithMessage(innErr.ErrUserOperation, i18n.GetWithDefaultEnglish("61915", languageCode))
		}
	}

	return &VerifyResult{
		FreeShipping: true,
	}, nil
}

func (g *pointsPay) Pay(ctx context.Context, in *PayParams) (*PayResult, error) {
	l := log.Context(ctx)
	translate, err := json.Marshal(in.GoodsNameTranslate)
	if err != nil {
		return nil, err
	}

	payRes, err := g.goldClient.PointsCost(ctx, &gold.PointsCostRequest{
		UserId:               in.UserId,
		ProductName:          in.GoodsName,
		EnumOrderType:        gold.EnumOrderTypeCost,
		Points:               int(in.TotalAmount),
		Remark:               in.PaymentNo,
		OrderNo:              in.OrderNo,
		ProductImgUrl:        in.ImageURL,
		TranslateProductName: string(translate),
	})
	if err != nil {
		l.Errorf("goldClient.Cost Err:%v", err)
		return nil, err
	}

	if !payRes.IsSuccess {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, payRes.Message)
	}

	return &PayResult{
		OperationId: payRes.Data.ID,
		PaidTotal:   in.TotalAmount,
	}, nil
}

func (g *pointsPay) Rollback(ctx context.Context, in *RollbackParams) error {
	l := log.Context(ctx)
	translate, err := json.Marshal(in.GoodsNameTranslate)
	if err != nil {
		return err
	}

	payRes, err := g.goldClient.PointsRollback(ctx, &gold.PointsRollbackRequest{
		OperationId:          in.OperationId,
		Points:               int(in.Total),
		TranslateProductName: string(translate),
	})
	if err != nil {
		l.Errorf("goldClient.Cost Err:%v", err)
		return err
	}

	if !payRes.IsSuccess {
		return innErr.WithMessage(innErr.ErrBadRequest, payRes.Message)
	}
	return nil
}

// =================================================== 礼品卡兑换 ===================================================

type GiftCardPay struct {
	service *Service
}

func newGiftCardPay(service *Service) *GiftCardPay {
	return &GiftCardPay{
		service: service,
	}
}

func (m *GiftCardPay) Verify(ctx context.Context, in *VerifyParams) (*VerifyResult, error) {
	if !in.NotCheck {
		l := log.Context(ctx)
		VerifyRes, err := m.service.userGiftCard.UserGiftCardIsExist(ctx, in.OperationTicket, in.UserId, in.GoodsId)
		if err != nil {
			l.Errorf("goldClient.UserBalance Err:%v", err)
			return nil, err
		}

		if VerifyRes == false {
			return nil, innErr.WithMessage(innErr.ErrUserOperation, "not gift card")
		}
	}

	return &VerifyResult{
		FreeShipping: true,
	}, nil
}

func (m *GiftCardPay) Pay(ctx context.Context, in *PayParams) (*PayResult, error) {
	var (
		l            = log.Context(ctx)
		rowsAffected int64
		err          error
	)

	rowsAffected, err = m.service.userGiftCard.UserGiftCardPay(ctx, in.OperationTicket, in.UserId, in.OrderNo)
	if err != nil {
		l.Errorf("DecreaseStock Err:%v", err)
		return nil, err
	}
	if rowsAffected == 0 {
		return nil, innErr.ErrBadRequest
	}
	return &PayResult{
		OperationId: in.OperationTicket,
		PaidTotal:   0,
	}, nil
}

func (m *GiftCardPay) Rollback(ctx context.Context, in *RollbackParams) error {
	var (
		l            = log.Context(ctx)
		rowsAffected int64
		err          error
	)

	rowsAffected, err = m.service.userGiftCard.UserGiftCardRollBack(ctx, in.OperationId)
	if err != nil {
		l.Errorf("DecreaseStock Err:%v", err)
		return err
	}
	if rowsAffected == 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "out of stock")
	}
	return nil
}

// =================================================== 任务兑换 ===================================================

type taskPayment struct {
	service *Service
}

func newTaskPayment(service *Service) *taskPayment {
	return &taskPayment{
		service: service,
	}
}

func (m *taskPayment) Verify(ctx context.Context, in *VerifyParams) (*VerifyResult, error) {
	l := log.Context(ctx)
	id, err := strconv.ParseUint(in.OperationTicket, 10, 32)
	if err != nil {
		log.Context(ctx).Errorf("无效的 OperationTicket: %s, 错误: %v", in.OperationTicket, err)
		return nil, err
	}

	// 获取奖励领取记录
	rewardIssue, err := m.service.taskRewardIssue.GetByID(ctx, uint(id))
	if err != nil {
		l.Errorf("获取奖励领取记录失败, ID: %d, 错误: %v", id, err)
		return nil, err
	}

	if rewardIssue.UserID != in.UserId {
		l.Warnf("用户ID不匹配, 请求用户: %s, 记录用户: %s", in.UserId, rewardIssue.UserID)
		return nil, innErr.ErrBadRequest
	}

	// 检查奖励状态是否处于待发放状态
	if rewardIssue.Status != models.TaskRewardIssueIssuing {
		l.Warnf("奖励状态不是待发放状态, ID: %d, 状态: %d", id, rewardIssue.Status)
		return nil, innErr.ErrBadRequest
	}
	// 查询任务配置
	taskConfig, err := m.service.taskConfig.GetTaskConfigByID(ctx, int64(rewardIssue.TaskConfigID))
	if err != nil {
		l.Errorf("获取任务配置失败, ID: %d, 错误: %v", rewardIssue.TaskConfigID, err)
		return nil, err
	}
	freeShipping := false
	if taskConfig.RewardType == models.RewardTypePhysical && taskConfig.RewardConfigObj.Goods != nil {
		freeShipping = taskConfig.RewardConfigObj.Goods.FreeShipping
	}

	return &VerifyResult{
		FreeShipping: freeShipping,
	}, nil
}

func (m *taskPayment) Pay(ctx context.Context, in *PayParams) (*PayResult, error) {
	// 将 OperationTicket 转换为整数 ID
	id, err := strconv.ParseUint(in.OperationTicket, 10, 32)
	if err != nil {
		log.Context(ctx).Errorf("无效的 OperationTicket: %s, 错误: %v", in.OperationTicket, err)
		return nil, err
	}

	// 获取奖励领取记录
	rewardIssue, err := m.service.taskRewardIssue.GetByID(ctx, uint(id))
	if err != nil {
		log.Context(ctx).Errorf("获取奖励领取记录失败, ID: %d, 错误: %v", id, err)
		return nil, err
	}

	// 验证用户ID是否匹配
	if rewardIssue.UserID != in.UserId {
		log.Context(ctx).Warnf("用户ID不匹配, 请求用户: %s, 记录用户: %s", in.UserId, rewardIssue.UserID)
		return nil, innErr.ErrBadRequest
	}

	// 检查奖励状态是否处于待发放状态
	if rewardIssue.Status != models.TaskRewardIssueIssuing {
		log.Context(ctx).Warnf("奖励状态不是待发放状态, ID: %d, 状态: %d", id, rewardIssue.Status)
		return nil, innErr.ErrBadRequest
	}

	// 更新奖励状态为已发放
	err = m.service.taskRewardIssue.UpdateStatus(ctx, uint(id), models.TaskRewardIssueIssued)
	if err != nil {
		log.Context(ctx).Errorf("更新奖励状态失败, ID: %d, 错误: %v", id, err)
		return nil, err
	}
	//修改用户任务进度状态为已领取
	err = m.service.taskProgress.UpdateStatus(ctx, rewardIssue.UserID, rewardIssue.TaskConfigID, models.UserProgressStatusReceived)
	if err != nil {
		log.Context(ctx).Errorf("更新用户任务进度状态失败, 用户: %s, 任务ID: %d, 错误: %v", rewardIssue.UserID, rewardIssue.TaskConfigID, err)
		return nil, err
	}

	// 构建支付结果
	result := &PayResult{
		OperationId: in.OperationTicket,
		PaidTotal:   in.TotalAmount,
	}

	log.Context(ctx).Infof("奖励发放成功, ID: %d, 用户: %s", id, in.UserId)
	return result, nil
}

func (m *taskPayment) Rollback(ctx context.Context, in *RollbackParams) error {
	// 如果需要回滚支付操作，则将奖励状态设回为待发放
	id, err := strconv.ParseUint(in.OperationId, 10, 32)
	if err != nil {
		log.Context(ctx).Errorf("无效的操作ID: %s, 错误: %v", in.OperationId, err)
		return err
	}

	rewardIssue, err := m.service.taskRewardIssue.GetByID(ctx, uint(id))
	if err != nil {
		log.Context(ctx).Warnf("回滚时找不到奖励记录, ID: %d, 错误: %v", id, err)
		return nil
	}

	if rewardIssue.Status == models.TaskRewardIssueIssued {
		err = m.service.taskRewardIssue.UpdateStatus(ctx, uint(id), models.TaskRewardIssueIssuing)
		if err != nil {
			log.Context(ctx).Errorf("回滚奖励状态失败, ID: %d, 错误: %v", id, err)
			return err
		}
		log.Context(ctx).Infof("成功回滚奖励状态, ID: %d", id)
	}

	return nil
}
