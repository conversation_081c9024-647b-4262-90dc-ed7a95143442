package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	v1 "api-expo/api/expo/v1"

	"github.com/airunny/wiki-go-tools/errors"
	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

// GetLiveImages 分页获取展会图片
func (s *Service) GetLiveImages(ctx context.Context, req *v1.GetLiveImagesRequest) (*v1.GetLiveImagesReply, error) {
	// 参数验证
	expoId, err := strconv.ParseInt(req.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	page := req.Page
	if page <= 0 {
		page = 1
	}

	size := req.Size
	if size <= 0 || size > 100 {
		size = 20
	}

	if req.Type == v1.ImageLiveType_IMAGE_TYPE_EXPO {
		return s.getExpoImages(ctx, expoId, page, size)
	} else if req.Type == v1.ImageLiveType_IMAGE_TYPE_EXHIBITOR_REVIEW {
		return s.getExhibitorReviewImages(ctx, expoId, page, size)
	} else {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid image type")
	}
}

// getExpoImages 获取展会图片
func (s *Service) getExpoImages(ctx context.Context, expoId int64, page, size int32) (*v1.GetLiveImagesReply, error) {
	images, total, err := s.expoImage.GetPageByExpoID(ctx, expoId, int(page), int(size))
	if err != nil {
		log.Context(ctx).Errorf("GetPageByExpoID failed: %v", err)
		return nil, err
	}

	pbImages := make([]*v1.LiveImage, 0, len(images))
	for _, image := range images {
		pbImages = append(pbImages, &v1.LiveImage{
			Id:                uint64(image.ID),
			ExpoId:            strconv.FormatInt(image.ExpoId, 10),
			ImageUrl:          urlformat.FullPath(image.ImageUrl, urlTemplate),
			ImageThumbnailUrl: urlformat.FullPath(image.ImageUrl, faceUrlTemplate),
			CreatedAt:         image.CreatedAt.Unix(),
		})
	}

	return &v1.GetLiveImagesReply{
		List:  pbImages,
		Total: total,
		Page:  page,
		Size:  size,
	}, nil
}

// getExhibitorReviewImages 获取展商回顾图片
func (s *Service) getExhibitorReviewImages(ctx context.Context, expoId int64, page, size int32) (*v1.GetLiveImagesReply, error) {
	images, err := s.getExhibitorReviewImagesFromCache(ctx, expoId)
	if err != nil {
		log.Context(ctx).Errorf("getExhibitorReviewImagesFromCache failed: %v", err)
		return nil, err
	}

	// 计算分页
	total := int64(len(images))
	startIndex := int((page - 1) * size)
	endIndex := int(page * size)

	if startIndex >= len(images) {
		return &v1.GetLiveImagesReply{
			List:  []*v1.LiveImage{},
			Total: total,
			Page:  page,
			Size:  size,
		}, nil
	}

	if endIndex > len(images) {
		endIndex = len(images)
	}

	pageImages := images[startIndex:endIndex]

	return &v1.GetLiveImagesReply{
		List:  pageImages,
		Total: total,
		Page:  page,
		Size:  size,
	}, nil
}

// getExhibitorReviewImagesFromCache 从缓存中获取展商回顾图片
func (s *Service) getExhibitorReviewImagesFromCache(ctx context.Context, expoId int64) ([]*v1.LiveImage, error) {
	cacheKey := fmt.Sprintf("expo_review_images:%d", expoId)

	// 先尝试从 Redis 缓存中获取
	cachedData, err := s.redisCli.Get(ctx, cacheKey).Result()
	if err == nil {
		var images []*v1.LiveImage
		if err := json.Unmarshal([]byte(cachedData), &images); err == nil {
			log.Context(ctx).Infof("从缓存中获取展商回顾图片成功，展会ID: %d, 图片数量: %d", expoId, len(images))
			return images, nil
		}
		log.Context(ctx).Errorf("解析缓存数据失败: %v", err)
	}

	// 缓存未命中，从数据库查询
	log.Context(ctx).Infof("缓存未命中，从数据库查询展商回顾图片，展会ID: %d", expoId)
	images, err := s.loadExhibitorReviewImagesFromDB(ctx, expoId)
	if err != nil {
		return nil, err
	}

	// 将结果缓存到 Redis，TTL 1分钟
	imageData, err := json.Marshal(images)
	if err != nil {
		log.Context(ctx).Errorf("序列化图片数据失败: %v", err)
		return images, nil // 返回数据，忽略缓存错误
	}

	err = s.redisCli.Set(ctx, cacheKey, string(imageData), time.Minute).Err()
	if err != nil {
		log.Context(ctx).Errorf("缓存图片数据失败: %v", err)
	} else {
		log.Context(ctx).Infof("缓存图片数据成功，展会ID: %d, 图片数量: %d", expoId, len(images))
	}

	return images, nil
}

// loadExhibitorReviewImagesFromDB 从数据库加载展商回顾图片
func (s *Service) loadExhibitorReviewImagesFromDB(ctx context.Context, expoId int64) ([]*v1.LiveImage, error) {
	// 查询所有展商回顾记录，然后过滤图片类型
	reviews, err := s.expoReview.FindByPage(ctx, expoId, 1, 1000) // 获取足够多的记录
	if err != nil {
		log.Context(ctx).Errorf("查询展商回顾失败: %v", err)
		return nil, err
	}

	var allImages []*v1.LiveImage
	var imageId uint64 = 1

	for _, review := range reviews {
		// 只处理图片类型的回顾
		if review.Type != v1.ExpoReviewType_REVIEW_TYPE_PICTURE {
			continue
		}

		if review.Extra == nil || review.Extra.V == nil {
			continue
		}

		for _, imageUrl := range review.Extra.V.Images {
			if imageUrl == "" {
				continue
			}

			allImages = append(allImages, &v1.LiveImage{
				Id:                imageId,
				ExpoId:            strconv.FormatInt(expoId, 10),
				ImageUrl:          urlformat.FullPath(imageUrl, urlTemplate),
				ImageThumbnailUrl: urlformat.FullPath(imageUrl, faceUrlTemplate),
				CreatedAt:         review.CreatedAt.Unix(),
			})
			imageId++
		}
	}

	log.Context(ctx).Infof("从数据库加载展商回顾图片完成，展会ID: %d, 图片数量: %d", expoId, len(allImages))
	return allImages, nil
}
