package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/id"
	"gold_store/pkg/vgoods"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/recovery"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

var (
	vpsCountryMapping = map[string]string{
		"hk-02":              "62137", // 香港
		"th-bkk-02":          "62138", // 曼谷
		"cn-sh2-02":          "62145", // 上海
		"sg-02":              "62146", // 新加坡
		"vn-sng-01":          "62139", // 胡志明市
		"jpn-tky-01":         "62140", // 东京
		"tw-tp-01":           "62141", // 台北
		"uae-dubai-01":       "62142", // 迪拜
		"uk-london-01":       "62150", // 伦敦
		"us-ws-01":           "62151", // 华盛顿
		"cn-bj2-05":          "62143", // 北京
		"idn-jakarta-01":     "62152", // 雅加达
		"afr-nigeria-01":     "62144", // 拉各斯
		"ap-shanghai-5":      "62145", // 上海
		"ap-singapore-4":     "62146", // 新加坡
		"eu-frankfurt-1":     "62147", // 法兰克福
		"ap-hongkong-3":      "62137", // 香港
		"ap-seoul-2":         "62148", // 首尔
		"na-siliconvalley-2": "62149", // 硅谷
	}
)

func (s *Service) checkVPSOrder() {
	var (
		page = 1
		size = 1000
		ctx  = context.Background()
	)

	for {
		orders, err := s.order.FindOrderIdsBySource(ctx, v1.OrderSource_VPS, page, size)
		if err != nil {
			panic(err)
		}

		if len(orders) <= 0 {
			break
		}

		vpsOrders, err := s.vps.FindByOrderId(ctx, orders)
		if err != nil {
			panic(err)
		}

		if len(orders) == len(vpsOrders) {
			page++
			continue
		}

		// 处理订单
		orderMapping := make(map[string]struct{}, len(vpsOrders))
		for _, orderId := range vpsOrders {
			orderMapping[orderId] = struct{}{}
		}

		for _, orderId := range orders {
			_, ok := orderMapping[orderId]
			if !ok {
				alarm.FeiShuAlarm("", fmt.Sprintf("VPS未找到订单号【%s】", orderId))
			}
		}
		page++
	}
}

func (s *Service) orderFromVPS(c *kafka.Consumer, message *kafka.Message) {
	defer c.CommitMessage(message)

	var (
		ctx   = context.Background()
		reqId = reqid.GenRequestID()
		data  VPSOrder
	)
	ctx = icontext.WithRequestId(ctx, reqId)
	l := log.Context(ctx)
	defer recovery.CatchGoroutinePanicWithContext(ctx)

	err := json.Unmarshal(message.Value, &data)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("VPS Unmarshal<%s> Err:%v", string(message.Value), err))
		return
	}

	if !data.Commit && data.Type != "bootstrap-insert" {
		return
	}

	order := data.Data
	// 防止订单循环导入
	if order.AppId == "goldmall" {
		return
	}

	image, err := s.vps.GetImage(ctx, order.Language, order.Zone)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("VPS 订单【%s】获取image【%d-%s】出错 %v", order.OrderId, order.Language, order.Zone, err))
		return
	}

	if err != nil {
		image = &models.VPSImage{
			Id: 9999,
		}
	}

	var (
		goodsId        = strconv.Itoa(image.Id)
		orderNo        = order.OrderId
		displayAmount  = order.DisplayAmount
		paymentMethod  = paymentMethodFromEA(order.Platform)
		operationNo    = order.PaymentId
		paymentNo      = id.GenerateId(id.BusinessPayment, id.WithChannel(strconv.Itoa(int(paymentMethod))))
		paymentStatus  = v1.PaymentStatus_PaymentStatusUNPAY
		orderStatus    = v1.OrderStatus_UNPAY
		price          = order.Amount
		totalAmount    = order.Amount
		paidTotal      float32
		quantity       = int32(1)
		goodsName      string
		goodsTranslate = &igorm.CustomValue[map[string]*v1.GoodsTranslate]{
			V: map[string]*v1.GoodsTranslate{},
		}
		specDesc        string
		specDescMapping = map[string]string{}
		paidTime        time.Time
		language        string
		currencySymbol  = getFirstChar(order.DisplayAmount)
		goodsImage      = vpsStaticImage
		createTime, _   = time.Parse(time.DateTime, order.OrderTime)
	)
	createTime = createTime.Add(-time.Hour * 8)

	if order.BrokerId != "" {
		var broker *vgoods.GetBrokerReply
		broker, err = s.virtualGoods.GetBroker(ctx, &vgoods.GetBrokerRequest{
			Codes:    []string{order.BrokerId},
			Language: "en",
		})
		if err != nil {
			// 这里不影响主流程
			alarm.FeiShuAlarm(reqId, fmt.Sprintf("VPS 订单【%s】获取交易商错误：【%s】Err:%v", order.OrderId, order.BrokerId, err))
		} else {
			if len(broker.Result) <= 0 {
				l.Errorf("Empty_Trader:[%s]", order.BrokerId)
				alarm.FeiShuAlarm(reqId, fmt.Sprintf("VPS 订单【%s】获取交易商为空：【%s】", order.OrderId, order.BrokerId))
			} else {
				goodsImage = &v1.Image{
					Url: broker.Result[0].Logo,
				}
			}
		}
	}

	switch order.Config {
	case 1:
		goodsName = "VPS Standard"
		specDesc = "1*CPU/1G/40G/1M"
	case 2:
		goodsName = "VPS Pro"
		specDesc = "2*CPU/2G/40G/1M"
	case 3:
		goodsName = "VPS cTrader"
		specDesc = "2*CPU/2G/40G/1M"
	case 4:
		goodsName = fmt.Sprintf("VPS Pro %s", order.Level)
		switch order.Level {
		case "L1":
			specDesc = "1*CPU/1G/50G/2M"
		case "L2":
			specDesc = "1*CPU/1G/60G/2M"
		case "L3":
			specDesc = "1*CPU/1G/70G/2M"
		case "L4":
			specDesc = "1*CPU/2G/70G/2M"
		case "L5":
			specDesc = "2*CPU/2G/70G/2M"
		}
	default:
		goodsName = "VPS Ultra"
		specDesc = "2*CPU/2G/60G/2M"
	}

	switch order.Language {
	case 1:
		language = "简体中文"
	case 2:
		language = "English"
	case 3:
		language = "繁體中文"
	case 4:
		language = "日本語"
	case 5:
		language = "ภาษาไทย"
	case 6:
		language = "Tiếng Việt"
	case 7:
		language = "لغة عربية"
	case 8:
		language = "Français"
	default:
		language = "简体中文"
	}

	if paymentMethod == v1.PaymentMethod_GOLD {
		totalAmount = float32(order.GoldCoin)
		displayAmount = fmt.Sprintf("%d", order.GoldCoin)
		price = float32(order.GoldCoin)
	}

	if order.TradeStatus == "TRADE_SUCCESS" {
		orderStatus = v1.OrderStatus_COMPLETE
		paymentStatus = v1.PaymentStatus_PaymentStatusPAID
		paidTime, _ = time.Parse(time.DateTime, order.FinishedAt)
		paidTime = paidTime.Add(-time.Hour * 8)
		paidTotal = order.Amount
		if paymentMethod == v1.PaymentMethod_GOLD {
			paidTotal = float32(order.GoldCoin)
		}
	}

	if createTime.Before(time.Date(2010, 0, 0, 0, 0, 0, 0, time.UTC)) {
		createTime = paidTime
	}

	if createTime.Before(time.Date(2010, 0, 0, 0, 0, 0, 0, time.UTC)) {
		createTime, _ = time.Parse(time.DateTime, order.CreateTime)
		createTime = createTime.Add(-time.Hour * 8)
	}

	if createTime.Before(time.Date(2010, 0, 0, 0, 0, 0, 0, time.UTC)) {
		createTime, _ = time.Parse(time.DateTime, order.UpdateAt)
		createTime = createTime.Add(-time.Hour * 8)
	}

	orderExtra := &igorm.CustomValue[*models.OrderExtra]{
		V: &models.OrderExtra{
			DisplayAmount: displayAmount,
			SpecDesc:      specDescMapping,
			VPSBrokerId:   order.BrokerId,
			VPSStart:      order.CreateTime,
			VPSEnd:        order.ExpireTime,
			VPSStatus:     order.OrderStatus,
			VPSLanguage:   language,
			VPSCountry:    vpsCountryMapping[order.Zone],
			VPSUser:       "Administrator",
		},
	}

	// 待支付订单不同步
	if orderStatus == v1.OrderStatus_UNPAY {
		return
	}

	switch data.Type {
	case "update":
		var (
			payment *models.Payment
			insert  bool
		)

		_, err = s.order.GetByOrderNo(ctx, orderNo)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("GetByOrderNo<%s> Err:%v", orderNo, err)
			return
		}

		// 到这里说明订单不存在，可能是先消费了更新；所以需要走insert逻辑
		if err != nil {
			insert = true
		}

		if !insert {
			payment, err = s.payment.GetByOrderNo(ctx, orderNo)
			if err != nil {
				l.Errorf("GetByOrderNo<%s> Err:%v", orderNo, err)
				return
			}

			var orderExtraValue any
			orderExtraValue, err = orderExtra.Value()
			if err != nil {
				l.Errorf("orderExtra.Value Err:%v", err)
				return
			}

			tx := s.goods.Begin()
			defer func() {
				if err == nil {
					tx.Commit()
				} else {
					tx.Rollback()
				}
			}()

			// 4、更新支付单信息
			err = s.payment.UpdatePayment(ctx, &models.Payment{
				PaymentNo:   payment.PaymentNo,
				OperationNo: operationNo,
				PaidTotal:   totalAmount,
				PaidTime:    paidTime,
				Status:      paymentStatus,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("payment.UpdatePayment Err:%v", err)
				return
			}

			// 5、更新支付订单
			err = s.order.Update(ctx, orderNo, map[string]interface{}{
				"status":          orderStatus,
				"total_amount":    totalAmount,
				"extra":           orderExtraValue,
				"currency_symbol": currencySymbol,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("order.UpdateStatus Err:%v", err)
				return
			}
			return
		}
		fallthrough
	case "bootstrap-insert", "insert":
		var (
			goodsVersion  = time.Now().Unix()
			goodsSnapshot *models.GoodsSnapshot
		)

		goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
			return
		}

		if err != nil {
			// 这里没有更新时间；所以只能每次添加新的
			goodsVersion = time.Now().Unix()
			goodsSnapshot = &models.GoodsSnapshot{
				GoodsId: goodsId,
				Version: goodsVersion,
				Snapshot: &igorm.CustomValue[*models.Snapshot]{
					V: &models.Snapshot{
						Goods: &models.Goods{
							Category:      v1.GoodsCategory_GOODS_CATEGORY_VPS,
							GoodsId:       goodsId,
							Name:          goodsName,
							BasePrice:     totalAmount,
							UseBasePrice:  true,
							SelectedPrice: totalAmount,
							Status:        v1.GoodsStatus_GoodsStatusOn,
							FreeShipping:  true,
							Image: &igorm.CustomValue[*v1.Image]{
								V: goodsImage,
							},
							Translate: goodsTranslate,
						},
					},
				},
			}

			err = s.goodsSnapshot.Add(ctx, goodsSnapshot)
			if err != nil && !errors.Is(err, ormhelper.ErrDuplicateKey) {
				l.Errorf("goodsSnapshot.Add Err:%v", err)
				return
			}

			if err != nil {
				goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
				if err != nil {
					l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
					return
				}
			}
		}

		var (
			newOrder = &models.Order{
				OrderNo:        orderNo,
				Source:         v1.OrderSource_VPS,
				PaymentMethod:  paymentMethod,
				Platform:       platformFromEA(order.Source),
				UserId:         order.UserId,
				CountryCode:    order.ApiCountryCode,
				Address:        &igorm.CustomValue[*models.Address]{},
				Quantity:       quantity,
				CurrencySymbol: currencySymbol,
				TotalAmount:    totalAmount,
				Status:         orderStatus,
				GoodsName:      goodsName,
				Extra:          orderExtra,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}

			newOrderItems = []*models.OrderItem{
				{
					OrderNo:         orderNo,
					GoodsId:         goodsId,
					Price:           price,
					PriceUnit:       currencySymbol,
					GoodsSnapshotId: goodsSnapshot.ID,
					Quantity:        quantity,
					TotalAmount:     totalAmount,
					GoodsName:       goodsName,
					SpecDesc:        specDesc,
					Model: gorm.Model{
						CreatedAt: createTime,
						UpdatedAt: createTime,
					},
				},
			}

			newPayment = &models.Payment{
				PaymentNo:   paymentNo,
				OrderNo:     orderNo,
				OperationNo: operationNo,
				TotalAmount: totalAmount,
				PaidTotal:   paidTotal,
				PaidMethod:  paymentMethod,
				Status:      paymentStatus,
				PaidTime:    paidTime,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}
		)

		tx := s.goods.Begin()
		defer func() {
			if err == nil {
				tx.Commit()
			} else {
				tx.Rollback()
			}
		}()

		// 1、添加订单
		err = s.order.Add(ctx, newOrder, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("order.Add Err:%v", err)
			return
		}

		// 3、添加订单项
		err = s.orderItem.BatchAdd(ctx, newOrderItems, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("orderItem.BatchAdd Err:%v", err)
			return
		}

		// 4、添加支付
		err = s.payment.Add(ctx, newPayment, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("payment.Add Err:%v", err)
			return
		}
	}
}
