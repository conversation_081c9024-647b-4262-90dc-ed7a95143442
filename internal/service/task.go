package service

import (
	"context"
	"encoding/json"
	errors2 "errors"
	"fmt"
	"strconv"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/dao"
	"gold_store/internal/models"
	"gold_store/pkg/gold"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// GetTaskList 获取用户任务列表
func (s *Service) GetTaskList(ctx context.Context, _ *v1.GetTaskListRequest) (*v1.GetTaskListReply, error) {
	userID, _ := icontext.UserIdFrom(ctx)

	type taskResult struct {
		tasks []*v1.TaskInfo
		err   error
	}

	dailyTaskChan := make(chan taskResult, 1)
	newComerTaskChan := make(chan taskResult, 1)

	go func() {
		tasks, err := s.getDailyTasks(ctx, userID)
		dailyTaskChan <- taskResult{tasks: tasks, err: err}
	}()

	go func() {
		tasks, err := s.getNewComerTasks(ctx, userID)
		newComerTaskChan <- taskResult{tasks: tasks, err: err}
	}()

	dailyResult := <-dailyTaskChan
	if dailyResult.err != nil {
		log.Context(ctx).Errorf("Failed to retrieve daily tasks: %v", dailyResult.err)
		return nil, errors.ErrInternalServer
	}

	newComerResult := <-newComerTaskChan
	if newComerResult.err != nil {
		log.Context(ctx).Errorf("Failed to retrieve newcomer tasks: %v", newComerResult.err)
		return nil, errors.ErrInternalServer
	}

	log.Context(ctx).Infof("成功获取任务列表，日常任务数量: %d, 新手任务数量: %d",
		len(dailyResult.tasks), len(newComerResult.tasks))

	return &v1.GetTaskListReply{
		Daily:    dailyResult.tasks,
		NewComer: newComerResult.tasks,
	}, nil
}

// ReceiveTaskReward 领取任务奖励
func (s *Service) ReceiveTaskReward(ctx context.Context, req *v1.ReceiveTaskRewardRequest) (*v1.ReceiveTaskRewardReply, error) {
	// 1. 验证请求和任务状态
	userID, taskConfig, taskProgress, err := s.validateTaskRewardRequest(ctx, req)
	if err != nil {
		log.Context(ctx).Errorf("Failed to validate task reward request: %v", err)
		return nil, errors.ErrInternalServer
	}

	// 2. 验证任务进度状态
	if err := s.validateTaskProgressStatus(ctx, taskProgress, req); err != nil {
		// 特殊处理：如果已领取，返回已存在的奖励记录
		if taskProgress.Status == models.UserProgressStatusReceived {
			existingReward, checkErr := s.taskRewardIssue.GetByTaskProgressID(ctx, taskProgress.ID)
			if checkErr == nil && existingReward != nil {
				reply := &v1.ReceiveTaskRewardReply{
					TaskId:        req.TaskId,
					RewardType:    int32(existingReward.RewardType),
					RewardStatus:  int32(existingReward.Status),
					RewardIssueId: int64(existingReward.ID),
					RewardInfo:    &v1.RewardInfo{},
				}

				// 根据奖励类型填充奖励信息
				switch taskConfig.RewardType {
				case models.RewardTypeGoldCoin:
					reply.RewardInfo.GoldCoins = int32(taskConfig.RewardConfigObj.GoldCoins)
				case models.RewardTypePhysical:
					s.fillPhysicalRewardInfo(ctx, taskConfig, reply)
				case models.RewardTypeVirtual:
					s.fillVirtualRewardInfo(ctx, taskConfig, reply)
				}

				return reply, nil
			}
		}
		return nil, err
	}

	// 3. 获取分布式锁
	release, err := s.acquireRewardLock(ctx, userID, req.TaskId)
	if err != nil {
		return nil, err
	}
	defer func() {
		if err := release(); err != nil {
			log.Context(ctx).Errorf("释放分布式锁失败: %v", err)
		}
	}()

	// 4. 再次检查任务进度状态
	taskProgress, err = s.doubleCheckTaskProgress(ctx, userID, req.TaskId)
	if err != nil {
		return nil, errors.ErrInternalServer
	}

	// 5. 处理已存在的奖励记录
	if reply, _, err := s.handleExistingReward(ctx, taskProgress, taskConfig, req); err != nil {
		return nil, errors.ErrInternalServer
	} else if reply != nil {
		return reply, nil
	}

	// 6. 创建新的奖励处理流程
	return s.processNewReward(ctx, userID, taskProgress, taskConfig, req)
}

// processNewReward 处理新奖励的完整流程
func (s *Service) processNewReward(ctx context.Context, userID string, taskProgress *models.TaskProgress, taskConfig *models.TaskConfig, req *v1.ReceiveTaskRewardRequest) (*v1.ReceiveTaskRewardReply, error) {
	reply := &v1.ReceiveTaskRewardReply{
		TaskId:     req.TaskId,
		RewardInfo: &v1.RewardInfo{},
	}

	operationID := fmt.Sprintf("task_reward_%s_%d_%d", userID, req.TaskId, taskProgress.ID)

	tx := s.taskProgress.Begin()
	if tx.Error != nil {
		log.Context(ctx).Errorf("开始事务失败: %v", tx.Error)
		return nil, errors.ErrInternalServer
	}

	var txSuccess bool
	defer func() {
		if !txSuccess && tx != nil {
			if rbErr := tx.Rollback().Error; rbErr != nil {
				log.Context(ctx).Errorf("回滚事务失败: %v", rbErr)
			} else {
				log.Context(ctx).Infof("事务已回滚")
			}
		}
	}()

	// 创建奖励记录
	rewardType := taskConfig.RewardType
	rewardStatus := models.TaskRewardIssuePending

	rewardIssue, err := s.taskRewardIssue.CreateRewardRecordWithTx(ctx, tx, userID, req.TaskId, taskProgress.ID, rewardType, rewardStatus, taskConfig.RewardConfigObj, operationID)
	if err != nil {
		log.Context(ctx).Errorf("创建奖励记录失败: %v", err)

		// 奖励记录创建失败报警
		alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("任务奖励记录创建失败: userId=%s, taskId=%d, operationID=%s, error=%v", userID, req.TaskId, operationID, err))

		return nil, err
	}

	rewardIssueID := int64(rewardIssue.ID)
	log.Context(ctx).Infof("创建奖励记录成功, rewardIssueID=%d, taskProgressID=%d", rewardIssueID, taskProgress.ID)

	// 根据奖励类型处理发放逻辑
	switch rewardType {
	case models.RewardTypeGoldCoin:
		rewardStatus, txSuccess, err = s.processGoldCoinReward(ctx, tx, userID, taskConfig, req, rewardIssue, reply)
		if err != nil {
			return nil, err
		}

	case models.RewardTypePhysical:
		rewardStatus, err = s.processPhysicalReward(ctx, tx, userID, taskConfig, req, rewardIssueID, reply)
		if err != nil {
			return nil, err
		}

	case models.RewardTypeVirtual:
		rewardStatus, err = s.processVirtualReward(ctx, tx, userID, taskConfig, req, rewardIssueID, reply)
		if err != nil {
			return nil, err
		}

	default:
		log.Context(ctx).Errorf("未知奖励类型: %d, userId=%s, taskId=%d", rewardType, userID, req.TaskId)
		return nil, errors.ErrInternalServer
	}

	// 提交事务（仅当积分奖励未自行提交时）
	if !txSuccess && tx != nil {
		if err = tx.Commit().Error; err != nil {
			log.Context(ctx).Errorf("提交事务失败: %v", err)

			// 数据库事务失败报警
			alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("任务奖励事务提交失败: userId=%s, taskId=%d, operationID=%s, error=%v", userID, req.TaskId, operationID, err))

			return nil, err
		}
		txSuccess = true
	}

	// 填充回复信息
	reply.RewardType = int32(rewardType)
	reply.RewardStatus = int32(rewardStatus)
	reply.RewardIssueId = rewardIssueID

	return reply, nil
}

// validateTaskRewardRequest 验证任务奖励请求
func (s *Service) validateTaskRewardRequest(ctx context.Context, req *v1.ReceiveTaskRewardRequest) (string, *models.TaskConfig, *models.TaskProgress, error) {
	userID, _ := icontext.UserIdFrom(ctx)

	taskConfig, err := s.taskConfig.GetTaskConfigByID(ctx, req.TaskId)
	if err != nil {
		log.Context(ctx).Errorf("获取任务配置失败: %v", err)
		return "", nil, nil, err
	}

	if taskConfig == nil || taskConfig.Status != int8(models.TaskStatusEnumOngoing) {
		log.Context(ctx).Warnf("任务不存在或已关闭，任务ID: %d", req.TaskId)
		return "", nil, nil, errors.ErrInternalServer
	}

	// 验证任务状态
	taskProgress, err := s.taskProgress.GetTaskProgress(ctx, userID, uint(req.TaskId))
	if err != nil {
		log.Context(ctx).Errorf("获取任务进度失败: %v", err)
		return "", nil, nil, err
	}

	// 如果没有任务进度记录，无法领取奖励
	if taskProgress == nil {
		log.Context(ctx).Infof("用户未有任务进度记录，无法领取奖励，任务ID: %d", req.TaskId)
		return "", nil, nil, errors.WithMessage(errors.ErrBadRequest, "No related task record found, please confirm if the task is completed")
	}

	return userID, taskConfig, taskProgress, nil
}

// validateTaskProgressStatus 验证任务进度状态是否可领取奖励
func (s *Service) validateTaskProgressStatus(ctx context.Context, taskProgress *models.TaskProgress, req *v1.ReceiveTaskRewardRequest) error {
	if taskProgress.Status != models.UserProgressStatusCompleted {
		var errMsg string
		switch taskProgress.Status {
		case models.UserProgressStatusOngoing:
			errMsg = "Task not completed, cannot claim reward"
		case models.UserProgressStatusReceived:
			existingReward, checkErr := s.taskRewardIssue.GetByTaskProgressID(ctx, taskProgress.ID)
			if checkErr == nil && existingReward != nil {
				// 对于所有类型的奖励，如果已领取过，不返回错误，而是返回成功状态
				return nil
			}
			errMsg = "Reward already claimed, cannot claim again"
		case models.UserProgressStatusExpired:
			errMsg = "Task expired, cannot claim reward"
		default:
			errMsg = "Task status abnormal, cannot claim reward"
		}
		log.Context(ctx).Warnf("任务状态不符合领取条件: %s, 任务ID: %d, 状态: %d", errMsg, req.TaskId, taskProgress.Status)
		return errors.WithMessage(errors.ErrBadRequest, errMsg)
	}
	return nil
}

// acquireRewardLock 获取奖励领取的分布式锁
func (s *Service) acquireRewardLock(ctx context.Context, userID string, taskID int64) (func() error, error) {
	lockKey := fmt.Sprintf("receive_task_reward:%s_%d", userID, taskID)
	release, err := s.locker.TryLock(ctx, lockKey, 15*time.Second)
	if err != nil {
		log.Context(ctx).Warnf("获取锁失败，可能是重复领取, userId=%s, taskId=%d, err=%v", userID, taskID, err)
		return nil, errors.WithMessage(errors.ErrBadRequest, "Operation too frequent, please try again later")
	}
	return release, nil
}

// doubleCheckTaskProgress 再次检查任务进度状态
func (s *Service) doubleCheckTaskProgress(ctx context.Context, userID string, taskID int64) (*models.TaskProgress, error) {
	taskProgressCheck, err := s.taskProgress.GetTaskProgress(ctx, userID, uint(taskID))
	if err != nil {
		log.Context(ctx).Errorf("重复检查任务进度失败: %v", err)
		return nil, err
	}

	if taskProgressCheck == nil {
		log.Context(ctx).Warnf("任务状态已改变，无法领取奖励, userId=%s, taskId=%d", userID, taskID)
		return nil, errors.WithMessage(errors.ErrBadRequest, "Task status changed, cannot claim reward")
	}

	if taskProgressCheck.Status != models.UserProgressStatusCompleted {
		log.Context(ctx).Warnf("任务状态已改变，无法领取奖励, userId=%s, taskId=%d, status=%d", userID, taskID, taskProgressCheck.Status)
		return nil, errors.WithMessage(errors.ErrBadRequest, "Task status changed, cannot claim reward")
	}

	return taskProgressCheck, nil
}

// handleExistingReward 处理已存在的奖励记录
func (s *Service) handleExistingReward(ctx context.Context, taskProgress *models.TaskProgress, taskConfig *models.TaskConfig, req *v1.ReceiveTaskRewardRequest) (*v1.ReceiveTaskRewardReply, int64, error) {
	existingReward, err := s.taskRewardIssue.GetByTaskProgressID(ctx, taskProgress.ID)
	if err != nil && !errors2.Is(err, gorm.ErrRecordNotFound) {
		log.Context(ctx).Errorf("查询奖励记录失败: %v", err)
		return nil, 0, err
	}

	if existingReward != nil {
		log.Context(ctx).Infof("该任务进度已有奖励记录，不再创建新记录, taskProgressId=%d, rewardId=%d", taskProgress.ID, existingReward.ID)

		reply := &v1.ReceiveTaskRewardReply{
			TaskId:     req.TaskId,
			RewardInfo: &v1.RewardInfo{},
		}

		// 设置奖励信息
		switch taskConfig.RewardType {
		case models.RewardTypeGoldCoin:
			reply.RewardInfo.GoldCoins = int32(taskConfig.RewardConfigObj.GoldCoins)
		case models.RewardTypePhysical:
			s.fillPhysicalRewardInfo(ctx, taskConfig, reply)
		case models.RewardTypeVirtual:
			s.fillVirtualRewardInfo(ctx, taskConfig, reply)
		}

		// 设置奖励记录的基本信息
		reply.RewardType = int32(existingReward.RewardType)
		reply.RewardStatus = int32(existingReward.Status)
		reply.RewardIssueId = int64(existingReward.ID)

		return reply, int64(existingReward.ID), nil
	}

	return nil, 0, nil
}

// fillPhysicalRewardInfo 填充实物奖励信息
func (s *Service) fillPhysicalRewardInfo(ctx context.Context, taskConfig *models.TaskConfig, reply *v1.ReceiveTaskRewardReply) {
	if taskConfig.RewardConfigObj.Goods != nil {
		goodsName := ""
		goodsIDStr := taskConfig.RewardConfigObj.Goods.GoodsID
		goodsInfoList, err := s.findGoodsInfo(ctx, []string{goodsIDStr})
		if err != nil {
			log.Context(ctx).Errorf("查询商品信息失败: %v, goodsId=%s", err, goodsIDStr)
		} else if len(goodsInfoList) > 0 {
			goodsName = goodsInfoList[0].Name
		}
		reply.RewardInfo.Goods = &v1.GoodsReward{
			GoodsId:      taskConfig.RewardConfigObj.Goods.GoodsID,
			GoodsName:    goodsName,
			GoodsIcon:    urlformat.FullPath(taskConfig.RewardConfigObj.Goods.GoodsIcon, urlTemplate),
			FreeShipping: taskConfig.RewardConfigObj.Goods.FreeShipping,
		}
	}
}

// fillVirtualRewardInfo 填充虚拟奖励信息
func (s *Service) fillVirtualRewardInfo(ctx context.Context, taskConfig *models.TaskConfig, reply *v1.ReceiveTaskRewardReply) {
	if taskConfig.RewardConfigObj.VirtualGoods != nil {
		goodsName := ""
		goodsIDStr := taskConfig.RewardConfigObj.VirtualGoods.GoodsID
		goodsInfoList, err := s.findGoodsInfo(ctx, []string{goodsIDStr})
		if err != nil && len(goodsInfoList) == 0 {
			log.Context(ctx).Errorf("查询商品信息失败: %v, goodsId=%s", err, goodsIDStr)
		} else if len(goodsInfoList) > 0 {
			goodsName = goodsInfoList[0].Name
		}
		reply.RewardInfo.VirtualGoods = &v1.VirtualGoodsReward{
			GoodsId:   taskConfig.RewardConfigObj.VirtualGoods.GoodsID,
			GoodsIcon: urlformat.FullPath(taskConfig.RewardConfigObj.VirtualGoods.GoodsIcon, urlTemplate),
			GoodsName: goodsName,
		}
	}
}

// processGoldCoinReward 处理积分奖励
func (s *Service) processGoldCoinReward(ctx context.Context, tx *gorm.DB, userID string, taskConfig *models.TaskConfig, req *v1.ReceiveTaskRewardRequest, rewardIssue *models.TaskRewardIssue, reply *v1.ReceiveTaskRewardReply) (models.TaskRewardIssueStatus, bool, error) {
	// 只有奖励记录状态不是"已发放"时，才发放积分
	if rewardIssue != nil && rewardIssue.Status != models.TaskRewardIssueIssued {
		// 先更新任务状态为"已领取"和奖励记录状态为"发放中"，避免重复发放
		err := s.taskProgress.UpdateStatusWithTx(ctx, tx, userID, uint(req.TaskId), models.UserProgressStatusReceived)
		if err != nil {
			log.Context(ctx).Errorf("更新任务状态为已领取失败: %v", err)
			return models.TaskRewardIssuePending, false, err
		}

		// 更新奖励记录状态为"发放中"
		err = s.taskRewardIssue.UpdateStatusWithTx(ctx, tx, rewardIssue.ID, models.TaskRewardIssueIssuing)
		if err != nil {
			log.Context(ctx).Errorf("更新奖励记录状态为发放中失败: %v", err)
			return models.TaskRewardIssuePending, false, err
		}

		if err = tx.Commit().Error; err != nil {
			log.Context(ctx).Errorf("提交事务失败: %v", err)
			return models.TaskRewardIssuePending, false, err
		}

		// 发放积分
		if taskConfig.RewardConfigObj.GoldCoins > 0 {
			taskTitle := s.getLocalizedTaskTitle(ctx, taskConfig)
			err = s.issueTaskGold(ctx, userID, taskConfig.RewardConfigObj.GoldCoins, taskTitle, urlformat.FullPath(taskConfig.TaskIcon, urlTemplate), taskConfig.ID)
			if err != nil {
				log.Context(ctx).Errorf("发放积分失败: %v, userId=%s, taskId=%d, goldAmount=%d", err, userID, req.TaskId, taskConfig.RewardConfigObj.GoldCoins)
				alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("任务积分奖励发放失败: userId=%s, taskId=%d, goldAmount=%d, error=%v", userID, req.TaskId, taskConfig.RewardConfigObj.GoldCoins, err))

				// 发放失败，更新奖励记录状态为"失败"
				updateErr := s.taskRewardIssue.UpdateStatusWithReason(ctx, rewardIssue.ID, models.TaskRewardIssueFailed, fmt.Sprintf("发放失败: %v", err))
				if updateErr != nil {
					log.Context(ctx).Errorf("更新奖励状态为失败时出错: %v", updateErr)
				}

				return models.TaskRewardIssueFailed, true, errors.ErrInternalServer
			}

			err = s.taskRewardIssue.UpdateStatus(ctx, rewardIssue.ID, models.TaskRewardIssueIssued)
			if err != nil {
				log.Context(ctx).Errorf("更新奖励记录状态为已发放失败: %v", err)
				return models.TaskRewardIssuePending, true, errors.ErrInternalServer
			}

			reply.RewardInfo.GoldCoins = int32(taskConfig.RewardConfigObj.GoldCoins)
			log.Context(ctx).Infof("积分奖励发放成功, userId=%s, taskId=%d, goldAmount=%d", userID, req.TaskId, taskConfig.RewardConfigObj.GoldCoins)
		}

		return models.TaskRewardIssueIssued, true, nil
	} else {
		// 奖励已经发放过，直接返回成功
		log.Context(ctx).Infof("积分奖励已发放，不重复发放, userId=%s, taskId=%d", userID, req.TaskId)
		reply.RewardInfo.GoldCoins = int32(taskConfig.RewardConfigObj.GoldCoins)
		return models.TaskRewardIssueIssued, false, nil
	}
}

// processPhysicalReward 处理实物奖励
func (s *Service) processPhysicalReward(ctx context.Context, tx *gorm.DB, userID string, taskConfig *models.TaskConfig, req *v1.ReceiveTaskRewardRequest, rewardIssueID int64, reply *v1.ReceiveTaskRewardReply) (models.TaskRewardIssueStatus, error) {
	if taskConfig.RewardConfigObj.Goods != nil {
		s.fillPhysicalRewardInfo(ctx, taskConfig, reply)
		log.Context(ctx).Infof("实物奖励记录创建成功, userId=%s, taskId=%d, goodsId=%s", userID, req.TaskId, taskConfig.RewardConfigObj.Goods.GoodsID)

		// 更新奖励记录状态为"发放中"
		err := s.taskRewardIssue.UpdateStatusWithTx(ctx, tx, uint(rewardIssueID), models.TaskRewardIssueIssuing)
		if err != nil {
			log.Context(ctx).Errorf("更新奖励记录状态失败: %v", err)
			return models.TaskRewardIssuePending, errors.ErrInternalServer
		}
	}
	return models.TaskRewardIssueIssuing, nil
}

// processVirtualReward 处理虚拟奖励
func (s *Service) processVirtualReward(ctx context.Context, tx *gorm.DB, userID string, taskConfig *models.TaskConfig, req *v1.ReceiveTaskRewardRequest, rewardIssueID int64, reply *v1.ReceiveTaskRewardReply) (models.TaskRewardIssueStatus, error) {
	if taskConfig.RewardConfigObj.VirtualGoods != nil {
		s.fillVirtualRewardInfo(ctx, taskConfig, reply)
		log.Context(ctx).Infof("虚拟商品奖励记录创建成功, userId=%s, taskId=%d, goodsId=%s", userID, req.TaskId, taskConfig.RewardConfigObj.VirtualGoods.GoodsID)

		err := s.taskRewardIssue.UpdateStatusWithTx(ctx, tx, uint(rewardIssueID), models.TaskRewardIssueIssuing)
		if err != nil {
			log.Context(ctx).Errorf("更新奖励记录状态失败: %v", err)
			return models.TaskRewardIssuePending, errors.ErrInternalServer
		}
	}
	return models.TaskRewardIssueIssuing, nil
}

// getLocalizedTaskTitle 获取本地化的任务标题
func (s *Service) getLocalizedTaskTitle(ctx context.Context, taskConfig *models.TaskConfig) string {
	taskTitle := taskConfig.TaskTitle
	langCode, _ := icontext.LanguageCodeFrom(ctx)
	if langCode == "" {
		langCode = "en"
	}
	// 如果有多语言配置，使用对应语言的标题
	if taskConfig.I18nObj != nil {
		if langInfo, exists := taskConfig.I18nObj[langCode]; exists && langInfo.TaskTitle != "" {
			taskTitle = langInfo.TaskTitle
		} else if langInfo, exists := taskConfig.I18nObj["zh-cn"]; exists && langInfo.TaskTitle != "" {
			taskTitle = langInfo.TaskTitle
		}
	}
	return taskTitle
}

// ListRewardRecords 查询奖励记录
func (s *Service) ListRewardRecords(_ context.Context, _ *v1.ListRewardRecordsRequest) (*v1.ListRewardRecordsReply, error) {
	// 实现查询奖励记录的逻辑
	return &v1.ListRewardRecordsReply{}, nil
}

// UpdateTaskProgressByPostStatus 根据帖子状态更新任务进度
// 用于处理Kafka消息中的帖子状态变更事件
func (s *Service) UpdateTaskProgressByPostStatus(ctx context.Context, userId string, taskType models.TaskSubType, targetCode string) error {
	taskEnumCode := string(taskType)
	taskConfigList, err := s.taskConfig.ListTaskConfigsByEnumCode(ctx, taskEnumCode)
	if err != nil {
		log.Errorf("获取任务配置失败, enumCode: %s, error: %v", taskEnumCode, err)
		return err
	}

	// 如果没有找到任务配置，记录日志并返回
	if len(taskConfigList) == 0 {
		return nil
	}

	// 处理每个匹配的任务配置
	for _, taskConfig := range taskConfigList {
		if targetCode != "" && taskConfig.TaskConfigObj.TargetCode != "" && taskConfig.TaskConfigObj.TargetCode != targetCode {
			log.Infof("任务目标不匹配, skip, taskId: %d, targetCode: %s, requiredCode: %s",
				taskConfig.ID, targetCode, taskConfig.TaskConfigObj.TargetCode)
			continue
		}

		taskID := taskConfig.ID
		taskProgress, err := s.taskProgress.GetTaskProgress(ctx, userId, taskID)
		if err != nil {
			log.Errorf("获取任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
			continue
		}

		now := time.Now().UTC()

		// 处理不同的任务状态场景
		if taskProgress == nil {
			if err := s.createNewTaskProgress(ctx, userId, taskID, taskConfig, now); err != nil {
				log.Errorf("创建任务进度记录失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
				continue
			}
			log.Infof("成功创建任务进度记录, userId: %s, taskId: %d, enumCode: %s", userId, taskID, taskEnumCode)
			continue
		}

		// 处理已有任务进度记录的情况
		if s.isNewComerTaskCompleted(taskConfig, taskProgress) {
			log.Infof("新手任务已完成或已领取奖励，不再更新进度, userId: %s, taskId: %d", userId, taskID)
			continue
		}

		// 处理任务过期情况
		if s.isTaskExpired(taskProgress, now) {
			if taskProgress.Status != models.UserProgressStatusExpired {
				if err := s.taskProgress.UpdateStatus(ctx, userId, taskID, models.UserProgressStatusExpired); err != nil {
					log.Errorf("更新过期任务状态失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
					continue
				}
			}

			if taskConfig.TaskType == models.TaskTypeDaily {
				if err := s.createNewTaskProgress(ctx, userId, taskID, taskConfig, now); err != nil {
					log.Errorf("创建新的任务进度记录失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
				} else {
					log.Infof("成功创建新的任务进度记录, userId: %s, taskId: %d", userId, taskID)
				}
			}
			continue
		}

		if taskProgress.Status != models.UserProgressStatusOngoing {
			log.Warnf("任务状态不适合更新进度, userId: %s, taskId: %d, status: %d", userId, taskID, taskProgress.Status)
			continue
		}

		if err := s.incrementTaskProgress(ctx, userId, taskID, taskProgress, taskConfig); err != nil {
			log.Errorf("更新任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		} else {
			log.Infof("成功更新任务进度, userId: %s, taskId: %d, enumCode: %s", userId, taskID, taskEnumCode)
		}
	}

	return nil
}

// createNewTaskProgress 创建新的任务进度记录
func (s *Service) createNewTaskProgress(ctx context.Context, userId string, taskID uint, taskConfig *models.TaskConfig, now time.Time) error {
	expireTime := s.calculateTaskExpireTime(taskConfig.TaskType, now)

	// 创建任务进度记录，统一使用UTC时区
	err := s.taskProgress.CreateTaskProgress(ctx, userId, taskID, expireTime, "UTC")
	if err != nil {
		log.Errorf("自动创建任务进度记录失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	log.Infof("自动创建任务进度记录成功, userId: %s, taskId: %d, 初始进度: 1", userId, taskID)

	// 检查任务是否已完成（如果仅需完成一次）
	if taskConfig.CompleteTimes <= 1 {
		err = s.taskProgress.UpdateStatus(ctx, userId, taskID, models.UserProgressStatusCompleted)
		if err != nil {
			log.Errorf("更新任务状态为已完成失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
			return err
		}
		log.Infof("任务已完成, userId: %s, taskId: %d", userId, taskID)
	}

	return nil
}

// calculateTaskExpireTime 计算任务过期时间
func (s *Service) calculateTaskExpireTime(taskType models.TaskType, now time.Time) time.Time {
	switch taskType {
	case models.TaskTypeDaily:
		// 日常任务：过期时间为UTC时间的次日0点
		tomorrow := now.AddDate(0, 0, 1)
		return time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, time.UTC)
	case models.TaskTypeNewComer:
		// 新手任务：设置为"永不过期"（设置为2040年1月1日）
		return time.Date(2040, 1, 1, 0, 0, 0, 0, time.UTC)
	default:
		// 默认使用一周后过期
		return now.AddDate(0, 0, 7)
	}
}

// isNewComerTaskCompleted 检查新手任务是否已完成或已领取
func (s *Service) isNewComerTaskCompleted(taskConfig *models.TaskConfig, taskProgress *models.TaskProgress) bool {
	return taskConfig.TaskType == models.TaskTypeNewComer &&
		(taskProgress.Status == models.UserProgressStatusCompleted ||
			taskProgress.Status == models.UserProgressStatusReceived)
}

// isTaskExpired 检查任务是否已过期
func (s *Service) isTaskExpired(taskProgress *models.TaskProgress, now time.Time) bool {
	return taskProgress.Status == models.UserProgressStatusExpired || taskProgress.ExpireTime.Before(now)
}

// incrementTaskProgress 任务进度并检查是否完成
func (s *Service) incrementTaskProgress(ctx context.Context, userId string, taskID uint, taskProgress *models.TaskProgress, taskConfig *models.TaskConfig) error {
	newProgress := taskProgress.UserProgress + 1
	err := s.taskProgress.UpdateProgress(ctx, userId, taskProgress.ID, newProgress)
	if err != nil {
		log.Errorf("更新任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	log.Infof("成功更新任务进度, userId: %s, taskId: %d, progress: %d -> %d", userId, taskID, taskProgress.UserProgress, newProgress)

	// 检查是否达到完成条件
	if newProgress >= taskConfig.CompleteTimes {
		err := s.taskProgress.UpdateStatus(ctx, userId, taskID, models.UserProgressStatusCompleted)
		if err != nil {
			log.Errorf("更新任务状态为已完成失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
			return err
		}
		log.Infof("任务已完成, userId: %s, taskId: %d", userId, taskID)
	}

	return nil
}

// getDailyTasks 获取日常任务列表
func (s *Service) getDailyTasks(ctx context.Context, userID string) ([]*v1.TaskInfo, error) {
	taskConfigs, progressMap, err := s.fetchTaskConfigs(ctx, userID, models.TaskTypeDaily)
	if err != nil {
		return nil, err
	}

	if len(taskConfigs) == 0 {
		return []*v1.TaskInfo{}, nil
	}

	// 批量获取商品信息映射
	goodsInfoMap := s.getGoodsInfoMap(ctx, taskConfigs)

	shadow, hasUhost := s.shadow(ctx)
	var result []*v1.TaskInfo
	for _, config := range taskConfigs {
		if shadow && config.TaskEnumCode == string(models.TaskSubTypeOpenVPS) && hasUhost == false {
			continue
		}
		progress, exists := progressMap[config.ID]
		var userProgress int32 = 0
		status := int32(models.UserProgressStatusOngoing)

		if exists && progress != nil {
			status = int32(progress.Status)
			userProgress = int32(progress.UserProgress)
		}
		taskInfo := s.buildTaskInfo(ctx, config, goodsInfoMap, shadow)
		if taskInfo == nil {
			continue
		}

		taskInfo.UserProgress = userProgress
		taskInfo.Status = status

		result = append(result, taskInfo)
	}

	log.Context(ctx).Infof("成功获取日常任务列表，数量: %d", len(result))
	return result, nil
}

// getNewComerTasks 获取新手任务列表
func (s *Service) getNewComerTasks(ctx context.Context, userID string) ([]*v1.TaskInfo, error) {
	taskConfigs, progressMap, err := s.fetchTaskConfigs(ctx, userID, models.TaskTypeNewComer)
	if err != nil {
		return nil, err
	}

	// 如果没有任务配置，返回空列表
	if len(taskConfigs) == 0 {
		log.Context(ctx).Info("未找到活跃的新手任务配置")
		return []*v1.TaskInfo{}, nil
	}

	// 批量获取商品信息映射
	goodsInfoMap := s.getGoodsInfoMap(ctx, taskConfigs)

	shadow, hasUhost := s.shadow(ctx)
	// 构建任务列表响应
	var result []*v1.TaskInfo
	for _, config := range taskConfigs {
		//vps屏蔽
		if shadow && config.TaskEnumCode == string(models.TaskSubTypeOpenVPS) && hasUhost == false {
			continue
		}
		log.Context(ctx).Infof("屏蔽VPS任务: %s userId %s shadow %t", config.TaskTitle, userID, shadow)
		progress, exists := progressMap[config.ID]
		status := int32(models.UserProgressStatusOngoing)
		var userProgress int32 = 0

		if exists && progress != nil {
			status = int32(progress.Status)
			userProgress = int32(progress.UserProgress)
		}

		taskInfo := s.buildTaskInfo(ctx, config, goodsInfoMap, shadow)
		if taskInfo == nil {
			continue
		}
		taskInfo.UserProgress = userProgress
		taskInfo.Status = status

		result = append(result, taskInfo)
	}

	log.Context(ctx).Infof("成功获取新手任务列表，数量: %d", len(result))

	// 如果新手任务都领取了奖励或者任务过期后，隐藏新手任务（返回空数组）
	if len(result) > 0 {
		allCompletedOrExpired := true
		for _, task := range result {
			// 任务只有在"已领取"或"过期"状态才算完成
			if task.Status != int32(models.UserProgressStatusExpired) &&
				task.Status != int32(models.UserProgressStatusReceived) {
				allCompletedOrExpired = false
				break
			}
		}

		if allCompletedOrExpired {
			log.Context(ctx).Info("所有新手任务已领取奖励或已过期，隐藏新手任务")
			return []*v1.TaskInfo{}, nil // 返回空列表隐藏新手任务
		}
	}

	return result, nil
}

// buildTaskInfo 构建任务信息
func (s *Service) buildTaskInfo(ctx context.Context, config *models.TaskConfig, goodsInfoMap map[string]*v1.GoodsListItem, shadow bool) *v1.TaskInfo {
	if config == nil {
		log.Context(ctx).Infof("任务配置不能为空")
		return nil
	}
	taskInfoMap := models.AllTaskInfoMap()

	rewardDesc := ""
	var physicalGoodsInfo, virtualGoodsInfo []*v1.GoodsListItem

	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	format := "+%s"
	if languageCode == "ar" {
		format = "%s+"
	}

	switch config.RewardType {
	case models.RewardTypeGoldCoin:
		if config.RewardConfigObj.GoldCoins > 0 {
			rewardDesc = fmt.Sprintf(format, strconv.Itoa(config.RewardConfigObj.GoldCoins))

		}
	case models.RewardTypePhysical:
		if config.RewardConfigObj.Goods != nil {
			goodsIDStr := config.RewardConfigObj.Goods.GoodsID
			if goodsInfo, ok := goodsInfoMap[goodsIDStr]; ok && goodsInfo != nil {
				physicalGoodsInfo = []*v1.GoodsListItem{goodsInfo}
				rewardDesc = fmt.Sprintf(format, goodsInfo.Name)
			} else {
				log.Context(ctx).Warnf("未找到实物商品信息: goodsId=%s", goodsIDStr)
				return nil
			}
		}
	case models.RewardTypeVirtual:
		if config.RewardConfigObj.VirtualGoods != nil {
			goodsIDStr := config.RewardConfigObj.VirtualGoods.GoodsID
			if goodsInfo, ok := goodsInfoMap[goodsIDStr]; ok && goodsInfo != nil {
				virtualGoodsInfo = []*v1.GoodsListItem{goodsInfo}
				// 如果是VPS商品且启用shadow模式，屏蔽该任务
				if goodsInfo.Category == v1.GoodsCategory_GOODS_CATEGORY_VPS && shadow {
					log.Context(ctx).Infof("屏蔽VPS商品奖励任务: taskId=%d, goodsId=%s, shadow=%t", config.ID, goodsIDStr, shadow)
					return nil
				}
				rewardDesc = fmt.Sprintf(format, goodsInfo.Name)
			} else {
				log.Context(ctx).Warnf("未找到虚拟商品信息: goodsId=%s", goodsIDStr)
				return nil
			}
		}
	default:
		log.Context(ctx).Warnf("未知奖励类型: %d", config.RewardType)
		return nil
	}

	taskInfo := &v1.TaskInfo{
		Id:            int64(config.ID),
		TaskType:      int32(config.TaskType),
		TaskDesc:      taskInfoMap[models.TaskSubType(config.TaskEnumCode)].Description,
		TaskCondition: models.TaskSubType(config.TaskEnumCode).GetTaskCondition(),
		TaskIcon:      urlformat.FullPath(config.TaskIcon, urlTemplate),
		RewardIcon:    urlformat.FullPath(config.RewardIcon, urlTemplate),
		UserProgress:  0,
		CompleteTimes: int32(config.CompleteTimes),
		ShowProject:   config.ShowProject,
		MinVersion:    config.MinVersion,
		VisibleUsers:  config.VisibleUsers,
		RewardType:    int32(config.RewardType),
		RewardDesc:    rewardDesc,
		TaskSubType:   config.TaskEnumCode,
		TaskTitle:     config.TaskTitle,
	}

	langCode, _ := icontext.LanguageCodeFrom(ctx)
	if langCode == "" {
		langCode = "en"
	}

	if config.I18nObj != nil {
		langInfo, exists := config.I18nObj[langCode]
		if !exists {
			langInfo, exists = config.I18nObj["zh-cn"]
			if !exists && len(config.I18nObj) > 0 {
				for _, v := range config.I18nObj {
					langInfo = v
					break
				}
			}
		}

		// 设置多语言任务描述和条件
		if langInfo.TaskDesc != "" {
			taskInfo.TaskDesc = langInfo.TaskDesc
		}
		if langInfo.TaskTitle != "" {
			taskInfo.TaskTitle = langInfo.TaskTitle
		}
	}

	// 如果多语言配置中没有数据，则回退到使用枚举中的默认值
	if taskInfo.TaskDesc == "" || taskInfo.TaskCondition == "" {
		if config.TaskEnumCode != "" {
			taskSubType := models.TaskSubType(config.TaskEnumCode)
			if taskInfo.TaskDesc == "" {
				taskInfo.TaskDesc = taskInfoMap[taskSubType].Description
			}
			if taskInfo.TaskCondition == "" {
				taskInfo.TaskCondition = taskSubType.GetTaskDesc()
			}
		}
	}

	// 从TaskConfigObj中获取额外任务配置
	if config.TaskConfigObj.TargetCode != "" {
		taskInfo.TargetCode = config.TaskConfigObj.TargetCode
	}
	if config.TaskConfigObj.TimeLimit > 0 {
		taskInfo.TimeLimit = config.TaskConfigObj.TimeLimit
	}

	// 填充奖励信息（从解析后的 RewardConfigObj 中获取）
	reward := &v1.RewardInfo{
		GoldCoins: int32(config.RewardConfigObj.GoldCoins),
	}

	// 填充实物奖励信息
	if config.RewardConfigObj.Goods != nil {
		goods := &v1.GoodsReward{
			GoodsId:      config.RewardConfigObj.Goods.GoodsID,
			GoodsIcon:    urlformat.FullPath(config.RewardConfigObj.Goods.GoodsIcon, urlTemplate),
			FreeShipping: config.RewardConfigObj.Goods.FreeShipping,
			GoodsName:    "", // 默认名称
		}

		// 如果成功获取了商品信息，使用实际名称
		if len(physicalGoodsInfo) > 0 {
			goods.GoodsName = physicalGoodsInfo[0].Name
		}

		reward.Goods = goods
	}

	// 填充虚拟奖励信息
	if config.RewardConfigObj.VirtualGoods != nil {
		virtualGoods := &v1.VirtualGoodsReward{
			GoodsIcon: urlformat.FullPath(config.RewardConfigObj.VirtualGoods.GoodsIcon, urlTemplate),
			GoodsId:   config.RewardConfigObj.VirtualGoods.GoodsID,
			GoodsName: "", // 默认名称
		}

		// 如果成功获取了虚拟商品信息，使用实际名称
		if len(virtualGoodsInfo) > 0 {
			virtualGoods.GoodsName = virtualGoodsInfo[0].Name
		}

		reward.VirtualGoods = virtualGoods
	}

	taskInfo.RewardInfo = reward
	return taskInfo
}

// fetchTaskConfigs 获取任务配置并构建进度映射
func (s *Service) fetchTaskConfigs(ctx context.Context, userID string, taskType models.TaskType) ([]*models.TaskConfig, map[uint]*models.TaskProgress, error) {
	// 获取任务配置
	minVersion, _ := icontext.AppVersionFrom(ctx)
	taskConfigParams := &dao.TaskConfigActiveParams{
		TaskType:   taskType,
		MinVersion: minVersion,
	}

	taskConfigs, err := s.taskConfig.ListActive(ctx, taskConfigParams)
	if err != nil {
		log.Context(ctx).Errorf("获取任务配置失败: %v", err)
		return nil, nil, err
	}

	// 如果没有任务配置，返回空列表
	if len(taskConfigs) == 0 {
		log.Context(ctx).Infof("未找到任务配置，任务类型: %d", taskType)
		return taskConfigs, nil, nil
	}

	// 提取任务ID列表
	var taskIDs []uint
	for _, config := range taskConfigs {
		taskIDs = append(taskIDs, config.ID)
	}

	// 获取用户的任务进度
	taskProgresses, err := s.taskProgress.ListByUserIDAndTaskTypeSimple(ctx, userID, taskIDs)
	if err != nil {
		log.Context(ctx).Errorf("获取用户任务进度失败: %v", err)
		return nil, nil, err
	}

	// 构建任务进度映射表 (taskID -> taskProgress)
	progressMap := make(map[uint]*models.TaskProgress)
	for _, progress := range taskProgresses {
		progressMap[progress.TaskConfigID] = progress
	}

	return taskConfigs, progressMap, nil
}

// getGoodsInfoMap 批量获取商品信息并构建映射
func (s *Service) getGoodsInfoMap(ctx context.Context, taskConfigs []*models.TaskConfig) map[string]*v1.GoodsListItem {
	goodsInfoMap := make(map[string]*v1.GoodsListItem)

	// 收集所有需要查询的商品ID
	var goodsIDs []string
	for _, config := range taskConfigs {
		if config.RewardConfigObj.Goods != nil && config.RewardConfigObj.Goods.GoodsID != "" {
			goodsIDs = append(goodsIDs, config.RewardConfigObj.Goods.GoodsID)
		}
		if config.RewardConfigObj.VirtualGoods != nil && config.RewardConfigObj.VirtualGoods.GoodsID != "" {
			goodsIDs = append(goodsIDs, config.RewardConfigObj.VirtualGoods.GoodsID)
		}
	}

	// 批量查询商品信息
	if len(goodsIDs) > 0 {
		goodsInfoList, err := s.findGoodsInfo(ctx, goodsIDs)
		if err != nil {
			log.Context(ctx).Errorf("批量查询商品信息失败: %v", err)
			return goodsInfoMap
		}

		// 构建商品信息映射
		for _, goodsInfo := range goodsInfoList {
			goodsInfoMap[goodsInfo.GoodsId] = goodsInfo
		}
	}

	return goodsInfoMap
}

// // SendTaskGold 任务完成发送积分
func (s *Service) issueTaskGold(ctx context.Context, userId string, goldAmount int, taskTitle string, icon string, taskId uint) error {
	if goldAmount <= 0 {
		return errors.ErrBadRequest
	}

	translateProductName, err := s.ConvertTaskI18nFormat(ctx, taskId)
	if err != nil {
		log.Context(ctx).Errorf("translateProductName error: %v", err)
	}
	req := &gold.DistributeRequest{
		UserId:               userId,
		ProductName:          taskTitle,
		EnumOrderType:        int(gold.EnumOrderTypeTask),
		Points:               goldAmount,
		Remark:               taskTitle,
		TranslateProductName: translateProductName,
		ProductImgUrl:        icon,
	}

	// 调用积分服务
	resp, err := s.goldClient.Distribute(ctx, req)
	if err != nil {
		log.Context(ctx).Errorf("任务发送积分失败: %v, userId=%s, goldAmount=%d", err, userId, goldAmount)
		return err
	}

	if !resp.IsSuccess {
		log.Context(ctx).Errorf("任务发送积分失败: code=%d, msg=%s, userId=%s, goldAmount=%d",
			resp.Code, resp.Message, userId, goldAmount)
		return errors.ErrBadRequest
	}

	log.Context(ctx).Infof("任务发送积分成功: userId=%s, goldAmount=%d", userId, goldAmount)
	return nil
}

// ConvertTaskI18nFormat 根据taskId将数据库task_config表中的i18n字段格式转换
func (s *Service) ConvertTaskI18nFormat(ctx context.Context, taskID uint) (string, error) {
	// 获取任务配置
	taskConfig, err := s.taskConfig.GetByID(ctx, taskID)
	if err != nil {
		if errors2.Is(err, gorm.ErrRecordNotFound) {
			log.Context(ctx).Warnf("任务配置不存在: taskID=%d", taskID)
			return "", errors.WithMessage(errors.ErrBadRequest, "任务配置不存在")
		}
		log.Context(ctx).Errorf("查询任务配置失败: taskID=%d, error=%v", taskID, err)
		return "", err
	}

	// 转换i18n格式，只保留task_title
	simplifiedI18n := make(map[string]string)
	for lang, langInfo := range taskConfig.I18nObj {
		simplifiedI18n[lang] = langInfo.TaskTitle
	}

	jsonBytes, err := json.Marshal(simplifiedI18n)
	if err != nil {
		log.Context(ctx).Errorf("序列化i18n数据失败: taskID=%d, error=%v", taskID, err)
		return "", errors.WithMessage(errors.ErrInternalServer, "数据格式化失败")
	}

	return string(jsonBytes), nil
}

// GetLatestActiveTaskProgress 获取用户最新的活跃任务进度
// 优先返回进行中的任务，如果没有则返回已完成的任务
func (s *Service) GetLatestActiveTaskProgress(ctx context.Context, userID string, taskConfigID uint) (*models.TaskProgress, error) {
	// 使用现有的方法，但增加状态筛选逻辑
	taskProgress, err := s.taskProgress.GetTaskProgress(ctx, userID, taskConfigID)
	if err != nil {
		log.Context(ctx).Errorf("查询任务进度失败: %v", err)
		return nil, err
	}

	// 如果找到任务且状态为进行中或已完成，则返回
	if taskProgress != nil &&
		(taskProgress.Status == models.UserProgressStatusOngoing ||
			taskProgress.Status == models.UserProgressStatusCompleted) {
		return taskProgress, nil
	}

	return nil, nil
}

// ProcessExpiredTasks 处理过期任务的定时任务
// 建议在定时任务中调用此方法，定期清理过期任务状态
func (s *Service) ProcessExpiredTasks(ctx context.Context) error {
	const batchSize = 100
	processedCount := 0

	log.Context(ctx).Info("开始处理过期任务")

	for {
		// 批量查找过期任务
		expiredTasks, err := s.taskProgress.FindExpiredTasks(ctx, batchSize)
		if err != nil {
			log.Context(ctx).Errorf("查找过期任务失败: %v", err)
			return err
		}

		if len(expiredTasks) == 0 {
			break
		}

		// 收集需要更新的任务ID
		var ids []uint
		for _, task := range expiredTasks {
			if task.Status != models.UserProgressStatusExpired {
				ids = append(ids, task.ID)
			}
		}

		if len(ids) == 0 {
			break
		}

		// 批量更新过期状态
		err = s.taskProgress.UpdateExpiredStatus(ctx, ids)
		if err != nil {
			log.Context(ctx).Errorf("批量更新过期任务状态失败: %v", err)
			return err
		}

		processedCount += len(ids)
		log.Context(ctx).Infof("已处理 %d 个过期任务", len(ids))

		// 如果处理的数量少于批次大小，说明已经处理完所有过期任务
		if len(expiredTasks) < batchSize {
			break
		}
	}

	log.Context(ctx).Infof("过期任务处理完成，总计处理: %d 个", processedCount)
	return nil
}

// QueryUserTaskFinished 查询用户是否完成特定任务
func (s *Service) QueryUserTaskFinished(ctx context.Context, in *v1.QueryUserTaskFinishedRequest) (*v1.QueryUserTaskFinishedReply, error) {
	// 参数验证
	if in.TaskEnumCode == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "任务枚举代码不能为空")
	}

	// 调用业务逻辑
	result, err := s.queryUserTaskFinished(ctx, in.UserId, in.TaskEnumCode)
	if err != nil {
		// 记录错误日志并返回系统错误
		log.Context(ctx).Errorf("查询用户任务完成状态失败: %v", err)
		return nil, errors.ErrInternalServer
	}

	return &v1.QueryUserTaskFinishedReply{
		IsFinished: result,
	}, nil
}

// queryUserTaskFinished 查询用户是否完成特定任务
func (s *Service) queryUserTaskFinished(ctx context.Context, userId string, taskEnum string) (bool, error) {
	// 参数验证
	if userId == "" {
		return false, errors.WithMessage(errors.ErrBadRequest, "用户ID不能为空")
	}
	if taskEnum == "" {
		return false, errors.WithMessage(errors.ErrBadRequest, "任务枚举代码不能为空")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("查询用户任务完成状态, userId: %s, taskEnum: %s", userId, taskEnum)

	// 第一步：根据任务枚举代码查询任务配置
	taskConfig, err := s.taskConfig.GetTaskConfigsByEnumCode(ctx, taskEnum)
	if err != nil {
		l.Errorf("查询任务配置失败: %v", err)
		return false, err
	}

	// 如果没有找到任务配置，表示任务不存在
	if taskConfig == nil {
		l.Infof("任务配置不存在, taskEnum: %s", taskEnum)
		return false, nil
	}

	// 第二步：根据任务ID查询用户任务进度
	taskProgress, err := s.taskProgress.GetByUserAndTaskID(ctx, userId, taskConfig.ID)
	if err != nil {
		l.Errorf("查询用户任务进度失败: %v", err)
		return false, err
	}

	// 如果没有找到任务进度记录，表示用户未开始或未领取该任务
	if taskProgress == nil {
		l.Infof("用户未找到任务进度记录, userId: %s, taskEnum: %s", userId, taskEnum)
		return false, nil
	}

	// 检查任务是否已完成
	isFinished := false
	// 判断任务是否已完成（状态为已完成或已领取奖励）
	isFinished = (taskProgress.Status == models.UserProgressStatusCompleted ||
		taskProgress.Status == models.UserProgressStatusReceived) &&
		taskProgress.IsCompleted(taskConfig)

	l.Infof("用户任务完成状态查询结果, userId: %s, taskEnum: %s, isFinished: %v, status: %v, progress: %d",
		userId, taskEnum, isFinished, taskProgress.Status, taskProgress.UserProgress)

	return isFinished, nil
}
