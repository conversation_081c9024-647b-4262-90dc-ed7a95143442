package service

import (
	"context"
	"encoding/json"
	innerr "errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-redis/redis/v8"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"api-expo/internal/util"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

const (
	audienceCachePrefix = "audience_list:"
	audienceCacheTTL    = 5 * time.Second
)

var (
	professionalsSubIdentities = []v1.SubIdentity{
		v1.SubIdentity_SUB_IDENTITY_FOREX,
		v1.SubIdentity_SUB_IDENTITY_SERVICE_PROVIDER,
		v1.SubIdentity_SUB_IDENTITY_FINTECH,
		v1.SubIdentity_SUB_IDENTITY_CRYPTO,
		v1.SubIdentity_SUB_IDENTITY_SERVICE_IB,
	}

	investorsSubIdentities = []v1.SubIdentity{
		v1.SubIdentity_SUB_IDENTITY_INVESTOR,
		v1.SubIdentity_SUB_IDENTITY_TRADER,
		v1.SubIdentity_SUB_IDENTITY_KOL,
	}

	otherSubIdentities = []v1.SubIdentity{
		v1.SubIdentity_SUB_IDENTITY_OTHER,
		v1.SubIdentity_SUB_IDENTITY_UNKNOWN,
	}
)

// AudienceItemWithSubIdentity 包含SubIdentity信息的观展用户项
type AudienceItemWithSubIdentity struct {
	*v1.AudienceItem
	SubIdentityCode v1.SubIdentity `json:"sub_identity_code"`
}

// AudienceCacheData 缓存的观展用户数据结构
type AudienceCacheData struct {
	Items    []*AudienceItemWithSubIdentity `json:"items"`
	CachedAt time.Time                      `json:"cached_at"`
}

// generateAudienceCacheKey 生成观展用户缓存键
func (s *Service) generateAudienceCacheKey(expoId int64) string {
	return fmt.Sprintf("%s%d", audienceCachePrefix, expoId)
}

// getAudienceFromCache 从缓存中获取观展用户数据
func (s *Service) getAudienceFromCache(ctx context.Context, expoId int64) (*AudienceCacheData, error) {
	var l = log.Context(ctx)

	cacheKey := s.generateAudienceCacheKey(expoId)

	data, err := s.redisCli.Get(ctx, cacheKey).Result()
	if err != nil {
		if innerr.Is(err, redis.Nil) {
			l.Debugf("观展用户缓存未命中: cache_key=%s", cacheKey)
			return nil, nil
		}
		l.Errorf("获取观展用户缓存失败: %v", err)
		return nil, err
	}

	var cacheData AudienceCacheData
	if err := json.Unmarshal([]byte(data), &cacheData); err != nil {
		l.Errorf("反序列化缓存数据失败: %v", err)
		s.redisCli.Del(ctx, cacheKey)
		return nil, err
	}

	l.Debugf("观展用户缓存命中: cache_key=%s, items=%d", cacheKey, len(cacheData.Items))
	return &cacheData, nil
}

// setAudienceToCache 将观展用户数据存储到缓存
func (s *Service) setAudienceToCache(ctx context.Context, expoId int64, cacheData *AudienceCacheData) error {
	var l = log.Context(ctx)

	cacheKey := s.generateAudienceCacheKey(expoId)
	cacheData.CachedAt = time.Now()

	data, err := json.Marshal(cacheData)
	if err != nil {
		l.Errorf("序列化缓存数据失败: %v", err)
		return err
	}

	err = s.redisCli.Set(ctx, cacheKey, string(data), audienceCacheTTL).Err()
	if err != nil {
		l.Errorf("缓存观展用户数据失败: %v", err)
		return err
	}

	l.Debugf("缓存观展用户数据成功: cache_key=%s, items=%d", cacheKey, len(cacheData.Items))
	return nil
}

// buildAudienceItemFromParticipant 从participant构建AudienceItemWithSubIdentity
func (s *Service) buildAudienceItemFromParticipant(audience *models.Participant, userInfoMap map[string]*UserInfo, languageCode string) *AudienceItemWithSubIdentity {
	var label []string
	if industryDesc := GetIdentityDesc(audience.IndustryCode, languageCode); industryDesc != "" {
		label = append(label, industryDesc)
	}
	if subIdentityDesc := GetSubIdentityDesc(audience.SubIdentityCode, languageCode); subIdentityDesc != "" {
		label = append(label, subIdentityDesc)
	}

	userId := ""
	if audience.ApplyUserid != "" && audience.IsResister == temporaryUserFlag {
		userId = audience.ApplyUserid
	} else {
		userId = audience.UserId
	}

	userInfo, exists := userInfoMap[userId]
	if !exists {
		userInfo = &UserInfo{}
	}

	var avatar string
	if userInfo.AvatarAddress != "" {
		avatar = userInfo.AvatarAddress
	} else {
		avatar = urlformat.FullPath(getLetterAvatar(audience.FirstName), urlTemplate)
	}

	nickname := userInfo.Nickname
	if nickname == "" {
		nickname = util.FullName(audience.LastName, audience.FirstName)
	}

	audienceItem := &v1.AudienceItem{
		Id:               userId,
		Nickname:         nickname,
		Avatar:           avatar,
		UserIdentityIcon: userInfo.userIdentityNewIcon,
		CountryCode:      audience.AreaCode,
		Identity:         v1.Identity(audience.IdentityCode),
		Check:            userInfo.IsFirmoffer,
		Label:            label,
		IsTemporary:      !audience.IsResister,
	}

	return &AudienceItemWithSubIdentity{
		AudienceItem:    audienceItem,
		SubIdentityCode: audience.SubIdentityCode,
	}
}

// filterAudienceByKeyword 根据关键字过滤观展用户
func (s *Service) filterAudienceByKeyword(audienceItems []*AudienceItemWithSubIdentity, keyword string) []*AudienceItemWithSubIdentity {
	if keyword == "" {
		return audienceItems
	}

	keyword = strings.ToLower(strings.TrimSpace(keyword))
	if keyword == "" {
		return audienceItems
	}

	var filtered []*AudienceItemWithSubIdentity
	for _, item := range audienceItems {
		if item == nil || item.AudienceItem == nil {
			continue
		}

		// 搜索昵称（不区分大小写）
		nickname := strings.ToLower(item.AudienceItem.Nickname)
		if strings.Contains(nickname, keyword) {
			filtered = append(filtered, item)
		}
	}

	return filtered
}

// filterAudienceBySubIdentity 根据子身份过滤观展用户
func (s *Service) filterAudienceBySubIdentity(audienceItems []*AudienceItemWithSubIdentity, subIdentities []v1.SubIdentity) []*AudienceItemWithSubIdentity {
	if len(subIdentities) == 0 {
		return audienceItems
	}

	// 创建子身份映射以提高查找效率
	subIdentityMap := make(map[v1.SubIdentity]bool)
	for _, subIdentity := range subIdentities {
		subIdentityMap[subIdentity] = true
	}

	var filtered []*AudienceItemWithSubIdentity
	for _, item := range audienceItems {
		if item == nil {
			continue
		}

		// 现在可以直接使用SubIdentityCode字段进行过滤
		if subIdentityMap[item.SubIdentityCode] {
			filtered = append(filtered, item)
		}
	}

	return filtered
}

// paginateAudience 对观展用户列表进行分页
func (s *Service) paginateAudience(audienceItems []*AudienceItemWithSubIdentity, page, size int) []*AudienceItemWithSubIdentity {
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = int(defaultPageSize)
	}

	startIndex := (page - 1) * size
	endIndex := startIndex + size

	if startIndex >= len(audienceItems) {
		return []*AudienceItemWithSubIdentity{}
	}

	if endIndex > len(audienceItems) {
		endIndex = len(audienceItems)
	}

	return audienceItems[startIndex:endIndex]
}

// GetAudienceList 获取观展用户列表
func (s *Service) GetAudienceList(ctx context.Context, in *v1.GetAudienceRequest) (*v1.GetAudienceReply, error) {
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	var (
		l = log.Context(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid expo ID")
	}

	in.Page, in.Size = util.ValidatePagination(in.Page, in.Size, defaultPageSize)

	cacheData, err := s.getAudienceFromCache(ctx, expoId)
	if err != nil {
		l.Warnf("获取缓存失败: %v", err)
	}

	var allAudienceItems []*AudienceItemWithSubIdentity

	if cacheData == nil {
		validParticipants, err := s.participant.GetValidParticipantsByExpoID(ctx, expoId)
		if err != nil {
			l.Errorf("participant.GetValidParticipantsByExpoID failed: %v", err)
			return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query audience list")
		}

		if len(validParticipants) == 0 {
			return &v1.GetAudienceReply{
				Items: []*v1.AudienceItem{},
			}, nil
		}

		applyUseridList := make([]string, 0, len(validParticipants))
		for _, record := range validParticipants {
			if record.ApplyUserid != "" {
				applyUseridList = append(applyUseridList, record.ApplyUserid)
			}
		}

		languageCode, _ := icontext.LanguageCodeFrom(ctx)
		userInfoMap, err := s.fetchUserInfoBatch(ctx, applyUseridList)
		if err != nil {
			l.Errorf("fetchUserInfoBatch failed: %v", err)
			userInfoMap = make(map[string]*UserInfo)
		}

		allAudienceItems = make([]*AudienceItemWithSubIdentity, 0, len(validParticipants))
		for _, participant := range validParticipants {
			item := s.buildAudienceItemFromParticipant(participant, userInfoMap, languageCode)
			allAudienceItems = append(allAudienceItems, item)
		}

		cacheData = &AudienceCacheData{
			Items: allAudienceItems,
		}
		if err := s.setAudienceToCache(ctx, expoId, cacheData); err != nil {
			l.Warnf("缓存数据失败: %v", err)
		}
	} else {
		allAudienceItems = cacheData.Items
		l.Debugf("使用缓存数据: items=%d", len(allAudienceItems))
	}

	searchedItems := s.filterAudienceByKeyword(allAudienceItems, in.Keyword)

	subIdentities := getSubIdentitiesByTabId(in.TabId)
	tabFilteredItems := s.filterAudienceBySubIdentity(searchedItems, subIdentities)

	paginatedItems := s.paginateAudience(tabFilteredItems, int(in.Page), int(in.Size))

	resultItems := make([]*v1.AudienceItem, 0, len(paginatedItems))
	for _, item := range paginatedItems {
		if item != nil && item.AudienceItem != nil {
			resultItems = append(resultItems, item.AudienceItem)
		}
	}

	l.Infof("GetAudienceList success: userID=%s, expoId=%d, tabId=%s, page=%d, size=%d, keyword=%s, total_filtered=%d, results=%d",
		userID, expoId, in.TabId, in.Page, in.Size, in.Keyword, len(tabFilteredItems), len(resultItems))

	return &v1.GetAudienceReply{
		Items: resultItems,
	}, nil
}

// GetAudienceTab 获取观展用户分类标签
func (s *Service) GetAudienceTab(ctx context.Context, in *v1.GetAudienceTabRequest) (*v1.GetAudienceTabReply, error) {
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid expo ID")
	}

	// 尝试从缓存获取观展用户数据
	cacheData, err := s.getAudienceFromCache(ctx, expoId)
	if err != nil {
		l.Warnf("获取缓存失败: %v", err)
	}

	var allAudienceItems []*AudienceItemWithSubIdentity

	if cacheData == nil {
		// 缓存未命中，从数据库获取数据
		validParticipants, err := s.participant.GetValidParticipantsByExpoID(ctx, expoId)
		if err != nil {
			l.Errorf("participant.GetValidParticipantsByExpoID failed: %v", err)
			return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query audience list")
		}

		if len(validParticipants) == 0 {
			// 没有数据时返回空的tab统计
			tabs := []*v1.AudienceTab{
				{
					Name:  i18n.GetWithDefaultEnglish("63184", languageCode),
					Id:    professionalId,
					Count: 0,
				},
				{
					Name:  i18n.GetWithDefaultEnglish("63185", languageCode),
					Id:    investorsId,
					Count: 0,
				},
				{
					Name:  i18n.GetWithDefaultEnglish("63186", languageCode),
					Id:    otherId,
					Count: 0,
				},
			}
			return &v1.GetAudienceTabReply{Tabs: tabs}, nil
		}

		allAudienceItems = make([]*AudienceItemWithSubIdentity, 0, len(validParticipants))
		for _, participant := range validParticipants {
			item := &AudienceItemWithSubIdentity{
				SubIdentityCode: participant.SubIdentityCode,
			}
			allAudienceItems = append(allAudienceItems, item)
		}

		applyUseridList := make([]string, 0, len(validParticipants))
		for _, record := range validParticipants {
			if record.ApplyUserid != "" {
				applyUseridList = append(applyUseridList, record.ApplyUserid)
			}
		}

		userInfoMap, err := s.fetchUserInfoBatch(ctx, applyUseridList)
		if err != nil {
			l.Errorf("fetchUserInfoBatch failed: %v", err)
			userInfoMap = make(map[string]*UserInfo)
		}

		// 构建完整的缓存数据
		fullAudienceItems := make([]*AudienceItemWithSubIdentity, 0, len(validParticipants))
		for _, participant := range validParticipants {
			item := s.buildAudienceItemFromParticipant(participant, userInfoMap, languageCode)
			fullAudienceItems = append(fullAudienceItems, item)
		}

		cacheData = &AudienceCacheData{
			Items: fullAudienceItems,
		}
		if err := s.setAudienceToCache(ctx, expoId, cacheData); err != nil {
			l.Warnf("缓存数据失败: %v", err)
		}

		allAudienceItems = fullAudienceItems
	} else {
		allAudienceItems = cacheData.Items
		l.Debugf("使用缓存数据统计tab: items=%d", len(allAudienceItems))
	}

	// 基于缓存数据统计各分类数量
	var professionalsCount, investorsCount, exhibitorsCount int64

	// 创建子身份映射以提高查找效率
	professionalsMap := make(map[v1.SubIdentity]bool)
	for _, subIdentity := range professionalsSubIdentities {
		professionalsMap[subIdentity] = true
	}

	investorsMap := make(map[v1.SubIdentity]bool)
	for _, subIdentity := range investorsSubIdentities {
		investorsMap[subIdentity] = true
	}

	othersMap := make(map[v1.SubIdentity]bool)
	for _, subIdentity := range otherSubIdentities {
		othersMap[subIdentity] = true
	}

	// 统计各分类数量
	for _, item := range allAudienceItems {
		if item == nil {
			continue
		}

		if professionalsMap[item.SubIdentityCode] {
			professionalsCount++
		} else if investorsMap[item.SubIdentityCode] {
			investorsCount++
		} else if othersMap[item.SubIdentityCode] {
			exhibitorsCount++
		}
	}

	tabs := []*v1.AudienceTab{
		{
			Name:  i18n.GetWithDefaultEnglish("63184", languageCode),
			Id:    professionalId,
			Count: professionalsCount,
		},
		{
			Name:  i18n.GetWithDefaultEnglish("63185", languageCode),
			Id:    investorsId,
			Count: investorsCount,
		},
		{
			Name:  i18n.GetWithDefaultEnglish("63186", languageCode),
			Id:    otherId,
			Count: exhibitorsCount,
		},
	}

	l.Infof("GetAudienceTab success: userID=%s, expoId=%d, professionals=%d, investors=%d, others=%d, cached=%t",
		userID, expoId, professionalsCount, investorsCount, exhibitorsCount, cacheData != nil)

	return &v1.GetAudienceTabReply{
		Tabs: tabs,
	}, nil
}

// GetAudienceDetail 获取单个观展用户详情
func (s *Service) GetAudienceDetail(ctx context.Context, in *v1.GetAudienceDetailRequest) (*v1.GetAudienceDetailReply, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	var (
		l = log.Context(ctx)
	)

	// 参数验证
	if in.ExpoId == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "expo ID is required")
	}
	if in.UserId == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "user ID is required")
	}

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "invalid expo ID")
	}

	// 根据用户ID查询participant信息
	audience, err := s.participant.FindByExpoIdAndUserId(ctx, expoId, in.UserId)
	if err != nil {
		if innerr.Is(err, ormhelper.ErrNotFound) {
			return nil, errors.WithMessage(errors.ErrResourceNotFound, "此用户未报名")
		}
		l.Errorf("participant.FindByExpoIdAndUserId failed: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "failed to query audience detail")
	}

	// 获取用户信息
	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	userInfoMap, err := s.fetchUserInfoBatch(ctx, []string{audience.ApplyUserid})
	if err != nil {
		l.Errorf("fetchUserInfoBatch failed: %v", err)
	}

	userInfo, exists := userInfoMap[audience.ApplyUserid]
	if !exists {
		l.Warnf("未找到用户信息: userID=%s", audience.ApplyUserid)
		userInfo = &UserInfo{}
	}

	// 构建标签
	var label []string
	if industryDesc := GetIdentityDesc(audience.IndustryCode, languageCode); industryDesc != "" {
		label = append(label, industryDesc)
	}
	if subIdentityDesc := GetSubIdentityDesc(audience.SubIdentityCode, languageCode); subIdentityDesc != "" {
		label = append(label, subIdentityDesc)
	}

	// 处理头像
	var avatar string
	if userInfo.AvatarAddress != "" {
		avatar = userInfo.AvatarAddress
	} else {
		avatar = urlformat.FullPath(getLetterAvatar(audience.FirstName), urlTemplate)
	}

	// 处理昵称
	nickname := userInfo.Nickname
	if nickname == "" {
		nickname = util.FullName(audience.LastName, audience.FirstName)
	}

	// 处理用户ID
	userId := ""
	if audience.ApplyUserid != "" && audience.IsResister == temporaryUserFlag {
		userId = audience.ApplyUserid
	} else {
		userId = audience.UserId
	}

	item := &v1.AudienceItem{
		Id:               userId,
		Nickname:         nickname,
		Avatar:           avatar,
		UserIdentityIcon: userInfo.userIdentityNewIcon,
		CountryCode:      audience.AreaCode,
		Identity:         v1.Identity(audience.IdentityCode),
		Check:            userInfo.IsFirmoffer,
		Label:            label,
		IsTemporary:      audience.IsResister == temporaryUserFlag,
	}

	l.Infof("GetAudienceDetail success: requestUserID=%s, expoId=%d, targetUserId=%s",
		userID, expoId, in.UserId)

	return &v1.GetAudienceDetailReply{
		Item: item,
	}, nil
}

// getSubIdentitiesByTabId 根据标签ID获取对应的身份列表
func getSubIdentitiesByTabId(tabId string) []v1.SubIdentity {
	switch tabId {
	case professionalId:
		return professionalsSubIdentities
	case investorsId:
		return investorsSubIdentities
	case otherId:
		return otherSubIdentities
	default:
		return []v1.SubIdentity{}
	}
}

func GetIdentityDesc(identity v1.Industry, languageCode string) string {
	switch identity {
	case v1.Industry_INDUSTRY_STOCK:
		return i18n.GetWithDefaultEnglish("63259", languageCode)
	case v1.Industry_INDUSTRY_FOREX:
		return i18n.GetWithDefaultEnglish("63185", languageCode)
	case v1.Industry_INDUSTRY_CRYPTO:
		return i18n.GetWithDefaultEnglish("63260", languageCode)
	case v1.Industry_INDUSTRY_FINTECH:
		return i18n.GetWithDefaultEnglish("63261", languageCode)
	default:
		return ""
	}
}
func GetSubIdentityDesc(subIdentity v1.SubIdentity, languageCode string) string {
	switch subIdentity {
	case v1.SubIdentity_SUB_IDENTITY_FOREX:
		return i18n.GetWithDefaultEnglish("63270", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_SERVICE_PROVIDER:
		return i18n.GetWithDefaultEnglish("63271", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_FINTECH:
		return i18n.GetWithDefaultEnglish("63272", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_CRYPTO:
		return i18n.GetWithDefaultEnglish("63273", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_SERVICE_IB:
		return i18n.GetWithDefaultEnglish("63274", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_INVESTOR:
		return i18n.GetWithDefaultEnglish("63275", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_TRADER:
		return i18n.GetWithDefaultEnglish("63276", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_OTHER:
		return i18n.GetWithDefaultEnglish("63277", languageCode)
	case v1.SubIdentity_SUB_IDENTITY_KOL:
		return i18n.GetWithDefaultEnglish("63261", languageCode)
	default:
		return ""
	}
}
