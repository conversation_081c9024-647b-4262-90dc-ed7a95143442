package service

import (
	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"context"
	"errors"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) checkExpoHall(ctx context.Context, in *v1.ExpoHall) error {
	// 参数校验
	if in.ExpoId == 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.Languages != nil && len(in.Languages) > 0 {
		//en 为必输
		enLang, exists := in.Languages["en"]
		if !exists {
			return innErr.WithMessage(innErr.ErrBadRequest, "找不到en语言配置")
		}

		if enLang == nil || enLang.Name == "" {
			return innErr.WithMessage(innErr.ErrBadRequest, "en 描述必须配置")
		}
	}

	if in.Type == v1.ExpoHallType_ExpoHallType_MAIN {
		hall, err := s.expoHall.GetHallType(ctx, in.ExpoId, in.Type)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			return err
		}

		// 已经存在
		if err == nil && int64(hall.ID) != in.Id {
			return innErr.WithMessage(innErr.ErrBadRequest, "主会场已经存在")
		}
	}
	return nil
}

func (s *Service) AddHall(ctx context.Context, in *v1.ExpoHall) (*v1.AddGuestReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoHall(ctx, in)
	if err != nil {
		return nil, err
	}

	_, err = s.expoHall.Add(ctx, &models.ExpoHall{
		Name:    in.Name,
		Type:    in.Type,
		Creator: in.Creator,
		Enable:  in.Enable,
		ExpoId:  in.ExpoId,
		Extra: &igorm.CustomValue[models.ExpoHallExtra]{
			V: models.ExpoHallExtra{
				Languages: in.Languages,
			},
		},
	})
	if err != nil {
		l.Errorf("expoHall.Add Err: %v", err)
		return nil, err
	}
	return &v1.AddGuestReply{}, nil
}

func (s *Service) GetHall(ctx context.Context, in *v1.GetHallRequest) (*v1.ExpoHall, error) {
	l := log.Context(ctx)

	if in.Id == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会会场ID不能为空")
	}

	out, err := s.expoHall.GetHallById(ctx, in.Id)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoHall.GetHallById Err: %v", err)
		return nil, err
	}

	if err != nil {
		return &v1.ExpoHall{}, nil
	}

	return &v1.ExpoHall{
		ExpoId: out.ExpoId,
		Type:   out.Type,
		Enable: out.Enable,
		Languages: func() map[string]*v1.ExpoHallLanguage {
			if out.Extra == nil || out.Extra.V.Languages == nil {
				return nil
			}
			return out.Extra.V.Languages
		}(),
		CreatedAt: out.CreatedAt.Unix(),
		Creator:   out.Creator,
		Id:        int64(out.ID),
	}, nil
}

func (s *Service) UpdateHall(ctx context.Context, in *v1.ExpoHall) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.checkExpoHall(ctx, in)
	if err != nil {
		return nil, err
	}

	_, err = s.expoHall.Update(ctx,
		in.Id, &models.ExpoHall{
			Name:   in.Name,
			ExpoId: in.ExpoId,
			Type:   in.Type,
			Enable: in.Enable,
			Extra: &igorm.CustomValue[models.ExpoHallExtra]{
				V: models.ExpoHallExtra{
					Languages: in.Languages,
				},
			},
			Creator: in.Creator,
		})
	if err != nil {
		l.Errorf("expoHall.Update Err: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetHallEnable(ctx context.Context, in *v1.SetHallEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Id == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会会场ID不能为空")
	}

	_, err := s.expoHall.SetEnable(ctx, in.Id, in.Enable)
	if err != nil {
		l.Errorf("expoHall.SetEnable Err: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListHall(ctx context.Context, in *v1.ListHallRequest) (list *v1.ListHallReply, err error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Page <= 0 {
		in.Page = 1
	}

	if in.Size <= 0 {
		in.Size = 10
	}

	halls, total, err := s.expoHall.FindAllByExpoId(ctx, in.ExpoId, in.Page, in.Size)
	if err != nil {
		l.Errorf("expoHall.FindAllByExpoId: %v", err)
		return nil, err
	}

	items := make([]*v1.ExpoHall, 0, len(halls))
	for _, v := range halls {
		var enName string
		if v.Extra != nil && v.Extra.V.Languages != nil {
			if lang, exists := v.Extra.V.Languages["en"]; exists && lang != nil {
				enName = lang.Name
			}
		}

		items = append(items, &v1.ExpoHall{
			Id:     int64(v.ID),
			ExpoId: v.ExpoId,
			Type:   v.Type,
			Name:   enName,
			Enable: v.Enable,
			Languages: func() map[string]*v1.ExpoHallLanguage {
				if v.Extra == nil || v.Extra.V.Languages == nil {
					return nil
				}
				return v.Extra.V.Languages
			}(),
			CreatedAt: v.CreatedAt.Unix(),
			Creator:   v.Creator,
		})
	}

	return &v1.ListHallReply{
		Items: items,
		Total: total,
	}, nil
}

func (s *Service) DeleteHall(ctx context.Context, in *v1.DeleteHallRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Id == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会会场ID不能为空")
	}

	err := s.expoHall.Delete(ctx, in.Id)
	if err != nil {
		l.Errorf("expoHall.Delete: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}
