package service

import (
	"context"
	"fmt"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
)

// FacePhotoUpload 展会照片上传与人脸识别
func (s *Service) FacePhotoUpload(ctx context.Context, req *v1.FacePhotoUploadRequest) (*v1.FacePhotoUploadReply, error) {
	// 参数校验
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.PhotoUrl == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "照片URL不能为空")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("展会照片上传与人脸识别:  photo_url=%s, max_face_num=%d", req.PhotoUrl, s.business.TencentAI.SearchConfig.MaxFaceNum)

	groupIds, err := s.faceGroupExpo.GetGroupIDsByExpo(ctx, req.ExpoId)
	if err != nil && len(groupIds) != 1 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "展会信息错误")
	}

	// 构建业务请求参数
	bizReq := &models.FacePhotoUploadRequest{
		GroupID:              groupIds[0],
		PhotoURL:             req.PhotoUrl,
		ExpoID:               req.ExpoId,
		MaxFaceNum:           s.business.TencentAI.SearchConfig.MaxFaceNum,
		MinFaceSize:          s.business.TencentAI.SearchConfig.MinFaceSize,
		NeedQualityDetection: true,
		Operator:             req.Operator,
	}

	// 调用业务逻辑
	result, err := s.UploadFacePhotos(ctx, bizReq)
	if err != nil {
		l.Errorf("展会照片上传失败: %v", err)
		return nil, errors.ErrInternalServer
	}

	// 转换为proto响应
	faces := make([]*v1.FaceUploadItem, 0, len(result.Faces))
	for _, face := range result.Faces {
		item := &v1.FaceUploadItem{
			FaceId:       face.FaceID,
			FaceUrl:      face.FaceURL,
			QualityScore: float32(face.QualityScore),
			Rect: &v1.FaceRect{
				X:      int32(face.Rect.X),
				Y:      int32(face.Rect.Y),
				Width:  int32(face.Rect.Width),
				Height: int32(face.Rect.Height),
			},
			Status:       face.Status,
			ErrorMessage: face.ErrorMessage,
		}
		faces = append(faces, item)
	}

	return &v1.FacePhotoUploadReply{
		UploadId:         result.UploadID,
		OriginalPhotoUrl: result.OriginalPhotoURL,
		TotalFaces:       int32(result.TotalFaces),
		SuccessFaces:     int32(result.SuccessFaces),
		FailedFaces:      int32(result.FailedFaces),
		Faces:            faces,
	}, nil
}

// FaceGroupCreate 创建人员库
func (s *Service) FaceGroupCreate(ctx context.Context, req *v1.FaceGroupCreateRequest) (*v1.FaceGroupCreateReply, error) {
	// 参数校验
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.Name == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库名称不能为空")
	}

	var (
		l           = log.Context(ctx)
		maxFaceNum  = req.MaxFaceNum
		description = req.Description
	)

	// 设置默认值
	if maxFaceNum <= 0 || maxFaceNum < 1000 {
		maxFaceNum = 10000
	}
	if description == "" {
		description = fmt.Sprintf("%s人员库", req.Name)
	}

	l.Infof("创建人员库: name=%s,  expo_id=%s", req.Name, req.ExpoId)

	// 构建业务请求参数
	bizReq := &models.FaceGroupCreateRequest{
		Name:        req.Name,
		Description: description,
		ExpoID:      req.ExpoId,
		MaxFaceNum:  int(maxFaceNum),
	}

	// 调用业务逻辑
	result, err := s.CreateFaceGroup(ctx, bizReq)
	if err != nil {
		l.Errorf("创建人员库失败: %v", err)
		return nil, errors.ErrInternalServer
	}

	return &v1.FaceGroupCreateReply{
		GroupId:            result.GroupID,
		Name:               result.Name,
		Description:        result.Description,
		FaceModelVersion:   result.FaceModelVersion,
		MaxFaces:           int32(result.MaxFaces),
		EstimatedFaceCount: int32(result.EstimatedFaceCount),
		Status:             int32(result.Status),
		CreatedAt:          result.CreatedAt,
	}, nil
}

// GetFaceGroupInfo 获取人员库信息
func (s *Service) GetFaceGroupInfo(ctx context.Context, req *v1.FaceGroupInfoRequest) (*v1.FaceGroupInfo, error) {
	// 参数校验
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.GroupId == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "人员库ID不能为空")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("获取人员库信息: group_id=%s", req.GroupId)

	// 调用业务逻辑
	result, err := s.getFaceGroupInfo(ctx, req.GroupId)
	if err != nil {
		l.Errorf("获取人员库信息失败: %v", err)
		return nil, errors.ErrInternalServer
	}

	return &v1.FaceGroupInfo{
		GroupId:            result.GroupID,
		Name:               result.Name,
		Description:        result.Description,
		FaceModelVersion:   result.FaceModelVersion,
		MaxFaces:           int32(result.MaxFaces),
		EstimatedFaceCount: int32(result.EstimatedFaceCount),
		Status:             int32(result.Status),
		CreatedAt:          result.CreatedAt,
	}, nil
}

// FaceGroupList 获取人员库列表
func (s *Service) FaceGroupList(ctx context.Context, req *v1.FaceGroupListRequest) (*v1.FaceGroupListReply, error) {
	var (
		l    = log.Context(ctx)
		page = req.Page
		size = req.Size
	)

	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 20
	}

	l.Infof("获取人员库列表: page=%d, size=%d", page, size)

	// 构建业务请求参数
	bizReq := &models.FaceGroupListRequest{
		Page: int(page),
		Size: int(size),
	}

	// 调用业务逻辑
	result, err := s.getFaceGroupList(ctx, bizReq)
	if err != nil {
		l.Errorf("获取人员库列表失败: %v", err)
		return nil, errors.ErrInternalServer
	}

	// 转换为proto响应
	groups := make([]*v1.FaceGroupInfo, 0, len(result.Groups))
	for _, group := range result.Groups {
		groupInfo := &v1.FaceGroupInfo{
			GroupId:            group.GroupID,
			Name:               group.Name,
			Description:        group.Description,
			FaceModelVersion:   group.FaceModelVersion,
			MaxFaces:           int32(group.MaxFaces),
			EstimatedFaceCount: int32(group.EstimatedFaceCount),
			Status:             int32(group.Status),
			CreatedAt:          group.CreatedAt,
		}
		groups = append(groups, groupInfo)
	}

	return &v1.FaceGroupListReply{
		Groups: groups,
		Total:  int32(result.Total),
		Page:   int32(result.Page),
		Size:   int32(result.Size),
	}, nil
}

// FaceGroupsByExpo 根据场景获取人员库
func (s *Service) FaceGroupsByExpo(ctx context.Context, req *v1.FaceGroupsByExpoRequest) (*v1.FaceGroupInfo, error) {
	// 参数校验
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.ExpoId == 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "场景ID不能为空")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("根据场景获取人员库: expo_id=%s", req.ExpoId)

	// 调用业务逻辑
	results, err := s.GetFaceGroupsByExpo(ctx, req.ExpoId)
	if err != nil {
		l.Errorf("根据场景获取人员库失败: %v", err)
		return nil, err
	}

	// 如果没有找到人员库，返回错误
	if len(results) == 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "场景下没有关联的人员库")
	}

	// 返回第一个人员库信息
	result := results[0]
	return &v1.FaceGroupInfo{
		GroupId:            result.GroupID,
		Name:               result.Name,
		Description:        result.Description,
		FaceModelVersion:   result.FaceModelVersion,
		MaxFaces:           int32(result.MaxFaces),
		EstimatedFaceCount: int32(result.EstimatedFaceCount),
		Status:             int32(result.Status),
		CreatedAt:          result.CreatedAt,
	}, nil
}

// FacesPhotoList  获取照片人脸列表
func (s *Service) FacesPhotoList(ctx context.Context, req *v1.PhotoFacesRequest) (*v1.PhotoFacesReply, error) {
	// 参数校验
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}
	if req.PhotoUrl == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "照片URL不能为空")
	}

	var (
		l = log.Context(ctx)
	)

	l.Infof("获取照片人脸列表: photo_url=%s", req.PhotoUrl)

	// 调用业务逻辑
	result, err := s.GetPhotoFaces(ctx, req.PhotoUrl)
	if err != nil {
		l.Errorf("获取照片人脸列表失败: %v", err)
		return nil, errors.ErrInternalServer
	}

	// 转换为proto响应
	faces := make([]*v1.FaceUploadItem, 0, len(result.Faces))
	for _, face := range result.Faces {
		item := &v1.FaceUploadItem{
			FaceId:       face.FaceID,
			FaceUrl:      face.FaceURL,
			QualityScore: float32(face.QualityScore),
			Rect: &v1.FaceRect{
				X:      int32(face.Rect.X),
				Y:      int32(face.Rect.Y),
				Width:  int32(face.Rect.Width),
				Height: int32(face.Rect.Height),
			},
			Status:       face.Status,
			ErrorMessage: face.ErrorMessage,
		}
		faces = append(faces, item)
	}

	return &v1.PhotoFacesReply{
		PhotoUrl:     result.OriginalPhotoURL,
		TotalFaces:   int32(result.TotalFaces),
		SuccessFaces: int32(result.SuccessFaces),
		FailedFaces:  int32(result.FailedFaces),
		Faces:        faces,
	}, nil
}
