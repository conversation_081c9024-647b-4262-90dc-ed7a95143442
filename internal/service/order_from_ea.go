package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/id"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/recovery"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) orderFromEA(c *kafka.Consumer, message *kafka.Message) {
	defer c.CommitMessage(message)

	var (
		ctx   = context.Background()
		reqId = reqid.GenRequestID()
		data  EAOrder
	)
	ctx = icontext.WithRequestId(ctx, reqId)
	l := log.Context(ctx)
	defer recovery.CatchGoroutinePanicWithContext(ctx)

	err := json.Unmarshal(message.Value, &data)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("EA Unmarshal<%s> Err:%v", string(message.Value), err))
		return
	}

	if !data.Commit && data.Type != "bootstrap-insert" {
		return
	}

	var (
		eaOrder = data.Data
		goodsId = strconv.Itoa(eaOrder.GoodsId)
	)

	eaInfo, err := s.ea.GetEAInfo(ctx, eaOrder.GoodsId)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("EA 订单【%s】获取EA【%d】出错：%s", eaOrder.OrderId, eaOrder.GoodsId, err))
		return
	}

	eaType, err := s.ea.GetEAType(ctx, eaInfo.ClassifyId)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("EA 订单【%s】获取EAType【%d】出错：%s", eaOrder.OrderId, eaInfo.ClassifyId, err))
		return
	}

	mtName := "MT4"
	if eaInfo.MTType == 1 {
		mtName = "MT5"
	}

	var (
		orderNo        = eaOrder.OrderId
		displayAmount  = eaOrder.DisplayAmount
		paymentMethod  = paymentMethodFromEA(eaOrder.Platform)
		operationNo    = eaOrder.PaymId
		paymentNo      = id.GenerateId(id.BusinessPayment, id.WithChannel(strconv.Itoa(int(paymentMethod))))
		paymentStatus  = v1.PaymentStatus_PaymentStatusUNPAY
		orderStatus    = v1.OrderStatus_UNPAY
		price          = eaOrder.Amount
		priceUnit      = "$"
		totalAmount    = eaOrder.Amount
		paidTotal      float32
		quantity       = int32(1)
		goodsName      = eaInfo.Name
		goodsTranslate = &igorm.CustomValue[map[string]*v1.GoodsTranslate]{
			V: map[string]*v1.GoodsTranslate{
				"zh-cn": {
					Name: eaInfo.Name,
				},
				"en": {
					Name: eaInfo.NameEN,
				},
				"zh-hk": {
					Name: eaInfo.NameHK,
				},
				"zh": {
					Name: eaInfo.NameHK,
				},
			},
		}
		eaSpecDesc = map[string]string{
			"zh-cn": fmt.Sprintf("%s;%s", eaType.Type, mtName),
			"en":    fmt.Sprintf("%s;%s", eaType.TypeEN, mtName),
			"zh-hk": fmt.Sprintf("%s;%s", eaType.TypeHK, mtName),
			"zh":    fmt.Sprintf("%s;%s", eaType.TypeHK, mtName),
		}
		specDesc       = fmt.Sprintf("%s;%s", eaType.Type, mtName)
		paidTime       time.Time
		currencySymbol = getFirstChar(eaOrder.DisplayAmount)
		createTime, _  = time.ParseInLocation(time.DateTime, eaOrder.CreateTime, time.UTC)
	)
	createTime = createTime.Add(-time.Hour * 8)

	if paymentMethod == v1.PaymentMethod_GOLD {
		priceUnit = ""
		price = float32(eaOrder.GoldCoin)
		totalAmount = float32(eaOrder.GoldCoin)
		displayAmount = fmt.Sprintf("%d", eaOrder.GoldCoin)
	}

	if eaOrder.TradeStatus == "TRADE_SUCCESS" {
		orderStatus = v1.OrderStatus_COMPLETE
		paymentStatus = v1.PaymentStatus_PaymentStatusPAID
		paidTime, _ = time.ParseInLocation(time.DateTime, eaOrder.FinishedAt, time.UTC)
		paidTotal = eaOrder.Amount
		if paymentMethod == v1.PaymentMethod_GOLD {
			paidTotal = float32(eaOrder.GoldCoin)
		}
		paidTime = paidTime.Add(-time.Hour * 8)
	}

	if paidTime.Before(time.Date(2010, 0, 0, 0, 0, 0, 0, time.UTC)) {
		paidTime = createTime
	}

	orderExtra := &igorm.CustomValue[*models.OrderExtra]{
		V: &models.OrderExtra{
			DisplayAmount: displayAmount,
			SpecDesc:      eaSpecDesc,
		},
	}

	// 待支付订单不同步
	if orderStatus == v1.OrderStatus_UNPAY {
		return
	}

	switch data.Type {
	case "update":
		var (
			payment *models.Payment
			insert  bool
		)

		_, err = s.order.GetByOrderNo(ctx, orderNo)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("order.GetByOrderNo<%s> Err:%v", orderNo, err)
			return
		}

		// 到这里说明订单不存在，可能是先消费了更新；所以需要走insert逻辑
		if err != nil {
			insert = true
		}

		if !insert {
			payment, err = s.payment.GetByOrderNo(ctx, orderNo)
			if err != nil {
				l.Errorf("payment.GetByOrderNo<%s> Err:%v", orderNo, err)
				return
			}

			var orderExtraValue any
			orderExtraValue, err = orderExtra.Value()
			if err != nil {
				l.Errorf("orderExtra.Value Err:%v", err)
				return
			}

			tx := s.goods.Begin()
			defer func() {
				if err == nil {
					tx.Commit()
				} else {
					tx.Rollback()
				}
			}()

			// 4、更新支付单信息
			err = s.payment.UpdatePayment(ctx, &models.Payment{
				PaymentNo:   payment.PaymentNo,
				OperationNo: operationNo,
				PaidTotal:   totalAmount,
				PaidTime:    paidTime,
				Status:      paymentStatus,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("payment.UpdatePayment Err:%v", err)
				return
			}

			// 5、更新支付订单
			err = s.order.Update(ctx, orderNo, map[string]interface{}{
				"status":          orderStatus,
				"total_amount":    totalAmount,
				"extra":           orderExtraValue,
				"currency_symbol": currencySymbol,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("order.UpdateStatus Err:%v", err)
				return
			}
			return
		}
		fallthrough
	case "bootstrap-insert", "insert":
		var (
			goodsSnapshot *models.GoodsSnapshot
			goodsVersion  = eaInfo.UpdateTime.Unix()
		)

		goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
			return
		}

		// 没有找到，这里新增
		if err != nil {
			goodsSnapshot = &models.GoodsSnapshot{
				GoodsId: goodsId,
				Version: goodsVersion,
				Snapshot: &igorm.CustomValue[*models.Snapshot]{
					V: &models.Snapshot{
						Goods: &models.Goods{
							Category:      v1.GoodsCategory_GOODS_CATEGORY_EA,
							GoodsId:       goodsId,
							Name:          goodsName,
							BasePrice:     totalAmount,
							UseBasePrice:  true,
							SelectedPrice: totalAmount,
							Description:   eaInfo.Description,
							Status:        v1.GoodsStatus_GoodsStatusOn,
							FreeShipping:  true,
							Image: &igorm.CustomValue[*v1.Image]{
								V: eaStaticImage,
							},
							Translate: goodsTranslate,
						},
					},
				},
			}

			err = s.goodsSnapshot.Add(ctx, goodsSnapshot)
			if err != nil && !errors.Is(err, ormhelper.ErrDuplicateKey) {
				l.Errorf("goodsSnapshot.Add Err:%v", err)
				return
			}

			if err != nil {
				goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
				if err != nil {
					l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
					return
				}
			}
		}

		var (
			newOrder = &models.Order{
				OrderNo:        orderNo,
				Source:         v1.OrderSource_EA,
				PaymentMethod:  paymentMethod,
				Platform:       platformFromEA(eaOrder.Source),
				UserId:         eaOrder.UserId,
				CountryCode:    eaOrder.Countrycode,
				Address:        &igorm.CustomValue[*models.Address]{},
				Quantity:       quantity,
				CurrencySymbol: currencySymbol,
				TotalAmount:    totalAmount,
				Status:         orderStatus,
				GoodsName:      goodsName,
				Extra:          orderExtra,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}

			newOrderItems = []*models.OrderItem{
				{
					OrderNo:         orderNo,
					GoodsId:         goodsId,
					Price:           price,
					PriceUnit:       priceUnit,
					GoodsSnapshotId: goodsSnapshot.ID,
					Quantity:        quantity,
					TotalAmount:     totalAmount,
					GoodsName:       goodsName,
					SpecDesc:        specDesc,
					Model: gorm.Model{
						CreatedAt: createTime,
						UpdatedAt: createTime,
					},
				},
			}

			newPayment = &models.Payment{
				PaymentNo:   paymentNo,
				OrderNo:     orderNo,
				OperationNo: operationNo,
				TotalAmount: totalAmount,
				PaidTotal:   paidTotal,
				PaidMethod:  paymentMethod,
				Status:      paymentStatus,
				PaidTime:    paidTime,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}
		)

		tx := s.goods.Begin()
		defer func() {
			if err == nil {
				tx.Commit()
			} else {
				tx.Rollback()
			}
		}()

		// 1、添加订单
		err = s.order.Add(ctx, newOrder, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("order.Add Err:%v", err)
			return
		}

		// 3、添加订单项
		err = s.orderItem.BatchAdd(ctx, newOrderItems, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("orderItem.BatchAdd Err:%v", err)
			return
		}

		// 4、添加支付
		err = s.payment.Add(ctx, newPayment, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("payment.Add Err:%v", err)
			return
		}
	}
}

func paymentMethodFromEA(in string) v1.PaymentMethod {
	// ALI：支付宝，WECHAT：微信，APPLEIAP：苹果内购，GOOGLEIAP：谷歌内购用户ID
	switch in {
	case "ALI":
		return v1.PaymentMethod_ALI
	case "WECHAT":
		return v1.PaymentMethod_WECHAT
	case "APPLEIAP":
		return v1.PaymentMethod_APPLE_IAP
	case "GOOGLEIAP":
		return v1.PaymentMethod_GOOGLE_IAP
	default:
		return v1.PaymentMethod_GOLD
	}
}

func platformFromEA(in string) v1.Platform {
	in = strings.ToLower(in)
	switch in {
	case "ios":
		return v1.Platform_IOS
	case "pc":
		return v1.Platform_PC
	case "android":
		return v1.Platform_ANDROID
	default:
		return v1.Platform_IOS
	}
}

func getFirstChar(s string) string {
	if len(s) == 0 {
		return ""
	}
	firstRune, _ := utf8.DecodeRuneInString(s)
	return isCurrencySymbol(string(firstRune))
}

func isCurrencySymbol(r string) string {
	_, ok := currencySymbols[r]
	if !ok {
		return ""
	}
	return r
}
