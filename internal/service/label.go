package service

import (
	"context"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
)

func (s *Service) LabelList(ctx context.Context, in *common.EmptyRequest) (*v1.LabelListReply, error) {
	return &v1.LabelListReply{
		Items: labels,
	}, nil
}

func (s *Service) ExistsLabel(labelId string) (*v1.Label, bool) {
	for _, label := range labels {
		if label.Id == labelId {
			return label, true
		}
	}
	return nil, false
}
