package service

import (
	"context"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"

	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/urlformat"
)

func (s *Service) GoldStoreQuickAccess(ctx context.Context, _ *common.EmptyRequest) (*v1.GoldStoreQuickAccessReply, error) {
	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	return &v1.GoldStoreQuickAccessReply{
		Items: []*v1.QuickAccessItem{
			{
				JumpType: v1.QuicAccessJumpType_QuicAccessJumpTypeGiftCard,
				Name:     i18n.GetWithDefaultEnglish("59865", languageCode), // 礼品卡
				Icon:     urlformat.FullPath("/gold_store/gift-card.png", urlTemplate),
			},
			{
				JumpType: v1.QuicAccessJumpType_QuicAccessJumpTypeEarnPoint,
				Name:     i18n.GetWithDefaultEnglish("59866", languageCode), // 赚积分
				Icon:     urlformat.FullPath("/gold_store/earn_points.png", urlTemplate),
			},
			//{
			//	JumpType: v1.QuicAccessJumpType_QuicAccessJumpTypePointsStore,
			//	Name:     i18n.GetWithDefaultEnglish("59867", languageCode), // 积分商城
			//	Icon:     urlformat.FullPath("/gold_store/point_store.png", urlTemplate),
			//},
			{
				JumpType: v1.QuicAccessJumpType_QuicAccessJumpTypeAddressManagement,
				Name:     i18n.GetWithDefaultEnglish("59868", languageCode), // 地址管理
				Icon:     urlformat.FullPath("/gold_store/address-management.png", urlTemplate),
			},
		},
	}, nil
}

func (s *Service) MyGoldJump(ctx context.Context, in *v1.MyGoldJumpRequest) (*v1.MyGoldJumpReply, error) {
	//countryCode, _ := icontext.CountryCodeFrom(ctx)
	var (
		show      = true
		userId, _ = icontext.UserIdFrom(ctx)
		sign      bool
		points    int32
	)

	if userId != "" {
		sign, _ = s.signRecord.CheckTodaySignIn(ctx, userId, in.Timezone)
		points, _ = s.CalculateTodaySignReward(ctx, userId, in.Timezone)
	}

	//if country.GetAreaCodeByCode(countryCode) == "85830918" {
	//	show = false
	//}
	return &v1.MyGoldJumpReply{
		JumpNew: show,
		Sign:    sign,
		Points:  points,
	}, nil
}
