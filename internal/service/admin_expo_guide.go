package service

import (
	"context"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) checkExpoGuide(in *v1.ExpoGuideInfo) error {
	if in.ExpoId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.MapUrl == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "地图地址不能为空！")
	}
	return nil
}

func (s *Service) AddExpoGuide(ctx context.Context, in *v1.ExpoGuideInfo) (*v1.AddExpoGuideReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoGuide(in)
	if err != nil {
		return nil, err
	}

	id, err := s.expoGuide.Add(ctx, &models.ExpoGuide{
		ExpoId:  in.ExpoId,
		MapURL:  in.MapUrl,
		Enable:  in.Enable,
		Creator: in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoGuideExtra]{
			V: &models.ExpoGuideExtra{},
		},
	})
	if err != nil {
		l.Errorf("expoGuide.Add Err:%v", err)
		return nil, err
	}
	return &v1.AddExpoGuideReply{
		Id: int64(id),
	}, nil
}

func (s *Service) GetExpoGuide(ctx context.Context, in *v1.GetExpoGuideRequest) (*v1.ExpoGuideInfo, error) {
	l := log.Context(ctx)

	guide, err := s.expoGuide.Get(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoGuide.Get Err:%v", err)
		return nil, err
	}
	return s.expoGuideToGRPC(guide), nil
}

func (s *Service) UpdateExpoGuide(ctx context.Context, in *v1.ExpoGuideInfo) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoGuide(in)
	if err != nil {
		return nil, err
	}

	err = s.expoGuide.Update(ctx, &models.ExpoGuide{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId:  in.ExpoId,
		MapURL:  in.MapUrl,
		Enable:  in.Enable,
		Creator: in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoGuideExtra]{
			V: &models.ExpoGuideExtra{},
		},
	})
	if err != nil {
		l.Errorf("expoGuide.Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoGuideEnable(ctx context.Context, in *v1.SetExpoGuideEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoGuide.UpdateEnable(ctx, in.ExpoId, in.Id, in.Enable)
	if err != nil {
		l.Errorf("expoGuide.UpdateEnable Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListExpoGuide(ctx context.Context, in *v1.ListExpoGuideRequest) (*v1.ListExpoGuideReply, error) {
	l := log.Context(ctx)

	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	guides, err := s.expoGuide.FindByPage(ctx, in.ExpoId, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoGuide.FindByExpoId Err:%v", err)
		return nil, err
	}

	total, err := s.expoGuide.CountByExpoId(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoGuide.CountByExpoId Err:%v", err)
		return nil, err
	}

	items := make([]*v1.ExpoGuideInfo, 0, len(guides))
	for _, value := range guides {
		items = append(items, s.expoGuideToGRPC(value))
	}
	return &v1.ListExpoGuideReply{
		Items: items,
		Total: total,
	}, nil
}

func (s *Service) DeleteExpoGuide(ctx context.Context, in *v1.DeleteExpoGuideRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoGuide.Delete(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoGuide.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) expoGuideToGRPC(in *models.ExpoGuide) *v1.ExpoGuideInfo {
	return &v1.ExpoGuideInfo{
		Id:        int64(in.ID),
		ExpoId:    in.ExpoId,
		MapUrl:    in.MapURL,
		Enable:    in.Enable,
		CreatedAt: in.CreatedAt.Unix(),
		Creator:   in.Creator,
	}
}
