package service

import (
	"context"
	"errors"

	"api-expo/api/common"
	communityV1 "api-expo/api/community/v1"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) AddExpoCommunity(ctx context.Context, in *v1.ExpoCommunity) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.ExpoId == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.Languages == nil && len(in.Languages) == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "社区描述不能为空")
	}

	//en 为必输
	enLang, exists := in.Languages["en"]
	if !exists {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "找不到en语言配置")
	}

	if enLang == nil || enLang.Description == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "描述必须配置")
	}

	if in.Creator == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "创建人不能为空")
	}

	if in.TopicId == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会话题不能为空")
	}

	_, err := s.community.GetTopicName(ctx, &communityV1.UserTopicNameRequest{
		TopicId: in.TopicId,
	})
	if err != nil {
		l.Errorf("community.GetTopicName Err:%v", err)
		return nil, err
	}

	//遍历in.Languages 下面Logo 是否有存在空的，如果存在返回提示当前语言对应的Logo不可为空
	for _, v := range in.Languages {
		if v.Logo == "" {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "Logo存在未传的情况")
		}
	}

	_, err = s.expoCommunity.Add(ctx, &models.ExpoCommunity{
		ExpoId: in.ExpoId,
		Extra: &igorm.CustomValue[*models.ExpoCommunityExtra]{
			V: &models.ExpoCommunityExtra{
				Languages: in.Languages,
			},
		},
		TopicId:     in.TopicId,
		Creator:     in.Creator,
		Description: in.Languages["en"].Description,
	})
	if err != nil {
		l.Errorf("expoCommunity.Add Err: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) GetExpoCommunity(ctx context.Context, in *v1.GetExpoCommunityRequest) (expoCty *v1.ExpoCommunity, err error) {
	l := log.Context(ctx)

	// 参数校验
	if in.ExpoId == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	expoCommunity, err := s.expoCommunity.Get(ctx, in.ExpoId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoCommunity.Get Err: %v", err)
		return nil, err
	}

	if err != nil {
		return &v1.ExpoCommunity{}, nil
	}

	topicName := ""
	if expoCommunity.TopicId != "" {
		topic, err := s.community.GetTopicName(ctx, &communityV1.UserTopicNameRequest{
			TopicId: expoCommunity.TopicId,
		})
		if err != nil {
			l.Errorf("community.GetTopicName Err:%v", err)
		} else {
			topicName = topic.TopicName
		}
	}

	return &v1.ExpoCommunity{
		ExpoId: expoCommunity.ExpoId,
		Languages: func() map[string]*v1.ExpoCommunityLanguage {
			if expoCommunity.Extra == nil || expoCommunity.Extra.V == nil || expoCommunity.Extra.V.Languages == nil {
				return nil
			}
			return expoCommunity.Extra.V.Languages
		}(),
		Enable:    expoCommunity.Enable,
		Creator:   expoCommunity.Creator,
		TopicId:   expoCommunity.TopicId,
		TopicName: topicName,
		CreatedAt: expoCommunity.CreatedAt.Unix(),
		UpdatedAt: expoCommunity.UpdatedAt.Unix(),
	}, nil
}

func (s *Service) UpdateExpoCommunity(ctx context.Context, in *v1.ExpoCommunity) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.ExpoId == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.TopicId == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会话题不能为空")
	}

	if in.Languages != nil && len(in.Languages) > 0 {
		//en 为必输
		enLang, exists := in.Languages["en"]
		if !exists {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "找不到en语言配置")
		}

		if enLang == nil || enLang.Description == "" {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "en 描述必须配置")
		}
	}

	_, err := s.expoCommunity.Update(ctx, &models.ExpoCommunity{
		ExpoId: in.ExpoId,
		Extra: &igorm.CustomValue[*models.ExpoCommunityExtra]{
			V: &models.ExpoCommunityExtra{
				Languages: in.Languages,
			},
		},
		Description: in.Languages["en"].Description,
		TopicId:     in.TopicId,
		Creator:     in.Creator,
		Enable:      in.Enable,
	})
	if err != nil {
		l.Errorf("expoCommunity.Update: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoCommunityEnable(ctx context.Context, in *v1.SetExpoCommunityEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.ExpoId == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	_, err := s.expoCommunity.SetEnable(ctx, in.ExpoId, in.Enable)
	if err != nil {
		l.Errorf("expoCommunity.SetEnable: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) DeleteExpoCommunity(ctx context.Context, in *v1.DeleteExpoCommunityRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.ExpoId == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	err := s.expoCommunity.Delete(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoCommunity.Delete Err: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}
