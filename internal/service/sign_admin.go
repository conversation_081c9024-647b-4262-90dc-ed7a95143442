package service

import (
	"context"
	errors2 "errors"
	"fmt"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gorm.io/gorm"
)

func (s *Service) CreateSignConfig(ctx context.Context, request *v1.CreateSignConfigRequest) (*v1.CreateSignConfigReply, error) {
	if request == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "请求参数不能为空")
	}

	// 验证签到天数
	if request.Day <= 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "签到天数必须大于0")
	}

	// 验证普通奖励
	if request.Reward == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天普通奖励不能为空", request.Day))
	}
	if request.Reward.RewardType != RewardTypeGoldCons {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天仅支持积分奖励", request.Day))
	}
	if request.Reward.GoldCoins <= 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天积分数量必须大于0", request.Day))
	}

	// 验证第一天不能有连续签到奖励
	if request.Day == 1 && request.HasConsecutiveReward {
		return nil, errors.WithMessage(errors.ErrBadRequest, "第1天不允许配置连续签到奖励")
	}

	// 验证连续签到奖励
	if request.HasConsecutiveReward {
		if request.ConsecutiveReward == nil {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天标记有连续签到奖励但奖励内容为空", request.Day))
		}
		if request.ConsecutiveReward.RewardType == "" {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天标记有连续签到奖励但奖励内容为空", request.Day))
		}
		if request.ConsecutiveReward.RewardType != RewardTypeGoldCons {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天连续签到仅支持积分奖励", request.Day))
		}
		if request.ConsecutiveReward.GoldCoins <= 0 {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天连续签到积分数量必须大于0", request.Day))
		}
	}

	if request.HasConsecutiveReward == false && request.ConsecutiveReward != nil {
		request.ConsecutiveReward = nil
	}

	existingConfig, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil && !errors2.Is(err, gorm.ErrRecordNotFound) {
		log.Context(ctx).Errorf("获取签到配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取签到配置失败")
	}

	var configValue models.ConfigValue
	if existingConfig != nil {
		configValue = existingConfig.ConfigValue
		// 检查当前奖励天数
		maxDay := 0
		for _, reward := range configValue.Rewards {
			if reward.Day > maxDay {
				maxDay = reward.Day
			}
			if reward.Day == int(request.Day) {
				return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天的奖励配置已存在", request.Day))
			}
		}
		if int(request.Day) != maxDay+1 {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("只能添加第%d天的奖励配置", maxDay+1))
		}
	} else {
		// 首次添加只能是第1天
		if request.Day != 1 {
			return nil, errors.WithMessage(errors.ErrBadRequest, "首次添加只能是第1天的奖励配置")
		}
		configValue = models.ConfigValue{
			CycleDays: 0,
			Rewards:   []models.ConfigReward{},
		}
	}

	// 构建新的奖励配置
	newReward := models.ConfigReward{
		Day:                  int(request.Day),
		HasConsecutiveReward: request.HasConsecutiveReward,
		Reward: &models.RewardJSON{
			RewardType: request.Reward.RewardType,
			GoldCoins:  request.Reward.GoldCoins,
		},
	}
	if request.HasConsecutiveReward && request.ConsecutiveReward != nil {
		newReward.ConsecutiveReward = &models.RewardJSON{
			RewardType: request.ConsecutiveReward.RewardType,
			GoldCoins:  request.ConsecutiveReward.GoldCoins,
		}
	}
	configValue.Rewards = append(configValue.Rewards, newReward)
	// 自动计算周期天数
	configValue.CycleDays = len(configValue.Rewards)

	config := &models.SignConfig{
		ConfigKey:   models.DefaultSignInConfigKey,
		ConfigValue: configValue,
		Status:      1,
		CycleDays:   int8(configValue.CycleDays),
	}
	if existingConfig != nil {
		config.ID = existingConfig.ID
		err = s.signTaskConfig.Update(ctx, config)
	} else {
		err = s.signTaskConfig.Create(ctx, config)
	}
	if err != nil {
		log.Context(ctx).Errorf("保存签到配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "保存签到配置失败")
	}
	responseConfig := &v1.SignRewardConfig{
		Day:                  request.Day,
		HasConsecutiveReward: request.HasConsecutiveReward,
		Reward:               request.Reward,
		ConsecutiveReward:    request.ConsecutiveReward,
	}
	return &v1.CreateSignConfigReply{
		Config: responseConfig,
	}, nil
}

func (s *Service) DeleteSignConfig(ctx context.Context, request *v1.DeleteSignConfigRequest) (*v1.DeleteSignConfigReply, error) {
	if request == nil || request.Day <= 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "签到天数必须大于0")
	}

	// 获取当前配置
	config, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil {
		log.Context(ctx).Errorf("获取签到配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取签到配置失败")
	}

	if config == nil {
		return nil, errors.WithMessage(errors.ErrInternalServer, "未找到有效的签到配置")
	}
	if len(config.ConfigValue.Rewards) == 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "无可删除的奖励配置")
	}
	// 只能删除最后一天
	maxDay := 0
	for _, reward := range config.ConfigValue.Rewards {
		if reward.Day > maxDay {
			maxDay = reward.Day
		}
	}
	if int(request.Day) != maxDay {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("只能删除最后一天（第%d天）的奖励配置", maxDay))
	}
	// 删除最后一天
	newRewards := make([]models.ConfigReward, 0, len(config.ConfigValue.Rewards)-1)
	for _, reward := range config.ConfigValue.Rewards {
		if reward.Day != int(request.Day) {
			newRewards = append(newRewards, reward)
		}
	}
	// 检查剩余天数是否连续
	//for i, reward := range newRewards {
	//	if reward.Day != i+1 {
	//		return nil, errors.WithMessage(errors.ErrInternalServer, "删除后天数不连续")
	//	}
	//}
	config.ConfigValue.Rewards = newRewards
	config.ConfigValue.CycleDays = len(newRewards)
	config.CycleDays = int8(len(newRewards))
	err = s.signTaskConfig.Update(ctx, config)
	if err != nil {
		log.Context(ctx).Errorf("删除签到配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "删除签到配置失败")
	}
	return &v1.DeleteSignConfigReply{
		Message: fmt.Sprintf("成功删除第%d天的奖励配置", request.Day),
	}, nil
}

func (s *Service) ListSignConfig(ctx context.Context, _ *v1.ListSignConfigRequest) (*v1.ListSignConfigReply, error) {
	// 获取当前配置
	config, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil && !errors2.Is(err, gorm.ErrRecordNotFound) {
		log.Context(ctx).Errorf("获取签到配置列表失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取签到配置列表失败")
	}

	// 如果没有配置，返回空列表
	configs := make([]*v1.SignRewardConfig, 0)
	if config == nil || len(config.ConfigValue.Rewards) == 0 {
		return &v1.ListSignConfigReply{
			Configs: configs,
		}, nil
	}

	// 转换配置格式
	for _, reward := range config.ConfigValue.Rewards {
		pbConfig := &v1.SignRewardConfig{
			Day:                  int32(reward.Day),
			HasConsecutiveReward: reward.HasConsecutiveReward,
			Reward: &v1.Reward{
				RewardType: reward.Reward.RewardType,
				GoldCoins:  reward.Reward.GoldCoins,
			},
		}

		if reward.HasConsecutiveReward && reward.ConsecutiveReward != nil {
			pbConfig.ConsecutiveReward = &v1.Reward{
				RewardType: reward.ConsecutiveReward.RewardType,
				GoldCoins:  reward.ConsecutiveReward.GoldCoins,
			}
		}

		configs = append(configs, pbConfig)
	}

	return &v1.ListSignConfigReply{
		Configs: configs,
	}, nil
}

func (s *Service) UpdateSignConfig(ctx context.Context, request *v1.UpdateSignConfigRequest) (*v1.UpdateSignConfigReply, error) {
	if request == nil || request.Day <= 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "签到天数必须大于0")
	}

	// 验证普通奖励
	if request.Reward == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天普通奖励不能为空", request.Day))
	}
	if request.Reward.RewardType != RewardTypeGoldCons {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天仅支持积分奖励", request.Day))
	}
	if request.Reward.GoldCoins <= 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天积分数量必须大于0", request.Day))
	}

	// 验证第一天不能有连续签到奖励
	if request.Day == 1 && request.HasConsecutiveReward {
		return nil, errors.WithMessage(errors.ErrBadRequest, "第1天不允许配置连续签到奖励")
	}

	// 验证连续签到奖励
	if request.HasConsecutiveReward {
		if request.ConsecutiveReward == nil {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天标记有连续签到奖励但奖励内容为空", request.Day))
		}
		if request.ConsecutiveReward.RewardType == "" {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天标记有连续签到奖励但奖励内容为空", request.Day))
		}
		if request.ConsecutiveReward.RewardType != RewardTypeGoldCons {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天连续签到仅支持积分奖励", request.Day))
		}
		if request.ConsecutiveReward.GoldCoins <= 0 {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天连续签到积分数量必须大于0", request.Day))
		}
	}

	// 设置HasConsecutiveReward为false时，不能设置连续签到奖励
	if request.HasConsecutiveReward == false && request.ConsecutiveReward != nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("第%d天标记为无连续签到奖励，但设置了奖励", request.Day))
	}

	// 获取当前配置
	config, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil {
		log.Context(ctx).Errorf("获取签到配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取签到配置失败")
	}

	if config == nil {
		return nil, errors.WithMessage(errors.ErrInternalServer, "未找到有效的签到配置")
	}

	// 检查天数是否超出周期
	if int(request.Day) > config.ConfigValue.CycleDays {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("签到天数不能超过周期天数%d", config.ConfigValue.CycleDays))
	}

	// 查找并更新指定天数的奖励配置
	found := false
	for i, reward := range config.ConfigValue.Rewards {
		if reward.Day == int(request.Day) {
			// 更新配置
			config.ConfigValue.Rewards[i].HasConsecutiveReward = request.HasConsecutiveReward
			config.ConfigValue.Rewards[i].Reward = &models.RewardJSON{
				RewardType: request.Reward.RewardType,
				GoldCoins:  request.Reward.GoldCoins,
			}

			if request.HasConsecutiveReward && request.ConsecutiveReward != nil {
				config.ConfigValue.Rewards[i].ConsecutiveReward = &models.RewardJSON{
					RewardType: request.ConsecutiveReward.RewardType,
					GoldCoins:  request.ConsecutiveReward.GoldCoins,
				}
			} else {
				config.ConfigValue.Rewards[i].ConsecutiveReward = nil
			}
			found = true
			break
		}
	}

	if !found {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("未找到第%d天的奖励配置", request.Day))
	}

	// 保存更新
	err = s.signTaskConfig.Update(ctx, config)
	if err != nil {
		log.Context(ctx).Errorf("更新签到配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "更新签到配置失败")
	}

	// 构建响应
	responseConfig := &v1.SignRewardConfig{
		Day:                  request.Day,
		HasConsecutiveReward: request.HasConsecutiveReward,
		Reward:               request.Reward,
		ConsecutiveReward:    request.ConsecutiveReward,
	}

	return &v1.UpdateSignConfigReply{
		Config: responseConfig,
	}, nil
}
