package service

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"unicode/utf8"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/country"
	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) checkAddress(languageCode string, in *v1.Address) error {
	if in.CountryCodeIso == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "invalid country code")
	}

	if utf8.RuneCountInString(in.CountryName) > 20 {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61908", languageCode)) // 国家名错误
	}

	_, exists := country.GetCountryByCode(in.CountryCodeIso)
	if !exists {
		return innErr.WithMessage(innErr.ErrBadRequest, "invalid country code")
	}

	if in.UserName == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62038", languageCode)) // 请输入用户名
	}

	userNameCount := utf8.RuneCountInString(in.UserName)
	if userNameCount < 2 || userNameCount > 50 {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62039", languageCode)) // 用户名错误
	}

	if !validateInternationalPhoneNumber(in.Phone) {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61910", languageCode)) // 手机号错误
	}

	if in.CityName == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61911", languageCode)) // 城市名错误
	}

	if utf8.RuneCountInString(in.CityName) > 20 {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61911", languageCode)) // 城市名错误
	}

	if in.StreetAddress == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("62040", languageCode)) // 请输入街道地址
	}

	if in.PostalCode != "" {
		postalCodeCount := utf8.RuneCountInString(in.PostalCode)
		if postalCodeCount < 3 || postalCodeCount > 10 {
			return innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("61913", languageCode)) // 邮政编码错误
		}
	}
	return nil
}

func (s *Service) GetCountryList(ctx context.Context, _ *common.EmptyRequest) (*v1.GetCountryListReply, error) {
	var (
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		l               = log.Context(ctx)
	)

	countryTranslations, err := s.countryTranslate.FindCountryTranslate(ctx, languageCode)
	if err != nil {
		l.Errorf("countryTranslate.FindCountryTranslate Err:%v", err)
		return nil, err
	}

	// 使用英文兜底
	if len(countryTranslations) <= 0 && languageCode != "en" {
		countryTranslations, err = s.countryTranslate.FindCountryTranslate(ctx, "en")
		if err != nil {
			l.Errorf("countryTranslate.FindCountryTranslate Err:%v", err)
			return nil, err
		}
	}

	items := make([]*v1.CountryInfo, 0, len(countryTranslations))
	for _, value := range countryTranslations {
		items = append(items, &v1.CountryInfo{
			Code: value.CountryCode,
			Name: value.CountryName,
		})
	}
	return &v1.GetCountryListReply{
		Items: items,
	}, nil
}

func (s *Service) GetAddressList(ctx context.Context, _ *common.EmptyRequest) (*v1.GetAddressListReply, error) {
	var (
		userId, _ = icontext.UserIdFrom(ctx)
		l         = log.Context(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	address, err := s.address.FindByUserId(ctx, userId)
	if err != nil {
		l.Errorf("FindByUserId Err:%v", err)
		return nil, err
	}

	items := make([]*v1.Address, 0, len(address))
	for _, value := range address {
		items = append(items, s.addressToGRPC(value))
	}
	return &v1.GetAddressListReply{
		Items: items,
	}, nil
}

func (s *Service) AddAddress(ctx context.Context, in *v1.Address) (*v1.AddAddressReply, error) {
	var (
		userId, _       = icontext.UserIdFrom(ctx)
		l               = log.Context(ctx)
		err             error
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	err = s.checkAddress(languageCode, in)
	if err != nil {
		return nil, err
	}

	tx := s.address.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	if in.IsDefault {
		err = s.address.CancelDefault(ctx, userId, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("CancelDefault Err:%v", err)
			return nil, err
		}
	}

	var newId uint
	newId, err = s.address.Add(ctx, &models.Address{
		UserID:              userId,
		CountryCode:         in.CountryCodeIso,
		CountryName:         in.CountryName,
		UserName:            in.UserName,
		PhoneCountryCode:    in.PhoneCountryCode,
		PhoneAreaCode:       in.PhoneAreaCode,
		Phone:               in.Phone,
		ProvinceName:        in.ProvinceName,
		CityName:            in.CityName,
		StreetAddress:       in.StreetAddress,
		BuildingUnitAddress: in.BuildingUnitAddress,
		PostalCode:          in.PostalCode,
		IsDefault:           in.IsDefault,
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("address.Add Err:%v", err)
		return nil, err
	}

	return &v1.AddAddressReply{
		Id:          int32(newId),
		AddressShow: fmt.Sprintf("%s %s %s %s %s", in.CountryName, in.ProvinceName, in.CityName, in.StreetAddress, in.BuildingUnitAddress),
	}, nil
}

func (s *Service) UpdateAddress(ctx context.Context, in *v1.Address) (*common.EmptyReply, error) {
	var (
		userId, _       = icontext.UserIdFrom(ctx)
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	err := s.checkAddress(languageCode, in)
	if err != nil {
		return nil, err
	}

	tx := s.address.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	if in.IsDefault {
		err = s.address.CancelDefault(ctx, userId, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("CancelDefault Err:%v", err)
			return nil, err
		}
	}

	err = s.address.Update(ctx, &models.Address{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		UserID:              userId,
		CountryCode:         in.CountryCodeIso,
		CountryName:         in.CountryName,
		UserName:            in.UserName,
		PhoneCountryCode:    in.PhoneCountryCode,
		PhoneAreaCode:       in.PhoneAreaCode,
		Phone:               in.Phone,
		ProvinceName:        in.ProvinceName,
		CityName:            in.CityName,
		StreetAddress:       in.StreetAddress,
		BuildingUnitAddress: in.BuildingUnitAddress,
		PostalCode:          in.PostalCode,
		IsDefault:           in.IsDefault,
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("address.Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) GetAddressDetail(ctx context.Context, in *v1.GetAddressDetailRequest) (*v1.Address, error) {
	var (
		userId, _ = icontext.UserIdFrom(ctx)
		l         = log.Context(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	address, err := s.address.Get(ctx, uint(in.Id), userId)
	if err != nil {
		l.Errorf("address.Get Err:%v", err)
		return nil, err
	}

	return s.addressToGRPC(address), nil
}

func (s *Service) SetAddressDefault(ctx context.Context, in *v1.SetAddressDefaultRequest) (*common.EmptyReply, error) {
	var (
		userId, _ = icontext.UserIdFrom(ctx)
		l         = log.Context(ctx)
		err       error
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	tx := s.address.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	err = s.address.CancelDefault(ctx, userId, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("address.CancelDefault Err:%v", err)
		return nil, err
	}

	err = s.address.SetDefault(ctx, uint(in.Id), userId, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("address.SetDefault Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) DeleteAddress(ctx context.Context, in *v1.DeleteAddressRequest) (*common.EmptyReply, error) {
	var (
		userId, _ = icontext.UserIdFrom(ctx)
		l         = log.Context(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	err := s.address.Delete(ctx, uint(in.Id), userId)
	if err != nil {
		l.Errorf("address.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) GetAddressCount(ctx context.Context, _ *common.EmptyRequest) (*v1.GetAddressCountReply, error) {
	var (
		userId, _ = icontext.UserIdFrom(ctx)
		l         = log.Context(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	count, err := s.address.Count(ctx, userId)
	if err != nil {
		l.Errorf("address.Count Err:%v", err)
		return nil, err
	}
	return &v1.GetAddressCountReply{
		Count: int32(count),
	}, nil
}

func (s *Service) GetOrderAddress(ctx context.Context, _ *common.EmptyRequest) (*v1.Address, error) {
	var (
		l         = log.Context(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	userAddress, err := s.address.GetDefault(ctx, userId)
	// 这里发生错误不影响主流程
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("address.GetDefault Err:%v", err)
	}

	// 如果没有默认的，获取最后一条
	if userAddress == nil {
		userAddress, err = s.address.GetUserLast(ctx, userId)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("address.GetUserLast Err:%v", err)
		}
	}
	return s.addressToGRPC(userAddress), nil
}

func (s *Service) addressToGRPC(in *models.Address) *v1.Address {
	if in == nil {
		return &v1.Address{}
	}

	return &v1.Address{
		Id:                  int32(in.ID),
		CountryCodeIso:      in.CountryCode,
		CountryName:         in.CountryName,
		UserName:            in.UserName,
		PhoneCountryCode:    in.PhoneCountryCode,
		PhoneAreaCode:       in.PhoneAreaCode,
		Phone:               in.Phone,
		ProvinceName:        in.ProvinceName,
		CityName:            in.CityName,
		StreetAddress:       in.StreetAddress,
		BuildingUnitAddress: in.BuildingUnitAddress,
		PostalCode:          in.PostalCode,
		IsDefault:           in.IsDefault,
		AddressShow:         fmt.Sprintf("%s %s %s %s %s", in.CountryName, in.ProvinceName, in.CityName, in.StreetAddress, in.BuildingUnitAddress),
	}
}

func validateInternationalPhoneNumber(phone string) bool {
	pattern := `^\d{6,20}$`
	matched, err := regexp.MatchString(pattern, phone)
	return err == nil && matched
}
