package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/id"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/recovery"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) orderFromVIP(c *kafka.Consumer, message *kafka.Message) {
	defer c.CommitMessage(message)

	var (
		ctx   = context.Background()
		reqId = reqid.GenRequestID()
		data  VIPOrder
	)

	ctx = icontext.WithRequestId(ctx, reqId)
	l := log.Context(ctx)
	defer recovery.CatchGoroutinePanicWithContext(ctx)

	err := json.Unmarshal(message.Value, &data)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("VIP Unmarshal<%s> Err:%v", string(message.Value), err))
		return
	}

	if !data.Commit && data.Type != "bootstrap-insert" {
		return
	}

	var (
		order   = data.Data
		orderNo = order.OrderId
	)

	goods, err := s.vip.GetProduct(ctx, orderNo)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("VIP 订单【%s】获取商品出错：%v", orderNo, err))
		return
	}

	translates, err := s.vip.FindProductTranslate(ctx, goods.OrderProductId)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("VIP 订单【%s】获取商品【%s】多语言出错：%v", orderNo, goods.OrderProductId, err))
		return
	}

	var (
		goodsNameTranslate = make(map[string]*v1.GoodsTranslate, len(translates))
		specDescTranslate  = make(map[string]string, len(translates))
		paymentMethod      = v1.PaymentMethod_GOLD
		goodsNameChinese   = ""
		specDescChinese    = ""
		displayAmount      = strconv.Itoa(order.ActualCoins)
		totalAmount        = float32(order.ActualCoins)
		price              = float32(goods.Coins)
		paidTotal          float32
	)

	for _, translate := range translates {
		var (
			languageCode = strings.ToLower(translate.LanguageCode)
			goodsName    = translate.TranslateContent.DurationTag
			specDesc     = translate.TranslateContent.Slogan
		)

		goodsNameTranslate[languageCode] = &v1.GoodsTranslate{
			Name: goodsName,
		}
		specDescTranslate[languageCode] = specDesc

		if languageCode == "zh-hk" {
			goodsNameTranslate["zh"] = &v1.GoodsTranslate{
				Name: goodsName,
			}
			specDescTranslate["zh"] = specDesc
		}

		if languageCode == "zh-cn" {
			goodsNameChinese = goodsName
			specDescChinese = specDesc
		}
	}

	// 现金支付
	if order.CostType == 1 {
		displayAmount = strings.Replace(order.Placeholder, "{0}", fmt.Sprintf("%.2f", order.CostPrice), -1)
		paymentMethod = v1.PaymentMethod_PAYMENT_METHOD_UNKNOWN
		totalAmount = order.CostPrice
		price = order.CostPrice
	}

	var (
		goodsId        = goods.OrderProductId
		operationNo    = order.GoldOrderNumber
		paymentNo      = id.GenerateId(id.BusinessPayment, id.WithChannel(strconv.Itoa(int(paymentMethod))))
		paymentStatus  = v1.PaymentStatus_PaymentStatusUNPAY
		orderStatus    = v1.OrderStatus_UNPAY
		quantity       = int32(1)
		goodsTranslate = &igorm.CustomValue[map[string]*v1.GoodsTranslate]{
			V: goodsNameTranslate,
		}
		orderExtra = &igorm.CustomValue[*models.OrderExtra]{
			V: &models.OrderExtra{
				DisplayAmount: displayAmount,
				SpecDesc:      specDescTranslate,
			},
		}
		paidTime      time.Time
		createTime, _ = time.ParseInLocation(time.DateTime, order.CreatedDate, time.UTC)
	)

	// 支付完成
	if order.OrderStatus == 2 {
		orderStatus = v1.OrderStatus_COMPLETE
		paymentStatus = v1.PaymentStatus_PaymentStatusPAID
		paidTime, _ = time.ParseInLocation(time.DateTime, order.FinishedTime, time.UTC)
		paidTotal = float32(order.ActualCoins)
		if order.CostType == 1 {
			paidTotal = order.CostPrice
		}
	}

	// 待支付订单不同步
	if orderStatus == v1.OrderStatus_UNPAY {
		return
	}

	switch data.Type {
	case "update":
		var (
			payment *models.Payment
			insert  bool
		)

		_, err = s.order.GetByOrderNo(ctx, orderNo)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("order.GetByOrderNo<%s> Err:%v", orderNo, err)
			return
		}

		// 到这里说明订单不存在，可能是先消费了更新；所以需要走insert逻辑
		if err != nil {
			insert = true
		}

		if !insert {
			payment, err = s.payment.GetByOrderNo(ctx, orderNo)
			if err != nil {
				l.Errorf("payment.GetByOrderNo<%s> Err:%v", orderNo, err)
				return
			}

			var orderExtraValue any
			orderExtraValue, err = orderExtra.Value()
			if err != nil {
				l.Errorf("orderExtra.Value Err:%v", err)
				return
			}

			tx := s.goods.Begin()
			defer func() {
				if err == nil {
					tx.Commit()
				} else {
					tx.Rollback()
				}
			}()

			// 4、更新支付单信息
			err = s.payment.UpdatePayment(ctx, &models.Payment{
				PaymentNo:   payment.PaymentNo,
				OperationNo: operationNo,
				PaidTotal:   totalAmount,
				PaidTime:    paidTime,
				Status:      paymentStatus,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("payment.UpdatePayment Err:%v", err)
				return
			}

			// 5、更新支付订单
			err = s.order.Update(ctx, orderNo, map[string]interface{}{
				"status":          orderStatus,
				"total_amount":    totalAmount,
				"extra":           orderExtraValue,
				"currency_symbol": order.CurrencySymbol,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("order.UpdateStatus Err:%v", err)
				return
			}
			return
		}
		fallthrough
	case "bootstrap-insert", "insert":
		var (
			goodsVersion  = time.Now().Unix()
			goodsSnapshot *models.GoodsSnapshot
		)

		goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
			return
		}

		if err != nil {
			// 这里没有更新时间；所以只能每次添加新的
			goodsSnapshot = &models.GoodsSnapshot{
				GoodsId: goodsId,
				Version: goodsVersion,
				Snapshot: &igorm.CustomValue[*models.Snapshot]{
					V: &models.Snapshot{
						Goods: &models.Goods{
							Category:      v1.GoodsCategory_GOODS_CATEGORY_VIP,
							GoodsId:       goodsId,
							Name:          goodsNameChinese,
							BasePrice:     totalAmount,
							UseBasePrice:  true,
							SelectedPrice: totalAmount,
							Status:        v1.GoodsStatus_GoodsStatusOn,
							FreeShipping:  true,
							Image: &igorm.CustomValue[*v1.Image]{
								V: vipStaticImage,
							},
							Translate: goodsTranslate,
						},
					},
				},
			}

			err = s.goodsSnapshot.Add(ctx, goodsSnapshot)
			if err != nil && !errors.Is(err, ormhelper.ErrDuplicateKey) {
				l.Errorf("goodsSnapshot.Add Err:%v", err)
				return
			}

			if err != nil {
				goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
				if err != nil {
					l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
					return
				}
			}
		}

		var (
			newOrder = &models.Order{
				OrderNo:        orderNo,
				Source:         v1.OrderSource_VIP,
				PaymentMethod:  paymentMethod,
				Platform:       v1.Platform_PlatformUNKNOWN, // 这里没有平台信息
				UserId:         order.UserId,
				CountryCode:    order.CurrencyCode,
				Address:        &igorm.CustomValue[*models.Address]{},
				Quantity:       quantity,
				CurrencySymbol: order.CurrencySymbol,
				TotalAmount:    totalAmount,
				Status:         orderStatus,
				GoodsName:      goodsNameChinese,
				Extra:          orderExtra,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}

			newOrderItems = []*models.OrderItem{
				{
					OrderNo:         orderNo,
					GoodsId:         goodsId,
					Price:           price,
					PriceUnit:       order.CurrencySymbol,
					GoodsSnapshotId: goodsSnapshot.ID,
					Quantity:        quantity,
					TotalAmount:     totalAmount,
					GoodsName:       goodsNameChinese,
					SpecDesc:        specDescChinese,
					Model: gorm.Model{
						CreatedAt: createTime,
						UpdatedAt: createTime,
					},
				},
			}

			newPayment = &models.Payment{
				PaymentNo:   paymentNo,
				OrderNo:     orderNo,
				OperationNo: operationNo,
				TotalAmount: totalAmount,
				PaidTotal:   paidTotal,
				PaidMethod:  paymentMethod,
				Status:      paymentStatus,
				PaidTime:    paidTime,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}
		)

		tx := s.goods.Begin()
		defer func() {
			if err == nil {
				tx.Commit()
			} else {
				tx.Rollback()
			}
		}()

		// 1、添加订单
		err = s.order.Add(ctx, newOrder, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("order.Add Err:%v", err)
			return
		}

		// 3、添加订单项
		err = s.orderItem.BatchAdd(ctx, newOrderItems, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("orderItem.BatchAdd Err:%v", err)
			return
		}

		// 4、添加支付
		err = s.payment.Add(ctx, newPayment, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("payment.Add Err:%v", err)
			return
		}
	}
}
