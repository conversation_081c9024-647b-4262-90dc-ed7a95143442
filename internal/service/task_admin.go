package service

import (
	"context"
	"fmt"
	"strings"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
	userCenterv1 "gold_store/api/user_center/v1"
	"gold_store/internal/dao"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
)

const GoldIconDefaultRewardIcon = "/fissionactivity/638876508665894071/FAM638876508665894071_841220.png"

// 不可删除的任务类型列表 - 这些是系统核心任务
var nonDeletableTaskTypes = []models.TaskSubType{
	models.TaskSubTypeOpenVPS,
	models.TaskSubTypeBindRealAccount,
	models.TaskSubTypeVerifyIdentity,
	models.TaskSubTypeFollowWikiFX,
	models.TaskSubTypeModifyUsername,
}

// ListTask 查询任务列表
func (s *Service) ListTask(ctx context.Context, req *v1.AdminListTaskRequest) (*v1.AdminListTaskReply, error) {
	queryParams := &dao.TaskConfigQueryParams{
		PageNum:  int64(req.Page),
		PageSize: int64(req.PageSize),
		Status:   int8(req.Status),
		TaskType: models.TaskType(req.TaskType),
	}

	taskConfigs, total, err := s.taskConfig.List(ctx, queryParams)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrInternalServer, "查询任务列表失败")
	}

	// 收集所有商品ID
	goodsIDs := make([]string, 0)
	for _, config := range taskConfigs {
		if config.RewardConfigObj.RewardType == models.RewardTypePhysical && config.RewardConfigObj.Goods != nil {
			goodsIDs = append(goodsIDs, config.RewardConfigObj.Goods.GoodsID)
		} else if config.RewardConfigObj.RewardType == models.RewardTypeVirtual && config.RewardConfigObj.VirtualGoods != nil {
			goodsIDs = append(goodsIDs, config.RewardConfigObj.VirtualGoods.GoodsID)
		}
	}
	goodsInfoMap, err := s.fetchGoodsInfoBatch(ctx, goodsIDs)
	if err != nil {
		log.Context(ctx).Warnf("批量获取商品信息失败: %v", err)
		goodsInfoMap = make(map[string]*GoodsInfo) // 使用空map继续处理
	}

	taskInfos := make([]*v1.AdminTaskInfo, 0, len(taskConfigs))
	for _, config := range taskConfigs {
		taskInfo := s.convertToAdminTaskInfo(ctx, config, goodsInfoMap)
		taskInfos = append(taskInfos, taskInfo)
	}

	return &v1.AdminListTaskReply{
		TaskList: taskInfos,
		Total:    fmt.Sprintf("%d", total),
		Page:     fmt.Sprintf("%d", req.Page),
	}, nil
}

// GetTaskDetail 任务详情
func (s *Service) GetTaskDetail(ctx context.Context, req *v1.AdminGetTaskDetailRequest) (*v1.AdminGetTaskDetailReply, error) {
	// 1. 根据ID获取任务配置
	taskConfig, err := s.taskConfig.GetTaskConfigByID(ctx, req.TaskId)
	if err != nil {
		log.Context(ctx).Errorf("获取任务配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取任务配置失败")
	}

	// 2. 验证任务是否存在
	if taskConfig == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("未找到任务: %d", req.TaskId))
	}

	// 收集商品ID
	goodsIDs := make([]string, 0)
	if taskConfig.RewardConfigObj.RewardType == models.RewardTypePhysical && taskConfig.RewardConfigObj.Goods != nil {
		goodsIDs = append(goodsIDs, taskConfig.RewardConfigObj.Goods.GoodsID)
	} else if taskConfig.RewardConfigObj.RewardType == models.RewardTypeVirtual && taskConfig.RewardConfigObj.VirtualGoods != nil {
		goodsIDs = append(goodsIDs, taskConfig.RewardConfigObj.VirtualGoods.GoodsID)
	}
	goodsInfoMap, err := s.fetchGoodsInfoBatch(ctx, goodsIDs)
	if err != nil {
		log.Context(ctx).Warnf("批量获取商品信息失败: %v", err)
		goodsInfoMap = make(map[string]*GoodsInfo) // 使用空map继续处理
	}

	// 3. 转换任务配置为API响应格式
	taskInfo := s.convertToAdminTaskInfo(ctx, taskConfig, goodsInfoMap)

	// 4. 返回任务详情
	return &v1.AdminGetTaskDetailReply{
		TaskInfo: taskInfo,
	}, nil
}

// convertToAdminTaskInfo 将TaskConfig转换为AdminTaskInfo，商品信息从goodsInfoMap获取
func (s *Service) convertToAdminTaskInfo(_ context.Context, config *models.TaskConfig, goodsInfoMap map[string]*GoodsInfo) *v1.AdminTaskInfo {
	taskInfo := &v1.AdminTaskInfo{
		TaskId:        int64(config.ID),
		Status:        int32(config.Status),
		TaskTitle:     config.TaskTitle,
		ShowProject:   config.ShowProject,
		MinVersion:    config.MinVersion,
		VisibleUsers:  config.VisibleUsers,
		TaskIcon:      config.TaskIcon,
		RewardIcon:    config.RewardIcon,
		CompleteTimes: int32(config.CompleteTimes),
		Modifier:      config.Modifier,
		SortOrder:     int32(config.SortOrder),
		TargetCode:    config.TaskConfigObj.TargetCode,
		TimeLimit:     config.TaskConfigObj.TimeLimit,
		ConfigVersion: config.ConfigVersion,
		RewardType:    int32(config.RewardType),
		TaskType:      int32(config.TaskType),
		TaskCondition: models.TaskSubType(config.TaskEnumCode).GetTaskCondition(),
		UpdateTime:    config.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 处理奖励信息
	if config.RewardConfigObj.RewardType != 0 {
		rewardInfo := &v1.RewardInfo{
			GoldCoins: int32(config.RewardConfigObj.GoldCoins),
		}
		if config.RewardConfigObj.RewardType == models.RewardTypePhysical && config.RewardConfigObj.Goods != nil {
			goodsID := config.RewardConfigObj.Goods.GoodsID
			goodsName := ""
			if goods, ok := goodsInfoMap[goodsID]; ok {
				goodsName = goods.GoodsName
			}
			rewardInfo.Goods = &v1.GoodsReward{
				GoodsId:      goodsID,
				FreeShipping: config.RewardConfigObj.Goods.FreeShipping,
				GoodsIcon:    config.RewardConfigObj.Goods.GoodsIcon,
				GoodsName:    goodsName,
			}
		} else if config.RewardConfigObj.RewardType == models.RewardTypeVirtual && config.RewardConfigObj.VirtualGoods != nil {
			goodsID := config.RewardConfigObj.VirtualGoods.GoodsID
			goodsName := ""
			if goods, ok := goodsInfoMap[goodsID]; ok {
				goodsName = goods.GoodsName
			}
			rewardInfo.VirtualGoods = &v1.VirtualGoodsReward{
				GoodsId:   goodsID,
				GoodsIcon: config.RewardConfigObj.VirtualGoods.GoodsIcon,
				GoodsName: goodsName,
			}
		}
		taskInfo.RewardInfo = rewardInfo
	}

	// 处理多语言配置
	if len(config.I18nObj) > 0 {
		i18nMap := make(map[string]*v1.I18NConfig)
		for lang, langInfo := range config.I18nObj {
			i18nMap[lang] = &v1.I18NConfig{
				TaskDesc:  langInfo.TaskDesc,
				TaskTitle: langInfo.TaskTitle,
			}
		}
		taskInfo.I18N = i18nMap
		if zhInfo, ok := config.I18nObj["zh-cn"]; ok {
			taskInfo.TaskDesc = zhInfo.TaskDesc
			taskInfo.TaskTitle = zhInfo.TaskTitle
		}
	}
	return taskInfo
}

// UpdateTaskStatus 修改任务状态，发布
func (s *Service) UpdateTaskStatus(ctx context.Context, req *v1.AdminUpdateTaskStatusRequest) (*common.EmptyReply, error) {
	// 1. 验证任务ID是否有效
	taskConfig, err := s.taskConfig.GetTaskConfigByID(ctx, req.TaskId)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取任务配置失败")
	}

	if taskConfig == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("未找到任务: %d", req.TaskId))
	}

	// 2. 验证状态是否有效
	status := int8(req.Status)
	if status != models.TaskStatusEnumOngoing && status != models.TaskStatusEnumNone {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("无效的任务状态: %d", req.Status))
	}
	if req.Modifier == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "填写修改人")
	}

	// 3. 更新任务状态
	err = s.taskConfig.UpdateStatus(ctx, uint(req.TaskId), status, req.Modifier)
	if err != nil {
		log.Context(ctx).Errorf("更新任务状态失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "更新任务状态失败")
	}

	log.Context(ctx).Infof("成功更新任务状态, 任务ID: %d, 新状态: %d, 操作人: %s",
		req.TaskId, req.Status, req.Modifier)
	return &common.EmptyReply{}, nil
}

// UpdateTaskConfig 修改任务信息
func (s *Service) UpdateTaskConfig(ctx context.Context, req *v1.AdminUpdateTaskConfigRequest) (*common.EmptyReply, error) {
	// 1. 获取当前任务配置
	existingTask, err := s.taskConfig.GetTaskConfigByID(ctx, req.TaskInfo.TaskId)
	if err != nil {
		log.Context(ctx).Errorf("获取任务配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取任务配置失败")
	}
	if existingTask == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "任务不存在")
	}

	// 验证任务完成次数
	if req.TaskInfo.CompleteTimes > 0 {
		existingTask.CompleteTimes = int(req.TaskInfo.CompleteTimes)
	}

	// 更新其他基础信息
	if req.TaskInfo.ShowProject != "" {
		existingTask.ShowProject = req.TaskInfo.ShowProject
	}

	if req.TaskInfo.MinVersion != "" {
		existingTask.MinVersion = req.TaskInfo.MinVersion
	}

	if req.TaskInfo.VisibleUsers != "" {
		existingTask.VisibleUsers = req.TaskInfo.VisibleUsers
	}

	if req.TaskInfo.TaskIcon != "" {
		existingTask.TaskIcon = req.TaskInfo.TaskIcon
	}

	rewardIcon := req.TaskInfo.RewardIcon
	if rewardIcon == "" {
		// 只在奖励类型发生变化时应用默认图标逻辑
		if req.TaskInfo.RewardType > 0 && int32(existingTask.RewardType) != req.TaskInfo.RewardType {
			if req.TaskInfo.RewardType == int32(models.RewardTypeGoldCoin) {
				rewardIcon = GoldIconDefaultRewardIcon
			} else {
				return nil, errors.WithMessage(errors.ErrBadRequest, "奖励图标不能为空")
			}
		}
	}
	if rewardIcon != "" {
		existingTask.RewardIcon = rewardIcon
	}

	// 5. 更新新增字段
	if req.TaskInfo.SortOrder > 0 {
		existingTask.SortOrder = int(req.TaskInfo.SortOrder)
	}
	// 不需要判断 nil，直接赋值
	existingTask.TaskConfigObj = models.TaskConfigData{}

	// 赋值字段
	existingTask.TaskConfigObj.TargetCode = req.TaskInfo.TargetCode
	existingTask.TaskConfigObj.TimeLimit = req.TaskInfo.TimeLimit

	if req.TaskInfo.ConfigVersion != "" {
		existingTask.ConfigVersion = req.TaskInfo.ConfigVersion
	}

	if req.TaskInfo.TaskTitle != "" {
		existingTask.TaskTitle = req.TaskInfo.TaskTitle
	}

	if int8(req.TaskInfo.Status) != existingTask.Status {
		// 验证状态是否有效
		if req.TaskInfo.Status != int32(models.TaskStatusEnumOngoing) && req.TaskInfo.Status != int32(models.TaskStatusEnumNone) {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("无效的任务状态: %d", req.TaskInfo.Status))
		}
		existingTask.Status = int8(req.TaskInfo.Status)
	}
	// 6. 更新奖励配置
	if req.TaskInfo.RewardInfo != nil {
		rewardDetail := models.RewardDetail{
			RewardType: models.RewardTypeGoldCoin, // 默认积分奖励
			GoldCoins:  int(req.TaskInfo.RewardInfo.GoldCoins),
		}

		// 优先使用传入的RewardType，如果没有传入则根据商品信息推断
		if req.TaskInfo.RewardType > 0 {
			rewardDetail.RewardType = models.RewardType(req.TaskInfo.RewardType)
		}

		// 根据奖励类型设置详细信息
		if rewardDetail.RewardType == models.RewardTypePhysical || req.TaskInfo.RewardInfo.Goods != nil {
			rewardDetail.RewardType = models.RewardTypePhysical
			if req.TaskInfo.RewardInfo.Goods == nil || req.TaskInfo.RewardInfo.Goods.GoodsId == "" {
				return nil, errors.WithMessage(errors.ErrBadRequest, "实物奖励必须提供商品信息")
			}
			rewardDetail.Goods = &models.GoodsDetail{
				GoodsID:      req.TaskInfo.RewardInfo.Goods.GoodsId,
				GoodsIcon:    req.TaskInfo.RewardInfo.Goods.GoodsIcon,
				FreeShipping: req.TaskInfo.RewardInfo.Goods.FreeShipping,
			}
		} else if rewardDetail.RewardType == models.RewardTypeVirtual || req.TaskInfo.RewardInfo.VirtualGoods != nil {
			rewardDetail.RewardType = models.RewardTypeVirtual
			if req.TaskInfo.RewardInfo.VirtualGoods == nil || req.TaskInfo.RewardInfo.VirtualGoods.GoodsId == "" {
				return nil, errors.WithMessage(errors.ErrBadRequest, "虚拟奖励必须提供商品信息")
			}
			rewardDetail.VirtualGoods = &models.VirtualGoods{
				GoodsID:   req.TaskInfo.RewardInfo.VirtualGoods.GoodsId,
				GoodsIcon: req.TaskInfo.RewardInfo.VirtualGoods.GoodsIcon,
			}
		}

		existingTask.RewardType = rewardDetail.RewardType
		existingTask.RewardConfigObj = rewardDetail
	}

	// 6. 更新多语言配置
	if len(req.TaskInfo.I18N) > 0 {
		i18nData := make(models.I18nData)
		// 添加新的多语
		for lang, langInfo := range req.TaskInfo.I18N {
			if langInfo.TaskDesc == "" && langInfo.TaskTitle == "" {
				log.Context(ctx).Warnf("跳过空语言配置: %s", lang)
				continue // 跳过空的语言配置
			}
			i18nData[lang] = models.LanguageInfo{
				TaskDesc:  langInfo.TaskDesc,
				TaskTitle: langInfo.TaskTitle,
			}
		}

		existingTask.I18nObj = i18nData
	}

	if req.TaskInfo.TaskDesc != "" || req.TaskInfo.TaskCondition != "" {
		// 如果没有提供多语言配置但更新了任务描述或条件
		if existingTask.I18nObj == nil {
			existingTask.I18nObj = make(map[string]models.LanguageInfo)
		}
		if _, ok := existingTask.I18nObj["zh-cn"]; !ok {
			existingTask.I18nObj["zh-cn"] = models.LanguageInfo{
				TaskDesc:  req.TaskInfo.TaskDesc,
				TaskTitle: req.TaskInfo.TaskTitle,
			}
		}
	}

	// 7. 更新修改人
	if req.TaskInfo.Modifier != "" {
		existingTask.Modifier = req.TaskInfo.Modifier
	}

	// 8. 保存更新
	err = s.taskConfig.Update(ctx, existingTask)
	if err != nil {
		log.Context(ctx).Errorf("更新任务配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "更新任务配置失败")
	}

	log.Context(ctx).Infof("成功更新任务配置, 任务ID: %d, 操作人: %s", req.TaskInfo.TaskId, req.TaskInfo.Modifier)
	return &common.EmptyReply{}, nil
}

// DeleteTask 删除任务
func (s *Service) DeleteTask(ctx context.Context, req *v1.AdminDeleteTaskRequest) (*common.EmptyReply, error) {
	// 1. 验证任务ID是否有效
	taskConfig, err := s.taskConfig.GetTaskConfigByID(ctx, req.TaskId)
	if err != nil {
		log.Context(ctx).Errorf("获取任务配置失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "获取任务配置失败")
	}

	// 部分任务不可以删除 - 检查是否为系统核心任务
	for _, disabledTask := range nonDeletableTaskTypes {
		if taskConfig.TaskEnumCode == string(disabledTask) {
			return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("任务 %s 不允许删除", disabledTask.GetTaskCondition()))
		}
	}

	if taskConfig == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, fmt.Sprintf("未找到任务: %d", req.TaskId))
	}

	// 2. 验证修改人
	if req.Modifier == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "修改人信息不能为空")
	}

	// 3. 执行删除操作（软删除，将状态设置为终止）
	err = s.taskConfig.Delete(ctx, uint(req.TaskId), req.Modifier)
	if err != nil {
		log.Context(ctx).Errorf("删除任务失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "删除任务失败")
	}

	log.Context(ctx).Infof("成功删除任务, 任务ID: %d, 操作人: %s", req.TaskId, req.Modifier)
	return &common.EmptyReply{}, nil
}

// CreateTask 新增任务
func (s *Service) CreateTask(ctx context.Context, req *v1.AdminCreateTaskRequest) (*v1.AdminCreateTaskReply, error) {
	if req.TaskInfo.CompleteTimes <= 0 {
		return nil, errors.WithMessage(errors.ErrBadRequest, "任务完成次数必须大于0")
	}

	if req.TaskInfo.RewardInfo == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "奖励信息不能为空")
	}

	rewardDetail := models.RewardDetail{
		RewardType: models.RewardTypeGoldCoin, // 默认为积分奖励
		GoldCoins:  int(req.TaskInfo.RewardInfo.GoldCoins),
	}

	// 优先使用传入的RewardType，如果没有传入则根据商品信息推断
	if req.TaskInfo.RewardType > 0 {
		rewardDetail.RewardType = models.RewardType(req.TaskInfo.RewardType)
	}

	// 根据奖励类型设置相应的详细信息
	if rewardDetail.RewardType == models.RewardTypePhysical || req.TaskInfo.RewardInfo.Goods != nil {
		rewardDetail.RewardType = models.RewardTypePhysical
		if req.TaskInfo.RewardInfo.Goods == nil || req.TaskInfo.RewardInfo.Goods.GoodsId == "" {
			return nil, errors.WithMessage(errors.ErrBadRequest, "实物奖励必须提供商品信息")
		}

		rewardDetail.Goods = &models.GoodsDetail{
			GoodsID:      req.TaskInfo.RewardInfo.Goods.GoodsId,
			FreeShipping: req.TaskInfo.RewardInfo.Goods.FreeShipping,
			GoodsIcon:    req.TaskInfo.RewardInfo.Goods.GoodsIcon,
		}
	} else if rewardDetail.RewardType == models.RewardTypeVirtual || req.TaskInfo.RewardInfo.VirtualGoods != nil {
		rewardDetail.RewardType = models.RewardTypeVirtual
		if req.TaskInfo.RewardInfo.VirtualGoods == nil || req.TaskInfo.RewardInfo.VirtualGoods.GoodsId == "" {
			return nil, errors.WithMessage(errors.ErrBadRequest, "虚拟奖励必须提供商品信息")
		}
		rewardDetail.VirtualGoods = &models.VirtualGoods{
			GoodsID:   req.TaskInfo.RewardInfo.VirtualGoods.GoodsId,
			GoodsIcon: req.TaskInfo.RewardInfo.VirtualGoods.GoodsIcon,
		}
	}

	// 设置默认图标
	taskIcon := req.TaskInfo.TaskIcon
	if taskIcon == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "任务图标不能为空")
	}

	rewardIcon := req.TaskInfo.RewardIcon
	if rewardIcon == "" {
		if req.TaskInfo.RewardType == int32(models.RewardTypeGoldCoin) {
			rewardIcon = GoldIconDefaultRewardIcon
		} else {
			return nil, errors.WithMessage(errors.ErrBadRequest, "奖励图标不能为空")
		}
	}

	if req.TaskInfo.TaskTitle == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "任务标题不能为空")
	}

	// 构建任务额外配置
	taskConfigData := models.TaskConfigData{
		TimeLimit: req.TaskInfo.TimeLimit,
	}

	// 如果提供了TargetCode，添加到任务额外配置中
	if req.TaskInfo.TargetCode != "" {
		taskConfigData.TargetCode = req.TaskInfo.TargetCode
	}

	// 7. 构建多语言配置
	i18nData := make(models.I18nData)

	// 如果提供了多语言配置，添加到I18n数据中
	if len(req.TaskInfo.I18N) > 0 {
		for lang, langInfo := range req.TaskInfo.I18N {
			if langInfo.TaskDesc == "" && langInfo.TaskTitle == "" {
				log.Context(ctx).Warnf("跳过空语言配置: %s", lang)
				continue
			}
			i18nData[lang] = models.LanguageInfo{
				TaskDesc:  langInfo.TaskDesc,
				TaskTitle: langInfo.TaskTitle,
			}
		}
	}

	if req.TaskInfo.TaskDesc != "" || req.TaskInfo.TaskCondition != "" {
		// 如果没有 zh-cn 语言包才生成，避免覆盖已有内容
		if _, ok := i18nData["zh-cn"]; !ok {
			i18nData["zh-cn"] = models.LanguageInfo{
				TaskDesc:  req.TaskInfo.TaskDesc,
				TaskTitle: req.TaskInfo.TaskTitle,
			}
		}
	}

	taskSubType := models.GetTaskEnumCodeByTitle(req.TaskInfo.TaskCondition).ToTaskInfo()
	if taskSubType.SubType == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "无效的任务条件")
	}
	// 8. 构建任务配置对象
	taskConfig := &models.TaskConfig{
		TaskType:        models.TaskType(req.TaskInfo.TaskType),
		TaskEnumCode:    string(taskSubType.SubType),
		CompleteTimes:   int(req.TaskInfo.CompleteTimes),
		TaskIcon:        taskIcon,
		RewardIcon:      rewardIcon,
		MinVersion:      req.TaskInfo.MinVersion,
		ShowProject:     req.TaskInfo.ShowProject,
		RewardType:      rewardDetail.RewardType,
		RewardConfigObj: rewardDetail,
		TaskConfigObj:   taskConfigData,
		TaskTitle:       req.TaskInfo.TaskTitle,
		I18nObj:         i18nData,
		ConfigVersion:   req.TaskInfo.ConfigVersion,
		SortOrder:       int(req.TaskInfo.SortOrder),
		VisibleUsers:    req.TaskInfo.VisibleUsers,
		Modifier:        req.TaskInfo.Modifier,
		Status:          int8(req.TaskInfo.Status),
	}

	// 9. 使用事务保证检查唯一性和创建操作的原子性
	err := s.taskConfig.CreateWithUniqueCheck(ctx, taskConfig, taskSubType.SubType)
	if err != nil {
		if strings.Contains(err.Error(), "同一种任务条件已存在任务") {
			return nil, errors.WithMessage(errors.ErrBadRequest, err.Error())
		}
		log.Context(ctx).Errorf("创建任务失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "创建任务失败")
	}

	log.Context(ctx).Infof("成功创建任务, ID: %d, 枚举代码: %s, 创建人: %s",
		taskConfig.ID, taskConfig.TaskEnumCode, req.TaskInfo.Modifier)

	return &v1.AdminCreateTaskReply{
		TaskId: int64(taskConfig.ID),
		Status: int32(taskConfig.Status),
	}, nil
}

// GetTaskTypes 获取任务类型列表
func (s *Service) GetTaskTypes(_ context.Context, _ *v1.GetTaskTypesRequest) (*v1.GetTaskTypesResponse, error) {
	// 获取所有任务类型信息
	allTaskInfoMap := models.AllTaskInfoMap()

	// 创建任务类型信息列表
	taskTypeInfoList := make([]*v1.TaskTypeInfo, 0, len(allTaskInfoMap))

	// 处理所有任务类型
	for enumCode, taskInfo := range allTaskInfoMap {
		taskTypeInfo := &v1.TaskTypeInfo{
			EnumCode:    string(enumCode),
			Title:       taskInfo.SubType.GetTaskCondition(),
			Description: enumCode.GetTaskDesc(),
			Condition:   enumCode.GetTaskCondition(),
		}

		// 将任务信息添加到列表中
		taskTypeInfoList = append(taskTypeInfoList, taskTypeInfo)
	}

	// 返回任务列表
	return &v1.GetTaskTypesResponse{
		Tasks: taskTypeInfoList,
	}, nil
}

// ListTaskGoods   查询任务配置的商品 实物，虚拟
func (s *Service) ListTaskGoods(ctx context.Context, _ *common.EmptyRequest) (*v1.AdminListTaskGoodsReply, error) {
	taskConfigs, err := s.taskConfig.ListGoodsAndVirtualGoodsTasks(ctx)
	if err != nil {
		return nil, errors.WithMessage(errors.ErrInternalServer, "查询任务商品失败")
	}

	// 组装返回
	resp := &v1.AdminListTaskGoodsReply{TaskGoods: make([]*v1.AdminTaskGoods, 0)}
	for _, config := range taskConfigs {
		if config.RewardType == models.RewardTypePhysical && config.RewardConfigObj.Goods != nil {
			resp.TaskGoods = append(resp.TaskGoods, &v1.AdminTaskGoods{
				GoodsId: config.RewardConfigObj.Goods.GoodsID,
				TaskId:  int32(config.ID),
			})
		} else if config.RewardType == models.RewardTypeVirtual && config.RewardConfigObj.VirtualGoods != nil {
			resp.TaskGoods = append(resp.TaskGoods, &v1.AdminTaskGoods{
				GoodsId: config.RewardConfigObj.VirtualGoods.GoodsID,
				TaskId:  int32(config.ID),
			})
		}
	}
	return resp, nil
}

// ListTaskProgress 查询用户任务进度列表
func (s *Service) ListTaskProgress(ctx context.Context, req *v1.AdminListTaskProgressRequest) (*v1.AdminListTaskProgressReply, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	queryParams := &dao.TaskProgressQueryParams{
		PageNum:      int64(req.Page),
		PageSize:     int64(req.PageSize),
		ProgressId:   req.TaskProgressId,
		TaskName:     req.TaskName,
		RewardStatus: int8(req.RewardStatus),
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
	}

	records, total, err := s.taskProgress.List(ctx, queryParams)
	if err != nil {
		log.Context(ctx).Errorf("查询用户任务进度列表失败: %v", err)
		return nil, errors.WithMessage(errors.ErrInternalServer, "查询用户任务进度列表失败")
	}

	userIDs := make([]string, 0, len(records))
	taskIDs := make([]int64, 0, len(records))
	goodsIDs := make([]string, 0, len(records))
	progressIDs := make([]int64, 0, len(records))
	for _, record := range records {
		userIDs = append(userIDs, record.UserID)
		taskIDs = append(taskIDs, record.TaskID)
		progressIDs = append(progressIDs, record.ProgressId)
		if record.RewardType == int(models.RewardTypePhysical) || record.RewardType == int(models.RewardTypeVirtual) {
			if goodsID := extractGoodsID(record.RewardName); goodsID != "" {
				goodsIDs = append(goodsIDs, goodsID)
			}
		}
	}

	userInfoMap, err := s.fetchUserInfoBatch(ctx, userIDs)
	if err != nil {
		log.Context(ctx).Warnf("批量获取用户信息失败: %v", err)
	}
	goodsInfoMap, err := s.fetchGoodsInfoBatch(ctx, goodsIDs)
	if err != nil {
		log.Context(ctx).Warnf("批量获取商品信息失败: %v", err)
	}
	orderInfoMap, err := s.fetchOrderInfoByTaskProgress(ctx, progressIDs)
	if err != nil {
		log.Context(ctx).Warnf("批量获取订单信息失败: %v", err)
	}
	rewardIssueMap, err := s.fetchRewardIssueByProgressIDs(ctx, progressIDs)
	if err != nil {
		log.Context(ctx).Warnf("批量获取任务奖励发放信息失败: %v", err)
	}

	taskProgressRecords := make([]*v1.TaskProgressRecord, 0, len(records))
	for _, record := range records {
		taskProgress := &v1.TaskProgressRecord{
			TaskProgressId: record.ProgressId,
			TaskTitle:      record.TaskName,
			RewardType:     int32(record.RewardType),
			TaskStatus:     int32(record.TaskStatus),
			RewardStatus:   int32(record.RewardStatus),
			CompletedAt:    record.UpdatedAt.Format("2006-01-02 15:04:05"),
			TaskId:         record.TaskID,
		}
		if userInfo, ok := userInfoMap[record.UserID]; ok {
			taskProgress.UserNickname = userInfo.Nickname
			taskProgress.UserId = userInfo.WikiNo
		} else {
			taskProgress.UserId = record.UserID
		}
		if orderInfo, ok := orderInfoMap[record.ProgressId]; ok {
			taskProgress.OrderNo = orderInfo.OrderNo
		}
		if record.RewardType == int(models.RewardTypeGoldCoin) {
			taskProgress.RewardName = record.RewardName
		} else if (record.RewardType == int(models.RewardTypePhysical) || record.RewardType == int(models.RewardTypeVirtual)) && extractGoodsID(record.RewardName) != "" {
			if goodsInfo, ok := goodsInfoMap[extractGoodsID(record.RewardName)]; ok {
				taskProgress.RewardName = goodsInfo.GoodsName
			} else {
				taskProgress.RewardName = record.RewardName
			}
		} else {
			taskProgress.RewardName = record.RewardName
		}
		if rewardIssue, ok := rewardIssueMap[record.ProgressId]; ok {
			taskProgress.TaskRewardId = rewardIssue.ID
		}
		taskProgressRecords = append(taskProgressRecords, taskProgress)
	}

	return &v1.AdminListTaskProgressReply{
		Records: taskProgressRecords,
		Total:   int32(total),
		Page:    req.Page,
	}, nil
}

// extractGoodsID 从奖励名称中提取商品ID
// 注意：这里假设rewardName直接就是商品ID，如果有更复杂的解析逻辑需要相应调整
func extractGoodsID(rewardName string) string {
	if rewardName == "" {
		return ""
	}
	// 目前只是简单的trim，如果rewardName格式有变化，需要更新此逻辑
	return strings.TrimSpace(rewardName)
}

// fetchUserInfoBatch 批量获取用户信息
func (s *Service) fetchUserInfoBatch(ctx context.Context, UserIds []string) (map[string]*UserInfo, error) {
	if len(UserIds) == 0 {
		return make(map[string]*UserInfo), nil
	}
	userInfo, err := s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
		UserIds: UserIds,
	})
	if err != nil {
		log.Context(ctx).Errorf("获取用户信息批量查询失败: %v", err)
		return make(map[string]*UserInfo), nil
	}

	result := make(map[string]*UserInfo)
	if userInfo == nil || userInfo.Message == nil {
		log.Context(ctx).Warnf("用户中心返回的数据为空")
		return result, nil
	}

	for _, user := range userInfo.Message {
		if user == nil || user.UserId == "" {
			continue
		}
		result[user.UserId] = &UserInfo{
			UserID:        user.UserId,
			Nickname:      user.NickName,
			WikiNo:        user.WikiFxNumber,
			AvatarAddress: user.AvatarAddress,
		}
	}
	return result, nil
}

// fetchGoodsInfoBatch 批量获取商品信息
func (s *Service) fetchGoodsInfoBatch(ctx context.Context, goodsIDs []string) (map[string]*GoodsInfo, error) {
	if len(goodsIDs) == 0 {
		return make(map[string]*GoodsInfo), nil
	}
	goods, err := s.goods.FindByGoodsIds(ctx, goodsIDs)
	if err != nil {
		log.Context(ctx).Errorf("批量获取商品信息失败: %v", err)
		return make(map[string]*GoodsInfo), err
	}
	result := make(map[string]*GoodsInfo)
	for _, g := range goods {
		result[g.GoodsId] = &GoodsInfo{
			GoodsID:   g.GoodsId,
			GoodsName: g.Name,
		}
	}
	// 对于未找到的商品，记录警告日志但不创建假数据，避免误导用户
	for _, id := range goodsIDs {
		if _, exists := result[id]; !exists {
			log.Context(ctx).Warnf("商品信息未找到: %s", id)
			// 不在结果中添加假数据，让调用方自行处理商品名称显示
		}
	}
	return result, nil
}

// fetchOrderInfoByTaskProgress 根据任务进度ID批量获取订单信息
func (s *Service) fetchOrderInfoByTaskProgress(ctx context.Context, progressIDs []int64) (map[int64]*OrderInfo, error) {
	if len(progressIDs) == 0 {
		return make(map[int64]*OrderInfo), nil
	}
	var uintProgressIDs []uint
	for _, id := range progressIDs {
		if id > 0 {
			uintProgressIDs = append(uintProgressIDs, uint(id))
		}
	}
	rewardIssues, err := s.taskRewardIssue.GetByTaskProgressIDs(ctx, uintProgressIDs)
	if err != nil {
		log.Context(ctx).Errorf("获取任务奖励发放记录失败: %v", err)
		return make(map[int64]*OrderInfo), err
	}
	operationIDs := make([]string, 0, len(rewardIssues))
	operationIDToProgressID := make(map[string]int64)
	for _, issue := range rewardIssues {
		if issue.OperationID != "" {
			operationIDs = append(operationIDs, issue.OperationID)
			operationIDToProgressID[issue.OperationID] = int64(issue.TaskProgressID)
		}
	}
	if len(operationIDs) == 0 {
		return make(map[int64]*OrderInfo), nil
	}
	payments, err := s.payment.FindByOperationNosForTask(ctx, operationIDs)
	if err != nil {
		log.Context(ctx).Errorf("查询支付记录失败: %v", err)
		return make(map[int64]*OrderInfo), err
	}
	result := make(map[int64]*OrderInfo)
	for _, payment := range payments {
		if progressID, exists := operationIDToProgressID[payment.OperationNo]; exists {
			result[progressID] = &OrderInfo{
				ProgressID: progressID,
				OrderNo:    payment.OrderNo,
			}
		}
	}
	for _, issue := range rewardIssues {
		progressID := int64(issue.TaskProgressID)
		if _, exists := result[progressID]; !exists && issue.OperationID != "" {
			result[progressID] = &OrderInfo{
				ProgressID: progressID,
				OrderNo:    issue.OperationID,
			}
		}
	}
	return result, nil
}

// fetchRewardIssueByProgressIDs 根据任务进度ID批量获取任务奖励发放信息
func (s *Service) fetchRewardIssueByProgressIDs(ctx context.Context, progressIDs []int64) (map[int64]*RewardIssueInfo, error) {
	if len(progressIDs) == 0 {
		return make(map[int64]*RewardIssueInfo), nil
	}
	result := make(map[int64]*RewardIssueInfo)
	var uintProgressIDs []uint
	for _, id := range progressIDs {
		if id > 0 {
			uintProgressIDs = append(uintProgressIDs, uint(id))
		}
	}
	rewardIssues, err := s.taskRewardIssue.GetByTaskProgressIDs(ctx, uintProgressIDs)
	if err != nil {
		return result, err
	}
	for _, issue := range rewardIssues {
		result[int64(issue.TaskProgressID)] = &RewardIssueInfo{
			ID:         int64(issue.ID),
			ProgressID: int64(issue.TaskProgressID),
			Status:     int(issue.Status),
		}
	}
	return result, nil
}

type UserInfo struct {
	UserID        string
	Nickname      string
	WikiNo        string
	AvatarAddress string
}

type GoodsInfo struct {
	GoodsID   string
	GoodsName string
}

type OrderInfo struct {
	ProgressID int64
	OrderNo    string
}

type RewardIssueInfo struct {
	ID         int64
	ProgressID int64
	Status     int
}
