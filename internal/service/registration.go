package service

import (
	"api-expo/internal/util"
	"api-expo/pkg/id"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	v1 "api-expo/api/expo/v1"
	userCenterV1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
)

const defaultTimeFormat = "2006-01-02 15:04:05"

func (s *Service) ExpoUserRegistrationExpoDetail(ctx context.Context, in *v1.ExpoUserRegistrationExpoDetailRequest) (*v1.ExpoUserRegistrationExpoDetailReply, error) {
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)
	expoCommunity, err := s.expoCommunity.GetByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoCommunity.GetByExpoId Err:%v", err)
		return nil, err
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	var (
		logo string
		desc string
	)

	if expoCommunity.Extra != nil {
		lang, ok := expoCommunity.Extra.V.Languages[languageCode]
		if !ok {
			lang, ok = expoCommunity.Extra.V.Languages["en"]
		}
		if ok {
			logo = lang.Logo
			desc = lang.Description
		}
	}
	var (
		identityConfigs []*v1.ExpoUserRegistrationExpoConfig
		businessConfigs []*v1.ExpoUserRegistrationExpoConfig
		expos           []*v1.ExpoUserRegistrationExpoDetailExpoConfigs
	)
	//个人

	//获取配置取身份 行业
	configs, err := s.config.GetMore(ctx, []string{IdentitySubOptions, IndustryOptions})
	if err != nil {
		l.Errorf("config.GetMore Err:%v", err)
		return nil, err
	}
	//身份
	identity, isFind := lo.Find(configs, func(item *models.Config) bool {
		return item.Type == IdentitySubOptions
	})
	if isFind {
		err = json.Unmarshal([]byte(identity.Value), &identityConfigs)
		if err != nil {
			l.Errorf("json.Unmarshal Err:%v", err)
			return nil, err
		}
	}
	//行业
	business, isFind := lo.Find(configs, func(item *models.Config) bool {
		return item.Type == IndustryOptions
	})
	if isFind {
		err = json.Unmarshal([]byte(business.Value), &businessConfigs)
		if err != nil {
			l.Errorf("json.Unmarshal Err:%v", err)
			return nil, err
		}
	}
	return &v1.ExpoUserRegistrationExpoDetailReply{
		Id:              strconv.FormatInt(expoCommunity.ExpoId, 10),
		Logo:            logo,
		StartTime:       expo.Start.Unix(),
		Name:            expo.Name,
		Description:     desc,
		IdentityConfigs: identityConfigs,
		BusinessConfigs: businessConfigs,
		ExpoConfigs:     expos,
	}, nil

}

func (s *Service) ExpoUserRegistration(ctx context.Context, in *v1.ExpoUserSignUpRequest) (*v1.ExpoUserSignUpReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		CountryCode, _  = icontext.CountryCodeFrom(ctx)
		ip, _           = icontext.ClientIPFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	key := fmt.Sprintf("expo_user_registration_%s", userId)
	release, err := s.locker.TryLock(ctx, key, time.Minute)
	if err != nil {
		return nil, innErr.ErrUserOperation
	}
	defer release()

	// 参数校验
	if in.Username == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63399", languageCode)) // 用户名
	}

	if in.Phone == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63400", languageCode)) // 手机号
	}

	if in.Email == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63401", languageCode)) // 邮箱
	}

	if in.Industry <= 0 || in.Industry > v1.Industry_INDUSTRY_OTHER {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63402", languageCode)) // 行业
	}

	if _, ok := v1.SubIdentity_name[int32(in.Identity)]; !ok {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63403", languageCode)) // 身份
	}

	if in.Company == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63404", languageCode)) // 公司
	}

	if in.Job == "" {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63405", languageCode)) // 职位
	}

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	var (
		status = v1.ExpoStatus_ExpoStatus_UNKNOWN
		now    = expo.GetNow()
	)

	if now.After(expo.End) {
		status = v1.ExpoStatus_ExpoStatus_END
	} else if now.After(expo.Start) {
		status = v1.ExpoStatus_ExpoStatus_PROCESSING
	} else {
		status = v1.ExpoStatus_ExpoStatus_NOT_START
	}

	if status == v1.ExpoStatus_ExpoStatus_END {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63393", languageCode)) // 展会已结束
	}

	var (
		applyUserId string
		isRegister  bool
	)

	//自己报名
	if !in.SignForOther {
		isApply, err := s.participant.ExistsByUserId(ctx, expo.ID, userId)
		if err != nil {
			l.Errorf("participant.ExistsByUserId Err:%v", err)
			return nil, err
		}
		applyUserId = userId
		if isApply {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63394", languageCode)) // 请勿重复报名
		}
		isRegister = true

		// 检测手机号是否存在
		exists, err := s.participant.ExistsByPhone(ctx, expoId, in.PhoneAreaCode, in.Phone)
		if err != nil {
			l.Errorf("participant.ExistsByPhone Err:%v", err)
			return nil, err
		}

		if exists {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63398", languageCode)) // 手机号存在
		}
	} else { //他人报名
		user, err := s.user.GetUserByPhoneNumber(ctx, &userCenterV1.GetUserByPhoneNumberRequest{
			PhoneNumber: in.Phone,
			AreaCode:    in.PhoneAreaCode,
		})
		if err != nil {
			l.Errorf("GetUserByPhoneNumber Err:%v", err)
		} else {
			if len(user.UserId) > 0 {
				applyUserId = user.UserId
				isRegister = true
			}
		}

		isApply, err := s.participant.ParticipantByPhoneOrEmailExist(ctx, in.PhoneAreaCode, in.Phone, in.Email, expoId)
		if err != nil {
			l.Errorf("participant.ExistsByUserId Err:%v", err)
			return nil, err
		}

		if isApply {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63394", languageCode))
		}
	}

	var (
		defaultTime, _ = time.Parse(defaultTimeFormat, "0000-00-00 00:00:00")
		product        = 2
		applyStatus    = v1.ApplyAudit_APPLY_AUDIT_Wait
	)
	ticketCode := ""
	if status == v1.ExpoStatus_ExpoStatus_PROCESSING {
		applyStatus = v1.ApplyAudit_APPLY_AUDIT_Pass
		ticketCode = util.GenerateCode(int(expoId))
		err := s.expo.RegistrantsIncr(ctx, expoId)
		if err != nil {
			l.Errorf("expo.RegistrantsIncr Err:%v", err)
		}

	}

	if pro := in.Industry; pro == v1.Industry_INDUSTRY_FOREX {
		product = 1
	}
	IsCommentNotice := 0
	if in.IsCheck {
		IsCommentNotice = 1
	}
	applyId, err := s.participant.Add(ctx, &models.Participant{
		UserId:          userId,
		ExpoId:          expoId,
		FirstName:       in.Username,
		Phone:           in.Phone,
		AreaCode:        in.PhoneAreaCode,
		Email:           in.Email,
		SubIdentityCode: in.Identity,
		IndustryCode:    in.Industry,
		Product:         product,
		CompanyName:     in.Company,
		Position:        in.Job,
		Mode:            1, //自主报名
		TicketType:      1, //普通票
		TicketStatus:    100,
		CreatedAt:       now,
		CheckedAt:       defaultTime,
		InvitedAt:       defaultTime,
		InvitedStatus:   4,
		RecordedAt:      defaultTime,
		ModifiedAt:      now,
		Code:            ticketCode,
		ApplyStatus:     applyStatus,
		ApplyUserid:     applyUserId,
		ForOther:        in.SignForOther,
		IsResister:      isRegister,
		IsCommentNotice: IsCommentNotice,
		CountryCode:     CountryCode,
		Ip:              ip,
	})
	if err != nil {
		l.Errorf("participant.Add Err:%v", err)
		return nil, err
	}
	if applyStatus == 2 {
		nickName := ""
		userinfo, err := s.user.GetBasicUserInfo(ctx, &userCenterV1.GetUserWikiNumbersRequest{
			UserIds: []string{userId},
		})
		if err != nil {
			l.Errorf("GetBasicUserInfo.Add Err:%v", err)
		}
		if len(userinfo.List) > 0 {
			nickName = userinfo.List[0].NickName
		}
		if in.IsCheck {
			comment := &models.ExpoComment{
				ID:           id.CreatePrimaryId("COM"),
				ExpoID:       expoId,
				UserID:       userId,
				Content:      commentContent,
				ContentType:  int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_REGISTRATION),
				Status:       2,
				LanguageCode: "zh-cn",
				CreatedAt:    now,
				UpdatedAt:    now,
				NickName:     nickName,
				CountryCode:  CountryCode,
				Ip:           ip,
			}
			err = s.comment.Add(ctx, comment)
			if err != nil {
				return nil, innErr.ErrBadRequest
			}
		}
		go s.PushMessageMessage(languageCode, expo.Langs, userId, expo.Name, expoId, int64(applyId))
	}

	return &v1.ExpoUserSignUpReply{
		ApplyId: strconv.FormatInt(int64(applyId), 10),
	}, nil
}

func (s *Service) ExhibitorRegistration(ctx context.Context, in *v1.ExhibitorSignUpRequest) (*v1.ExhibitorSignUpReply, error) {
	var (
		l                        = log.Context(ctx)
		languageCode, _          = icontext.LanguageCodeFrom(ctx)
		PreferredlanguageCode, _ = icontext.PreferredLanguageCodeFrom(ctx)
		userId, _                = icontext.UserIdFrom(ctx)
		basicData, _             = icontext.BasicDataFrom(ctx)
		countryCode, _           = icontext.CountryCodeFrom(ctx)
		ip, _                    = icontext.ClientIPFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	_, err = s.expoApply.Add(ctx, &models.ExpoExhibitorApply{
		UserId:                userId,
		ExpoId:                expoId,
		Booth:                 in.ExhibitSize,
		Company:               in.Company,
		Website:               in.Website,
		Contact:               in.Contact,
		PhoneAreaCode:         in.PhoneAreaCode,
		Phone:                 in.Phone,
		Email:                 in.Email,
		Status:                1,
		Reason:                "",
		ClientIP:              ip,
		LanguageCode:          languageCode,
		PreferredLanguageCode: PreferredlanguageCode,
		CountryCode:           countryCode,
		BasicData:             basicData,
	})
	if err != nil {
		l.Errorf("expoApply.Add Err:%v", err)
		return nil, err
	}
	return &v1.ExhibitorSignUpReply{}, nil
}

// ExpoUserRegistrationFinish 展会完成获取展会信息
func (s *Service) ExpoUserRegistrationFinish(ctx context.Context, in *v1.ExpoUserRegistrationFinishRequest) (*v1.ExpoUserRegistrationFinishReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		//PreferredlanguageCode, _ = icontext.PreferredLanguageCodeFrom(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
		//basicData, _             = icontext.BasicDataFrom(ctx)
		//countryCode, _           = icontext.CountryCodeFrom(ctx)
		//ip, _                    = icontext.ClientIPFrom(ctx)
	)
	applyStaus := map[int]string{
		1: "待审核",
		2: "审核通过",
		3: "审核未通过",
	}

	applyId, err := strconv.ParseInt(in.ApplyId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	ticket, err := s.participant.GetByTicketIdAndUserId(ctx, applyId, userId)
	if err != nil {
		l.Errorf("participant.GetByTicketIdAndUserId Err:%v", err)
		return nil, err
	}
	if ticket == nil {
		return nil, innErr.ErrBadRequest //errors.WithMessage(errors.ErrInternalServer, "error")
	}

	expoCommunity, err := s.expoCommunity.GetByExpoId(ctx, ticket.ExpoId)
	if err != nil {
		l.Errorf("expoCommunity.GetByExpoId Err:%v", err)
		return nil, err
	}

	expo, err := s.expo.Get(ctx, ticket.ExpoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	var (
		logo string
		desc string
	)

	if expoCommunity.Extra != nil {
		lang, ok := expoCommunity.Extra.V.Languages[languageCode]
		if !ok {
			lang, ok = expoCommunity.Extra.V.Languages["en"]
		}
		if ok {
			logo = lang.Logo
			desc = lang.Description
		}
	}
	//审核状态
	status := ""
	if apply, ok := applyStaus[int(ticket.ApplyStatus)]; ok {
		status = apply
	}
	ticketCode := "----"
	if len(ticket.Code) > 0 {
		ticketCode = ticket.Code
	}
	return &v1.ExpoUserRegistrationFinishReply{
		TicketCode: ticketCode,
		Status:     status,
		ExpoName:   expo.Name,
		ExpoDesc:   desc,
		ExpoLogo:   logo,
		StartTime:  expo.Start.Unix(),
	}, nil

}
