package service

import (
	"api-expo/pkg/rabbit"
	"context"
	"testing"

	"github.com/zeebo/assert"
)

func TestNotification_String(t *testing.T) {
	s := new(Service)
	notificationRabbit, err := rabbit.NewRabbit(&rabbit.Config{
		URL:        "amqp://fxeye:<EMAIL>:5672/MessageCenter",
		Exchange:   "messagesync",
		RoutingKey: "WikiFX.ActivityNotice.Message.Sync",
	})
	if err != nil {
		panic(err)
	}
	s.notification = notificationRabbit

	s.scheduleStartNotification(context.Background(), &Content{
		NickName:        "嘉宾测试wikinumber",
		LanguageCode:    "zh-cn",
		ExpoId:          "1011",
		ScheduleGuestId: "10644",
		Date:            "2025.07.30",
		Start:           "2025.07.30 08:00",
		Avatar:          "https://expoliveimgs.zy223.comde8982762.jpg_wiki-template-global",
		Themes: map[string]string{
			"zh-cn": "展会即将开始",
			"zh":    "展會即將開始",
			"en":    "The exhibition will begin shortly",
		},
		UserId: "**********",
	})
	assert.Nil(t, err)
}
