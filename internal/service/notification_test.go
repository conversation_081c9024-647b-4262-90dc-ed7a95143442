package service

import (
	"testing"
	"time"

	"gold_store/pkg/rabbit"

	"github.com/stretchr/testify/assert"
)

func TestService_MockOrderNotification(t *testing.T) {
	notificationRabbit, err := rabbit.NewRabbit(&rabbit.Config{
		URL:        "amqp://fxeye:<EMAIL>:5672/MessageCenter",
		Exchange:   "messagesync",
		RoutingKey: "WikiFX.LogisticsNotify.Message.Sync",
	})
	if err != nil {
		panic(err)
	}

	notice := &Notification{
		BusinessId:       "20250603O018363230735013",
		BusinessCategory: "Logistics",
		Title: map[string]string{
			"zh-cn": "订单物流信息",
		},
		Content: map[string]string{
			"zh-cn": "您的订单已进入仓库，准备出库",
		},
		CustomInformation: map[string]string{
			"Code":             "20250603O018363230735013",
			"type":             "11",
			"largeIcon":        "https://img.fx696.com/gold_store/express-shipping-zh.png",
			"languageCode":     "zh-cn",
			"title":            "订单物流信息",
			"alert":            "您的订单已进入仓库，准备出库",
			"userId":           "**********",
			"logistics_status": "0",
		},
		UserId:        "**********",
		CountryCodes:  nil,
		LanguageCodes: nil,
		AreaCodes:     nil,
		PushTimestamp: time.Now().Add(10 * time.Second).UnixMilli(),
		Snapshot:      nil,
		AggInfo:       nil,
	}
	err = notificationRabbit.Publish(notice.String())
	assert.Nil(t, err)
}
