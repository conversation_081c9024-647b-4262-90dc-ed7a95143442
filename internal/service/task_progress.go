package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/community"
	"gold_store/pkg/message_parser"

	"github.com/airunny/wiki-go-tools/alarm"
	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-kratos/kratos/v2/log"
)

// HandleTaskMessage 处理特定任务类型的消息
func (s *Service) HandleTaskMessage(taskType models.TaskSubType, session *kafka.Consumer, message *kafka.Message) error {
	ctx := context.Background()
	ctx = icontext.WithRequestId(ctx, reqid.GenRequestID())
	handler, exists := s.getTaskHandlerMap()[taskType]
	if !exists {
		log.Context(ctx).Warnf("No handler found for task type: %v", taskType)
		return nil
	}

	return handler(ctx, session, message)
}

// 获取任务类型对应的处理函数映射
func (s *Service) getTaskHandlerMap() map[models.TaskSubType]TaskConsumerHandler {

	handlerMap := make(map[models.TaskSubType]TaskConsumerHandler)

	// 只注册一次 dynamic_wikicommunity_posts 的 handler
	handlerMap[models.TaskSubTypePostMoment] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		// 解析消息内容，区分 post_moment 和 post_business
		log.Context(ctx).Infof("Processing dynamic_wikicommunity_posts message: %s", string(message.Value))
		data, _, err := message_parser.ParseCDCMessage(message.Value, []string{"PostsId", "UserId", "Status", "ReleaseType"})
		if err != nil {
			log.Context(ctx).Errorf("Failed to parse dynamic_wikicommunity_posts message: %v", err)
			return err
		}

		if data == nil {
			log.Context(ctx).Warnf("Parsed data is nil, skipping message processing")
			return nil
		}

		status, statusOk := message_parser.GetIntField(data, "Status")
		if !statusOk {
			log.Context(ctx).Warnf("Status field not found or invalid type")
			return nil
		}

		if status != 200 {
			return nil // 只有状态为200时才处理
		}
		postsId, _ := message_parser.GetStringField(data, "PostsId")
		userId, _ := message_parser.GetStringField(data, "UserId")
		releaseType, _ := message_parser.GetIntField(data, "ReleaseType")

		resp, err := s.CommunityClient.GetJoinActivityByPostsID(ctx, &community.GetJoinActivityByPostsIDRequest{
			PostsId: postsId,
		})
		if err != nil {
			log.Context(ctx).Errorf("Failed to get join activity by posts ID: %v", err)
		}

		if resp != nil && resp.Succeed && resp.Result.IsExist {
			log.Context(ctx).Infof("判定为 post_moment，Status changed to 200 for post %s by user %s", postsId, userId)
			err = s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeJoinActivity, "")
		}

		if releaseType == 1 {
			log.Context(ctx).Infof("判定为 post_business，Status changed to 200 for post %s by user %s", postsId, userId)
			err = s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypePostBusiness, "")
		} else if releaseType == 2 {
			log.Context(ctx).Infof("判定为 post_moment，Status changed to 200 for post %s by user %s", postsId, userId)
			err = s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypePostMoment, "")
		} else {
			log.Context(ctx).Infof("Unknown releaseType %d for post %s by user %s", releaseType, postsId, userId)
			return nil
		}
		if err != nil {
			log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			return err
		}

		return nil
	}

	handlerMap[models.TaskSubTypePostBusiness] = handlerMap[models.TaskSubTypePostMoment]

	//评价商业或动态
	handlerMap[models.TaskSubTypeCommentPost] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing comment_post message: %s", string(message.Value))

		// 使用提取的解析函数
		data, _, err := message_parser.ParseCDCMessage(message.Value,
			[]string{"PostsId", "UserId", "Status"})
		if err != nil {
			log.Context(ctx).Errorf("Failed to parse comment_post message: %v", err)
			return err
		}

		if data == nil {
			log.Context(ctx).Warnf("Parsed data is nil, skipping message processing")
			return nil
		}

		status, ok := message_parser.GetIntField(data, "Status")
		if !ok {
			log.Context(ctx).Warnf("Status field not found or invalid type")
			return nil
		}

		userId, _ := message_parser.GetStringField(data, "UserId")
		postsId, _ := message_parser.GetStringField(data, "PostsId")

		if status == 200 {
			log.Context(ctx).Infof("Status changed to 200 for PostsId comment %s by user %s", postsId, userId)
			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeCommentPost, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
				return err
			}
		}

		return nil
	}

	// 处理评价经纪商任务
	handlerMap[models.TaskSubTypeRateDealer] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing rate_dealer message: %s", string(message.Value))
		data, _, err := message_parser.ParseCDCMessage(message.Value,
			[]string{"UserId", "Status", "PostsId"})
		if err != nil {
			log.Context(ctx).Errorf("Failed to parse rate_dealer message: %v", err)
			return err
		}

		// 安全地获取字段值
		status, ok := message_parser.GetIntField(data, "Status")
		if !ok {
			log.Context(ctx).Warnf("Status field not found or invalid type")
			return nil
		}

		userId, _ := message_parser.GetStringField(data, "UserId")
		postsId, _ := message_parser.GetStringField(data, "PostsId")

		if status == 200 {
			log.Context(ctx).Infof("Status changed to 200 for PostsId %s by user %s", postsId, userId)
			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeRateDealer, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			}
		}

		return nil
	}

	// 参与活动
	handlerMap[models.TaskSubTypeJoinActivity] = handlerMap[models.TaskSubTypePostMoment]

	// 修改用户名称
	handlerMap[models.TaskSubTypeModifyUsername] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing modify_username message: %s", string(message.Value))
		data, oldData, err := message_parser.ParseCDCMessage(message.Value,
			[]string{"UserId", "Status", "NickName"})
		if err != nil {
			log.Context(ctx).Errorf("Failed to parse modify_username message: %v", err)
			return err
		}

		userId, _ := message_parser.GetStringField(data, "UserId")

		// 检查NickName是否发生变化
		newNickName, newNameExists := message_parser.GetStringField(data, "NickName")
		oldNickName, oldNameExists := message_parser.GetStringField(oldData, "NickName")

		if newNameExists && oldNameExists && newNickName != oldNickName {
			log.Context(ctx).Infof("NickName changed from '%s' to '%s' for user %s", oldNickName, newNickName, userId)
			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeModifyUsername, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			}
		}

		return nil
	}

	// 修改用户头像
	handlerMap[models.TaskSubTypeModifyAvatar] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing modify_avatar message: %s", string(message.Value))
		data, oldData, err := message_parser.ParseCDCMessage(message.Value,
			[]string{"UserId", "Status", "AvatarAddress"})
		if err != nil {
			log.Context(ctx).Errorf("Failed to parse modify_avatar message: %v", err)
			return err
		}

		userId, _ := message_parser.GetStringField(data, "UserId")

		// 检查AvatarAddress是否发生变化
		newAvatar, newAvatarExists := message_parser.GetStringField(data, "AvatarAddress")
		oldAvatar, oldAvatarExists := message_parser.GetStringField(oldData, "AvatarAddress")

		// 只有当状态为200且头像确实发生了变化时才更新任务进度
		if newAvatarExists && oldAvatarExists && newAvatar != oldAvatar {
			log.Context(ctx).Infof("AvatarAddress changed for user %s", userId)
			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeModifyAvatar, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			}
		}
		return nil
	}

	// 实名认证
	handlerMap[models.TaskSubTypeVerifyIdentity] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing verify_identity message: %s", string(message.Value))

		// 解析CDC消息
		var cdcData map[string]interface{}
		if err := json.Unmarshal(message.Value, &cdcData); err != nil {
			log.Context(ctx).Errorf("Failed to parse open_vps CDC message: %v", err)
			return err
		}

		// 获取数据部分
		data, ok := cdcData["data"].(map[string]interface{})
		if !ok {
			log.Context(ctx).Warnf("CDC message doesn't contain proper data field")
			return nil
		}
		status, ok := message_parser.GetIntField(data, "FaceAuthstatus")
		if !ok {
			log.Context(ctx).Warnf("Status field not found or invalid type")
			return nil
		}

		userId, _ := message_parser.GetStringField(data, "UserId")

		//如果是历史回放数据
		messageType, typeExists := cdcData["type"].(string)
		if !typeExists {
			log.Context(ctx).Infof("CDC message type not found, assuming bootstrap-insert")
			return nil
		}

		if status == 1 {
			if messageType == "bootstrap-insert" {
				if err := s.CompleteHistoricalTasks(ctx, userId, models.TaskSubTypeVerifyIdentity, ""); err != nil {
					log.Context(ctx).Errorf("Failed to update task progress for TaskSubTypeVerifyIdentity: %v", err)
					return err
				}

				return nil
			}
			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeVerifyIdentity, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			}
		}
		return nil
	}

	// 绑定实盘
	handlerMap[models.TaskSubTypeBindRealAccount] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing bind_real_account message: %s", string(message.Value))
		// 解析CDC消息
		var cdcData map[string]interface{}
		if err := json.Unmarshal(message.Value, &cdcData); err != nil {
			log.Context(ctx).Errorf("Failed to parse open_vps CDC message: %v", err)
			return err
		}

		// 获取数据部分
		data, ok := cdcData["data"].(map[string]interface{})
		if !ok {
			log.Context(ctx).Warnf("CDC message doesn't contain proper data field")
			return nil
		}

		isDelete, _ := data["is_delete"].(int32)
		realType, _ := data["real_type"].(int32)
		userId, ok := data["user_id"].(string)
		if !ok {
			log.Context(ctx).Errorf("UserId field not found or empty in bind_real_account message")
			return nil
		}

		//如果是历史回放数据
		messageType, typeExists := cdcData["type"].(string)
		if !typeExists {
			log.Context(ctx).Infof("CDC message type not found, assuming bootstrap-insert")
			return nil
		}

		if isDelete == 0 && realType == 0 {
			if messageType == "bootstrap-insert" {
				if err := s.CompleteHistoricalTasks(ctx, userId, models.TaskSubTypeBindRealAccount, ""); err != nil {
					log.Context(ctx).Errorf("Failed to update task progress for TaskSubTypeBindRealAccount: %v", err)
					return err
				}

				return nil
			}

			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeBindRealAccount, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			}
		}
		return nil
	}

	// 开通VPS
	handlerMap[models.TaskSubTypeOpenVPS] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		//log.Context(ctx).Infof("Processing open_vps message: %s", string(message.Value))

		// 解析CDC消息
		var cdcData map[string]interface{}
		if err := json.Unmarshal(message.Value, &cdcData); err != nil {
			log.Context(ctx).Errorf("Failed to parse open_vps CDC message: %v", err)
			return err
		}

		// 检查表名是否是hostinfo
		tableName, tableExists := cdcData["table"].(string)
		if !tableExists || tableName != "hostinfo" {
			log.Context(ctx).Infof("Not from hostinfo table, actual table: %v", tableName)
			return nil
		}

		// 获取数据部分
		data, ok := cdcData["data"].(map[string]interface{})
		if !ok {
			log.Context(ctx).Warnf("CDC message doesn't contain proper data field")
			return nil
		}

		// 获取用户ID
		userId, userIdExists := data["UserId"].(string)
		if !userIdExists || userId == "" {
			log.Context(ctx).Errorf("UserId field not found or empty in open_vps message")
			return nil
		}

		//如果是历史回放数据
		messageType, typeExists := cdcData["type"].(string)
		if !typeExists {
			log.Context(ctx).Infof("CDC message type not found, assuming bootstrap-insert")
			return nil
		}

		if messageType == "bootstrap-insert" {
			if err := s.CompleteHistoricalTasks(ctx, userId, models.TaskSubTypeOpenVPS, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress for open VPS action: %v", err)
				return err
			}

			return nil
		}

		// 检查是否是插入操作(开通VPS)
		if messageType != "insert" {
			//log.Context(ctx).Infof("Not an insert operation for open_vps, type: %v", messageType)
			return nil
		}
		if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeOpenVPS, ""); err != nil {
			log.Context(ctx).Errorf("Failed to update task progress for open VPS action: %v", err)
			return err
		}

		return nil
	}

	// 浏览交易商详情页
	handlerMap[models.TaskSubTypeViewDealer] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing view_dealer message: %s", string(message.Value))
		data, err := message_parser.ParseFlatMessage(message.Value,
			[]string{"user_id", "status", "event"})
		if err != nil {
			log.Context(ctx).Errorf("Failed to parse view_dealer message: %v", err)
			return err
		}

		status, ok := message_parser.GetIntField(data, "status")
		if !ok {
			log.Context(ctx).Warnf("Status field not found or invalid type")
			return nil
		}
		userId, ok := message_parser.GetStringField(data, "user_id")
		if !ok {
			log.Context(ctx).Warnf("UserId field not found or invalid type")
			return nil
		}
		event, ok := message_parser.GetStringField(data, "event")
		if !ok {
			log.Context(ctx).Warnf("Event field not found or invalid type")
			return nil
		}

		if status == 1 {
			log.Context(ctx).Infof("Status changed to 200 for user %s", userId)

			var taskType models.TaskSubType
			switch event {
			case string(models.TaskSubTypeViewDealer):
				log.Context(ctx).Infof("Event is ViewDealer for user %s", userId)
				taskType = models.TaskSubTypeViewDealer
			case string(models.TaskSubTypeSearch):
				log.Context(ctx).Infof("Event is Search for user %s", userId)
				taskType = models.TaskSubTypeSearch
			default:
				log.Context(ctx).Warnf("Unknown event type: %s for user %s", event, userId)
				return nil
			}

			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, taskType, ""); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress: %v", err)
			}
		}
		return nil
	}

	// 搜索
	handlerMap[models.TaskSubTypeSearch] = handlerMap[models.TaskSubTypeViewDealer]

	// WikiFX Activity-关注
	handlerMap[models.TaskSubTypeFollowWikiFX] = func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error {
		log.Context(ctx).Infof("Processing follow_wikifx message: %s", string(message.Value))

		// 解析完整CDC消息格式
		var cdcData map[string]interface{}
		if err := json.Unmarshal(message.Value, &cdcData); err != nil {
			log.Context(ctx).Errorf("Failed to parse follow_wikifx CDC message: %v", err)
			return err
		}

		// 获取数据部分
		data, ok := cdcData["data"].(map[string]interface{})
		if !ok {
			log.Context(ctx).Warnf("CDC message doesn't contain proper data field")
			return nil
		}

		// 获取关注用户ID
		userId, userIdExists := data["UserId"].(string)
		if !userIdExists || userId == "" {
			log.Context(ctx).Warnf("UserId field not found or empty in follow_wikifx message")
			return nil
		}

		// 获取关注用户ID
		code, ok := data["AttentionedUserId"].(string)
		if !ok || code == "" {
			log.Context(ctx).Warnf("Code field not found or empty in follow_wikifx message")
			return nil
		}

		// 需要查一下目前配置是需要关注那个公众号
		taskEnumCode := string(models.TaskSubTypeFollowWikiFX)
		taskConfigList, err := s.taskConfig.ListTaskConfigsByEnumCode(ctx, taskEnumCode)
		if err != nil {
			log.Context(ctx).Errorf("获取任务配置失败, enumCode: %s, error: %v", taskEnumCode, err)
			return err
		}

		// 如果没有找到任务配置，记录日志并返回
		if len(taskConfigList) == 0 {
			log.Context(ctx).Infof("未找到任务配置, enumCode: %s", taskEnumCode)
			return nil
		}

		//如果是历史回放数据
		messageType, typeExists := cdcData["type"].(string)
		if !typeExists {
			log.Context(ctx).Infof("CDC message type not found, assuming bootstrap-insert")
			return nil
		}

		if messageType == "bootstrap-insert" {
			// 对每个符合条件的任务配置进行处理
			for _, taskConfig := range taskConfigList {
				// 检查是否需要关注WikiFX
				if taskConfig.TaskConfigObj.TargetCode != code {
					continue
				}

				// 更新任务进度
				if err := s.CompleteHistoricalTasks(ctx, userId, models.TaskSubTypeFollowWikiFX, code); err != nil {
					log.Context(ctx).Errorf("Failed to update task progress for follow action (taskId: %d): %v", taskConfig.ID, err)
					// 继续处理下一个任务配置，不中断整个处理流程
				} else {
					log.Context(ctx).Infof("Successfully updated task progress for task ID: %d, enumCode: %s", taskConfig.ID, taskEnumCode)
				}
			}

			return nil
		}

		// 验证是否是(关注)
		if messageType != "insert" {
			log.Context(ctx).Infof("Not an insert operation for follow_wikifx, type: %v", messageType)
			return nil
		}

		// 对每个符合条件的任务配置进行处理
		for _, taskConfig := range taskConfigList {
			// 检查是否需要关注WikiFX
			if taskConfig.TaskConfigObj.TargetCode != code {
				continue
			}

			// 更新任务进度
			if err := s.UpdateTaskProgressByPostStatus(ctx, userId, models.TaskSubTypeFollowWikiFX, code); err != nil {
				log.Context(ctx).Errorf("Failed to update task progress for follow action (taskId: %d): %v", taskConfig.ID, err)
				// 继续处理下一个任务配置，不中断整个处理流程
			} else {
				log.Context(ctx).Infof("Successfully updated task progress for task ID: %d, enumCode: %s", taskConfig.ID, taskEnumCode)
			}
		}

		return nil
	}

	return handlerMap
}

// TaskConsumerHandler  任务消费处理函数类型
type TaskConsumerHandler func(ctx context.Context, session *kafka.Consumer, message *kafka.Message) error

type TaskEventMessage struct {
	UserId     string    `json:"user_id"`
	Event      string    `json:"event"`
	Status     int       `json:"status"`
	CreateTime time.Time `json:"create_time"`
}

// SendTaskEventToQueue 发送任务事件
func (s *Service) SendTaskEventToQueue(ctx context.Context, req *v1.TaskEventRequest) (*v1.TaskEventReply, error) {
	if req.UserId == "" {
		return nil, innErr.ErrLogin
	}

	if req.Event != string(models.TaskSubTypeViewDealer) && req.Event != string(models.TaskSubTypeSearch) {
		log.Context(ctx).Errorf("非法的任务事件类型: req %s", req.Event)

		return &v1.TaskEventReply{Success: false},
			innErr.WithMessage(innErr.ErrBadRequest, "非法的事件类型，只支持浏览交易商和搜索事件")
	}

	// 确保 TaskProducer 已初始化
	if s.ConfluentProducer == nil {
		log.Context(ctx).Error("任务消息生产者未初始化")
		return &v1.TaskEventReply{Success: false}, innErr.WithMessage(innErr.ErrInternalServer, "消息队列服务未就绪")
	}

	// 构建消息体
	message := map[string]interface{}{
		"user_id": req.UserId,
		"event":   req.Event,
		"status":  req.Status,
		"time":    time.Now().Unix(),
	}

	data, err := json.Marshal(message)
	if err != nil {
		log.Context(ctx).Errorf("Failed to marshal task event message: %v", err)
		return &v1.TaskEventReply{Success: false}, innErr.WithMessage(innErr.ErrInternalServer, "Failed to encode message")
	}

	topic := s.business.TaskProducer.Topic
	if topic == "" {
		topic = "dynamic_wiki_gold_store_task"
	}

	msg := &kafka.Message{
		TopicPartition: kafka.TopicPartition{
			Topic:     &topic,
			Partition: kafka.PartitionAny,
		},
		Value: data,
		Headers: []kafka.Header{
			{
				Key:   "request_id",
				Value: []byte(reqid.GenRequestID()),
			},
		},
	}

	// 发送消息到 Kafka
	err = s.ConfluentProducer.Produce(msg, nil)
	if err != nil {
		log.Context(ctx).Errorf("Failed to send task event message for user %s: %v", req.UserId, err)

		// Kafka消息发送失败报警
		alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("任务事件Kafka消息发送失败: userId=%s, event=%s, topic=%s, error=%v", req.UserId, req.Event, topic, err))

		return &v1.TaskEventReply{Success: false}, innErr.WithMessage(innErr.ErrInternalServer, "Failed to send message")
	}

	// 等待消息发送完成
	s.ConfluentProducer.Flush(1000) // 等待所有消息发送，最多等待1秒

	// 记录成功日志
	log.Context(ctx).Infof("成功发送任务事件到队列, userId=%s, event=%s", req.UserId, req.Event)

	return &v1.TaskEventReply{Success: true}, nil
}

// completeTaskProgress 直接完成任务进度（用于历史数据）
func (s *Service) completeTaskProgress(ctx context.Context, userId string, taskID uint, taskProgress *models.TaskProgress, taskConfig *models.TaskConfig) error {
	// 只有未完成的任务才需要更新
	if taskProgress.Status == models.UserProgressStatusCompleted || taskProgress.Status == models.UserProgressStatusReceived {
		log.Infof("任务已经是完成或已领取状态，无需更新, userId: %s, taskId: %d, status: %d", userId, taskID, taskProgress.Status)
		return nil
	}

	// 更新进度为完成所需次数
	err := s.taskProgress.UpdateProgress(ctx, userId, taskProgress.ID, taskConfig.CompleteTimes)
	if err != nil {
		log.Errorf("更新历史任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	// 更新状态为已领取（历史数据设置为已领取状态，避免用户重复领取奖励）
	err = s.taskProgress.UpdateStatus(ctx, userId, taskID, models.UserProgressStatusReceived)
	if err != nil {
		log.Errorf("更新历史任务状态为已领取失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	log.Infof("历史任务已标记为已领取, userId: %s, taskId: %d, progress: %d -> %d",
		userId, taskID, taskProgress.UserProgress, taskConfig.CompleteTimes)
	return nil
}

// CompleteHistoricalTasks 根据任务类型批量完成历史任务
// 用于上线后刷新历史数据，将已经在之前版本完成相关任务的用户设置为已完成且已领取状态
func (s *Service) CompleteHistoricalTasks(ctx context.Context, userId string, taskType models.TaskSubType, targetCode string) error {
	taskEnumCode := string(taskType)
	taskConfigList, err := s.taskConfig.ListTaskConfigsByEnumCode(ctx, taskEnumCode)
	if err != nil {
		log.Errorf("获取任务配置失败, enumCode: %s, error: %v", taskEnumCode, err)
		return err
	}
	if len(taskConfigList) == 0 {
		return nil
	}

	// 处理每个匹配的任务配置
	for _, taskConfig := range taskConfigList {
		if targetCode != "" && taskConfig.TaskConfigObj.TargetCode != "" && taskConfig.TaskConfigObj.TargetCode != targetCode {
			log.Infof("任务目标不匹配, skip, taskId: %d, targetCode: %s, requiredCode: %s",
				taskConfig.ID, targetCode, taskConfig.TaskConfigObj.TargetCode)
			continue
		}

		taskID := taskConfig.ID

		// 查询用户是否有该任务的进度记录
		taskProgress, err := s.taskProgress.GetTaskProgress(ctx, userId, taskID)
		if err != nil {
			log.Errorf("获取任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
			continue
		}

		now := time.Now().UTC()

		// 处理不同的任务状态场景
		if taskProgress == nil {
			// 用户没有该任务的进度记录，创建新的已完成任务记录
			if err := s.createCompletedTaskProgress(ctx, userId, taskID, taskConfig, now); err != nil {
				log.Errorf("创建历史任务进度记录失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
				continue
			}
			log.Infof("成功创建历史任务进度记录(已完成), userId: %s, taskId: %d, enumCode: %s", userId, taskID, taskEnumCode)
			continue
		}

		// 处理已有任务进度记录的情况
		// 如果新手任务已完成或已领取，不再更新
		if taskConfig.TaskType == models.TaskTypeNewComer &&
			(taskProgress.Status == models.UserProgressStatusCompleted || taskProgress.Status == models.UserProgressStatusReceived) {
			log.Infof("新手任务已完成或已领取奖励，不再更新历史数据, userId: %s, taskId: %d", userId, taskID)
			continue
		}

		// 处理任务过期情况
		if taskProgress.Status == models.UserProgressStatusExpired || taskProgress.ExpireTime.Before(now) {
			// 对于过期任务，如果是日常任务，创建新的已完成任务
			if taskConfig.TaskType == models.TaskTypeDaily {
				if err := s.createCompletedTaskProgress(ctx, userId, taskID, taskConfig, now); err != nil {
					log.Errorf("创建新的历史任务进度记录失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
				} else {
					log.Infof("成功创建新的历史任务进度记录(已完成), userId: %s, taskId: %d", userId, taskID)
				}
			}
			continue
		}

		// 直接将任务设置为已完成状态
		if err := s.completeTaskProgress(ctx, userId, taskID, taskProgress, taskConfig); err != nil {
			log.Errorf("强制完成历史任务失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		} else {
			log.Infof("成功强制完成历史任务, userId: %s, taskId: %d, enumCode: %s", userId, taskID, taskEnumCode)
		}
	}

	return nil
}

// createCompletedTaskProgress 创建已完成且已领取状态的任务进度记录（用于历史数据）
func (s *Service) createCompletedTaskProgress(ctx context.Context, userId string, taskID uint, taskConfig *models.TaskConfig, now time.Time) error {
	expireTime := s.calculateTaskExpireTime(taskConfig.TaskType, now)

	// 创建任务进度记录，统一使用UTC时区
	err := s.taskProgress.CreateTaskProgress(ctx, userId, taskID, expireTime, "UTC")
	if err != nil {
		log.Errorf("自动创建任务进度记录失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	// 获取新创建的任务进度
	taskProgress, err := s.taskProgress.GetTaskProgress(ctx, userId, taskID)
	if err != nil {
		log.Errorf("获取新创建的任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	if taskProgress == nil {
		return innErr.ErrInternalServer
	}

	// 更新进度为完成所需次数
	err = s.taskProgress.UpdateProgress(ctx, userId, taskProgress.ID, taskConfig.CompleteTimes)
	if err != nil {
		log.Errorf("更新历史任务进度失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	// 更新状态为已领取（历史数据设置为已领取状态，避免用户重复领取奖励）
	err = s.taskProgress.UpdateStatus(ctx, userId, taskID, models.UserProgressStatusReceived)
	if err != nil {
		log.Errorf("更新历史任务状态为已领取失败, userId: %s, taskId: %d, error: %v", userId, taskID, err)
		return err
	}

	log.Infof("历史任务已标记为已领取, userId: %s, taskId: %d, progress: %d", userId, taskID, taskConfig.CompleteTimes)
	return nil
}
