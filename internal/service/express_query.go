package service

import (
	"context"

	"github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
)

// QueryExpressInfo 查询快递信息接口
func (s *Service) QueryExpressInfo(ctx context.Context, req *v1.QueryExpressInfoRequest) (*v1.QueryExpressInfoReply, error) {
	// 1. 参数验证
	if req.TrackingNo == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "快递单号不能为空")
	}

	// 2. 从数据库获取快递公司信息
	logistics, err := s.logistics.GetByTrackingNo(ctx, req.TrackingNo)
	if err != nil {
		log.Context(ctx).Errorf("查询物流信息失败: %v", err)
		return nil, err
	}

	if logistics == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "未找到该快递单号的物流信息")
	}

	// 3. 直接从数据库构建返回结果，不主动同步
	log.Context(ctx).Infof("用户查询快递信息: 单号=%s, 仅返回数据库数据", req.TrackingNo)
	return s.buildExpressInfoReplyFromLogisticsDetail(ctx, logistics)
}

// buildExpressInfoReplyFromLogisticsDetail 从logistics_detail表构建返回结果
func (s *Service) buildExpressInfoReplyFromLogisticsDetail(ctx context.Context, logistics *models.Logistics) (*v1.QueryExpressInfoReply, error) {
	// 获取物流详情
	details, err := s.logisticsDetail.FindByTrackingNo(ctx, logistics.TrackingNo)
	if err != nil {
		log.Context(ctx).Errorf("获取物流详情失败: %v", err)
		return nil, err
	}

	var traces []*v1.ExpressTrace
	for _, detail := range details {
		stepDesc := ""
		contextDesc := ""
		if detail.Detail != nil && detail.Detail.V != nil {
			stepDesc = detail.Detail.V.City
			contextDesc = detail.Detail.V.Context
		}

		traces = append(traces, &v1.ExpressTrace{
			Time:     detail.EventTime.Format("2006-01-02 15:04:05"),
			Context:  contextDesc,
			Location: stepDesc,
			Status:   detail.Status,
		})
	}

	// 从logistics主表获取当前状态
	var status v1.LogisticStepStatus
	var isCompleted bool
	var lastUpdate string

	if len(details) > 0 {
		// 使用最新的状态
		status = details[0].Status
		isCompleted = status == v1.LogisticStepStatus_LogisticStepStatusSIGNED
		lastUpdate = details[0].EventTime.Format("2006-01-02 15:04:05")
	} else {
		// 如果没有详情，使用logistics主表的状态
		status = logistics.Status
		isCompleted = status == v1.LogisticStepStatus_LogisticStepStatusSIGNED
		lastUpdate = logistics.UpdatedAt.Format("2006-01-02 15:04:05")
	}

	return &v1.QueryExpressInfoReply{
		TrackingNo:  logistics.TrackingNo,
		CarrierCode: logistics.CarrierCode,
		CarrierName: logistics.CarrierName,
		Status:      status,
		IsCompleted: isCompleted,
		LastUpdate:  lastUpdate,
		Traces:      traces,
	}, nil
}
