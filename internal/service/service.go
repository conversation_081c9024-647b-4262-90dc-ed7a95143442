package service

import (
	"context"

	"api-expo/api/common"
	communityV1 "api-expo/api/community/v1"
	v1 "api-expo/api/expo/v1"
	userV1 "api-expo/api/user_center/v1"
	"api-expo/internal/conf"
	"api-expo/internal/dao"
	"api-expo/internal/util"
	"api-expo/pkg/emailsender"
	"api-expo/pkg/immessagecenter"
	"api-expo/pkg/rabbit"
	"api-expo/pkg/tencentAi"
	"api-expo/pkg/upstream"

	igrpc "github.com/airunny/wiki-go-tools/grpc"
	"github.com/airunny/wiki-go-tools/locker"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
	"github.com/robfig/cron"
)

var ProviderSet = wire.NewSet(NewGreeterService)

type Service struct {
	v1.UnimplementedServiceServer
	business              *conf.Business
	notification          *rabbit.Rabbit
	upstream              *upstream.Client
	imMessageCenter       *immessagecenter.Client
	emailSender           *emailsender.EmailSender
	expo                  *dao.Expo
	expoCommunity         *dao.ExpoCommunity
	expoExhibitor         *dao.ExpoExhibitor
	expoExhibitorEmployee *dao.ExpoExhibitorEmployee
	expoApply             *dao.ExpoExhibitorApply
	expoGuest             *dao.ExpoGuest
	expoGuide             *dao.ExpoGuide
	expoHall              *dao.ExpoHall
	expoImage             *dao.ExpoImage
	expoLive              *dao.ExpoLive
	expoPartner           *dao.ExpoPartner
	expoReview            *dao.ExpoReview
	expoSchedule          *dao.ExpoSchedule
	expoScheduleGuest     *dao.ExpoScheduleGuest
	expoScheduleReserve   *dao.ExpoScheduleReserve
	guest                 *dao.Guest
	redisCli              *redis.Client
	locker                locker.Locker
	tencentAI             *tencentAi.Client
	participant           *dao.Participant
	faceGroup             *dao.FaceGroup
	facePhotoRelation     *dao.FacePhotoRelation
	faceGroupExpo         *dao.FaceGroupExpo
	community             communityV1.WikiCommunityClient
	user                  userV1.UserCenterClient
	comment               *dao.Comment
	commentAt             *dao.CommentAt
	commentImg            *dao.CommentImage
	commentLike           *dao.CommentLike
	commentTrans          *dao.CommentTrans
	config                *dao.Config
	cron                  *cron.Cron
}

func NewGreeterService(
	business *conf.Business,
	redisCli *redis.Client,
	expo *dao.Expo,
	expoCommunity *dao.ExpoCommunity,
	expoExhibitor *dao.ExpoExhibitor,
	expoExhibitorEmployee *dao.ExpoExhibitorEmployee,
	expoApply *dao.ExpoExhibitorApply,
	expoGuest *dao.ExpoGuest,
	expoGuide *dao.ExpoGuide,
	expoHall *dao.ExpoHall,
	expoImage *dao.ExpoImage,
	expoLive *dao.ExpoLive,
	expoPartner *dao.ExpoPartner,
	expoReview *dao.ExpoReview,
	expoSchedule *dao.ExpoSchedule,
	expoScheduleGuest *dao.ExpoScheduleGuest,
	expoScheduleReserve *dao.ExpoScheduleReserve,
	guest *dao.Guest,
	participant *dao.Participant,
	faceGroup *dao.FaceGroup,
	facePhotoRelation *dao.FacePhotoRelation,
	faceGroupExpo *dao.FaceGroupExpo,
	comment *dao.Comment,
	commentAt *dao.CommentAt,
	commentImg *dao.CommentImage,
	commentLike *dao.CommentLike,
	commentTrans *dao.CommentTrans,
	config *dao.Config,
) *Service {
	urlformat.SetFormat(urlformat.NewFormat(business.OssDomain))
	surveyFormat = urlformat.NewFormat(business.SurveyDomain)

	upstreamClient, err := upstream.NewClient(business.Upstream)
	if err != nil {
		panic(err)
	}

	tencentAI, err := tencentAi.NewClient(business.TencentAI)
	if err != nil {
		panic(err)
	}

	imMessageCenterClient, err := immessagecenter.NewClientWithConfig(business.ImMessageCenter)
	if err != nil {
		panic(err)
	}

	emailSender := emailsender.New(business.EmailSender)

	lk, err := locker.NewLockerWithRedis(redisCli)
	if err != nil {
		panic(err)
	}

	notificationRabbit, err := rabbit.NewRabbit(business.NotificationRabbit)
	if err != nil {
		panic(err)
	}

	service := &Service{
		business:              business,
		notification:          notificationRabbit,
		upstream:              upstreamClient,
		imMessageCenter:       imMessageCenterClient,
		emailSender:           emailSender,
		expo:                  expo,
		expoCommunity:         expoCommunity,
		expoExhibitor:         expoExhibitor,
		expoExhibitorEmployee: expoExhibitorEmployee,
		expoApply:             expoApply,
		expoGuest:             expoGuest,
		expoGuide:             expoGuide,
		expoHall:              expoHall,
		expoImage:             expoImage,
		expoLive:              expoLive,
		expoPartner:           expoPartner,
		expoReview:            expoReview,
		expoSchedule:          expoSchedule,
		expoScheduleGuest:     expoScheduleGuest,
		expoScheduleReserve:   expoScheduleReserve,
		guest:                 guest,
		redisCli:              redisCli,
		locker:                lk,
		tencentAI:             tencentAI,
		participant:           participant,
		faceGroup:             faceGroup,
		facePhotoRelation:     facePhotoRelation,
		faceGroupExpo:         faceGroupExpo,
		community:             communityV1.NewWikiCommunityClient(igrpc.DialInsecureWithShort(context.Background(), business.CommunityEndpoint)),
		user:                  userV1.NewUserCenterClient(igrpc.DialInsecureWithShort(context.Background(), business.UserCenterEndpoint)),
		comment:               comment,
		commentAt:             commentAt,
		commentImg:            commentImg,
		commentLike:           commentLike,
		commentTrans:          commentTrans,
		config:                config,
		cron:                  cron.New(),
	}

	err = service.cron.AddFunc(business.NotifyCheckSpec, service.notificationCheck)
	if err != nil {
		panic(err)
	}

	service.cron.Start()
	// 初始化带 Redis 持久化的同步任务管理器
	util.SetGlobalSyncTaskManagerWithRedis(service.redisCli)
	// 设置私聊消息任务
	service.setupMessageEmailTask()
	return service
}

func (s *Service) Healthy(_ context.Context, _ *common.EmptyRequest) (*common.HealthyReply, error) {
	return &common.HealthyReply{
		Status: common.HealthyStatus_HealthyStatusSERVING,
	}, nil
}
