package service

import (
	"context"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
	userCenterv1 "gold_store/api/user_center/v1"
	"gold_store/internal/conf"
	"gold_store/internal/dao"
	"gold_store/pkg/community"
	"gold_store/pkg/express"
	"gold_store/pkg/gold"
	"gold_store/pkg/ikafka"
	"gold_store/pkg/rabbit"
	"gold_store/pkg/vgoods"

	"github.com/airunny/wiki-go-tools/alarm"
	igrpc "github.com/airunny/wiki-go-tools/grpc"
	"github.com/airunny/wiki-go-tools/locker"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-redis/redis/v8"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(NewService)

type Service struct {
	v1.UnimplementedServiceServer
	v1.UnimplementedBackgroundServer
	user             userCenterv1.UserCenterClient
	business         *conf.Business
	locker           locker.Locker
	goods            *dao.Goods
	goodsStatistics  *dao.GoodsStatistics
	goodsSnapshot    *dao.GoodsSnapshot
	order            *dao.Order
	orderItem        *dao.OrderItem
	payment          *dao.Payment
	sku              *dao.SKU
	address          *dao.Address
	country          *dao.Country
	countryTranslate *dao.CountryTranslate
	logistics        *dao.Logistics
	logisticsDetail  *dao.LogisticsDetail
	expressQuery     *dao.ExpressQuery

	//签到
	signRecord     *dao.SignRecord
	signTaskConfig *dao.SignConfig
	signRewardLog  *dao.SignRewardLog

	// 任务相关
	taskConfig      *dao.TaskConfig
	taskProgress    *dao.TaskProgress
	taskRewardIssue *dao.TaskRewardIssue

	ConfluentProducer *kafka.Producer
	TaskConsumer      *TaskConsumer

	goldClient      *gold.Client
	notification    *rabbit.Rabbit
	virtualGoods    *vgoods.Client
	CommunityClient *community.Client
	//礼品券
	userGiftCard       *dao.UserGiftCard
	giftCardUserRemind *dao.GiftCardUserRemind
	giftCardSetting    *dao.GiftCardSetting
	ea                 *dao.EA
	vps                *dao.VPS
	vip                *dao.VIP
	exhibition         *dao.Exhibition
	expressClient      *express.Client
}

func NewService(
	business *conf.Business,
	redisCli *redis.Client,
	goods *dao.Goods,
	goodsStatistics *dao.GoodsStatistics,
	goodsSnapshot *dao.GoodsSnapshot,
	order *dao.Order,
	orderItem *dao.OrderItem,
	payment *dao.Payment,
	sku *dao.SKU,
	address *dao.Address,
	country *dao.Country,
	countryTranslate *dao.CountryTranslate,
	logistics *dao.Logistics,
	logisticsDetail *dao.LogisticsDetail,
	expressQuery *dao.ExpressQuery,
	signRecordDao *dao.SignRecord,
	signTaskConfigDao *dao.SignConfig,
	signRewardLogDao *dao.SignRewardLog,
	taskConfig *dao.TaskConfig,
	taskProgress *dao.TaskProgress,
	rewardIssue *dao.TaskRewardIssue,
	userGiftCard *dao.UserGiftCard,
	giftCardUserRemind *dao.GiftCardUserRemind,
	giftCardSetting *dao.GiftCardSetting,
	ea *dao.EA,
	vps *dao.VPS,
	vip *dao.VIP,
	exhibition *dao.Exhibition,
) (*Service, func()) {
	urlformat.SetFormat(urlformat.NewFormat(business.OssDomain))
	exhibitionFormat = urlformat.NewFormat(business.ExhibitionDomain)
	brokerFormat = urlformat.NewFormat(business.BrokerDomain)
	err := alarm.NewDefaultAlarm(business.FeiShuAlarm)
	if err != nil {
		panic(err)
	}

	expressClient := express.NewClient(&express.Config{
		Key:      business.Express.Key,
		Customer: business.Express.Customer,
		Secret:   business.Express.Secret,
		BaseURL:  business.Express.BaseURL,
	})

	lk, err := locker.NewLockerWithRedis(redisCli)
	if err != nil {
		panic(err)
	}

	// 初始化 confluent-kafka-go 的生产者
	confluentConfig := &kafka.ConfigMap{
		"bootstrap.servers": business.TaskProducer.Brokers[0], // 使用与原有配置相同的 brokers
		"client.id":         "gold_store_confluent",
	}

	confluentProducer, err := kafka.NewProducer(confluentConfig)
	if err != nil {
		panic(err)
	}

	goldClient, err := gold.NewClient(business.GoldDomain)
	if err != nil {
		panic(err)
	}

	communityClient, err := community.NewClient("")
	if err != nil {
		panic(err)
	}

	notificationRabbit, err := rabbit.NewRabbit(business.NotificationRabbit)
	if err != nil {
		panic(err)
	}

	virtualGoodsClient, err := vgoods.NewClient(business.VirtualGoods)
	if err != nil {
		panic(err)
	}

	// 注册支付方式
	service := &Service{
		user:               userCenterv1.NewUserCenterClient(igrpc.DialInsecureWithShort(context.Background(), business.UserCenterEndpoint)),
		business:           business,
		locker:             lk,
		goods:              goods,
		goodsStatistics:    goodsStatistics,
		goodsSnapshot:      goodsSnapshot,
		order:              order,
		orderItem:          orderItem,
		payment:            payment,
		sku:                sku,
		address:            address,
		country:            country,
		countryTranslate:   countryTranslate,
		logistics:          logistics,
		logisticsDetail:    logisticsDetail,
		expressQuery:       expressQuery,
		signRecord:         signRecordDao,
		signTaskConfig:     signTaskConfigDao,
		signRewardLog:      signRewardLogDao,
		taskConfig:         taskConfig,
		taskProgress:       taskProgress,
		taskRewardIssue:    rewardIssue,
		ConfluentProducer:  confluentProducer,
		goldClient:         goldClient,
		notification:       notificationRabbit,
		virtualGoods:       virtualGoodsClient,
		CommunityClient:    communityClient,
		userGiftCard:       userGiftCard,
		giftCardUserRemind: giftCardUserRemind,
		giftCardSetting:    giftCardSetting,
		ea:                 ea,
		vps:                vps,
		vip:                vip,
		exhibition:         exhibition,
		expressClient:      expressClient,
	}

	service.TaskConsumer = NewTaskConsumer(business, service, nil)
	if err = service.TaskConsumer.Start(); err != nil {
		panic(err)
	}
	// 积分支付方式
	registerPointsPay(goldClient)
	// 礼品卡兑换
	registerGiftCard(service)
	// 任务兑换
	registerTask(service)
	// 订单合并
	consumers := service.initOrderMerge(business)

	return service, func() {
		if service.ConfluentProducer != nil {
			service.ConfluentProducer.Close()
		}

		if service.TaskConsumer != nil {
			service.TaskConsumer.Stop()
		}

		for _, consumer := range consumers {
			_ = consumer.Close()
		}
	}
}

func (s *Service) initOrderMerge(business *conf.Business) []*ikafka.ConfluentConsumer {
	// ea
	eaConsumer, err := ikafka.NewConfluentConsumer(business.OrderMerge.EAConsumer)
	if err != nil {
		panic(err)
	}
	eaConsumer.Start(s.orderFromEA)

	// vps
	vpsConsumer, err := ikafka.NewConfluentConsumer(business.OrderMerge.VPSConsumer)
	if err != nil {
		panic(err)
	}
	vpsConsumer.Start(s.orderFromVPS)

	// vip
	vipConsumer, err := ikafka.NewConfluentConsumer(business.OrderMerge.VIPConsumer)
	if err != nil {
		panic(err)
	}
	vipConsumer.Start(s.orderFromVIP)

	// 展会
	exhibitionConsumer, err := ikafka.NewConfluentConsumer(business.OrderMerge.ExhibitionConsumer)
	if err != nil {
		panic(err)
	}
	exhibitionConsumer.Start(s.orderFromExhibition)
	return []*ikafka.ConfluentConsumer{eaConsumer, vpsConsumer, vipConsumer, exhibitionConsumer}
}

func (s *Service) Healthy(_ context.Context, _ *common.EmptyRequest) (*common.HealthyReply, error) {
	return &common.HealthyReply{
		Status: common.HealthyStatus_HealthyStatusSERVING,
	}, nil
}
