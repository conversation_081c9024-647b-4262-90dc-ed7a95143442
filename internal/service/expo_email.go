package service

import (
	"context"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"api-expo/internal/util"
	"api-expo/pkg/emailsender"
	"api-expo/pkg/immessagecenter"

	"github.com/go-kratos/kratos/v2/log"
)

// setupMessageEmailTask 设置私信邮件任务
func (s *Service) setupMessageEmailTask() {
	if s.business.MessageEmailTask == nil || !s.business.MessageEmailTask.Enabled {
		log.Info("私信邮件任务未启用")
		return
	}

	cron := util.NewEmailCron(s)
	cron.Start()

	log.Info("私信邮件任务已启动")
}

// SendMessageEmailsInternal 发送私聊邮件
func (s *Service) SendMessageEmailsInternal(ctx context.Context) error {
	return s.SendWikiExpoEmails(ctx)
}

// SendMessageEmails 发送私聊邮件
func (s *Service) SendMessageEmails(ctx context.Context) error {
	return s.SendWikiExpoEmails(ctx)
}

// generateEmailHTML 生成邮件HTML内容
func (s *Service) generateEmailHTML(data EmailTemplateData) (string, error) {
	templatePath := filepath.Join("internal", "util", "email_template.html")
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		return "", fmt.Errorf("读取HTML模板文件失败: %w", err)
	}

	tmpl, err := template.New("email").Parse(string(templateContent))
	if err != nil {
		return "", fmt.Errorf("解析HTML模板失败: %w", err)
	}

	var buf strings.Builder
	err = tmpl.Execute(&buf, data)
	if err != nil {
		return "", fmt.Errorf("执行HTML模板失败: %w", err)
	}

	return buf.String(), nil
}

// GenerateWikiExpoEmailContent 生成WikiEXPO邮件内容
func (s *Service) GenerateWikiExpoEmailContent(ctx context.Context) ([]EmailTemplate, error) {
	// 获取活跃展会
	expos, err := s.expo.FindProcessing(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取展会失败: %w", err)
	}
	if len(expos) == 0 {
		log.Context(ctx).Info("没有活跃展会")
		return []EmailTemplate{}, nil
	}

	var allEmailTemplates []EmailTemplate
	cutoffDate := time.Now().AddDate(0, 0, -s.business.MessageEmailTask.MessageDaysRange)

	// 每个展会
	for _, expo := range expos {
		log.Context(ctx).Infof("开始处理展会: %s (ID: %d)", expo.Name, expo.ID)

		// 获取该展会的消息
		req := &immessagecenter.ExhibitionRecordListRequest{
			ExhibitionId: fmt.Sprintf("%d", expo.ID),
		}
		resp, err := s.imMessageCenter.GetExhibitionRecordList(ctx, req)
		if err != nil {
			log.Context(ctx).Errorf("获取展会 %s 消息失败: %v", expo.Name, err)
			continue
		}

		// 按用户分组该展会的消息
		userMessages := make(map[string][]immessagecenter.ExhibitionRecord)
		for _, msg := range resp.Result {
			if msg.SendDate.After(cutoffDate) {
				userMessages[msg.TargetId] = append(userMessages[msg.TargetId], msg)
			}
		}

		if len(userMessages) == 0 {
			log.Context(ctx).Infof("展会 %s 没有近期消息", expo.Name)
			continue
		}

		expoEmailTemplates, err := s.generateEmailTemplatesForExpo(ctx, expo, userMessages)
		if err != nil {
			log.Context(ctx).Errorf("生成展会 %s 邮件模板失败: %v", expo.Name, err)
			continue
		}
		allEmailTemplates = append(allEmailTemplates, expoEmailTemplates...)

		log.Context(ctx).Infof("展会 %s 生成了 %d 个邮件模板", expo.Name, len(expoEmailTemplates))
	}

	log.Context(ctx).Infof("总共生成了 %d 个邮件模板", len(allEmailTemplates))
	return allEmailTemplates, nil
}

// generateEmailTemplatesForExpo 为单个展会生成邮件模板
func (s *Service) generateEmailTemplatesForExpo(ctx context.Context, expo *models.Expo, userMessages map[string][]immessagecenter.ExhibitionRecord) ([]EmailTemplate, error) {
	var emailTemplates []EmailTemplate

	// 批量查询用户信息 - 获取所有用户ID
	var userIds []string
	for targetId := range userMessages {
		userIds = append(userIds, targetId)
	}

	// 批量查询参与者信息（包含邮箱和姓名）
	userEmailMap := make(map[string]string)
	userParticipantMap := make(map[string]*models.Participant)

	// 批量查询参与者信息
	participants, err := s.participant.FindByUserIdsAndExpoId(context.Background(), userIds, expo.ID)
	if err != nil {
		log.Context(ctx).Errorf("批量查询展会 %d 参与者信息失败: %v", expo.ID, err)
		return nil, err
	}

	// 转换为map
	for _, participant := range participants {
		if participant.Email != "" {
			userEmailMap[participant.UserId] = participant.Email
			userParticipantMap[participant.UserId] = participant
		}
	}

	allSenderIds := make(map[string]bool)
	for _, messages := range userMessages {
		for _, msg := range messages {
			allSenderIds[msg.UserId] = true
		}
	}
	var senderUserIds []string
	for userId := range allSenderIds {
		senderUserIds = append(senderUserIds, userId)
	}

	userInfoMap, err := s.fetchUserInfoBatch(context.Background(), senderUserIds)
	if err != nil {
		log.Context(ctx).Errorf("批量获取用户信息失败: %v", err)
		userInfoMap = make(map[string]*UserInfo) // 使用空map，后续会fallback到默认头像
	}

	for targetId, messages := range userMessages {
		// 检查是否有邮箱地址
		userEmail, hasEmail := userEmailMap[targetId]
		if !hasEmail || userEmail == "" {
			log.Context(ctx).Infof("用户 %s 没有邮箱地址，跳过发送邮件", targetId)
			continue
		}

		// 排序消息
		sort.Slice(messages, func(i, j int) bool {
			return messages[i].SendDate.BeforeCustom(messages[j].SendDate)
		})

		// 去重消息（相同发送者只保留最新一条）
		senderLatest := make(map[string]immessagecenter.ExhibitionRecord)
		for _, msg := range messages {
			if existing, exists := senderLatest[msg.UserId]; !exists || msg.SendDate.AfterCustom(existing.SendDate) {
				senderLatest[msg.UserId] = msg
			}
		}

		var uniqueMessages []immessagecenter.ExhibitionRecord
		for _, msg := range senderLatest {
			uniqueMessages = append(uniqueMessages, msg)
		}

		if len(uniqueMessages) == 0 {
			continue
		}

		// 按时间排序
		sort.Slice(uniqueMessages, func(i, j int) bool {
			return uniqueMessages[i].SendDate.BeforeCustom(uniqueMessages[j].SendDate)
		})

		fanAvatars := make([]string, 0, 6)
		for i := 0; i < len(uniqueMessages) && i < 6; i++ {
			senderUserId := uniqueMessages[i].UserId
			if userInfo, exists := userInfoMap[senderUserId]; exists && userInfo.AvatarAddress != "" {
				fanAvatars = append(fanAvatars, userInfo.AvatarAddress)
			} else {
				fanAvatars = append(fanAvatars, "")
			}
		}

		// 格式化展会时间
		expoTimeStr := expo.Start.Format("2006-01-02")
		if !expo.End.IsZero() && expo.End.Format("2006-01-02") != expo.Start.Format("2006-01-02") {
			expoTimeStr = fmt.Sprintf("%s 至 %s", expo.Start.Format("2006-01-02"), expo.End.Format("2006-01-02"))
		}

		// 获取用户真实姓名
		participant := userParticipantMap[targetId]
		userName := util.FullName(participant.LastName, participant.FirstName)
		if userName == "" {
			continue
		}

		templateData := EmailTemplateData{
			ExpoTitle:          expo.Name,
			ExpoTime:           expoTimeStr,
			ExpoLocation:       expo.Location,
			FanCount:           len(uniqueMessages),
			UserName:           userName,
			FanAvatars:         fanAvatars,
			ViewMessagesURL:    fmt.Sprintf("https://www.wikifx.com/expo/%d/messages?target=%s", expo.ID, targetId),
			BackgroundImageURL: "https://via.placeholder.com/600x150/007cba/ffffff?text=WikiEXPO",
		}

		// 生成HTML内容
		htmlContent, err := s.generateEmailHTML(templateData)
		if err != nil {
			log.Context(ctx).Errorf("生成HTML内容失败: %v", err)
			continue
		}

		emailTemplates = append(emailTemplates, EmailTemplate{
			Subject:      fmt.Sprintf("WikiEXPO %s - %d位粉丝正在期盼您的回复", expo.Name, len(uniqueMessages)),
			UserId:       targetId,
			Recipient:    userEmail,
			HTMLContent:  htmlContent,
			MessageCount: len(uniqueMessages),
			ExpoID:       expo.ID,
			ExpoName:     expo.Name,
		})
	}

	return emailTemplates, nil
}

// SendWikiExpoEmails 发送WikiEXPO邮件
func (s *Service) SendWikiExpoEmails(ctx context.Context) error {
	config := s.business.MessageEmailTask
	lockTimeout := time.Duration(config.LockTimeoutMinutes) * time.Minute
	release, err := s.locker.TryLock(ctx, config.LockKey+"_wikiexpo", lockTimeout)
	if err != nil {
		return fmt.Errorf("获取锁失败: %w", err)
	}
	defer release()

	// 生成WikiEXPO邮件内容
	emailTemplates, err := s.GenerateWikiExpoEmailContent(ctx)
	if err != nil {
		return err
	}

	// 发送邮件
	for i, t := range emailTemplates {
		log.Context(ctx).Infof(
			"发送WikiEXPO邮件 %d/%d 到用户: %s (邮箱: %s) | 展会: %s (ID: %d) | 主题: %s | 消息数: %d",
			i+1, len(emailTemplates), t.UserId, t.Recipient,
			t.ExpoName, t.ExpoID,
			t.Subject,
			t.MessageCount,
		)

		// 发送邮件
		emailTemplate := &emailsender.SendEmailTemplate{
			UserId:       t.UserId,
			UserEmail:    t.Recipient,
			LanguageCode: "en",
			Subject:      t.Subject,
			HTMLContent:  t.HTMLContent,
		}

		resp, err := s.emailSender.SendTemplateEmail(ctx, emailTemplate)
		if err != nil {
			log.Context(ctx).Errorf("发送邮件失败: %v", err)
			continue
		}

		if !resp.Succeed {
			log.Context(ctx).Errorf("邮件服务返回失败: %s", resp.Message)
		} else {
			log.Context(ctx).Info("邮件发送成功")
		}
	}

	log.Context(ctx).Infof("WikiEXPO邮件发送完成，共处理 %d 封邮件", len(emailTemplates))
	return nil
}

// EmailTemplate 邮件模板
type EmailTemplate struct {
	Subject      string
	UserId       string // 用户ID
	Recipient    string // 用户邮箱
	HTMLContent  string
	MessageCount int
	ExpoID       int64  // 展会ID
	ExpoName     string // 展会名称
}

// EmailTemplateData WikiEXPO邮件模板数据
type EmailTemplateData struct {
	ExpoTitle          string   // 会议主题
	ExpoTime           string   // 会议时间
	ExpoLocation       string   // 会议地点
	FanCount           int      // 粉丝数量
	UserName           string   // 用户名称
	FanAvatars         []string // 粉丝头像列表
	ViewMessagesURL    string   // 查看消息链接
	BackgroundImageURL string   // 背景图链接
}

// SendMessageEmailsAPI 发送展会邮件的API接口
func (s *Service) SendMessageEmailsAPI(ctx context.Context, in *v1.SendMessageEmailsRequest) (*v1.SendMessageEmailsReply, error) {
	l := log.Context(ctx)
	l.Infof("SendMessageEmails API called with request_id: %s", in.GetRequestId())
	err := s.SendMessageEmails(ctx)
	if err != nil {
		l.Errorf("SendMessageEmails failed: %v", err)
		return &v1.SendMessageEmailsReply{
			Success:       false,
			Message:       "邮件发送失败: " + err.Error(),
			ErrorMessages: []string{err.Error()},
		}, nil
	}

	l.Info("SendMessageEmails completed successfully")
	return &v1.SendMessageEmailsReply{
		Success:       true,
		Message:       "邮件发送成功",
		ErrorMessages: []string{},
	}, nil
}
