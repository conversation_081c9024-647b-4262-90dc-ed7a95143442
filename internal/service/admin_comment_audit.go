package service

import (
	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"context"
	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"time"
)

func (s *Service) CommentPageList(ctx context.Context, request *v1.AdminCommentPageListRequest) (*v1.AdminCommentPageListReply, error) {
	StausMap := map[int32]string{
		0: "未知",
		1: "待审核",
		2: "审核通过",
		3: "审核未通过",
	}
	l := log.Context(ctx)
	list, total, err := s.comment.AdminPageCommentPageList(ctx, request.ExpoId, int(request.Status), int(request.Page), int(request.Size), request.Keyword, int(request.TimeType), request.StartTime, request.EndTime)
	if err != nil {
		l.Error(ctx, "CommentPageList error", err)
		return nil, innErr.ErrBadRequest
	}

	items := make([]*v1.AdminCommentPageListItem, 0, request.Size)
	if len(list) == 0 {
		return &v1.AdminCommentPageListReply{
			List:  items,
			Total: total,
		}, err
	}
	expo_ids := lo.Map(list, func(item *models.ExpoComment, index int) int64 {
		return item.ExpoID
	})
	commentIds := lo.Map(list, func(item *models.ExpoComment, index int) string {
		return item.ID
	})
	images, err := s.commentImg.GetListByCommentId(ctx, commentIds)
	if err != nil {
		l.Error(ctx, "CommentPageList error", err)
		return nil, innErr.ErrBadRequest
	}
	trans_zhcn, err := s.commentTrans.GetListByCommentId(ctx, commentIds, "zh-cn")
	if err != nil {
		l.Error(ctx, "CommentPageList error", err)
		return nil, innErr.ErrBadRequest
	}

	expoList, err := s.expo.FindByIds(ctx, expo_ids)
	if err != nil {
		l.Error(ctx, "CommentPageList error", err)
		return nil, innErr.ErrBadRequest
	}
	for _, v := range list {
		expoName := ""
		expo, isSearch := lo.Find(expoList, func(item *models.Expo) bool {
			return item.ID == v.ExpoID
		})
		if isSearch {
			expoName = expo.Name
		}
		tranContent := ""
		transContent, isSearch := lo.Find(trans_zhcn, func(item *models.ExpoCommentTran) bool {
			return item.CommentID == v.ID
		})
		if isSearch {
			tranContent = transContent.Content
		}
		AuditTime := ""
		if v.AuditAt.Valid {
			AuditTime = v.AuditAt.Time.Add(time.Hour * 8).Format(time.DateTime)
		}
		item := &v1.AdminCommentPageListItem{
			CommentId:    v.ID,
			UserId:       v.UserID,
			Content:      v.Content,
			CreatedAt:    v.CreatedAt.Add(time.Hour * 8).Format(time.DateTime),
			UpdatedAt:    v.UpdatedAt.Add(time.Hour * 8).Format(time.DateTime),
			AuditTime:    AuditTime,
			LanguageCode: v.LanguageCode,
			CountryCode:  v.CountryCode,
			Status:       StausMap[v.Status],
			AuditUser:    v.AuditName,
			Tran_Content: tranContent,
			RefuseReason: v.RefuseReason,
			Images:       createImage(images, v.ID),
			ExpoId:       v.ExpoID,
			ExpoName:     expoName,
		}
		items = append(items, item)
	}
	return &v1.AdminCommentPageListReply{
		List:  items,
		Total: total,
	}, err
}

// 审核互动
func (s *Service) CommentAudit(ctx context.Context, request *v1.CommentAuditRequest) (*v1.CommentAuditReply, error) {
	l := log.Context(ctx)
	err := s.comment.AdminCommentAudit(ctx, request.CommentId, int(request.Status), request.AuditUser, request.RefuseReason)
	if err != nil {
		l.Error(ctx, "AdminCommentAudit error", err)
		return nil, innErr.ErrBadRequest
	}
	return &v1.CommentAuditReply{}, nil
}

// 翻译列表
func (s *Service) CommentTransList(ctx context.Context, request *v1.CommentTransListRequest) (*v1.CommentTransListReply, error) {
	l := log.Context(ctx)
	list, err := s.commentTrans.GetListSingleByCommentId(ctx, request.CommentId)
	if err != nil {
		l.Error(ctx, "GetListSingleByCommentId error", err)
		return nil, innErr.ErrBadRequest
	}
	item := make([]*v1.CommentTransListItem, 0, 20)
	for _, v := range list {
		item = append(item, &v1.CommentTransListItem{
			TransId:      v.ID,
			Content:      v.Content,
			LanguageCode: v.LanguageCode,
			TransName:    v.TransUser,
			Time:         v.CreatedAt.Add(time.Hour * 8).Format(time.DateTime),
		})
	}
	return &v1.CommentTransListReply{
		List: item,
	}, nil
}

// 翻译
func (s *Service) CommentTranslateCallBack(ctx context.Context, request *v1.CommentTranslateCallBackRequest) (*v1.CommentTranslateCallBackReply, error) {
	l := log.Context(ctx)
	now := time.Now().UTC()
	if len(request.Text) == 0 {
		return nil, innErr.ErrBadRequest
	}
	err := s.commentTrans.UpdateOrInsertTans(ctx, request.OperationId, request.To, models.ExpoCommentTran{
		CommentID:    request.OperationId,
		LanguageCode: request.To,
		Content:      request.Text[0],
		CreatedAt:    now,
		UpdatedAt:    now,
	})
	if err != nil {
		l.Error(ctx, "UpdateOrInsertTans error", err)
		return nil, innErr.ErrBadRequest
	}
	return &v1.CommentTranslateCallBackReply{
		Message: "success",
	}, nil

}

// 修改翻译
func (s *Service) EditCommentTranslate(ctx context.Context, request *v1.EditCommentTranslateRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.commentTrans.UpdateTrans(ctx, request.TransId, request.Content)
	if err != nil {
		l.Error(ctx, "UpdateTrans error", err)
		return nil, innErr.ErrBadRequest
	}
	return &common.EmptyReply{}, nil
}

// 帖子详情
func (s *Service) CommentDetail(ctx context.Context, request *v1.CommentDetailRequest) (*v1.CommentDetailReply, error) {
	l := log.Context(ctx)
	single, err := s.comment.GetCommentSingle(ctx, request.CommentId)
	if err != nil {
		l.Error(ctx, "GetCommentSingle error", err)
		return nil, innErr.ErrBadRequest
	}
	return &v1.CommentDetailReply{
		CommentId:    single.ID,
		Content:      single.Content,
		UserId:       single.UserID,
		LanguageCode: single.LanguageCode,
	}, nil
}
