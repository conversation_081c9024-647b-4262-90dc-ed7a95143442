package service

import (
	v1 "gold_store/api/gold_store/v1"

	"github.com/airunny/wiki-go-tools/urlformat"
)

const (
	goodsLabelColor      = "#D97716"
	emsIcon              = "https://img.fx696.com/gold_store/ems3x.png_wiki-template-global"
	statisticReportImage = "/gold_store/report3x.png"
)

var (
	supportedLanguageCodes = []string{
		"zh-cn", "zh", "en", "de", "fil", "fr", "hi", "id", "it", "ja",
		"ko", "pt", "ru", "es", "th", "vi", "ms", "ar", "tr",
	}
	exhibitionFormat urlformat.Format
	brokerFormat     urlformat.Format
	eaStaticImage    = &v1.Image{
		Url:    "/gold_store/en.png",
		Width:  264,
		Height: 264,
	}
	vpsStaticImage = &v1.Image{
		Url:    "/gold_store/vps.png",
		Width:  264,
		Height: 264,
	}
	vipStaticImage = &v1.Image{
		Url:    "/gold_store/vip.png",
		Width:  264,
		Height: 264,
	}

	currencySymbols = map[string]struct{}{
		"$": {}, // 美元
		"¥": {}, // 日元/人民币（全角）
		"￥": {}, // 人民币（中文输入法常见）
		"€": {}, // 欧元
		"£": {}, // 英镑
		"₽": {}, // 卢布
		"₩": {}, // 韩元
		"₹": {}, // 印度卢比
		"₺": {}, // 土耳其里拉
		"₴": {}, // 乌克兰格里夫纳
		"₿": {}, // 比特币（BTC）
		"¢": {}, // 分（美分）
		"¤": {}, // 通用货币符号
	}
)

var (
	urlTemplate = urlformat.WithTemplate("_wiki-template-global")
	labels      = []*v1.Label{
		{
			Id:       "L100000",
			Name:     "闪电直发",
			Category: v1.LabelCategory_LABEL_CATEGORY_IMAGE,
			Values: map[string]string{
				"zh":    "/gold_store/express-shipping-zh1.png",
				"zh-hk": "/gold_store/express-shipping-zh1.png",
				"zh-tw": "/gold_store/express-shipping-zh1.png",
				"zh-cn": "/gold_store/express-shipping-zh-cn1.png",
				"en":    "/gold_store/express-shipping-en1.png",
			},
		},
		{
			Id:       "L200000",
			Name:     "独家出版",
			Category: v1.LabelCategory_LABEL_CATEGORY_IMAGE,
			Values: map[string]string{
				"zh":    "/gold_store/exclusive-zh1.png",
				"zh-hk": "/gold_store/exclusive-zh1.png",
				"zh-tw": "/gold_store/exclusive-zh1.png",
				"zh-cn": "/gold_store/exclusive-zh-cn1.png",
				"en":    "/gold_store/exclusive-en1.png",
			},
		},
	}

	goodsStaticSpecs = []*v1.GoodsStaticSpecOfCategory{
		{
			Category: v1.GoodsCategory_GOODS_CATEGORY_PHYSICAL,
			Specs: []*v1.GoodsStaticSpec{
				{
					Id:   "SP00001",
					Name: "颜色",
					Key:  "59859",
				},
				{
					Id:   "SP00002",
					Name: "尺寸",
					Key:  "59860",
				},
				{
					Id:   "SP00003",
					Name: "语言",
					Key:  "59927",
				},
			},
		},
		{
			Category: v1.GoodsCategory_GOODS_CATEGORY_VIP,
			Specs: []*v1.GoodsStaticSpec{
				{
					Id:   "SP00004",
					Name: "时间",
					Key:  "60017",
					Units: []*v1.GoodsStaticSpecUnit{
						{
							Id:       "60019",
							Name:     "d",
							ShowName: "天",
							Key:      "60019",
						},
						{
							Id:       "60021",
							Name:     "m",
							ShowName: "月",
							Key:      "60021",
						},
						{
							Id:       "60023",
							Name:     "y",
							ShowName: "年",
							Key:      "60023",
						},
					},
				},
			},
		},
		{
			Category: v1.GoodsCategory_GOODS_CATEGORY_VPS,
			Specs: []*v1.GoodsStaticSpec{
				{
					Id:   "SP00005",
					Name: "配置",
					Key:  "59929",
					Values: []*v1.GoodsStaticSpecValue{
						{
							Id:   "L1",
							Name: "1*CPU,1G*RAM,40G*HDD,1M*ADSL",
						},
						{
							Id:   "L2",
							Name: "2*CPU,2G*RAM,60G*HDD,2M*ADSL",
						},
						{
							Id:   "L3",
							Name: "1*CPU,1G*RAM,50G*HDD,2M*ADSL",
						},
						{
							Id:   "L4",
							Name: "1*CPU,1G*RAM,60G*HDD,2M*ADSL",
						},
						{
							Id:   "L5",
							Name: "1*CPU,1G*RAM,70G*HDD,2M*ADSL",
						},
						{
							Id:   "L6",
							Name: "1*CPU,2G*RAM,70G*HDD,2M*ADSL",
						},
						{
							Id:   "L7",
							Name: "2*CPU,2G*RAM,70G*HDD,2M*ADSL",
						},
					},
				},
				{
					Id:   "SP00006",
					Name: "区域",
					Key:  "60091",
					Values: []*v1.GoodsStaticSpecValue{
						{
							Id:   "cn-bj2-05",
							Name: "北京",
							Translate: map[string]string{
								"ar":    "بكين",
								"de":    "Peking",
								"en":    "Beijing",
								"es":    "Pequim",
								"fil":   "Beijing",
								"fr":    "Pékin",
								"hi":    "बीजिंग",
								"id":    "Beijing",
								"it":    "Pechino",
								"ja":    "北京",
								"ko":    "베이징",
								"ms":    "Beijing",
								"pt":    "Beijing",
								"ru":    "Пекин",
								"th":    "ปักกิ่ง",
								"tr":    "Pekin",
								"vi":    "Bắc Kinh",
								"zh":    "北京",
								"zh-cn": "北京",
							},
						},
						{
							Id:   "hk-02",
							Name: "香港",
							Translate: map[string]string{
								"ar":    "هونغ كونغ",
								"de":    "Hongkong",
								"en":    "Hong Kong",
								"es":    "Hong Kong",
								"fil":   "Hong Kong",
								"fr":    "Hong Kong",
								"hi":    "हॉगकॉग",
								"id":    "Hong Kong",
								"it":    "Hong Kong",
								"ja":    "香港",
								"ko":    "홍콩",
								"ms":    "Hong Kong",
								"pt":    "Hong Kong",
								"ru":    "Гонконг",
								"th":    "ฮ่องกง",
								"tr":    "Hong Kong",
								"vi":    "Hong Kong",
								"zh":    "香港",
								"zh-cn": "香港",
							},
						},
						{
							Id:   "uae-dubai-01",
							Name: "迪拜",
							Translate: map[string]string{
								"ar":    "دبي",
								"de":    "Dubai",
								"en":    "Dubai",
								"es":    "Dubai",
								"fil":   "Dubai",
								"fr":    "Dubaï",
								"hi":    "दुबई",
								"id":    "Dubai",
								"it":    "Dubai",
								"ja":    "ドバイ",
								"ko":    "두바이",
								"ms":    "Dubai",
								"pt":    "Dubai",
								"ru":    "Дубай",
								"th":    "ดูไบ",
								"tr":    "Dubai",
								"vi":    "Dubai",
								"zh":    "迪拜",
								"zh-cn": "迪拜",
							},
						},
						{
							Id:   "tw-tp-01",
							Name: "台北",
							Translate: map[string]string{
								"ar":    "تايبيه",
								"de":    "Taipei",
								"en":    "Taipei",
								"es":    "Taipei",
								"fil":   "Taipei",
								"fr":    "Taïpei",
								"hi":    "ताइपे",
								"id":    "Taipei",
								"it":    "Taipei",
								"ja":    "台北",
								"ko":    "타이페이",
								"ms":    "Taipei",
								"pt":    "Taipei",
								"ru":    "Тайбэй",
								"th":    "ไทเป",
								"tr":    "Taipei",
								"vi":    "Taipei",
								"zh":    "臺北",
								"zh-cn": "台北",
							},
						},
						{
							Id:   "th-bkk-02",
							Name: "曼谷",
							Translate: map[string]string{
								"ar":    "بانكوك",
								"de":    "Bangkok",
								"en":    "Bangkok",
								"es":    "Bangkok",
								"fil":   "Bangkok",
								"fr":    "Bangkok",
								"hi":    "बैंकाक",
								"id":    "Bangkok",
								"it":    "Bangkok",
								"ja":    "バンコク",
								"ko":    "방콕",
								"ms":    "Bangkok",
								"pt":    "Bangkok",
								"ru":    "Бангкок",
								"th":    "กรุงเทพมหานคร",
								"tr":    "Bangkok",
								"vi":    "Bangkok",
								"zh":    "曼谷",
								"zh-cn": "曼谷",
							},
						},
						{
							Id:   "jpn-tky-01",
							Name: "东京",
							Translate: map[string]string{
								"ar":    "طوكيو",
								"de":    "Tokio",
								"en":    "Tokyo",
								"es":    "Tokyo",
								"fil":   "Tokyo",
								"fr":    "Tokyo",
								"hi":    "टोक्यो",
								"id":    "Tokyo",
								"it":    "Tokyo",
								"ja":    "東京",
								"ko":    "도쿄",
								"ms":    "Tokyo",
								"pt":    "Tóquio",
								"ru":    "Токио",
								"th":    "โตเกียว",
								"tr":    "Tokyo",
								"vi":    "Tokyo",
								"zh":    "東京",
								"zh-cn": "东京",
							},
						},
						{
							Id:   "afr-nigeria-01",
							Name: "拉各斯",
							Translate: map[string]string{
								"ar":    "لاغوس",
								"de":    "Lagos",
								"en":    "Lagos",
								"es":    "Lagos",
								"fil":   "Lagos",
								"fr":    "Lagos",
								"hi":    "लागोस",
								"id":    "Lagos",
								"it":    "Lagos",
								"ja":    "ラゴス",
								"ko":    "라고스",
								"ms":    "Lagos",
								"pt":    "Lagos",
								"ru":    "Лагос",
								"th":    "ลากอส",
								"tr":    "Lagos",
								"vi":    "Lagos",
								"zh":    "拉各斯",
								"zh-cn": "拉各斯",
							},
						},
						{
							Id:   "vn-sng-01",
							Name: "胡志明市",
							Translate: map[string]string{
								"ar":    "مدينة هو تشي مينه",
								"de":    "Ho-Chi-Minh-Stadt",
								"en":    "Ho Chi Ming City",
								"es":    "Ciudad de Ho Chi Minh",
								"fil":   "Lungsod ng Ho Chi Minh",
								"fr":    "Ho Chi Minh-Ville",
								"hi":    "हो ची मिंन शहर",
								"id":    "Kota Ho Chi Minh",
								"it":    "Ho Chi Minh City",
								"ja":    "ホーチミン市",
								"ko":    "호치민시",
								"ms":    "Bandar Ho Chi Minh",
								"pt":    "Cidade de Ho Chi Minh",
								"ru":    "Хошимин",
								"th":    "โฮจิมินห์ซิตี้",
								"tr":    "Ho Şi Ming Şehri",
								"vi":    "Thành phố Hồ Chí Minh",
								"zh":    "胡志明市",
								"zh-cn": "胡志明市",
							},
						},
					},
				},
				{
					Id:   "SP00007",
					Name: "语言",
					Key:  "59927",
					Values: []*v1.GoodsStaticSpecValue{
						{
							Id:   "1",
							Name: "简体中文",
						},
						{
							Id:   "2",
							Name: "English",
						},
						{
							Id:   "3",
							Name: "繁体中文",
						},
						{
							Id:   "4",
							Name: "日本語",
						},
						{
							Id:   "5",
							Name: "ภาษาไทย",
						},
						{
							Id:   "6",
							Name: "Tiếng Việt",
						},
						{
							Id:   "7",
							Name: "لغة عربية",
						},
						{
							Id:   "8",
							Name: "Français",
						},
					},
				},
			},
		},
	}
)

func getStaticSpecByCategory(category v1.GoodsCategory) (
	map[string]*v1.GoodsStaticSpec,
	map[string]map[string]*v1.GoodsStaticSpecValue,
	map[string]map[string]*v1.GoodsStaticSpecUnit,
	bool) {

	var (
		staticSpecs []*v1.GoodsStaticSpec
		matched     bool
	)

	for _, value := range goodsStaticSpecs {
		if value.Category == category {
			staticSpecs = value.Specs
			matched = true
			break
		}
	}

	var (
		staticSpecMap      = make(map[string]*v1.GoodsStaticSpec, len(staticSpecs))
		staticSpecValueMap = make(map[string]map[string]*v1.GoodsStaticSpecValue, len(staticSpecs))
		staticSpecUnitMap  = make(map[string]map[string]*v1.GoodsStaticSpecUnit, len(staticSpecs))
	)

	for _, spec := range staticSpecs {
		var (
			valueMap = make(map[string]*v1.GoodsStaticSpecValue, len(spec.Values))
			unitMap  = make(map[string]*v1.GoodsStaticSpecUnit, len(spec.Units))
		)

		for _, value := range spec.Values {
			valueMap[value.Id] = value
		}

		for _, unit := range spec.Units {
			unitMap[unit.Id] = unit
		}
		staticSpecMap[spec.Id] = spec
		staticSpecValueMap[spec.Id] = valueMap
		staticSpecUnitMap[spec.Id] = unitMap
	}
	return staticSpecMap, staticSpecValueMap, staticSpecUnitMap, matched
}
