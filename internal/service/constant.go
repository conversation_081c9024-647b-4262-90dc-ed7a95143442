package service

import (
	"strings"
	"unicode"
	"unicode/utf8"

	v1 "api-expo/api/expo/v1"

	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/mozillazg/go-pinyin"
)

const (
	speakerId      = "speakers"
	professionalId = "professionals"
	investorsId    = "investors"
	otherId        = "other"
	exhibitorId    = "exhibitors"

	imageLiveIcon = "/expologo/image_live.png"
	highLightIcon = "/expologo/highlight.png"

	facebookIcon  = "/expologo/facebook.png"
	twitterIcon   = "/expologo/x.png"
	linkedinIcon  = "/expologo/linkedin.png"
	instagramIcon = "/expologo/instagram.png"
	redditIcon    = "/expologo/reddit.png"
	youtubeIcon   = "/expologo/youtube.png"
	telegramIcon  = "/expologo/telegram.png"
	tiktokIcon    = "/expologo/tiktop.png"
	wechatIcon    = "/expologo/wechat.png"
	whatsAppIcon  = "/expologo/whatsapp.png"

	exhibitorIcon    = "/expologo/icon-exhibiter.png"    // 参展商
	investorIcon     = "/expologo/icon-investor.png"     // 投资者
	otherIcon        = "/expologo/icon-other.png"        // 其他
	speakerIcon      = "/expologo/icon-speaker.png"      // 嘉宾
	professionalIcon = "/expologo/icon-professional.png" // 从业者

	defaultLiveAppId = "1400638854"

	temporaryUserFlag = true
)

var (
	surveyFormat       urlformat.Format
	supportedLanguages = []string{"zh-cn", "zh", "en", "de", "fil", "fr", "hi", "id", "it", "ja", "ko", "pt", "ru", "es", "th", "vi", "ms", "ar", "tr"}

	hostRoleIcon      = "https://expoliveimgs.zy223.com/expologo/host_role.png_wiki-template-global"
	speakerRoleIcon   = "https://expoliveimgs.zy223.com/expologo/speaker_role.png_wiki-template-global"
	sponsorLevelItems = []*v1.SponsorLevelItem{
		{
			Level: v1.SponsorLevel_SponsorLevel_SILVER,
			Icon:  "https://expoliveimgs.zy223.com/expologo/silver.png_wiki-template-global",
			Name:  "白银",
			Key:   "62996",
		},
		{
			Level: v1.SponsorLevel_SponsorLevel_GOLD,
			Icon:  "https://expoliveimgs.zy223.com/expologo/gold.png_wiki-template-global",
			Name:  "黄金",
			Key:   "62997",
		},
		{
			Level: v1.SponsorLevel_SponsorLevel_PLATINUM,
			Icon:  "https://expoliveimgs.zy223.com/expologo/platinum.png_wiki-template-global",
			Name:  "铂金",
			Key:   "62998",
		},
		{
			Level: v1.SponsorLevel_SponsorLevel_DIAMOND,
			Icon:  "https://expoliveimgs.zy223.com/expologo/diamond.png_wiki-template-global",
			Name:  "钻石",
			Key:   "62999",
		},
		{
			Level: v1.SponsorLevel_SponsorLevel_GOLOBAL,
			Icon:  "https://expoliveimgs.zy223.com/expologo/global.png_wiki-template-global",
			Name:  "全球",
			Key:   "63000",
		},
	}

	partnerItems = []*v1.ExpoPartnerTypeItem{
		{
			Id:   1,
			Name: "WEB3.0、METAVERSE和游戏合作伙伴",
			Key:  "63209",
		},
		{
			Id:   2,
			Name: "金融科技、人工智能、外汇和支付合作伙伴",
			Key:  "63210",
		},
		{
			Id:   3,
			Name: "VCS,金融服务合作伙伴",
			Key:  "63211",
		},
		{
			Id:   4,
			Name: "社区合作伙伴(加密货币和外汇)",
			Key:  "63212",
		},
		{
			Id:   5,
			Name: "区块链和数字资产合作伙伴",
			Key:  "63208",
		},
	}
	scheduleTypeMap = map[v1.ScheduleType]string{
		v1.ScheduleType_ScheduleType_ALL:        "全部",
		v1.ScheduleType_ScheduleType_Theme:      "主题演讲",
		v1.ScheduleType_ScheduleType_Lecture:    "围炉谈话",
		v1.ScheduleType_ScheduleType_RoundTable: "圆桌会议",
	}

	hallTypeMap = map[v1.ExpoHallType]string{
		v1.ExpoHallType_ExpoHallType_MAIN: "主会场",
		v1.ExpoHallType_ExpoHallType_SUB:  "分会场",
	}
	// 字母头像
	letterAvatars = map[string]string{
		"a": "https://expoliveimgs.zy223.com/expologo/avatar_a.png_wiki-template-global",
		"b": "https://expoliveimgs.zy223.com/expologo/avatar_b.png_wiki-template-global",
		"c": "https://expoliveimgs.zy223.com/expologo/avatar_c.png_wiki-template-global",
		"d": "https://expoliveimgs.zy223.com/expologo/avatar_d.png_wiki-template-global",
		"e": "https://expoliveimgs.zy223.com/expologo/avatar_e.png_wiki-template-global",
		"f": "https://expoliveimgs.zy223.com/expologo/avatar_f.png_wiki-template-global",
		"g": "https://expoliveimgs.zy223.com/expologo/avatar_g.png_wiki-template-global",
		"h": "https://expoliveimgs.zy223.com/expologo/avatar_h.png_wiki-template-global",
		"i": "https://expoliveimgs.zy223.com/expologo/avatar_i.png_wiki-template-global",
		"j": "https://expoliveimgs.zy223.com/expologo/avatar_j.png_wiki-template-global",
		"k": "https://expoliveimgs.zy223.com/expologo/avatar_k.png_wiki-template-global",
		"l": "https://expoliveimgs.zy223.com/expologo/avatar_l.png_wiki-template-global",
		"m": "https://expoliveimgs.zy223.com/expologo/avatar_m.png_wiki-template-global",
		"n": "https://expoliveimgs.zy223.com/expologo/avatar_n.png_wiki-template-global",
		"o": "https://expoliveimgs.zy223.com/expologo/avatar_o.png_wiki-template-global",
		"p": "https://expoliveimgs.zy223.com/expologo/avatar_p.png_wiki-template-global",
		"q": "https://expoliveimgs.zy223.com/expologo/avatar_q.png_wiki-template-global",
		"r": "https://expoliveimgs.zy223.com/expologo/avatar_r.png_wiki-template-global",
		"s": "https://expoliveimgs.zy223.com/expologo/avatar_s.png_wiki-template-global",
		"t": "https://expoliveimgs.zy223.com/expologo/avatar_t.png_wiki-template-global",
		"u": "https://expoliveimgs.zy223.com/expologo/avatar_u.png_wiki-template-global",
		"v": "https://expoliveimgs.zy223.com/expologo/avatar_v.png_wiki-template-global",
		"w": "https://expoliveimgs.zy223.com/expologo/avatar_w.png_wiki-template-global",
		"x": "https://expoliveimgs.zy223.com/expologo/avatar_x.png_wiki-template-global",
		"y": "https://expoliveimgs.zy223.com/expologo/avatar_y.png_wiki-template-global",
		"z": "https://expoliveimgs.zy223.com/expologo/avatar_z.png_wiki-template-global",
	}
	defaultAvatar = "https://expoliveimgs.zy223.com/expologo/avatar_default.png_wiki-template-global"
)

var (
	defaultPageSize = int32(10)
	faceUrlTemplate = urlformat.WithTemplate("_list109")
	urlTemplate     = urlformat.WithTemplate("_wiki-template-global")
	surveyTemplate  = urlformat.WithTemplate("_fxeye-survey")
	expoDetailTabs  = map[string]*v1.ExpoDetailTab{
		"63003": {
			Name: "展会议程",
			Id:   "EXPO20001",
		},
		"63258": {
			Name: " 展会互动",
			Id:   "EXPO20002",
		},
		"63004": {
			Name: " 参展指南",
			Id:   "EXPO20003",
		},
		"63005": {
			Name: " 合作伙伴",
			Id:   "EXPO20004",
		},
		"63006": {
			Name: " 展会话题",
			Id:   "EXPO20005",
		},
		"63009": {
			Name: " 展会实勘",
			Id:   "EXPO20006",
		},
	}
)

func getPartnerItem(id int32) (*v1.ExpoPartnerTypeItem, bool) {
	for _, item := range partnerItems {
		if id == item.Id {
			return item, true
		}
	}
	return nil, false
}

func getLetterAvatar(nickname string) string {
	trimmed := strings.TrimSpace(nickname)
	if len(trimmed) == 0 {
		return defaultAvatar
	}

	var (
		firstChar, _ = utf8.DecodeRuneInString(trimmed)
		firstLetter  = string(firstChar)
	)

	// 如果是汉字
	if unicode.Is(unicode.Han, firstChar) {
		// 使用拼音库获取拼音首字母
		pinyinArgs := pinyin.NewArgs()
		pinyinArgs.Style = pinyin.FirstLetter // 只获取首字母
		pinyinList := pinyin.Pinyin(string(firstChar), pinyinArgs)
		if len(pinyinList) > 0 && len(pinyinList[0]) > 0 {
			firstLetter = strings.ToUpper(pinyinList[0][0][:1])
		}
	} else {
		if unicode.IsLetter(firstChar) {
			firstLetter = strings.ToUpper(string(firstChar))
		}
	}

	avatar, ok := letterAvatars[strings.ToLower(firstLetter)]
	if ok {
		return avatar
	}
	return defaultAvatar
}

func getExpoTabs(languageCode string) []*v1.ExpoTab {
	return []*v1.ExpoTab{
		{
			Id:   "EXPO0000001",
			Name: i18n.GetWithDefaultEnglish("62619", languageCode), // 全部
		},
		{
			Id:   "EXPO1000002",
			Name: i18n.GetWithDefaultEnglish("62623", languageCode), // 报名中
		},
		{
			Id:   "EXPO1000003",
			Name: i18n.GetWithDefaultEnglish("62622", languageCode), // 进行中
		},
		{
			Id:   "EXPO0000004",
			Name: i18n.GetWithDefaultEnglish("62625", languageCode), // 已结束
		},
	}
}

func getExpoDetailTab(key, languageCode string) *v1.ExpoDetailTab {
	value, ok := expoDetailTabs[key]
	if !ok {
		return &v1.ExpoDetailTab{}
	}
	value.Name = i18n.GetWithDefaultEnglish(key, languageCode)
	return value
}

func getSponsorLevelIcon(level v1.SponsorLevel, languageCode string) string {
	for _, value := range sponsorLevelItems {
		if value.Level == level {
			value.Name = i18n.GetWithDefaultEnglish(value.Key, languageCode)
			return value.Icon
		}
	}
	return ""
}
