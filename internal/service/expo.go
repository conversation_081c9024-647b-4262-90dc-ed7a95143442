package service

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"api-expo/api/common"
	communityV1 "api-expo/api/community/v1"
	v1 "api-expo/api/expo/v1"
	userCenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"
	"api-expo/pkg/upstream"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	"golang.org/x/sync/errgroup"
)

// ExpoTab 展会tab
func (s *Service) ExpoTab(ctx context.Context, _ *common.EmptyRequest) (*v1.ExpoTabReply, error) {
	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	return &v1.ExpoTabReply{
		Tabs: getExpoTabs(languageCode),
	}, nil
}

// ExpoList 展会列表
func (s *Service) ExpoList(ctx context.Context, in *v1.ExpoListRequest) (*v1.ExpoListReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	communities, err := s.expoCommunity.FindEnable(ctx)
	if err != nil {
		l.Errorf("expoCommunity.FindAll Err:%v", err)
		return nil, err
	}

	var (
		communityMapping = make(map[int64]*models.ExpoCommunity, len(communities))
		expoIds          = make([]int64, 0, len(communities))
	)

	for _, community := range communities {
		communityMapping[community.ExpoId] = community
		expoIds = append(expoIds, community.ExpoId)
	}

	var expos []*models.Expo
	if len(expoIds) > 0 {
		expos, err = s.expo.FindByIds(ctx, expoIds)
		if err != nil {
			l.Errorf("s.expo.FindAll Err:%v", err)
			return nil, err
		}
	}

	var (
		out        = make([]*v1.ExpoBase, 0, len(expos))
		expoGroups = make(map[v1.ExpoStatus][]*v1.ExpoBase)
	)

	for _, expo := range expos {
		community, ok := communityMapping[expo.ID]
		if !ok {
			continue
		}

		if !community.Enable {
			continue
		}

		var (
			expoStatus = v1.ExpoStatus_ExpoStatus_UNKNOWN
			logo       string
			audience   string
			now        = expo.GetNow()
		)

		if expo.Registrants > 0 {
			audience = i18n.GetWithTemplateDataDefaultEnglish("62627", languageCode, []string{strconv.Itoa(expo.Registrants)}) // x人报名
		}

		if now.After(expo.End) {
			expoStatus = v1.ExpoStatus_ExpoStatus_END
			audience = i18n.GetWithDefaultEnglish("62625", languageCode) // 已结束
		} else if now.After(expo.Start) {
			expoStatus = v1.ExpoStatus_ExpoStatus_PROCESSING
		} else {
			expoStatus = v1.ExpoStatus_ExpoStatus_NOT_START
		}

		if community.Extra != nil && community.Extra.V != nil {
			lang, ok := community.Extra.V.Languages[languageCode]
			if ok {
				logo = lang.Logo
			}

			lang, ok = community.Extra.V.Languages["en"]
			if ok && logo == "" {
				logo = lang.Logo
			}
		}

		expoGroups[expoStatus] = append(expoGroups[expoStatus], &v1.ExpoBase{
			Id:          strconv.FormatInt(expo.ID, 10),
			Logo:        logo,
			Status:      expoStatus,
			Name:        expo.Name,
			Address:     expo.Location,
			Audience:    audience,
			CountryCode: expo.CountryCode,
			StartTime:   expo.Start.Unix(),
			Zone:        expo.TimeZone,
		})
	}

	switch in.TabId {
	case "EXPO1000002": // 报名中
		out = expoGroups[v1.ExpoStatus_ExpoStatus_NOT_START]
	case "EXPO1000003": // 进行中
		out = expoGroups[v1.ExpoStatus_ExpoStatus_PROCESSING]
	case "EXPO0000004": // 已结束
		out = expoGroups[v1.ExpoStatus_ExpoStatus_END]
	default:
		out = append(out, expoGroups[v1.ExpoStatus_ExpoStatus_PROCESSING]...)
		out = append(out, expoGroups[v1.ExpoStatus_ExpoStatus_NOT_START]...)
		out = append(out, expoGroups[v1.ExpoStatus_ExpoStatus_END]...)
	}

	var (
		start = (in.Page - 1) * in.Size
		end   = start + in.Size
	)

	if start > int32(len(out)) {
		return &v1.ExpoListReply{}, nil
	}

	if end > int32(len(out)) {
		end = int32(len(out))
	}

	return &v1.ExpoListReply{
		Expos: out[start:end],
	}, nil
}

func (s *Service) ExpoDetailTab(ctx context.Context, in *v1.ExpoDetailTabRequest) (*v1.ExpoDetailTabReply, error) {
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	var (
		status = v1.ExpoStatus_ExpoStatus_UNKNOWN
		now    = expo.GetNow()
	)

	if now.After(expo.End) {
		status = v1.ExpoStatus_ExpoStatus_END
	} else if now.After(expo.Start) {
		status = v1.ExpoStatus_ExpoStatus_PROCESSING
	} else {
		status = v1.ExpoStatus_ExpoStatus_NOT_START
	}

	commentCount, err := s.comment.CountByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("comment.CountByExpoId Err:%v", err)
		return nil, err
	}

	var tabs []*v1.ExpoDetailTab
	if commentCount < 300 {
		tabs = append(tabs,
			getExpoDetailTab("63003", languageCode), // 展会议程
			getExpoDetailTab("63258", languageCode), // 展会互动
		)
	}

	if commentCount > 500 {
		tabs = append(tabs,
			getExpoDetailTab("63258", languageCode), // 展会互动
			getExpoDetailTab("63003", languageCode), // 展会议程
		)
	}

	tabs = append(tabs,
		getExpoDetailTab("63004", languageCode), // 参展指南
		getExpoDetailTab("63005", languageCode), // 合作伙伴
		getExpoDetailTab("63006", languageCode), // 展会话题
	)

	if status == v1.ExpoStatus_ExpoStatus_END {
		tabs = append(tabs,
			getExpoDetailTab("63007", languageCode), // 展会实勘
		)
	}
	return &v1.ExpoDetailTabReply{
		Tabs: tabs,
	}, nil
}

// GetExpoDetail 展会详情
func (s *Service) GetExpoDetail(ctx context.Context, in *v1.ExpoDetailRequest) (*v1.ExpoDetail, error) {
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		eg, _           = errgroup.WithContext(ctx)
	)
	eg.SetLimit(10)

	expoCommunity, err := s.expoCommunity.GetByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoCommunity.GetByExpoId Err:%v", err)
		return nil, err
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	var (
		status = v1.ExpoStatus_ExpoStatus_UNKNOWN
		logo   string
		now    = expo.GetNow()
		desc   string
	)

	if now.After(expo.End) {
		status = v1.ExpoStatus_ExpoStatus_END
	} else if now.After(expo.Start) {
		status = v1.ExpoStatus_ExpoStatus_PROCESSING
	} else {
		status = v1.ExpoStatus_ExpoStatus_NOT_START
	}

	if expoCommunity.Extra != nil {
		lang, ok := expoCommunity.Extra.V.Languages[languageCode]
		if ok {
			logo = urlformat.FullPath(lang.Logo, urlTemplate)
			desc = lang.Description
		}

		lang, ok = expoCommunity.Extra.V.Languages["en"]
		if ok && desc == "" {
			logo = urlformat.FullPath(lang.Logo, urlTemplate)
			desc = lang.Description
		}
	}

	var (
		guestCount   int64
		guestAvatars []string
	)

	// 1、嘉宾
	eg.Go(func() error {
		//guestCount, err = s.expoGuest.CountByExpoId(ctx, expo.ID)
		//if err != nil {
		//	l.Errorf("CountByExpoId Err:%v", err)
		//	return err
		//}
		//
		//var expoGuests []*models.ExpoGuest
		//expoGuests, err = s.expoGuest.FindByExpoId(ctx, expo.ID, 3)
		//if err != nil {
		//	l.Errorf("FindByExpoIdWithSize Err:%v", err)
		//	return err
		//}
		var schedules []*models.ExpoSchedule
		schedules, err = s.expoSchedule.FindByExpoId(ctx, expoId)
		if err != nil {
			l.Errorf("expoSchedule.FindByExpoId Err:%v", err)
			return err
		}

		scheduleIds := make([]int64, 0, len(schedules))
		for _, schedule := range schedules {
			scheduleIds = append(scheduleIds, int64(schedule.ID))
		}

		var scheduleGuests []*models.ExpoScheduleGuest
		scheduleGuests, err = s.expoScheduleGuest.FindByScheduleIds(ctx, scheduleIds)
		if err != nil {
			l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
			return err
		}
		guestCount = int64(len(scheduleGuests))

		guestIds := make([]int64, 0, len(scheduleGuests))
		for _, expoGuest := range scheduleGuests {
			guestIds = append(guestIds, expoGuest.GuestId)
		}

		var guests []*models.Guest
		guests, err = s.guest.FindByIds(ctx, guestIds)
		if err != nil {
			l.Errorf("FindByIds Err:%v", err)
			return err
		}

		guestMapping := make(map[uint]*models.Guest, len(guests))
		for _, guest := range guests {
			guestMapping[guest.ID] = guest
		}

		guestAvatars = make([]string, 0, len(guests))
		for _, expoGuest := range scheduleGuests {
			guestInfo, ok := guestMapping[uint(expoGuest.GuestId)]
			if !ok {
				continue
			}
			guestAvatars = append(guestAvatars, urlformat.FullPath(guestInfo.Avatar, urlTemplate))
		}
		if len(guestAvatars) > 3 {
			guestAvatars = guestAvatars[:3]
		}
		return nil
	})

	// 2、从业者
	var (
		professionalCount int64
		professional      []*models.Participant
	)

	eg.Go(func() error {
		professionalCount, err = s.participant.CountBySubIdentity(ctx, expoId, []v1.SubIdentity{
			v1.SubIdentity_SUB_IDENTITY_FOREX,
			v1.SubIdentity_SUB_IDENTITY_SERVICE_PROVIDER,
			v1.SubIdentity_SUB_IDENTITY_FINTECH,
			v1.SubIdentity_SUB_IDENTITY_CRYPTO,
			v1.SubIdentity_SUB_IDENTITY_SERVICE_IB,
		})

		professional, err = s.participant.FindBySubIdentity(ctx, expoId, []v1.SubIdentity{
			v1.SubIdentity_SUB_IDENTITY_FOREX,
			v1.SubIdentity_SUB_IDENTITY_SERVICE_PROVIDER,
			v1.SubIdentity_SUB_IDENTITY_FINTECH,
			v1.SubIdentity_SUB_IDENTITY_CRYPTO,
			v1.SubIdentity_SUB_IDENTITY_SERVICE_IB,
		}, 3)
		if err != nil {
			l.Errorf("participant.FindByExpoId Err:%v", err)
			return err
		}
		return nil
	})

	var (
		investorCount int64
		investor      []*models.Participant
	)

	// 3、投资者
	eg.Go(func() error {
		investorCount, err = s.participant.CountBySubIdentity(ctx, expoId, []v1.SubIdentity{
			v1.SubIdentity_SUB_IDENTITY_INVESTOR,
			v1.SubIdentity_SUB_IDENTITY_TRADER,
			v1.SubIdentity_SUB_IDENTITY_KOL,
		})
		if err != nil {
			l.Errorf("CountBySubIdentity Err:%v", err)
			return err
		}

		investor, err = s.participant.FindBySubIdentity(ctx, expoId, []v1.SubIdentity{
			v1.SubIdentity_SUB_IDENTITY_INVESTOR,
			v1.SubIdentity_SUB_IDENTITY_TRADER,
			v1.SubIdentity_SUB_IDENTITY_KOL,
		}, 3)
		if err != nil {
			l.Errorf("participant.FindByExpoId Err:%v", err)
			return err
		}
		return nil
	})

	var (
		otherCount int64
		other      []*models.Participant
	)

	// 4、未分组
	eg.Go(func() error {
		otherCount, err = s.participant.CountBySubIdentity(ctx, expoId, []v1.SubIdentity{v1.SubIdentity_SUB_IDENTITY_OTHER})
		if err != nil {
			l.Errorf("CountBySubIdentity Err:%v", err)
			return err
		}

		other, err = s.participant.FindBySubIdentity(ctx, expoId, []v1.SubIdentity{v1.SubIdentity_SUB_IDENTITY_OTHER}, 3)
		if err != nil {
			l.Errorf("FindByExpoIdWithSize Err:%v", err)
			return err
		}
		return nil
	})

	var (
		exhibitorCount  int64
		exhibitors      []*models.ExpoExhibitor
		exhibitorValues []*v1.ExpoExhibitor
	)

	// 5、参展商/赞助商
	eg.Go(func() error {
		exhibitors, err = s.expoExhibitor.FindByExpoId(ctx, expo.ID)
		if err != nil {
			l.Errorf("FindByExpoIdWithSize Err:%v", err)
			return err
		}

		traderCodes := make([]string, 0, len(exhibitors))
		for _, exhibitor := range exhibitors {
			if exhibitor.TraderCode != "" {
				traderCodes = append(traderCodes, exhibitor.TraderCode)
			}
		}

		traderMapping := make(map[string]*upstream.TraderInfo, len(traderCodes))
		if len(traderCodes) > 0 {
			var traderRes *upstream.GetTraderReply
			traderRes, err = s.upstream.GetTraders(ctx, &upstream.GetTraderRequest{
				Codes:       traderCodes,
				Country:     countryCode,
				HasDisabled: true,
				Language:    languageCode,
			})
			if err != nil {
				l.Errorf("upstream.GetTraders Err:%v", err)
				// 这里不影响主流程
			} else {
				for _, trader := range traderRes.Result {
					traderMapping[trader.Code] = trader
				}
			}
		}

		exhibitorCount = int64(len(exhibitors))
		exhibitorValues = make([]*v1.ExpoExhibitor, 0, len(exhibitors))
		for _, exhibitor := range exhibitors {
			traderLogo := urlformat.FullPath(exhibitor.Logo, urlTemplate)
			if trader, ok := traderMapping[exhibitor.TraderCode]; ok && exhibitor.TraderCode != "" {
				traderLogo = trader.Logo
			}

			exhibitorValues = append(exhibitorValues, &v1.ExpoExhibitor{
				Name:             exhibitor.TraderName,
				Logo:             traderLogo,
				SponsorLevelIcon: getSponsorLevelIcon(exhibitor.SponsorLevel, languageCode),
				TraderCode:       exhibitor.TraderCode,
				ExhibitorType:    exhibitor.ExhibitorType,
			})
		}
		return nil
	})

	var (
		mainLive  string
		liveCover string
		liveItems []*v1.ExpoVideoItem
	)

	// 6、展会直播
	eg.Go(func() error {
		var expoLives []*models.ExpoLive
		expoLives, err = s.expoLive.FindByExpoId(ctx, expo.ID)
		if err != nil {
			l.Errorf("expoLive.FindByExpoIdWithSize Err:%v", err)
			return err
		}

		for _, live := range expoLives {
			var (
				name    = i18n.GetWithTemplateDataDefaultEnglish("62995", languageCode, []string{strconv.Itoa(int(live.Level))}) // 视角
				liveUrl = live.Url
			)

			if liveUrl == "" {
				if live.AppId == "" {
					live.AppId = defaultLiveAppId
				}

				liveUrl = fmt.Sprintf("%s_%s_%s_main", live.AppId, live.RoomId, live.UserId)
			}

			if liveUrl == "" {
				continue
			}

			if live.Level == 0 {
				mainLive = live.Url
				liveCover = live.Cover
				name = i18n.GetWithDefaultEnglish("63397", languageCode) // 主视角
			}

			liveItems = append(liveItems, &v1.ExpoVideoItem{
				Url:   liveUrl,
				Name:  name,
				Cover: urlformat.FullPath(live.Cover, urlTemplate),
			})
		}
		return nil
	})

	var (
		imageCount int64
		images     []string
	)

	// 7、图片直播
	eg.Go(func() error {
		imageCount, err = s.expoImage.CountByExpoId(ctx, expo.ID)
		if err != nil {
			l.Errorf("expoImage.CountByExpoId Err:%v", err)
			// 这里错误不影响主流程
		}

		var expoImages []*models.ExpoImage
		expoImages, err = s.expoImage.FindByExpoId(ctx, expo.ID, 9)
		if err != nil {
			l.Errorf("expoImage.FindByExpoIdWithSize Err:%v", err)
			return err
		}

		images = make([]string, 0, len(expoImages))
		for _, image := range expoImages {
			images = append(images, urlformat.FullPath(image.ImageUrl, urlTemplate))
		}
		return nil
	})

	var (
		registration = false
	)

	// 8、当前用户是否报名
	eg.Go(func() error {
		if userId != "" {
			registration, err = s.participant.ExistsByUserId(ctx, expo.ID, userId)
			if err != nil {
				l.Errorf("participant.ExistsByUserId Err:%v", err)
				// 这里错误不影响主流程
			}
		}
		return nil
	})

	var (
		mainLiveReview      string
		mainLiveReviewName  string
		mainLiveReviewCover string
		livesReviews        []*v1.ExpoVideoItem
		highlights          []string
		highlightCount      int64
	)

	// 9、展会回顾
	eg.Go(func() error {
		if status == v1.ExpoStatus_ExpoStatus_END {
			var reviews []*models.ExpoReview
			reviews, err = s.expoReview.FindByExpoId(ctx, expoId)
			if err != nil {
				l.Errorf("expoReview.FindByExpoId Err:%v", err)
				// 这里发生错误不影响主流程
			}

			for _, review := range reviews {
				var (
					imageUrls  []string
					reviewDesc string
				)

				if review.Extra != nil && review.Extra.V != nil {
					imageUrls = review.Extra.V.Images
					lang, ok := review.Extra.V.Languages[languageCode]
					if ok {
						reviewDesc = lang.Description
					}

					lang, ok = review.Extra.V.Languages["en"]
					if ok && reviewDesc == "" {
						reviewDesc = lang.Description
					}
				}

				if review.Type == v1.ExpoReviewType_REVIEW_TYPE_VIDEO {
					if mainLiveReview == "" {
						mainLiveReview = review.URL
						mainLiveReviewName = reviewDesc
						mainLiveReviewCover = urlformat.FullPath(review.Cover, urlTemplate)
					}

					livesReviews = append(livesReviews, &v1.ExpoVideoItem{
						Url:   review.URL,
						Name:  reviewDesc,
						Cover: urlformat.FullPath(review.Cover, urlTemplate),
					})
					continue
				}

				highlightCount += int64(len(imageUrls))
				for _, image := range imageUrls {
					highlights = append(highlights, urlformat.FullPath(image, urlTemplate))
					if len(highlights) > 9 {
						break
					}
				}
			}
		}
		return nil
	})

	// 10、展会话题
	var topicName string
	eg.Go(func() error {
		if expoCommunity.TopicId != "" {
			var topic *communityV1.UserTopicNameReply
			topic, err = s.community.GetTopicName(ctx, &communityV1.UserTopicNameRequest{
				TopicId: expoCommunity.TopicId,
			})
			if err != nil {
				l.Errorf("community.GetTopicName Err:%v", err)
			} else {
				topicName = topic.TopicName
			}
		}
		return nil
	})
	err = eg.Wait()
	if err != nil {
		return nil, err
	}

	userIds := make([]string, 0, 10)
	for _, user := range professional {
		if user.UserId == "" {
			continue
		}
		userIds = append(userIds, user.ApplyUserid)
	}

	for _, user := range investor {
		if user.UserId == "" {
			continue
		}
		userIds = append(userIds, user.ApplyUserid)
	}

	for _, user := range other {
		if user.UserId == "" {
			continue
		}
		userIds = append(userIds, user.ApplyUserid)
	}

	var (
		professionalAvatars []string
		investorAvatars     []string
		otherAvatars        []string
	)

	if len(userIds) > 0 {
		var userRes *userCenterv1.GetUsersReply
		userRes, err = s.user.GetUsersInfo(ctx, &userCenterv1.GetUsersRequest{
			UserIds: userIds,
		})
		if err != nil {
			l.Errorf("GetUsersInfo Err:%v", err)
			return nil, err
		}

		userMapping := make(map[string]*userCenterv1.UserInfo, len(userRes.Message))
		for _, user := range userRes.Message {
			userMapping[user.UserId] = user
		}

		//从业者
		for _, value := range professional {
			avatar := ""
			user, ok := userMapping[value.ApplyUserid]
			if ok {
				avatar = user.AvatarAddress
			} else {
				avatar = getLetterAvatar(fmt.Sprintf("%s%s", value.FirstName, value.LastName))
			}
			professionalAvatars = append(professionalAvatars, avatar)
		}

		//投资者
		for _, value := range investor {
			avatar := ""
			user, ok := userMapping[value.ApplyUserid]
			if ok {
				avatar = user.AvatarAddress
			} else {
				avatar = getLetterAvatar(fmt.Sprintf("%s%s", value.FirstName, value.LastName))
			}
			investorAvatars = append(investorAvatars, avatar)
		}

		//未分组
		for _, value := range other {
			avatar := ""
			user, ok := userMapping[value.ApplyUserid]
			if ok {
				avatar = user.AvatarAddress
			} else {
				avatar = getLetterAvatar(fmt.Sprintf("%s%s", value.FirstName, value.LastName))
			}
			otherAvatars = append(otherAvatars, avatar)
		}
	}

	imageLive := &v1.ExpoImage{
		Icon:   urlformat.FullPath(imageLiveIcon, urlTemplate),
		Name:   i18n.GetWithDefaultEnglish("63140", languageCode), // 图片直播
		Count:  imageCount,
		Images: images,
		Type:   v1.ExpoImageType_ExpoImageType_IMAGE_LIVE,
	}

	if status == v1.ExpoStatus_ExpoStatus_END && len(highlights) > 0 {
		imageLive = &v1.ExpoImage{
			Icon:   urlformat.FullPath(highLightIcon, urlTemplate),
			Name:   i18n.GetWithDefaultEnglish("63139", languageCode), // 精彩瞬间
			Count:  highlightCount,
			Images: highlights,
			Type:   v1.ExpoImageType_ExpoImageType_HIGHTLIGHT,
		}
	}

	return &v1.ExpoDetail{
		Id:          strconv.FormatInt(expo.ID, 10),
		Logo:        logo,
		Status:      status,
		Name:        expo.Name,
		Description: desc,
		CountryCode: expo.CountryCode,
		StartTime:   expo.Start.Unix(),
		Map: &v1.ExpoMap{
			Longitude:   expo.Longitude,
			Latitude:    expo.Latitude,
			BaiduImage:  surveyFormat.FullPath(expo.BaiduMap, surveyTemplate),
			GoogleImage: surveyFormat.FullPath(expo.GoogleMap, surveyTemplate),
			Address:     expo.LocationName,
			Name:        expo.Location,
		},
		AudienceGroups: []*v1.ExpoAudienceGroup{
			{
				Category: v1.ExpoAudienceGroupCategory_Speakers,
				Name:     i18n.GetWithDefaultEnglish("63183", languageCode), // 演讲嘉宾
				Count:    strconv.Itoa(int(guestCount)),
				Avatars:  guestAvatars,
				Icon:     urlformat.FullPath(speakerIcon, urlTemplate),
				Id:       speakerId,
			},
			{
				Category: v1.ExpoAudienceGroupCategory_Professionals,
				Name:     i18n.GetWithDefaultEnglish("63184", languageCode), // 从业者
				Count:    strconv.Itoa(int(professionalCount)),
				Avatars:  professionalAvatars,
				Icon:     urlformat.FullPath(professionalIcon, urlTemplate),
				Id:       professionalId,
			},
			{
				Category: v1.ExpoAudienceGroupCategory_Investors,
				Name:     i18n.GetWithDefaultEnglish("63185", languageCode), // 投资者
				Count:    strconv.Itoa(int(investorCount)),
				Avatars:  investorAvatars,
				Icon:     urlformat.FullPath(investorIcon, urlTemplate),
				Id:       investorsId,
			},
			{
				Category: v1.ExpoAudienceGroupCategory_NotGrouped,
				Name:     i18n.GetWithDefaultEnglish("63186", languageCode), // 未分组
				Count:    strconv.Itoa(int(otherCount)),
				Avatars:  otherAvatars,
				Icon:     urlformat.FullPath(otherIcon, urlTemplate),
				Id:       otherId,
			},
			{
				Category:   v1.ExpoAudienceGroupCategory_Exhibitors,
				Name:       i18n.GetWithDefaultEnglish("63187", languageCode), // 参展商/赞助商
				Count:      strconv.Itoa(int(exhibitorCount)),
				Exhibitors: exhibitorValues,
				Icon:       urlformat.FullPath(exhibitorIcon, urlTemplate),
				Id:         exhibitorId,
			},
		},
		Live: &v1.ExpoVideo{
			Url:   mainLive,
			Name:  i18n.GetWithDefaultEnglish("63138", languageCode), // 现场直播
			Cover: urlformat.FullPath(liveCover, urlTemplate),
			Items: liveItems,
		},
		Video: &v1.ExpoVideo{
			Url:   mainLiveReview,
			Name:  mainLiveReviewName,
			Cover: mainLiveReviewCover,
			Items: livesReviews,
		},
		Registration:     registration,
		Image:            imageLive,
		ExpoRegisterLink: fmt.Sprintf("%s?expoId=%s&languageCode=%s&countryCode=%s", s.business.ExpoRegisterLink, in.ExpoId, languageCode, countryCode),
		TopicId:          expoCommunity.TopicId,
		TopicName:        topicName,
	}, nil
}

// ExpoSchedule 展会议程
func (s *Service) ExpoSchedule(ctx context.Context, in *v1.ExpoScheduleRequest) (*v1.ExpoScheduleReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	schedules, err := s.expoSchedule.FindByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoSchedule.FindByExpoIdWithSize Err:%v", err)
		return nil, err
	}

	var (
		scheduleIds     = make([]int64, 0, len(schedules))
		scheduleMapping = make(map[int64]*models.ExpoSchedule, len(schedules))
		zoneDiff        = expo.GetZoneDiff()
		now             = time.Now().UTC().Add(zoneDiff)
	)

	for _, schedule := range schedules {
		scheduleIds = append(scheduleIds, int64(schedule.ID))
		scheduleMapping[int64(schedule.ID)] = schedule
	}

	var scheduleGuests []*models.ExpoScheduleGuest
	if len(scheduleIds) > 0 {
		scheduleGuests, err = s.expoScheduleGuest.FindByScheduleIds(ctx, scheduleIds)
		if err != nil {
			l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
			return nil, err
		}
	}

	guestIds := make([]int64, 0, len(scheduleGuests))
	for _, scheduleGuest := range scheduleGuests {
		guestIds = append(guestIds, scheduleGuest.GuestId)
	}

	var guests []*models.Guest
	if len(guestIds) > 0 {
		guests, err = s.guest.FindByIds(ctx, guestIds)
		if err != nil {
			l.Errorf("guest.FindByIds Err:%v", err)
			return nil, err
		}
	}

	var (
		guestMapping         = make(map[uint]*v1.ExpoScheduleHallLineSpeaker, len(guests))
		scheduleGuestMapping = make(map[uint][]*v1.ExpoScheduleHallLineSpeaker, len(schedules))
		reserves             []*models.ExpoScheduleReserve
	)

	for _, guest := range guests {
		if !guest.Enable {
			continue
		}

		var (
			label       []string
			description string
		)

		if guest.Extra != nil {
			lang, ok := guest.Extra.V.Languages[languageCode]
			if ok {
				label = lang.Label
				description = lang.Description
			}

			lang, ok = guest.Extra.V.Languages["en"]
			if ok && description == "" {
				label = lang.Label
				description = lang.Description
			}
		}

		guestMapping[guest.ID] = &v1.ExpoScheduleHallLineSpeaker{
			SpeakerId:   strconv.FormatInt(int64(guest.ID), 10),
			UserId:      guest.UserId,
			Label:       label,
			Name:        guest.Name,
			Avatar:      urlformat.FullPath(guest.Avatar, urlTemplate),
			Description: description,
		}
	}

	if userId != "" {
		reserves, err = s.expoScheduleReserve.FindByUserId(ctx, userId, []int64{expoId})
		if err != nil {
			l.Errorf("expoScheduleReserve.ExistsByUserId Err:%v", err)
			return nil, err
		}
	}

	reserveMapping := make(map[int64]struct{}, len(reserves))
	for _, reserve := range reserves {
		reserveMapping[reserve.ScheduleGuestId] = struct{}{}
	}

	for _, guest := range scheduleGuests {
		guestInfo, ok := guestMapping[uint(guest.GuestId)]
		if !ok {
			continue
		}

		schedule, ok := scheduleMapping[guest.ScheduleId]
		if !ok {
			continue
		}

		var (
			subscribe = false
			role      = i18n.GetWithDefaultEnglish("63183", languageCode) // 演讲嘉宾
			roleIcon  = speakerRoleIcon
		)

		if guest.Role == 1 {
			role = i18n.GetWithDefaultEnglish("63358", languageCode) // 主持人
			roleIcon = hostRoleIcon
		}

		if _, ok = reserveMapping[int64(guest.ID)]; ok {
			subscribe = true
		}

		scheduleGuestMapping[uint(guest.ScheduleId)] = append(scheduleGuestMapping[uint(guest.ScheduleId)], &v1.ExpoScheduleHallLineSpeaker{
			SpeakerId:       guestInfo.SpeakerId,
			UserId:          guestInfo.UserId,
			Label:           guestInfo.Label,
			Subscribe:       subscribe,
			Name:            guestInfo.Name,
			Avatar:          guestInfo.Avatar,
			Description:     guestInfo.Description,
			ScheduleGuestId: strconv.FormatInt(int64(guest.ID), 10),
			Role:            role,
			RoleIcon:        roleIcon,
			CanSubscribe:    now.Before(schedule.Start.Add(zoneDiff)),
		})
	}

	halls, err := s.expoHall.FindByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoHall.FindByExpoIdWithSize Err:%v", err)
		return nil, err
	}

	hallMapping := make(map[uint]string, len(halls))
	for _, hall := range halls {
		var name string
		if hall.Extra != nil {
			lang, ok := hall.Extra.V.Languages[languageCode]
			if ok {
				name = lang.Name
			}

			lang, ok = hall.Extra.V.Languages["en"]
			if ok && name == "" {
				name = lang.Name
			}
		}
		hallMapping[hall.ID] = name
	}

	var (
		scheduleValues       = make([]*v1.ExpoSchedule, 0, len(schedules))
		scheduleValueMapping = make(map[string]*v1.ExpoSchedule, len(schedules))
		scheduleHallMapping  = make(map[string]map[int64]*v1.ExpoScheduleHall, len(halls))
	)

	for _, schedule := range schedules {
		hallName, ok := hallMapping[uint(schedule.HallId)]
		if !ok {
			continue
		}

		var (
			start              = schedule.Start.Add(zoneDiff).Format("2006.01.02 15:04")
			end                = schedule.End.Add(zoneDiff).Format("2006.01.02 15:04")
			theme              = schedule.GetTypeName(languageCode)
			description        = schedule.Theme
			expoScheduleGuests = scheduleGuestMapping[schedule.ID]
		)

		if schedule.Extra != nil {
			lang, ok := schedule.Extra.V.Languages[languageCode]
			if ok {
				description = lang.Theme
			}

			lang, ok = schedule.Extra.V.Languages["en"]
			if ok && description == "" {
				description = lang.Theme
			}
		}

		if schedule.Start.Add(zoneDiff).Format(time.DateOnly) == schedule.End.Add(zoneDiff).Format(time.DateOnly) {
			end = schedule.End.Add(zoneDiff).Format("15:04")
		}

		var (
			date    = schedule.Start.Add(zoneDiff).Format("2006.01.02")
			newHall = &v1.ExpoScheduleHall{
				Name: hallName,
				Lines: []*v1.ExpoScheduleHallLine{
					{
						Timestamp:     schedule.Start.Unix(),
						TimestampShow: fmt.Sprintf("%s-%s", start, end),
						Theme:         theme,
						Description:   description,
						Speakers:      expoScheduleGuests,
						Start:         schedule.Start.Unix(),
						End:           schedule.End.Unix(),
					},
				},
			}
		)

		scheduleValue, ok := scheduleValueMapping[date]
		if !ok {
			scheduleValue = &v1.ExpoSchedule{
				Date: date,
				Halls: []*v1.ExpoScheduleHall{
					newHall,
				},
			}
			scheduleValueMapping[date] = scheduleValue
			scheduleHallMapping[date] = map[int64]*v1.ExpoScheduleHall{
				schedule.HallId: newHall,
			}
			scheduleValues = append(scheduleValues, scheduleValue)
			continue
		}

		scheduleHall, ok := scheduleHallMapping[date][schedule.HallId]
		if !ok {
			scheduleValue.Halls = append(scheduleValue.Halls, newHall)
			scheduleHallMapping[date][schedule.HallId] = newHall
			continue
		}
		scheduleHall.Lines = append(scheduleHall.Lines, &v1.ExpoScheduleHallLine{
			Timestamp:     schedule.Start.Unix(),
			TimestampShow: fmt.Sprintf("%s-%s", start, end),
			Theme:         theme,
			Description:   description,
			Speakers:      expoScheduleGuests,
		})
	}

	return &v1.ExpoScheduleReply{
		Schedules: scheduleValues,
	}, nil
}

// ReserveSchedule 预约议程
func (s *Service) ReserveSchedule(ctx context.Context, in *v1.ReserveScheduleRequest) (*v1.ReserveScheduleReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)
	languageCode = "zh-cn"

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	scheduleGuestId, err := strconv.ParseInt(in.ScheduleGuestId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	key := fmt.Sprintf("expo_schedule_reserve:%s", userId)
	release, err := s.locker.TryLock(ctx, key, time.Minute)
	if err != nil {
		return nil, innErr.WithMessage(innErr.ErrLogin, "operation frequency")
	}
	defer release()

	expoCommunity, err := s.expoCommunity.GetByExpoId(ctx, expoId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoCommunity.GetByExpoId Err:%v", err)
		return nil, err
	}

	if err != nil {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63395", languageCode)) // 展会不可用
	}

	if !expoCommunity.Enable {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63395", languageCode)) // 展会不可用
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}
	zoneDiff := expo.GetZoneDiff()

	if expo.GetNow().After(expo.End) {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63393", languageCode)) // 展已结束
	}

	scheduleGuest, err := s.expoScheduleGuest.GetById(ctx, expoId, scheduleGuestId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoScheduleGuest.ExistsByUserId Err:%v", err)
		return nil, err
	}

	// 客户存在
	if err != nil {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63396", languageCode)) // 议程不可用
	}

	schedule, err := s.expoSchedule.Get(ctx, expoId, scheduleGuest.ScheduleId)
	if err != nil {
		l.Errorf("expoSchedule.Get Err:%v", err)
		return nil, err
	}

	if schedule.Enable == false {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63396", languageCode)) // 议程不可用
	}

	if time.Now().UTC().Add(zoneDiff).After(schedule.End.Add(zoneDiff)) {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, i18n.GetWithDefaultEnglish("63414", languageCode)) // 议程已结束
	}

	if in.Reserve {
		_, err = s.expoScheduleReserve.Add(ctx, &models.ExpoScheduleReserve{
			ExpoId:          expoId,
			ScheduleGuestId: scheduleGuestId,
			UserId:          userId,
			LanguageCode:    languageCode,
		})
		if err != nil && !errors.Is(err, ormhelper.ErrDuplicateKey) {
			l.Errorf("expoScheduleReserve.Add Err:%v", err)
			return nil, err
		}

		var exists bool
		exists, err = s.expoScheduleReserve.ExistsWithUnscoped(ctx, userId, expoId, scheduleGuestId)
		if err != nil {
			l.Errorf("expoScheduleReserve.ExistsWithUnscoped Err:%v", err)
		}

		// 如果存在说明已经发送过通知
		if !exists {
			var guest *models.Guest
			guest, err = s.guest.Get(ctx, scheduleGuest.GuestId)
			if err != nil {
				l.Errorf("guest.Get Err:%v", err)
			} else {
				themes := make(map[string]string, 18)
				if schedule.Extra != nil && schedule.Extra.V != nil {
					for key, lang := range schedule.Extra.V.Languages {
						themes[key] = lang.Theme
					}
				}
				s.scheduleReserveNotification(ctx, &Content{
					NickName:        guest.Name,
					LanguageCode:    languageCode,
					ExpoId:          strconv.FormatInt(scheduleGuest.ExpoId, 10),
					ScheduleGuestId: in.ScheduleGuestId,
					Date:            schedule.Start.Add(zoneDiff).Format("2006.01.02"),
					Start:           schedule.Start.Add(zoneDiff).Format("2006.01.02 15:04"),
					Avatar:          urlformat.FullPath(guest.Avatar, urlTemplate),
					Themes:          themes,
					UserId:          userId,
				})
			}
		}

		return &v1.ReserveScheduleReply{
			Reserve: true,
		}, nil
	}

	reserve, err := s.expoScheduleReserve.GetWithUnscoped(ctx, userId, expoId, scheduleGuestId)
	if err != nil && errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoScheduleReserve.Get Err:%v", err)
		return nil, err
	}

	// 没有找到
	if err != nil {
		return &v1.ReserveScheduleReply{}, nil
	}

	// 如果已经删除
	if reserve.DeletedAt.Valid {
		err = s.expoScheduleReserve.Restore(ctx, userId, expoId, scheduleGuestId)
	} else {
		err = s.expoScheduleReserve.DeleteByUserId(ctx, userId, expoId, scheduleGuestId)
	}
	if err != nil {
		l.Errorf("expoScheduleReserve.DeleteByUserId Err:%v", err)
		return nil, err
	}
	return &v1.ReserveScheduleReply{}, nil
}

// ExpoGuide 展会指南
func (s *Service) ExpoGuide(ctx context.Context, in *v1.ExpoGuideRequest) (*v1.ExpoGuideReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}

	expoGuide, err := s.expoGuide.GetByExpoId(ctx, expoId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("expoGuide.GetByExpoId Err:%v", err)
		return nil, err
	}

	if err != nil {
		expoGuide = &models.ExpoGuide{
			ExpoId: expoId,
		}
	}

	exhibitors, err := s.expoExhibitor.FindByExpoIdWithSize(ctx, expoId, 10)
	if err != nil {
		l.Errorf("FindByExpoId Err:%v", err)
		return nil, err
	}

	traderCodes := make([]string, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		if exhibitor.TraderCode != "" {
			traderCodes = append(traderCodes, exhibitor.TraderCode)
		}
	}

	traderMapping := make(map[string]*upstream.TraderInfo, len(traderCodes))
	if len(traderCodes) > 0 {
		var traderRes *upstream.GetTraderReply
		traderRes, err = s.upstream.GetTraders(ctx, &upstream.GetTraderRequest{
			Codes:    traderCodes,
			Language: languageCode,
		})
		if err != nil {
			l.Errorf("upstream.GetTraders Err:%v", err)
			return nil, err
		}

		for _, trader := range traderRes.Result {
			traderMapping[trader.Code] = trader
		}
	}

	booths := make([]*v1.ExpoBooth, 0, len(exhibitors))
	for _, exhibitor := range exhibitors {
		var (
			traderName = exhibitor.TraderName
			logo       = urlformat.FullPath(exhibitor.MinLogo, urlTemplate)
		)

		if trader, ok := traderMapping[exhibitor.TraderCode]; ok {
			logo = trader.Icon
			traderName = trader.ShowName
		}

		booths = append(booths, &v1.ExpoBooth{
			TraderCode:       exhibitor.TraderCode,
			Logo:             logo,
			Name:             traderName,
			SponsorLevelIcon: getSponsorLevelIcon(exhibitor.SponsorLevel, languageCode),
			Booth:            exhibitor.Booth,
			ExhibitorType:    exhibitor.ExhibitorType,
		})
	}

	return &v1.ExpoGuideReply{
		GuideMapUrl: urlformat.FullPath(expoGuide.MapURL, urlTemplate),
		Booth:       booths,
		Map: &v1.ExpoMap{
			Longitude:   expo.Longitude,
			Latitude:    expo.Latitude,
			BaiduImage:  surveyFormat.FullPath(expo.BaiduMap, surveyTemplate),
			GoogleImage: surveyFormat.FullPath(expo.GoogleMap, surveyTemplate),
			Address:     expo.LocationName,
			Name:        expo.Location,
		},
	}, nil
}

// ExpoPartner 展会合作伙伴
func (s *Service) ExpoPartner(ctx context.Context, in *v1.ExpoPartnerRequest) (*v1.ExpoPartnerReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	partners, err := s.expoPartner.FindByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoPartner.FindByExpoId Err:%v", err)
		return nil, err
	}

	var (
		groups       = make([]*v1.ExpoPartnerGroup, 0, len(partnerItems))
		groupMapping = make(map[int32]*v1.ExpoPartnerGroup, len(partnerItems))
	)

	for _, item := range partnerItems {
		var (
			name    = item.Name
			newName = i18n.GetWithDefaultEnglish(item.Key, languageCode)
		)

		if newName != "" {
			name = newName
		}

		newGroup := &v1.ExpoPartnerGroup{
			Name:     name,
			Partners: make([]*v1.ExpoPartnerItem, 0, len(partners)),
		}
		groups = append(groups, newGroup)
		groupMapping[item.Id] = newGroup
	}

	for _, partner := range partners {
		group, ok := groupMapping[int32(partner.Type)]
		if !ok {
			continue
		}
		group.Partners = append(group.Partners, &v1.ExpoPartnerItem{
			Logo:    urlformat.FullPath(partner.Logo, urlTemplate),
			Website: partner.Website,
		})
	}

	newGroups := make([]*v1.ExpoPartnerGroup, 0, len(groups))
	for _, group := range groups {
		if len(group.Partners) <= 0 {
			continue
		}
		newGroups = append(newGroups, group)
	}

	return &v1.ExpoPartnerReply{
		Groups: newGroups,
	}, nil
}

// GetSpeakerList 获取演讲嘉宾列表
func (s *Service) GetSpeakerList(ctx context.Context, in *v1.GetSpeakerListRequest) (*v1.GetSpeakerListReply, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	expo, err := s.expo.Get(ctx, expoId)
	if err != nil {
		l.Errorf("expo.Get Err:%v", err)
		return nil, err
	}
	zoneDiff := expo.GetZoneDiff()

	schedules, err := s.expoSchedule.FindByExpoId(ctx, expoId)
	if err != nil {
		l.Errorf("expoSchedule.FindByExpoIdWithSize Err:%v", err)
		return nil, err
	}

	var (
		scheduleIds     = make([]int64, 0, len(schedules))
		scheduleMapping = make(map[uint]*models.ExpoSchedule, len(schedules))
	)

	for _, schedule := range schedules {
		scheduleIds = append(scheduleIds, int64(schedule.ID))
		scheduleMapping[schedule.ID] = schedule
	}

	var scheduleGuests []*models.ExpoScheduleGuest
	if len(scheduleIds) > 0 {
		scheduleGuests, err = s.expoScheduleGuest.FindByScheduleIds(ctx, scheduleIds)
		if err != nil {
			l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
			return nil, err
		}
	}

	guestIds := make([]int64, 0, len(scheduleGuests))
	for _, scheduleGuest := range scheduleGuests {
		guestIds = append(guestIds, scheduleGuest.GuestId)
	}

	var guests []*models.Guest
	if len(guestIds) > 0 {
		guests, err = s.guest.FindByIds(ctx, guestIds)
		if err != nil {
			l.Errorf("guest.FindByIds Err:%v", err)
			return nil, err
		}
	}

	var (
		guestMapping         = make(map[uint]*models.Guest, len(guests))
		scheduleGuestMapping = make(map[uint][]*v1.ExpoSpeaker, len(schedules))
		reserves             []*models.ExpoScheduleReserve
	)

	for _, guest := range guests {
		if !guest.Enable {
			continue
		}
		guestMapping[guest.ID] = guest
	}

	if userId != "" {
		reserves, err = s.expoScheduleReserve.FindByUserId(ctx, userId, []int64{expoId})
		if err != nil {
			l.Errorf("expoScheduleReserve.ExistsByUserId Err:%v", err)
			return nil, err
		}
	}

	reserveMapping := make(map[int64]struct{}, len(reserves))
	for _, reserve := range reserves {
		reserveMapping[reserve.ScheduleGuestId] = struct{}{}
	}

	for _, guest := range scheduleGuests {
		guestInfo, ok := guestMapping[uint(guest.GuestId)]
		if !ok {
			continue
		}

		schedule, ok := scheduleMapping[uint(guest.ScheduleId)]
		if !ok {
			continue
		}

		var (
			guestLabel  []string
			guestDesc   string
			start       = schedule.Start.Add(zoneDiff).Format("2006.01.02 15:04")
			end         = schedule.End.Add(zoneDiff).Format("2006.01.02 15:04")
			theme       = schedule.GetTypeName(languageCode)
			description = schedule.Theme
		)

		if guestInfo.Extra != nil {
			lang, ok := guestInfo.Extra.V.Languages[languageCode]
			if ok {
				guestLabel = lang.Label
				guestDesc = lang.Description
			}

			lang, ok = guestInfo.Extra.V.Languages["en"]
			if ok && guestDesc == "" {
				guestLabel = lang.Label
				guestDesc = lang.Description
			}
		}

		if schedule.Extra != nil {
			lang, ok := schedule.Extra.V.Languages[languageCode]
			if ok {
				description = lang.Theme
			}

			lang, ok = schedule.Extra.V.Languages["en"]
			if ok && description == "" {
				description = lang.Theme
			}
		}

		if schedule.Start.Add(zoneDiff).Format(time.DateOnly) == schedule.End.Add(zoneDiff).Format(time.DateOnly) {
			end = schedule.End.Add(zoneDiff).Format("15:04")
		}

		var (
			subscribe = false
			role      = i18n.GetWithDefaultEnglish("63183", languageCode) // 演讲嘉宾
		)

		if guest.Role == 1 {
			role = i18n.GetWithDefaultEnglish("63358", languageCode) // 主持人
		}

		if _, ok = reserveMapping[int64(guest.ID)]; ok {
			subscribe = true
		}

		scheduleGuestMapping[uint(guest.ScheduleId)] = append(scheduleGuestMapping[uint(guest.ScheduleId)], &v1.ExpoSpeaker{
			SpeakerId:          strconv.FormatInt(int64(guestInfo.ID), 10),
			UserId:             guestInfo.UserId,
			Name:               guestInfo.Name,
			Avatar:             urlformat.FullPath(guestInfo.Avatar, urlTemplate),
			Timestamp:          schedule.Start.Unix(),
			ScheduleGuestId:    strconv.FormatInt(int64(guest.ID), 10),
			Theme:              theme,
			Description:        description,
			Subscribe:          subscribe,
			SpeakerDescription: guestDesc,
			Label:              guestLabel,
			TimestampShow:      fmt.Sprintf("%s-%s", start, end),
			ExpoId:             strconv.FormatInt(guest.ExpoId, 10),
			Start:              schedule.Start.Unix(),
			End:                schedule.End.Unix(),
			Role:               role,
		})
	}

	var (
		scheduleValues       = make([]*v1.ExpoSpeakerSchedule, 0, len(schedules))
		scheduleValueMapping = make(map[string]*v1.ExpoSpeakerSchedule, len(schedules))
		guestFilter          = make(map[string]struct{})
	)

	for _, schedule := range schedules {
		var (
			date                = schedule.Start.Add(zoneDiff).Format("2006.01.02")
			scheduleGuestsItems = scheduleGuestMapping[schedule.ID]
			addGuests           = make([]*v1.ExpoSpeaker, 0, len(scheduleGuests))
		)

		if len(scheduleGuestsItems) <= 0 {
			continue
		}

		for _, guest := range scheduleGuestsItems {
			key := fmt.Sprintf("%s-%s", date, guest.SpeakerId)
			if _, ok := guestFilter[key]; ok {
				continue
			}
			addGuests = append(addGuests, guest)
			guestFilter[key] = struct{}{}
		}

		scheduleValue, ok := scheduleValueMapping[date]
		if !ok {
			scheduleValue = &v1.ExpoSpeakerSchedule{
				Date:     date,
				Speakers: addGuests,
			}
			scheduleValueMapping[date] = scheduleValue
			scheduleValues = append(scheduleValues, scheduleValue)
			continue
		}
		scheduleValue.Speakers = append(scheduleValue.Speakers, addGuests...)
	}

	return &v1.GetSpeakerListReply{
		Schedule: scheduleValues,
	}, nil
}

// GetSpeakerDetail 获取演讲嘉宾详情
func (s *Service) GetSpeakerDetail(ctx context.Context, in *v1.GetSpeakerDetailRequest) (*v1.GetSpeakerDetailReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		expoId          int64
	)

	if in.ExpoId != "" {
		var err error
		expoId, err = strconv.ParseInt(in.ExpoId, 10, 64)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
	}

	spId, err := strconv.ParseInt(in.SpeakerId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	guest, err := s.guest.Get(ctx, spId)
	if err != nil {
		l.Errorf("guest.Get Err:%v", err)
		return nil, err
	}

	var (
		guestDesc  string
		guestLabel []string
		name       = guest.Name
		avatar     = urlformat.FullPath(guest.Avatar, urlTemplate)
		fans       int64
		follow     int64
		like       int64
	)

	if guest.UserId != "" {
		var userRes *userCenterv1.GetUserFollowAndFansCountReply
		userRes, err = s.user.GetUserFollowAndFansCount(ctx, &userCenterv1.GetUserFollowAndFansCountRequest{
			UserId: guest.UserId,
		})
		if err != nil {
			l.Errorf("GetUsersInfo Err:%v", err)
			// 不影响主流程
		} else {
			fans = int64(userRes.FansCount)
			follow = int64(userRes.FollowCount)
		}

		var comRes *communityV1.GetSingleUserApplaudCountReply
		comRes, err = s.community.GetSingleUserApplaudCount(ctx, &communityV1.GetSingleUserApplaudCountRequest{
			UserId: guest.UserId,
		})
		if err != nil {
			l.Errorf("community.GetSingleUserApplaudCount Err:%v", err)
			// 不影响主流程
		} else {
			like = comRes.ApplaudNumber
		}
	}

	if guest.Extra != nil {
		lang, ok := guest.Extra.V.Languages[languageCode]
		if ok {
			guestDesc = lang.Description
			guestLabel = lang.Label
		}

		lang, ok = guest.Extra.V.Languages["en"]
		if ok && guestDesc == "" {
			guestDesc = lang.Description
			guestLabel = lang.Label
		}
	}

	socialMedia := make([]*v1.SocialMedia, 0, 8)
	// facebook
	if guest.Facebook != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(facebookIcon, urlTemplate),
			Name:  "Facebook",
			Value: guest.Facebook,
		})
	}

	//  twitter
	if guest.Twitter != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(twitterIcon, urlTemplate),
			Name:  "Twitter",
			Value: guest.Twitter,
		})
	}

	// linkedin
	if guest.Linkedin != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(linkedinIcon, urlTemplate),
			Name:  "Linkedin",
			Value: guest.Linkedin,
		})
	}

	// instagram
	if guest.Instagram != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(instagramIcon, urlTemplate),
			Name:  "Instagram",
			Value: guest.Instagram,
		})
	}

	// reddit
	if guest.Reddit != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(redditIcon, urlTemplate),
			Name:  "Reddit",
			Value: guest.Reddit,
		})
	}

	// youtube
	if guest.Youtube != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(youtubeIcon, urlTemplate),
			Name:  "Youtube",
			Value: guest.Youtube,
		})
	}

	// telegram
	if guest.Telegram != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(telegramIcon, urlTemplate),
			Name:  "Telegram",
			Value: guest.Telegram,
		})
	}

	// tiktok
	if guest.TikTok != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(tiktokIcon, urlTemplate),
			Name:  "TikTok",
			Value: guest.TikTok,
		})
	}

	// wechat
	if guest.Wechat != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(wechatIcon, urlTemplate),
			Name:  "Wechat",
			Value: guest.Wechat,
		})
	}

	// whatsApp
	if guest.WhatsApp != "" {
		socialMedia = append(socialMedia, &v1.SocialMedia{
			Icon:  urlformat.FullPath(whatsAppIcon, urlTemplate),
			Name:  "WhatsApp",
			Value: guest.WhatsApp,
		})
	}

	roles, schedules, expos, err := s.getSpeakerSchedule(ctx, expoId, guest)
	if err != nil {
		l.Errorf("getSpeakerSchedule Err:%v", err)
		return nil, err
	}

	return &v1.GetSpeakerDetailReply{
		Id:          strconv.FormatInt(int64(guest.ID), 10),
		Name:        name,
		Avatar:      urlformat.FullPath(avatar, urlTemplate),
		Fans:        fans,
		Follow:      follow,
		Like:        like,
		Description: guestDesc,
		Label:       guestLabel,
		SocialMedia: socialMedia,
		Schedule:    schedules,
		UserId:      guest.UserId,
		Expos:       expos,
		IsTemporary: !guest.IsRegister,
		Roles:       roles,
	}, nil
}

func (s *Service) GetSpeakerSchedule(ctx context.Context, in *v1.GetSpeakerScheduleRequest) (*v1.GetSpeakerScheduleReply, error) {
	l := log.Context(ctx)

	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	spId, err := strconv.ParseInt(in.SpeakerId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	guest, err := s.guest.Get(ctx, spId)
	if err != nil {
		l.Errorf("guest.Get Err:%v", err)
		return nil, err
	}

	_, schedules, _, err := s.getSpeakerSchedule(ctx, expoId, guest)
	if err != nil {
		l.Errorf("getSpeakerSchedule Err:%v", err)
		return nil, err
	}

	return &v1.GetSpeakerScheduleReply{
		Schedule: schedules,
	}, nil
}

func (s *Service) getSpeakerSchedule(ctx context.Context, selectedExpoId int64, guestInfo *models.Guest) ([]string, []*v1.ExpoSpeakerSchedule, []*v1.SpeakerExpo, error) {
	var (
		l               = log.Context(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
	)

	var (
		guestDesc  string
		guestLabel []string
		reserves   []*models.ExpoScheduleReserve
		err        error
	)

	if guestInfo.Extra != nil {
		lang, ok := guestInfo.Extra.V.Languages[languageCode]
		if ok {
			guestDesc = lang.Description
			guestLabel = lang.Label
		}

		lang, ok = guestInfo.Extra.V.Languages["en"]
		if ok && guestDesc == "" {
			guestDesc = lang.Description
			guestLabel = lang.Label
		}
	}

	// 获取当前嘉宾参加的所有议程
	scheduleGuests, err := s.expoScheduleGuest.FindByGuestId(ctx, int64(guestInfo.ID))
	if err != nil {
		l.Errorf("expoScheduleGuest.FindByGuestId Err:%v", err)
		return nil, nil, nil, err
	}

	var (
		scheduleIds          = make([]int64, 0, len(scheduleGuests))
		scheduleGuestMapping = make(map[int64]*models.ExpoScheduleGuest, len(scheduleGuests))
	)

	for _, scheduleGuest := range scheduleGuests {
		scheduleIds = append(scheduleIds, scheduleGuest.ScheduleId)
		scheduleGuestMapping[scheduleGuest.ScheduleId] = scheduleGuest
	}

	// 获取所有的议程
	schedules, err := s.expoSchedule.FindByIds(ctx, scheduleIds)
	if err != nil {
		l.Errorf("expoSchedule.FindByIds Err:%v", err)
		return nil, nil, nil, err
	}

	var (
		expos       []*models.Expo
		expoIds     = make([]int64, 0, len(schedules))
		expoMapping = make(map[int64]*models.Expo, len(schedules))
	)

	for _, schedule := range schedules {
		expoIds = append(expoIds, schedule.ExpoId)
		if selectedExpoId == 0 {
			selectedExpoId = schedule.ExpoId
		}
	}

	if len(expoIds) > 0 {
		expos, err = s.expo.FindByIds(ctx, expoIds)
		if err != nil {
			l.Errorf("expo.FindByIds Err:%v", err)
			return nil, nil, nil, err
		}
	}

	for _, expo := range expos {
		expoMapping[expo.ID] = expo
	}

	if userId != "" {
		reserves, err = s.expoScheduleReserve.FindByUserId(ctx, userId, expoIds)
		if err != nil {
			l.Errorf("expoScheduleReserve.ExistsByUserId Err:%v", err)
			return nil, nil, nil, err
		}
	}

	// 当前用户预约的议程
	reserveMapping := make(map[int64]struct{}, len(reserves))
	for _, reserve := range reserves {
		reserveMapping[reserve.ScheduleGuestId] = struct{}{}
	}

	var (
		guestSchedules         = make([]*v1.ExpoSpeakerSchedule, 0, len(schedules))
		scheduleValueMapping   = make(map[string]*v1.ExpoSpeakerSchedule, len(schedules))
		guestExpos             = make([]*v1.SpeakerExpo, 0, len(schedules))
		guestExpoMapping       = make(map[int64]struct{}, len(schedules))
		currentExpoRoles       []string
		currentExpoRoleMapping = make(map[string]struct{}, len(schedules))
	)

	for _, schedule := range schedules {
		expo, ok := expoMapping[schedule.ExpoId]
		if !ok {
			continue
		}

		var (
			zoneDiff = expo.GetZoneDiff()
			now      = time.Now().UTC().Add(zoneDiff)
		)

		guest, ok := scheduleGuestMapping[int64(schedule.ID)]
		if !ok {
			continue
		}

		var (
			start        = schedule.Start.Add(zoneDiff).Format("2006.01.02 15:04")
			end          = schedule.End.Add(zoneDiff).Format("2006.01.02 15:04")
			theme        = schedule.GetTypeName(languageCode)
			scheduleDesc = schedule.Theme
			subscribe    bool
			role         = i18n.GetWithDefaultEnglish("63183", languageCode) // 演讲嘉宾
			selected     = schedule.ExpoId == selectedExpoId
			roleIcon     = speakerRoleIcon
		)

		if guest.Role == 1 {
			role = i18n.GetWithDefaultEnglish("63358", languageCode) // 主持人
			roleIcon = hostRoleIcon
		}

		if selected {
			if _, ok = currentExpoRoleMapping[role]; !ok {
				currentExpoRoles = append(currentExpoRoles, role)
				currentExpoRoleMapping[role] = struct{}{}
			}
		}

		if _, ok = guestExpoMapping[schedule.ExpoId]; !ok {
			guestExpos = append(guestExpos, &v1.SpeakerExpo{
				ExpoId:   strconv.FormatInt(schedule.ExpoId, 10),
				Name:     expo.Name,
				Selected: selected,
			})
			guestExpoMapping[schedule.ExpoId] = struct{}{}
		}

		if selectedExpoId > 0 && schedule.ExpoId != selectedExpoId {
			continue
		}

		if _, ok = reserveMapping[int64(schedule.ID)]; ok {
			subscribe = true
		}

		if schedule.Extra != nil {
			var lang *v1.ExpoScheduleLanguage
			lang, ok = schedule.Extra.V.Languages[languageCode]
			if ok {
				scheduleDesc = lang.Theme
			}

			lang, ok = schedule.Extra.V.Languages["en"]
			if ok && scheduleDesc == "" {
				scheduleDesc = lang.Theme
			}
		}

		if schedule.Start.Add(zoneDiff).Format(time.DateOnly) == schedule.End.Add(zoneDiff).Format(time.DateOnly) {
			end = schedule.End.Add(zoneDiff).Format("15:04")
		}

		var (
			date       = schedule.Start.Add(zoneDiff).Format("2006.01.02")
			newSpeaker = &v1.ExpoSpeaker{
				SpeakerId:          strconv.FormatInt(int64(guestInfo.ID), 10),
				UserId:             guestInfo.UserId,
				Name:               guestInfo.Name,
				Avatar:             urlformat.FullPath(guestInfo.Avatar, urlTemplate),
				Timestamp:          schedule.Start.Unix(),
				ScheduleGuestId:    strconv.FormatInt(int64(guest.ID), 10),
				Theme:              theme,
				Description:        scheduleDesc,
				Subscribe:          subscribe,
				SpeakerDescription: guestDesc,
				Label:              guestLabel,
				TimestampShow:      fmt.Sprintf("%s-%s", start, end),
				ExpoId:             strconv.FormatInt(expo.ID, 10),
				Start:              schedule.Start.Unix(),
				End:                schedule.End.Unix(),
				Role:               role,
				IsTemporary:        !guestInfo.IsRegister,
				RoleIcon:           roleIcon,
				CanSubscribe:       now.Before(schedule.Start.Add(zoneDiff)),
			}
		)

		scheduleValue, ok := scheduleValueMapping[date]
		if !ok {
			scheduleValue = &v1.ExpoSpeakerSchedule{
				Date: date,
				Speakers: []*v1.ExpoSpeaker{
					newSpeaker,
				},
			}
			scheduleValueMapping[date] = scheduleValue
			guestSchedules = append(guestSchedules, scheduleValue)
			continue
		}
		scheduleValue.Speakers = append(scheduleValue.Speakers, newSpeaker)
	}
	return currentExpoRoles, guestSchedules, guestExpos, nil
}
