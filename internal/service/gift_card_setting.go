package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) GiftCardSettingPageList(ctx context.Context, request *v1.GiftCardSettingPageListRequest) (*v1.GiftCardSettingPageListReply, error) {
	list, total, err := s.giftCardSetting.PageList(ctx, int(request.Size), int(request.Page))
	fmt.Println("22")
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	fmt.Println("11")
	var (
		l     = log.Context(ctx)
		items = make([]*v1.GiftCardSettingPageListReplyItem, 0, len(list))
		//查询产品名称

	)

	// 查询已发送了多少用户
	groupRes, err := s.userGiftCard.UserGiftCardGroupBySettingId(ctx)
	if err != nil {
		l.Errorf("UserGiftCardGroupBySettingId Err:%v", err)
		return &v1.GiftCardSettingPageListReply{}, nil
	}

	for _, v := range list {
		inDateType := "相对"
		startTime := ""
		endTime := ""
		if v.InDate == 0 {
			inDateType = "固定"
			startTime = v.StartTime.Format("2006-01-02 15:04:05")
			endTime = v.EndTime.Format("2006-01-02 15:04:05")
		}
		usedTimes := 0
		singleGroup := FirstT(groupRes, func(m *models.UserGiftCardGroupby) bool {
			return v.ID == m.SettingID
		})
		if singleGroup != nil {
			usedTimes = singleGroup.Count
		}
		items = append(items, &v1.GiftCardSettingPageListReplyItem{
			Id:               int32(v.ID),
			GoodsId:          v.GoodsID,
			InDate:           inDateType,
			InDateDays:       strconv.Itoa(int(v.IndateDays)),
			StartTime:        startTime,
			EndTime:          endTime,
			UsedTimes:        int32(usedTimes),
			UseTimes:         v.UseTimes,
			PromotionChannel: v.PromotionChannel,
			Proposer:         v.Proposer,
			CreatedAt:        v.CreatedAt.Format("2006-01-02 15:04:05"),
			CreatedUser:      v.CreatedUser,
		})
	}

	return &v1.GiftCardSettingPageListReply{
		Total: int32(total),
		Items: items,
	}, nil
}

func (s *Service) EditGiftCardSetting(ctx context.Context, request *v1.EditGiftCardSettingRequest) (*v1.EditGiftCardSettingReply, error) {
	now := time.Now().UTC()
	model := models.GiftCardSetting{
		GoodsID:          request.GoodsId,
		IsPinkage:        request.IsPinkage,
		InDate:           request.InDate,
		IndateDays:       request.InDateDays,
		UseTimes:         request.UseTimes,
		Proposer:         request.Proposer,
		PromotionChannel: request.PromotionChannel,
		CreatedUser:      request.CreatedUser,
		UpdatedAt:        now,
		CreatedAt:        now,
		Image:            request.Image,
	}
	if request.InDate == 1 {
		model.StartTime = now
		model.EndTime = now
	} else {
		layout := "2006-01-02 15:04:05"
		startTime, _ := time.Parse(time.DateTime, request.StartTime)
		endTime, _ := time.Parse(layout, request.EndTime)
		model.StartTime = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.UTC)
		model.EndTime = time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 23, 59, 59, 0, time.UTC)
	}
	if request.Id > 0 {
		single, err := s.giftCardSetting.GetSingle(ctx, int64(request.Id))
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
		model.CreatedAt = single.CreatedAt
		model.ID = int64(request.Id)
		fmt.Println(model.StartTime)
		s.giftCardSetting.Edit(ctx, &model)
	} else {
		s.giftCardSetting.Add(ctx, &model)
	}
	return &v1.EditGiftCardSettingReply{}, nil
}

func (s *Service) DeleteGiftCardSetting(ctx context.Context, request *v1.DeleteGiftCardSettingRequest) (*v1.DeleteGiftCardSettingReply, error) {
	err := s.giftCardSetting.Delete(ctx, int64(request.Id))
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	return &v1.DeleteGiftCardSettingReply{}, nil
}
func (s *Service) GetSingleGiftCardSetting(ctx context.Context, request *v1.GetSingleGiftCardSettingRequest) (*v1.GetSingleGiftCardSettingReply, error) {
	single, err := s.giftCardSetting.GetSingle(ctx, int64(request.Id))
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	goodsInfos, err := s.findGoodsInfo(ctx, []string{single.GoodsID})
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	goodsName := ""
	if len(goodsInfos) > 0 {
		goodsName = goodsInfos[0].Name
	}
	return &v1.GetSingleGiftCardSettingReply{
		Id:               single.ID,
		InDate:           single.InDate,
		InDateDays:       single.IndateDays,
		StartTime:        single.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:          single.EndTime.Format("2006-01-02 15:04:05"),
		PromotionChannel: single.PromotionChannel,
		Proposer:         single.Proposer,
		CreatedUser:      single.CreatedUser,
		UseTimes:         single.UseTimes,
		GoodsId:          single.GoodsID,
		GoodsName:        goodsName,
		IsPinkage:        single.IsPinkage,
		Image:            single.Image,
	}, nil

}

func (s *Service) GetSingleGiftCardSettingIsSend(ctx context.Context, request *v1.GetSingleGiftCardSettingIsSendRequest) (*v1.GetSingleGiftCardSettingIsSendReply, error) {
	res, err := s.userGiftCard.GiftCardSettingIsSend(ctx, int64(request.Id))
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	return &v1.GetSingleGiftCardSettingIsSendReply{
		IsUse: res,
	}, nil
}

func (s *Service) SendGiftCard(ctx context.Context, request *v1.SendGiftCardRequest) (*v1.SendGiftCardReply, error) {
	single, err := s.giftCardSetting.GetSingle(ctx, int64(request.SettingId))
	now := time.Now().UTC()
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	var userGiftCards []models.UserGiftCard
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.UTC)
	fmt.Print(startTime)
	for _, v := range request.UserIds {
		endTime := time.Now()
		if single.InDate == 0 { //固定
			if now.After(single.StartTime) {
				startTime = single.StartTime
			}
			endTime = single.EndTime
		} else {
			endTime = startTime.AddDate(0, 0, int(single.InDate)).Add(time.Hour*23 + time.Minute*59 + time.Second*59)
		}
		single := models.UserGiftCard{
			UserID:     v,
			SettingID:  single.ID,
			GoodsID:    single.GoodsID,
			IsPinkage:  single.IsPinkage,
			Status:     0,
			CreatedAt:  now,
			UpdatedAt:  now,
			StartTime:  startTime,
			EndTime:    endTime,
			ShowNotice: 0,
		}
		fmt.Println(single.CreatedAt)
		userGiftCards = append(userGiftCards, single)
	}
	err = s.userGiftCard.AddBatches(ctx, userGiftCards)
	return &v1.SendGiftCardReply{}, err

}

func (s *Service) SendGiftCardRecord(ctx context.Context, request *v1.SendGiftCardRecordRequest) (*v1.SendGiftCardRecordReply, error) {
	var items []*v1.SendGiftCardRecordItem
	now := time.Now().UTC()
	list, total, err := s.userGiftCard.GetPageListBySettingId(ctx, int(request.SettingId), int(request.Size), int(request.Page))
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	for _, v := range list {
		showStatus := ""
		if v.Status == 1 {
			showStatus = "已使用"
		} else if v.EndTime.Before(now) {
			showStatus = "已过期"
		} else {
			showStatus = "未使用"
		}
		userTime := ""
		if v.Status == 1 {
			userTime = v.UsedTime.Format(time.DateTime)
		}
		items = append(items, &v1.SendGiftCardRecordItem{
			UserId:      v.UserID,
			ReceiveTime: v.CreatedAt.Format(time.DateTime),
			UsedTime:    userTime,
			Status:      v.Status,
			ShowStatus:  showStatus,
			OrderId:     v.OrderId,
		})
	}
	return &v1.SendGiftCardRecordReply{
		Total: int32(total),
		Items: items,
	}, err
}

// GetGoodsIsConfigGiftCard 商品是否配置了卡券
func (s *Service) GetGoodsIsConfigGiftCard(ctx context.Context, goodsId string) (bool, error) {
	isExist, err := s.giftCardSetting.GetGoodsIsConfigGiftCard(ctx, goodsId)
	if err != nil {
		return false, innErr.ErrBadRequest
	}
	return isExist, nil

}
