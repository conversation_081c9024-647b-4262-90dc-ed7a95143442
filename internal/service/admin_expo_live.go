package service

import (
	"context"
	"errors"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) checkExpoLive(ctx context.Context, in *v1.ExpoLive) error {
	if in.ExpoId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.Url == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "直播地址不能为空")
	}

	if in.Cover == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "封面不能为空")
	}

	live, err := s.expoLive.GetByExpoIdAndLevel(ctx, in.ExpoId, in.Level)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		return err
	}

	// 说明存在
	if err == nil && int64(live.ID) != in.Id {
		return innErr.WithMessage(innErr.ErrBadRequest, "该等级已存在")
	}
	//if in.RoomId == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "房间号不能为空")
	//}
	//
	//if in.UserId == "" {
	//	return innErr.WithMessage(innErr.ErrBadRequest, "用户ID不能为空")
	//}
	return nil
}

func (s *Service) AddExpoLive(ctx context.Context, in *v1.ExpoLive) (*v1.AddExpoLiveReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoLive(ctx, in)
	if err != nil {
		return nil, err
	}

	id, err := s.expoLive.Add(ctx, &models.ExpoLive{
		ExpoId:  in.ExpoId,
		Url:     in.Url,
		RoomId:  in.RoomId,
		UserId:  in.UserId,
		Cover:   in.Cover,
		Level:   in.Level,
		Enable:  in.Enable,
		Creator: in.Creator,
	})
	if err != nil && !errors.Is(err, ormhelper.ErrDuplicateKey) {
		l.Errorf("expoLive.Add Err:%v", err)
		return nil, err
	}

	return &v1.AddExpoLiveReply{
		Id: int64(id),
	}, nil
}

func (s *Service) GetExpoLive(ctx context.Context, in *v1.GetExpoLiveRequest) (*v1.ExpoLive, error) {
	l := log.Context(ctx)

	live, err := s.expoLive.Get(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoLive.Get Err:%v", err)
		return nil, err
	}
	return s.expoLiveToGRPC(live), nil
}

func (s *Service) UpdateExpoLive(ctx context.Context, in *v1.ExpoLive) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoLive(ctx, in)
	if err != nil {
		return nil, err
	}

	err = s.expoLive.Update(ctx, &models.ExpoLive{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId:  in.ExpoId,
		Url:     in.Url,
		RoomId:  in.RoomId,
		UserId:  in.UserId,
		Cover:   in.Cover,
		Level:   in.Level,
		Enable:  in.Enable,
		Creator: in.Creator,
	})
	if err != nil {
		l.Errorf("Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoLiveEnable(ctx context.Context, in *v1.SetExpoLiveEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoLive.UpdateEnable(ctx, in.ExpoId, in.Id, in.Enable)
	if err != nil {
		l.Errorf("expoLive.UpdateEnable Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListExpoLive(ctx context.Context, in *v1.ListExpoLiveRequest) (*v1.ListExpoLiveReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	lives, err := s.expoLive.FindByPage(ctx, in.ExpoId, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoLive.FindByPage Err:%v", err)
		return nil, err
	}

	total, err := s.expoLive.CountByExpoId(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoLive.CountByExpoId Err:%v", err)
		return nil, err
	}

	values := make([]*v1.ExpoLive, 0, len(lives))
	for _, live := range lives {
		values = append(values, s.expoLiveToGRPC(live))
	}
	return &v1.ListExpoLiveReply{
		Lives: values,
		Total: total,
	}, nil
}

func (s *Service) DeleteExpoLive(ctx context.Context, in *v1.DeleteExpoLiveRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoLive.Delete(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoLive.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) expoLiveToGRPC(in *models.ExpoLive) *v1.ExpoLive {
	return &v1.ExpoLive{
		Id:        int64(in.ID),
		ExpoId:    in.ExpoId,
		Level:     in.Level,
		Cover:     in.Cover,
		Url:       in.Url,
		RoomId:    in.RoomId,
		UserId:    in.UserId,
		AppId:     in.AppId,
		Enable:    in.Enable,
		CreatedAt: in.CreatedAt.Unix(),
		Creator:   in.Creator,
	}
}
