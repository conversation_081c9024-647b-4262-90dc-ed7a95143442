package service

import (
	"context"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) checkExpoReview(in *v1.ExpoReviewInfo) error {
	if in.ExpoId <= 0 {
		return innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if in.Type == v1.ExpoReviewType_REVIEW_TYPE_VIDEO {
		if in.Languages == nil {
			return innErr.WithMessage(innErr.ErrBadRequest, "视频描述不能为空")
		}

		if in.Cover == "" {
			return innErr.WithMessage(innErr.ErrBadRequest, "封面不能为空")
		}
	}
	return nil
}

func (s *Service) AddExpoReview(ctx context.Context, in *v1.ExpoReviewInfo) (*v1.AddExpoReviewReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoReview(in)
	if err != nil {
		return nil, err
	}

	count := int64(1)
	if in.Type == v1.ExpoReviewType_REVIEW_TYPE_PICTURE {
		count = int64(len(in.ImageUrl))
	}

	id, err := s.expoReview.Add(ctx, &models.ExpoReview{
		ExpoId:  in.ExpoId,
		Type:    in.Type,
		URL:     in.VideoUrl,
		Count:   count,
		Cover:   in.Cover,
		Enable:  in.Enable,
		Creator: in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoReviewExtra]{
			V: &models.ExpoReviewExtra{
				Languages: in.Languages,
				Images:    in.ImageUrl,
			},
		},
	})

	if err != nil {
		l.Errorf("expoReview.Add Err:%v", err)
		return nil, err
	}

	return &v1.AddExpoReviewReply{
		Id: int64(id),
	}, nil
}

func (s *Service) GetExpoReview(ctx context.Context, in *v1.GetExpoReviewRequest) (*v1.ExpoReviewInfo, error) {
	l := log.Context(ctx)

	review, err := s.expoReview.GetById(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoReview.GetById Err:%v", err)
		return nil, err
	}
	return s.expoReviewToGRPC(review), nil
}

func (s *Service) UpdateExpoReview(ctx context.Context, in *v1.ExpoReviewInfo) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.checkExpoReview(in)
	if err != nil {
		return nil, err
	}

	count := int64(1)
	if in.Type == v1.ExpoReviewType_REVIEW_TYPE_PICTURE {
		count = int64(len(in.ImageUrl))
	}

	err = s.expoReview.Update(ctx, &models.ExpoReview{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId:  in.ExpoId,
		Type:    in.Type,
		URL:     in.VideoUrl,
		Count:   count,
		Cover:   in.Cover,
		Enable:  in.Enable,
		Creator: in.Creator,
		Extra: &igorm.CustomValue[*models.ExpoReviewExtra]{
			V: &models.ExpoReviewExtra{
				Languages: in.Languages,
				Images:    in.ImageUrl,
			},
		},
	})
	if err != nil {
		l.Errorf("Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) SetExpoReviewEnable(ctx context.Context, in *v1.SetExpoReviewEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.expoReview.UpdateEnable(ctx, &models.ExpoReview{
		Model: gorm.Model{
			ID: uint(in.Id),
		},
		ExpoId: in.ExpoId,
		Enable: in.Enable,
	})
	if err != nil {
		l.Errorf("Update Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListExpoReview(ctx context.Context, in *v1.ListExpoReviewRequest) (*v1.ListExpoReviewReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}
	l := log.Context(ctx)

	count, err := s.expoReview.CountByExpoId(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoReview.CountByExpoId Err:%v", err)
		return nil, err
	}

	reviews, err := s.expoReview.FindByPage(ctx, in.ExpoId, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("expoReview.FindByPage Err:%v", err)
		return nil, err
	}

	values := make([]*v1.ExpoReviewInfo, 0, len(reviews))
	for _, review := range reviews {
		values = append(values, s.expoReviewToGRPC(review))
	}
	return &v1.ListExpoReviewReply{
		Items: values,
		Total: count,
	}, nil
}

func (s *Service) DeleteExpoReview(ctx context.Context, in *v1.DeleteExpoReviewRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoReview.Delete(ctx, in.ExpoId, in.Id)
	if err != nil {
		l.Errorf("expoReview.Delete Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) expoReviewToGRPC(in *models.ExpoReview) *v1.ExpoReviewInfo {
	var (
		imageUrls []string
		fileCount = 1
		languages map[string]*v1.ExpoReviewLanguage
	)

	if in.Extra != nil && in.Extra.V != nil {
		imageUrls = in.Extra.V.Images
		fileCount = len(imageUrls)
		languages = in.Extra.V.Languages
	}

	return &v1.ExpoReviewInfo{
		Id:        int64(in.ID),
		ExpoId:    in.ExpoId,
		Type:      in.Type,
		VideoUrl:  in.URL,
		ImageUrl:  imageUrls,
		Cover:     in.Cover,
		Languages: languages,
		FileCount: int64(fileCount),
		Enable:    in.Enable,
		CreatedAt: in.CreatedAt.Unix(),
		Creator:   in.Creator,
	}
}
