package service

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	userCenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
)

func (s *Service) AddExpoGuest(ctx context.Context, in *v1.AddExpoGuestRequest) (*v1.AddExpoGuestReply, error) {
	l := log.Context(ctx)
	if in.ExpoId <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会ID不能为空")
	}

	if len(in.GuestIds) < 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "请添加嘉宾")
	}

	guests, err := s.expoGuest.FindByGuestIds(ctx, in.ExpoId, in.GuestIds)
	if err != nil {
		l.<PERSON>("expoGuest.FindByGuestIds Err:%v", err)
		return nil, err
	}

	if len(guests) > 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "嘉宾已经存在")
	}

	newGuests := make([]*models.ExpoGuest, 0, len(in.GuestIds))
	for _, guestId := range in.GuestIds {
		newGuests = append(newGuests, &models.ExpoGuest{
			ExpoId:  in.ExpoId,
			GuestId: guestId,
			Enable:  true,
			Creator: in.Creator,
		})
	}

	err = s.expoGuest.BatchAdd(ctx, newGuests)
	if err != nil {
		l.Errorf("expoGuest.BatchAdd Err:%v", err)
		return nil, err
	}
	return &v1.AddExpoGuestReply{}, nil
}

func (s *Service) GetExpoGuest(ctx context.Context, in *v1.GetExpoGuestRequest) (*v1.GetExpoGuestReply, error) {
	l := log.Context(ctx)
	// 查询当前展会的嘉宾
	expoGuests, err := s.expoGuest.FindByGuestIds(ctx, in.ExpoId, []int64{in.GuestId})
	if err != nil {
		l.Errorf("expoGuest.FindByGuestIds Err:%v", err)
		return nil, err
	}

	if len(expoGuests) < 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "当前展会没有该嘉宾")
	}

	// 查询当前展会的所有议程
	expoSchedules, err := s.expoSchedule.FindByExpoId(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoSchedule.FindByExpoId Err:%v", err)
		return nil, err
	}

	var (
		scheduleIds     = make([]int64, 0, len(expoSchedules))
		scheduleMapping = make(map[int64]*models.ExpoSchedule, len(expoSchedules))
	)

	for _, schedule := range expoSchedules {
		scheduleIds = append(scheduleIds, int64(schedule.ID))
		scheduleMapping[int64(schedule.ID)] = schedule
	}

	var (
		scheduleGuests      []*models.ExpoScheduleGuest                                        // 当前议程的所有嘉宾
		unionScheduleGuests = make(map[int64][]*models.ExpoScheduleGuest, len(scheduleGuests)) // 所有的[议程-嘉宾]映射
		joinScheduleIds     = make(map[int64]struct{}, len(scheduleIds))                       // 当前嘉宾参与的议程列表
	)

	if len(scheduleIds) > 0 {
		// 获取所有议程的嘉宾
		scheduleGuests, err = s.expoScheduleGuest.FindByScheduleIds(ctx, scheduleIds)
		if err != nil {
			l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
			return nil, err
		}
	}

	for _, guest := range scheduleGuests {
		unionScheduleGuests[guest.ScheduleId] = append(unionScheduleGuests[guest.ScheduleId], guest)
		if guest.GuestId == in.GuestId {
			joinScheduleIds[guest.ScheduleId] = struct{}{}
		}
	}
	// 当前嘉宾是主持人 也加入议程列表
	for _, schedule := range expoSchedules {
		if schedule.HostId == in.GuestId {
			joinScheduleIds[int64(schedule.ID)] = struct{}{}
		}
	}

	var (
		guestIds = make([]int64, 0, len(scheduleGuests)) // 当前嘉宾有交集的所有嘉宾
		hallIds  = make([]int64, 0, len(scheduleIds))    // 当前嘉宾有交集的会场
	)
	//收集当前嘉宾同议程的其他主持人和演讲嘉宾
	for scheduleId, values := range unionScheduleGuests {
		if _, ok := joinScheduleIds[scheduleId]; !ok {
			continue
		}

		for _, value := range values {
			guestIds = append(guestIds, value.GuestId)
		}

		schedule, ok := scheduleMapping[scheduleId]
		if !ok {
			continue
		}
		guestIds = append(guestIds, schedule.HostId)
	}
	guestIds = append(guestIds, in.GuestId)
	//收集当前嘉宾参与的议程的会场
	for _, schedule := range expoSchedules {
		if _, ok := joinScheduleIds[int64(schedule.ID)]; !ok {
			continue
		}
		hallIds = append(hallIds, schedule.HallId)
	}

	var (
		guests []*models.Guest
		halls  []*models.ExpoHall
	)

	if len(guestIds) > 0 {
		guests, err = s.guest.FindByIdsWithUnscoped(ctx, guestIds)
		if err != nil {
			l.Errorf("guest.FindByIds Err:%v", err)
			return nil, err
		}
	}

	if len(hallIds) > 0 {
		halls, err = s.expoHall.FindByIds(ctx, hallIds)
		if err != nil {
			l.Errorf("expoHall.FindByIds Err:%v", err)
			return nil, err
		}
	}

	var (
		guestMapping = make(map[int64]*models.Guest, len(guests))
		hallMapping  = make(map[int64]*models.ExpoHall, len(halls))

		currentGuest *models.Guest
	)

	for _, guest := range guests {
		guestMapping[int64(guest.ID)] = guest

		if guest.ID == uint(in.GuestId) {
			currentGuest = guest
		}
	}

	if currentGuest == nil {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "没有该嘉宾")
	}

	for _, hall := range halls {
		hallMapping[int64(hall.ID)] = hall
	}

	var schedules []*v1.ExpoGuestSchedule
	for _, schedule := range expoSchedules {
		if _, ok := joinScheduleIds[int64(schedule.ID)]; !ok {
			continue
		}

		hall, ok := hallMapping[schedule.HallId]
		if !ok {
			continue
		}

		var (
			hostItem   *v1.ExpoGuestScheduleItem
			guestsItem []*v1.ExpoGuestScheduleItem
			hallType   = "主会场"
			hallName   string
		)

		if hall.Type == v1.ExpoHallType_ExpoHallType_SUB {
			hallType = "子会场"
		}

		if hall.Extra != nil {
			for key, value := range hall.Extra.V.Languages {
				if strings.ToLower(key) == "zh-cn" {
					hallName = value.Name
					break
				}
			}
		}

		hostGuest, ok := guestMapping[schedule.HostId]
		if ok {
			hostItem = &v1.ExpoGuestScheduleItem{
				GuestId:    int64(hostGuest.ID),
				Name:       hostGuest.Name,
				Phone:      hostGuest.Phone,
				Email:      hostGuest.Email,
				Avatar:     hostGuest.Avatar,
				WikiNumber: hostGuest.WikiNumber,
				UserId:     hostGuest.UserId,
			}
		}

		for _, value := range unionScheduleGuests[int64(schedule.ID)] {
			//if value.GuestId == in.GuestId {
			//	continue
			//}
			guestTemp := guestMapping[value.GuestId]
			guestsItem = append(guestsItem, &v1.ExpoGuestScheduleItem{
				GuestId:    int64(guestTemp.ID),
				Name:       guestTemp.Name,
				Phone:      guestTemp.Phone,
				Email:      guestTemp.Email,
				Avatar:     guestTemp.Avatar,
				WikiNumber: guestTemp.WikiNumber,
				UserId:     guestTemp.UserId,
			})
		}
		scheduleTypeMap := map[v1.ScheduleType]string{
			v1.ScheduleType_ScheduleType_ALL:        "全部",
			v1.ScheduleType_ScheduleType_Theme:      "主题演讲",
			v1.ScheduleType_ScheduleType_Lecture:    "围炉谈话",
			v1.ScheduleType_ScheduleType_RoundTable: "圆桌会议",
		}
		schedules = append(schedules, &v1.ExpoGuestSchedule{
			HallType:         fmt.Sprintf("%s-%s", hallType, hallName),
			Theme:            schedule.Theme,
			Start:            schedule.Start.Unix(),
			End:              schedule.End.Unix(),
			Host:             hostItem,
			Guests:           guestsItem,
			ScheduleType:     schedule.Type,
			ScheduleTypeName: scheduleTypeMap[schedule.Type],
		})
	}

	currentGuestItem := &v1.Guest{
		Id:            int64(currentGuest.ID),
		Name:          currentGuest.Name,
		Avatar:        currentGuest.Avatar,
		Email:         currentGuest.Email,
		Phone:         currentGuest.Phone,
		PhoneAreaCode: currentGuest.PhoneAreaCode,
		WikiNumber:    currentGuest.WikiNumber,
		Creator:       currentGuest.Creator,
		Languages:     currentGuest.Extra.V.Languages,
		Facebook:      currentGuest.Facebook,
		Instagram:     currentGuest.Instagram,
		Linkedin:      currentGuest.Linkedin,
		Reddit:        currentGuest.Reddit,
		Telegram:      currentGuest.Telegram,
		Tiktok:        currentGuest.TikTok,
		Twitter:       currentGuest.Twitter,
		Wechat:        currentGuest.Wechat,
		WhatsApp:      currentGuest.WhatsApp,
		Youtube:       currentGuest.Youtube,
		UserId:        currentGuest.UserId,
		Enable:        currentGuest.Enable,
		CreatedAt:     currentGuest.CreatedAt.Unix(),
		UpdatedAt:     currentGuest.UpdatedAt.Unix(),
	}

	return &v1.GetExpoGuestReply{
		Guest:     currentGuestItem,
		Schedules: schedules,
	}, nil
}

func (s *Service) ListExpoGuest(ctx context.Context, in *v1.ListExpoGuestRequest) (*v1.ListExpoGuestReply, error) {
	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	l := log.Context(ctx)
	expoGuests, total, err := s.guest.SearchWithExpoGuest(ctx, in.ExpoId, in.Name, in.Mobile, in.Email, in.Type, int(in.Page), int(in.Size))
	if err != nil {
		l.Errorf("guest.SearchWithExpoGuest Err:%v", err)
		return nil, err
	}

	groups, err := s.expoScheduleGuest.GroupByExpoId(ctx, in.ExpoId)
	if err != nil {
		l.Errorf("expoScheduleGuest.GroupByExpoId Err:%v", err)
		return nil, err
	}

	groupMapping := make(map[int64]*models.ExpoScheduleGuestGroup, len(groups))
	for _, value := range groups {
		groupMapping[value.GuestId] = value
	}

	guests := make([]*v1.ExpoGuest, 0, len(expoGuests))
	for _, guest := range expoGuests {
		var (
			avatar        = guest.Avatar
			name          = guest.Name
			wikiNumber    = guest.WikiNumber
			scheduleCount int32
		)

		if group, ok := groupMapping[int64(guest.ID)]; ok {
			scheduleCount = int32(group.Count)
		}

		guests = append(guests, &v1.ExpoGuest{
			ExpoId:        in.ExpoId,
			GuestId:       int64(guest.ID),
			Avatar:        avatar,
			Name:          name,
			WikiNumber:    wikiNumber,
			Phone:         guest.Phone,
			Email:         guest.Email,
			ScheduleCount: scheduleCount,
			Enable:        guest.Enable,
			CreatedAt:     guest.CreatedAt.Unix(),
			Creator:       guest.Creator,
		})
	}

	return &v1.ListExpoGuestReply{
		Guests: guests,
		Total:  total,
	}, nil
}

func (s *Service) DeleteExpoGuest(ctx context.Context, in *v1.DeleteExpoGuestRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	if len(in.GuestIds) <= 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "请选择要删除的嘉宾")
	}

	err := s.expoGuest.DeleteByGuestIds(ctx, in.ExpoId, in.GuestIds)
	if err != nil {
		l.Errorf("expoGuest.DeleteByGuestIds Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) guestToExpoGuestScheduleItem(in *models.Guest, users map[string]*userCenterv1.UserInfo) *v1.ExpoGuestScheduleItem {
	if in == nil {
		return nil
	}

	var (
		avatar     = in.Avatar
		name       = in.Name
		wikiNumber = in.WikiNumber
	)

	if in.UserId != "" {
		user, ok := users[in.UserId]
		if ok {
			avatar = user.AvatarAddress
			name = user.NickName
			wikiNumber = user.WikiFxNumber
		}
	}

	return &v1.ExpoGuestScheduleItem{
		GuestId:    int64(in.ID),
		Name:       name,
		UserId:     in.UserId,
		WikiNumber: wikiNumber,
		Avatar:     avatar,
		Phone:      in.Phone,
		Email:      in.Email,
	}
}

func (s *Service) guestToGRPC(in *models.Guest, users map[string]*userCenterv1.UserInfo) *v1.Guest {
	if in == nil {
		return nil
	}

	var (
		avatar     = in.Avatar
		name       = in.Name
		wikiNumber = in.WikiNumber
		languages  map[string]*v1.GuestLanguage
	)

	if in.Extra != nil && in.Extra.V != nil {
		languages = in.Extra.V.Languages
	}

	if in.UserId != "" {
		user, ok := users[in.UserId]
		if ok {
			avatar = user.AvatarAddress
			name = user.NickName
			wikiNumber = user.WikiFxNumber
		}
	}

	return &v1.Guest{
		Id:            int64(in.ID),
		Avatar:        avatar,
		Name:          name,
		WikiNumber:    wikiNumber,
		PhoneAreaCode: in.PhoneAreaCode,
		Phone:         in.Phone,
		Email:         in.Email,
		WhatsApp:      in.WhatsApp,
		Wechat:        in.Wechat,
		Facebook:      in.Facebook,
		Twitter:       in.Twitter,
		Linkedin:      in.Linkedin,
		Instagram:     in.Instagram,
		Telegram:      in.Telegram,
		Youtube:       in.Youtube,
		Reddit:        in.Reddit,
		Tiktok:        in.TikTok,
		Enable:        in.Enable,
		CreatedAt:     in.CreatedAt.Unix(),
		UpdatedAt:     in.UpdatedAt.Unix(),
		Creator:       in.Creator,
		Languages:     languages,
		UserId:        in.UserId,
	}
}

func (s *Service) ListGuestsNotInExpo(ctx context.Context, in *v1.ListGuestsNotInExpoRequest) (*v1.ListGuestsNotInExpoReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Page <= 0 {
		in.Page = 1
	}

	if in.Size <= 0 {
		in.Size = 10
	}

	guests, total, err := s.guest.ListGuestsNotInExpo(ctx, in.Name, in.ExpoId, in.Page, in.Size)
	if err != nil {
		l.Errorf("guest.ListGuestsNotInExpo: %v", err)
		return nil, err
	}

	guestsItems := make([]*v1.Guest, 0, len(guests))
	for _, v := range guests {
		guestsItems = append(guestsItems, &v1.Guest{
			Id:            int64(v.ID),
			Name:          v.Name,
			Avatar:        v.Avatar,
			Email:         v.Email,
			Phone:         v.Phone,
			PhoneAreaCode: v.PhoneAreaCode,
			WikiNumber:    v.WikiNumber,
			Creator:       v.Creator,
			UserId:        v.UserId,
			Enable:        v.Enable,
			CreatedAt:     v.CreatedAt.Unix(),
			UpdatedAt:     v.UpdatedAt.Unix(),
		})
	}

	return &v1.ListGuestsNotInExpoReply{
		Guests: guestsItems,
		Total:  total,
	}, nil
}

func (s *Service) SetExpoGuestEnable(ctx context.Context, in *v1.SetExpoGuestEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	err := s.expoGuest.SetExpoGuestEnable(ctx, in.ExpoId, in.GuestId, in.Enable)
	if err != nil {
		l.Errorf("expoGuest.SetExpoGuestEnable Err:%v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ExportExpoGuest(ctx context.Context, in *v1.ExportExpoGuestRequest) (*v1.ExportExpoGuestReply, error) {
	var (
		l    = log.Context(ctx)
		size = 10
		page = 1
	)

	var (
		filePath = path.Join(os.TempDir(), fmt.Sprintf("expo_guest_%d.xlsx", time.Now().UnixMicro()))
		excel    = excelize.NewFile()
	)

	writer, err := excel.NewStreamWriter("Sheet1")
	if err != nil {
		l.Errorf("excel.NewStreamWriter Err:%v", err)
		return nil, err
	}

	styleID, err := excel.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Color: "777777",
			Size:  14,
			Bold:  true,
		},
	})

	err = writer.SetRow("A1", []interface{}{"嘉宾头像", "嘉宾姓名", "嘉宾ID", "嘉宾天眼号", "手机号码", "邮箱", "嘉宾title", "嘉宾介绍", "社交媒体（facebook）", "议程数量"}, excelize.RowOpts{
		Height:       16,
		StyleID:      styleID,
		OutlineLevel: 0,
	})
	if err != nil {
		l.Errorf("writer.SetRow Err:%v", err)
		return nil, err
	}

	total := 1
	for {
		var expoGuests []*models.Guest
		expoGuests, _, err = s.guest.SearchWithExpoGuest(ctx, in.ExpoId, in.Name, in.Mobile, in.Email, in.Type, page, size)
		if err != nil {
			l.Errorf("guest.SearchWithExpoGuest Err:%v", err)
			return nil, err
		}

		var groups []*models.ExpoScheduleGuestGroup
		groups, err = s.expoScheduleGuest.GroupByExpoId(ctx, in.ExpoId)
		if err != nil {
			l.Errorf("expoScheduleGuest.GroupByExpoId Err:%v", err)
			return nil, err
		}

		groupMapping := make(map[int64]*models.ExpoScheduleGuestGroup, len(groups))
		for _, value := range groups {
			groupMapping[value.GuestId] = value
		}

		for _, guest := range expoGuests {
			var (
				desc          string
				label         []string
				scheduleCount int32
			)

			if guest.Extra != nil && guest.Extra.V != nil {
				lang, ok := guest.Extra.V.Languages["zh-cn"]
				if !ok {
					lang, ok = guest.Extra.V.Languages["en"]
				}

				if ok {
					desc = lang.Description
					label = lang.Label
				}
			}

			if group, ok := groupMapping[int64(guest.ID)]; ok {
				scheduleCount = int32(group.Count)
			}

			err = writer.SetRow(fmt.Sprintf("A%d", total+1), []interface{}{
				guest.Avatar,
				guest.Name,
				guest.ID,
				guest.WikiNumber,
				guest.Phone,
				guest.Email,
				strings.Join(label, ","),
				desc,
				guest.Facebook,
				scheduleCount,
			})
			if err != nil {
				return nil, err
			}
			total++
		}

		if len(expoGuests) <= 0 {
			break
		}
		page++
	}
	err = writer.Flush()
	if err != nil {
		l.Errorf("writer.Flush Err:%v", err)
		return nil, err
	}

	err = excel.SaveAs(filePath)
	if err != nil {
		l.Errorf("excel.SaveAs Err:%v", err)
		return nil, err
	}

	content := bytes.NewBuffer(nil)
	_, err = excel.WriteTo(content)
	if err != nil {
		l.Errorf("file.WriteTo Err:%v", err)
		return nil, err
	}
	return &v1.ExportExpoGuestReply{
		Data: base64.StdEncoding.EncodeToString(content.Bytes()),
	}, nil
}
