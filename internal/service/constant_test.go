package service

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	v1 "gold_store/api/gold_store/v1"

	"github.com/stretchr/testify/assert"
)

func TestGetTrans(t *testing.T) {
	languages := []string{
		"zh",    // 中文
		"zh-cn", // 中文简体
		"de",
		"en",
		"es",
		"fil",
		"fr",
		"hi",
		"id",
		"it",
		"ja",
		"ko",
		"ms",
		"pt",
		"ru",
		"th",
		"vi",
		"ar",
		"tr",
	}

	req, err := http.NewRequest(http.MethodPost, "http://cloudvpsapi.fxeyeinterface.com/mt4Os/uhost/config", nil)
	assert.Nil(t, err)

	var (
		values       []*v1.GoodsStaticSpecValue
		valueMapping = make(map[string]*v1.GoodsStaticSpecValue, 10)
	)

	for _, language := range languages {
		req.Header.Set("languagecode", language)
		res, err := http.DefaultClient.Do(req)
		assert.Nil(t, err)

		var response Response
		err = json.NewDecoder(res.Body).Decode(&response)
		assert.Nil(t, err)
		assert.Equal(t, 200, response.ErrorCode)

		for _, data := range response.Data {
			if data.Config != 1 {
				continue
			}

			for _, zone := range data.LanguageList {
				name := ""
				if language == "zh-cn" {
					name = zone.Name
				}

				value, ok := valueMapping[zone.Name]
				if !ok {
					value = &v1.GoodsStaticSpecValue{
						Id:   fmt.Sprintf("%v", zone.Language),
						Name: name,
						Translate: map[string]string{
							language: zone.Name,
						},
					}
					values = append(values, value)
					valueMapping[zone.Name] = value
				}

				if zone.Name != "" {
					value.Translate[language] = zone.Name
				}
				if name != "" {
					value.Name = name
				}
			}
		}
	}

	str, _ := json.Marshal(values)
	println(string(str))
}

type Response struct {
	ErrorCode int `json:"ErrorCode"`
	Data      []struct {
		Config   int `json:"Config"`
		ZoneList []struct {
			Zone       string `json:"Zone"`
			RegionName string `json:"RegionName"`
		} `json:"zoneList"`
		LanguageList []struct {
			Language int    `json:"language"`
			Name     string `json:"name"`
			OsIcon   string `json:"osIcon"`
			OsName   string `json:"osName"`
		} `json:"languageList"`
	} `json:"Data"`
	RequestId string `json:"requestId"`
}
