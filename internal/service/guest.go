package service

import (
	"context"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	usercenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) checkGuest(ctx context.Context, in *v1.Guest, new bool) error {
	l := log.Context(ctx)
	if in.Name == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "嘉宾姓名不可为空")
	}

	if in.Phone == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "手机号码不可为空")
	}

	if in.Avatar == "" {
		return innErr.WithMessage(innErr.ErrBadRequest, "请上传头像")
	}

	if new {
		// 判断是否存在相同的嘉宾
		exists, err := s.guest.ExistsByPhone(ctx, in.Phone)
		if err != nil {
			l.Errorf("guest.ExistsByPhone Err: %v", err)
			return err
		}

		if exists {
			return innErr.WithMessage(innErr.ErrBadRequest, "该手机号码已存在")
		}

		if in.UserId != "" {
			// 判断是否存在相同的用户ID
			exists, err = s.guest.ExistsByUserId(ctx, in.UserId)
			if err != nil {
				l.Errorf("guest.ExistsByUserId Err: %v", err)
				return err
			}

			if exists {
				return innErr.WithMessage(innErr.ErrBadRequest, "该用户ID已存在")
			}
		}

		// 判断是否存在相同的邮箱
		exists, err = s.guest.ExistsByEmail(ctx, in.Email)
		if err != nil {
			l.Errorf("guest.ExistsByEmail Err: %v", err)
			return err
		}

		if exists {
			return innErr.WithMessage(innErr.ErrBadRequest, "该邮箱已存在")
		}
	}

	//根据WikiNumber查询用户ID
	if in.WikiNumber != "" {
		userResult, err := s.user.GetUserUserIdByWikiNumber(ctx, &usercenterv1.GetUserWikiNumbersRequest{
			UserIds: []string{in.WikiNumber},
		})

		if err != nil {
			return innErr.WithMessage(innErr.ErrBadRequest, "该WikiNumber号无法查找到相关UserId")
		}

		if userResult == nil || len(userResult.List) == 0 {
			return innErr.WithMessage(innErr.ErrBadRequest, "该WikiNumber号未找到对应的用户ID")
		}
		in.UserId = userResult.List[0].UserId
	}
	return nil
}

// AddGuest 添加嘉宾
func (s *Service) AddGuest(ctx context.Context, in *v1.Guest) (*v1.AddGuestReply, error) {
	l := log.Context(ctx)
	err := s.checkGuest(ctx, in, true)
	if err != nil {
		return nil, err
	}

	tx := s.guest.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	var (
		result     uint
		isRegister bool
	)

	if in.WikiNumber == "" && in.UserId != "" {
		var userRes *usercenterv1.CreateExpoPreUserReply
		userRes, err = s.user.CreateExpoPreUser(ctx, &usercenterv1.CreateExpoPreUserRequest{
			ReleaseId:   in.Id,
			AreaCode:    in.PhoneAreaCode,
			PhoneNumber: in.Phone,
			Email:       in.Email,
		})
		if err != nil {
			l.Errorf("user.CreateExpoPreUser Err:%v", err)
			return nil, err
		}
		in.UserId = userRes.UserId
		isRegister = true
	}

	result, err = s.guest.Add(ctx, &models.Guest{
		Avatar:  in.Avatar,
		Creator: in.Creator,
		UserId:  in.UserId,
		Email:   in.Email,
		Enable:  in.Enable,
		Extra: &igorm.CustomValue[*models.GuestExtra]{
			V: &models.GuestExtra{
				Languages: in.Languages,
			},
		},
		Facebook:      in.Facebook,
		Instagram:     in.Instagram,
		Linkedin:      in.Linkedin,
		Reddit:        in.Reddit,
		Telegram:      in.Telegram,
		TikTok:        in.Tiktok,
		Twitter:       in.Twitter,
		Wechat:        in.Wechat,
		WhatsApp:      in.WhatsApp,
		WikiNumber:    in.WikiNumber,
		Youtube:       in.Youtube,
		Name:          in.Name,
		Phone:         in.Phone,
		PhoneAreaCode: in.PhoneAreaCode,
		CountryFlag:   in.CountryFlag,
		IsRegister:    isRegister,
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("guest.Add Err:%v", err)
		return nil, err
	}

	//遍历in.ExpoIds 并添加到expo_guest表中
	expoGuests := make([]*models.ExpoGuest, 0, len(in.ExpoIds))
	for _, v := range in.ExpoIds {
		expoGuests = append(expoGuests, &models.ExpoGuest{
			ExpoId:  v,
			GuestId: int64(result),
			Enable:  true,
			Creator: in.Creator,
		})
	}

	err = s.expoGuest.BatchAdd(ctx, expoGuests, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoGuest.BatchAdd Err:%v", err)
		return nil, err
	}

	return &v1.AddGuestReply{
		Id: int64(result),
	}, nil
}

// GetGuest 获取单个嘉宾
func (s *Service) GetGuest(ctx context.Context, in *v1.GetGuestRequest) (gst *v1.Guest, err error) {
	l := log.Context(ctx)
	gst = &v1.Guest{}
	// 参数校验
	if in.Id == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "嘉宾ID不能为空")
	}

	guest, err := s.guest.Get(ctx, in.Id)
	if err != nil {
		l.Errorf("guest.Get: %v", err)
		return nil, err
	}

	expoGuest, err := s.expoGuest.FindByGuestId(ctx, in.Id)
	if err != nil {
		l.Errorf("expoGuest.FindByGuestId: %v", err)
		return nil, err
	}

	expoGuestIds := make([]int64, 0, len(expoGuest))
	for _, v := range expoGuest {
		expoGuestIds = append(expoGuestIds, v.ExpoId)
	}

	return &v1.Guest{
		Id:            int64(guest.ID),
		Name:          guest.Name,
		Avatar:        guest.Avatar,
		Email:         guest.Email,
		Phone:         guest.Phone,
		PhoneAreaCode: guest.PhoneAreaCode,
		WikiNumber:    guest.WikiNumber,
		Creator:       guest.Creator,
		Languages: func() map[string]*v1.GuestLanguage {
			if guest.Extra == nil || guest.Extra.V == nil || guest.Extra.V.Languages == nil {
				return nil
			}
			return guest.Extra.V.Languages
		}(),
		Facebook:    guest.Facebook,
		Instagram:   guest.Instagram,
		Linkedin:    guest.Linkedin,
		Reddit:      guest.Reddit,
		Telegram:    guest.Telegram,
		Tiktok:      guest.TikTok,
		Twitter:     guest.Twitter,
		Wechat:      guest.Wechat,
		WhatsApp:    guest.WhatsApp,
		Youtube:     guest.Youtube,
		UserId:      guest.UserId,
		Enable:      guest.Enable,
		ExpoIds:     expoGuestIds,
		CountryFlag: guest.CountryFlag,
		CreatedAt:   guest.CreatedAt.Unix(),
		UpdatedAt:   guest.UpdatedAt.Unix(),
	}, nil
}

// UpdateGuest 更新嘉宾
func (s *Service) UpdateGuest(ctx context.Context, in *v1.Guest) (*common.EmptyReply, error) {
	l := log.Context(ctx)

	err := s.checkGuest(ctx, in, false)
	if err != nil {
		l.Errorf("checkGuest Err:%v", err)
		return nil, err
	}

	tx := s.guest.Begin()
	defer func() {
		if err == nil {
			tx.Commit()
		} else {
			tx.Rollback()
		}
	}()

	_, err = s.guest.Update(ctx, in.Id, &models.Guest{
		Avatar:  in.Avatar,
		Creator: in.Creator,
		Email:   in.Email,
		Extra: &igorm.CustomValue[*models.GuestExtra]{
			V: &models.GuestExtra{
				Languages: in.Languages,
			},
		},
		Facebook:      in.Facebook,
		Instagram:     in.Instagram,
		Linkedin:      in.Linkedin,
		Reddit:        in.Reddit,
		Telegram:      in.Telegram,
		TikTok:        in.Tiktok,
		Twitter:       in.Twitter,
		Wechat:        in.Wechat,
		WhatsApp:      in.WhatsApp,
		WikiNumber:    in.WikiNumber,
		Youtube:       in.Youtube,
		Name:          in.Name,
		Phone:         in.Phone,
		PhoneAreaCode: in.PhoneAreaCode,
		UserId:        in.UserId,
		Enable:        in.Enable,
		CountryFlag:   in.CountryFlag,
	}, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("guest.Update: %v", err)
		return nil, err
	}

	err = s.expoGuest.DeleteByGuestId(ctx, in.Id, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoGuest.DeleteByGuestId: %v", err)
		return nil, err
	}

	expoGuests := make([]*models.ExpoGuest, 0, len(in.ExpoIds))
	for _, v := range in.ExpoIds {
		expoGuests = append(expoGuests, &models.ExpoGuest{
			ExpoId:  v,
			GuestId: in.Id,
			Enable:  true,
			Creator: in.Creator,
		})
	}

	err = s.expoGuest.BatchAdd(ctx, expoGuests, igorm.WithTransaction(tx))
	if err != nil {
		l.Errorf("expoGuest.BatchAdd: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

// SetGuestEnable 设置嘉宾启用状态
func (s *Service) SetGuestEnable(ctx context.Context, in *v1.SetGuestEnableRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Id == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "嘉宾ID不能为空")
	}

	_, err := s.guest.SetEnable(ctx, in.Id, in.Enable)
	if err != nil {
		l.Errorf("guest.SetEnable: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}

func (s *Service) ListGuest(ctx context.Context, in *v1.ListGuestRequest) (listGuestReply *v1.ListGuestReply, err error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Page <= 0 {
		in.Page = 1
	}

	if in.Size <= 0 {
		in.Size = 10
	}

	guests, total, err := s.guest.FindByPage(ctx, in.Name, in.Email, in.Phone, in.IsHasUserId, in.Page, in.Size)
	if err != nil {
		l.Errorf("guest.FindByPage: %v", err)
		return nil, err
	}

	var (
		guestItems = make([]*v1.Guest, 0, len(guests))
		guestIds   = make([]int64, 0, len(guests))
	)

	for _, v := range guests {
		guestIds = append(guestIds, int64(v.ID))
		guestItems = append(guestItems, &v1.Guest{
			Id:            int64(v.ID),
			Name:          v.Name,
			Avatar:        v.Avatar,
			Email:         v.Email,
			Phone:         v.Phone,
			PhoneAreaCode: v.PhoneAreaCode,
			WikiNumber:    v.WikiNumber,
			Creator:       v.Creator,
			Languages:     v.Extra.V.Languages,
			Facebook:      v.Facebook,
			Instagram:     v.Instagram,
			Linkedin:      v.Linkedin,
			Reddit:        v.Reddit,
			Telegram:      v.Telegram,
			Tiktok:        v.TikTok,
			Twitter:       v.Twitter,
			Wechat:        v.Wechat,
			WhatsApp:      v.WhatsApp,
			Youtube:       v.Youtube,
			UserId:        v.UserId,
			Enable:        v.Enable,
			CountryFlag:   v.CountryFlag,
			CreatedAt:     v.CreatedAt.Unix(),
			UpdatedAt:     v.UpdatedAt.Unix(),
		})
	}

	expoGuests, err := s.expoGuest.FindExpoIdsByGuestIds(ctx, guestIds)
	if err != nil {
		l.Errorf("expoGuest.FindExpoIdsByGuestIds Err:%v", err)
		return nil, err
	}

	var (
		expoGuestMap = make(map[int64][]*models.ExpoGuest, len(expoGuests))
		expoIds      = make([]int64, 0, len(expoGuests))
	)

	for _, expoGuest := range expoGuests {
		expoGuestMap[expoGuest.GuestId] = append(expoGuestMap[expoGuest.GuestId], expoGuest)
		expoIds = append(expoIds, expoGuest.ExpoId)
	}

	//批量获取expo数据
	var expos []*models.Expo
	if len(expoIds) > 0 {
		expos, err = s.expo.FindByIds(ctx, expoIds)
		if err != nil {
			l.Errorf("expo.FindByIds Err:%v", err)
			return nil, err
		}
	}

	exposMap := make(map[int64]*models.Expo, len(expos))
	for _, v := range expos {
		exposMap[v.ID] = v
	}

	for _, guestItem := range guestItems {
		guestExpos, ok := expoGuestMap[guestItem.Id]
		if !ok {
			continue
		}

		outGuestExpos := make([]*v1.Guest_ExpoItem, 0, len(guestExpos))
		for _, expoItem := range guestExpos {
			var expo *models.Expo
			expo, ok = exposMap[expoItem.ExpoId]
			if !ok {
				continue
			}
			outGuestExpos = append(outGuestExpos, &v1.Guest_ExpoItem{
				Id:   expoItem.ExpoId,
				Name: expo.Name,
			})
		}
		guestItem.Expos = outGuestExpos
	}

	return &v1.ListGuestReply{
		Guests: guestItems,
		Total:  total,
	}, nil
}

func (s *Service) DeleteGuest(ctx context.Context, in *v1.DeleteGuestRequest) (*common.EmptyReply, error) {
	l := log.Context(ctx)
	// 参数校验
	if in.Id == 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "嘉宾ID不能为空")
	}

	//根据嘉宾ID 查找嘉宾的关联展会信息
	expoGuests, err := s.expoGuest.FindByGuestId(ctx, in.Id)
	if err != nil {
		l.Errorf("expoGuest.FindByGuestId Err: %v", err)
		return nil, err
	}

	if len(expoGuests) > 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "嘉宾下存在关联展会信息，无法删除")
	}

	//根据嘉宾ID 查找嘉宾关联的议程信息
	expoScheduleGuests, err := s.expoScheduleGuest.FindByGuestId(ctx, in.Id)
	if err != nil {
		l.Errorf("expoScheduleGuest.FindByGuestId: %v", err)
		return nil, err
	}

	if len(expoScheduleGuests) > 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "嘉宾下存在关联议程信息，无法删除")
	}

	//根据嘉宾ID 查找HotsId及主持人关联议程主表信息
	expoSchedules, err := s.expoSchedule.FindByHostId(ctx, in.Id)
	if err != nil {
		l.Errorf("expoSchedule.FindByHostId: %v", err)
		return nil, err
	}

	if len(expoSchedules) > 0 {
		return nil, innErr.WithMessage(innErr.ErrBadRequest, "该嘉宾存在为议程关联的主持人，无法删除")
	}

	err = s.guest.Delete(ctx, in.Id)
	if err != nil {
		l.Errorf("guest.Delete Err: %v", err)
		return nil, err
	}
	return &common.EmptyReply{}, nil
}
