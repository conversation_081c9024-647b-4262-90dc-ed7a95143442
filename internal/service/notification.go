package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) notificationCheck() {
	var (
		ctx = icontext.WithRequestId(context.Background(), reqid.GenRequestID())
		l   = log.Context(ctx)
	)

	key := fmt.Sprintf("schedule:notify:%s", time.Now().Format("2006010215"))
	release, err := s.locker.TryLock(ctx, key, time.Hour)
	if err != nil {
		return
	}
	defer release()

	schedules, err := s.expoSchedule.FindWithNotStart(ctx)
	if err != nil {
		l.<PERSON>("expoSchedule.FindWithNotStart Err:%v", err)
		return
	}

	var (
		expoIds         = make([]int64, 0, len(schedules))
		scheduleMapping = make(map[int64]*models.ExpoSchedule, len(schedules))
	)

	for _, schedule := range schedules {
		expoIds = append(expoIds, schedule.ExpoId)
		scheduleMapping[int64(schedule.ID)] = schedule
	}

	var expos []*models.Expo
	if len(expoIds) > 0 {
		expos, err = s.expo.FindByIds(ctx, expoIds)
		if err != nil {
			l.Errorf("expo.FindByIds Err:%v", err)
			return
		}
	}

	expoMapping := make(map[int64]*models.Expo, len(expos))
	for _, expo := range expos {
		expoMapping[expo.ID] = expo
		expoIds = append(expoIds, expo.ID)
	}

	var preStartScheduleIds []int64
	for _, schedule := range schedules {
		expo, ok := expoMapping[schedule.ExpoId]
		if !ok {
			continue
		}
		zoneDiff := expo.GetZoneDiff()

		var (
			start = schedule.Start.Add(zoneDiff)
			now   = time.Now().UTC().Add(zoneDiff)
		)

		// 不在24小时内
		if !(start.After(now) && start.Before(now.Add(24*time.Hour))) {
			continue
		}
		preStartScheduleIds = append(preStartScheduleIds, int64(schedule.ID))
	}

	if len(preStartScheduleIds) <= 0 {
		return
	}

	var scheduleGuests []*models.ExpoScheduleGuest
	scheduleGuests, err = s.expoScheduleGuest.FindByScheduleIds(ctx, preStartScheduleIds)
	if err != nil {
		l.Errorf("expoScheduleGuest.FindByScheduleIds Err:%v", err)
		return
	}

	var (
		scheduleGuestIds     = make([]int64, 0, len(scheduleGuests))
		guestIds             = make([]int64, 0, len(scheduleGuests))
		scheduleGuestMapping = make(map[int64]*models.ExpoScheduleGuest, len(scheduleGuests))
	)

	for _, scheduleGuest := range scheduleGuests {
		scheduleGuestIds = append(scheduleGuestIds, int64(scheduleGuest.ID))
		scheduleGuestMapping[int64(scheduleGuest.ID)] = scheduleGuest
		guestIds = append(guestIds, scheduleGuest.GuestId)
	}

	var guests []*models.Guest
	if len(guestIds) > 0 {
		guests, err = s.guest.FindByIds(ctx, guestIds)
		if err != nil {
			l.Errorf("guest.FindByIds Err:%v", err)
			return
		}
	}

	guestMapping := make(map[int64]*models.Guest, len(guests))
	for _, guest := range guests {
		guestMapping[int64(guest.ID)] = guest
	}

	if len(scheduleGuestIds) <= 0 {
		return
	}

	reserves, err := s.expoScheduleReserve.FindByScheduleGuestIdsAndNotify(ctx, scheduleGuestIds)
	if err != nil {
		l.Errorf("expoScheduleReserve.FindByScheduleGuestIdsAndNotify Err:%v", err)
		return
	}

	for _, reserve := range reserves {
		if reserve.Notify > 0 {
			continue
		}

		scheduleGuest, ok := scheduleGuestMapping[reserve.ScheduleGuestId]
		if !ok {
			continue
		}

		schedule, ok := scheduleMapping[scheduleGuest.ScheduleId]
		if !ok {
			continue
		}

		expo, ok := expoMapping[schedule.ExpoId]
		if !ok {
			continue
		}
		zoneDiff := expo.GetZoneDiff()

		var (
			start = schedule.Start.Add(zoneDiff)
			now   = time.Now().UTC().Add(zoneDiff)
		)

		// 不在24小时内
		if !(start.After(now) && start.Before(now.Add(24*time.Hour))) {
			continue
		}

		guest, ok := guestMapping[scheduleGuest.GuestId]
		if !ok {
			continue
		}

		themes := make(map[string]string, 18)
		if schedule.Extra != nil && schedule.Extra.V != nil {
			for key, lang := range schedule.Extra.V.Languages {
				themes[key] = lang.Theme
			}
		}

		s.scheduleStartNotification(ctx, &Content{
			NickName:        guest.Name,
			LanguageCode:    reserve.LanguageCode,
			ExpoId:          strconv.FormatInt(reserve.ExpoId, 10),
			ScheduleGuestId: strconv.FormatInt(reserve.ScheduleGuestId, 10),
			Date:            schedule.Start.Add(zoneDiff).Format("2006.01.02"),
			Start:           schedule.Start.Add(zoneDiff).Format("2006.01.02 15:04"),
			Avatar:          urlformat.FullPath(guest.Avatar, urlTemplate),
			Themes:          themes,
			UserId:          reserve.UserId,
		})

		err = s.expoScheduleReserve.SetNotify(ctx, int64(reserve.ID))
		if err != nil {
			l.Errorf("expoScheduleReserve.SetNotify Err:%v", err)
			continue
		}
	}
}

func (s *Service) scheduleReserveNotification(ctx context.Context, in *Content) {
	var (
		l       = log.Context(ctx)
		title   = make(map[string]string, len(supportedLanguages))
		content = make(map[string]string, len(supportedLanguages))
		alert   = i18n.GetWithDefaultEnglish("63415", in.LanguageCode) // 展会议程预约成功
		theme   string
	)

	for key, value := range in.Themes {
		if key == in.LanguageCode {
			theme = value
			break
		}

		if key == "en" && theme == "" {
			theme = value
		}
	}

	for _, language := range supportedLanguages {
		title[language] = i18n.GetWithDefaultEnglish("63416", language) // 您成功预约了嘉宾议程
		curTheme, ok := in.Themes[language]
		if !ok {
			curTheme = theme
		}
		content[language] = i18n.GetWithTemplateDataDefaultEnglish("63438", language, []string{curTheme, in.Start}) // 您成功预约了嘉宾议程：{0}将在{1}开始",
	}

	err := s.notification.Publish((&Notification{
		BusinessId:       in.ExpoId,
		BusinessCategory: "AppointmentAgenda",
		Title:            title,
		Content:          content,
		CustomInformation: map[string]string{
			"expoId":          in.ExpoId,
			"date":            in.Date,
			"scheduleGuestId": in.ScheduleGuestId,
			"avatar":          in.Avatar,
			"languageCode":    in.LanguageCode,
			"alert":           alert,
			"userId":          in.UserId,
			"nickName":        in.NickName,
		},
		UserId:        in.UserId,
		PushTimestamp: time.Now().Add(5 * time.Second).UnixMilli(),
	}).String())
	if err != nil {
		l.Errorf("notification.Publish Err:%v", err)
	}
}

func (s *Service) scheduleStartNotification(ctx context.Context, in *Content) {
	var (
		l       = log.Context(ctx)
		title   = make(map[string]string, len(supportedLanguages))
		content = make(map[string]string, len(supportedLanguages))
		alert   = i18n.GetWithDefaultEnglish("63195", in.LanguageCode) // 展会即将开始
		theme   string
	)

	for key, value := range in.Themes {
		if key == in.LanguageCode {
			theme = value
			break
		}

		if key == "en" && theme == "" {
			theme = value
		}
	}

	for _, language := range supportedLanguages {
		title[language] = i18n.GetWithDefaultEnglish("63195", language) // 展会即将开始
		curTheme, ok := in.Themes[language]
		if !ok {
			curTheme = theme
		}
		content[language] = i18n.GetWithTemplateDataDefaultEnglish("63196", language, []string{curTheme, in.Start}) // 您预约的展会议程：{0} 将在{1}开始
	}

	value := (&Notification{
		BusinessId:       in.ExpoId,
		BusinessCategory: "AppointmentGuests",
		Title:            title,
		Content:          content,
		CustomInformation: map[string]string{
			"expoId":          in.ExpoId,
			"date":            in.Date,
			"scheduleGuestId": in.ScheduleGuestId,
			"avatar":          in.Avatar,
			"languageCode":    in.LanguageCode,
			"alert":           alert,
			"userId":          in.UserId,
			"nickName":        in.NickName,
		},
		UserId:        in.UserId,
		PushTimestamp: time.Now().Add(5 * time.Second).UnixMilli(),
	}).String()

	fmt.Println("发送数据：", string(value))
	err := s.notification.Publish(value)
	if err != nil {
		l.Errorf("notification.Publish Err:%v", err)
	}
}

type Content struct {
	NickName        string
	LanguageCode    string
	ExpoId          string
	ScheduleGuestId string
	Date            string
	Start           string
	Avatar          string
	Themes          map[string]string
	UserId          string
}

type Notification struct {
	BusinessId        string                 `json:"BusinessId"`
	BusinessCategory  string                 `json:"BusinessCategory"`
	Title             map[string]string      `json:"Title"`
	Content           map[string]string      `json:"Content"`
	CustomInformation map[string]string      `json:"CustomInformation"`
	UserId            string                 `json:"UserId"`
	CountryCodes      []string               `json:"CountryCodes"`
	LanguageCodes     []string               `json:"LanguageCodes"`
	AreaCodes         []string               `json:"AreaCodes"`
	PushTimestamp     int64                  `json:"PushTimestamp"`
	Snapshot          map[string]interface{} `json:"Snapshot"`
	AggInfo           map[string]string      `json:"AggInfo"`
}

func (n *Notification) String() []byte {
	str, _ := json.Marshal(n)
	return str
}
