package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/pkg/id"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/recovery"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

func (s *Service) orderFromExhibition(c *kafka.Consumer, message *kafka.Message) {
	defer c.CommitMessage(message)

	var (
		ctx   = context.Background()
		reqId = reqid.GenRequestID()
		data  ExhibitionOrder
	)
	ctx = icontext.WithRequestId(ctx, reqId)
	l := log.Context(ctx)
	defer recovery.CatchGoroutinePanicWithContext(ctx)

	err := json.Unmarshal(message.Value, &data)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("展会 Unmarshal<%s> Err:%v", string(message.Value), err))
		return
	}

	if !data.Commit && data.Type != "bootstrap-insert" {
		return
	}

	var (
		order           = data.Data
		langs           []*models.ExpoLang
		exhibitionUsers []models.ExhibitionUser
	)

	expo, err := s.exhibition.GetExpo(ctx, order.ExpoId)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("展会订单【%s】获取expo【%d】为空", order.OrderNo, order.ExpoId))
		return
	}

	if expo.Langs != "" {
		err = json.Unmarshal([]byte(expo.Langs), &langs)
		if err != nil {
			l.Errorf("json.Unmarshal<%s> Err:%v", expo.Langs, err)
		}
		// 这里发生错误不影响主流程
	}

	//ticketType, err := s.exhibition.GetTicketType(ctx, order.TicketType)
	//if err != nil {
	//	l.Errorf("GetTicketType Err:%v", err)
	//	return
	//}

	parts, err := s.exhibition.FindParticipant(ctx, order.OrderNo)
	if err != nil {
		alarm.FeiShuAlarm(reqId, fmt.Sprintf("展会订单【%s】获取Participatn 出错：%v", order.OrderNo, err))
		return
	}

	for _, part := range parts {
		exhibitionUsers = append(exhibitionUsers, models.ExhibitionUser{
			FirstName: part.FirstName,
			LastName:  part.LastName,
			Email:     part.Email,
			Phone:     part.Phone,
		})
	}

	var (
		orderNo        = order.OrderNo
		goodsId        = strconv.Itoa(order.ExpoId)
		displayAmount  = ""
		paymentMethod  = v1.PaymentMethod_PAYMENT_METHOD_UNKNOWN
		operationNo    = order.PaymentNo
		paymentNo      = id.GenerateId(id.BusinessPayment, id.WithChannel(strconv.Itoa(int(paymentMethod))))
		paymentStatus  = v1.PaymentStatus_PaymentStatusUNPAY
		orderStatus    = v1.OrderStatus_UNPAY
		price          = order.Price
		totalAmount    = order.Amount
		paidTotal      float32
		quantity       = order.Quantity
		goodsName      = expo.Name
		goodsImage     = &v1.Image{}
		specDesc       = fmt.Sprintf("%s", expo.Start.Format("2006-01-02 15:04"))
		eaSpecDesc     = map[string]string{}
		paidTime       time.Time
		currencySymbol string
		createTime, _  = time.ParseInLocation(time.DateTime, order.CreatedAt, time.UTC)
	)

	if strings.TrimSpace(expo.TimeZone) != "" {
		specDesc = fmt.Sprintf("%s(%s)", expo.Start.Format("2006-01-02 15:04"), strings.ToUpper(expo.TimeZone))
	}

	for _, lang := range langs {
		if strings.ToLower(lang.Lang) == "en" {
			goodsImage.Url = lang.AppCover
			break
		}
	}

	switch order.Payment {
	case 71: // 微信
		paymentMethod = v1.PaymentMethod_WECHAT
	case 72: // 支付宝
		paymentMethod = v1.PaymentMethod_ALI
	case 73: // 银联
		paymentMethod = v1.PaymentMethod_UNION_PAY
	case 74: // AppleLap
		paymentMethod = v1.PaymentMethod_APPLE_IAP
	case 75: // ApplyPay
		paymentMethod = v1.PaymentMethod_APPLE_PAY
	case 76: // GoogleLap
		paymentMethod = v1.PaymentMethod_GOOGLE_IAP
	case 77: // GooglePay
		paymentMethod = v1.PaymentMethod_GOOGLE_PAY
	case 78: // Paypal
		paymentMethod = v1.PaymentMethod_PAYPAL
	case 79: // 微信H5
		paymentMethod = v1.PaymentMethod_WECHAT_H5
	case 80: // aliwap
		paymentMethod = v1.PaymentMethod_ALI_WAP
	case 81: // ForexPay
		paymentMethod = v1.PaymentMethod_FOREX_PAY
	case 82: // 金币
		paymentMethod = v1.PaymentMethod_GOLD
	}

	if paymentMethod == v1.PaymentMethod_GOLD {
		displayAmount = fmt.Sprintf("%.0f", order.Amount)
	} else {
		switch expo.PriceUnit {
		case 1: // 人民币
			currencySymbol = "￥"
			displayAmount = fmt.Sprintf("￥%.2f", order.Amount)
		case 2: // 美元
			currencySymbol = "$"
			displayAmount = fmt.Sprintf("$%.2f", order.Amount)
		case 3: // 欧元
			currencySymbol = "€"
			displayAmount = fmt.Sprintf("€%.2f", order.Amount)
		default:
			currencySymbol = "$"
			displayAmount = fmt.Sprintf("$%.2f", order.Amount)
		}
	}

	switch order.Status {
	case 200:
		paidTotal = order.Amount
		orderStatus = v1.OrderStatus_COMPLETE
		paymentStatus = v1.PaymentStatus_PaymentStatusPAID
		paidTime, _ = time.ParseInLocation(time.DateTime, order.PaidAt, time.UTC)
	case 500:
		orderStatus = v1.OrderStatus_CANCEL
		paymentStatus = v1.PaymentStatus_PaymentStatusCANCELED
	default:

	}

	orderExtra := &igorm.CustomValue[*models.OrderExtra]{
		V: &models.OrderExtra{
			DisplayAmount:          displayAmount,
			SpecDesc:               eaSpecDesc,
			ExhibitionTicketType:   order.TicketTypeName,
			ExhibitionCouponCode:   order.CouponCode,
			ExhibitionUsers:        exhibitionUsers,
			ExhibitionLocationName: expo.LocationName,
			ExhibitionLocation:     expo.Location,
		},
	}

	switch data.Type {
	case "update":
		var (
			payment *models.Payment
			insert  bool
		)

		_, err = s.order.GetByOrderNo(ctx, orderNo)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("GetByOrderNo<%s> Err:%v", orderNo, err)
			return
		}

		// 到这里说明订单不存在，可能是先消费了更新；所以需要走insert逻辑
		if err != nil {
			insert = true
		}

		if !insert {
			payment, err = s.payment.GetByOrderNo(ctx, orderNo)
			if err != nil {
				l.Errorf("GetByOrderNo<%s> Err:%v", orderNo, err)
				return
			}

			var orderExtraValue any
			orderExtraValue, err = orderExtra.Value()
			if err != nil {
				l.Errorf("orderExtra.Value Err:%v", err)
				return
			}

			tx := s.goods.Begin()
			defer func() {
				if err == nil {
					tx.Commit()
				} else {
					tx.Rollback()
				}
			}()

			// 4、更新支付单信息
			err = s.payment.UpdatePayment(ctx, &models.Payment{
				PaymentNo:   payment.PaymentNo,
				OperationNo: operationNo,
				PaidTotal:   totalAmount,
				PaidTime:    paidTime,
				Status:      paymentStatus,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("payment.UpdatePayment Err:%v", err)
				return
			}

			// 5、更新支付订单
			err = s.order.Update(ctx, orderNo, map[string]interface{}{
				"status":          orderStatus,
				"total_amount":    totalAmount,
				"extra":           orderExtraValue,
				"currency_symbol": currencySymbol,
			}, igorm.WithTransaction(tx))
			if err != nil {
				l.Errorf("order.UpdateStatus Err:%v", err)
				return
			}
			return
		}
		fallthrough
	case "bootstrap-insert", "insert":
		var (
			goodsSnapshot *models.GoodsSnapshot
			goodsVersion  = time.Now().Unix()
		)

		goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
		if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
			l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
			return
		}

		// 没有找到，这里新增
		if err != nil {
			goodsSnapshot = &models.GoodsSnapshot{
				GoodsId: goodsId,
				Version: goodsVersion,
				Snapshot: &igorm.CustomValue[*models.Snapshot]{
					V: &models.Snapshot{
						Goods: &models.Goods{
							Category:      v1.GoodsCategory_GOODS_CATEGORY_EXHIBITION,
							GoodsId:       goodsId,
							Name:          goodsName,
							BasePrice:     totalAmount,
							UseBasePrice:  true,
							SelectedPrice: totalAmount,
							Status:        v1.GoodsStatus_GoodsStatusOn,
							FreeShipping:  true,
							Image: &igorm.CustomValue[*v1.Image]{
								V: goodsImage,
							},
							Translate: &igorm.CustomValue[map[string]*v1.GoodsTranslate]{},
						},
					},
				},
			}

			err = s.goodsSnapshot.Add(ctx, goodsSnapshot)
			if err != nil && !errors.Is(err, ormhelper.ErrDuplicateKey) {
				l.Errorf("goodsSnapshot.Add Err:%v", err)
				return
			}

			if err != nil {
				goodsSnapshot, err = s.goodsSnapshot.GetByGoodsId(ctx, goodsId, goodsVersion)
				if err != nil {
					l.Errorf("goodsSnapshot.GetByGoodsId Err:%v", err)
					return
				}
			}
		}

		var (
			newOrder = &models.Order{
				OrderNo:        orderNo,
				Source:         v1.OrderSource_EXHIBITION,
				PaymentMethod:  paymentMethod,
				Platform:       v1.Platform_PlatformUNKNOWN,
				UserId:         order.UserId,
				Address:        &igorm.CustomValue[*models.Address]{},
				Quantity:       quantity,
				CurrencySymbol: currencySymbol,
				TotalAmount:    totalAmount,
				Status:         orderStatus,
				GoodsName:      goodsName,
				Extra:          orderExtra,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}

			newOrderItems = []*models.OrderItem{
				{
					OrderNo:         orderNo,
					GoodsId:         goodsId,
					Price:           price,
					PriceUnit:       currencySymbol,
					GoodsSnapshotId: goodsSnapshot.ID,
					Quantity:        quantity,
					TotalAmount:     totalAmount,
					GoodsName:       goodsName,
					SpecDesc:        specDesc,
					Model: gorm.Model{
						CreatedAt: createTime,
						UpdatedAt: createTime,
					},
				},
			}

			newPayment = &models.Payment{
				PaymentNo:   paymentNo,
				OrderNo:     orderNo,
				OperationNo: operationNo,
				TotalAmount: totalAmount,
				PaidTotal:   paidTotal,
				PaidMethod:  paymentMethod,
				Status:      paymentStatus,
				PaidTime:    paidTime,
				Model: gorm.Model{
					CreatedAt: createTime,
					UpdatedAt: createTime,
				},
			}
		)

		tx := s.goods.Begin()
		defer func() {
			if err == nil {
				tx.Commit()
			} else {
				tx.Rollback()
			}
		}()

		// 1、添加订单
		err = s.order.Add(ctx, newOrder, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("order.Add Err:%v", err)
			return
		}

		// 3、添加订单项
		err = s.orderItem.BatchAdd(ctx, newOrderItems, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("orderItem.BatchAdd Err:%v", err)
			return
		}

		// 4、添加支付
		err = s.payment.Add(ctx, newPayment, igorm.WithTransaction(tx))
		if err != nil {
			l.Errorf("payment.Add Err:%v", err)
			return
		}
	}
}
