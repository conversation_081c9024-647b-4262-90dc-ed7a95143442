package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"
	"gold_store/internal/util"
	"gold_store/pkg/gold"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/reqid"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

const (
	SignStatusDuplicate = "duplicate" // 重复签到
	SignStatusSuccess   = "success"
	SignStatusFailed    = "failed"
	RewardTypeGoldCons  = "gold_coins" // 奖励类型：金币
)

// RewardTypeNameMap 奖励类型与名称的映射关系
var RewardTypeNameMap = map[string]string{
	RewardTypeGoldCons: "积分",
}

// GetRewardTypeName 获取奖励类型对应的名称
func GetRewardTypeName(rewardType string) string {
	if name, ok := RewardTypeNameMap[rewardType]; ok {
		return name
	}
	return "未知奖励"
}

// GetSignAggregateInfo 获取签到聚合信息
// 聚合用户签到状态、历史记录、奖励配置和其他用户动态，为前端提供完整的签到页面数据
func (s *Service) GetSignAggregateInfo(ctx context.Context, req *v1.GetSignAggregateInfoRequest) (*v1.GetSignAggregateInfoReply, error) {
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "FORBIDDEN")
	}

	config, err := s.getSignConfig(ctx)
	if err != nil {
		return nil, err
	}

	userSignInfo, err := s.getUserSignInfo(ctx, userID, req.Timezone)
	if err != nil {
		return nil, err
	}

	signHistory, err := s.getUserSignHistory(ctx, userID, userSignInfo.ConsecutiveDays)
	if err != nil {
		return nil, err
	}

	config.NextConsecutiveSignDescription = s.getNextConsecutiveSignDescription(ctx, config, int(userSignInfo.ConsecutiveDays), userSignInfo.HasSignedToday)

	otherSignLog, err := s.getOtherUserSignLogs(ctx)
	if err != nil {
		log.Context(ctx).Errorf("获取其他用户签到记录失败: %v", err)
	}

	return &v1.GetSignAggregateInfoReply{
		Config:       config,
		UserSignInfo: userSignInfo,
		SignHistory:  signHistory,
		OtherSignLog: otherSignLog,
	}, nil
}

// SignIn 执行用户签到操作
// 使用分布式锁防止重复签到，根据连续天数计算奖励，支持普通奖励和连续签到额外奖励
// 奖励发放失败时会回滚签到记录，确保数据一致性
func (s *Service) SignIn(ctx context.Context, req *v1.SignInRequest) (*v1.SignInReply, error) {
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "FORBIDDEN")
	}

	if req.Timezone == "" {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "INVALID_ARGUMENT")
	}

	lockKey := fmt.Sprintf("sign_in_lock:%s", userID)
	release, err := s.locker.TryLock(ctx, lockKey, time.Minute)
	if err != nil {
		log.Context(ctx).Warnf("获取锁失败，可能是重复领取, userId=%s, lockKey=%s, err=%v", userID, lockKey, err)
		return nil, errors.WithMessage(errors.ErrBadRequest, "Operation too frequent, please try again later")
	}
	defer func() {
		if err := release(); err != nil {
			log.Context(ctx).Errorf("释放分布式锁失败: %v", err)
		}
	}()

	hasSigned, err := s.checkHasSigned(ctx, userID, req.Timezone)
	if err != nil {
		return nil, err
	}

	if hasSigned {
		return s.getSignInResult(ctx, userID, req.Timezone, true)
	}

	consecutiveDays, err := s.calculateConsecutiveDays(ctx, userID, req.Timezone)
	if err != nil {
		return nil, err
	}

	config, err := s.getSignConfig(ctx)
	if err != nil {
		return nil, err
	}

	regularReward, consecutiveReward, err := s.determineRewards(ctx, config, consecutiveDays)
	if err != nil {
		return nil, err
	}

	signRecord, err := s.recordSignIn(ctx, userID, req.Timezone, consecutiveDays, regularReward, consecutiveReward)
	if err != nil {
		return nil, err
	}

	var totalGold int32
	if regularReward != nil && regularReward.RewardType == RewardTypeGoldCons {
		totalGold += regularReward.GoldCoins
	}
	if consecutiveReward != nil && consecutiveReward.RewardType == RewardTypeGoldCons {
		totalGold += consecutiveReward.GoldCoins
	}

	if totalGold > 0 {
		reward := &v1.Reward{
			RewardType: RewardTypeGoldCons,
			GoldCoins:  totalGold,
		}
		err = s.processReward(ctx, userID, models.SourceTypeRegularSign, signRecord.ID, reward)
		if err != nil {
			log.Context(ctx).Errorf("处理签到奖励失败: %v", err)
			if rollbackErr := s.rollbackSignRecord(ctx, signRecord); rollbackErr != nil {
				log.Context(ctx).Errorf("回滚签到记录失败: %v", rollbackErr)
			}
			return nil, err
		}
	}

	return s.getSignInResult(ctx, userID, req.Timezone, false)
}

// getSignConfig 获取并验证签到配置
// 确保配置的周期天数有效、奖励列表不为空、天数配置无重复
func (s *Service) getSignConfig(ctx context.Context) (*v1.SignConfig, error) {
	var l = log.Context(ctx)

	config, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil {
		l.Errorf("获取签到配置失败: %v", err)
		return nil, err
	}

	if config == nil {
		l.Warn("未找到有效的签到配置")
		return nil, errors.ErrInternalServer
	}

	if config.ConfigValue.CycleDays <= 0 {
		l.Warnf("无效的周期天数: %d", config.ConfigValue.CycleDays)
		return nil, errors.ErrInternalServer
	}

	if len(config.ConfigValue.Rewards) == 0 {
		l.Warn("奖励配置列表为空")
		return nil, errors.ErrInternalServer
	}

	pbConfig := &v1.SignConfig{
		CycleDays:                      int32(config.ConfigValue.CycleDays),
		NextConsecutiveSignDescription: "",
		Reward:                         make([]*v1.SignRewardConfig, 0, len(config.ConfigValue.Rewards)),
	}

	for i, reward := range config.ConfigValue.Rewards {
		if reward.Reward == nil {
			l.Warnf("跳过第%d个奖励配置：普通奖励为空", i+1)
			continue
		}

		if reward.Reward.RewardType == "" {
			l.Warnf("跳过第%d个奖励配置：奖励类型为空", i+1)
			continue
		}

		if reward.Day <= 0 {
			l.Warnf("跳过第%d个奖励配置：天数无效(%d)", i+1, reward.Day)
			continue
		}

		pbReward := &v1.SignRewardConfig{
			Day:                  int32(reward.Day),
			HasConsecutiveReward: reward.HasConsecutiveReward,
			Reward: &v1.Reward{
				RewardType: reward.Reward.RewardType,
				GoldCoins:  reward.Reward.GoldCoins,
			},
		}

		if reward.HasConsecutiveReward {
			if reward.ConsecutiveReward != nil && reward.ConsecutiveReward.RewardType != "" {
				pbReward.ConsecutiveReward = &v1.Reward{
					RewardType: reward.ConsecutiveReward.RewardType,
					GoldCoins:  reward.ConsecutiveReward.GoldCoins,
				}
			} else {
				l.Warnf("第%d天标记有连续签到奖励但奖励内容无效", reward.Day)
				pbReward.HasConsecutiveReward = false
			}
		}

		pbConfig.Reward = append(pbConfig.Reward, pbReward)
	}

	if len(pbConfig.Reward) == 0 {
		l.Error("没有有效的奖励配置")
		return nil, errors.ErrInternalServer
	}

	dayMap := make(map[int]bool)
	for _, reward := range pbConfig.Reward {
		day := int(reward.Day)
		if dayMap[day] {
			l.Errorf("重复的奖励配置天数: %d", day)
			return nil, errors.ErrInternalServer
		}
		dayMap[day] = true
	}

	return pbConfig, nil
}

// getNextConsecutiveSignDescription 获取下一个连续签到奖励描述
// 根据当前连续天数和签到状态，计算下一个有连续奖励的天数和奖励内容
func (s *Service) getNextConsecutiveSignDescription(ctx context.Context, config *v1.SignConfig, consecutiveDays int, hasSign bool) string {
	cycleDays := int(config.CycleDays)
	if cycleDays == 0 || len(config.Reward) == 0 {
		return ""
	}

	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	// 计算今天是周期内的第几天
	today := (consecutiveDays % cycleDays)
	if today == 0 && consecutiveDays > 0 {
		today = cycleDays
	} else if today == 0 {
		today = 1
	}

	// 查找今天是否有连续签到奖励
	var todayConsecutiveReward *v1.SignRewardConfig
	for _, reward := range config.Reward {
		if int(reward.Day) == today && reward.HasConsecutiveReward && reward.ConsecutiveReward != nil && reward.ConsecutiveReward.RewardType != "" {
			todayConsecutiveReward = reward
			break
		}
	}

	if todayConsecutiveReward != nil {
		if hasSign {
			// 今天已签到，查找下一个有连续签到奖励的天数
			for i := 1; i <= cycleDays; i++ {
				nextDay := (today + i) % cycleDays
				if nextDay == 0 {
					nextDay = cycleDays
				}
				for _, reward := range config.Reward {
					if int(reward.Day) == nextDay && reward.HasConsecutiveReward && reward.ConsecutiveReward != nil && reward.ConsecutiveReward.RewardType != "" {
						return i18n.GetWithTemplateDataDefaultEnglish("61841", languageCode, []string{strconv.Itoa(nextDay), strconv.Itoa(int(reward.ConsecutiveReward.GoldCoins))})
					}
				}
			}
			return ""
		} else {
			return i18n.GetWithTemplateDataDefaultEnglish("61841", languageCode, []string{strconv.Itoa(today), strconv.Itoa(int(todayConsecutiveReward.ConsecutiveReward.GoldCoins))})
		}
	}

	// 今天没有连续签到奖励，查找下一个
	for i := 1; i <= cycleDays; i++ {
		nextDay := (today + i) % cycleDays
		if nextDay == 0 {
			nextDay = cycleDays
		}
		for _, reward := range config.Reward {
			if int(reward.Day) == nextDay && reward.HasConsecutiveReward && reward.ConsecutiveReward != nil && reward.ConsecutiveReward.RewardType != "" {
				return i18n.GetWithTemplateDataDefaultEnglish("61841", languageCode, []string{strconv.Itoa(nextDay), strconv.Itoa(int(reward.ConsecutiveReward.GoldCoins))})
			}
		}
	}
	return ""
}

// getUserSignInfo 获取用户当前签到状态
// 计算连续签到天数、周期内剩余天数和未领取奖励，支持时区处理
func (s *Service) getUserSignInfo(ctx context.Context, userID string, timezone string) (*v1.UserSignInfo, error) {
	var l = log.Context(ctx)

	hasSigned, err := s.checkHasSigned(ctx, userID, timezone)
	if err != nil {
		return nil, err
	}

	// 获取用户最近的签到记录
	latestRecord, err := s.signRecord.GetLatestRecord(ctx, userID)
	if err != nil {
		l.Errorf("获取用户最近签到记录失败: %v", err)
		return nil, err
	}

	config, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil {
		l.Errorf("获取签到配置失败: %v", err)
		return nil, err
	}

	if config == nil {
		l.Warn("未找到有效的签到配置")
		return nil, errors.ErrInternalServer
	}

	var consecutiveDays int
	if latestRecord != nil {
		if hasSigned {
			consecutiveDays = latestRecord.ConsecutiveDays
		} else {
			isYesterday := latestRecord.IsYesterday(timezone)
			if isYesterday {
				consecutiveDays = latestRecord.ConsecutiveDays
			} else {
				consecutiveDays = 0
			}
		}
	}

	cycleDays := int(config.CycleDays)
	remainingDays := cycleDays - (consecutiveDays % cycleDays)
	if remainingDays == cycleDays && consecutiveDays > 0 {
		remainingDays = 0
	}

	var remainingRewards []*v1.Reward
	currentDayInCycle := consecutiveDays % cycleDays
	if currentDayInCycle == 0 && consecutiveDays > 0 {
		currentDayInCycle = cycleDays
	}

	for _, rewardConfig := range config.ConfigValue.Rewards {
		if rewardConfig.Day > currentDayInCycle {
			reward := &v1.Reward{
				RewardType: rewardConfig.Reward.RewardType,
				GoldCoins:  rewardConfig.Reward.GoldCoins,
			}
			remainingRewards = append(remainingRewards, reward)

			if rewardConfig.HasConsecutiveReward && rewardConfig.ConsecutiveReward != nil &&
				rewardConfig.ConsecutiveReward.RewardType != "" {
				consecutiveReward := &v1.Reward{
					RewardType: rewardConfig.ConsecutiveReward.RewardType,
					GoldCoins:  rewardConfig.ConsecutiveReward.GoldCoins,
				}
				remainingRewards = append(remainingRewards, consecutiveReward)
			} else if rewardConfig.HasConsecutiveReward {
				l.Warnf("第%d天标记有连续签到奖励但奖励内容为空", rewardConfig.Day)
			}
		}
	}

	return &v1.UserSignInfo{
		HasSignedToday:         hasSigned,
		ConsecutiveDays:        int32(consecutiveDays),
		RemainingDaysInCycle:   int32(remainingDays),
		RemainingRewardInCycle: remainingRewards,
		SignedDesc:             s.getSigneDesc(ctx, hasSigned, remainingRewards),
	}, nil
}

// getSigneDesc 生成签到状态描述文案
// 已签到时显示其他可获得奖励的总金币数，未签到时提示去签到
func (s *Service) getSigneDesc(ctx context.Context, hasSignedToday bool, remainRewardInCycle []*v1.Reward) string {
	languageCode, _ := icontext.LanguageCodeFrom(ctx)

	if hasSignedToday {
		// 计算其他任务可获得的金币总数
		var totalGoldCoins int32
		taskList, err := s.GetTaskList(ctx, nil)
		if err == nil && taskList != nil {
			for _, task := range taskList.Daily {
				if task.Status == int32(models.UserProgressStatusOngoing) && task.RewardType == int32(models.RewardTypeGoldCoin) && task.RewardInfo != nil {
					totalGoldCoins += task.RewardInfo.GoldCoins
				}
			}
			for _, task := range taskList.NewComer {
				if task.Status == int32(models.UserProgressStatusOngoing) && task.RewardType == int32(models.RewardTypeGoldCoin) && task.RewardInfo != nil {
					totalGoldCoins += task.RewardInfo.GoldCoins
				}
			}
		}

		return i18n.GetWithTemplateDataDefaultEnglish("61821", languageCode, []string{strconv.Itoa(int(totalGoldCoins))})
	} else {
		return i18n.GetWithDefaultEnglish("61820", languageCode)
	}
}

// getUserSignHistory 获取用户签到历史记录
// 获取指定天数的签到记录，转换为前端展示格式，支持时区处理
func (s *Service) getUserSignHistory(ctx context.Context, userID string, days int32) ([]*v1.SignHistory, error) {
	var l = log.Context(ctx)

	records, err := s.signRecord.GetUserRecentRecords(ctx, userID, int(days))
	if err != nil {
		l.Errorf("获取用户签到历史失败: %v", err)
		return nil, err
	}

	history := make([]*v1.SignHistory, 0, len(records))

	for _, record := range records {
		// 用户的时区将UTC时间转换为用户本地时间
		var signDateFormatted string
		signDateFormatted = record.SignDate.Format("2006-01-02")
		if record.UserTimezone != "" {
			loc, err := util.ParseTimezone(record.UserTimezone)
			if err == nil {
				signDateFormatted = record.SignDate.In(loc).Format("2006-01-02")
			}
		}

		signHistory := &v1.SignHistory{
			SignDate:          signDateFormatted,
			Day:               int32(record.ConsecutiveDays % int(days)),
			Reward:            &v1.Reward{},
			ConsecutiveReward: &v1.Reward{},
		}

		// 处理周期性天数计算
		if signHistory.Day == 0 && record.ConsecutiveDays > 0 {
			signHistory.Day = days
		}

		if record.RewardJSON.Regular.RewardType != "" {
			signHistory.Reward = &v1.Reward{
				RewardType: record.RewardJSON.Regular.RewardType,
				GoldCoins:  record.RewardJSON.Regular.GoldCoins,
			}
		}

		if record.RewardJSON.Consecutive.RewardType != "" {
			signHistory.ConsecutiveReward = &v1.Reward{
				RewardType: record.RewardJSON.Consecutive.RewardType,
				GoldCoins:  record.RewardJSON.Consecutive.GoldCoins,
			}
		}

		history = append(history, signHistory)
	}

	return history, nil
}

// getOtherUserSignLogs 获取其他用户签到动态
// 展示最近的真实用户签到记录，失败时使用默认数据
func (s *Service) getOtherUserSignLogs(ctx context.Context) ([]*v1.SignLog, error) {
	var l = log.Context(ctx)

	signRecords, err := s.signRecord.GetRecentSignRecords(ctx, 10)
	if err != nil {
		l.Errorf("获取最近签到记录失败: %v", err)
		return nil, err
	}

	if len(signRecords) == 0 {
		return s.getDefaultSignLogs(), nil
	}

	userIDs := make([]string, 0, len(signRecords))
	for _, record := range signRecords {
		if record != nil && record.UserID != "" {
			userIDs = append(userIDs, record.UserID)
		}
	}

	if len(userIDs) == 0 {
		return s.getDefaultSignLogs(), nil
	}

	userInfoMap, err := s.fetchUserInfoBatch(ctx, userIDs)
	if err != nil || len(userInfoMap) == 0 {
		l.Warnf("获取用户信息失败，使用默认数据: %v", err)
		return s.getDefaultSignLogs(), nil
	}

	logs := make([]*v1.SignLog, 0, len(signRecords))
	for _, record := range signRecords {
		if record == nil {
			l.Warn("跳过空的签到记录")
			continue
		}

		if record.UserID == "" {
			l.Warn("跳过用户ID为空的签到记录")
			continue
		}

		description := s.getSignRewardDescription(ctx, record)
		if description == "" {
			l.Warnf("签到记录奖励描述为空: recordID=%d", record.ID)
			continue
		}

		userInfo, exists := userInfoMap[record.UserID]
		if !exists {
			l.Warnf("未找到用户信息: userID=%s", record.UserID)
			continue
		}

		if userInfo == nil {
			l.Warnf("用户信息为空: userID=%s", record.UserID)
			continue
		}

		nickname := userInfo.Nickname
		if nickname == "" {
			nickname = record.UserID
		}

		avatar := userInfo.AvatarAddress
		if avatar == "" {
			avatar = urlformat.FullPath("/fissionactivity/638848266633046151/FAM638848266633046151_587266.png", urlTemplate)
		}

		otherSignedLog := &v1.SignLog{
			Description: description,
			Name:        s.getMaskedUsername(nickname),
			Avatar:      avatar,
		}
		logs = append(logs, otherSignedLog)

		if len(logs) >= 3 {
			break
		}
	}

	if len(logs) < 3 {
		defaultLogs := s.getDefaultSignLogs()
		neededLogs := 3 - len(logs)
		for i := 0; i < neededLogs && i < len(defaultLogs); i++ {
			logs = append(logs, defaultLogs[i])
		}
	}

	return logs, nil
}

// getDefaultSignLogs 获取默认的签到动态数据
// 当无法获取真实用户签到记录时使用的兜底数据
func (s *Service) getDefaultSignLogs() []*v1.SignLog {
	return []*v1.SignLog{
		{
			Description: "签到获得#5#积分",
			Name:        "ab**ef",
			Avatar:      urlformat.FullPath("/fissionactivity/638848266633046151/FAM638848266633046151_587266.png", urlTemplate),
		},
	}
}

// getMaskedUsername 对用户名进行脱敏处理
// 短名称不脱敏，长名称保留首尾字符，中间用**替代
func (s *Service) getMaskedUsername(userName string) string {
	if userName == "" {
		return "未**名"
	}
	runes := []rune(userName)
	length := len(runes)
	if length <= 2 {
		return userName
	}

	headChars, tailChars := 1, 1
	if length > 6 {
		headChars, tailChars = 2, 2
	}
	head := string(runes[:headChars])
	tail := string(runes[length-tailChars:])
	return head + "**" + tail
}

// getSignRewardDescription 生成签到奖励的描述文案
// 根据签到记录的奖励信息生成用户可读的描述，支持普通奖励和连续签到奖励
func (s *Service) getSignRewardDescription(ctx context.Context, record *models.SignRecord) string {
	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	var description string

	if record.RewardJSON.Regular.RewardType == RewardTypeGoldCons && record.RewardJSON.Regular.GoldCoins > 0 {
		description = i18n.GetWithTemplateDataDefaultEnglish("61840", languageCode, []string{strconv.Itoa(int(record.RewardJSON.Regular.GoldCoins))})
	}

	if record.RewardJSON.Consecutive.RewardType == RewardTypeGoldCons && record.RewardJSON.Consecutive.GoldCoins > 0 {
		description = i18n.GetWithTemplateDataDefaultEnglish("61840", languageCode, []string{strconv.Itoa(int(record.RewardJSON.Consecutive.GoldCoins + record.RewardJSON.Regular.GoldCoins))})
	}

	return description
}

// checkHasSigned 检查用户今日是否已签到
// 调用DAO层查询今日签到状态，失败时返回系统错误
func (s *Service) checkHasSigned(ctx context.Context, userID string, timezone string) (bool, error) {
	hasSigned, err := s.signRecord.CheckTodaySignIn(ctx, userID, timezone)
	if err != nil {
		log.Context(ctx).Errorf("检查用户今日是否已签到失败: %v", err)
		return false, errors.ErrInternalServer
	}
	return hasSigned, nil
}

// calculateConsecutiveDays 计算连续签到天数,注意这是签到的时候的连续签到天，会判断是否断签，前提是判断今天已经牵到了
// 检查最近签到记录，如果是昨天签到则连续天数+1，否则重置为1
func (s *Service) calculateConsecutiveDays(ctx context.Context, userID string, timezone string) (int, error) {
	var l = log.Context(ctx)

	latestRecord, err := s.signRecord.GetLatestRecord(ctx, userID)
	if err != nil {
		l.Errorf("获取用户最近签到记录失败: %v", err)
		return 0, err
	}

	if latestRecord == nil {
		return 1, nil
	}

	isYesterday := latestRecord.IsYesterday(timezone)
	if isYesterday {
		// 如果是昨天，连续签到天数+1
		return latestRecord.ConsecutiveDays + 1, nil
	} else {
		return 1, nil
	}
}

// determineRewards 根据连续签到天数确定奖励内容
// 计算当前在周期内的天数，查找对应的普通奖励和连续签到奖励配置
func (s *Service) determineRewards(ctx context.Context, config *v1.SignConfig, consecutiveDays int) (*v1.Reward, *v1.Reward, error) {
	var l = log.Context(ctx)

	if config == nil {
		return nil, nil, fmt.Errorf("config is nil")
	}
	if consecutiveDays <= 0 {
		return nil, nil, fmt.Errorf("consecutiveDays must be positive, got: %d", consecutiveDays)
	}

	cycleDays := int(config.CycleDays)
	if cycleDays <= 0 {
		return nil, nil, fmt.Errorf("invalid cycle days: %d", cycleDays)
	}

	dayInCycle := consecutiveDays % cycleDays
	if dayInCycle == 0 {
		dayInCycle = cycleDays
	}

	var targetRewardConfig *v1.SignRewardConfig
	for _, rewardConfig := range config.Reward {
		if rewardConfig == nil {
			l.Warn("跳过空的奖励配置")
			continue
		}
		if int(rewardConfig.Day) == dayInCycle {
			targetRewardConfig = rewardConfig
			break
		}
	}

	if targetRewardConfig == nil {
		l.Errorf("未找到第%d天的奖励配置", dayInCycle)
		return nil, nil, errors.ErrInternalServer
	}

	if targetRewardConfig.Reward == nil {
		return nil, nil, errors.ErrInternalServer
	}

	regularReward := targetRewardConfig.Reward

	var consecutiveReward *v1.Reward
	if targetRewardConfig.HasConsecutiveReward {
		if targetRewardConfig.ConsecutiveReward != nil && targetRewardConfig.ConsecutiveReward.RewardType != "" {
			consecutiveReward = targetRewardConfig.ConsecutiveReward
			l.Infof("第%d天有连续签到奖励: %s, 数量: %d",
				dayInCycle, consecutiveReward.RewardType, consecutiveReward.GoldCoins)
		} else {
			l.Warnf("第%d天标记有连续签到奖励但奖励内容为空", dayInCycle)
		}
	}

	return regularReward, consecutiveReward, nil
}

// getValidTimezone 获取有效的时区信息
// 解析时区字符串，失败时使用UTC作为默认时区
func (s *Service) getValidTimezone(ctx context.Context, timezone string) (*time.Location, error) {
	if timezone == "" {
		return time.UTC, nil
	}
	loc, err := util.ParseTimezone(timezone)
	if err != nil {
		log.Context(ctx).Errorf("解析时区失败: %v, 使用默认UTC时区", err)
		return time.UTC, nil
	}
	return loc, nil
}

// recordSignIn 记录用户签到信息
// 创建签到记录，包含奖励信息、连续天数、时区等，用于奖励发放和历史查询
func (s *Service) recordSignIn(ctx context.Context, userID string, timezone string, consecutiveDays int, regularReward, consecutiveReward *v1.Reward) (*models.SignRecord, error) {
	var l = log.Context(ctx)

	loc, err := s.getValidTimezone(ctx, timezone)
	if err != nil {
		return nil, err
	}
	now := util.GetCurrentTimeWithEnvByTimezone(timezone).In(loc)

	taskConfig, err := s.signTaskConfig.GetActiveConfig(ctx, models.DefaultSignInConfigKey)
	if err != nil {
		l.Errorf("获取任务配置失败: %v", err)
		return nil, err
	}

	if taskConfig == nil {
		return nil, errors.ErrInternalServer
	}

	var rewardCoin int32
	if regularReward != nil && regularReward.RewardType == RewardTypeGoldCons {
		rewardCoin += regularReward.GoldCoins
	}
	if consecutiveReward != nil && consecutiveReward.RewardType == RewardTypeGoldCons {
		rewardCoin += consecutiveReward.GoldCoins
	}

	// 构建奖励JSON数据
	var rewardJSON models.RewardJSONMap
	if regularReward != nil {
		rewardJSON.Regular = models.RewardJSON{
			RewardType: regularReward.RewardType,
			GoldCoins:  regularReward.GoldCoins,
		}
	}
	if consecutiveReward != nil && consecutiveReward.RewardType != "" {
		rewardJSON.Consecutive = models.RewardJSON{
			RewardType: consecutiveReward.RewardType,
			GoldCoins:  consecutiveReward.GoldCoins,
		}
	}

	signRecord := &models.SignRecord{
		UserID:          userID,
		SignDate:        now,
		TaskID:          taskConfig.ID,
		ConsecutiveDays: consecutiveDays,
		RewardJSON:      rewardJSON,
		UserTimezone:    timezone,
	}

	err = s.signRecord.Create(ctx, signRecord)
	if err != nil {
		l.Errorf("创建签到记录失败: %v", err)
		return nil, err
	}

	return signRecord, nil
}

// processReward 处理签到奖励发放
// 根据奖励类型调用对应的处理逻辑，目前仅支持金币奖励
func (s *Service) processReward(ctx context.Context, userID string, sourceType int8, recordId uint, reward *v1.Reward) error {
	switch reward.RewardType {
	case RewardTypeGoldCons:
		return s.processCoinReward(ctx, userID, sourceType, recordId, reward)
	default:
		return errors.ErrInternalServer
	}
}

// processCoinReward 处理金币奖励发放
// 先创建处理中状态的奖励流水，调用金币服务发放，根据结果更新流水状态并报警
func (s *Service) processCoinReward(ctx context.Context, userID string, sourceType int8, recordId uint, reward *v1.Reward) error {
	var l = log.Context(ctx)

	rewardLog, err := s.createRewardLog(ctx, userID, sourceType, recordId, RewardTypeGoldCons, reward.GoldCoins, models.StatusProcessing)
	if err != nil {
		return err
	}

	issueErr := s.issueGoldCoins(ctx, userID, int(reward.GoldCoins))

	if issueErr == nil {
		rewardLog.Status = models.StatusSuccess
	} else {
		rewardLog.Status = models.StatusFailed
		l.Errorf("发放金币失败: %v rewardLogId %d userId %s", issueErr, rewardLog.ID, userID)
		alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("签到金币发放失败: userId=%s, goldCoins=%d, rewardLogId=%d, error=%v", userID, reward.GoldCoins, rewardLog.ID, issueErr))
	}

	updateErr := s.signRewardLog.Update(ctx, rewardLog)
	if updateErr != nil {
		l.Errorf("更新奖励记录状态失败: %v", updateErr)
	}

	if issueErr != nil {
		return errors.ErrInternalServer
	}
	return nil
}

// createRewardLog 创建奖励发放记录
// 生成唯一操作ID防重，记录奖励发放状态，支持幂等性处理
func (s *Service) createRewardLog(ctx context.Context, userID string, sourceType int8, recordId uint, rewardType string, goldCoins int32, status int8) (*models.SignRewardLog, error) {
	var l = log.Context(ctx)

	operationID := fmt.Sprintf("sign_reward_%s_%s_%d", rewardType, userID, recordId)
	rewardLog := &models.SignRewardLog{
		UserID:      userID,
		SourceType:  sourceType,
		RecordId:    recordId,
		GoldCoins:   goldCoins,
		Status:      status,
		OperationID: operationID,
	}

	err := s.signRewardLog.Create(ctx, rewardLog)
	if err != nil {
		l.Errorf("创建奖励记录失败: %v", err)
		// 检查是否因重复操作ID导致的失败
		if existLog, existErr := s.signRewardLog.GetByOperationID(ctx, operationID); existErr == nil && existLog != nil {
			l.Infof("奖励记录已存在，操作ID: %s", operationID)
			return existLog, nil
		}
		return nil, err
	}
	return rewardLog, nil
}

// getSignInResult 构建签到结果响应
// 根据签到记录计算周期状态、下一天奖励、签到描述等，支持重复签到和正常签到两种状态
func (s *Service) getSignInResult(ctx context.Context, userID string, timezone string, alreadySigned bool) (*v1.SignInReply, error) {
	var l = log.Context(ctx)

	latestRecord, err := s.signRecord.GetLatestRecord(ctx, userID)
	if err != nil {
		l.Errorf("获取用户最近签到记录失败: %v", err)
		return nil, err
	}

	if latestRecord == nil {
		return nil, errors.ErrInternalServer
	}

	config, err := s.getSignConfig(ctx)
	if err != nil {
		return nil, err
	}

	cycleDays := int(config.CycleDays)
	isCycleEnd := latestRecord.ConsecutiveDays > 0 && latestRecord.ConsecutiveDays%cycleDays == 0
	signDateStr := latestRecord.SignDate.Format("2006-01-02")

	nextDay := (latestRecord.ConsecutiveDays % cycleDays) + 1
	if nextDay > cycleDays {
		nextDay = 1
	}

	var nextRewardConfig *v1.SignRewardConfig
	for _, rewardConfig := range config.Reward {
		if int(rewardConfig.Day) == nextDay {
			nextRewardConfig = rewardConfig
			break
		}
	}

	nextRewards := make([]*v1.Reward, 0)
	if nextRewardConfig != nil {
		if nextRewardConfig.Reward != nil {
			nextRewards = append(nextRewards, nextRewardConfig.Reward)
		}
		if nextRewardConfig.HasConsecutiveReward && nextRewardConfig.ConsecutiveReward != nil {
			nextRewards = append(nextRewards, nextRewardConfig.ConsecutiveReward)
		}
	}

	signDescription := ""
	signStatus := SignStatusSuccess
	languageCode, _ := icontext.LanguageCodeFrom(ctx)

	if alreadySigned {
		signDescription = i18n.GetWithDefaultEnglish("61399", languageCode)
		signStatus = SignStatusDuplicate
	} else if nextRewardConfig != nil && nextRewardConfig.Reward != nil {
		var totalCoins int
		if nextRewardConfig.ConsecutiveReward != nil {
			totalCoins = int(nextRewardConfig.Reward.GoldCoins + nextRewardConfig.ConsecutiveReward.GoldCoins)
		} else {
			totalCoins = int(nextRewardConfig.Reward.GoldCoins)
		}
		signDescription = i18n.GetWithTemplateDataDefaultEnglish("59873", languageCode, []string{strconv.Itoa(totalCoins)})
	}

	regularReward := &v1.Reward{
		RewardType: latestRecord.RewardJSON.Regular.RewardType,
		GoldCoins:  latestRecord.RewardJSON.Regular.GoldCoins,
	}

	var consecutiveReward *v1.Reward
	if latestRecord.RewardJSON.Consecutive.RewardType != "" {
		consecutiveReward = &v1.Reward{
			RewardType: latestRecord.RewardJSON.Consecutive.RewardType,
			GoldCoins:  latestRecord.RewardJSON.Consecutive.GoldCoins,
		}

		if !alreadySigned {
			signDescription = i18n.GetWithTemplateDataDefaultEnglish("62081", languageCode, []string{strconv.Itoa(latestRecord.ConsecutiveDays), strconv.Itoa(int(consecutiveReward.GoldCoins))})
		}
	}

	result := &v1.SignInReply{
		SignDate:          signDateStr,
		Reward:            []*v1.Reward{regularReward},
		ConsecutiveReward: []*v1.Reward{},
		HasSignedToday:    true,
		ConsecutiveDays:   int32(latestRecord.ConsecutiveDays),
		IsCycleEnd:        isCycleEnd,
		NextReward:        nextRewards,
		SignDescription:   signDescription,
		SignStatus:        signStatus,
	}

	if consecutiveReward != nil {
		result.ConsecutiveReward = []*v1.Reward{consecutiveReward}
	}

	return result, nil
}

// issueGoldCoins 调用金币服务发放签到奖励
// 构建多语言产品信息，设置超时控制，失败时触发飞书报警
func (s *Service) issueGoldCoins(ctx context.Context, userId string, goldAmount int) error {
	var l = log.Context(ctx)

	if userId == "" {
		return fmt.Errorf("用户ID不能为空")
	}

	if goldAmount <= 0 {
		return fmt.Errorf("金币数量必须大于0，当前值: %d", goldAmount)
	}

	if s.goldClient == nil {
		return fmt.Errorf("金币服务客户端未初始化")
	}

	languageCode, _ := icontext.LanguageCodeFrom(ctx)
	translateProductName, err := json.Marshal(s.GetLanguageExtraStruct(ctx, "61820"))
	if err != nil {
		l.Warnf("GetLanguageExtraRawString: %v", translateProductName)
	}

	req := &gold.DistributeRequest{
		UserId:               userId,
		ProductName:          i18n.GetWithDefaultEnglish("61820", languageCode),
		TranslateProductName: string(translateProductName),
		EnumOrderType:        int(gold.EnumOrderTypeSign),
		Points:               goldAmount,
		Remark:               i18n.GetWithDefaultEnglish("61820", languageCode),
		ProductImgUrl:        urlformat.FullPath("/fissionactivity/638860153281190854/FAM638860153281190854_675216.png", urlTemplate),
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	resp, err := s.goldClient.Distribute(timeoutCtx, req)
	if err != nil {
		l.Errorf("签到发送金币失败: %v, userId=%s, goldAmount=%d", err, userId, goldAmount)
		alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("签到金币服务调用失败: userId=%s, goldAmount=%d, error=%v", userId, goldAmount, err))
		return fmt.Errorf("金币发放服务异常: %w", err)
	}

	if resp == nil {
		l.Errorf("签到发送金币响应为空: userId=%s, goldAmount=%d", userId, goldAmount)
		return fmt.Errorf("金币发放服务响应异常")
	}

	if !resp.IsSuccess {
		l.Errorf("签到发送金币失败: code=%d, msg=%s, userId=%s, goldAmount=%d",
			resp.Code, resp.Message, userId, goldAmount)
		alarm.FeiShuAlarm(reqid.GenRequestID(), fmt.Sprintf("签到金币发放业务失败: userId=%s, goldAmount=%d, code=%d, msg=%s", userId, goldAmount, resp.Code, resp.Message))
		return fmt.Errorf("签到发送金币失败: %s", resp.Message)
	}

	l.Infof("签到发送金币成功: userId=%s, goldAmount=%d,", userId, goldAmount)
	return nil
}

// rollbackSignRecord 回滚签到记录
// 当奖励发放失败时删除已创建的签到记录，确保数据一致性
func (s *Service) rollbackSignRecord(ctx context.Context, signRecord *models.SignRecord) error {
	if signRecord == nil {
		return fmt.Errorf("signRecord is nil")
	}

	err := s.signRecord.Delete(ctx, signRecord.ID)
	if err != nil {
		log.Context(ctx).Errorf("删除失败的签到记录失败: recordID=%d, err=%v", signRecord.ID, err)
		return err
	}

	log.Context(ctx).Infof("成功回滚签到记录: userID=%s, recordID=%d", signRecord.UserID, signRecord.ID)
	return nil
}

// GetLanguageExtraStruct 获取多语言配置结构
// 为指定的i18n代码生成所有支持语言的翻译映射，用于产品名称等多语言场景
func (s *Service) GetLanguageExtraStruct(ctx context.Context, code string) map[string]string {
	var l = log.Context(ctx)

	languageCodeList := supportedLanguageCodes
	languageCodeMap := make(map[string]string)
	for _, languageCode := range languageCodeList {
		value := i18n.GetWithDefaultEnglish(code, languageCode)
		if value == "" {
			l.Warnf("多语言内容为空: code=%s, language=%s", code, languageCode)
			continue
		}
		languageCodeMap[languageCode] = value
	}
	return languageCodeMap
}

// CalculateTodaySignReward 计算今天签到可获得的积分,今天可以签到可以获得积分，不区分签到没签到
func (s *Service) CalculateTodaySignReward(ctx context.Context, userID string, timezone string) (int32, error) {
	var l = log.Context(ctx)
	// 计算连续签到天数
	consecutiveDays := 0
	latestRecord, err := s.signRecord.GetLatestRecord(ctx, userID)
	if err != nil {
		l.Errorf("获取用户最近签到记录失败: %v", err)
		return 0, err
	}

	if latestRecord == nil {
		consecutiveDays = 1
	} else {
		//判断是否断签，最近一次签到是今天说明已经签到，如果是昨天
		isYesterday := latestRecord.IsYesterday(timezone)
		isToday := latestRecord.IsToday(timezone)
		if isToday {
			consecutiveDays = latestRecord.ConsecutiveDays
		} else if isYesterday {
			consecutiveDays = latestRecord.ConsecutiveDays + 1
		} else {
			consecutiveDays = 1
		}
	}

	config, err := s.getSignConfig(ctx)
	if err != nil {
		l.Errorf("获取签到配置失败: %v", err)
		return 0, err
	}

	regularReward, consecutiveReward, err := s.determineRewards(ctx, config, consecutiveDays)
	if err != nil {
		l.Errorf("确定签到奖励失败: %v", err)
		return 0, err
	}

	// 计算总积分
	var totalGoldCoins int32
	if regularReward != nil && regularReward.RewardType == RewardTypeGoldCons {
		totalGoldCoins += regularReward.GoldCoins
	}
	if consecutiveReward != nil && consecutiveReward.RewardType == RewardTypeGoldCons {
		totalGoldCoins += consecutiveReward.GoldCoins
	}

	return totalGoldCoins, nil
}
