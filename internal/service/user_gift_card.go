package service

import (
	"context"
	"errors"
	"strings"
	"time"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/i18n"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	v1 "gold_store/api/gold_store/v1"
)

const (
	UserGiftCardTagEnable  = "61370"
	UserGiftCardTagDisable = "61371"
	StampExpiredEn         = "gold_store/giftcard/en02.png"
	StampRedeemedEn        = "gold_store/giftcard/en01.png"
	StampExpiredZh         = "gold_store/giftcard/zh02.png"
	StampRedeemedZh        = "gold_store/giftcard/zh01.png"
)

func SelectTNoWhere[T comparable, K any](s []*T, fnc2 func(*T) K) []K {
	var result []K
	for _, ptr := range s {
		result = append(result, fnc2(ptr))

	}
	return result
}
func FirstT[T any](s []*T, fnc func(*T) bool) *T {
	for _, ptr := range s {
		if fnc(ptr) {
			return ptr
		}
	}
	return nil
}

// GetUserGiftCard 礼品券
func (s *Service) GetUserGiftCard(ctx context.Context, in *v1.GetUserGiftCardRequest) (*v1.GetUserGiftCardReply, error) {
	var (
		l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		//只有是第一次进来并且是第一页才返回tab
		tabs = make([]*v1.GiftCardTab, 0, 2)
		now  = time.Now()
	)

	if in.Size <= 0 {
		in.Size = 10
	}

	if in.Page <= 0 {
		in.Page = 1
	}

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	if in.Page == 1 {
		totalCount, err := s.userGiftCard.CountByUserId(ctx, userId)
		if err != nil {
			l.Errorf("userGiftCard.CountByUserId Err:%v", err)
			return nil, err
		}

		enableCount, err := s.userGiftCard.EnableCountByUserId(ctx, userId)
		if err != nil {
			l.Errorf("userGiftCard.EnableCountByUserId Err:%v", err)
		}
		disableCount := totalCount - enableCount

		tabs = append(tabs, &v1.GiftCardTab{
			Name:  i18n.GetWithDefaultEnglish(UserGiftCardTagEnable, languageCode), // 可用卡
			Value: v1.UserGiftCardTabType_UserGiftCardTabTypeENABLE,
			Count: int32(enableCount),
		})

		tabs = append(tabs, &v1.GiftCardTab{
			Name:  i18n.GetWithDefaultEnglish(UserGiftCardTagDisable, languageCode), // 不可用卡
			Value: v1.UserGiftCardTabType_UserGiftCardTabTypeDISABLE,
			Count: int32(disableCount),
		})
	}

	list, err := s.userGiftCard.PageList(ctx, int(in.Status), int(in.Size), int(in.Page), userId)
	if err != nil {
		l.Errorf("userGiftCard.PageList Err:%v", err)
		return nil, err
	}

	var (
		giftCards    = make([]*v1.GiftCard, 0, len(list))
		goodsIds     = make([]string, 0, len(list))
		goodsMapping = make(map[string]*v1.GoodsListItem, len(list))
	)

	for _, value := range list {
		goodsIds = append(goodsIds, value.GoodsID)
	}

	if len(goodsIds) > 0 {
		var goodsList []*v1.GoodsListItem
		goodsList, err = s.findGoodsInfo(ctx, goodsIds)
		if err != nil {
			l.Errorf("findGoodsInfo Err:%v", err)
			return nil, err
		}

		for _, goods := range goodsList {
			goodsMapping[goods.GoodsId] = goods
		}
	}

	for _, v := range list {
		goods, ok := goodsMapping[v.GoodsID]
		if !ok {
			continue
		}

		var (
			statusStamp = ""
			status      = v1.GiftCardStatus_GiftCardStatusUnknown
		)

		// 如果已使用
		if v.Status == 1 {
			status = v1.GiftCardStatus_GiftCardStatusUsed // 已使用
		} else if v.StartTime.After(now) {
			status = v1.GiftCardStatus_GiftCardStatusNotReady // 未到生效时间
		} else if v.EndTime.Before(now) {
			status = v1.GiftCardStatus_GiftCardStatusExpired // 以过期
		} else {
			status = v1.GiftCardStatus_GiftCardStatusEnable // 可用
		}

		if in.Status == v1.UserGiftCardTabType_UserGiftCardTabTypeDISABLE {
			statusStamp = getStamp(languageCode, int(v.Status))
		}

		giftCards = append(giftCards, &v1.GiftCard{
			GoodsId:     v.GoodsID,
			StartTime:   v.StartTime.Unix(),
			EndTime:     v.EndTime.Unix(),
			Name:        goods.Name,
			Status:      status,
			StatusStamp: statusStamp,
			Id:          v.ID,
			Image:       goods.Image,
		})
	}

	return &v1.GetUserGiftCardReply{
		Tabs:      tabs,
		GiftCards: giftCards,
	}, nil

}

func getStamp(languageCode string, status int) string {
	var statusStamp = ""
	if strings.ToLower(languageCode) == "zh-cn" {
		if status == 1 {
			statusStamp = urlformat.FullPath(StampRedeemedZh, urlTemplate)
		} else {
			statusStamp = urlformat.FullPath(StampExpiredZh, urlTemplate)
		}
	} else {
		if status == 1 {
			statusStamp = urlformat.FullPath(StampRedeemedEn, urlTemplate)
		} else {
			statusStamp = urlformat.FullPath(StampExpiredEn, urlTemplate)
		}
	}
	return statusStamp
}

// GiftCardRemind 礼品弹框
func (s *Service) GiftCardRemind(ctx context.Context, _ *v1.GiftCardRemindRequest) (*v1.GiftCardRemindReply, error) {
	var (
		l         = log.Context(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
	)

	if userId == "" {
		return nil, innErr.ErrLogin
	}

	//查询用户获得的卡券
	userLastGiftCard, err := s.userGiftCard.GetLast(ctx, userId)
	if err != nil && !errors.Is(err, ormhelper.ErrNotFound) {
		l.Errorf("userGiftCard.GetLast Err:%v", err)
		return nil, err
	}

	// 到这里说明没有找到
	if err != nil {
		return &v1.GiftCardRemindReply{
			IsHaveGift: false,
		}, nil
	}
	giftsetting, err := s.giftCardSetting.GetSingle(ctx, userLastGiftCard.SettingID)
	if err != nil {
		l.Errorf("findGoodsInfo Err:%v", err)
	}
	var (
		goodsName  string
		goodsImage *v1.Image
	)

	if userLastGiftCard.GoodsID != "" {
		var goodsList []*v1.GoodsListItem
		goodsList, err = s.findGoodsInfo(ctx, []string{userLastGiftCard.GoodsID})
		if err != nil {
			l.Errorf("findGoodsInfo Err:%v", err)
			return nil, err
		}

		if len(goodsList) > 0 {
			goodsName = goodsList[0].Name
			goodsImage = goodsList[0].Image
			goodsImage.Url = urlformat.FullPath(giftsetting.Image, urlTemplate)
		}
	}

	err = s.userGiftCard.UpdateGiftCardNoticeStatus(ctx, userId)
	if err != nil {
		l.Errorf("UpdateGiftCardNoticeStatus Err:%v", err)
		// 这里发生错误不影响主流程
	}

	return &v1.GiftCardRemindReply{
		Id:         userLastGiftCard.ID,
		Name:       goodsName,
		Image:      goodsImage,
		StartTime:  userLastGiftCard.StartTime.Unix(),
		EndTime:    userLastGiftCard.EndTime.Unix(),
		GoodsId:    userLastGiftCard.GoodsID,
		IsHaveGift: true,
	}, nil
}
