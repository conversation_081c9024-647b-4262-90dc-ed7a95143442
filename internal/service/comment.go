package service

import (
	"cmp"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"

	"api-expo/api/common"
	communityv1 "api-expo/api/community/v1"
	v1 "api-expo/api/expo/v1"
	usercenterv1 "api-expo/api/user_center/v1"
	"api-expo/internal/models"
	"api-expo/internal/util"
	"api-expo/pkg/id"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
)

const (
	commandmentUrl                = "https://h8imgs.zy223.com"
	nickNameColor                 = "#3D3D3D"
	commandmentTemplate           = "-list600"
	IndustryOptions        string = "industry_options"
	IdentityOptions        string = "identity_options"
	ParticipantModeOptions string = "participant_mode_options"
	TicketTypeOptions      string = "ticket_type_options"
	TicketStatusOptions    string = "ticket_status_options"
	InvitedStatusOptions   string = "invited_status_options"
	ProductOptions         string = "product_options"
	IdentitySubOptions     string = "identity_sub_options"
	CooperationOptions     string = "cooperation_options"

	ExpoStatusOptions               string = "expo_status_options"
	ExpoPriceUnitOptions            string = "expo_price_unit_options"
	ExpoAwardOptions                string = "expo_award_options"
	ExpoTypeOptions                 string = "expo_type_options"
	rediskey_comment_publish        string = "comment_publish_redis"
	rediskey_comment_publish_verify string = "rediskey_comment_publish_verify"
	ShiPanContent                          = "我完成了{0}，获得了{1}，可以与参展嘉宾、交易商沟通了"
	BookContent                            = "我预约了{0}{1}的展会演讲"
)

func selectTNoWhere[T comparable, K any](s []*T, fnc2 func(*T) K) []K {
	var result []K
	for _, ptr := range s {
		result = append(result, fnc2(ptr))

	}
	return result
}
func firstT[T any](s []*T, fnc func(*T) bool) *T {
	for _, ptr := range s {
		if fnc(ptr) {
			return ptr
		}
	}
	return nil
}
func existsT[T comparable](s []T, fnc func(T) bool) bool {
	for _, ptr := range s {
		if fnc(ptr) {
			return true
		}
	}
	return false
}

func orderByInt[T comparable](s []*T, fnc func(*T) int32) []*T {
	sort.Slice(s, func(i, j int) bool {
		return fnc(s[i]) < fnc(s[j])
	})
	return s
}
func orderByIntDesc[T comparable](s []*T, fnc func(*T) int32) []*T {
	sort.Slice(s, func(i, j int) bool {
		return fnc(s[i]) > fnc(s[j])
	})
	return s
}
func selectT[T comparable, K any](s []*T, fnc func(*T) bool, fnc2 func(*T) K) []K {
	var result []K
	for _, ptr := range s {
		if fnc(ptr) {
			result = append(result, fnc2(ptr))
		}
	}
	return result
}
func Where[T comparable](s []*T, fnc func(*T) bool) []*T {
	var result []*T
	for _, ptr := range s {
		if fnc(ptr) {
			result = append(result, ptr)
		}
	}
	return result
}

func getUserByUserIs(s *Service, ctx context.Context, userIds []string) (*usercenterv1.GetBasicUserInfoReply, error) {
	l := log.Context(ctx)
	userInfos, err := s.user.GetBasicUserInfo(ctx, &usercenterv1.GetUserWikiNumbersRequest{
		UserIds: userIds,
	})
	if err != nil {
		l.Errorf("获取用户信息失败: %v", err)
		return nil, err
	}
	return userInfos, nil
}

// PostExpoInteraction 发布评价
func (s *Service) PostExpoInteraction(ctx context.Context, request *v1.PostExpoInteractionRequest) (*v1.PostExpoInteractionReply, error) {
	var (
		//l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
		Ip, _           = icontext.ClientIPFrom(ctx)
		//只有是第一次进来并且是第一页才返回tab
		now = time.Now().UTC()
	)

	expoId, err := strconv.ParseInt(request.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	if !isShowCommentGeetTest(s, ctx, userId) {
		return &v1.PostExpoInteractionReply{
			IsOpenGeettest: true,
		}, nil
	}

	userInfos, err := s.fetchUserInfoBatch(ctx, []string{userId})
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	if len(userInfos) == 0 {
		return nil, innErr.ErrBadRequest
	}
	userInfo := createUserInfo(userInfos, userId)

	contents := []string{"预祝展会圆满成功！🎉🎉🎉", "为展会加油助威！😎😎😎"}
	var (
		contentType int32 = 1
		status      int32 = 1
		parentId    string
		rootId      string
		replyName   string
	)
	if len(request.ReplyId) != 0 {
		parentId = request.ReplyId
		var singleContent, _ = s.comment.Get(ctx, request.ReplyId)
		if singleContent == nil {
			return nil, innErr.ErrBadRequest
		}
		replyName = singleContent.NickName
		if len(singleContent.RootID) == 0 {
			rootId = request.ReplyId
		} else {
			rootId = singleContent.RootID
		}
	}
	if lo.ContainsBy(contents, func(s string) bool {
		return s == request.Content
	}) == true {
		contentType = 3
		status = 2
	} else {
		tranResult, err := s.upstream.Translate(request.Content, "zh-CN")
		if err == nil {
			if len(tranResult.Language) > 0 {
				languageCode = tranResult.Language
			}
		}
	}
	comment := &models.ExpoComment{
		ID:            id.CreatePrimaryId("COM"),
		ExpoID:        expoId,
		UserID:        userId,
		Content:       request.Content,
		ContentType:   contentType,
		Status:        status,
		ContentDesc:   "",
		RootID:        rootId,
		ParentID:      parentId,
		LanguageCode:  languageCode,
		CountryCode:   countryCode,
		Ip:            Ip,
		CreatedAt:     now,
		UpdatedAt:     now,
		LikeCount:     0,
		NickName:      userInfo.NickName,
		ReplyNickName: replyName,
	}
	err = s.comment.Add(ctx, comment)

	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	//添加图片

	images := make([]*models.ExpoCommentImage, 0, 10)
	if len(request.Images) > 0 {
		for k, v := range request.Images {
			images = append(images, &models.ExpoCommentImage{
				CommentID: comment.ID,
				Sort:      int32(k),
				CreatedAt: now,
				UpdatedAt: now,
				ImageURL:  v,
			})
		}
		err := s.commentImg.Add(ctx, images)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
	}
	//添加@
	commentAts := make([]*models.ExpoCommentAt, 0, 10)
	if len(request.AtUsers) > 0 {
		for _, v := range request.AtUsers {
			commentAts = append(commentAts, &models.ExpoCommentAt{
				CommentID: comment.ID,
				UserID:    v.UserId,
				Nickname:  v.NickName,
				CreatedAt: now,
				UpdatedAt: now,
			})
		}
		err := s.commentAt.Add(ctx, commentAts)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
	}
	commentSingle, err := s.comment.Get(ctx, comment.ID)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	atUsers, _ := s.commentAt.GetListByCommentId(ctx, []string{comment.ID})
	var atList = CreateAtUser(atUsers, comment.ID)
	resultComment := &v1.ExpoInteraction{
		CommentId:     comment.ID,
		Content:       commentSingle.Content,
		OriginContent: commentSingle.Content,
		ReplyName:     replyName,
		Country:       "中国",
		Time:          strconv.FormatInt(commentSingle.CreatedAt.Unix(), 10),
		IsFirstUser:   false,
		UserId:        commentSingle.UserID,
		Like:          true,
		ContentType:   commentSingle.ContentType,
		Images:        createImage(images, commentSingle.ID),
		CommentAt:     atList,
		UserInfo:      userInfo,
	}
	return &v1.PostExpoInteractionReply{
		Info: resultComment,
		Type: 1,
	}, nil

}

// 获取图片
func createImage(images []*models.ExpoCommentImage, commentId string) []string {
	var result []string
	if images != nil {
		query := Where(images, func(image *models.ExpoCommentImage) bool {
			return image.CommentID == commentId
		})
		if len(query) > 0 {
			slices.SortFunc(query, func(i, j *models.ExpoCommentImage) int {
				return cmp.Compare(i.Sort, j.Sort)
			})
			result = lo.Map(query, func(image *models.ExpoCommentImage, i int) string {
				return commandmentUrl + image.ImageURL + commandmentTemplate
			})
		}
	}
	return result
}

func createUserInfo(userinfos map[string]*UserInfo, userid string) *v1.UserInfo {
	if userinfo, ok := userinfos[userid]; ok {
		return &v1.UserInfo{
			UserId:        userinfo.UserID,
			NickName:      userinfo.Nickname,
			AvatarAddress: userinfo.AvatarAddress,
			DarenIcon:     userinfo.DarenIcon,
			VipIcon:       userinfo.VipIcon,
			UserStatus:    userinfo.userStatus,
			IdentityIcon:  userinfo.UserIdentity,
		}
	} else {
		return nil
	}

}
func createSimpleUserInfo(userinfo []*usercenterv1.GetBasicUserInfoItem, userid string) *v1.UserInfo {
	query, isFind := lo.Find(userinfo, func(userinfo *usercenterv1.GetBasicUserInfoItem) bool {
		return userinfo.UserId == userid
	})
	if isFind {
		return &v1.UserInfo{
			UserId:   query.UserId,
			NickName: query.NickName,
		}
	}
	return nil

}
func getCommnentContent(trans []*models.ExpoCommentTran, preferredlanguageCode, languageCode, content, commentId string) (string, string) {
	var isTrans = !strings.Contains(preferredlanguageCode, strings.ToLower(languageCode))
	var tranContent = content //原文
	var originContent = ""
	if isTrans {
		tranSingle, isFind := lo.Find(trans, func(t *models.ExpoCommentTran) bool {
			return commentId == t.CommentID
		})
		if isFind {
			tranContent = tranSingle.Content
		}
		originContent = content
	}
	return tranContent, originContent
}
func CreateAtUser(atUsers []*models.ExpoCommentAt, commentId string) []*v1.CommentAt {
	var atList = make([]*v1.CommentAt, 0, 10)
	queryAt := Where(atUsers, func(t *models.ExpoCommentAt) bool {
		return commentId == t.CommentID
	})
	if queryAt != nil {
		for _, v := range queryAt {
			atList = append(atList, &v1.CommentAt{
				Name: v.Nickname,
				Id:   v.UserID,
				Type: 1,
			})
		}
	}
	return atList
}

// 评价列表
func (s *Service) ExpoInteraction(ctx context.Context, request *v1.ExpoInteractionRequest) (*v1.ExpoInteractionReply, error) {

	var (
		l                        = log.Context(ctx)
		languageCode, _          = icontext.LanguageCodeFrom(ctx)
		PreferredlanguageCode, _ = icontext.PreferredLanguageCodeFrom(ctx)
		userId, _                = icontext.UserIdFrom(ctx)
	)

	expoId, err := strconv.ParseInt(request.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	languageCode = strings.ToLower(languageCode)
	PreferredlanguageCode = strings.ToLower(PreferredlanguageCode)
	var total int64 = 0
	if request.Page == 1 {
		total, _ = s.comment.GetTotalCountByExpoId(ctx, expoId)
	}
	//获取列表
	list, err := s.comment.PageParentList(ctx, expoId, int(request.Page), int(request.Size), userId)

	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	resultList := make([]*v1.ExpoInteraction, 0, request.Size)

	if len(list) > 0 {
		expoId, err := strconv.ParseInt(request.ExpoId, 10, 64)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
		expoinfo, err := s.expo.Get(ctx, expoId)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
		if expoinfo == nil {
			return nil, innErr.WithMessage(innErr.ErrBadRequest, "展会不存在")
		}

		commentIds := lo.Map(list, func(item *models.ExpoComment, index int) string {
			return item.ID
		})
		userIds := lo.Map(list, func(item *models.ExpoComment, index int) string {
			return item.UserID
		})
		//回复
		replyList, _ := s.comment.PageListByRootId(ctx, commentIds, userId)
		if len(replyList) > 0 {
			for _, v := range replyList {
				commentIds = append(commentIds, v.ID)
				userIds = append(userIds, v.UserID)
			}
		}
		applyUserIds, err := s.participant.FindUserIdByUserIdsAndExpoId(ctx, userIds, expoId)

		userInfos, err := s.fetchUserInfoBatch(ctx, userIds)

		if err != nil {
			return nil, innErr.ErrBadRequest
		}
		//处理翻译
		var (
			trans, _ = s.commentTrans.GetListByCommentId(ctx, commentIds, languageCode)

			//处理图片
			images, _ = s.commentImg.GetListByCommentId(ctx, commentIds)

			//处理@
			atUsers, _ = s.commentAt.GetListByCommentId(ctx, commentIds)

			likeCommentIds = make([]*string, 0, 50)
		)
		replyCount, err := s.comment.GetTotalCountByRootIds(ctx, commentIds)

		if err != nil {
			l.Errorf("获取回复数量: %v", err)
			return nil, innErr.ErrBadRequest
		}

		// 处理点赞
		if len(userId) > 0 {
			likeCommentIds, _ = s.commentLike.GetListByCommentIds(ctx, commentIds, userId)
		}
		for _, v := range list {
			userinfo := createUserInfo(userInfos, v.UserID)
			if userinfo == nil {
				continue
			}
			//处理翻译
			tranContent, originContent := getCommnentContent(trans, PreferredlanguageCode, v.LanguageCode, v.Content, v.ID)
			//处理at
			var atList = CreateAtUser(atUsers, v.ID)
			//处理回复
			replyItem := make([]*v1.ExpoInteraction, 0, 4)
			replyListQuery := Where(replyList, func(t *models.ExpoComment) bool {
				return v.ID == t.RootID
			})
			if replyListQuery != nil {
				for _, singleReply := range replyListQuery {
					//用户处理
					replyUserInfo := createUserInfo(userInfos, singleReply.UserID)
					if replyUserInfo == nil {
						continue
					}
					//内容
					replyTranContent, replyOriginContent := getCommnentContent(trans, PreferredlanguageCode, singleReply.LanguageCode, singleReply.Content, singleReply.ID)
					//@处理
					isApply := false
					if len(applyUserIds) > 0 {
						isApply = lo.ContainsBy(applyUserIds, func(t *string) bool {

							return *t == singleReply.UserID
						})
					}
					var replyAtList = CreateAtUser(atUsers, singleReply.ID)
					replyItem = append(replyItem, &v1.ExpoInteraction{
						CommentId:     singleReply.ID,
						Content:       replyTranContent,
						OriginContent: replyOriginContent,
						ReplyName:     singleReply.ReplyNickName,
						Country:       "中国",
						Time:          strconv.FormatInt(singleReply.CreatedAt.Unix(), 10),
						IsFirstUser:   false,
						UserId:        singleReply.UserID,
						Like:          lo.ContainsBy(likeCommentIds, func(t *string) bool { return &singleReply.ID == t }),
						ContentType:   singleReply.ContentType,
						Images:        createImage(images, singleReply.ID),
						CommentAt:     replyAtList,
						UserInfo:      replyUserInfo,
						IsApply:       isApply,
					})
				}
			}
			var count int32 = 0
			replyInfo, isFind := lo.Find(replyCount, func(t *models.ExpoCommentView) bool {
				return v.ID == t.RootID
			})
			if isFind {
				count = replyInfo.Count
			}
			extras := make([]*v1.CommentExtra, 0, 2)
			if v.ContentType == int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_BIND) {
				originContent = ""
				extras = append(extras, &v1.CommentExtra{
					Type:    1,
					Content: "绑定实盘任务",
				})
				extras = append(extras, &v1.CommentExtra{
					Type:    2,
					Content: "自由聊天权限",
				})
				tranContent = v.Content
				v.ContentType = 2
			} else if v.ContentType == int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_FIXED) {
				v.ContentType = 1
			} else if v.ContentType == int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_BOOK) {
				extraContent := v.ContentDesc
				var extraMap map[string]string
				if len(extraContent) > 0 {
					fmt.Println(extraContent)
					err := json.Unmarshal([]byte(extraContent), &extraMap)
					if err != nil {
						l.Errorf("反序列化错误: %v", err)
					}
					if len(extraMap) == 2 {
						tranContent = v.Content
						extras = append(extras, &v1.CommentExtra{
							Type:    1,
							Content: extraMap["time"],
						})
						extras = append(extras, &v1.CommentExtra{
							Type:    2,
							Content: "小明",
							Extra:   "https://img.fx696.com/avatar/5490976320/5490976320_71794.jpg_wiki200",
							GuestId: extraMap["guest"],
						})
					}
				}
				v.ContentType = 3
				originContent = ""
			} else if v.ContentType == int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_REGISTRATION) { //报名通知
				originContent = ""
				tranContent = util.Format(v.Content, expoinfo.Name)
				tranContent = tranContent + "🎉🎉🎉"
				v.ContentType = 1
			} else {

			}
			isApply := false
			if len(applyUserIds) > 0 {

				isApply = lo.ContainsBy(applyUserIds, func(t *string) bool {
					return *t == v.UserID
				})
			}
			item := &v1.ExpoInteraction{
				CommentId:     v.ID,
				Content:       tranContent,
				OriginContent: originContent,
				Country:       "中国",
				Time:          strconv.FormatInt(v.CreatedAt.Unix(), 10),
				IsFirstUser:   false,
				UserId:        v.UserID,
				Like:          lo.ContainsBy(likeCommentIds, func(t *string) bool { return &v.ID == t }),
				ContentType:   v.ContentType,
				Images:        createImage(images, v.ID),
				CommentAt:     atList,
				UserInfo:      userinfo,
				Reply:         replyItem,
				ReplyCount:    count,
				CommentExtra:  extras,
				IsApply:       isApply,
			}
			resultList = append(resultList, item)
		}
	}
	return &v1.ExpoInteractionReply{
		Total:    total,
		Comments: resultList,
	}, nil
}

// ExpoInteractionLike 点赞
func (s *Service) ExpoInteractionLike(ctx context.Context, request *v1.ExpoInteractionLikeRequest) (*v1.ExpoInteractionLikeReply, error) {
	userId, _ := icontext.UserIdFrom(ctx)
	datetime := time.Now().UTC()
	comment, err := s.comment.Get(ctx, request.CommentId)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	if comment == nil {
		return nil, innErr.ErrBadRequest
	}
	likeQuery, err := s.commentLike.Get(ctx, request.CommentId, userId)

	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	if len(likeQuery) > 0 {
		err := s.commentLike.Delete(ctx, request.CommentId, userId)
		if err != nil {
			return nil, innErr.ErrBadRequest
		}
		return &v1.ExpoInteractionLikeReply{
			Status: false,
		}, nil
	} else {
		err := s.commentLike.Add(ctx, &models.ExpoCommentLike{
			CommentID: request.CommentId,
			UserID:    userId,
			CreatedAt: datetime,
			UpdatedAt: datetime,
		})

		if err != nil {
			return nil, innErr.ErrBadRequest
		}
		return &v1.ExpoInteractionLikeReply{
			Status: true,
		}, nil
	}
}

func (s *Service) ExpoInteractionLoadReplyList(ctx context.Context, request *v1.ExpoInteractionLoadReplyListRequest) (*v1.ExpoInteractionLoadReplyListReply, error) {
	var (
		l                        = log.Context(ctx)
		languageCode, _          = icontext.LanguageCodeFrom(ctx)
		PreferredlanguageCode, _ = icontext.PreferredLanguageCodeFrom(ctx)
		userId, _                = icontext.UserIdFrom(ctx)
	)
	languageCode = strings.ToLower(languageCode)
	PreferredlanguageCode = strings.ToLower(PreferredlanguageCode)
	resultList := make([]*v1.ExpoInteraction, 0, request.Size)
	list, err := s.comment.PageReplyPageList(ctx, request.CommentId, request.ExceptReplyIds, int(request.Page), int(request.Size), userId)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	if len(list) > 0 {
		commentIds := selectTNoWhere(list, func(v *models.ExpoComment) string {
			return v.ID
		})
		userIds := selectTNoWhere(list, func(v *models.ExpoComment) string {
			return v.UserID
		})
		trans, _ := s.commentTrans.GetListByCommentId(ctx, commentIds, languageCode)
		//处理图片
		images, _ := s.commentImg.GetListByCommentId(ctx, commentIds)
		//处理@
		atUsers, _ := s.commentAt.GetListByCommentId(ctx, commentIds)
		userInfos, err := s.fetchUserInfoBatch(ctx, userIds)

		if err != nil {
			l.Errorf("获取用户信息失败: %v", err)
			return nil, innErr.ErrBadRequest
		}
		likeCommentIds := make([]*string, 0, 50)
		// 处理点赞
		if len(userId) > 0 {
			likeCommentIds, _ = s.commentLike.GetListByCommentIds(ctx, commentIds, userId)
		}

		for _, singleReply := range list {
			//用户处理
			replyUserInfo := createUserInfo(userInfos, singleReply.UserID)
			if replyUserInfo == nil {
				continue
			}

			//内容
			replyTranContent, replyOriginContent := getCommnentContent(trans, PreferredlanguageCode, singleReply.LanguageCode, singleReply.Content, singleReply.ID)
			//@处理
			var replyAtList = CreateAtUser(atUsers, singleReply.ID)
			resultList = append(resultList, &v1.ExpoInteraction{
				CommentId:     singleReply.ID,
				Content:       replyTranContent,
				OriginContent: replyOriginContent,
				ReplyName:     singleReply.ReplyNickName,
				Country:       "中国",
				Time:          strconv.FormatInt(singleReply.CreatedAt.Unix(), 10),
				IsFirstUser:   false,
				UserId:        singleReply.UserID,
				Like:          lo.ContainsBy(likeCommentIds, func(t *string) bool { return &singleReply.ID == t }),
				ContentType:   singleReply.ContentType,
				Images:        createImage(images, singleReply.ID),
				CommentAt:     replyAtList,
				UserInfo:      replyUserInfo,
			})
		}
	}
	return &v1.ExpoInteractionLoadReplyListReply{
		Replys: resultList,
	}, nil
}

// ExpoInteractionCount 评论数量
func (s *Service) ExpoInteractionCount(ctx context.Context, in *v1.ExpoInteractionCountRequest) (*v1.ExpoInteractionCountReply, error) {
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}

	query, err := s.comment.GetTotalCountByExpoId(ctx, expoId)
	if err != nil {
		return nil, innErr.ErrBadRequest
	}
	return &v1.ExpoInteractionCountReply{
		Count: query,
	}, nil

}

// ExpoTopic 展会话题
func (s *Service) ExpoTopic(ctx context.Context, in *v1.ExpoTopicRequest) (*v1.ExpoTopicReply, error) {
	var (
		//l               = log.Context(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		//PreferredlanguageCode, _ = icontext.PreferredLanguageCodeFrom(ctx)
		userId, _ = icontext.UserIdFrom(ctx)
	)
	expoIdint64, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		log.Error("[ExpoTopic 展会id转int失败]" + err.Error())
		return nil, innErr.ErrBadRequest
	}
	expoInfo, err := s.expoCommunity.GetByExpoId(ctx, expoIdint64)
	if err != nil {
		log.Error("[GetByExpoId]" + err.Error())
		return nil, innErr.ErrBadRequest
	}
	if len(expoInfo.TopicId) == 0 {
		return &v1.ExpoTopicReply{
			List: []*v1.TopicPostsData{},
		}, nil
	}
	detail, err := s.community.GetTopicDetail(ctx, &communityv1.GetTopicDetailRequest{
		UserId:    userId,
		TopicId:   expoInfo.TopicId,
		PageIndex: in.Page,
		PageSize:  in.Size,
		Type:      2,
	})
	if err != nil {
		log.Error("[GetTopicDetail]" + err.Error())
		return nil, innErr.ErrBadRequest
	}
	lcount := len(detail.PostsCol)
	userIds := make([]string, 0, lcount)
	postIds := make([]string, 0, lcount)
	for _, v := range detail.PostsCol {
		userIds = append(userIds, v.UserId)
		postIds = append(postIds, v.PostsId)
	}
	users, err := userBind(ctx, s, userIds, userId, languageCode, []string{})
	cAMap, e := getCollectionApplaud(ctx, s, postIds)
	if e != nil {
		log.Error("[GetTopicDetail][s.community.GetCollectionApplaud] " + e.Error())
	}
	posts := make([]*v1.TopicPostsData, 0, lcount)
	for _, v := range detail.PostsCol {
		u := users[v.UserId]
		images := make([]*v1.Images, 0, len(v.Images))
		for _, vv := range v.Images {
			images = append(images, &v1.Images{
				List:   vv.List,
				Detail: vv.Detail,
				Height: vv.Height,
				Width:  vv.Width,
			})
		}
		sign := &v1.Sign{}
		if v.Sign != nil {
			sign = &v1.Sign{
				IsShow:  v.Sign.IsShow,
				BgColor: v.Sign.BgColor,
				Word:    v.Sign.Word,
				Icon:    v.Sign.Icon,
			}
		}
		applaud := false
		collect := false
		var applaudNumber int64 = 0
		if p, ok := cAMap[v.PostsId]; ok {
			applaud = p.IsApplaud
			applaudNumber = p.ApplaudNumber
			collect = p.IsCollect
		}
		p := &v1.PostsData{
			PostsId:         v.PostsId,
			Title:           v.Title,
			TitleNew:        v.TitleNew,
			Content:         v.Content,
			ShareUrl:        v.ShareUrl,
			ThemeCode:       v.ThemeCode,
			PublishTime:     v.PublicTime / 1000,
			Sign:            sign,
			Images:          images,
			ServiceTypeName: v.Theme,
			ServiceTypeCode: v.ThemeCode,
			CountryName:     v.GetCountryName(),
			Grade:           v.Grade,
			ContentLanguage: v.ContentLanguage,
			IsApplaud:       applaud,
			IsCollect:       collect,
			ApplaudNumber:   applaudNumber,
		}
		posts = append(posts, &v1.TopicPostsData{
			UserInfo:  u,
			PostsInfo: p,
			DataType:  3,
		})

	}
	return &v1.ExpoTopicReply{
		List: posts,
	}, nil
}

func userBind(ctx context.Context, s *Service, uIds []string, userLoginId, languageCode string, traderCodes []string) (map[string]*v1.UserData, error) {
	l := log.Context(ctx)
	if len(uIds) == 0 && len(traderCodes) == 0 {
		return map[string]*v1.UserData{}, nil
	}
	var (
		param = &usercenterv1.GetUsersRequest{
			UserLoginId:  userLoginId,
			UserIds:      uIds,
			LanguageCode: languageCode,
			TraderCodes:  traderCodes,
		}
		out = make(map[string]*v1.UserData)
		err error
	)
	resp := &usercenterv1.GetUsersReply{}
	resp, err = s.user.GetUsersInfo(ctx, param)
	if err != nil {
		return out, err
	}

	//resp, err := p.client.GetUsersInfo(ctx, param)
	data, _ := json.Marshal(param)
	respData, _ := json.Marshal(resp)
	l.Infof("[user][Bind]req: %v, resp: %v", string(data), string(respData))

	if resp == nil {
		err = errors.New(fmt.Sprintf("[p.client.Bind] resp is Nil, req is %s", string(data)))
		return out, err
	}
	if len(resp.Message) == 0 {
		err = errors.New(fmt.Sprintf("[p.client.Bind] resp.Items is Nil, req is %s", string(data)))
		return out, err
	}

	if len(resp.Message) != len(param.UserIds)+len(param.TraderCodes) {
		l.Infof("[user][Bind]reqs and resps count different, reqs:%d, resps:%d", len(param.UserIds)+len(param.TraderCodes), len(resp.Message))
	}
	for _, v := range resp.Message {
		if _, ok := out[v.OriginId]; !ok {
			var isSelf, isShowFollow bool
			var wikifxNumber = v.WikiFxNumber
			if wikifxNumber == "" { // 官方号、交易商号、服务商号 这个为空
				wikifxNumber = v.UserId
			}
			if v.AttentionStauts == 4 {
				isSelf = true
			}
			isShowFollow = !isSelf
			out[v.OriginId] = &v1.UserData{
				UserId:          v.UserId,
				NickName:        v.NickName,
				NickNameColor:   v.NickNameColor,
				AvatarAddress:   v.AvatarAddress,
				VipIcon:         v.EnterpriseVIcon2,
				DarenIcon:       v.DarenIcon,
				RightLabelType:  v.UserStatus,
				IsFollow:        v.IsFollow,
				IsSelf:          isSelf,
				IsShowFlow:      isShowFollow,
				Official:        v.Official,
				OfficialColor:   v.OfficialColor,
				FansCount:       int64(v.FansCount),
				TimeAfterLabel:  v.StaffTag,
				IdentityIcon:    v.UserIdentityNewIcon,
				RegisterLong:    v.RegisterLong,
				Position:        v.Position,
				EnterpriseType:  v.EnterpriseType,
				OfficialNumber:  v1.OfficialNumberType(v.OfficialNumberType),
				AvatarFrame:     v.AvatarFrame,
				WikiFxNumber:    wikifxNumber,
				AttentionStauts: v.AttentionStauts,
			}
		}
	}

	return out, nil
}

func getCollectionApplaud(ctx context.Context, s *Service, postsId []string) (out map[string]v1.PostsData, err error) {
	l := log.Context(ctx)
	out = make(map[string]v1.PostsData)
	var (
		loginUserId, _ = icontext.UserIdFrom(ctx)
		param          = &communityv1.GetPostsApplaudRequest{
			UserLoginId: loginUserId,
			PostsIds:    postsId,
		}
		applaudMapping = make(map[string]struct{})
		collectMapping = make(map[string]struct{})
		numberMapping  = make(map[string]*communityv1.GetPostsApplaudAndCollectReplyCount)
	)
	resp := &communityv1.GetPostsApplaudAndCollectReply{}
	resp, err = s.community.GetPostsApplaudAndCollect(ctx, param)

	//resp, err := p.client.GetPostsApplaudAndCollect(ctx, param)
	data, _ := json.Marshal(param)
	respData, _ := json.Marshal(resp)
	l.Infof("[community][GetCollectionApplaud]req: %v, resp: %v", string(data), string(respData))
	if err != nil {
		return out, err
	}
	if resp != nil {
		for _, v := range resp.Applauds {
			applaudMapping[v] = struct{}{}
		}
		for _, v := range resp.Collects {
			collectMapping[v] = struct{}{}
		}
		for _, v := range resp.ApplaudsCounts {
			numberMapping[v.PostsId] = v
		}
	}
	out = make(map[string]v1.PostsData, len(applaudMapping))
	for _, v := range postsId {
		var (
			isApplaud, isCollect bool
		)
		if _, ok := applaudMapping[v]; ok {
			isApplaud = true
		}
		if _, ok := collectMapping[v]; ok {
			isCollect = true
		}
		var applaudNumber int64 = 0
		var commentNumber int64 = 0
		var collectNumber int64 = 0
		var playTimes int64 = 0
		ShowPlayTimes := "0"
		showApplaudNumber := "0"
		showCommentNumber := "0"
		showCollectNumber := "0"
		isShowApplaudNumber := true
		isShowCommentNumber := true
		isShowCollectNumber := true
		IsShowPlayTimes := true
		if value, ok := numberMapping[v]; ok {
			applaudNumber = int64(value.ApplaudNumber)
			showApplaudNumber = value.ShowApplaudNumber
			commentNumber = int64(value.CommentNumber)
			showCommentNumber = value.ShowCommentNumber
			collectNumber = int64(value.CollectNumber)
			showCollectNumber = value.ShowCollectNumber
			isShowApplaudNumber = value.IsShowApplaudNumber
			isShowCommentNumber = value.IsShowCommentNumber
			isShowCollectNumber = value.IsShowCollectNumber
			IsShowPlayTimes = value.IsShowPlayTimes
			playTimes = int64(value.PlayTimes)
			ShowPlayTimes = value.ShowPlayTimes

		}
		out[v] =
			v1.PostsData{
				PostsId:             v,
				IsApplaud:           isApplaud,
				IsCollect:           isCollect,
				ApplaudNumber:       applaudNumber,
				ShowApplaudNumber:   showApplaudNumber,
				CommentNumber:       commentNumber,
				ShowCommentNumber:   showCommentNumber,
				CollectNumber:       collectNumber,
				ShowCollectNumber:   showCollectNumber,
				IsShowApplaudNumber: isShowApplaudNumber,
				IsShowCommentNumber: isShowCommentNumber,
				IsShowCollectNumber: isShowCollectNumber,
				IsShowPlayTimes:     IsShowPlayTimes,
				ShowPlayTimes:       ShowPlayTimes,
				PlayTimes:           playTimes,
			}
	}

	return out, err
}

// 是否弹出极验
func isShowCommentGeetTest(s *Service, ctx context.Context, userId string) bool {
	redisKey := rediskey_comment_publish + userId
	redisKeyVerify := rediskey_comment_publish_verify + userId
	now := time.Now().UTC()
	if s.redisCli.Exists(ctx, redisKeyVerify).Val() > 0 {
		if s.redisCli.Get(ctx, redisKeyVerify).Val() == "0" {
			return false
		} else {
			s.redisCli.Del(ctx, redisKeyVerify)
			return true
		}
	} else {
		if s.redisCli.Exists(ctx, redisKey).Val() > 0 {
			redisValue := s.redisCli.SMembers(ctx, redisKey).Val()
			timeMinute := now.Add(-time.Minute * 10)
			query := lo.Filter(redisValue, func(item string, index int) bool {
				t, _ := time.Parse(time.DateTime, item)
				return t.After(timeMinute)
			})
			fmt.Println(len(query))
			if len(query) >= 5 {
				s.redisCli.Set(ctx, redisKeyVerify, "0", 10*time.Minute)
				return false
			} else {
				s.redisCli.SAdd(ctx, redisKey, now.Format(time.DateTime))
				return true
			}
		} else {
			s.redisCli.SAdd(ctx, redisKey, now.Format(time.DateTime))
			s.redisCli.Expire(ctx, redisKey, 24*time.Hour)
			return true
		}
	}

}
func (s *Service) ExpoInteractionGeetTest(ctx context.Context, request *v1.ExpoInteractionGeetTestRequest) (*v1.ExpoInteractionGeetTestReply, error) {
	geetest, err := s.upstream.GeetTestVerify(ctx, request.LotNumber, request.CaptchaOutput, request.PassToken, request.GenTime)
	userId, _ := icontext.UserIdFrom(ctx)
	l := log.Context(ctx)
	redisKeyVerify := rediskey_comment_publish_verify + userId
	redisKey := rediskey_comment_publish + userId
	if err != nil {
		l.Error("极验验证失败" + err.Error())
		return nil, err
	}
	if !geetest {
		return &v1.ExpoInteractionGeetTestReply{
			Success: false,
		}, nil
	}
	if s.redisCli.Exists(ctx, redisKeyVerify).Val() > 0 {
		s.redisCli.Set(ctx, redisKeyVerify, "1", 10*time.Minute)
		s.redisCli.Del(ctx, redisKey)
		s.redisCli.SAdd(ctx, redisKey, time.Now().UTC().Format(time.DateTime))
	}
	return &v1.ExpoInteractionGeetTestReply{
		Success: true,
	}, nil
}

// ExpoInteractionPreview 展会互动预告语
func (s *Service) ExpoInteractionPreview(ctx context.Context, in *common.EmptyRequest) (*v1.ExpoInteractionPreviewReply, error) {
	return &v1.ExpoInteractionPreviewReply{
		Previews: []string{
			"为展会加油！🎉🎉🎉",
			"期待展会成功举办",
			"WikiEXPO HongKong",
		},
	}, nil
}

// 绑定实盘插入评论
func (s *Service) BindShiPanComment(ctx context.Context, userid string, expoId int64) error {
	userinfo, err := s.user.GetBasicUserInfo(ctx, &usercenterv1.GetUserWikiNumbersRequest{
		UserIds: []string{userid},
	})
	if err != nil {
		return err
	}
	nickName := ""
	if len(userinfo.List) > 0 {
		nickName = userinfo.List[0].NickName
	}
	now := time.Now().UTC()

	comment := &models.ExpoComment{
		ID:            id.CreatePrimaryId("COM"),
		ExpoID:        expoId,
		UserID:        userid,
		Content:       ShiPanContent,
		ContentType:   int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_BIND),
		Status:        2,
		ContentDesc:   "1,2",
		RootID:        "",
		ParentID:      "",
		LanguageCode:  "zh-cn",
		CreatedAt:     now,
		UpdatedAt:     now,
		LikeCount:     0,
		NickName:      nickName,
		ReplyNickName: "",
	}
	err = s.comment.Add(ctx, comment)
	if err != nil {
		return err
	}
	return nil
}

// 预约评价
func (s *Service) BookComment(ctx context.Context, userid string, expoId int64, bookTime, guestId string) error {
	userinfo, err := s.user.GetBasicUserInfo(ctx, &usercenterv1.GetUserWikiNumbersRequest{
		UserIds: []string{userid},
	})
	if err != nil {
		return err
	}
	nickName := ""
	if len(userinfo.List) > 0 {
		nickName = userinfo.List[0].NickName
	}
	now := time.Now().UTC()
	contentDesc := map[string]string{
		"time":  bookTime,
		"guest": guestId,
	}
	jsonData, err := json.Marshal(contentDesc)
	comment := &models.ExpoComment{
		ID:            id.CreatePrimaryId("COM"),
		ExpoID:        expoId,
		UserID:        userid,
		Content:       BookContent,
		ContentType:   int32(v1.CommentContentType_COMMENT_CONTENT_TYPE_BOOK),
		Status:        2,
		ContentDesc:   string(jsonData),
		RootID:        "",
		ParentID:      "",
		LanguageCode:  "zh-cn",
		CreatedAt:     now,
		UpdatedAt:     now,
		LikeCount:     0,
		NickName:      nickName,
		ReplyNickName: "",
	}
	err = s.comment.Add(ctx, comment)
	if err != nil {
		return err
	}
	return nil
}
