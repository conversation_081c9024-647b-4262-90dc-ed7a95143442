package service

import (
	"context"
	"crypto/md5"
	"encoding/json"
	innerr "errors"
	"fmt"
	"strconv"
	"time"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/alarm"
	"github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
)

// FaceSearch 用户人脸搜索接口
func (s *Service) FaceSearch(ctx context.Context, req *v1.FaceSearchRequest) (*v1.FaceSearchReply, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "user not logged in")
	}

	// 参数校验
	if req == nil {
		return nil, errors.WithMessage(errors.ErrBadRequest, "request parameters cannot be empty")
	}
	if req.SearchPhotoUrl == "" {
		return nil, errors.WithMessage(errors.ErrBadRequest, "search photo URL cannot be empty")
	}

	expoId, err := strconv.ParseInt(req.ExpoId, 10, 64)
	if err != nil {
		return nil, errors.ErrBadRequest
	}
	pageSize := 100

	var (
		l = log.Context(ctx)

		faceSearchReply = &v1.FaceSearchReply{
			SearchId:           "",
			TotalSearchedFaces: 0,
			SearchDurationMs:   0,
			Results:            []*v1.FaceSearchResultItem{},
			TotalResults:       0,
			Page:               1,
			PageSize:           int32(pageSize),
			FromCache:          false,
			ImageHeight:        0,
			ImageWidth:         0,
			SearchFaceRect:     []*v1.FaceRect{},
		}
	)

	groupIds, err := s.faceGroupExpo.GetGroupIDsByExpo(ctx, expoId)
	if err != nil {
		l.Warnf("获取人脸组失败: user_id=%s, expo_id=%d, error=%v", userID, req.ExpoId, err)
		return faceSearchReply, nil
	}

	// 检查是否有有效的人脸组
	if len(groupIds) == 0 {
		l.Warnf("用户人脸搜索失败: user_id=%s, expo_id=%d, 没有可用的人员库", userID, req.ExpoId)
		// 返回空结果而不是错误
		return faceSearchReply, nil
	}

	l.Infof("用户人脸搜索: user_id=%s, photo_url=%s, group_ids=%v, page=%d, page_size=%d",
		userID, req.SearchPhotoUrl, groupIds, 1, pageSize)

	// 构建业务请求参数
	bizReq := &models.FaceSearchRequest{
		SearchPhotoURL: req.SearchPhotoUrl,
		GroupIDs:       groupIds,
		NeedFaceRect:   true,
		Page:           1,
		PageSize:       pageSize,
	}

	result, err := s.SearchFacePhotos(ctx, bizReq)
	if err != nil {
		l.Errorf("人脸搜索失败: %v", err)
		return nil, err
	}

	results := make([]*v1.FaceSearchResultItem, 0, len(result.Results))
	for _, item := range result.Results {
		resultItem := &v1.FaceSearchResultItem{
			PhotoUrl:          urlformat.FullPath(item.PhotoURL, urlTemplate),
			PhotoThumbnailUrl: urlformat.FullPath(item.PhotoURL, faceUrlTemplate),
			MaxSimilarity:     float32(item.MaxSimilarity),
		}

		results = append(results, resultItem)
	}

	// 构建响应
	reply := &v1.FaceSearchReply{
		SearchId:           result.SearchID,
		TotalSearchedFaces: int32(result.TotalSearchedFaces),
		SearchDurationMs:   result.SearchDurationMs,
		Results:            results,
		TotalResults:       int32(result.TotalResults),
		Page:               int32(result.Page),
		PageSize:           int32(result.PageSize),
		FromCache:          result.FromCache,
		ImageHeight:        int32(result.ImageHeight),
		ImageWidth:         int32(result.ImageWidth),
	}

	// 如果需要返回人脸坐标，设置搜索图片中所有检测到的人脸坐标
	if len(result.SearchFaceRect) > 0 {
		searchFaceRects := make([]*v1.FaceRect, 0, len(result.SearchFaceRect))
		for _, rect := range result.SearchFaceRect {
			searchFaceRects = append(searchFaceRects, &v1.FaceRect{
				X:      int32(rect.X),
				Y:      int32(rect.Y),
				Width:  int32(rect.Width),
				Height: int32(rect.Height),
			})
		}
		reply.SearchFaceRect = searchFaceRects
	}

	return reply, nil
}

// CheckFaceSearchRateLimit 检查人脸搜索频率限制
func (s *Service) CheckFaceSearchRateLimit(ctx context.Context, userID, clientIP string) error {
	var (
		l   = log.Context(ctx)
		now = time.Now()
	)

	// 检查用户频率限制
	if err := s.checkUserRateLimit(ctx, userID, now); err != nil {
		l.Warnf("用户频率限制: user_id=%s, error=%v", userID, err)
		return err
	}

	// 检查IP频率限制
	if err := s.checkIPRateLimit(ctx, clientIP, now); err != nil {
		l.Warnf("IP频率限制: client_ip=%s, error=%v", clientIP, err)
		return err
	}

	return nil
}

// checkUserRateLimit 检查用户频率限制
func (s *Service) checkUserRateLimit(ctx context.Context, userID string, now time.Time) error {
	config := s.business.TencentAI.RateLimitConfig

	// 检查每分钟限制
	minuteKey := fmt.Sprintf("%s%s:%s", faceSearchUserLimitPrefix, userID, now.Format("2006-01-02:15:04"))
	minuteCount, err := s.redisCli.Incr(ctx, minuteKey).Result()
	if err != nil {
		return errors.WithMessage(errors.ErrBadRequest, "operation too frequent, please try again later")
	}

	// 设置过期时间
	if minuteCount == 1 {
		s.redisCli.Expire(ctx, minuteKey, time.Minute)
	}

	// 检查每分钟限制
	if minuteCount > int64(config.UserLimitPerMin) {
		// 触发报警
		s.triggerFaceSearchAlert(ctx, "用户每分钟限制", userID, minuteCount, int64(config.UserLimitPerMin))
		return errors.WithMessage(errors.ErrBadRequest, "operation too frequent, please try again later")
	}

	// 检查每日限制
	dayKey := fmt.Sprintf("%s%s:%s", faceSearchUserLimitPrefix, userID, now.Format("2006-01-02"))
	dayCount, err := s.redisCli.Incr(ctx, dayKey).Result()
	if err != nil {
		return errors.WithMessage(errors.ErrBadRequest, "operation too frequent, please try again later")
	}

	// 设置过期时间
	if dayCount == 1 {
		s.redisCli.Expire(ctx, dayKey, 24*time.Hour)
	}

	// 检查每日限制
	if dayCount > int64(config.UserLimitPerDay) {
		// 触发报警
		s.triggerFaceSearchAlert(ctx, "用户每日限制", userID, dayCount, int64(config.UserLimitPerDay))
		return errors.WithMessage(errors.ErrBadRequest, "daily search limit exceeded")
	}

	// 检查是否达到报警阈值
	s.checkFaceSearchAlertThreshold(ctx, "用户每分钟", userID, minuteCount, int64(config.UserLimitPerMin))
	s.checkFaceSearchAlertThreshold(ctx, "用户每日", userID, dayCount, int64(config.UserLimitPerDay))

	return nil
}

// checkIPRateLimit 检查IP频率限制
func (s *Service) checkIPRateLimit(ctx context.Context, clientIP string, now time.Time) error {
	config := s.business.TencentAI.RateLimitConfig

	// 检查每分钟限制
	minuteKey := fmt.Sprintf("%s%s:%s", faceSearchIPLimitPrefix, clientIP, now.Format("2006-01-02:15:04"))
	minuteCount, err := s.redisCli.Incr(ctx, minuteKey).Result()
	if err != nil {
		return errors.WithMessage(errors.ErrBadRequest, "network access too frequent, please try again later")
	}

	// 设置过期时间
	if minuteCount == 1 {
		s.redisCli.Expire(ctx, minuteKey, time.Minute)
	}

	// 检查每分钟限制
	if minuteCount > int64(config.IPLimitPerMin) {
		// 触发报警
		s.triggerFaceSearchAlert(ctx, "IP每分钟限制", clientIP, minuteCount, int64(config.IPLimitPerMin))
		return errors.WithMessage(errors.ErrBadRequest, "network access too frequent, please try again later")
	}

	// 检查是否达到报警阈值
	s.checkFaceSearchAlertThreshold(ctx, "IP每分钟", clientIP, minuteCount, int64(config.IPLimitPerMin))

	return nil
}

// GetFaceSearchCachedResult 获取人脸搜索缓存结果
func (s *Service) GetFaceSearchCachedResult(ctx context.Context, photoURL string) (*models.FaceSearchResult, error) {
	var (
		l = log.Context(ctx)
	)

	cacheKey := s.generateFaceSearchCacheKey(photoURL)

	data, err := s.redisCli.Get(ctx, cacheKey).Result()
	if err != nil {
		// 如果是redis.Nil错误，表示缓存不存在，这是正常情况
		if innerr.Is(err, redis.Nil) {
			l.Debugf("人脸搜索缓存未命中: cache_key=%s", cacheKey)
			return nil, nil // 返回nil而不是错误
		}
		l.Errorf("获取人脸搜索缓存失败: %v", err)
		return nil, err
	}

	var result models.FaceSearchResult
	if err := json.Unmarshal([]byte(data), &result); err != nil {
		l.Errorf("反序列化缓存数据失败: %v", err)
		s.redisCli.Del(ctx, cacheKey)
		return nil, err
	}

	l.Debugf("人脸搜索缓存命中: cache_key=%s", cacheKey)
	return &result, nil
}

// SetFaceSearchCachedResult 设置人脸搜索缓存结果
func (s *Service) SetFaceSearchCachedResult(ctx context.Context, photoURL string, result *models.FaceSearchResult) error {
	cacheKey := s.generateFaceSearchCacheKey(photoURL)

	data, err := json.Marshal(result)
	if err != nil {
		return err
	}

	cacheTTL := s.business.TencentAI.CacheConfig.TTL
	return s.redisCli.Set(ctx, cacheKey, string(data), time.Duration(cacheTTL)*time.Second).Err()
}

// generateFaceSearchCacheKey 生成人脸搜索缓存键
func (s *Service) generateFaceSearchCacheKey(photoURL string) string {
	hash := md5.Sum([]byte(photoURL))
	return fmt.Sprintf("%s%x", faceSearchCachePrefix, hash)
}

// checkFaceSearchAlertThreshold 检查人脸搜索报警阈值
func (s *Service) checkFaceSearchAlertThreshold(ctx context.Context, limitType, identifier string, current, limit int64) {
	config := s.business.TencentAI.RateLimitConfig
	threshold := float64(current) / float64(limit) * 100
	if threshold >= float64(config.AlertThreshold) {
		// 检查是否已经报警过（防止重复报警）
		alertKey := fmt.Sprintf("%salert:%s:%s:%s", faceSearchAlertPrefix, limitType, identifier, time.Now().Format("2006-01-02:15:04"))
		exists, err := s.redisCli.Exists(ctx, alertKey).Result()
		if err != nil || exists > 0 {
			return
		}

		// 设置报警标记（5分钟内不重复报警）
		s.redisCli.Set(ctx, alertKey, "1", 5*time.Minute)

		// 触发报警
		s.triggerFaceSearchAlert(ctx, fmt.Sprintf("%s阈值警告", limitType), identifier, current, limit)
	}
}

// triggerFaceSearchAlert 触发人脸搜索报警
func (s *Service) triggerFaceSearchAlert(ctx context.Context, alertType, identifier string, current, limit int64) {
	reqID, _ := icontext.RequestIdFrom(ctx)

	message := fmt.Sprintf("人脸搜索%s - 标识符: %s, 当前次数: %d, 限制次数: %d, 使用率: %.1f%%",
		alertType, identifier, current, limit, float64(current)/float64(limit)*100)

	alarm.FeiShuAlarm(reqID, message)
}

// paginateFaceSearchResult 对人脸搜索结果进行分页处理
func (s *Service) paginateFaceSearchResult(result *models.FaceSearchResult, page, pageSize int) *models.FaceSearchResult {
	if result == nil || len(result.Results) == 0 {
		return result
	}

	// 设置默认值
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大每页数量
	}

	totalResults := len(result.Results)

	// 计算分页
	startIndex := (page - 1) * pageSize
	endIndex := startIndex + pageSize

	// 检查边界
	if startIndex >= totalResults {
		// 超出范围，返回空结果
		paginatedResult := *result // 复制原始结果
		paginatedResult.Results = []*models.FaceSearchResultItem{}
		paginatedResult.TotalResults = totalResults
		paginatedResult.Page = page
		paginatedResult.PageSize = pageSize
		return &paginatedResult
	}

	if endIndex > totalResults {
		endIndex = totalResults
	}

	// 创建分页结果
	paginatedResult := *result // 复制原始结果
	paginatedResult.Results = result.Results[startIndex:endIndex]
	paginatedResult.TotalResults = totalResults
	paginatedResult.Page = page
	paginatedResult.PageSize = pageSize

	return &paginatedResult
}

// CheckFaceSearchLimit 检查用户人脸搜索限制状态
func (s *Service) CheckFaceSearchLimit(ctx context.Context, _ *v1.CheckFaceSearchLimitRequest) (*v1.CheckFaceSearchLimitReply, error) {
	// 用户身份校验
	userID, can := icontext.UserIdFrom(ctx)
	if !can {
		return nil, errors.WithMessage(errors.ErrAccessTokenExpired, "用户未登录")
	}

	config := s.business.TencentAI.RateLimitConfig
	now := time.Now()
	minuteKey, dayKey := s.generateUserLimitKeys(userID, now)

	// 批量获取Redis数据
	pipe := s.redisCli.Pipeline()
	minuteCmd := pipe.Get(ctx, minuteKey)
	dayCmd := pipe.Get(ctx, dayKey)
	_, _ = pipe.Exec(ctx)

	// 处理分钟级使用量
	minuteUsed := 0
	if minuteCmd.Err() == nil {
		minuteUsed, _ = minuteCmd.Int()
	}

	// 处理日级使用量
	dayUsed := 0
	if dayCmd.Err() == nil {
		dayUsed, _ = dayCmd.Int()
	}

	// 计算是否超限
	minuteExceeded := minuteUsed >= config.UserLimitPerMin
	dayExceeded := dayUsed >= config.UserLimitPerDay
	overLimit := minuteExceeded || dayExceeded

	return &v1.CheckFaceSearchLimitReply{
		OverLimit: overLimit,
	}, nil
}

// VerifyGeetestAndClearLimit 极验验证并清空人脸搜索限制
func (s *Service) VerifyGeetestAndClearLimit(ctx context.Context, req *v1.VerifyGeetestRequest) (*v1.VerifyGeetestReply, error) {
	// 参数校验
	if req == nil {
		return &v1.VerifyGeetestReply{
			Success: false,
			Message: "请求参数不能为空",
		}, nil
	}

	// 将 proto 请求转换为内部模型
	geetestReq := &models.GeetestValidationRequest{
		LotNumber:     req.LotNumber,
		CaptchaOutput: req.CaptchaOutput,
		PassToken:     req.PassToken,
		GenTime:       req.GenTime,
	}

	// 调用内部验证方法
	success, err := s.verifyGeetestAndClearLimitInternal(ctx, geetestReq)
	if err != nil {
		return &v1.VerifyGeetestReply{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	if success {
		return &v1.VerifyGeetestReply{
			Success: true,
			Message: "验证成功，已清空人脸搜索限制",
		}, nil
	} else {
		return &v1.VerifyGeetestReply{
			Success: false,
			Message: "验证失败，请重新验证",
		}, nil
	}
}
