package service

import (
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"
	"context"
	"strconv"
	"strings"

	"github.com/airunny/wiki-go-tools/urlformat"
	"github.com/go-kratos/kratos/v2/log"
)

func (s *Service) GetExpoByIdsKeyword(ctx context.Context, in *v1.GetExpoByIdsKeywordRequest) (*v1.GetExpoByIdsKeywordReplyList, error) {
	var expoIds []int64
	if len(in.ExpoIds) > 0 {
		strList := strings.Split(in.ExpoIds, ",")
		int64Slice := make([]int64, 0, len(strList))
		for _, s := range strList {
			// 去除可能的空格
			s = strings.TrimSpace(s)
			if s == "" {
				continue
			}
			num, err := strconv.ParseInt(s, 10, 64)
			if err != nil {
				return nil, err
			}
			int64Slice = append(int64Slice, num)
		}
		expoIds = int64Slice
	}
	expos, err := s.expo.FindByIdsKeyword(ctx, expoIds, in.Keyword)
	if err != nil {
		return nil, err
	}
	if len(expos) > 0 {
		expoList := make([]*v1.GetExpoByIdsKeywordReply, len(expos))
		for i, expo := range expos {
			expoList[i] = &v1.GetExpoByIdsKeywordReply{
				ExpoId:   strconv.FormatInt(expo.ID, 10),
				ExpoName: expo.Name,
			}
		}
		return &v1.GetExpoByIdsKeywordReplyList{Items: expoList}, nil
	}
	return &v1.GetExpoByIdsKeywordReplyList{}, nil
}

func (s *Service) GetExpoDetailById(ctx context.Context, in *v1.GetExpoDetailByIdRequest) (*v1.GetExpoDetailByIdReply, error) {
	expoId, err := strconv.ParseInt(in.ExpoId, 10, 64)
	if err != nil {
		return nil, err
	}
	expo, expoErr := s.expo.FindById(ctx, expoId)
	var (
		status = v1.ExpoStatus_ExpoStatus_UNKNOWN
		logo   string
		l      = log.Context(ctx)
		now    = expo.GetNow()
		desc   string
	)
	if expoErr != nil {
		return nil, expoErr
	}
	if expo.Extra != nil {
		lang, ok := expo.Extra.V.Languages[in.LanguageCode]
		if !ok {
			lang, ok = expo.Extra.V.Languages["en"]
		}

		if ok {
			logo = urlformat.FullPath(lang.Logo, urlTemplate)
			desc = lang.Description
		}
	}
	var (
		mainLiveReview      string
		mainLiveReviewName  string
		mainLiveReviewCover string
		livesReviews        []*v1.ExpoVideoItem
		highlights          []string
		highlightCount      int64
	)
	if now.After(expo.End) {
		status = v1.ExpoStatus_ExpoStatus_END
	} else if now.After(expo.Start) {
		status = v1.ExpoStatus_ExpoStatus_PROCESSING
	} else {
		status = v1.ExpoStatus_ExpoStatus_NOT_START
	}
	if status == v1.ExpoStatus_ExpoStatus_END {
		var reviews []*models.ExpoReview
		reviews, err = s.expoReview.FindByExpoId(ctx, expoId)
		if err != nil {
			l.Errorf("expoReview.FindByExpoId Err:%v", err)
			// 这里发生错误不影响主流程
		}

		for _, review := range reviews {
			var (
				imageUrls  []string
				reviewDesc string
			)

			if review.Extra != nil && review.Extra.V != nil {
				imageUrls = review.Extra.V.Images
				lang, ok := review.Extra.V.Languages[in.LanguageCode]
				if !ok {
					lang, ok = review.Extra.V.Languages["en"]
				}
				if ok {
					reviewDesc = lang.Description
				}
			}

			if review.Type == v1.ExpoReviewType_REVIEW_TYPE_VIDEO {
				if mainLiveReview == "" {
					mainLiveReview = review.URL
					mainLiveReviewName = reviewDesc
					mainLiveReviewCover = urlformat.FullPath(review.Cover, urlTemplate)
				}

				livesReviews = append(livesReviews, &v1.ExpoVideoItem{
					Url:   review.URL,
					Name:  reviewDesc,
					Cover: urlformat.FullPath(review.Cover, urlTemplate),
				})
				continue
			}

			highlightCount += int64(len(imageUrls))
			for _, image := range imageUrls {
				highlights = append(highlights, urlformat.FullPath(image, urlTemplate))
				if len(highlights) > 9 {
					break
				}
			}
		}
		return &v1.GetExpoDetailByIdReply{
			ExpoId:         strconv.FormatInt(expo.ID, 10),
			Name:           expo.Name,
			Logo:           logo,
			CountryCode:    expo.CountryCode,
			StartTime:      expo.Start.Unix(),
			EndTime:        expo.End.Unix(),
			Wonderfulatlas: highlights,
			Description:    desc,
			Videos: &v1.ExpoVideo{
				Url:   mainLiveReview,
				Name:  mainLiveReviewName,
				Cover: mainLiveReviewCover,
				Items: livesReviews,
			},
		}, nil
	} else {
		return &v1.GetExpoDetailByIdReply{
			ExpoId:      strconv.FormatInt(expo.ID, 10),
			Name:        expo.Name,
			Description: desc,
			Logo:        logo,
			CountryCode: expo.CountryCode,
			StartTime:   expo.Start.Unix(),
			EndTime:     expo.End.Unix(),
		}, nil
	}
}
