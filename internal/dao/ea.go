package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
)

type EA struct {
	*DBModel
}

func NewEA(db EaDB) *EA {
	return &EA{
		DBModel: NewDBModel(db),
	}
}

func (s *EA) GetEAInfo(ctx context.Context, id int, opts ...igorm.Option) (*models.EAInfo, error) {
	var out models.EAInfo
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *EA) GetEAType(ctx context.Context, id int, opts ...igorm.Option) (*models.EAType, error) {
	var out models.EAType
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *EA) GetEAOrderIds(ctx context.Context, opts ...igorm.Option) ([]string, error) {
	var out []string
	err := s.session(ctx, opts...).
		Table("eaorders").
		Select("order_id").
		Where("trade_status = ?", "TRADE_SUCCESS").
		Pluck("order_id", &out).Error
	return out, ormhelper.WrapErr(err)
}
