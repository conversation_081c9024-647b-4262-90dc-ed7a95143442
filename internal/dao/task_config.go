package dao

import (
	"context"
	"errors"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gold_store/internal/models"

	"gorm.io/gorm"
)

type TaskConfig struct {
	db *gorm.DB
}

// NewTaskConfig 创建任务配置DAO实例
func NewTaskConfig(db *gorm.DB) *TaskConfig {
	return &TaskConfig{
		db: db,
	}
}

// Create 创建任务配置
func (t *TaskConfig) Create(ctx context.Context, taskConfig *models.TaskConfig) error {
	return t.db.WithContext(ctx).Create(taskConfig).Error
}

// Update 更新任务配置
func (t *TaskConfig) Update(ctx context.Context, taskConfig *models.TaskConfig) error {
	return t.db.WithContext(ctx).Save(taskConfig).Error
}

// UpdateStatus 更新任务状态
func (t *TaskConfig) UpdateStatus(ctx context.Context, id uint, status int8, modifier string) error {
	return t.db.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("id = ?", id).
		Update("status", status).
		Update("modifier", modifier). // 更新最后修改人
		Error
}

// Delete 删除任务配置(软删除，将状态设置为关闭)
func (t *TaskConfig) Delete(ctx context.Context, id uint, modifier string) error {
	return t.db.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("id = ?", id).
		Update("status", models.TaskStatusEnumClose). // 将状态设置为关闭
		Update("modifier", modifier).                 // 更新最后修改人
		Error
}

// GetByID 根据ID获取任务配置
func (t *TaskConfig) GetByID(ctx context.Context, id uint) (*models.TaskConfig, error) {
	var taskConfig models.TaskConfig
	err := t.db.WithContext(ctx).First(&taskConfig, id).Error
	if err != nil {
		return nil, err
	}
	return &taskConfig, nil
}

// GetTaskConfigByID 根据任务ID获取任务配置
func (t *TaskConfig) GetTaskConfigByID(ctx context.Context, id int64) (*models.TaskConfig, error) {
	var taskConfig models.TaskConfig
	err := t.db.WithContext(ctx).Where("id = ?", id).First(&taskConfig).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &taskConfig, nil
}

// List 获取任务配置列表
func (t *TaskConfig) List(ctx context.Context, params *TaskConfigQueryParams) ([]*models.TaskConfig, int64, error) {
	var taskConfigs []*models.TaskConfig
	var count int64

	query := t.db.WithContext(ctx).Model(&models.TaskConfig{})

	// 添加查询条件
	if params != nil {
		if params.Status >= 0 && params.Status <= 2 { // 0=关闭, 1=开启, 2=已完成
			query = query.Where("status = ?", params.Status)
		}
		if params.Status == -1 {
			query = query.Where("status != ?", models.TaskStatusEnumClose) // -1表示查询所有非关闭状态
		}
		if params.TaskType > 0 {
			query = query.Where("task_type = ?", params.TaskType)
		}
		if params.ShowProject != "" {
			query = query.Where("show_project = ?", params.ShowProject)
		}
		if params.RewardType > 0 {
			query = query.Where("reward_type = ?", params.RewardType)
		}
	}

	// 计算总数
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if params != nil && params.PageSize > 0 {
		offset := (params.PageNum - 1) * params.PageSize
		query = query.Offset(int(offset)).Limit(int(params.PageSize))
	}

	// 排序
	query = query.Order("sort_order DESC, id DESC")

	// 执行查询
	err = query.Find(&taskConfigs).Error
	if err != nil {
		return nil, 0, err
	}

	return taskConfigs, count, nil
}

// ListByIDs 根据ID列表获取任务配置
func (t *TaskConfig) ListByIDs(ctx context.Context, ids []uint) ([]*models.TaskConfig, error) {
	if len(ids) == 0 {
		return []*models.TaskConfig{}, nil
	}

	var taskConfigs []*models.TaskConfig
	err := t.db.WithContext(ctx).
		Where("id IN ?", ids).
		Find(&taskConfigs).Error
	if err != nil {
		return nil, err
	}

	return taskConfigs, nil
}

// ListByTaskType 根据任务类型获取任务配置
func (t *TaskConfig) ListByTaskType(ctx context.Context, taskType models.TaskType) ([]*models.TaskConfig, error) {
	var taskConfigs []*models.TaskConfig
	err := t.db.WithContext(ctx).
		Where("task_type = ? AND status = 1", taskType).
		Order("sort_order DESC, id DESC").
		Find(&taskConfigs).Error
	if err != nil {
		return nil, err
	}

	return taskConfigs, nil
}

// ListActive 获取有效的任务配置
func (t *TaskConfig) ListActive(ctx context.Context, params *TaskConfigActiveParams) ([]*models.TaskConfig, error) {
	var taskConfigs []*models.TaskConfig

	query := t.db.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("status = ?", models.TaskStatusEnumOngoing) // 1=开启
	// 添加查询条件
	if params != nil {
		if params.TaskType > 0 {
			query = query.Where("task_type = ?", params.TaskType)
		}
		//todo 这些条件先不管
		//if params.ShowProject != "" {
		//	query = query.Where("show_project = ?", params.ShowProject)
		//}
		if params.MinVersion != "" {
			query = query.Where("min_version <= ? OR min_version IS NULL", params.MinVersion)
		}
		//if params.VisibleUsers != "" {
		//	query = query.Where("visible_users = ? OR visible_users = '全部用户' OR visible_users IS NULL", params.VisibleUsers)
		//}
	}

	// 排序
	query = query.Order("sort_order DESC, id DESC")

	// 执行查询
	err := query.Find(&taskConfigs).Error
	if err != nil {
		return nil, err
	}

	return taskConfigs, nil
}

// UpdateSortOrder 更新任务排序值
func (t *TaskConfig) UpdateSortOrder(ctx context.Context, id uint, sortOrder int) error {
	return t.db.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("id = ?", id).
		Update("sort_order", sortOrder).
		Error
}

// BatchUpdate 批量更新任务配置
func (t *TaskConfig) BatchUpdate(ctx context.Context, taskConfigs []*models.TaskConfig) error {
	if len(taskConfigs) == 0 {
		return nil
	}

	return t.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, config := range taskConfigs {
			if err := tx.Save(config).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetTaskConfigsByEnumCode 根据枚举代码获取相关任务配置（返回单个配置，向后兼容）
func (t *TaskConfig) GetTaskConfigsByEnumCode(ctx context.Context, enumCode string) (*models.TaskConfig, error) {
	var taskConfig models.TaskConfig
	err := t.db.WithContext(ctx).
		Where("task_enum_code = ? AND status = ?", enumCode, models.TaskStatusEnumOngoing).
		Order("created_at DESC"). // 按创建时间降序排列，最新创建的排在前面
		First(&taskConfig).Error  // 只获取第一条记录
	return &taskConfig, ormhelper.WrapErr(err)
}

// ListTaskConfigsByEnumCode 根据枚举代码获取所有相关任务配置
func (t *TaskConfig) ListTaskConfigsByEnumCode(ctx context.Context, enumCode string) ([]*models.TaskConfig, error) {
	var taskConfigs []*models.TaskConfig
	err := t.db.WithContext(ctx).
		Where("task_enum_code = ? AND status = ?", enumCode, models.TaskStatusEnumOngoing).
		Order("sort_order DESC, id DESC").
		Find(&taskConfigs).Error

	if err != nil {
		return nil, err
	}

	if len(taskConfigs) == 0 {
		return nil, nil
	}

	return taskConfigs, nil
}

// HasTaskByCondition 判断同一种 task_condition 是否已存在任务
func (t *TaskConfig) HasTaskByCondition(ctx context.Context, taskCondition models.TaskSubType) (bool, error) {
	if taskCondition == "" {
		return false, nil
	}

	// 定义不可重复创建的任务子类型列表
	nonDuplicableTasks := map[models.TaskSubType]struct{}{
		models.TaskSubTypeModifyUsername:  {},
		models.TaskSubTypeModifyAvatar:    {},
		models.TaskSubTypeVerifyIdentity:  {},
		models.TaskSubTypeBindRealAccount: {},
		models.TaskSubTypeOpenVPS:         {},
		models.TaskSubTypeFollowWikiFX:    {},
	}

	// 如果当前任务类型不在不可重复列表中，则认为它没有重复（根据此函数的特定逻辑）
	if _, ok := nonDuplicableTasks[taskCondition]; !ok {
		return false, nil
	}

	// 对于指定不可重复的任务类型，检查数据库中是否已存在活动任务
	var count int64
	err := t.db.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("task_enum_code = ? AND status != ?", taskCondition, models.TaskStatusEnumClose).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CreateWithUniqueCheck 创建任务配置时检查唯一性（使用事务保证原子性）
func (t *TaskConfig) CreateWithUniqueCheck(ctx context.Context, taskConfig *models.TaskConfig, taskSubType models.TaskSubType) error {
	return t.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 在事务中检查任务条件唯一性
		exist, err := t.hasTaskByConditionTx(ctx, tx, taskSubType)
		if err != nil {
			return err
		}
		if exist {
			return errors.New("同一种任务条件已存在任务，不能重复创建")
		}

		// 在事务中创建任务配置
		return tx.Create(taskConfig).Error
	})
}

// hasTaskByConditionTx 在事务中检查任务条件唯一性
func (t *TaskConfig) hasTaskByConditionTx(ctx context.Context, tx *gorm.DB, taskCondition models.TaskSubType) (bool, error) {
	if taskCondition == "" {
		return false, nil
	}

	// 定义不可重复创建的任务子类型列表
	nonDuplicableTasks := map[models.TaskSubType]struct{}{
		models.TaskSubTypeModifyUsername:  {},
		models.TaskSubTypeModifyAvatar:    {},
		models.TaskSubTypeVerifyIdentity:  {},
		models.TaskSubTypeBindRealAccount: {},
		models.TaskSubTypeOpenVPS:         {},
		models.TaskSubTypeFollowWikiFX:    {},
	}

	// 如果当前任务类型不在不可重复列表中，则认为它没有重复
	if _, ok := nonDuplicableTasks[taskCondition]; !ok {
		return false, nil
	}

	// 对于指定不可重复的任务类型，检查数据库中是否已存在活动任务
	var count int64
	err := tx.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("task_enum_code = ? AND status != ?", taskCondition, models.TaskStatusEnumClose).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// TaskConfigQueryParams 任务配置查询参数
type TaskConfigQueryParams struct {
	Status      int8              `json:"status"`
	TaskType    models.TaskType   `json:"task_type"`
	ShowProject string            `json:"show_project"`
	RewardType  models.RewardType `json:"reward_type"`
	PageNum     int64             `json:"page_num"`
	PageSize    int64             `json:"page_size"`
}

// TaskConfigActiveParams 有效任务配置查询参数
type TaskConfigActiveParams struct {
	TaskType     models.TaskType `json:"task_type"`
	ShowProject  string          `json:"show_project"`
	MinVersion   string          `json:"min_version"`
	VisibleUsers string          `json:"visible_users"`
}

// ListGoodsAndVirtualGoodsTasks 查询状态为未发布和进行中的任务的实物和虚拟商品奖励情况
func (t *TaskConfig) ListGoodsAndVirtualGoodsTasks(ctx context.Context) ([]*models.TaskConfig, error) {
	var taskConfigs []*models.TaskConfig
	err := t.db.WithContext(ctx).Model(&models.TaskConfig{}).
		Where("status IN ? AND (reward_type = ? OR reward_type = ?)", []int{models.TaskStatusEnumNone, models.TaskStatusEnumOngoing}, models.RewardTypePhysical, models.RewardTypeVirtual).
		Order("sort_order DESC, id DESC").
		Find(&taskConfigs).Error
	if err != nil {
		return nil, err
	}
	return taskConfigs, nil
}
