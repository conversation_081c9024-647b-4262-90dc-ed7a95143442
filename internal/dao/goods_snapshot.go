package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type GoodsSnapshot struct {
	*DBModel
}

func NewGoodsSnapshot(db *gorm.DB) *GoodsSnapshot {
	return &GoodsSnapshot{
		DBModel: NewDBModel(db),
	}
}

func (s *GoodsSnapshot) Add(ctx context.Context, in *models.GoodsSnapshot, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *GoodsSnapshot) Get(ctx context.Context, id uint, opts ...igorm.Option) (*models.GoodsSnapshot, error) {
	var out models.GoodsSnapshot
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *GoodsSnapshot) GetByGoodsId(ctx context.Context, goodsId string, version int64, opts ...igorm.Option) (*models.GoodsSnapshot, error) {
	var out models.GoodsSnapshot
	err := s.session(ctx, opts...).
		Where("goods_id = ? and version = ?", goodsId, version).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *GoodsSnapshot) FindByIds(ctx context.Context, ids []uint, opts ...igorm.Option) ([]*models.GoodsSnapshot, error) {
	var out []*models.GoodsSnapshot
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
