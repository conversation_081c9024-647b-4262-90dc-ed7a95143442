package dao

import (
	"context"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoExhibitor struct {
	*DBModel
}

func NewExpoExhibitor(db *gorm.DB) *ExpoExhibitor {
	return &ExpoExhibitor{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoExhibitor) Add(ctx context.Context, in *models.ExpoExhibitor, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoExhibitor) BatchAdd(ctx context.Context, in []*models.ExpoExhibitor, opts ...igorm.Option) error {
	return s.session(ctx, opts...).CreateInBatches(in, len(in)).Error
}

func (s *ExpoExhibitor) ExistsByTraderCode(ctx context.Context, expoId int64, traderCode string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitor{}).
		Where("expo_id = ? and trader_code = ?", expoId, traderCode).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) GetByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (*models.ExpoExhibitor, error) {
	var out models.ExpoExhibitor
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("created_at desc").
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) Get(ctx context.Context, expoId, id int64, opts ...igorm.Option) (*models.ExpoExhibitor, error) {
	var out models.ExpoExhibitor
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) Update(ctx context.Context, in *models.ExpoExhibitor, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Omit("created_at").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) UpdateEnable(ctx context.Context, expoId, id int64, enable bool, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitor{}).
		Where("expo_id = ? and id = ?", expoId, id).
		Update("enable", enable).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) FindByPage(ctx context.Context, expoId int64, name string, level v1.SponsorLevel, page, size int, opts ...igorm.Option) ([]*models.ExpoExhibitor, error) {
	var (
		out     []*models.ExpoExhibitor
		session = s.session(ctx, opts...).Where("expo_id = ?", expoId)
	)

	if name != "" {
		session = session.Where("trader_name like ?", "%"+name+"%")
	}

	if level > 0 {
		session = session.Where("sponsor_level = ?", level)
	}

	err := session.
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) Delete(ctx context.Context, expoId, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		Delete(&models.ExpoExhibitor{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitor{}).
		Where("expo_id = ? and enable = 1", expoId).
		Count(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) FindByExpoIdWithSize(ctx context.Context, expoId int64, size int, opts ...igorm.Option) ([]*models.ExpoExhibitor, error) {
	var out []*models.ExpoExhibitor
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("`rank` ASC, sponsor_level DESC, created_at ASC").
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// FindByExpoIdPage 分页查询参展商列表，按照赞助等级和排序字段排序
func (s *ExpoExhibitor) FindByExpoIdPage(ctx context.Context, expoId int64, page, size int, opts ...igorm.Option) ([]*models.ExpoExhibitor, int64, error) {
	var (
		out   []*models.ExpoExhibitor
		total int64
	)
	offset := (page - 1) * size

	// 查询分页数据
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitor{}).
		Where("expo_id = ? AND enable = ?", expoId, true).
		Count(&total).
		Order("`rank` ASC, sponsor_level DESC, created_at ASC").
		Limit(size).
		Offset(offset).
		Find(&out).Error
	return out, total, ormhelper.WrapErr(err)
}

// CountByExpoIdEnabled 统计启用的参展商数量
func (s *ExpoExhibitor) CountByExpoIdEnabled(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitor{}).
		Where("expo_id = ? AND enable = ?", expoId, true).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

// FindByIds 根据ID列表查询参展商
func (s *ExpoExhibitor) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.ExpoExhibitor, error) {
	if len(ids) == 0 {
		return []*models.ExpoExhibitor{}, nil
	}

	var out []*models.ExpoExhibitor
	err := s.session(ctx, opts...).
		Where("id IN ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoExhibitor, error) {
	var out []*models.ExpoExhibitor
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("`rank` ASC, sponsor_level DESC, created_at ASC").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitor) GetByExpoIdAndTraderCode(ctx context.Context, expoId int64, traderCode string, opts ...igorm.Option) (*models.ExpoExhibitor, error) {
	var out models.ExpoExhibitor
	err := s.session(ctx, opts...).
		Where("expo_id = ? and trader_code=? and  enable = 1", expoId, traderCode).
		Order("created_at desc").
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}
