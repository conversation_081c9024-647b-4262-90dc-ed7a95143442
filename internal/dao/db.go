package dao

import (
	"context"
	"errors"
	"time"

	"gold_store/api/common"
	"gold_store/internal/conf"

	"github.com/airunny/wiki-go-tools/env"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/iredis"
	"github.com/go-kratos/kratos/v2/log"
	redis "github.com/go-redis/redis/v8"
	goCache "github.com/liyanbing/go-cache"
	redisCache "github.com/liyanbing/go-cache/cacher/redis"
	"gorm.io/gorm"
)

type (
	StaticDB     *gorm.DB
	VpsDB        *gorm.DB
	EaDB         *gorm.DB
	VipDB        *gorm.DB
	ReportDB     *gorm.DB
	ExhibitionDB *gorm.DB
)

func NewEaDB(c *conf.Data, logger log.Logger) (EaDB, func(), error) {
	database := c.EA
	if database == nil {
		return nil, nil, errors.New("empty ea config")
	}
	return newMySQL(database, logger)
}

func NewVpsDB(c *conf.Data, logger log.Logger) (VpsDB, func(), error) {
	database := c.VPS
	if database == nil {
		return nil, nil, errors.New("empty vps config")
	}
	return newMySQL(database, logger)
}

func NewVipDB(c *conf.Data, logger log.Logger) (VipDB, func(), error) {
	database := c.VIP
	if database == nil {
		return nil, nil, errors.New("empty vip config")
	}
	return newMySQL(database, logger)
}

func NewReportDB(c *conf.Data, logger log.Logger) (ReportDB, func(), error) {
	database := c.Report
	if database == nil {
		return nil, nil, errors.New("empty report config")
	}
	return newMySQL(database, logger)
}

func NewExhibitionDB(c *conf.Data, logger log.Logger) (ExhibitionDB, func(), error) {
	database := c.Exhibition
	if database == nil {
		return nil, nil, errors.New("empty exhibition config")
	}
	return newMySQL(database, logger)
}

func NewStaticDB(c *conf.Data, logger log.Logger) (StaticDB, func(), error) {
	database := c.Static
	if database == nil {
		return nil, nil, errors.New("empty static config")
	}
	return newMySQL(database, logger)
}

func NewMySQL(c *conf.Data, logger log.Logger) (*gorm.DB, func(), error) {
	database := c.Database
	if database == nil {
		return nil, nil, errors.New("empty database config")
	}
	return newMySQL(database, logger)
}

func newMySQL(database *common.DataConfig_Database, logger log.Logger) (*gorm.DB, func(), error) {
	mysqlClient, closer, err := igorm.NewGORM(&igorm.Config{
		LogLevel:    int(database.Level),
		MaxOpen:     int(database.MaxOpen),
		MaxIdle:     int(database.MaxIdle),
		MaxLifeTime: time.Duration(database.MaxLifeTimeSeconds) * time.Second,
		Source:      database.Source,
	}, logger)
	if err != nil {
		return nil, nil, err
	}

	return mysqlClient, func() {
		closer.Close()
	}, nil
}

func NewRedis(c *conf.Data, logger log.Logger) (*redis.Client, func(), error) {
	redisClient, err := iredis.NewClient(&iredis.Config{
		Address:  c.Redis.Address,
		Username: c.Redis.Username,
		Password: c.Redis.Password,
		DB:       int(c.Redis.Db),
		MaxIdle:  int(c.Redis.MaxIdle),
	}, logger)
	if err != nil {
		return nil, nil, err
	}

	return redisClient, func() {
		redisClient.Close()
	}, nil
}

func NewRedisCache(redisClient *redis.Client) (goCache.Cache, error) {
	cache := redisCache.NewRedisCache(redisClient)
	cache.SetNamespace(env.GetServiceName())
	return cache, nil
}

type DBModel struct {
	db *gorm.DB
}

func NewDBModel(db *gorm.DB) *DBModel {
	return &DBModel{
		db: db,
	}
}

func (s *DBModel) session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return igorm.NewOptions(s.db, opts...).Session().WithContext(ctx)
}

func (s *DBModel) Begin() *gorm.DB {
	return s.db.Begin()
}

func (s *DBModel) WithTx(ctx context.Context, fn func(ctx context.Context) error) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 创建新的上下文，将事务传递给子操作
		txCtx := context.WithValue(ctx, "tx", tx)
		return fn(txCtx)
	})
}
