package dao

import (
	"context"
	"errors"
	"time"

	"api-expo/api/common"

	"github.com/airunny/wiki-go-tools/env"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/iredis"
	"github.com/go-kratos/kratos/v2/log"
	redis "github.com/go-redis/redis/v8"
	goCache "github.com/liyanbing/go-cache"
	redisCache "github.com/liyanbing/go-cache/cacher/redis"
	"gorm.io/gorm"
)

func NewMySQL(c *common.DataConfig, logger log.Logger) (*gorm.DB, func(), error) {
	database := c.Database
	if database == nil {
		return nil, nil, errors.New("empty database config")
	}

	mysqlClient, closer, err := igorm.NewGORM(&igorm.Config{
		LogLevel:    int(database.Level),
		MaxOpen:     int(database.MaxOpen),
		MaxIdle:     int(database.MaxIdle),
		MaxLifeTime: time.Duration(database.MaxLifeTimeSeconds) * time.Second,
		Source:      c.Database.Source,
	}, logger)
	if err != nil {
		return nil, nil, err
	}

	return mysqlClient, func() {
		closer.Close()
	}, nil
}

func NewRedis(c *common.DataConfig, logger log.Logger) (*redis.Client, func(), error) {
	redisClient, err := iredis.NewClient(&iredis.Config{
		Address:  c.Redis.Address,
		Password: c.Redis.Password,
		DB:       int(c.Redis.Db),
		MaxIdle:  int(c.Redis.MaxIdle),
	}, logger)
	if err != nil {
		return nil, nil, err
	}

	return redisClient, func() {
		redisClient.Close()
	}, nil
}

func NewRedisCache(redisClient *redis.Client) (goCache.Cache, error) {
	cache := redisCache.NewRedisCache(redisClient)
	cache.SetNamespace(env.GetServiceName())
	return cache, nil
}

type DBModel struct {
	db *gorm.DB
}

func NewDBModel(db *gorm.DB) *DBModel {
	return &DBModel{
		db: db,
	}
}

func (s *DBModel) session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	return igorm.NewOptions(s.db, opts...).Session().WithContext(ctx)
}

func (s *DBModel) Begin() *gorm.DB {
	return s.db.Begin()
}
func (s *DBModel) WithTx(ctx context.Context, fn func(ctx context.Context) error) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		txCtx := context.WithValue(ctx, "tx", tx)
		return fn(txCtx)
	})
}
