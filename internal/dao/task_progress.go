package dao

import (
	"context"
	"time"

	"gorm.io/gorm"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
)

type TaskProgress struct {
	*DBModel
}

// NewTaskProgress 创建任务进度DAO实例
func NewTaskProgress(db *gorm.DB) *TaskProgress {
	return &TaskProgress{
		DBModel: NewDBModel(db),
	}
}

// Create 创建任务进度
func (t *TaskProgress) Create(ctx context.Context, taskProgress *models.TaskProgress) error {
	return t.db.WithContext(ctx).Create(taskProgress).Error
}

// Update 更新任务进度
func (t *TaskProgress) Update(ctx context.Context, taskProgress *models.TaskProgress) error {
	return t.db.WithContext(ctx).Save(taskProgress).Error
}

// GetByUserAndTaskID 根据用户ID和任务ID获取任务进度
func (t *TaskProgress) GetByUserAndTaskID(ctx context.Context, userID string, taskConfigID uint) (*models.TaskProgress, error) {
	var taskProgress models.TaskProgress
	err := t.db.WithContext(ctx).
		Where("user_id = ? AND task_config_id = ?", userID, taskConfigID).
		First(&taskProgress).Error
	if err != nil {
		return nil, err
	}
	return &taskProgress, nil
}

// GetWithTaskConfig 获取任务进度及关联的任务配置
func (t *TaskProgress) GetWithTaskConfig(ctx context.Context, userID string, taskConfigID uint) (*models.TaskProgress, error) {
	var taskProgress models.TaskProgress
	err := t.db.WithContext(ctx).
		Preload("TaskConfig").
		Where("user_id = ? AND task_config_id = ?", userID, taskConfigID).
		First(&taskProgress).Error
	if err != nil {
		return nil, err
	}
	return &taskProgress, nil
}

// ListByUserID 获取用户所有任务进度
func (t *TaskProgress) ListByUserID(ctx context.Context, userID string) ([]*models.TaskProgress, error) {
	var taskProgresses []*models.TaskProgress
	err := t.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Find(&taskProgresses).Error
	if err != nil {
		return nil, err
	}
	return taskProgresses, nil
}

// ListByUserIDWithTaskConfig 获取用户所有任务进度及关联的任务配置
func (t *TaskProgress) ListByUserIDWithTaskConfig(ctx context.Context, userID string) ([]*models.TaskProgress, error) {
	var taskProgresses []*models.TaskProgress
	err := t.db.WithContext(ctx).
		Preload("TaskConfig").
		Where("user_id = ?", userID).
		Find(&taskProgresses).Error
	if err != nil {
		return nil, err
	}
	return taskProgresses, nil
}

// ListByUserIDAndStatus 根据用户ID和状态获取任务进度
func (t *TaskProgress) ListByUserIDAndStatus(ctx context.Context, userID string, status models.UserProgressStatus) ([]*models.TaskProgress, error) {
	var taskProgresses []*models.TaskProgress
	err := t.db.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, status).
		Find(&taskProgresses).Error
	if err != nil {
		return nil, err
	}
	return taskProgresses, nil
}

// ListByUserIDAndTaskType 根据用户ID和任务类型获取任务进度
// 可以通过状态参数过滤特定状态的任务，如果状态参数为nil，则获取所有状态
// 可以选择是否过滤已过期的任务
func (t *TaskProgress) ListByUserIDAndTaskType(ctx context.Context, userID string, taskConfigIDs []uint, status []models.UserProgressStatus, filterExpired bool) ([]*models.TaskProgress, error) {
	if len(taskConfigIDs) == 0 {
		return []*models.TaskProgress{}, nil
	}

	query := t.db.WithContext(ctx).
		Where("user_id = ? AND task_config_id IN ?", userID, taskConfigIDs)

	// 如果指定了状态过滤，则添加状态条件
	if len(status) > 0 {
		query = query.Where("status IN ?", status)
	}

	// 如果需要过滤已过期任务，添加过期时间条件
	if filterExpired {
		now := time.Now().UTC()
		query = query.Where("expire_time > ?", now)
	}

	// 执行查询
	var taskProgresses []*models.TaskProgress
	err := query.Find(&taskProgresses).Error
	if err != nil {
		return nil, err
	}
	return taskProgresses, nil
}

// ListByUserIDAndTaskTypeSimple 但默认排除过期状态的任务
func (t *TaskProgress) ListByUserIDAndTaskTypeSimple(ctx context.Context, userID string, taskConfigIDs []uint) ([]*models.TaskProgress, error) {
	// 获取除过期状态外的所有任务状态
	activeStatuses := []models.UserProgressStatus{
		models.UserProgressStatusNone,      // 未领取任务
		models.UserProgressStatusOngoing,   // 进行中
		models.UserProgressStatusCompleted, // 已完成未领取
		models.UserProgressStatusReceived,  // 已完成已领取
	}

	// 默认过滤掉已过期的任务
	return t.ListByUserIDAndTaskType(ctx, userID, taskConfigIDs, activeStatuses, true)
}

// UpdateProgress 更新任务进度
func (t *TaskProgress) UpdateProgress(ctx context.Context, userID string, progressId uint, progress int) error {
	return t.db.WithContext(ctx).
		Model(&models.TaskProgress{}).
		Where("id = ? AND user_id = ? ", progressId, userID).
		Updates(map[string]interface{}{
			"user_progress": progress,
		}).Error
}

// UpdateStatus 更新任务状态
func (t *TaskProgress) UpdateStatus(ctx context.Context, userID string, taskConfigID uint, status models.UserProgressStatus) error {
	return t.db.WithContext(ctx).
		Model(&models.TaskProgress{}).
		Where("user_id = ? AND task_config_id = ?", userID, taskConfigID).
		Updates(map[string]interface{}{
			"status": status,
		}).Error
}

// UpdateStatusAndReceiveTime 更新任务状态和领取时间
func (t *TaskProgress) UpdateStatusAndReceiveTime(ctx context.Context, userID string, taskConfigID uint, status models.UserProgressStatus) error {
	now := time.Now().UTC()
	return t.db.WithContext(ctx).
		Model(&models.TaskProgress{}).
		Where("user_id = ? AND task_config_id = ?", userID, taskConfigID).
		Updates(map[string]interface{}{
			"status":       status,
			"receive_time": now,
		}).Error
}

// FindExpiredTasks 查找已过期任务
func (t *TaskProgress) FindExpiredTasks(ctx context.Context, limit int) ([]*models.TaskProgress, error) {
	var taskProgresses []*models.TaskProgress
	now := time.Now().UTC()
	err := t.db.WithContext(ctx).
		Where("expire_time < ? AND status IN ?", now, []models.UserProgressStatus{models.UserProgressStatusOngoing, models.UserProgressStatusCompleted}).
		Limit(limit).
		Find(&taskProgresses).Error
	if err != nil {
		return nil, err
	}
	return taskProgresses, nil
}

// UpdateExpiredStatus 批量更新过期任务状态
func (t *TaskProgress) UpdateExpiredStatus(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	return t.db.WithContext(ctx).
		Model(&models.TaskProgress{}).
		Where("id IN ?", ids).
		Update("status", models.UserProgressStatusExpired).
		Error
}

// DeleteByTaskConfigID 根据任务配置ID删除任务进度
func (t *TaskProgress) DeleteByTaskConfigID(ctx context.Context, taskConfigID uint) error {
	return t.db.WithContext(ctx).
		Where("task_config_id = ?", taskConfigID).
		Delete(&models.TaskProgress{}).
		Error
}

// CountCompletedTasks 统计用户已完成任务数量
func (t *TaskProgress) CountCompletedTasks(ctx context.Context, userID string, taskType models.TaskType) (int64, error) {
	var count int64
	query := t.db.WithContext(ctx).
		Model(&models.TaskProgress{}).
		Joins("JOIN task_config ON task_progress.task_config_id = task_config.id").
		Where("task_progress.user_id = ? AND task_progress.status = ?",
			userID, models.UserProgressStatusReceived)

	if taskType > 0 {
		query = query.Where("task_config.task_type = ?", taskType)
	}

	err := query.Count(&count).Error
	return count, err
}

// GetTaskProgress 根据用户ID和任务ID获取任务进度
// 注意：此方法对于日常任务可能返回历史记录，建议使用 GetCurrentTaskProgress
func (t *TaskProgress) GetTaskProgress(ctx context.Context, userID string, taskConfigID uint) (*models.TaskProgress, error) {
	var taskProgress models.TaskProgress
	err := t.db.WithContext(ctx).
		Where("user_id = ? AND task_config_id = ? AND expire_time >= ?", userID, taskConfigID, time.Now().UTC()).Order("created_at DESC").
		First(&taskProgress).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &taskProgress, nil
}

// CreateTaskProgress 创建任务进度记录，初始进度默认为1
func (t *TaskProgress) CreateTaskProgress(ctx context.Context, userID string, taskConfigID uint, expireTime time.Time, userTimezone string) error {
	// 创建新的任务进度记录
	taskProgress := &models.TaskProgress{
		UserID:       userID,
		TaskConfigID: taskConfigID,
		Status:       models.UserProgressStatusOngoing,
		UserProgress: 1, // 初始值设为1，表示已经完成一次
		ExpireTime:   expireTime,
		UserTimezone: userTimezone,
	}

	// 使用FirstOrCreate模式，如果记录不存在则创建，存在则不做任何操作
	// 这是一个原子操作，可以避免并发问题
	result := t.db.WithContext(ctx).
		Where("user_id = ? AND task_config_id = ? AND expire_time = ?",
			userID, taskConfigID, expireTime).
		FirstOrCreate(taskProgress)

	// 判断操作是否成功
	if result.Error != nil {
		// 如果是唯一键冲突，则忽略错误（表示记录已存在）
		if result.Error.Error() == "Error 1062: Duplicate entry" {
			return nil
		}
		return result.Error
	}

	return nil
}

// GetOngoingTasksByUserIDAndTaskIDs 获取用户在指定任务ID列表中且未过期的进行中任务
func (t *TaskProgress) GetOngoingTasksByUserIDAndTaskIDs(ctx context.Context, userID string, taskIDs []uint) ([]*models.TaskProgress, error) {
	if len(taskIDs) == 0 {
		return []*models.TaskProgress{}, nil
	}

	var progresses []*models.TaskProgress
	now := time.Now().UTC()
	err := t.db.WithContext(ctx).
		Where("user_id = ? AND task_config_id IN ? AND status = ? AND expire_time > ?",
			userID, taskIDs, models.UserProgressStatusOngoing, now).
		Find(&progresses).Error

	if err != nil {
		return nil, err
	}

	return progresses, nil
}

// UpdateStatusWithTx 使用事务更新任务状态
func (t *TaskProgress) UpdateStatusWithTx(ctx context.Context, tx *gorm.DB, userID string, taskConfigID uint, status models.UserProgressStatus) error {
	return tx.WithContext(ctx).
		Model(&models.TaskProgress{}).
		Where("user_id = ? AND task_config_id = ?", userID, taskConfigID).
		Updates(map[string]interface{}{
			"status": status,
		}).Error
}

// GetByUserAndTaskEnumCode 根据用户ID和任务枚举代码获取任务进度
func (t *TaskProgress) GetByUserAndTaskEnumCode(ctx context.Context, userID string, taskEnumCode string, opts ...igorm.Option) (*models.TaskProgress, error) {
	var taskProgress models.TaskProgress
	err := t.session(ctx, opts...).
		Joins("JOIN task_config ON task_progress.task_config_id = task_config.id").
		Where("task_progress.user_id = ? AND task_config.task_enum_code = ? AND task_config.status = ?", userID, taskEnumCode, models.TaskStatusEnumOngoing).
		Preload("TaskConfig").
		First(&taskProgress).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, ormhelper.WrapErr(err)
	}
	return &taskProgress, nil
}

// TaskProgressQueryParams 任务进度查询参数
type TaskProgressQueryParams struct {
	PageNum      int64  `json:"page_num"`      // 页码
	PageSize     int64  `json:"page_size"`     // 每页数量
	ProgressId   int64  `json:"progress_id"`   // 流水号
	TaskName     string `json:"task_name"`     // 任务名称
	RewardStatus int8   `json:"reward_status"` // 奖励状态：0=全部,1=已领取,2=未领取
	StartTime    string `json:"start_time"`    // 开始时间
	EndTime      string `json:"end_time"`      // 结束时间
}

// TaskProgressRecord 任务进度记录DTO
type TaskProgressRecord struct {
	ProgressId   int64     `json:"progress_id"`   // 流水号
	TaskName     string    `json:"task_name"`     // 任务名称
	RewardType   int       `json:"reward_type"`   // 奖励类型
	RewardName   string    `json:"reward_name"`   // 任务奖品名称
	TaskStatus   int       `json:"task_status"`   // 任务状态
	RewardStatus int       `json:"reward_status"` // 奖励状态
	UpdatedAt    time.Time `json:"updated_at"`    // 完成时间
	TaskID       int64     `json:"task_id"`       // 任务ID
	UserID       string    `json:"user_id"`       // 用户ID
}

// List 查询任务进度列表
func (t *TaskProgress) List(ctx context.Context, params *TaskProgressQueryParams) ([]*TaskProgressRecord, int64, error) {
	// 设置默认值
	if params.PageNum <= 0 {
		params.PageNum = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 创建查询
	query := t.db.WithContext(ctx).Table("task_progress as tp").
		Select(`
			tp.id as progress_id,
			COALESCE(tc.task_title, '') as task_name,
			COALESCE(tc.reward_type, 0) as reward_type,
			CASE
				WHEN tc.reward_type = 1 THEN CONCAT(CAST(JSON_EXTRACT(tc.reward_config, '$.gold_coins') AS CHAR), '金币')
				WHEN tc.reward_type = 2 THEN JSON_UNQUOTE(JSON_EXTRACT(tc.reward_config, '$.goods.goods_id'))
				WHEN tc.reward_type = 3 THEN JSON_UNQUOTE(JSON_EXTRACT(tc.reward_config, '$.virtual_goods.goods_id'))
				ELSE '未知奖励'
			END as reward_name,
			tp.status as task_status,
			CASE
				WHEN tp.status = 3 THEN 1
				WHEN tp.status IN (0, 1, 2, 4) THEN 2
				ELSE 2
			END as reward_status,
			tp.updated_at,
			tp.task_config_id as task_id,
			tp.user_id
		`).
		Joins("LEFT JOIN task_config as tc ON tp.task_config_id = tc.id")

	// 应用筛选条件
	// 流水号筛选
	if params.ProgressId > 0 {
		query = query.Where("tp.id = ?", params.ProgressId)
	}

	// 任务名称筛选
	if params.TaskName != "" {
		query = query.Where("tc.task_title LIKE ?", "%"+params.TaskName+"%")
	}

	// 奖励状态筛选
	if params.RewardStatus == 1 { // 已领取
		query = query.Where("tp.status = ?", models.UserProgressStatusReceived)
	} else if params.RewardStatus == 2 { // 未领取
		query = query.Where("tp.status IN ?", []models.UserProgressStatus{
			models.UserProgressStatusNone,
			models.UserProgressStatusOngoing,
			models.UserProgressStatusCompleted,
			models.UserProgressStatusExpired,
		})
	}

	// 时间区间筛选
	if params.StartTime != "" && params.EndTime != "" {
		query = query.Where("tp.receive_time BETWEEN ? AND ? OR tp.updated_at BETWEEN ? AND ?",
			params.StartTime, params.EndTime, params.StartTime, params.EndTime)
	} else if params.StartTime != "" {
		query = query.Where("tp.receive_time >= ? OR tp.updated_at >= ?", params.StartTime, params.StartTime)
	} else if params.EndTime != "" {
		query = query.Where("tp.receive_time <= ? OR tp.updated_at <= ?", params.EndTime, params.EndTime)
	}

	// 计算总记录数
	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if params.PageSize > 0 {
		offset := (params.PageNum - 1) * params.PageSize
		query = query.Offset(int(offset)).Limit(int(params.PageSize))
	}

	// 按完成时间降序排序
	query = query.Order("tp.updated_at DESC")

	// 执行查询
	var records []*TaskProgressRecord
	err = query.Find(&records).Error
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
