package dao

import (
	"context"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gold_store/internal/models"
	"gorm.io/gorm"
)

type GiftCardSetting struct {
	*DBModel
}

func NewGiftCardSetting(db *gorm.DB) *GiftCardSetting {
	return &GiftCardSetting{
		DBModel: NewDBModel(db),
	}
}

func (s *GiftCardSetting) Add(ctx context.Context, in *models.GiftCardSetting, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *GiftCardSetting) Edit(ctx context.Context, in *models.GiftCardSetting, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Save(in).Error
	return ormhelper.WrapErr(err)
}

func (s *GiftCardSetting) Delete(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Delete(&models.GiftCardSetting{}, id).Error
	return ormhelper.WrapErr(err)
}

func (s *GiftCardSetting) GetSingle(ctx context.Context, id int64, opts ...igorm.Option) (*models.GiftCardSetting, error) {
	var giftCardSetting *models.GiftCardSetting
	err := s.session(ctx, opts...).Where("id=?", id).Find(&giftCardSetting).Error
	return giftCardSetting, ormhelper.WrapErr(err)
}

func (s *GiftCardSetting) PageList(ctx context.Context, size, page int, opts ...igorm.Option) ([]*models.GiftCardSetting, int64, error) {
	var count int64
	var giftCardSetting []*models.GiftCardSetting
	err := s.session(ctx, opts...).Order("created_at desc").
		Model(&models.GiftCardSetting{}).
		Count(&count).
		Offset((page - 1) * size).
		Limit(size).
		Find(&giftCardSetting).Error
	return giftCardSetting, count, ormhelper.WrapErr(err)
}

// GetGoodsIsConfigGiftCard 商品是否配置了兑换券
func (s *GiftCardSetting) GetGoodsIsConfigGiftCard(ctx context.Context, goods_id string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.GiftCardSetting{}).
		Where("goods_id=?", goods_id).Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}
