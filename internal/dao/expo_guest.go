package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoGuest struct {
	*DBModel
}

func NewExpoGuest(db *gorm.DB) *ExpoGuest {
	return &ExpoGuest{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoGuest) Add(ctx context.Context, in *models.ExpoGuest, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoGuest) BatchAdd(ctx context.Context, in []*models.ExpoGuest, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).CreateInBatches(in, len(in)).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoGuest) DeleteByGuestIds(ctx context.Context, expoId int64, guestId []int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and guest_id in ?", expoId, guestId).
		Delete(&models.ExpoGuest{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoGuest) SetExpoGuestEnable(ctx context.Context, expoId int64, guestId int64, enable bool, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoGuest{}).
		Where("expo_id = ? and guest_id = ?", expoId, guestId).
		Update("enable", enable).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoGuest) FindByGuestIds(ctx context.Context, expoId int64, guestId []int64, opts ...igorm.Option) ([]*models.ExpoGuest, error) {
	var out []*models.ExpoGuest
	err := s.session(ctx, opts...).
		Where("expo_id = ? and guest_id in ?", expoId, guestId).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoGuest) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoGuest{}).
		Where("expo_id = ?", expoId).
		Count(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoGuest) FindByExpoId(ctx context.Context, expoId int64, size int, opts ...igorm.Option) ([]*models.ExpoGuest, error) {
	var out []*models.ExpoGuest
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Order("created_at desc").
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// GetAudiencesByExpoId 根据展会ID获取观展用户列表，只使用expo_guest表
func (s *ExpoGuest) GetAudiencesByExpoId(ctx context.Context, expoId int64, laber string, keyword string, page, size int32, opts ...igorm.Option) ([]*models.ExpoGuest, int64, error) {
	var items []*models.ExpoGuest
	var total int64

	// 构建查询条件
	query := s.session(ctx, opts...).Where("expo_id = ?", expoId)

	// TODO: 根据tabId过滤不同类型的观展用户
	// 这里预留扩展，可以在expo_guest表添加type字段来分类

	// 关键字搜索 - 现在只基于guest_id进行搜索
	if keyword != "" {
		// 如果关键字是数字，尝试按guest_id搜索
		// 否则跳过关键字搜索，因为expo_guest表没有其他可搜索字段
		query = query.Where("guest_id = ?", keyword)
	}

	// 获取总数
	if err := query.Model(&models.ExpoGuest{}).Count(&total).Error; err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	// 分页查询
	offset := (page - 1) * size
	err := query.Offset(int(offset)).Limit(int(size)).Find(&items).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	return items, total, nil
}

// FindByGuestId 根据GuestID获取用户关联了哪些展会
func (s *ExpoGuest) FindByGuestId(ctx context.Context, guestId int64, opts ...igorm.Option) ([]*models.ExpoGuest, error) {
	var out []*models.ExpoGuest
	err := s.session(ctx, opts...).
		Where("guest_id = ?", guestId).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoGuest) FindExpoIdsByGuestIds(ctx context.Context, guestId []int64, opts ...igorm.Option) ([]*models.ExpoGuest, error) {
	var out []*models.ExpoGuest
	err := s.session(ctx, opts...).
		Where("guest_id in ? and enable = 1", guestId).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// DeleteByGuestId 根据嘉宾ID删除
func (s *ExpoGuest) DeleteByGuestId(ctx context.Context, guestId int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("guest_id =?", guestId).
		Delete(&models.ExpoGuest{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoGuest) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.ExpoGuest, error) {
	var out []*models.ExpoGuest
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}
