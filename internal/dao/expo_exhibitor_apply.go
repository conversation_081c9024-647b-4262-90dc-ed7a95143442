package dao

import (
	"context"
	"time"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoExhibitorApply struct {
	*DBModel
}

func NewExpoExhibitorApply(db *gorm.DB) *ExpoExhibitorApply {
	return &ExpoExhibitorApply{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoExhibitorApply) Add(ctx context.Context, in *models.ExpoExhibitorApply, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoExhibitorApply) SetStatus(ctx context.Context, id int64, status v1.ExhibitorApplyStatus, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitorApply{}).
		Where("id = ?", id).
		Update("status", status).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoExhibitorApply) Search(ctx context.Context, in *models.ExpoExhibitorApplySearch, opts ...igorm.Option) ([]*models.ExpoExhibitorApply, int64, error) {
	var (
		out     []*models.ExpoExhibitorApply
		total   int64
		session = s.session(ctx, opts...).Model(&ExpoExhibitorApply{})
	)

	if in.Company != "" {
		session = session.Where("company LIKE ?", "%"+in.Company+"%")
	}

	if in.Start > 0 {
		session = session.Where("created_at >= ?", time.Unix(in.Start, 0).UTC())
	}

	if in.End > 0 {
		session = session.Where("created_at <= ?", time.Unix(in.End, 0).UTC())
	}

	if in.Status > 0 {
		session = session.Where("status = ?", in.Status)
	}

	err := session.Count(&total).
		Order("created_at desc").
		Find(&out).Error
	return out, total, ormhelper.WrapErr(err)
}
