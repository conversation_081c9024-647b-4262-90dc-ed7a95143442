package dao

import (
	"context"
	"fmt"
	"strconv"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	innErr "github.com/airunny/wiki-go-tools/errors"
	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Order struct {
	*DBModel
}

func NewOrder(db *gorm.DB) *Order {
	return &Order{
		DBModel: NewDBModel(db),
	}
}

func (s *Order) Add(ctx context.Context, in *models.Order, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *Order) Get(ctx context.Context, orderNo, userId string, opts ...igorm.Option) (*models.Order, error) {
	var order models.Order
	err := s.session(ctx, opts...).
		Where("order_no = ? and user_id = ?", orderNo, userId).
		First(&order).Error
	return &order, ormhelper.WrapErr(err)
}

func (s *Order) GetByOrderNo(ctx context.Context, orderNo string, opts ...igorm.Option) (*models.Order, error) {
	var order models.Order
	err := s.session(ctx, opts...).
		Where("order_no = ?", orderNo).
		First(&order).Error
	return &order, ormhelper.WrapErr(err)
}

func (s *Order) SetTrackingNo(ctx context.Context, orderNo, trackingNo string, opts ...igorm.Option) (int64, error) {
	ret := s.session(ctx, opts...).
		Model(&models.Order{}).
		Where("order_no = ? and tracking_no = ?", orderNo, "").
		Updates(map[string]interface{}{
			"tracking_no": trackingNo,
			"status":      v1.OrderStatus_DELIVERY,
		})
	return ret.RowsAffected, ormhelper.WrapErr(ret.Error)
}

func (s *Order) GetCount(ctx context.Context, userId string, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.Order{}).
		Where("user_id = ?", userId).
		Count(&out).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Order) GetCountBySource(ctx context.Context, userId string, source v1.OrderSource, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.Order{}).
		Where("user_id = ? and source = ?", userId, source).
		Count(&out).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Order) GetCountByStatus(ctx context.Context, userId string, status v1.OrderStatus, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.Order{}).
		Where("user_id = ? and status = ?", userId, status).
		Count(&out).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Order) UpdateStatus(ctx context.Context, orderNo string, status v1.OrderStatus, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Order{}).
		Where("order_no = ?", orderNo).
		Update("status", status).Error
	return ormhelper.WrapErr(err)
}

func (s *Order) Update(ctx context.Context, orderNo string, updates map[string]interface{}, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Order{}).
		Where("order_no = ?", orderNo).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

func (s *Order) FindOrderByUserId(ctx context.Context, in *models.FindOrderParams, opts ...igorm.Option) ([]*models.Order, string, error) {
	var (
		out        []*models.Order
		nextOffset string
		intOffset  = int64(1)
		err        error
	)

	if in.Offset != "" {
		intOffset, err = strconv.ParseInt(in.Offset, 10, 64)
		if err != nil {
			return nil, "", innErr.ErrBadRequest
		}
	}

	if intOffset <= 0 {
		intOffset = 1
	}

	var (
		query = "user_id = ?"
		args  = []interface{}{in.UserId}
	)

	if in.Status > 0 {
		query = "user_id = ? and status = ?"
		args = []interface{}{in.UserId, in.Status}
	}

	if len(in.Sources) > 0 {
		query = fmt.Sprintf("%s and source in ?", query)
		args = append(args, in.Sources)
	}

	err = s.session(ctx, opts...).
		Where(query, args...).
		Order("created_at desc").
		Offset(in.Size * (int(intOffset) - 1)).
		Limit(in.Size + 1).
		Find(&out).Error
	if err != nil {
		return nil, nextOffset, err
	}

	count := len(out)
	if count > 1 && count > in.Size {
		nextOffset = strconv.Itoa(int(intOffset) + 1)
	}

	if count > in.Size {
		out = out[:count-1]
	}
	return out, nextOffset, nil
}

func (s *Order) FilterOrder(ctx context.Context, in *models.FilterOrderParams, opts ...igorm.Option) ([]*models.Order, int64, error) {
	var (
		out     []*models.Order
		total   int64
		session = s.session(ctx, opts...).Where("source = 0")
	)

	if in.OrderNo != "" {
		session = session.Where("order_no = ?", in.OrderNo)
	}

	if in.PaymentMethod >= 0 {
		session = session.Where("payment_method = ?", in.PaymentMethod)
	}

	if in.Status > 0 {
		session = session.Where("status = ?", in.Status)
	}

	if in.GoodsName != "" {
		session = session.Where("goods_name like ?", fmt.Sprintf("%%%s%%", in.GoodsName))
	}

	err := session.
		Model(&models.Order{}).
		Count(&total).
		Order("created_at desc").
		Offset(int((in.Page - 1) * in.Size)).
		Limit(int(in.Size)).
		Find(&out).Error
	return out, total, ormhelper.WrapErr(err)
}

func (s *Order) FindOrderIdsBySource(ctx context.Context, source v1.OrderSource, page, size int, opts ...igorm.Option) ([]string, error) {
	var out []string
	err := s.session(ctx, opts...).
		Model(&models.Order{}).
		Select("order_no").
		Where("source = ?", source).
		Order("order_no").
		Offset((page-1)*size).
		Limit(size).
		Pluck("order_no", &out).Error
	return out, ormhelper.WrapErr(err)
}
