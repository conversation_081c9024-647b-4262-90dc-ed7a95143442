package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type CommentAt struct {
	*DBModel
}

func NewCommentAt(db *gorm.DB) *CommentAt {
	return &CommentAt{
		DBModel: NewDBModel(db),
	}
}

func (s *CommentAt) Add(ctx context.Context, in []*models.ExpoCommentAt, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

// 获取列表
func (s *CommentAt) GetListByCommentId(ctx context.Context, commentIds []string, opts ...igorm.Option) ([]*models.ExpoCommentAt, error) {
	var list []*models.ExpoCommentAt
	err := s.session(ctx, opts...).Where("comment_id in (?)", commentIds).Find(&list).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return list, nil
}
