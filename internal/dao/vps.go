package dao

import (
	"context"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
)

type VPS struct {
	*DBModel
}

func NewVPS(db VpsDB) *VPS {
	return &VPS{
		DBModel: NewDBModel(db),
	}
}

func (s *VPS) FindByOrderId(ctx context.Context, orderIds []string, opts ...igorm.Option) ([]string, error) {
	var out []string
	err := s.session(ctx, opts...).
		Table("hostorders").
		Select("order_id").
		Where("order_id in ? and trade_status = ?", orderIds, "TRADE_SUCCESS").
		Pluck("order_id", &out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *VPS) GetImage(ctx context.Context, language int, zone string, opts ...igorm.Option) (*models.VPSImage, error) {
	var out models.VPSImage
	err := s.session(ctx, opts...).
		Where("Language = ? and Zone = ?", language, zone).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}
