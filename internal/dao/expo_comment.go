package dao

import (
	"context"
	"fmt"
	"time"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Comment struct {
	*DBModel
}

func NewComment(db *gorm.DB) *Comment {
	return &Comment{
		DBModel: NewDBModel(db),
	}
}

func (s *Comment) Add(ctx context.Context, in *models.ExpoComment, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}
func (s *Comment) Get(ctx context.Context, id string, opts ...igorm.Option) (*models.ExpoComment, error) {
	var comment models.ExpoComment
	err := s.session(ctx, opts...).Where("id = ?", id).First(&comment).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &comment, nil
}

func (s *Comment) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoComment{}).
		Where("expo_id = ? AND status=2", expoId).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

// PageParentList 获取父级评论
func (s *Comment) PageParentList(ctx context.Context, expoId int64, page int, size int, userId string, opts ...igorm.Option) ([]*models.ExpoComment, error) {
	var comments []*models.ExpoComment
	query := s.session(ctx, opts...).Where("expo_id = ? and  parent_id='' ", expoId)
	if len(userId) > 0 {
		query = query.Where(" status=2 or (status=1 and user_id=? )", userId)
	} else {
		query = query.Where(" status=2")
	}
	err := query.
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&comments).Error
	fmt.Println(err)
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return comments, nil
}

func (s *Comment) PageListByRootId(ctx context.Context, rootIds []string, userId string, opts ...igorm.Option) ([]*models.ExpoComment, error) {
	var comments []*models.ExpoComment
	sql := ""
	var err error
	if len(userId) > 0 {
		sql = "select * from (select *,row_number() over(partition by root_id order by created_at desc ) rs from  expo_comment where root_id in ? and (status=2 or (user_id=? and status=1 ))) a where a.rs <4"
		err = s.session(ctx, opts...).Raw(sql, rootIds, userId).Find(&comments).Error

	} else {
		sql = "select * from (select *,row_number() over(partition by root_id order by created_at desc ) rs from  expo_comment where root_id in ? and status=2 ) a where a.rs <4"
		err = s.session(ctx, opts...).Raw(sql, rootIds).Find(&comments).Error
	}
	fmt.Println(err)
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return comments, nil
}

// GetTotalCount 根据展会Id 获取总数
func (s *Comment) GetTotalCountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var total int64 = 0
	err := s.session(ctx, opts...).Model(&models.ExpoComment{}).Where("expo_id = ? and status=2", expoId).Count(&total).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return total, nil
}

// 加载评论
func (s *Comment) PageReplyPageList(ctx context.Context, rootId string, exceptIds []string, page int, size int, userId string, opts ...igorm.Option) ([]*models.ExpoComment, error) {
	var comments []*models.ExpoComment
	query := s.session(ctx, opts...).Where("root_id = ? and  id not in ? ", rootId, exceptIds)
	if len(userId) > 0 {
		query = query.Where(" status=2 or (status=1 and user_id=? )", userId)
	} else {
		query = query.Where(" status=2")
	}
	err := query.
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&comments).Error
	fmt.Println(err)
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return comments, nil
}

// 获取多个回复总数
func (s *Comment) GetTotalCountByRootIds(ctx context.Context, rootIds []string, opts ...igorm.Option) ([]*models.ExpoCommentView, error) {
	var result []*models.ExpoCommentView
	err := s.session(ctx, opts...).
		Model(&models.ExpoComment{}).
		Select("root_id,count(root_id) as count").
		Where("root_id in ? and status=2", rootIds).
		Group("root_id").Find(&result).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return result, nil
}

// 后台评价列表
func (s *Comment) AdminPageCommentPageList(ctx context.Context, expoId int64, status, page, size int, keyword string, dateType int, startTime, endTime string, opts ...igorm.Option) ([]*models.ExpoComment, int64, error) {
	var comments []*models.ExpoComment
	query := s.session(ctx, opts...).Where(" content_type=1")
	if expoId > 0 {
		query = query.Where(" expo_Id=? ", expoId)
	}
	if status > 0 {
		query = query.Where(" status=? ", status)
	}
	if len(keyword) > 0 {
		query = query.Where(" user_id= ? or id=? ", keyword, keyword)
	}
	var dateStart = time.Now().Add(-time.Hour * 24 * 90)
	var dateEnd = time.Now().Add(time.Hour * 24)
	if len(startTime) > 0 {
		timeStart, err := time.Parse("2006-01-02", startTime)
		if err == nil {
			dateStart = timeStart
		}
	}
	if len(endTime) > 0 {
		timeEnd, err := time.Parse("2006-01-02", startTime)
		if err == nil {
			dateEnd = timeEnd
		}
	}
	if dateType == 0 {
		query = query.Where(" created_at >= ? and created_at <= ?", dateStart, dateEnd)
	} else {
		query = query.Where(" audit_at >= ? and audit_at <= ?", dateStart, dateEnd)
	}
	var total int64
	query.Model(&models.ExpoComment{}).Count(&total)
	err := query.
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&comments).Error

	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}
	return comments, total, nil
}

// AdminCommentEdit 审核评价
func (s *Comment) AdminCommentAudit(ctx context.Context, commentId string, status int, createName string, refuseReason string, opts ...igorm.Option) error {
	now := time.Now().UTC()
	err := s.session(ctx, opts...).
		Model(&models.ExpoComment{}).
		Where("id = ?", commentId).
		Update("status", status).
		Update("updated_at", now).
		Update("audit_at", now).
		Update("audit_name", createName).
		Update("refuse_reason", refuseReason).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

func (s *Comment) GetCommentSingle(ctx context.Context, commentId string, opts ...igorm.Option) (*models.ExpoComment, error) {
	var result models.ExpoComment
	err := s.session(ctx, opts...).Where("id=?", commentId).First(&result).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &result, nil
}
