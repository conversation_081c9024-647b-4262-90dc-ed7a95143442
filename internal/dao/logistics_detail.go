package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type LogisticsDetail struct {
	*DBModel
}

func NewLogisticsDetail(db *gorm.DB) *LogisticsDetail {
	return &LogisticsDetail{
		DBModel: NewDBModel(db),
	}
}

func (s *LogisticsDetail) Add(ctx context.Context, in *models.LogisticsDetail, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *LogisticsDetail) FindByOrderNo(ctx context.Context, orderNo string, opts ...igorm.Option) ([]*models.LogisticsDetail, error) {
	var out []*models.LogisticsDetail
	err := s.session(ctx, opts...).
		Where("order_no = ?", orderNo).
		Order("event_time desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *LogisticsDetail) SetTrackingNoByOrderNo(ctx context.Context, orderNo string, trackingNo string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.LogisticsDetail{}).
		Where("order_no = ? and tracking_no = ?", orderNo, "").
		Update("tracking_no", trackingNo).Error
	return ormhelper.WrapErr(err)
}

// FindByTrackingNo 根据快递单号查找物流详情
func (s *LogisticsDetail) FindByTrackingNo(ctx context.Context, trackingNo string, opts ...igorm.Option) ([]*models.LogisticsDetail, error) {
	var out []*models.LogisticsDetail
	err := s.session(ctx, opts...).
		Where("tracking_no = ?", trackingNo).
		Order("event_time desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// AddIfNotExists 只在记录不存在时添加物流详情（避免重复）
func (s *LogisticsDetail) AddIfNotExists(ctx context.Context, in *models.LogisticsDetail, opts ...igorm.Option) error {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.LogisticsDetail{}).
		Where("delivery_id = ?", in.DeliveryId).
		Count(&count).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}

	// 如果记录已存在，则跳过
	if count > 0 {
		return nil
	}

	// 记录不存在，则添加
	err = s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}
