package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoExhibitorEmployee struct {
	*DBModel
}

func NewExpoExhibitorEmployee(db *gorm.DB) *ExpoExhibitorEmployee {
	return &ExpoExhibitorEmployee{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoExhibitorEmployee) Exists(ctx context.Context, expoId, exhibitorId int64, userId string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).Model(&models.ExpoExhibitorEmployee{}).
		Where("expo_id = ? and exhibitor_id = ? and user_id = ?", expoId, exhibitorId, userId).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitorEmployee) Add(ctx context.Context, in *models.ExpoExhibitorEmployee, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoExhibitorEmployee) SetEnable(ctx context.Context, exhibitorId, id int64, enable bool, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitorEmployee{}).
		Where("id = ? and exhibitor_id = ?", id, exhibitorId).
		Update("enable", enable).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoExhibitorEmployee) FindByExhibitorIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.ExpoExhibitorEmployee, error) {
	var out []*models.ExpoExhibitorEmployee
	err := s.session(ctx, opts...).
		Where("exhibitor_id in ?", ids).Where("enable = ?", true).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// FindByExpoIdPage 根据展会ID分页查询参展商员工
func (s *ExpoExhibitorEmployee) FindByExpoIdPage(ctx context.Context, expoId int64, page, size int, opts ...igorm.Option) ([]*models.ExpoExhibitorEmployee, error) {
	var out []*models.ExpoExhibitorEmployee
	offset := (page - 1) * size
	err := s.session(ctx, opts...).
		Where("expo_id = ? AND enable = ?", expoId, true).
		Order("created_at desc").
		Limit(size).
		Offset(offset).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// CountByExpoId 统计展会参展商员工总数
func (s *ExpoExhibitorEmployee) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitorEmployee{}).
		Where("expo_id = ? AND enable = ?", expoId, true).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

// CountByExhibitorId 统计指定参展商的员工数量
func (s *ExpoExhibitorEmployee) CountByExhibitorId(ctx context.Context, exhibitorId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoExhibitorEmployee{}).
		Where("exhibitor_id = ? AND enable = ?", exhibitorId, true).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoExhibitorEmployee) Delete(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("id =?", id).
		Delete(&models.ExpoExhibitorEmployee{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoExhibitorEmployee) DeleteByUserId(ctx context.Context, expoId, exhibitorId int64, userId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and exhibitor_id = ? and user_id = ?", expoId, exhibitorId, userId).
		Delete(&models.ExpoExhibitorEmployee{}).Error
	return ormhelper.WrapErr(err)
}
