package dao

import (
	"context"

	"api-expo/api/common"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoCommunity struct {
	*DBModel
}

func NewExpoCommunity(db *gorm.DB) *ExpoCommunity {
	return &ExpoCommunity{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoCommunity) FindEnable(ctx context.Context, opts ...igorm.Option) ([]*models.ExpoCommunity, error) {
	var out []*models.ExpoCommunity
	err := s.session(ctx, opts...).
		Where("enable = 1").
		Find(&out).Error
	return out, err
}

func (s *ExpoCommunity) Add(ctx context.Context, in *models.ExpoCommunity, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).
		Select("expo_id", "description", "enable", "creator", "extra").
		Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoCommunity) Get(ctx context.Context, expoId int64, opts ...igorm.Option) (*models.ExpoCommunity, error) {
	var out models.ExpoCommunity
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Order("created_at DESC").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *ExpoCommunity) Update(ctx context.Context, in *models.ExpoCommunity, opts ...igorm.Option) (*common.EmptyReply, error) {
	err := s.session(ctx, opts...).
		Select("expo_id", "description", "enable", "extra", "creator", "topic_id").
		Where("expo_id = ?", in.ExpoId).
		Updates(in).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &common.EmptyReply{}, nil
}

func (s *ExpoCommunity) GetByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (*models.ExpoCommunity, error) {
	var out models.ExpoCommunity
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *ExpoCommunity) SetEnable(ctx context.Context, expoId int64, enable bool, opts ...igorm.Option) (*common.EmptyReply, error) {
	err := s.session(ctx, opts...).
		Model(&models.ExpoCommunity{}).
		Where("expo_id = ?", expoId).
		Update("enable", enable).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &common.EmptyReply{}, nil
}

func (s *ExpoCommunity) Delete(ctx context.Context, expoId int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id =?", expoId).
		Delete(&models.ExpoCommunity{}).Error
	return ormhelper.WrapErr(err)
}
