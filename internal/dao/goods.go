package dao

import (
	"context"
	"fmt"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Goods struct {
	*DBModel
}

func NewGoods(db *gorm.DB) *Goods {
	return &Goods{
		DBModel: NewDBModel(db),
	}
}

func (s *Goods) Add(ctx context.Context, in *models.Goods, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *Goods) Total(ctx context.Context, opts ...igorm.Option) (int64, error) {
	var total int64
	err := s.session(ctx, opts...).
		Model(&models.Goods{}).
		Count(&total).Error
	return total, ormhelper.WrapErr(err)
}

func (s *Goods) Update(ctx context.Context, in *models.Goods, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Where("goods_id = ?", in.GoodsId).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *Goods) Get(ctx context.Context, goodsId string, opts ...igorm.Option) (*models.Goods, error) {
	var goods models.Goods
	err := s.session(ctx, opts...).
		Where("goods_id = ?", goodsId).
		First(&goods).Error
	return &goods, ormhelper.WrapErr(err)
}

func (s *Goods) List(ctx context.Context, keyword string, status v1.GoodsStatus, size, page int, opts ...igorm.Option) ([]*models.Goods, int64, error) {
	var (
		goods   []*models.Goods
		session = s.session(ctx, opts...).Model(&models.Goods{})
		total   int64
	)

	if status >= 0 {
		session.Where("status = ?", status)
	}

	if keyword != "" {
		session = session.Where("name like ?", fmt.Sprintf("%%%s%%", keyword))
	}

	err := session.
		Count(&total).
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&goods).Error
	return goods, total, ormhelper.WrapErr(err)
}

func (s *Goods) FindByGoodsIds(ctx context.Context, goodsIds []string, opts ...igorm.Option) ([]*models.Goods, error) {
	var goods []*models.Goods
	err := s.session(ctx, opts...).
		Where("goods_id in ?", goodsIds).
		Find(&goods).Error
	return goods, ormhelper.WrapErr(err)
}

func (s *Goods) Delete(ctx context.Context, goodsId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("goods_id = ?", goodsId).
		Delete(&models.Goods{}).Error
	return ormhelper.WrapErr(err)
}

func (s *Goods) UpdateStatus(ctx context.Context, goodsId string, status v1.GoodsStatus, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Goods{}).
		Where("goods_id = ?", goodsId).
		Update("status", status).Error
	return ormhelper.WrapErr(err)
}
