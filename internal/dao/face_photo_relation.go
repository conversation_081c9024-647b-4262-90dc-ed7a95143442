package dao

import (
	"context"
	"fmt"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

// FacePhotoRelation 人脸照片关联DAO
type FacePhotoRelation struct {
	*DBModel
}

// NewFacePhotoRelation 创建人脸照片关联DAO实例
func NewFacePhotoRelation(db *gorm.DB) *FacePhotoRelation {
	return &FacePhotoRelation{
		DBModel: NewDBModel(db),
	}
}

// session 重写session方法以支持事务
func (d *FacePhotoRelation) session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	// 如果上下文中有事务，使用事务
	if tx, ok := ctx.Value("tx").(*gorm.DB); ok {
		return igorm.NewOptions(tx, opts...).Session().WithContext(ctx)
	}
	return d.DBModel.session(ctx, opts...)
}

// Create 创建人脸照片关联
func (d *FacePhotoRelation) Create(ctx context.Context, relation *models.FacePhotoRelation, opts ...igorm.Option) error {
	if relation == nil {
		return ormhelper.WrapErr(fmt.Errorf("人脸照片关联数据不能为空"))
	}
	err := d.session(ctx, opts...).Create(relation).Error
	return ormhelper.WrapErr(err)
}

// BatchCreate 批量创建人脸照片关联
func (d *FacePhotoRelation) BatchCreate(ctx context.Context, relations []*models.FacePhotoRelation, opts ...igorm.Option) error {
	if len(relations) == 0 {
		return nil
	}

	// 分批创建，避免单次插入数据过多
	batchSize := 100
	for i := 0; i < len(relations); i += batchSize {
		end := i + batchSize
		if end > len(relations) {
			end = len(relations)
		}

		batch := relations[i:end]
		err := d.session(ctx, opts...).CreateInBatches(batch, batchSize).Error
		if err != nil {
			return ormhelper.WrapErr(err)
		}
	}
	return nil
}

// GetByID 根据ID获取人脸照片关联
func (d *FacePhotoRelation) GetByID(ctx context.Context, id uint64, opts ...igorm.Option) (*models.FacePhotoRelation, error) {
	if id == 0 {
		return nil, ormhelper.WrapErr(fmt.Errorf("人脸照片关联ID不能为空"))
	}

	var relation models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("id = ?", id).
		First(&relation).Error
	return &relation, ormhelper.WrapErr(err)
}

// GetByFaceBusinessID 根据业务人脸ID获取关联信息
func (d *FacePhotoRelation) GetByFaceBusinessID(ctx context.Context, faceBusinessID string, opts ...igorm.Option) (*models.FacePhotoRelation, error) {
	if faceBusinessID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("业务人脸ID不能为空"))
	}

	var relation models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("face_business_id = ?", faceBusinessID).
		First(&relation).Error
	return &relation, ormhelper.WrapErr(err)
}

// GetActiveByFaceBusinessID 根据业务人脸ID获取正常状态的关联信息
func (d *FacePhotoRelation) GetActiveByFaceBusinessID(ctx context.Context, faceBusinessID string, opts ...igorm.Option) (*models.FacePhotoRelation, error) {
	if faceBusinessID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("业务人脸ID不能为空"))
	}

	var relation models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("face_business_id = ? AND status = ?", faceBusinessID, models.FacePhotoStatusNormal).
		First(&relation).Error
	return &relation, ormhelper.WrapErr(err)
}

// GetByTencentPersonID 根据腾讯云PersonID获取关联信息
func (d *FacePhotoRelation) GetByTencentPersonID(ctx context.Context, tencentPersonID string, opts ...igorm.Option) (*models.FacePhotoRelation, error) {
	if tencentPersonID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("腾讯云PersonID不能为空"))
	}

	var relation models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("tencent_person_id = ?", tencentPersonID).
		First(&relation).Error
	return &relation, ormhelper.WrapErr(err)
}

// GetByTencentPersonIDs 根据腾讯云PersonID列表批量获取
func (d *FacePhotoRelation) GetByTencentPersonIDs(ctx context.Context, tencentPersonIDs []string, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if len(tencentPersonIDs) == 0 {
		return []*models.FacePhotoRelation{}, nil
	}

	var relations []*models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("tencent_person_id IN ?", tencentPersonIDs).
		Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// GetActiveByTencentPersonIDs 根据腾讯云PersonID列表批量获取正常状态的关联信息
func (d *FacePhotoRelation) GetActiveByTencentPersonIDs(ctx context.Context, tencentPersonIDs []string, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if len(tencentPersonIDs) == 0 {
		return []*models.FacePhotoRelation{}, nil
	}

	var relations []*models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("tencent_person_id IN ? AND status = ?", tencentPersonIDs, models.FacePhotoStatusNormal).
		Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// GetByOriginalPhotoURL 根据原始照片URL获取所有人脸
func (d *FacePhotoRelation) GetByOriginalPhotoURL(ctx context.Context, originalPhotoURL string, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if originalPhotoURL == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("原始照片URL不能为空"))
	}

	var relations []*models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("original_photo_url = ?", originalPhotoURL).
		Order("created_at ASC").
		Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// GetActiveByOriginalPhotoURL 根据原始照片URL获取正常状态的所有人脸
func (d *FacePhotoRelation) GetActiveByOriginalPhotoURL(ctx context.Context, originalPhotoURL string, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if originalPhotoURL == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("原始照片URL不能为空"))
	}

	var relations []*models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("original_photo_url = ? AND status = ?", originalPhotoURL, models.FacePhotoStatusNormal).
		Order("created_at ASC").
		Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// GetByGroupID 根据人员库ID获取人脸列表
func (d *FacePhotoRelation) GetByGroupID(ctx context.Context, groupID string, limit, offset int, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	query := d.session(ctx, opts...).
		Where("group_id = ?", groupID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	var relations []*models.FacePhotoRelation
	err := query.Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// GetActiveByGroupID 根据人员库ID获取正常状态的人脸列表
func (d *FacePhotoRelation) GetActiveByGroupID(ctx context.Context, groupID string, limit, offset int, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	query := d.session(ctx, opts...).
		Where("group_id = ? AND status = ?", groupID, models.FacePhotoStatusNormal).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	var relations []*models.FacePhotoRelation
	err := query.Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// List 获取人脸照片关联列表
func (d *FacePhotoRelation) List(ctx context.Context, params *models.FacePhotoQueryRequest, opts ...igorm.Option) ([]*models.FacePhotoRelation, int64, error) {
	if params == nil {
		params = &models.FacePhotoQueryRequest{}
	}

	// 设置默认分页参数
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}

	query := d.session(ctx, opts...).Model(&models.FacePhotoRelation{})

	// 条件筛选
	if params.GroupID != "" {
		query = query.Where("group_id = ?", params.GroupID)
	}
	if params.OriginalPhotoURL != "" {
		query = query.Where("original_photo_url = ?", params.OriginalPhotoURL)
	}
	if params.UploadUserID != "" {
		query = query.Where("upload_user_id = ?", params.UploadUserID)
	}
	if params.Status != 0 {
		query = query.Where("status = ?", params.Status)
	}
	if params.MinQualityScore > 0 {
		query = query.Where("quality_score >= ?", params.MinQualityScore)
	}

	// 统计总数
	var total int64
	countQuery := query
	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	// 分页查询
	var relations []*models.FacePhotoRelation
	offset := (params.Page - 1) * params.PageSize
	err = query.Order("created_at DESC").
		Limit(params.PageSize).
		Offset(offset).
		Find(&relations).Error

	return relations, total, ormhelper.WrapErr(err)
}

// Update 更新人脸照片关联信息
func (d *FacePhotoRelation) Update(ctx context.Context, id uint64, updates map[string]interface{}, opts ...igorm.Option) error {
	if id == 0 {
		return ormhelper.WrapErr(fmt.Errorf("人脸照片关联ID不能为空"))
	}
	if len(updates) == 0 {
		return ormhelper.WrapErr(fmt.Errorf("更新数据不能为空"))
	}

	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("id = ?", id).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// UpdateByFaceBusinessID 根据业务人脸ID更新关联信息
func (d *FacePhotoRelation) UpdateByFaceBusinessID(ctx context.Context, faceBusinessID string, updates map[string]interface{}, opts ...igorm.Option) error {
	if faceBusinessID == "" {
		return ormhelper.WrapErr(fmt.Errorf("业务人脸ID不能为空"))
	}
	if len(updates) == 0 {
		return ormhelper.WrapErr(fmt.Errorf("更新数据不能为空"))
	}

	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("face_business_id = ?", faceBusinessID).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// Delete 软删除人脸照片关联
func (d *FacePhotoRelation) Delete(ctx context.Context, id uint64, opts ...igorm.Option) error {
	if id == 0 {
		return ormhelper.WrapErr(fmt.Errorf("人脸照片关联ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FacePhotoStatusDeleted,
	}
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("id = ?", id).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// DeleteByFaceBusinessID 根据业务人脸ID软删除关联
func (d *FacePhotoRelation) DeleteByFaceBusinessID(ctx context.Context, faceBusinessID string, opts ...igorm.Option) error {
	if faceBusinessID == "" {
		return ormhelper.WrapErr(fmt.Errorf("业务人脸ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FacePhotoStatusDeleted,
	}
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("face_business_id = ?", faceBusinessID).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// DeleteByGroupID 根据人员库ID批量软删除关联
func (d *FacePhotoRelation) DeleteByGroupID(ctx context.Context, groupID string, opts ...igorm.Option) error {
	if groupID == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FacePhotoStatusDeleted,
	}
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("group_id = ?", groupID).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// CountByGroupID 统计人员库中的人脸数量
func (d *FacePhotoRelation) CountByGroupID(ctx context.Context, groupID string, opts ...igorm.Option) (int64, error) {
	if groupID == "" {
		return 0, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var count int64
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("group_id = ? AND status = ?", groupID, models.FacePhotoStatusNormal).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

// CountByOriginalPhotoURL 统计原始照片中的人脸数量
func (d *FacePhotoRelation) CountByOriginalPhotoURL(ctx context.Context, originalPhotoURL string, opts ...igorm.Option) (int64, error) {
	if originalPhotoURL == "" {
		return 0, ormhelper.WrapErr(fmt.Errorf("原始照片URL不能为空"))
	}

	var count int64
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("original_photo_url = ? AND status = ?", originalPhotoURL, models.FacePhotoStatusNormal).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

// GetHighQualityFaces 获取高质量人脸列表
func (d *FacePhotoRelation) GetHighQualityFaces(ctx context.Context, groupID string, minQuality float64, limit int, opts ...igorm.Option) ([]*models.FacePhotoRelation, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}
	if minQuality <= 0 {
		minQuality = 60.0 // 默认最小质量分数
	}
	if limit <= 0 {
		limit = 100
	}

	var relations []*models.FacePhotoRelation
	err := d.session(ctx, opts...).
		Where("group_id = ? AND status = ? AND quality_score >= ?", groupID, models.FacePhotoStatusNormal, minQuality).
		Order("quality_score DESC, created_at DESC").
		Limit(limit).
		Find(&relations).Error
	return relations, ormhelper.WrapErr(err)
}

// ExistsByTencentPersonID 检查腾讯云PersonID是否已存在
func (d *FacePhotoRelation) ExistsByTencentPersonID(ctx context.Context, tencentPersonID string, opts ...igorm.Option) (bool, error) {
	if tencentPersonID == "" {
		return false, ormhelper.WrapErr(fmt.Errorf("腾讯云PersonID不能为空"))
	}

	var count int64
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("tencent_person_id = ?", tencentPersonID).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

// ExistsByFaceBusinessID 检查业务人脸ID是否已存在
func (d *FacePhotoRelation) ExistsByFaceBusinessID(ctx context.Context, faceBusinessID string, opts ...igorm.Option) (bool, error) {
	if faceBusinessID == "" {
		return false, ormhelper.WrapErr(fmt.Errorf("业务人脸ID不能为空"))
	}

	var count int64
	err := d.session(ctx, opts...).
		Model(&models.FacePhotoRelation{}).
		Where("face_business_id = ?", faceBusinessID).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}
