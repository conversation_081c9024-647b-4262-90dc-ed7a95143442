package dao

import (
	"context"
	"fmt"
	"time"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type CommentTrans struct {
	*DBModel
}

func NewCommentTrans(db *gorm.DB) *CommentTrans {
	return &CommentTrans{
		DBModel: NewDBModel(db),
	}
}

func (s *CommentTrans) Add(ctx context.Context, in models.ExpoCommentTran, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}
func (s *CommentTrans) GetSingleByCommentIdLanguageCode(ctx context.Context, commentId, languageCode string, opts ...igorm.Option) (*models.ExpoCommentTran, error) {
	var single *models.ExpoCommentTran
	err := s.session(ctx, opts...).Where("comment_id = ? and language_code=?", commentId, languageCode).FirstOrInit(&single).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return single, nil
}

func (s *CommentTrans) GetListByCommentId(ctx context.Context, commentIds []string, languageCode string, opts ...igorm.Option) ([]*models.ExpoCommentTran, error) {
	var list []*models.ExpoCommentTran
	err := s.session(ctx, opts...).Where("comment_id IN ? and language_code=?", commentIds, languageCode).Find(&list).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return list, nil
}

// GetListSingleByCommentId 获取单个帖子的翻译
func (s *CommentTrans) GetListSingleByCommentId(ctx context.Context, commentId string, opts ...igorm.Option) ([]*models.ExpoCommentTran, error) {
	var list []*models.ExpoCommentTran
	err := s.session(ctx, opts...).Where("comment_id=?", commentId).Find(&list).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return list, nil
}

// 修改翻译
func (s *CommentTrans) UpdateOrInsertTans(ctx context.Context, commentId, languageCode string, model models.ExpoCommentTran, opts ...igorm.Option) error {
	single, err := s.GetSingleByCommentIdLanguageCode(ctx, commentId, languageCode)
	now := time.Now().UTC()
	if err != nil {
		return err
	}
	if single.ID == 0 {
		fmt.Println(1111111111111111)
		return s.Add(ctx, model)
	} else {
		err = s.session(ctx, opts...).Model(&models.ExpoCommentTran{}).Where("comment_id=? and language_code=?", commentId, languageCode).
			Update("content", model.Content).
			Update("updated_at", now).Error
		return err
	}
}
func (s *CommentTrans) UpdateTrans(ctx context.Context, transId int64, content string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Where("id=?", transId).
		Model(&models.ExpoCommentTran{}).
		Update("content", content).
		Update("updated_at", time.Now().UTC()).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}
