package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Config struct {
	*DBModel
}

func NewConfig(db *gorm.DB) *Config {
	return &Config{
		DBModel: NewDBModel(db),
	}
}

func (s *Config) Get(ctx context.Context, configType string, opts ...igorm.Option) (*models.Config, error) {
	var comment models.Config
	err := s.session(ctx, opts...).Where("config_type = ?", configType).First(&comment).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &comment, nil
}

func (s *Config) GetMore(ctx context.Context, configTypeIds []string, opts ...igorm.Option) ([]*models.Config, error) {
	var comment []*models.Config
	err := s.session(ctx, opts...).Where("config_type in ?", configTypeIds).Find(&comment).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return comment, nil
}
