package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoGuide struct {
	*DBModel
}

func NewExpoGuide(db *gorm.DB) *ExpoGuide {
	return &ExpoGuide{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoGuide) Add(ctx context.Context, in *models.ExpoGuide, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoGuide) GetByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (*models.ExpoGuide, error) {
	var out models.ExpoGuide
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("created_at desc").
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoGuide) Get(ctx context.Context, expoId, id int64, opts ...igorm.Option) (*models.ExpoGuide, error) {
	var out models.ExpoGuide
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoGuide) Update(ctx context.Context, in *models.ExpoGuide, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Omit("created_at").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoGuide) UpdateEnable(ctx context.Context, expoId, id int64, enable bool, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoGuide{}).
		Where("expo_id = ? and id = ?", expoId, id).
		Update("enable", enable).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoGuide) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoGuide{}).
		Where("expo_id = ?", expoId).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoGuide) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoGuide, error) {
	var out []*models.ExpoGuide
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("level asc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoGuide) FindByPage(ctx context.Context, expoId int64, page, size int, opts ...igorm.Option) ([]*models.ExpoGuide, error) {
	var out []*models.ExpoGuide
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoGuide) Delete(ctx context.Context, expoId, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		Delete(&models.ExpoGuide{}).Error
	return ormhelper.WrapErr(err)
}
