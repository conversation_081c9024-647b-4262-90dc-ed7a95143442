package dao

import (
	"context"
	"time"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Participant struct {
	*DBModel
}

func NewParticipant(db *gorm.DB) *Participant {
	return &Participant{
		DBModel: NewDBModel(db),
	}
}

func (s *Participant) Add(ctx context.Context, in *models.Participant, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *Participant) ExistsByUserId(ctx context.Context, expoId int64, userId string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Participant{}).
		Where("expo_id = ? and user_id = ? and apply_status != 3 and ticket_status != 500", expoId, userId).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

func (s *Participant) ExistsByPhone(ctx context.Context, expoId int64, phoneAreaCode, phone string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Participant{}).
		Where("expo_id = ? and area_code = ? and phone = ? and apply_status != 3 and ticket_status != 500", expoId, phoneAreaCode, phone).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

func (s *Participant) CountBySubIdentity(ctx context.Context, expoId int64, subIdentityCodes []v1.SubIdentity, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Participant{}).
		Where("expo_id = ? and sub_identity_code in ? and apply_status = ?", expoId, subIdentityCodes, v1.ApplyAudit_APPLY_AUDIT_Pass).Where("ticket_status != ?", models.TicketStatusDel).Where("is_employee = ?", models.IsEmployeeNo).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *Participant) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *Participant) FindBySubIdentity(ctx context.Context, expoId int64, subIdentityCodes []v1.SubIdentity, size int, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("expo_id = ? and sub_identity_code in ?  and apply_status = ?", expoId, subIdentityCodes, v1.ApplyAudit_APPLY_AUDIT_Pass).Where("is_employee = ?", models.IsEmployeeNo).
		Order("created_at desc").
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// GetByExpoID 根据展会ID获取参会者列表
func (s *Participant) GetByExpoID(ctx context.Context, expoID int64, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoID).
		Order("created_at DESC").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Participant) FindByUserId(ctx context.Context, userId string, expoId int64, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("user_id = ? and expo_id = ? and apply_status = ?", userId, expoId, v1.ApplyAudit_APPLY_AUDIT_Pass).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *Participant) FindByExpoIdWithKeyword(ctx context.Context, expoId int64, subIdentityCodes []v1.SubIdentity, keyword string, page, size int, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant

	db := s.session(ctx, opts...).
		Where("expo_id = ? and apply_status = ? and ticket_status != ?", expoId, v1.ApplyAudit_APPLY_AUDIT_Pass, models.TicketStatusDel).Where("is_employee = ?", models.IsEmployeeNo)

	if len(subIdentityCodes) > 0 && keyword == "" {
		db = db.Where("sub_identity_code in ?", subIdentityCodes)
	}

	if keyword != "" {
		q := "%" + keyword + "%"
		db = db.Where("first_name LIKE ? OR last_name LIKE ?", q, q)
	}

	err := db.
		Order("created_at DESC").
		Limit(size).Offset((page - 1) * size).
		Find(&out).Error

	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// FindByTicketIds 根据票夹ID列表查询参与者
func (s *Participant) FindByTicketIds(ctx context.Context, ticketIds []int64, opts ...igorm.Option) ([]*models.Participant, error) {
	if len(ticketIds) == 0 {
		return []*models.Participant{}, nil
	}

	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("id IN ?", ticketIds).Where("ticket_status != ?", models.TicketStatusDel).Where("apply_status = ?", v1.ApplyAudit_APPLY_AUDIT_Pass).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// GetByTicketId 根据票夹ID查询参与者
func (s *Participant) GetByTicketId(ctx context.Context, ticketId int64, opts ...igorm.Option) (*models.Participant, error) {
	var out models.Participant
	err := s.session(ctx, opts...).
		Where("id = ?", ticketId).
		First(&out).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Participant) GetByTicketIdAndUserId(ctx context.Context, ticketId int64, userId string, opts ...igorm.Option) (*models.Participant, error) {
	var out models.Participant
	err := s.session(ctx, opts...).
		Where("id = ? and user_id=?", ticketId, userId).
		First(&out).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Participant) ParticipantByPhoneOrEmailExist(ctx context.Context, areaCode, phone, email string, expoId int64, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(models.Participant{}).
		Where("expo_id=? and ((area_code=? and  phone=?) or email =?) and apply_status != 3 and ticket_status != 500", expoId, areaCode, phone, email).
		Count(&count).Error
	if err != nil {
		return false, ormhelper.WrapErr(err)
	}
	return count > 0, nil
}

func (s *Participant) ApplyAudit(ctx context.Context, applyId int64, status v1.ApplyAudit, creator, applyUserId string, IsResister bool, refuse v1.ApplyRefuseReason, ticketCode string, opts ...igorm.Option) error {
	return s.session(ctx, opts...).
		Model(models.Participant{}).
		Where("id = ?", applyId).
		Updates(models.Participant{
			ApplyStatus:  status,
			AuditUser:    creator,
			AuditAt:      time.Now().UTC(),
			RefuseReason: refuse,
			ApplyUserid:  applyUserId,
			IsResister:   IsResister,
			Code:         ticketCode,
		}).Error

}

// FindByUserIdAndExpoId 根据用户ID和展会ID查询参与者信息
func (s *Participant) FindByUserIdAndExpoId(ctx context.Context, userId string, expoId int64, opts ...igorm.Option) (*models.Participant, error) {
	var participant models.Participant
	err := s.session(ctx, opts...).
		Where("user_id = ? AND expo_id = ?", userId, expoId).
		First(&participant).Error
	return &participant, ormhelper.WrapErr(err)
}

// FindByUserIdsAndExpoId 根据用户ID列表和展会ID批量查询参与者信息
func (s *Participant) FindByUserIdsAndExpoId(ctx context.Context, userIds []string, expoId int64, opts ...igorm.Option) ([]*models.Participant, error) {
	if len(userIds) == 0 {
		return []*models.Participant{}, nil
	}

	var participants []*models.Participant
	err := s.session(ctx, opts...).
		Where("user_id IN ? AND expo_id = ?", userIds, expoId).
		Find(&participants).Error
	return participants, ormhelper.WrapErr(err)
}

// 根据userid查询参加展会userid
func (s *Participant) FindUserIdByUserIdsAndExpoId(ctx context.Context, userIds []string, expoId int64, opts ...igorm.Option) ([]*string, error) {
	if len(userIds) == 0 {
		return []*string{}, nil
	}
	var participants []*string
	err := s.session(ctx, opts...).
		Model(models.Participant{}).
		Select("user_id").
		Where("user_id IN ? AND expo_id = ? and apply_status=2", userIds, expoId).
		Find(&participants).Error
	return participants, ormhelper.WrapErr(err)
}

// FindByExpoIdAndUserId 根据展会ID和用户ID查询单个参与者
func (s *Participant) FindByExpoIdAndUserId(ctx context.Context, expoId int64, userId string, opts ...igorm.Option) (*models.Participant, error) {
	var out models.Participant
	err := s.session(ctx, opts...).
		Where("expo_id = ? AND apply_userid = ? AND apply_status = ? AND ticket_status != ?",
			expoId, userId, v1.ApplyAudit_APPLY_AUDIT_Pass, models.TicketStatusDel).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

// GetValidParticipantsByExpoID 根据展会ID获取有效的参会者列表（已通过审核且未删除）
func (s *Participant) GetValidParticipantsByExpoID(ctx context.Context, expoID int64, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("expo_id = ? AND apply_status = ? AND ticket_status != ? AND is_employee = ?",
			expoID, v1.ApplyAudit_APPLY_AUDIT_Pass, models.TicketStatusDel, models.IsEmployeeNo).
		Order("created_at DESC").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Participant) UpdateIsEmployee(ctx context.Context, applyId int64, opts ...igorm.Option) error {
	return s.session(ctx, opts...).Model(&models.Participant{}).Where(" id=?", applyId).Update("is_employee", 1).Error
}
