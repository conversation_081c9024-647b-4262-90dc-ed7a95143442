package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type CommentImage struct {
	*DBModel
}

func NewCommentImage(db *gorm.DB) *CommentImage {
	return &CommentImage{
		DBModel: NewDBModel(db),
	}
}

func (s *CommentImage) Add(ctx context.Context, in []*models.ExpoCommentImage, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

// GetListByCommentId 评价Id获取图片信息
func (s *CommentImage) GetListByCommentId(ctx context.Context, commentIds []string, opts ...igorm.Option) ([]*models.ExpoCommentImage, error) {
	var images []*models.ExpoCommentImage
	err := s.session(ctx, opts...).Where("comment_id in ?", commentIds).Find(&images).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return images, nil
}
