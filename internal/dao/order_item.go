package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type OrderItem struct {
	*DBModel
}

func NewOrderItem(db *gorm.DB) *OrderItem {
	return &OrderItem{
		DBModel: NewDBModel(db),
	}
}

func (s *OrderItem) BatchAdd(ctx context.Context, in []*models.OrderItem, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).CreateInBatches(in, len(in)).Error
	return ormhelper.WrapErr(err)
}

func (s *OrderItem) FindByOrderNo(ctx context.Context, orderNo string, opts ...igorm.Option) ([]*models.OrderItem, error) {
	var out []*models.OrderItem
	err := s.session(ctx, opts...).
		Where("order_no = ?", orderNo).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *OrderItem) FindByOrderNos(ctx context.Context, orderNos []string, opts ...igorm.Option) ([]*models.OrderItem, error) {
	var out []*models.OrderItem
	err := s.session(ctx, opts...).
		Where("order_no in ?", orderNos).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
