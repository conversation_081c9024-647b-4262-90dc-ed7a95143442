package dao

import (
	"context"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/ormhelper"
)

type CountryTranslate struct {
	*DBModel
}

func NewCountryTranslate(db StaticDB) *CountryTranslate {
	return &CountryTranslate{
		DBModel: NewDBModel(db),
	}
}

func (s *CountryTranslate) FindCountryTranslate(ctx context.Context, languageCode string) ([]*models.CountryTranslation, error) {
	var out []*models.CountryTranslation
	err := s.session(ctx).
		Where("LanguageCode = ?", languageCode).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *CountryTranslate) GetCountryTranslate(ctx context.Context, countryCode string, languageCode string) (*models.CountryTranslation, error) {
	var out models.CountryTranslation
	err := s.session(ctx).
		Where("CountryCode = ? and LanguageCode = ?", countryCode, languageCode).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}
