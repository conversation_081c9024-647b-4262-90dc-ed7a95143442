package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoReview struct {
	*DBModel
}

func NewExpoReview(db *gorm.DB) *ExpoReview {
	return &ExpoReview{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoReview) Add(ctx context.Context, in *models.ExpoReview, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoReview) GetById(ctx context.Context, expoId, id int64, opts ...igorm.Option) (*models.ExpoReview, error) {
	var out models.ExpoReview
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoReview) Update(ctx context.Context, in *models.ExpoReview, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Omit("created_at").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoReview) UpdateEnable(ctx context.Context, in *models.ExpoReview, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("enable").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoReview) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoReview{}).
		Where("expo_id = ?", expoId).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoReview) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoReview, error) {
	var out []*models.ExpoReview
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("created_at desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoReview) FindByPage(ctx context.Context, expoId int64, page, size int, opts ...igorm.Option) ([]*models.ExpoReview, error) {
	var out []*models.ExpoReview
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoReview) Delete(ctx context.Context, expoId, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		Delete(&models.ExpoReview{}).Error
	return ormhelper.WrapErr(err)
}
