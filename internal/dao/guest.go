package dao

import (
	"context"
	"fmt"
	"strconv"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Guest struct {
	*DBModel
}

func NewGuest(db *gorm.DB) *Guest {
	return &Guest{
		DBModel: NewDBModel(db),
	}
}

// Add 添加嘉宾
func (s *Guest) Add(ctx context.Context, in *models.Guest, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

// FindByIds 根据ID批量获取嘉宾信息
func (s *Guest) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.Guest, error) {
	var out []*models.Guest
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// FindByIdsWithUnscoped 根据ID批量获取嘉宾信息 包含删除的
func (s *Guest) FindByIdsWithUnscoped(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.Guest, error) {
	var out []*models.Guest
	err := s.session(ctx, opts...).
		Unscoped().
		Where("id in ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// Get 根据ID获取嘉宾信息
func (s *Guest) Get(ctx context.Context, id int64, opts ...igorm.Option) (*models.Guest, error) {
	var out models.Guest
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

// Update 更新嘉宾信息
func (s *Guest) Update(ctx context.Context, id int64, in *models.Guest, opts ...igorm.Option) (*common.EmptyReply, error) {
	err := s.session(ctx, opts...).
		Select("avatar", "name", "user_id", "wiki_number", "phone_area_code", "phone", "email", "wechat", "whats_app", "facebook", "twitter", "linkedin", "instagram", "reddit", "youtube", "telegram", "tiktok", "enable", "extra", "creator", "country_flag").
		Where("id =?", id).
		Updates(in).Error
	return &common.EmptyReply{}, ormhelper.WrapErr(err)
}

// SetEnable 设置嘉宾是否启用
func (s *Guest) SetEnable(ctx context.Context, id int64, enable bool, opts ...igorm.Option) (*common.EmptyReply, error) {
	err := s.session(ctx, opts...).
		Model(&models.Guest{}).
		Where("id =?", id).
		Update("enable", enable).Error
	return &common.EmptyReply{}, ormhelper.WrapErr(err)
}

// Delete 删除嘉宾
func (s *Guest) Delete(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("id =?", id).
		Delete(&models.Guest{}).Error
	return ormhelper.WrapErr(err)
}

// FindByPage 嘉宾列表
func (s *Guest) FindByPage(ctx context.Context, name string, email string, phone string, isHasUserId int32, page, size int32, opts ...igorm.Option) ([]*models.Guest, int64, error) {
	var (
		out     []*models.Guest
		total   int64
		session = s.session(ctx, opts...).Where("deleted_at is null")
	)

	// 应用其他过滤条件
	if name != "" {
		// 判断name 是否是数字类型，如果是数字类型，则id 精准匹配
		if isNumeric(name) {
			session = session.Where("id =?", name)
		} else {
			session = session.Where("name like ?", "%"+name+"%")
		}
	}

	if email != "" {
		session = session.Where("email like ?", "%"+email+"%")
	}

	if phone != "" {
		session = session.Where("phone like ?", "%"+phone+"%")
	}

	switch isHasUserId {
	case 1:
		session = session.Where("wiki_number <> ''")
	case 2:
		session = session.Where("wiki_number = ''")
	}

	err := session.
		Model(&models.Guest{}).
		Count(&total).
		Order("created_at desc").
		Limit(int(size)).
		Offset(int((page - 1) * size)).
		Find(&out).Error
	return out, total, ormhelper.WrapErr(err)
}

func (s *Guest) SearchWithExpoGuest(ctx context.Context, expoId int64, name, phone, email string, numberType v1.WikiNumberType, page, size int, opts ...igorm.Option) ([]*models.Guest, int64, error) {
	var (
		out     []*models.Guest
		session = s.session(ctx, opts...).
			Select("guest.id,guest.avatar,guest.name,guest.wiki_number,guest.user_id,guest.phone_area_code,guest.email,guest.phone,expo_guest.enable,expo_guest.creator,expo_guest.created_at").
			Model(&models.Guest{}).
			Unscoped().
			Joins("join expo_guest on guest.id = expo_guest.guest_id").
			Where("expo_guest.expo_id = ? and expo_guest.deleted_at is null", expoId)
		total int64
	)

	if name != "" {
		session = session.Where("guest.name like ?", fmt.Sprintf("%%%s%%", name))
	}

	if phone != "" {
		session = session.Where("guest.phone like ?", fmt.Sprintf("%%%s%%", phone))
	}

	if email != "" {
		session = session.Where("guest.email like ?", fmt.Sprintf("%%%s%%", email))
	}

	switch numberType {
	case v1.WikiNumberType_WikiNumber_HAS:
		session = session.Where("wiki_number != ''")
	case v1.WikiNumberType_WikiNumber_NO:
		session = session.Where("wiki_number = ''")
	}

	err := session.
		Count(&total).
		Order("expo_guest.created_at desc").
		Offset(size * (page - 1)).
		Limit(size).
		Find(&out).Error
	return out, total, ormhelper.WrapErr(err)
}

func (s *Guest) ListGuestsNotInExpo(ctx context.Context, name string, expoId int64, page, size int32, opts ...igorm.Option) ([]*models.Guest, int64, error) {
	var out []*models.Guest
	var total int64

	db := s.session(ctx, opts...)
	db = db.Select("DISTINCT g.*").
		Table("guest g").
		Joins("LEFT JOIN expo_guest eg ON eg.guest_id = g.id AND eg.expo_id = ?", expoId).
		Where("g.deleted_at IS NULL").
		Where("g.enable = TRUE").
		Where("NOT EXISTS (SELECT 1 FROM expo_guest eg WHERE eg.guest_id = g.id AND eg.expo_id = ? AND eg.deleted_at IS NULL)", expoId)

	if name != "" {
		db = db.Where("g.name like ?", "%"+name+"%")
	}

	err := db.
		Model(&models.Guest{}).
		Order("g.created_at desc").
		Count(&total).
		Limit(int(size)).
		Offset(int((page - 1) * size)).
		Find(&out).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}
	return out, total, nil
}

// ExistsByPhone 根据手机号查询嘉宾
func (s *Guest) ExistsByPhone(ctx context.Context, phone string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Guest{}).
		Where("phone = ?", phone).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

// ExistsByUserId 根据用户ID查询嘉宾
func (s *Guest) ExistsByUserId(ctx context.Context, userId string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Guest{}).
		Where("user_id =?", userId).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

// ExistsByEmail 根据邮箱查询嘉宾
func (s *Guest) ExistsByEmail(ctx context.Context, email string, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Guest{}).
		Where("email =?", email).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

func isNumeric(s string) bool {
	_, err := strconv.Atoi(s)
	return err == nil
}
