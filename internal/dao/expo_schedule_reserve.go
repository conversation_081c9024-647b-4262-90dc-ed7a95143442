package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoScheduleReserve struct {
	*DBModel
}

func NewExpoScheduleReserve(db *gorm.DB) *ExpoScheduleReserve {
	return &ExpoScheduleReserve{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoScheduleReserve) ExistsWithUnscoped(ctx context.Context, userId string, expoId, scheduleGuestId int64, opts ...igorm.Option) (bool, error) {
	var count int64
	err := s.session(ctx, opts...).
		Unscoped().
		Where("user_id = ? and expo_id = ? and schedule_guest_id = ?", userId, expoId, scheduleGuestId).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleReserve) GetWithUnscoped(ctx context.Context, userId string, expoId, scheduleGuestId int64, opts ...igorm.Option) (*models.ExpoScheduleReserve, error) {
	var out models.ExpoScheduleReserve
	err := s.session(ctx, opts...).
		Unscoped().
		Where("user_id = ? and expo_id = ? and schedule_guest_id = ?", userId, expoId, scheduleGuestId).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleReserve) Add(ctx context.Context, in *models.ExpoScheduleReserve, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).
		Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoScheduleReserve) Restore(ctx context.Context, userId string, expoId, scheduleGuestId int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Unscoped().
		Model(&models.ExpoScheduleReserve{}).
		Where("user_id = ? and expo_id = ? and schedule_guest_id = ?", userId, expoId, scheduleGuestId).
		Update("deleted_at", nil).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoScheduleReserve) FindByUserId(ctx context.Context, userId string, expoIds []int64, opts ...igorm.Option) ([]*models.ExpoScheduleReserve, error) {
	var out []*models.ExpoScheduleReserve
	err := s.session(ctx, opts...).
		Where("user_id = ? and expo_id in ?", userId, expoIds).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleReserve) DeleteByUserId(ctx context.Context, userId string, expoId, scheduleGuestId int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("user_id = ? and expo_id = ? and schedule_guest_id = ?", userId, expoId, scheduleGuestId).
		Delete(&models.ExpoScheduleReserve{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoScheduleReserve) FindByScheduleGuestIdsAndNotify(ctx context.Context, scheduleGuestIds []int64, opts ...igorm.Option) ([]*models.ExpoScheduleReserve, error) {
	var out []*models.ExpoScheduleReserve
	err := s.session(ctx, opts...).
		Where("schedule_guest_id in ? and notify = 0", scheduleGuestIds).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleReserve) SetNotify(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoScheduleReserve{}).
		Where("id = ?", id).
		Update("notify", 1).Error
	return ormhelper.WrapErr(err)
}
