package dao

import (
	"context"
	"fmt"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

// FaceGroupExpo 人员库场景关联DAO
type FaceGroupExpo struct {
	*DBModel
}

// NewFaceGroupExpo 创建人员库场景关联DAO实例
func NewFaceGroupExpo(db *gorm.DB) *FaceGroupExpo {
	return &FaceGroupExpo{
		DBModel: NewDBModel(db),
	}
}

// session 重写session方法以支持事务
func (d *FaceGroupExpo) session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	// 如果上下文中有事务，使用事务
	if tx, ok := ctx.Value("tx").(*gorm.DB); ok {
		return igorm.NewOptions(tx, opts...).Session().WithContext(ctx)
	}
	return d.DBModel.session(ctx, opts...)
}

// Create 创建人员库场景关联
func (d *FaceGroupExpo) Create(ctx context.Context, scene *models.FaceGroupExpo, opts ...igorm.Option) error {
	if scene == nil {
		return ormhelper.WrapErr(fmt.Errorf("人员库场景关联数据不能为空"))
	}
	err := d.session(ctx, opts...).Create(scene).Error
	return ormhelper.WrapErr(err)
}

// GetByID 根据ID获取人员库场景关联
func (d *FaceGroupExpo) GetByID(ctx context.Context, id uint64, opts ...igorm.Option) (*models.FaceGroupExpo, error) {
	if id == 0 {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库场景关联ID不能为空"))
	}

	var scene models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("id = ?", id).
		First(&scene).Error
	return &scene, ormhelper.WrapErr(err)
}

// GetByGroupID 根据人员库ID获取场景关联
func (d *FaceGroupExpo) GetByGroupID(ctx context.Context, groupID string, opts ...igorm.Option) ([]*models.FaceGroupExpo, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var scenes []*models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("group_id = ?", groupID).
		Order("created_at DESC").
		Find(&scenes).Error
	return scenes, ormhelper.WrapErr(err)
}

// GetActiveByGroupID 根据人员库ID获取正常状态的场景关联
func (d *FaceGroupExpo) GetActiveByGroupID(ctx context.Context, groupID string, opts ...igorm.Option) ([]*models.FaceGroupExpo, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var scenes []*models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("group_id = ? AND status = ?", groupID, models.FaceGroupExpoStatusNormal).
		Order("created_at DESC").
		Find(&scenes).Error
	return scenes, ormhelper.WrapErr(err)
}

// GetByExpo 根据场景信息获取人员库关联
func (d *FaceGroupExpo) GetByExpo(ctx context.Context, expoID string, opts ...igorm.Option) ([]*models.FaceGroupExpo, error) {
	if expoID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("场景ID不能为空"))
	}

	var scenes []*models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("expo_id = ?", expoID).
		Order("created_at DESC").
		Find(&scenes).Error
	return scenes, ormhelper.WrapErr(err)
}

// GetActiveByExpo 根据场景信息获取正常状态的人员库关联
func (d *FaceGroupExpo) GetActiveByExpo(ctx context.Context, expoID string, opts ...igorm.Option) ([]*models.FaceGroupExpo, error) {
	if expoID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("场景ID不能为空"))
	}

	var scenes []*models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("expo_id = ? AND status = ?", expoID, models.FaceGroupExpoStatusNormal).
		Order("created_at DESC").
		Find(&scenes).Error
	return scenes, ormhelper.WrapErr(err)
}

// GetByGroupAndExpo 根据人员库ID和场景信息获取唯一关联
func (d *FaceGroupExpo) GetByGroupAndExpo(ctx context.Context, groupID string, expoID string, opts ...igorm.Option) (*models.FaceGroupExpo, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}
	if expoID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("场景ID不能为空"))
	}

	var scene models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("group_id = ? AND scene_type = ? AND expo_id = ?", groupID, expoID).
		First(&scene).Error
	return &scene, ormhelper.WrapErr(err)
}

// GetActiveByGroupAndExpo 根据人员库ID和场景信息获取正常状态的唯一关联
func (d *FaceGroupExpo) GetActiveByGroupAndExpo(ctx context.Context, groupID string, expoID string, opts ...igorm.Option) (*models.FaceGroupExpo, error) {
	if groupID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}
	if expoID == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("场景ID不能为空"))
	}

	var scene models.FaceGroupExpo
	err := d.session(ctx, opts...).
		Where("group_id = ? AND expo_id = ? AND status = ?", groupID, expoID, models.FaceGroupExpoStatusNormal).
		First(&scene).Error
	return &scene, ormhelper.WrapErr(err)
}

// GetGroupIDsByExpo 根据场景信息获取人员库ID列表
func (d *FaceGroupExpo) GetGroupIDsByExpo(ctx context.Context, expoID int64, opts ...igorm.Option) ([]string, error) {
	if expoID == 0 {
		return nil, ormhelper.WrapErr(fmt.Errorf("场景ID不能为空"))
	}

	var groupIDs []string
	err := d.session(ctx, opts...).
		Model(&models.FaceGroupExpo{}).
		Where(" expo_id = ? AND status = ?", expoID, models.FaceGroupExpoStatusNormal).
		Pluck("group_id", &groupIDs).Error
	return groupIDs, ormhelper.WrapErr(err)
}

// List 获取人员库场景关联列表
func (d *FaceGroupExpo) List(ctx context.Context, groupID string, limit, offset int, opts ...igorm.Option) ([]*models.FaceGroupExpo, int64, error) {
	query := d.session(ctx, opts...).Model(&models.FaceGroupExpo{})

	// 条件筛选
	if groupID != "" {
		query = query.Where("group_id = ?", groupID)
	}

	// 统计总数
	var total int64
	countQuery := query
	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	// 分页查询
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	var scenes []*models.FaceGroupExpo
	err = query.Order("created_at DESC").Find(&scenes).Error
	return scenes, total, ormhelper.WrapErr(err)
}

// Update 更新人员库场景关联信息
func (d *FaceGroupExpo) Update(ctx context.Context, id uint64, updates map[string]interface{}, opts ...igorm.Option) error {
	if id == 0 {
		return ormhelper.WrapErr(fmt.Errorf("人员库场景关联ID不能为空"))
	}
	if len(updates) == 0 {
		return ormhelper.WrapErr(fmt.Errorf("更新数据不能为空"))
	}

	err := d.session(ctx, opts...).
		Model(&models.FaceGroupExpo{}).
		Where("id = ?", id).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// UpdateByGroupAndExpo 根据人员库和场景信息更新关联
func (d *FaceGroupExpo) UpdateByGroupAndExpo(ctx context.Context, groupID string, expoID string, updates map[string]interface{}, opts ...igorm.Option) error {
	if groupID == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}
	if expoID == "" {
		return ormhelper.WrapErr(fmt.Errorf("场景ID不能为空"))
	}
	if len(updates) == 0 {
		return ormhelper.WrapErr(fmt.Errorf("更新数据不能为空"))
	}

	err := d.session(ctx, opts...).
		Model(&models.FaceGroupExpo{}).
		Where("group_id = ? AND expo_id = ?", groupID, expoID).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// Delete 软删除人员库场景关联
func (d *FaceGroupExpo) Delete(ctx context.Context, id uint64, opts ...igorm.Option) error {
	if id == 0 {
		return ormhelper.WrapErr(fmt.Errorf("人员库场景关联ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FaceGroupExpoStatusDeleted,
	}
	err := d.session(ctx, opts...).
		Model(&models.FaceGroupExpo{}).
		Where("id = ?", id).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// DeleteByGroupID 根据人员库ID批量软删除场景关联
func (d *FaceGroupExpo) DeleteByGroupID(ctx context.Context, groupID string, opts ...igorm.Option) error {
	if groupID == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FaceGroupExpoStatusDeleted,
	}
	err := d.session(ctx, opts...).
		Model(&models.FaceGroupExpo{}).
		Where("group_id = ?", groupID).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// CountByGroupID 统计人员库关联的场景数量
func (d *FaceGroupExpo) CountByGroupID(ctx context.Context, groupID string, opts ...igorm.Option) (int64, error) {
	if groupID == "" {
		return 0, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var count int64
	err := d.session(ctx, opts...).
		Model(&models.FaceGroupExpo{}).
		Where("group_id = ? AND status = ?", groupID, models.FaceGroupExpoStatusNormal).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}
