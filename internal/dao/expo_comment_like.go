package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type CommentLike struct {
	*DBModel
}

func NewCommentLike(db *gorm.DB) *CommentLike {
	return &CommentLike{
		DBModel: NewDBModel(db),
	}
}

func (s *CommentLike) Add(ctx context.Context, in *models.ExpoCommentLike, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}
func (s *CommentLike) Delete(ctx context.Context, commentId string, userId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Where("comment_id = ? and user_id = ?", commentId, userId).Delete(&models.ExpoCommentLike{}).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}
func (s *CommentLike) Get(ctx context.Context, commentId string, userId string, opts ...igorm.Option) ([]*models.ExpoCommentLike, error) {
	var like []*models.ExpoCommentLike
	err := s.session(ctx, opts...).Where("comment_id = ? and user_id = ?", commentId, userId).Find(&like).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return like, nil
}

// GetListByCommentIds 批量获取点赞
func (s *CommentLike) GetListByCommentIds(ctx context.Context, commentIds []string, userId string, opts ...igorm.Option) ([]*string, error) {
	var like []*string
	err := s.session(ctx, opts...).Where("comment_id = ? and user_id = ?", commentIds, userId).Select("comment_id").Find(&like).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return like, nil
}
