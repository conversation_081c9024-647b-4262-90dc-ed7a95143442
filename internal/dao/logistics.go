package dao

import (
	"context"
	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Logistics struct {
	*DBModel
}

func NewLogistics(db *gorm.DB) *Logistics {
	return &Logistics{
		DBModel: NewDBModel(db),
	}
}

func (s *Logistics) Add(ctx context.Context, in *models.Logistics, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *Logistics) CountByTrackingNo(ctx context.Context, trackingNo string, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Logistics{}).
		Where("tracking_no = ?", trackingNo).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *Logistics) CountByOrderNo(ctx context.Context, orderNo string, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.Logistics{}).
		Where("order_no = ?", orderNo).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *Logistics) GetByOrderNo(ctx context.Context, orderNo string, opts ...igorm.Option) (*models.Logistics, error) {
	var out models.Logistics
	err := s.session(ctx, opts...).
		Where("order_no = ?", orderNo).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *Logistics) GetByTrackingNo(ctx context.Context, trackingNo string, opts ...igorm.Option) (*models.Logistics, error) {
	var out models.Logistics
	err := s.session(ctx, opts...).
		Where("tracking_no = ?", trackingNo).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Logistics) UpdateStatus(ctx context.Context, orderNo string, status v1.LogisticStepStatus, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Logistics{}).
		Where("order_no = ?", orderNo).
		Update("status", status).Error
	return ormhelper.WrapErr(err)
}

// Update 根据订单号更新物流信息的多个字段
func (s *Logistics) Update(ctx context.Context, orderNo string, updates map[string]interface{}, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Logistics{}).
		Where("order_no = ?", orderNo).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}
