package dao

import (
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	NewMySQL,
	NewStaticDB,
	NewEaDB,
	NewVpsDB,
	NewVipDB,
	//NewReportDB,
	NewExhibitionDB,
	NewRedis,
	NewRedisCache,

	NewCountry,
	NewCountryTranslate,
	NewGoods,
	NewGoodsStatistics,
	NewGoodsSnapshot,
	NewSKU,
	NewAddress,
	NewOrder,
	NewOrderItem,
	NewPayment,
	NewLogistics,
	NewLogisticsDetail,
	NewExpressQuery,

	NewSignRecord,
	NewSignTaskConfig,
	NewSignRewardLog,

	// 任务模块
	NewTaskConfig,
	NewTaskProgress,
	NewRewardIssue,

	NewUserGiftCard,
	NewGiftCardUserRemind,
	NewGiftCardSetting,

	NewEA,
	NewVPS,
	NewVIP,
	//NewReport,
	NewExhibition,
)
