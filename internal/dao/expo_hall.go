package dao

import (
	"context"

	"api-expo/api/common"
	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoHall struct {
	*DBModel
}

func NewExpoHall(db *gorm.DB) *ExpoHall {
	return &ExpoHall{
		DBModel: NewDBModel(db),
	}
}

// Add 添加
func (s *ExpoHall) Add(ctx context.Context, in *models.ExpoHall, opts ...igorm.Option) (uint, error) {

	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

// GetHallByExpoId 根据展会ID获取会场列表
func (s *ExpoHall) GetHallById(ctx context.Context, id int64, opts ...igorm.Option) (*models.ExpoHall, error) {
	var out *models.ExpoHall
	err := s.session(ctx, opts...).
		Model(&models.ExpoHall{}).
		Where("id =?", id).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// SetEnable 设置启用状态
func (s *ExpoHall) SetEnable(ctx context.Context, id int64, enable bool, opts ...igorm.Option) (*common.EmptyReply, error) {
	err := s.session(ctx, opts...).
		Model(&models.ExpoHall{}).
		Where("id =?", id).
		Update("enable", enable).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &common.EmptyReply{}, nil
}

// Delete 删除
func (s *ExpoHall) Delete(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("id =?", id).
		Delete(&models.ExpoHall{}).Error
	return ormhelper.WrapErr(err)
}

// Update 更新
func (s *ExpoHall) Update(ctx context.Context, id int64, in *models.ExpoHall, opts ...igorm.Option) (*common.EmptyReply, error) {
	err := s.session(ctx, opts...).
		Select("expo_id", "type", "name", "enable", "extra", "creator").
		Where("id =?", id).
		Updates(in).Error
	return &common.EmptyReply{}, ormhelper.WrapErr(err)
}

// FindAllByExpoId	根据展会ID获取会场列表
func (s *ExpoHall) FindAllByExpoId(ctx context.Context, expoId int64, page, size int32, opts ...igorm.Option) ([]*models.ExpoHall, int64, error) {
	var out []*models.ExpoHall
	var total int64
	// 创建基础会话
	db := s.session(ctx, opts...)
	// 应用软删除过滤
	db = db.Where("expo_id =?", expoId).Where("deleted_at is null")
	// 执行查询
	err := db.
		Model(&models.ExpoHall{}).
		Order("created_at desc").
		Count(&total).
		Limit(int(size)).
		Offset(int((page - 1) * size)).
		Find(&out).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}
	return out, total, nil
}

func (s *ExpoHall) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.ExpoHall, error) {
	var out []*models.ExpoHall
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoHall) GetHallType(ctx context.Context, expoId int64, t v1.ExpoHallType, opts ...igorm.Option) (*models.ExpoHall, error) {
	var out models.ExpoHall
	err := s.session(ctx, opts...).
		Where("expo_id = ? and type = ?", expoId, t).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoHall) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoHall, error) {
	var out []*models.ExpoHall
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}
