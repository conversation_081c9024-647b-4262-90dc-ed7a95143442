package dao

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gold_store/internal/models"
)

type TaskRewardIssue struct {
	*DBModel
}

// NewRewardIssue 创建奖励发放DAO实例
func NewRewardIssue(db *gorm.DB) *TaskRewardIssue {
	return &TaskRewardIssue{
		DBModel: NewDBModel(db),
	}
}

// Create 创建奖励发放记录
func (r *TaskRewardIssue) Create(ctx context.Context, rewardIssue *models.TaskRewardIssue) error {
	return r.db.WithContext(ctx).Create(rewardIssue).Error
}

// Update 更新奖励发放记录
func (r *TaskRewardIssue) Update(ctx context.Context, rewardIssue *models.TaskRewardIssue) error {
	return r.db.WithContext(ctx).Save(rewardIssue).Error
}

// CreateWithTx 使用事务创建奖励发放记录
func (r *TaskRewardIssue) CreateWithTx(ctx context.Context, tx *gorm.DB, rewardIssue *models.TaskRewardIssue) error {
	return tx.WithContext(ctx).Create(rewardIssue).Error
}

// GetByID 根据ID获取奖励发放记录
func (r *TaskRewardIssue) GetByID(ctx context.Context, id uint) (*models.TaskRewardIssue, error) {
	var rewardIssue models.TaskRewardIssue
	err := r.db.WithContext(ctx).First(&rewardIssue, id).Error
	if err != nil {
		return nil, err
	}
	return &rewardIssue, nil
}

// GetByOperationID 根据操作ID获取奖励发放记录
func (r *TaskRewardIssue) GetByOperationID(ctx context.Context, operationID string) (*models.TaskRewardIssue, error) {
	var rewardIssue models.TaskRewardIssue
	err := r.db.WithContext(ctx).
		Where("operation_id = ?", operationID).
		First(&rewardIssue).Error
	if err != nil {
		return nil, err
	}
	return &rewardIssue, nil
}

// ListByUserID 获取用户的奖励发放记录
func (r *TaskRewardIssue) ListByUserID(ctx context.Context, userID string, params *RewardIssueQueryParams) ([]*models.TaskRewardIssue, int64, error) {
	var rewardIssues []*models.TaskRewardIssue
	var count int64

	query := r.db.WithContext(ctx).Model(&models.TaskRewardIssue{}).
		Where("user_id = ?", userID)

	// 添加查询条件
	if params != nil {
		if params.Status > 0 {
			query = query.Where("status = ?", params.Status)
		}
		if params.RewardType > 0 {
			query = query.Where("reward_type = ?", params.RewardType)
		}
		if params.TaskConfigID > 0 {
			query = query.Where("task_config_id = ?", params.TaskConfigID)
		}
		if !params.StartTime.IsZero() && !params.EndTime.IsZero() {
			query = query.Where("created_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
		}
	}

	// 计算总数
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if params != nil && params.PageSize > 0 {
		offset := (params.PageNum - 1) * params.PageSize
		query = query.Offset(int(offset)).Limit(int(params.PageSize))
	}

	// 排序
	query = query.Order("id DESC")

	// 执行查询
	err = query.Find(&rewardIssues).Error
	if err != nil {
		return nil, 0, err
	}

	return rewardIssues, count, nil
}

// ListByUserIDWithTaskConfig 获取用户的奖励发放记录（包含任务配置）
func (r *TaskRewardIssue) ListByUserIDWithTaskConfig(ctx context.Context, userID string, params *RewardIssueQueryParams) ([]*models.TaskRewardIssue, int64, error) {
	var rewardIssues []*models.TaskRewardIssue
	var count int64

	query := r.db.WithContext(ctx).Model(&models.TaskRewardIssue{}).
		Where("user_id = ?", userID)

	// 添加查询条件
	if params != nil {
		if params.Status > 0 {
			query = query.Where("status = ?", params.Status)
		}
		if params.RewardType > 0 {
			query = query.Where("reward_type = ?", params.RewardType)
		}
		if params.TaskConfigID > 0 {
			query = query.Where("task_config_id = ?", params.TaskConfigID)
		}
		if !params.StartTime.IsZero() && !params.EndTime.IsZero() {
			query = query.Where("created_at BETWEEN ? AND ?", params.StartTime, params.EndTime)
		}
	}

	// 计算总数
	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	if params != nil && params.PageSize > 0 {
		offset := (params.PageNum - 1) * params.PageSize
		query = query.Offset(int(offset)).Limit(int(params.PageSize))
	}

	// 排序
	query = query.Order("id DESC")

	// 预加载任务配置
	query = query.Preload("TaskConfig")

	// 执行查询
	err = query.Find(&rewardIssues).Error
	if err != nil {
		return nil, 0, err
	}

	return rewardIssues, count, nil
}

// UpdateStatus 更新奖励发放状态
func (r *TaskRewardIssue) UpdateStatus(ctx context.Context, id uint, status models.TaskRewardIssueStatus) error {
	return r.db.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("id = ?", id).
		Update("status", status).
		Error
}

// UpdateStatusWithReason 更新奖励发放状态并设置失败原因
func (r *TaskRewardIssue) UpdateStatusWithReason(ctx context.Context, id uint, status models.TaskRewardIssueStatus, failReason string) error {
	return r.db.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":      status,
			"fail_reason": failReason,
		}).Error
}

// UpdateStatusWithTx 使用事务更新奖励发放状态
func (r *TaskRewardIssue) UpdateStatusWithTx(ctx context.Context, tx *gorm.DB, id uint, status models.TaskRewardIssueStatus) error {
	return tx.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("id = ?", id).
		Update("status", status).
		Error
}

// UpdateStatusWithReasonAndTx 使用事务更新奖励发放状态并设置失败原因
func (r *TaskRewardIssue) UpdateStatusWithReasonAndTx(ctx context.Context, tx *gorm.DB, id uint, status models.TaskRewardIssueStatus, failReason string) error {
	return tx.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":      status,
			"fail_reason": failReason,
		}).Error
}

// FindFailedRewards 查找发放失败的奖励记录
func (r *TaskRewardIssue) FindFailedRewards(ctx context.Context, limit int) ([]*models.TaskRewardIssue, error) {
	var rewardIssues []*models.TaskRewardIssue
	err := r.db.WithContext(ctx).
		Where("status = ?", models.TaskRewardIssueFailed).
		Limit(limit).
		Find(&rewardIssues).Error
	if err != nil {
		return nil, err
	}
	return rewardIssues, nil
}

// FindPendingRewards 查找待发放的奖励记录
func (r *TaskRewardIssue) FindPendingRewards(ctx context.Context, limit int) ([]*models.TaskRewardIssue, error) {
	var rewardIssues []*models.TaskRewardIssue
	err := r.db.WithContext(ctx).
		Where("status = ?", models.TaskRewardIssuePending).
		Limit(limit).
		Find(&rewardIssues).Error
	if err != nil {
		return nil, err
	}
	return rewardIssues, nil
}

// BatchUpdateStatus 批量更新奖励发放状态
func (r *TaskRewardIssue) BatchUpdateStatus(ctx context.Context, ids []uint, status models.TaskRewardIssueStatus) error {
	if len(ids) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("id IN ?", ids).
		Update("status", status).
		Error
}

// CountRewardsOfUser 统计用户获得的奖励数量
func (r *TaskRewardIssue) CountRewardsOfUser(ctx context.Context, userID string, rewardType models.RewardType) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("user_id = ? AND status = ?", userID, models.TaskRewardIssueIssued)

	if rewardType > 0 {
		query = query.Where("reward_type = ?", rewardType)
	}

	err := query.Count(&count).Error
	return count, err
}

// CreateRewardRecordWithTx 在事务中创建奖励记录
func (r *TaskRewardIssue) CreateRewardRecordWithTx(ctx context.Context, tx *gorm.DB, userID string, taskID int64, taskProgressID uint, rewardType models.RewardType, status models.TaskRewardIssueStatus, rewardConfig models.RewardDetail, operationID string) (*models.TaskRewardIssue, error) {
	// 将奖励配置转换为JSON字符串
	rewardConfigJSON, err := json.Marshal(rewardConfig)
	if err != nil {
		log.Context(ctx).Errorf("序列化奖励配置失败: %v", err)
		return nil, err
	}

	// 构建奖励发放记录对象
	rewardIssue := &models.TaskRewardIssue{
		UserID:         userID,
		TaskConfigID:   uint(taskID),
		TaskProgressID: taskProgressID,
		RewardType:     rewardType,
		RewardConfig:   string(rewardConfigJSON),
		Status:         status,
		OperationID:    operationID,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 使用事务创建奖励记录
	err = r.CreateWithTx(ctx, tx, rewardIssue)
	if err != nil {
		log.Context(ctx).Errorf("创建奖励记录失败: %v, userId=%s, taskId=%d", err, userID, taskID)
		return nil, err
	}

	log.Context(ctx).Infof("创建奖励记录成功, userId=%s, taskId=%d, taskProgressId=%d, rewardIssueId=%d, rewardType=%d",
		userID, taskID, taskProgressID, rewardIssue.ID, rewardType)

	return rewardIssue, nil
}

// ExistsByTaskProgressID 检查指定任务进度ID是否已有奖励记录
func (r *TaskRewardIssue) ExistsByTaskProgressID(ctx context.Context, taskProgressID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TaskRewardIssue{}).
		Where("task_progress_id = ?", taskProgressID).
		Count(&count).Error

	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetByTaskProgressID 根据任务进度ID获取奖励记录
func (r *TaskRewardIssue) GetByTaskProgressID(ctx context.Context, taskProgressID uint) (*models.TaskRewardIssue, error) {
	var rewardIssue models.TaskRewardIssue
	err := r.db.WithContext(ctx).
		Where("task_progress_id = ?", taskProgressID).
		First(&rewardIssue).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &rewardIssue, nil
}

// GetByTaskProgressIDs 根据多个任务进度ID批量获取奖励发放记录
func (r *TaskRewardIssue) GetByTaskProgressIDs(ctx context.Context, taskProgressIDs []uint) ([]*models.TaskRewardIssue, error) {
	if len(taskProgressIDs) == 0 {
		return []*models.TaskRewardIssue{}, nil
	}

	var rewardIssues []*models.TaskRewardIssue
	err := r.db.WithContext(ctx).
		Where("task_progress_id IN ?", taskProgressIDs).
		Find(&rewardIssues).Error

	if err != nil {
		return nil, err
	}
	return rewardIssues, nil
}

// RewardIssueQueryParams 奖励发放记录查询参数
type RewardIssueQueryParams struct {
	Status       models.TaskRewardIssueStatus `json:"status"`
	RewardType   models.RewardType            `json:"reward_type"`
	TaskConfigID uint                         `json:"task_config_id"`
	StartTime    time.Time                    `json:"start_time"`
	EndTime      time.Time                    `json:"end_time"`
	PageNum      int64                        `json:"page_num"`
	PageSize     int64                        `json:"page_size"`
}
