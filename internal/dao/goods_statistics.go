package dao

import (
	"context"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type GoodsStatistics struct {
	*DBModel
}

func NewGoodsStatistics(db *gorm.DB) *GoodsStatistics {
	return &GoodsStatistics{
		DBModel: NewDBModel(db),
	}
}

func (s *GoodsStatistics) Add(ctx context.Context, in *models.GoodsStatistics, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *GoodsStatistics) FindBySales(ctx context.Context, size, page int, opts ...igorm.Option) ([]*models.GoodsStatistics, error) {
	var out []*models.GoodsStatistics
	err := s.session(ctx, opts...).
		Where("status = ?", v1.GoodsStatus_GoodsStatusOn).
		Order("total_sales desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *GoodsStatistics) GetByGoodsId(ctx context.Context, goodsId string, opts ...igorm.Option) (*models.GoodsStatistics, error) {
	var out models.GoodsStatistics
	err := s.session(ctx, opts...).Where("goods_id = ?", goodsId).First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *GoodsStatistics) FindByGoodsIds(ctx context.Context, goodsIds []string, opts ...igorm.Option) ([]*models.GoodsStatistics, error) {
	var out []*models.GoodsStatistics
	err := s.session(ctx, opts...).
		Where("goods_id in ?", goodsIds).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *GoodsStatistics) UpdateStatus(ctx context.Context, goodsId string, status v1.GoodsStatus, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.GoodsStatistics{}).
		Where("goods_id = ?", goodsId).
		Update("status", status).Error
	return ormhelper.WrapErr(err)
}

func (s *GoodsStatistics) UpdatesSales(ctx context.Context, goodsId string, sales int32, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.GoodsStatistics{}).
		Where("goods_id = ?", goodsId).
		Updates(map[string]interface{}{
			"sales":       gorm.Expr("sales + ?", sales),
			"total_sales": gorm.Expr("total_sales + ?", sales),
		}).Error
	return ormhelper.WrapErr(err)
}

func (s *GoodsStatistics) IncreaseGoodsViews(ctx context.Context, goodsId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.GoodsStatistics{}).
		Where("goods_id = ?", goodsId).
		Updates(map[string]interface{}{
			"views": gorm.Expr("views + ?", 1),
		}).Error
	return ormhelper.WrapErr(err)
}

func (s *GoodsStatistics) Delete(ctx context.Context, goodsId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("goods_id = ?", goodsId).
		Delete(&models.GoodsStatistics{}).Error
	return ormhelper.WrapErr(err)
}
