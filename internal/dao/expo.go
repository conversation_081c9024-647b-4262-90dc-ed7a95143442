package dao

import (
	"context"
	"time"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Expo struct {
	*DBModel
}

func NewExpo(db *gorm.DB) *Expo {
	return &Expo{
		DBModel: NewDBModel(db),
	}
}

func (s *Expo) Add(ctx context.Context, in *models.Expo, opts ...igorm.Option) (int64, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *Expo) RegistrantsIncr(ctx context.Context, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Expo{}).
		Where("id = ?", id).
		Update("registrants", gorm.Expr("registrants + ?", 1)).Error
	return ormhelper.WrapErr(err)
}

func (s *Expo) Get(ctx context.Context, id int64, opts ...igorm.Option) (*models.Expo, error) {
	var out models.Expo
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Expo) FindWithNotStart(ctx context.Context, opts ...igorm.Option) ([]*models.ExpoUnion, error) {
	var (
		out []*models.ExpoUnion
		now = time.Now()
	)

	err := s.session(ctx, opts...).
		Select("expo.*,expo_community.extra").
		Model(&models.ExpoCommunity{}).
		Joins("left join expo on expo_community.expo_id = expo.id").
		Where("expo_community.enable = 1 and expo_community.deleted_at is null").
		Where("expo.start > ? and expo.start < ?", now.Add(-24*time.Hour), now.Add(48*time.Hour)).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *Expo) GetExpoByCountryCode(ctx context.Context, countryCode string, status v1.ExpoStatus, opts ...igorm.Option) ([]*models.ExpoUnion, error) {
	var (
		out     []*models.ExpoUnion
		session = s.session(ctx, opts...).
			Select("expo.*,expo_community.extra").
			Model(&models.ExpoCommunity{}).
			Joins("left join expo on expo_community.expo_id = expo.id").
			Where("expo_community.enable = 1 and expo_community.deleted_at is null and countryCode = ?", countryCode)
		now = time.Now()
	)

	switch status {
	case v1.ExpoStatus_ExpoStatus_NOT_START:
		session = session.Where("start > ?", now)
	case v1.ExpoStatus_ExpoStatus_PROCESSING:
		session = session.Where("start <= ? and end >= ?", now, now)
	case v1.ExpoStatus_ExpoStatus_END:
		session = session.Where("end < ?", now)
	}

	err := session.
		Order("start desc").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Expo) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.Expo, error) {
	var out []*models.Expo
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Order("start desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

//func (s *Expo) FindByStatus(ctx context.Context, status v1.ExpoStatus, size, page int, opts ...igorm.Option) ([]*models.ExpoUnion, error) {
//	var (
//		out     []*models.ExpoUnion
//		session = s.session(ctx, opts...).
//			Select("expo.*,expo_community.extra").
//			Model(&models.ExpoCommunity{}).
//			Joins("left join expo on expo_community.expo_id = expo.id").
//			Where("expo_community.enable = 1 and expo_community.deleted_at is null")
//		now = time.Now()
//	)
//
//	switch status {
//	case v1.ExpoStatus_ExpoStatus_NOT_START:
//		session = session.Where("start > ?", now)
//	case v1.ExpoStatus_ExpoStatus_PROCESSING:
//		session = session.Where("start <= ? and end >= ?", now, now)
//	case v1.ExpoStatus_ExpoStatus_END:
//		session = session.Where("end < ?", now)
//	default:
//		err := s.session(ctx, opts...).Raw(`
//select * from
//(
//	(select expo.*,com.extra from (select * from expo_community where enable = 1 and deleted_at is null) as com inner join (select * from expo where start <= @start and end >= @end ) as expo on com.expo_id = expo.id)
//	UNION ALL
//	(select expo.*,com.extra from (select * from expo_community where enable = 1 and deleted_at is null) as com inner join (select * from expo where start > @start) as expo on com.expo_id = expo.id)
//	UNION ALL
//	(select expo.*,com.extra from (select * from expo_community where enable = 1 and deleted_at is null) as com inner join (select * from expo where end < @end) as expo on com.expo_id = expo.id)
//) as expo limit @limit offset @offset
//`, sql.Named("start", now), sql.Named("end", now), sql.Named("limit", size), sql.Named("offset", size*(page-1))).
//			Scan(&out).Error
//		return out, ormhelper.WrapErr(err)
//	}
//
//	err := session.
//		Order("start desc").
//		Offset(size * (page - 1)).
//		Limit(size).
//		Find(&out).Error
//	if err != nil {
//		return nil, ormhelper.WrapErr(err)
//	}
//	return out, nil
//}

// FindProcessing 正在进行中的展会
func (s *Expo) FindProcessing(ctx context.Context, opts ...igorm.Option) ([]*models.Expo, error) {
	var out []*models.Expo
	now := time.Now()
	err := s.session(ctx, opts...).
		Where("start <= ? and end >= ?", now, now).
		Order("start desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *Expo) FindByIdsKeyword(ctx context.Context, ids []int64, keyword string, opts ...igorm.Option) ([]*models.Expo, error) {
	var out []*models.Expo
	session := s.session(ctx, opts...).
		Select("expo.*,expo_community.extra").
		Model(&models.ExpoCommunity{}).
		Joins("left join expo on expo_community.expo_id = expo.id").
		Where("expo_community.enable = 1 and expo_community.deleted_at is null")

	if keyword != "" {
		session = session.Where("expo.name LIKE ? ", "%"+keyword+"%")
	}

	if len(ids) > 0 {
		session = session.Where("expo.id IN ?", ids)
	}
	err := session.Order("start desc").Limit(1000).Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *Expo) FindById(ctx context.Context, id int64, opts ...igorm.Option) (*models.ExpoUnion, error) {
	var out models.ExpoUnion
	session := s.session(ctx, opts...).
		Select("expo.*,expo_community.extra").
		Model(&models.ExpoCommunity{}).
		Joins("left join expo on expo_community.expo_id = expo.id").
		Where("expo_community.enable = 1 and expo_community.deleted_at is null")
	session = session.Where("expo.id = ?", id)
	err := session.First(&out).Error
	return &out, ormhelper.WrapErr(err)
}
