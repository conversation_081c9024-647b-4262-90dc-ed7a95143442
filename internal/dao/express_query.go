package dao

import (
	"context"
	"time"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpressQuery struct {
	*DBModel
}

func NewExpressQuery(db *gorm.DB) *ExpressQuery {
	return &ExpressQuery{
		DBModel: NewDBModel(db),
	}
}

func (e *ExpressQuery) session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	// 如果上下文中有事务，使用事务
	if tx, ok := ctx.Value("tx").(*gorm.DB); ok {
		return igorm.NewOptions(tx, opts...).Session().WithContext(ctx)
	}
	return e.DBModel.session(ctx, opts...)
}

// SaveQueryLog 保存快递查询日志
func (e *ExpressQuery) SaveQueryLog(ctx context.Context, log *models.ExpressQueryLog, opts ...igorm.Option) error {
	err := e.session(ctx, opts...).Create(log).Error
	return ormhelper.WrapErr(err)
}

// GetQueryLogByTrackingNo 根据快递单号获取查询日志
func (e *ExpressQuery) GetQueryLogByTrackingNo(ctx context.Context, trackingNo string, opts ...igorm.Option) (*models.ExpressQueryLog, error) {
	var log models.ExpressQueryLog
	err := e.session(ctx, opts...).
		Where("tracking_no = ?", trackingNo).
		Order("created_at DESC").
		First(&log).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &log, nil
}

// UpdateLogisticsFromExpress 根据快递查询结果更新物流信息
func (e *ExpressQuery) UpdateLogisticsFromExpress(ctx context.Context, orderNo string, status v1.LogisticStepStatus, lastUpdate time.Time, opts ...igorm.Option) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// 根据状态设置相应的时间字段
	switch status {
	case v1.LogisticStepStatus_LogisticStepStatusSHIPPED:
		updates["shipping_time"] = lastUpdate
	case v1.LogisticStepStatus_LogisticStepStatusSIGNED:
		updates["sign_time"] = lastUpdate
		updates["delivery_time"] = lastUpdate
	}

	err := e.session(ctx, opts...).
		Model(&models.Logistics{}).
		Where("order_no = ?", orderNo).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}
