package dao

import (
	"context"
	"fmt"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

// FaceGroup 人员库DAO
type FaceGroup struct {
	*DBModel
}

// NewFaceGroup 创建人员库DAO实例
func NewFaceGroup(db *gorm.DB) *FaceGroup {
	return &FaceGroup{
		DBModel: NewDBModel(db),
	}
}

// session 重写session方法以支持事务
func (d *FaceGroup) session(ctx context.Context, opts ...igorm.Option) *gorm.DB {
	// 如果上下文中有事务，使用事务
	if tx, ok := ctx.Value("tx").(*gorm.DB); ok {
		return igorm.NewOptions(tx, opts...).Session().WithContext(ctx)
	}
	return d.DBModel.session(ctx, opts...)
}

// Create 创建人员库
func (d *FaceGroup) Create(ctx context.Context, group *models.FaceGroup, opts ...igorm.Option) error {
	if group == nil {
		return ormhelper.WrapErr(fmt.Errorf("人员库数据不能为空"))
	}
	err := d.session(ctx, opts...).Create(group).Error
	return ormhelper.WrapErr(err)
}

// GetByID 根据ID获取人员库
func (d *FaceGroup) GetByID(ctx context.Context, id string, opts ...igorm.Option) (*models.FaceGroup, error) {
	if id == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var group models.FaceGroup
	err := d.session(ctx, opts...).
		Where("id = ?", id).
		First(&group).Error
	return &group, ormhelper.WrapErr(err)
}

// GetByName 根据名称获取人员库
func (d *FaceGroup) GetByName(ctx context.Context, name string, opts ...igorm.Option) (*models.FaceGroup, error) {
	if name == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库名称不能为空"))
	}

	var group models.FaceGroup
	err := d.session(ctx, opts...).
		Where("name = ?", name).
		First(&group).Error
	return &group, ormhelper.WrapErr(err)
}

// GetActiveByID 根据ID获取正常状态的人员库
func (d *FaceGroup) GetActiveByID(ctx context.Context, id string, opts ...igorm.Option) (*models.FaceGroup, error) {
	if id == "" {
		return nil, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var group models.FaceGroup
	err := d.session(ctx, opts...).
		Where("id = ? AND status = ?", id, models.FaceGroupStatusNormal).
		First(&group).Error
	return &group, ormhelper.WrapErr(err)
}

// List 获取人员库列表
func (d *FaceGroup) List(ctx context.Context, params *models.FaceGroupQueryRequest, opts ...igorm.Option) ([]*models.FaceGroup, int64, error) {
	if params == nil {
		params = &models.FaceGroupQueryRequest{}
	}

	// 设置默认分页参数
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}

	query := d.session(ctx, opts...).Model(&models.FaceGroup{})

	// 条件筛选
	if len(params.GroupIDs) > 0 {
		query = query.Where("id IN ?", params.GroupIDs)
	}
	if params.Name != "" {
		query = query.Where("name LIKE ?", fmt.Sprintf("%%%s%%", params.Name))
	}
	if params.Status != 0 {
		query = query.Where("status = ?", params.Status)
	}

	// 统计总数
	var total int64
	countQuery := query
	err := countQuery.Count(&total).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	// 分页查询
	var groups []*models.FaceGroup
	offset := (params.Page - 1) * params.PageSize
	err = query.Order("created_at DESC").
		Limit(params.PageSize).
		Offset(offset).
		Find(&groups).Error

	return groups, total, ormhelper.WrapErr(err)
}

// Update 更新人员库信息
func (d *FaceGroup) Update(ctx context.Context, id string, updates map[string]interface{}, opts ...igorm.Option) error {
	if id == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}
	if len(updates) == 0 {
		return ormhelper.WrapErr(fmt.Errorf("更新数据不能为空"))
	}

	err := d.session(ctx, opts...).
		Model(&models.FaceGroup{}).
		Where("id = ?", id).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// UpdateFaceCount 更新人脸数量
func (d *FaceGroup) UpdateFaceCount(ctx context.Context, id string, increment int, opts ...igorm.Option) error {
	if id == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	err := d.session(ctx, opts...).
		Model(&models.FaceGroup{}).
		Where("id = ?", id).
		UpdateColumn("estimated_face_count", gorm.Expr("estimated_face_count + ?", increment)).Error
	return ormhelper.WrapErr(err)
}

// Delete 软删除人员库
func (d *FaceGroup) Delete(ctx context.Context, id string, opts ...igorm.Option) error {
	if id == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FaceGroupStatusDeleted,
	}
	err := d.session(ctx, opts...).
		Model(&models.FaceGroup{}).
		Where("id = ?", id).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// MarkDeleting 标记人员库为删除中状态
func (d *FaceGroup) MarkDeleting(ctx context.Context, id string, opts ...igorm.Option) error {
	if id == "" {
		return ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	updates := map[string]interface{}{
		"status": models.FaceGroupStatusDeleting,
	}
	err := d.session(ctx, opts...).
		Model(&models.FaceGroup{}).
		Where("id = ? AND status = ?", id, models.FaceGroupStatusNormal).
		Updates(updates).Error
	return ormhelper.WrapErr(err)
}

// GetActiveList 获取正常状态的人员库列表
func (d *FaceGroup) GetActiveList(ctx context.Context, limit int, opts ...igorm.Option) ([]*models.FaceGroup, error) {
	if limit <= 0 {
		limit = 100
	}

	var groups []*models.FaceGroup
	err := d.session(ctx, opts...).
		Where("status = ?", models.FaceGroupStatusNormal).
		Order("created_at DESC").
		Limit(limit).
		Find(&groups).Error
	return groups, ormhelper.WrapErr(err)
}

// GetByIDs 根据ID列表批量获取人员库
func (d *FaceGroup) GetByIDs(ctx context.Context, ids []string, opts ...igorm.Option) ([]*models.FaceGroup, error) {
	if len(ids) == 0 {
		return []*models.FaceGroup{}, nil
	}

	var groups []*models.FaceGroup
	err := d.session(ctx, opts...).
		Where("id IN ?", ids).
		Find(&groups).Error
	return groups, ormhelper.WrapErr(err)
}

// GetActiveByIDs 根据ID列表批量获取正常状态的人员库
func (d *FaceGroup) GetActiveByIDs(ctx context.Context, ids []string, opts ...igorm.Option) ([]*models.FaceGroup, error) {
	if len(ids) == 0 {
		return []*models.FaceGroup{}, nil
	}

	var groups []*models.FaceGroup
	err := d.session(ctx, opts...).
		Where("id IN ? AND status = ?", ids, models.FaceGroupStatusNormal).
		Find(&groups).Error
	return groups, ormhelper.WrapErr(err)
}

// Count 统计人员库数量
func (d *FaceGroup) Count(ctx context.Context, status models.FaceGroupStatus, opts ...igorm.Option) (int64, error) {
	var count int64
	query := d.session(ctx, opts...).Model(&models.FaceGroup{})

	if status != 0 {
		query = query.Where("status = ?", status)
	}

	err := query.Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

// Exists 检查人员库是否存在
func (d *FaceGroup) Exists(ctx context.Context, id string, opts ...igorm.Option) (bool, error) {
	if id == "" {
		return false, ormhelper.WrapErr(fmt.Errorf("人员库ID不能为空"))
	}

	var count int64
	err := d.session(ctx, opts...).
		Model(&models.FaceGroup{}).
		Where("id = ?", id).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

// ExistsByName 检查人员库名称是否已存在
func (d *FaceGroup) ExistsByName(ctx context.Context, name string, excludeID string, opts ...igorm.Option) (bool, error) {
	if name == "" {
		return false, ormhelper.WrapErr(fmt.Errorf("人员库名称不能为空"))
	}

	query := d.session(ctx, opts...).
		Model(&models.FaceGroup{}).
		Where("name = ?", name)

	if excludeID != "" {
		query = query.Where("id != ?", excludeID)
	}

	var count int64
	err := query.Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}
