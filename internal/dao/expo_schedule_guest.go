package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoScheduleGuest struct {
	*DBModel
}

func NewExpoScheduleGuest(db *gorm.DB) *ExpoScheduleGuest {
	return &ExpoScheduleGuest{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoScheduleGuest) Add(ctx context.Context, in *models.ExpoScheduleGuest, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoScheduleGuest) DeleteByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Delete(&models.ExpoScheduleGuest{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) DeleteByScheduleId(ctx context.Context, scheduleId int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("schedule_id = ?", scheduleId).
		Delete(&models.ExpoScheduleGuest{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) BatchAdd(ctx context.Context, in []*models.ExpoScheduleGuest, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).CreateInBatches(in, len(in)).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) GetById(ctx context.Context, expoId int64, id int64, opts ...igorm.Option) (*models.ExpoScheduleGuest, error) {
	var out models.ExpoScheduleGuest
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.ExpoScheduleGuest, error) {
	var out []*models.ExpoScheduleGuest
	err := s.session(ctx, opts...).
		Where("id in ?", ids).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) FindByScheduleIds(ctx context.Context, scheduleIds []int64, opts ...igorm.Option) ([]*models.ExpoScheduleGuest, error) {
	var out []*models.ExpoScheduleGuest
	err := s.session(ctx, opts...).
		Where("schedule_id in ?", scheduleIds).
		Order("id asc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) FindByScheduleIdsWithUnscoped(ctx context.Context, scheduleIds []int64, opts ...igorm.Option) ([]*models.ExpoScheduleGuest, error) {
	var out []*models.ExpoScheduleGuest
	err := s.session(ctx, opts...).
		Unscoped().
		Where("schedule_id in ?", scheduleIds).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) FindByGuestId(ctx context.Context, guestId int64, opts ...igorm.Option) ([]*models.ExpoScheduleGuest, error) {
	var out []*models.ExpoScheduleGuest
	err := s.session(ctx, opts...).
		Where("guest_id = ?", guestId).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoScheduleGuest) GroupByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoScheduleGuestGroup, error) {
	var out []*models.ExpoScheduleGuestGroup
	err := s.session(ctx, opts...).
		Table("expo_schedule_guest").
		Select("guest_id as guest_id,count(DISTINCT schedule_id) as count").
		Where("expo_id = ?", expoId).
		Group("guest_id").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}
