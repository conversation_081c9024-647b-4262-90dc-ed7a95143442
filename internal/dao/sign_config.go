package dao

import (
	"context"
	"errors"
	"gold_store/internal/models"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// SignConfig 签到任务配置数据访问对象
type SignConfig struct {
	db     *gorm.DB
	logger *log.Helper
}

// NewSignTaskConfig 创建签到任务配置DAO
func NewSignTaskConfig(db *gorm.DB, logger log.Logger) *SignConfig {
	return &SignConfig{
		db:     db,
		logger: log.NewHelper(logger),
	}
}

// Create 创建签到任务配置
func (d *SignConfig) Create(ctx context.Context, config *models.SignConfig) error {
	result := d.db.WithContext(ctx).Create(config)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// Update 更新签到任务配置
func (d *SignConfig) Update(ctx context.Context, config *models.SignConfig) error {
	result := d.db.WithContext(ctx).Save(config)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// Delete 删除签到任务配置
func (d *SignConfig) Delete(ctx context.Context, id uint) error {
	result := d.db.WithContext(ctx).Delete(&models.SignConfig{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetByConfigKey 根据配置键获取签到任务配置
func (d *SignConfig) GetByConfigKey(ctx context.Context, configKey string) (*models.SignConfig, error) {
	var config models.SignConfig
	result := d.db.WithContext(ctx).Where("config_key = ?", configKey).First(&config)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &config, nil
}

// GetActiveConfig 获取有效的签到配置
func (d *SignConfig) GetActiveConfig(ctx context.Context, configKey string) (*models.SignConfig, error) {
	var config models.SignConfig
	result := d.db.WithContext(ctx).
		Where("config_key = ? AND status = ?", configKey, models.ConfigStatusActive).
		First(&config)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &config, nil
}

// ListAllConfigs 获取所有配置
func (d *SignConfig) ListAllConfigs(ctx context.Context) ([]*models.SignConfig, error) {
	var configs []*models.SignConfig
	result := d.db.WithContext(ctx).Find(&configs)
	if result.Error != nil {
		return nil, result.Error
	}
	return configs, nil
}

// UpdateConfigStatus 更新配置状态
func (d *SignConfig) UpdateConfigStatus(ctx context.Context, id uint, status int8) error {
	result := d.db.WithContext(ctx).
		Model(&models.SignConfig{}).
		Where("id = ?", id).
		Update("status", status)

	if result.Error != nil {
		return result.Error
	}
	return nil
}
