package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoImage struct {
	*DBModel
}

func NewExpoImage(db *gorm.DB) *ExpoImage {
	return &ExpoImage{
		DBModel: NewDBModel(db),
	}
}

// Add 添加展会图片
func (s *ExpoImage) Add(ctx context.Context, in *models.ExpoImage, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

// Get 获取单个展会图片
func (s *ExpoImage) Get(ctx context.Context, id uint64, opts ...igorm.Option) (*models.ExpoImage, error) {
	var out models.ExpoImage
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *ExpoImage) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoImage{}).
		Where("expo_id = ?", expoId).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoImage) FindByExpoId(ctx context.Context, expoId int64, size int, opts ...igorm.Option) ([]*models.ExpoImage, error) {
	var out []*models.ExpoImage
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Order("created_at desc").
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// GetByExpoID 根据展会ID获取图片
func (s *ExpoImage) GetByExpoID(ctx context.Context, expoID uint64, opts ...igorm.Option) ([]*models.ExpoImage, error) {
	var out []*models.ExpoImage
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoID).
		Order("created_at DESC").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

// GetPageByExpoID 分页获取展会图片
func (s *ExpoImage) GetPageByExpoID(ctx context.Context, expoID int64, page, size int, opts ...igorm.Option) ([]*models.ExpoImage, int64, error) {
	var (
		out   []*models.ExpoImage
		total int64
	)

	session := s.session(ctx, opts...).
		Model(&models.ExpoImage{}).
		Where("expo_id = ?", expoID)

	// 获取总数
	err := session.Count(&total).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	// 分页查询
	offset := (page - 1) * size
	err = session.
		Order("created_at DESC").
		Offset(offset).
		Limit(size).
		Find(&out).Error
	if err != nil {
		return nil, 0, ormhelper.WrapErr(err)
	}

	return out, total, nil
}

// Update 更新展会图片
func (s *ExpoImage) Update(ctx context.Context, id uint64, updates map[string]interface{}, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoImage{}).
		Where("id = ?", id).
		Updates(updates).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

// Delete 删除展会图片
func (s *ExpoImage) Delete(ctx context.Context, id uint64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		Delete(&models.ExpoImage{}).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

// BatchDelete 批量删除展会图片
func (s *ExpoImage) BatchDelete(ctx context.Context, ids []uint64, opts ...igorm.Option) error {
	if len(ids) == 0 {
		return nil
	}

	err := s.session(ctx, opts...).
		Where("id IN ?", ids).
		Delete(&models.ExpoImage{}).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}

// CheckDuplicateUrls 检查重复的图片URL
func (s *ExpoImage) CheckDuplicateUrls(ctx context.Context, expoID int64, imageUrls []string, opts ...igorm.Option) ([]string, error) {
	if len(imageUrls) == 0 {
		return []string{}, nil
	}

	var existingImages []*models.ExpoImage
	err := s.session(ctx, opts...).
		Where("expo_id = ? AND image_url IN ?", expoID, imageUrls).
		Find(&existingImages).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}

	duplicateUrls := make([]string, 0, len(existingImages))
	for _, img := range existingImages {
		duplicateUrls = append(duplicateUrls, img.ImageUrl)
	}

	return duplicateUrls, nil
}

// BatchAdd 批量添加展会图片
func (s *ExpoImage) BatchAdd(ctx context.Context, images []*models.ExpoImage, opts ...igorm.Option) error {
	if len(images) == 0 {
		return nil
	}

	err := s.session(ctx, opts...).CreateInBatches(images, len(images)).Error
	if err != nil {
		return ormhelper.WrapErr(err)
	}
	return nil
}
