package dao

import (
	"context"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/ormhelper"

	"github.com/airunny/wiki-go-tools/igorm"
)

type Exhibition struct {
	*DBModel
}

func NewExhibition(db ExhibitionDB) *Exhibition {
	return &Exhibition{
		DBModel: NewDBModel(db),
	}
}

func (s *Exhibition) GetExpo(ctx context.Context, id int, opts ...igorm.Option) (*models.Expo, error) {
	var out models.Expo
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *Exhibition) GetTicketType(ctx context.Context, id int, opts ...igorm.Option) (*models.TicketType, error) {
	var out models.TicketType
	err := s.session(ctx, opts...).
		Where("id = ?", id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *Exhibition) FindParticipant(ctx context.Context, orderNo string, opts ...igorm.Option) ([]*models.Participant, error) {
	var out []*models.Participant
	err := s.session(ctx, opts...).
		Where("order_no = ?", orderNo).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
