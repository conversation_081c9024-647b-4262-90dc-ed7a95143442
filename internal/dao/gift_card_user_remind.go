package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type GiftCardUserRemind struct {
	*DBModel
}

func NewGiftCardUserRemind(db *gorm.DB) *GiftCardUserRemind {
	return &GiftCardUserRemind{
		DBModel: NewDBModel(db),
	}
}

// UserGiftUserRemindList 获取用户卡券弹框
func (s *GiftCardUserRemind) GetSingle(ctx context.Context, userId string, opts ...igorm.Option) (*models.GiftCardUserRemind, error) {
	var giftCardUserRemind *models.GiftCardUserRemind
	err := s.session(ctx, opts...).
		Where("user_id=?", userId).
		Order("created_at desc").
		Limit(1).
		Find(&giftCardUserRemind).Error
	return giftCardUserRemind, ormhelper.WrapErr(err)
}

func (s *GiftCardUserRemind) Add(ctx context.Context, in *models.GiftCardUserRemind, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).Create(in).Error
	return ormhelper.WrapErr(err)
}
