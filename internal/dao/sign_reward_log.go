package dao

import (
	"context"
	"errors"
	"gold_store/internal/models"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// SignRewardLog 签到奖励日志数据访问对象
type SignRewardLog struct {
	db     *gorm.DB
	logger *log.Helper
}

// NewSignRewardLog 创建签到奖励日志DAO
func NewSignRewardLog(db *gorm.DB, logger log.Logger) *SignRewardLog {
	return &SignRewardLog{
		db:     db,
		logger: log.NewHelper(logger),
	}
}

// Create 创建奖励流水记录
func (d *SignRewardLog) Create(ctx context.Context, log *models.SignRewardLog) error {
	result := d.db.WithContext(ctx).Create(log)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetByID 根据ID获取奖励流水记录
func (d *SignRewardLog) GetByID(ctx context.Context, id uint) (*models.SignRewardLog, error) {
	var rewardLog models.SignRewardLog
	result := d.db.WithContext(ctx).First(&rewardLog, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &rewardLog, nil
}

// Update 更新奖励流水记录
func (d *SignRewardLog) Update(ctx context.Context, log *models.SignRewardLog) error {
	result := d.db.WithContext(ctx).Save(log)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// Delete 删除奖励流水记录
func (d *SignRewardLog) Delete(ctx context.Context, id uint) error {
	result := d.db.WithContext(ctx).Delete(&models.SignRewardLog{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetByOperationID 根据操作ID获取奖励流水记录(用于幂等性检查)
func (d *SignRewardLog) GetByOperationID(ctx context.Context, operationID string) (*models.SignRewardLog, error) {
	var rewardLog models.SignRewardLog
	result := d.db.WithContext(ctx).Where("operation_id = ?", operationID).First(&rewardLog)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &rewardLog, nil
}

// GetUserRewards 获取用户奖励记录
func (d *SignRewardLog) GetUserRewards(ctx context.Context, userID string, limit, offset int) ([]*models.SignRewardLog, error) {
	var logs []*models.SignRewardLog
	result := d.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&logs)

	if result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

// CountUserRewards 统计用户奖励记录总数
func (d *SignRewardLog) CountUserRewards(ctx context.Context, userID string) (int64, error) {
	var count int64
	result := d.db.WithContext(ctx).
		Model(&models.SignRewardLog{}).
		Where("user_id = ?", userID).
		Count(&count)

	if result.Error != nil {
		return 0, result.Error
	}
	return count, nil
}

// GetBySignID 根据签到ID获取奖励流水记录
func (d *SignRewardLog) GetBySignID(ctx context.Context, sourceType int8, signID uint) ([]*models.SignRewardLog, error) {
	var logs []*models.SignRewardLog
	result := d.db.WithContext(ctx).
		Where("source_type = ? AND sign_id = ?", sourceType, signID).
		Find(&logs)

	if result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

// GetByTaskID 根据任务ID获取奖励流水记录
func (d *SignRewardLog) GetByTaskID(ctx context.Context, taskID uint) ([]*models.SignRewardLog, error) {
	var logs []*models.SignRewardLog
	result := d.db.WithContext(ctx).
		Where("task_id = ?", taskID).
		Find(&logs)

	if result.Error != nil {
		return nil, result.Error
	}
	return logs, nil
}

// UpdateStatus 更新奖励发放状态
func (d *SignRewardLog) UpdateStatus(ctx context.Context, id uint, status int8, errorMsg string) error {
	updates := map[string]interface{}{
		"status": status,
	}

	if errorMsg != "" {
		updates["error_msg"] = errorMsg
	}

	result := d.db.WithContext(ctx).
		Model(&models.SignRewardLog{}).
		Where("id = ?", id).
		Updates(updates)

	if result.Error != nil {
		return result.Error
	}
	return nil
}
