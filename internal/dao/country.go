package dao

import (
	"context"

	"github.com/airunny/wiki-go-tools/ormhelper"
)

type Country struct {
	*DBModel
}

func NewCountry(db StaticDB) *Country {
	return &Country{
		DBModel: NewDBModel(db),
	}
}

func (s *Country) Exists(ctx context.Context, countryCode string) (bool, error) {
	var count int64
	err := s.session(ctx).Where("CountryCode = ?", countryCode).Count(&count).Error
	if err != nil {
		return false, ormhelper.WrapErr(err)
	}
	return count > 0, nil
}
