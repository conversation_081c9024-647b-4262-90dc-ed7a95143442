package dao

import (
	"context"
	"fmt"
	"time"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type UserGiftCard struct {
	*DBModel
}

func NewUserGiftCard(db *gorm.DB) *UserGiftCard {
	return &UserGiftCard{
		DBModel: NewDBModel(db),
	}
}

// PageList 分页获取
// useType 1 可用 2不可用
func (s *UserGiftCard) PageList(ctx context.Context, useType, size, page int, userId string, opts ...igorm.Option) ([]*models.UserGiftCard, error) {
	var (
		now          = time.Now().UTC()
		userGiftCard []*models.UserGiftCard
		query        = s.session(ctx, opts...).
				Where("user_id = ?", userId)
	)

	if useType == 1 { //可用
		query = query.Where("status = 0 and start_time < ? and end_time > ?", now, now)
	} else {
		query = query.Where("status = 1 or end_time < ?", now)
	}

	err := query.Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&userGiftCard).Error
	return userGiftCard, ormhelper.WrapErr(err)
}

func (s *UserGiftCard) CountByUserId(ctx context.Context, userId string, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("user_id = ?", userId).
		Count(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *UserGiftCard) EnableCountByUserId(ctx context.Context, userId string, opts ...igorm.Option) (int64, error) {
	var (
		out int64
		now = time.Now()
	)

	err := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("user_id = ? and status = 0 and start_time < ? and end_time > ?", userId, now, now).
		Count(&out).Error
	return out, ormhelper.WrapErr(err)
}

// List 获取用户所有卡券
func (s *UserGiftCard) List(ctx context.Context, userId string, opts ...igorm.Option) ([]*models.UserGiftCard, error) {
	var userGiftCard []*models.UserGiftCard
	err := s.session(ctx, opts...).
		Where("user_id = ?", userId).
		Order("created_at desc").
		Find(&userGiftCard).Error
	return userGiftCard, ormhelper.WrapErr(err)
}

// GetLast 获取最新一条
func (s *UserGiftCard) GetLast(ctx context.Context, userId string, opts ...igorm.Option) (*models.UserGiftCard, error) {
	var (
		userGiftCard models.UserGiftCard
		now          = time.Now().UTC()
	)

	err := s.session(ctx, opts...).
		Where("user_id=? and status=0 and start_time<? and end_time>=? and show_notice=0", userId, now, now).
		Order("created_at desc").
		First(&userGiftCard).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &userGiftCard, ormhelper.WrapErr(err)
}

// GiftCardSettingIsSend 卡券配置是否已经发放过
func (s *UserGiftCard) GiftCardSettingIsSend(ctx context.Context, settingId int64, opts ...igorm.Option) (bool, error) {
	var total int64
	err := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("setting_id = ?", settingId).Count(&total).Error
	if err != nil {
		return false, ormhelper.WrapErr(err)
	}

	return total > 0, ormhelper.WrapErr(err)
}

// UpdateGiftCardNoticeStatus 修改用户提醒状态
func (s *UserGiftCard) UpdateGiftCardNoticeStatus(ctx context.Context, userid string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("user_id = ?", userid).
		Update("show_notice", 1).Error
	return ormhelper.WrapErr(err)
}

// UserGiftCardIsExist 验证卡券是否存在
func (s *UserGiftCard) UserGiftCardIsExist(ctx context.Context, id, userid, goodsId string, opts ...igorm.Option) (bool, error) {
	var (
		count int64
		now   = time.Now()
	)

	err := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("id = ? and goods_id = ? and user_id = ? and status= 0 and start_time < ? and end_time > ? ", id, goodsId, userid, now, now).
		Count(&count).Error
	return count > 0, ormhelper.WrapErr(err)
}

// UserGiftCardPay 礼品卡兑换
func (s *UserGiftCard) UserGiftCardPay(ctx context.Context, id, userid, orderId string, opts ...igorm.Option) (int64, error) {
	var now = time.Now().UTC()
	ret := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("id = ? and user_id = ? and status = 0 and start_time < ? and end_time > ?", id, userid, now, now).
		Updates(map[string]interface{}{"status": 1, "used_time": now, "updated_at": now, "remark": "礼品卡兑换", "order_id": orderId})
	if ret.Error != nil {
		return 0, ormhelper.WrapErr(ret.Error)
	}
	return ret.RowsAffected, nil
}

// UserGiftCardRollBack 礼品卡退回
func (s *UserGiftCard) UserGiftCardRollBack(ctx context.Context, id string, opts ...igorm.Option) (int64, error) {
	var now = time.Now().UTC()
	ret := s.session(ctx, opts...).
		Model(&models.UserGiftCard{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{"status": 0, "used_time": now, "updated_at": now, "remark": "礼品卡退回"})
	if ret.Error != nil {
		return 0, ormhelper.WrapErr(ret.Error)
	}
	return ret.RowsAffected, nil
}

// UserGiftCardGroupBySettingId 根据礼品卡配置分组
func (s *UserGiftCard) UserGiftCardGroupBySettingId(ctx context.Context, opts ...igorm.Option) ([]*models.UserGiftCardGroupby, error) {
	var result []*models.UserGiftCardGroupby
	err := s.session(ctx, opts...).Raw("select setting_id SettingID,count(setting_id) Count  from user_gift_card group by setting_id ").Scan(&result)
	return result, ormhelper.WrapErr(err.Error)
}

func (s *UserGiftCard) AddBatches(ctx context.Context, models []models.UserGiftCard, opts ...igorm.Option) error {
	return s.session(ctx, opts...).CreateInBatches(models, 100).Error
}

func (s *UserGiftCard) GetPageListBySettingId(ctx context.Context, settingId, size, page int, opts ...igorm.Option) ([]*models.UserGiftCard, int64, error) {
	var count int64
	var userGiftCard []*models.UserGiftCard
	err := s.session(ctx, opts...).Model(&models.UserGiftCard{}).
		Where("setting_id = ?", settingId).Order("created_at desc").
		Count(&count).
		Offset((page - 1) * size).
		Limit(size).
		Find(&userGiftCard).Error
	fmt.Print(err)
	return userGiftCard, count, ormhelper.WrapErr(err)
}
