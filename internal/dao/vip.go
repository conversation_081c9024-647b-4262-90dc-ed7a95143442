package dao

import (
	"context"
	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
)

type VIP struct {
	*DBModel
}

func NewVIP(db VipDB) *VIP {
	return &VIP{
		DBModel: NewDBModel(db),
	}
}

func (s *VIP) GetProduct(ctx context.Context, orderNo string, opts ...igorm.Option) (*models.Product, error) {
	var out models.Product
	err := s.session(ctx, opts...).
		Where("OrderId = ?", orderNo).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *VIP) FindProductTranslate(ctx context.Context, productId string, opts ...igorm.Option) ([]*models.ProductTranslate, error) {
	var out []*models.ProductTranslate
	err := s.session(ctx, opts...).
		Where("OrderProductId = ?", productId).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}
