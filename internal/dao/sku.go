package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type SKU struct {
	*DBModel
}

func NewSKU(db *gorm.DB) *SKU {
	return &SKU{
		DBModel: NewDBModel(db),
	}
}

func (s *SKU) BatchAdd(ctx context.Context, in []*models.SKU, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).CreateInBatches(in, len(in)).Error
	return ormhelper.WrapErr(err)
}

func (s *SKU) FindByGoodsIds(ctx context.Context, goodsIds []string, opts ...igorm.Option) ([]*models.SKU, error) {
	var skus []*models.SKU
	err := s.session(ctx, opts...).
		Where("goods_id in ?", goodsIds).
		Find(&skus).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return skus, nil
}

func (s *SKU) FindByGoodsId(ctx context.Context, goodsId string, opts ...igorm.Option) ([]*models.SKU, error) {
	var skus []*models.SKU
	err := s.session(ctx, opts...).
		Where("goods_id = ?", goodsId).
		Find(&skus).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return skus, nil
}

func (s *SKU) DecreaseStock(ctx context.Context, skuId string, count int32, opts ...igorm.Option) (int64, error) {
	ret := s.session(ctx, opts...).
		Model(&models.SKU{}).
		Where("sku_id = ? and stock >= ?", skuId, count).
		Updates(map[string]interface{}{
			"stock": gorm.Expr("stock - ?", count),
			"sales": gorm.Expr("sales + ?", count),
		})
	if ret.Error != nil {
		return 0, ormhelper.WrapErr(ret.Error)
	}
	return ret.RowsAffected, nil
}

func (s *SKU) IncreaseStock(ctx context.Context, skuId string, count int32, opts ...igorm.Option) (int64, error) {
	ret := s.session(ctx, opts...).
		Model(&models.SKU{}).
		Where("sku_id = ?", skuId).
		Updates(map[string]interface{}{
			"stock": gorm.Expr("stock + ?", count),
			"sales": gorm.Expr("sales - ?", count),
		})
	if ret.Error != nil {
		return 0, ormhelper.WrapErr(ret.Error)
	}
	return ret.RowsAffected, nil
}

func (s *SKU) Update(ctx context.Context, in *models.SKU, opts ...igorm.Option) (int64, error) {
	ret := s.session(ctx, opts...).
		Select("*").
		Where("sku_id = ?", in.SkuID).
		Updates(in)
	return ret.RowsAffected, ormhelper.WrapErr(ret.Error)
}

func (s *SKU) IncrSales(ctx context.Context, skuId string, count int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.SKU{}).
		Where("sku_id = ?", skuId).
		Update("sales", gorm.Expr("sales + ?", count)).Error
	return ormhelper.WrapErr(err)
}
