package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoPartner struct {
	*DBModel
}

func NewExpoPartner(db *gorm.DB) *ExpoPartner {
	return &ExpoPartner{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoPartner) Add(ctx context.Context, in *models.ExpoPartner, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoPartner) BatchAdd(ctx context.Context, in []*models.ExpoPartner, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).CreateInBatches(in, len(in)).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoPartner) GetById(ctx context.Context, expoId, id int64, opts ...igorm.Option) (*models.ExpoPartner, error) {
	var out models.ExpoPartner
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoPartner) Update(ctx context.Context, in *models.ExpoPartner, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Omit("created_at", "extra").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoPartner) UpdateEnable(ctx context.Context, in *models.ExpoPartner, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("enable").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoPartner) CountByCondition(ctx context.Context, conditions map[string]interface{}, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoPartner{}).
		Where(conditions).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoPartner) FindByPage(ctx context.Context, conditions map[string]interface{}, page, size int, opts ...igorm.Option) ([]*models.ExpoPartner, error) {
	var out []*models.ExpoPartner
	err := s.session(ctx, opts...).
		Where(conditions).
		Order("`rank`").
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoPartner) Delete(ctx context.Context, expoId, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		Delete(&models.ExpoPartner{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoPartner) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoPartner, error) {
	var out []*models.ExpoPartner
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("`rank`").
		Order("created_at desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoPartner) FindByCondition(ctx context.Context, conditions map[string]interface{}, opts ...igorm.Option) ([]*models.ExpoPartner, error) {
	var out []*models.ExpoPartner
	err := s.session(ctx, opts...).
		Where(conditions).
		Order("`rank`").
		Order("created_at desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}
