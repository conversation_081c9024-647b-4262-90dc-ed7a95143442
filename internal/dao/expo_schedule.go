package dao

import (
	"context"
	"time"

	v1 "api-expo/api/expo/v1"
	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoSchedule struct {
	*DBModel
}

func NewExpoSchedule(db *gorm.DB) *ExpoSchedule {
	return &ExpoSchedule{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoSchedule) Add(ctx context.Context, in *models.ExpoSchedule, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoSchedule) Get(ctx context.Context, expoId, id int64, opts ...igorm.Option) (*models.ExpoSchedule, error) {
	var out models.ExpoSchedule
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoSchedule{}).
		Select("count(DISTINCT expo_schedule.id) ").
		Joins("JOIN expo_schedule_guest ON expo_schedule.id = expo_schedule_guest.schedule_id").
		Where("expo_schedule.expo_id = ?", expoId).
		Where("expo_schedule_guest.deleted_at is null").
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) FindWithNotStart(ctx context.Context, opts ...igorm.Option) ([]*models.ExpoSchedule, error) {
	var (
		out []*models.ExpoSchedule
		now = time.Now()
	)

	err := s.session(ctx, opts...).
		Where("start > ? and start < ? and enable = 1", now.Add(-24*time.Hour), now.Add(48*time.Hour)).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) FindByPageCount(ctx context.Context, expoId int64, scheduleType v1.ScheduleType, guestName string, page, size int, opts ...igorm.Option) ([]*models.ExpoSchedule, int64, error) {
	var (
		out     []*models.ExpoSchedule
		count   int64
		session = s.session(ctx, opts...).
			Where("expo_schedule.expo_id = ?", expoId)
	)

	if scheduleType != v1.ScheduleType_ScheduleType_ALL {
		session = session.Where("expo_schedule.type = ?", scheduleType)
	}

	// 模糊匹配嘉宾或主持人名字
	if guestName != "" {
		session = session.Joins("JOIN expo_schedule_guest ON expo_schedule.id = expo_schedule_guest.schedule_id").
			Joins("JOIN guest ON expo_schedule_guest.guest_id = guest.id").
			Where("guest.name LIKE ?", "%"+guestName+"%")
	}

	// 查询分页数据
	err := session.Model(&models.ExpoSchedule{}).
		Count(&count).
		Group("expo_schedule.id").
		Order("expo_schedule.created_at DESC").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, count, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) Update(ctx context.Context, in *models.ExpoSchedule, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("expo_id", "hall_id", "type", "theme", "host_id", "date", "start", "end", "enable", "extra").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) UpdateEnable(ctx context.Context, expoId, id int64, enable bool, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoSchedule{}).
		Where("expo_id = ? and id = ?", expoId, id).
		Update("enable", enable).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) Delete(ctx context.Context, expoId, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		Delete(&models.ExpoSchedule{}).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) GetByStartAndEnd(ctx context.Context, expoId, hallId int64, start, end time.Time, opts ...igorm.Option) (*models.ExpoSchedule, error) {
	var out models.ExpoSchedule
	err := s.session(ctx, opts...).
		Model(&models.ExpoSchedule{}).
		Where("expo_id = ? and hall_id = ? and start >= ? and end <= ?", expoId, hallId, start, end).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoSchedule, error) {
	var out []*models.ExpoSchedule
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("start").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) FindByIds(ctx context.Context, ids []int64, opts ...igorm.Option) ([]*models.ExpoSchedule, error) {
	var out []*models.ExpoSchedule
	err := s.session(ctx, opts...).
		Where("id in ? and enable = 1", ids).
		Order("start").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoSchedule) FindByPage(ctx context.Context, expoId int64, scheduleType v1.ScheduleType, guestName string, page, size int, opts ...igorm.Option) ([]*models.ExpoSchedule, error) {
	var out []*models.ExpoSchedule
	db := s.session(ctx, opts...)
	db = db.Where("expo_schedule.expo_id = ?", expoId).Where("expo_schedule.deleted_at is null")
	if scheduleType != v1.ScheduleType_ScheduleType_ALL {
		db = db.Where("expo_schedule.type = ?", scheduleType)
	}

	// 关联 expo_schedule_guest 和 guest 表
	db = db.Joins("JOIN expo_schedule_guest ON expo_schedule.id = expo_schedule_guest.schedule_id").
		Joins("JOIN guest ON expo_schedule_guest.guest_id = guest.id").
		Joins("JOIN guest host ON expo_schedule.host_id = host.id").
		Where("expo_schedule_guest.deleted_at is null")

	// 模糊筛选 guest.name
	if guestName != "" {
		db = db.Where("guest.name LIKE ?", "%"+guestName+"%").
			Where("host.name LIKE ?", "%"+guestName+"%")

	}

	// 去重，避免因为关联表产生重复记录
	db = db.Group("expo_schedule.id")

	err := db.Order("expo_schedule.created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

// FindByHostId 根据hostid查询
func (s *ExpoSchedule) FindByHostId(ctx context.Context, hostId int64, opts ...igorm.Option) ([]*models.ExpoSchedule, error) {
	var out []*models.ExpoSchedule
	db := s.session(ctx, opts...)
	db = db.Where("host_id = ?", hostId)
	err := db.Order("expo_schedule.created_at desc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}
