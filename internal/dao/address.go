package dao

import (
	"context"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Address struct {
	*DBModel
}

func NewAddress(db *gorm.DB) *Address {
	return &Address{
		DBModel: NewDBModel(db),
	}
}

func (s *Address) Add(ctx context.Context, in *models.Address, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *Address) Delete(ctx context.Context, id uint, userId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("id = ? and user_id = ?", id, userId).
		Delete(&models.Address{}).Error
	return ormhelper.WrapErr(err)
}

func (s *Address) Get(ctx context.Context, id uint, userId string, opts ...igorm.Option) (*models.Address, error) {
	var out models.Address
	err := s.session(ctx, opts...).
		Where("id = ? and user_id = ?", id, userId).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Address) GetUserLast(ctx context.Context, userId string, opts ...igorm.Option) (*models.Address, error) {
	var out models.Address
	err := s.session(ctx, opts...).
		Where("user_id = ?", userId).
		Order("updated_at desc").
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Address) Count(ctx context.Context, userId string, opts ...igorm.Option) (int64, error) {
	var out int64
	err := s.session(ctx, opts...).
		Model(&models.Address{}).
		Where("user_id = ?", userId).
		Count(&out).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Address) GetDefault(ctx context.Context, userId string, opts ...igorm.Option) (*models.Address, error) {
	var out models.Address
	err := s.session(ctx, opts...).
		Where("user_id = ? and is_default = ?", userId, 1).
		First(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return &out, nil
}

func (s *Address) Update(ctx context.Context, in *models.Address, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Where("id = ? and user_id = ?", in.ID, in.UserID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *Address) FindByUserId(ctx context.Context, userId string, opts ...igorm.Option) ([]*models.Address, error) {
	var out []*models.Address
	err := s.session(ctx, opts...).
		Where("user_id = ?", userId).
		Order("updated_at desc").
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Address) CancelDefault(ctx context.Context, userId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Address{}).
		Where("user_id = ?", userId).
		Update("is_default", 0).Error
	return ormhelper.WrapErr(err)
}

func (s *Address) SetDefault(ctx context.Context, id uint, userId string, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.Address{}).
		Where("id = ? and user_id = ?", id, userId).
		Update("is_default", 1).Error
	return ormhelper.WrapErr(err)
}
