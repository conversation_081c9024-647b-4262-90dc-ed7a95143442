package dao

import (
	"context"

	"api-expo/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type ExpoLive struct {
	*DBModel
}

func NewExpoLive(db *gorm.DB) *ExpoLive {
	return &ExpoLive{
		DBModel: NewDBModel(db),
	}
}

func (s *ExpoLive) Add(ctx context.Context, in *models.ExpoLive, opts ...igorm.Option) (uint, error) {
	err := s.session(ctx, opts...).Create(in).Error
	if err != nil {
		return 0, ormhelper.WrapErr(err)
	}
	return in.ID, nil
}

func (s *ExpoLive) Get(ctx context.Context, expoId, id int64, opts ...igorm.Option) (*models.ExpoLive, error) {
	var out models.ExpoLive
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoLive) GetByExpoIdAndLevel(ctx context.Context, expoId int64, level int32, opts ...igorm.Option) (*models.ExpoLive, error) {
	var out models.ExpoLive
	err := s.session(ctx, opts...).
		Model(&models.ExpoLive{}).
		Where("expo_id = ? and level = ?", expoId, level).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *ExpoLive) Update(ctx context.Context, in *models.ExpoLive, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("*").
		Omit("created_at").
		Where("expo_id = ? and id = ?", in.ExpoId, in.ID).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoLive) UpdateEnable(ctx context.Context, expoId, id int64, enable bool, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Model(&models.ExpoLive{}).
		Where("expo_id = ? and id = ?", expoId, id).
		Update("enable", enable).Error
	return ormhelper.WrapErr(err)
}

func (s *ExpoLive) FindByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) ([]*models.ExpoLive, error) {
	var out []*models.ExpoLive
	err := s.session(ctx, opts...).
		Where("expo_id = ? and enable = 1", expoId).
		Order("level asc").
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoLive) FindByPage(ctx context.Context, expoId int64, page, size int, opts ...igorm.Option) ([]*models.ExpoLive, error) {
	var out []*models.ExpoLive
	err := s.session(ctx, opts...).
		Where("expo_id = ?", expoId).
		Order("created_at desc").
		Offset((page - 1) * size).
		Limit(size).
		Find(&out).Error
	return out, ormhelper.WrapErr(err)
}

func (s *ExpoLive) CountByExpoId(ctx context.Context, expoId int64, opts ...igorm.Option) (int64, error) {
	var count int64
	err := s.session(ctx, opts...).
		Model(&models.ExpoLive{}).
		Where("expo_id = ?", expoId).
		Count(&count).Error
	return count, ormhelper.WrapErr(err)
}

func (s *ExpoLive) Delete(ctx context.Context, expoId, id int64, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Where("expo_id = ? and id = ?", expoId, id).
		Delete(&models.ExpoLive{}).Error
	return ormhelper.WrapErr(err)
}
