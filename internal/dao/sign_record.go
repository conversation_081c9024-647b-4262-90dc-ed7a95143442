package dao

import (
	"context"
	"errors"
	"gold_store/internal/models"
	"gold_store/internal/util"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

// SignRecord 签到记录数据访问对象
type SignRecord struct {
	db     *gorm.DB
	logger *log.Helper
}

// NewSignRecord 创建签到记录DAO
func NewSignRecord(db *gorm.DB, logger log.Logger) *SignRecord {
	return &SignRecord{
		db:     db,
		logger: log.NewHelper(logger),
	}
}

// Create 创建签到记录
func (d *SignRecord) Create(ctx context.Context, record *models.SignRecord) error {
	result := d.db.WithContext(ctx).Create(record)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetByID 根据ID获取签到记录
func (d *SignRecord) GetByID(ctx context.Context, id uint) (*models.SignRecord, error) {
	var record models.SignRecord
	result := d.db.WithContext(ctx).First(&record, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &record, nil
}

// Update 更新签到记录
func (d *SignRecord) Update(ctx context.Context, record *models.SignRecord) error {
	result := d.db.WithContext(ctx).Save(record)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// Delete 删除签到记录
func (d *SignRecord) Delete(ctx context.Context, id uint) error {
	result := d.db.WithContext(ctx).Delete(&models.SignRecord{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// CheckTodaySignIn 检查用户今日是否已签到
func (d *SignRecord) CheckTodaySignIn(ctx context.Context, userID string, timezone string) (bool, error) {
	// 解析时区
	loc, err := util.ParseTimezone(timezone)
	if err != nil {
		return false, err
	}
	// 获取用户所在时区的今日日期
	now := util.GetCurrentTimeWithEnvByTimezone(timezone).In(loc)

	// 计算用户时区的今天起始时间（0点0分0秒）
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
	// 计算用户时区的明天起始时间
	tomorrowStart := todayStart.AddDate(0, 0, 1)

	// 转换为UTC时间进行数据库查询（因为数据库存储的是UTC时间）
	todayStartUTC := todayStart.UTC()
	tomorrowStartUTC := tomorrowStart.UTC()

	d.logger.Infof("检查用户今日是否已签到: userID=%s, timezone=%s(offset=%s), 用户当前时间=%s, 今日开始(用户时区)=%s, 今日开始(UTC)=%s, 明日开始(UTC)=%s",
		userID, timezone, loc.String(), now.Format(time.RFC3339), todayStart.Format(time.RFC3339),
		todayStartUTC.Format(time.RFC3339), tomorrowStartUTC.Format(time.RFC3339))

	var records []*models.SignRecord
	result := d.db.WithContext(ctx).
		Where("user_id = ? AND sign_date >= ? AND sign_date < ?",
			userID, todayStartUTC, tomorrowStartUTC).
		Find(&records)

	if result.Error != nil {
		d.logger.Errorf("查询签到记录失败: %v, userID=%s", result.Error, userID)
		return false, result.Error
	}

	// 记录所有查到的签到记录，帮助调试
	if len(records) > 0 {
		for i, record := range records {
			d.logger.Infof("找到签到记录 #%d: ID=%d, 用户=%s, 签到时间=%s, 用户时区=%s",
				i+1, record.ID, record.UserID, record.SignDate.Format(time.RFC3339), record.UserTimezone)
		}
	}

	d.logger.Infof("用户今日签到检查结果: userID=%s, 已签到=%v, 记录数=%d", userID, len(records) > 0, len(records))
	return len(records) > 0, nil
}

// GetLatestRecord 获取用户最近的签到记录
func (d *SignRecord) GetLatestRecord(ctx context.Context, userID string) (*models.SignRecord, error) {
	var record models.SignRecord
	result := d.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("sign_date DESC, id DESC"). // 增加id降序，防止同一天多条记录时取最新一条
		First(&record)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &record, nil
}

// GetUserRecentRecords 获取用户最近n天的签到记录
func (d *SignRecord) GetUserRecentRecords(ctx context.Context, userID string, days int) ([]*models.SignRecord, error) {
	var records []*models.SignRecord
	result := d.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("sign_date DESC").
		Limit(days).
		Find(&records)

	if result.Error != nil {
		return nil, result.Error
	}
	return records, nil
}

// CalculateConsecutiveDays 计算用户连续签到天数
func (d *SignRecord) CalculateConsecutiveDays(ctx context.Context, userID string, timezone string) (int, error) {
	// 获取用户最近的签到记录
	latestRecord, err := d.GetLatestRecord(ctx, userID)
	if err != nil {
		return 0, err
	}

	// 如果没有签到记录，返回0
	if latestRecord == nil {
		return 0, nil
	}

	// 解析时区
	loc, err := util.ParseTimezone(timezone)
	if err != nil {
		return 0, err
	}

	// 获取用户所在时区的今日和昨日日期
	// now := time.Now().In(loc)
	now := util.GetCurrentTimeWithEnvByTimezone(timezone).In(loc)
	yesterday := now.AddDate(0, 0, -1)

	// 将最近签到日期转换到用户时区
	recordDate := latestRecord.SignDate.In(loc)

	// 比较日期（仅比较年月日）
	nowDate := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)
	yesterdayDate := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, loc)
	recordDateOnly := time.Date(recordDate.Year(), recordDate.Month(), recordDate.Day(), 0, 0, 0, 0, loc)

	// 如果最近签到不是今天也不是昨天，则连续签到中断，返回0
	if !recordDateOnly.Equal(nowDate) && !recordDateOnly.Equal(yesterdayDate) {
		return 0, nil
	}

	// 返回记录中的连续签到天数
	return latestRecord.ConsecutiveDays, nil
}

// GetSignHistory 获取用户签到历史记录（范围查询）
func (d *SignRecord) GetSignHistory(ctx context.Context, userID string, startDate, endDate time.Time) ([]*models.SignRecord, error) {
	var records []*models.SignRecord
	result := d.db.WithContext(ctx).
		Where("user_id = ? AND sign_date BETWEEN ? AND ?", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).
		Order("sign_date ASC").
		Find(&records)

	if result.Error != nil {
		return nil, result.Error
	}
	return records, nil
}

// GetUserAllTasksLatestRecords 获取用户所有任务的最新签到记录
func (d *SignRecord) GetUserAllTasksLatestRecords(ctx context.Context, userID string) ([]*models.SignRecord, error) {
	var records []*models.SignRecord

	// 使用子查询获取每个任务的最新记录ID
	subQuery := d.db.Model(&models.SignRecord{}).
		Select("MAX(id)").
		Where("user_id = ?", userID).
		Group("task_id")

	// 使用主查询获取这些ID对应的完整记录
	result := d.db.WithContext(ctx).
		Where("id IN (?)", subQuery).
		Find(&records)

	if result.Error != nil {
		return nil, result.Error
	}
	return records, nil
}

// GetRecentSignRecords 获取最近的n条签到记录
func (d *SignRecord) GetRecentSignRecords(ctx context.Context, limit int) ([]*models.SignRecord, error) {
	var records []*models.SignRecord
	result := d.db.WithContext(ctx).
		Order("sign_date DESC").
		Limit(limit).
		Find(&records)

	if result.Error != nil {
		return nil, result.Error
	}
	return records, nil
}
