package dao

import (
	"context"
	v1 "gold_store/api/gold_store/v1"

	"gold_store/internal/models"

	"github.com/airunny/wiki-go-tools/igorm"
	"github.com/airunny/wiki-go-tools/ormhelper"
	"gorm.io/gorm"
)

type Payment struct {
	*DBModel
}

func NewPayment(db *gorm.DB) *Payment {
	return &Payment{
		DBModel: NewDBModel(db),
	}
}

func (s *Payment) Add(ctx context.Context, in *models.Payment, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Create(in).Error
	return ormhelper.WrapErr(err)
}

func (s *Payment) GetByOrderNo(ctx context.Context, orderNo string, opts ...igorm.Option) (*models.Payment, error) {
	var out models.Payment
	err := s.session(ctx, opts...).
		Where("order_no = ?", orderNo).
		First(&out).Error
	return &out, ormhelper.WrapErr(err)
}

func (s *Payment) FindByOrderNos(ctx context.Context, orderNos []string, opts ...igorm.Option) ([]*models.Payment, error) {
	var out []*models.Payment
	err := s.session(ctx, opts...).
		Where("order_no in ?", orderNos).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}

func (s *Payment) UpdatePayment(ctx context.Context, in *models.Payment, opts ...igorm.Option) error {
	err := s.session(ctx, opts...).
		Select("operation_no", "paid_total", "paid_time", "status", "extra").
		Where("payment_no = ?", in.PaymentNo).
		Updates(in).Error
	return ormhelper.WrapErr(err)
}

// FindByOperationNos 根据操作ID列表批量查询支付记录
func (s *Payment) FindByOperationNosForTask(ctx context.Context, operationNos []string, opts ...igorm.Option) ([]*models.Payment, error) {
	var out []*models.Payment
	err := s.session(ctx, opts...).
		Where("operation_no in ? and paid_method = ?", operationNos, v1.PaymentMethod_value["TASK"]).
		Find(&out).Error
	if err != nil {
		return nil, ormhelper.WrapErr(err)
	}
	return out, nil
}
