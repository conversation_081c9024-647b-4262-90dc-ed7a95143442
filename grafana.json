{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 0}, "id": 4, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "disableTextWrap": false, "editorMode": "code", "expr": " rate(server_requests_code_total[$Interval])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{operation}}", "range": true, "refId": "A", "useBackend": false}], "title": "Requests QPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 0}, "id": 1, "options": {"legend": {"calcs": ["min", "max", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.2.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "rate(server_requests_duration_sec_sum{service=\"$Service\",namespace=\"$Namespace\",kind=\"$Kind\"}[$Interval])/rate(server_requests_duration_sec_count{service=\"$Service\",namespace=\"$Namespace\",kind=\"$Kind\"}[$Interval])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "{{operation}}", "range": true, "refId": "A", "useBackend": false}], "title": "接口响应时间", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "disableTextWrap": false, "editorMode": "code", "expr": "rate(server_requests_code_total{service=\"$Service\", namespace=\"$Namespace\", kind=\"$Kind\", code=~\"40.*\"}[$Interval])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{operation}}(400*)", "range": true, "refId": "参数错误", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "editorMode": "code", "expr": "rate(server_requests_code_total{service=\"$Service\", namespace=\"$Namespace\", kind=\"$Kind\", code=~\"50.*\"}[$Interval])", "hide": false, "instant": false, "legendFormat": "{{operation}}(500*)", "range": true, "refId": "服务端错误"}], "title": "接口错误码", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.2.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "histogram_quantile($Quantile, sum(irate(server_requests_duration_sec_bucket{service=\"$Service\",namespace=\"$Namespace\",kind=\"$Kind\"}[$Interval])) by (le))", "format": "time_series", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "interval": "", "legendFormat": "{{operation}}", "range": true, "refId": "A", "useBackend": false}], "title": "quantile", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 39, "tags": [], "templating": {"list": [{"allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "definition": "label_values(server_requests_code_total,service)", "hide": 0, "includeAll": true, "label": "Service", "multi": false, "name": "Service", "options": [], "query": {"qryType": 1, "query": "label_values(server_requests_code_total,service)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "definition": "label_values(server_requests_code_total{service=\"$Service\"},namespace)", "hide": 0, "includeAll": true, "label": "Namespace", "multi": false, "name": "Namespace", "options": [], "query": {"qryType": 1, "query": "label_values(server_requests_code_total{service=\"$Service\"},namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "d6b62553-c776-426d-9c97-043cd349496f"}, "definition": "label_values(server_requests_code_total{service=\"$Service\", namespace=\"$Namespace\"},kind)", "hide": 0, "includeAll": true, "label": "Kind", "multi": false, "name": "Kind", "options": [], "query": {"qryType": 1, "query": "label_values(server_requests_code_total{service=\"$Service\", namespace=\"$Namespace\"},kind)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "1m", "value": "1m"}, "hide": 0, "includeAll": false, "label": "Interval", "multi": false, "name": "Interval", "options": [{"selected": true, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": false, "text": "0.95", "value": "0.95"}, "hide": 0, "includeAll": false, "label": "Quantile", "multi": false, "name": "Quantile", "options": [{"selected": true, "text": "0.95", "value": "0.95"}, {"selected": false, "text": "0.75", "value": "0.75"}, {"selected": false, "text": "0.25", "value": "0.25"}], "query": "0.95,0.75,0.25", "queryValue": "", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "metrics", "uid": "dba6f8db-af17-4ee3-98a6-5f463205049d", "version": 34, "weekStart": ""}