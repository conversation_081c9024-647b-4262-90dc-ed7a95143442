FROM golang:1.24 AS builder

COPY . /src
WORKDIR /src


#RUN GOPROXY=http://**************:36944 && make build
#RUN export GOPROXY=http://goproxy.fxeyeinterface.com:8081 &&  make build
#RUN #export GOPROXY=https://goproxy.io &&
RUN make build

FROM debian:stable-slim

RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

ARG WIKI

COPY --from=builder /src/bin/api-expo /app/app
COPY --from=builder /src/configs/config.yaml /data/conf/config.yaml
COPY --from=builder /src/docs/language.zip /app/language.zip
COPY --from=builder /src/docs/logo.png /app/logo.png
ENV LANGUAGE_PATH=/app/language.zip
ENV LOGO_FILE_PATH=/app/logo.png
ENV REGISTRY=NO

WORKDIR /app

EXPOSE 81
EXPOSE 80
VOLUME /data/conf

CMD ["./app", "-conf", "/data/conf"]
