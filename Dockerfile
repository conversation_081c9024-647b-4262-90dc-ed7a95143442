FROM golang:1.24 AS builder

COPY . /src
WORKDIR /src

ARG WIKI
RUN echo "${WIKI}"

#RUN GOPROXY=http://**************:36944 && make build
#RUN export GOPROXY=http://goproxy.fxeyeinterface.com:8081 &&  make build
RUN make build

FROM debian:stable-slim

RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*

ARG WIKI
RUN echo "${WIKI}"

COPY --from=builder /src/bin/${WIKI} /app/app
COPY --from=builder /src/configs/config.yaml /data/conf/config.yaml
COPY --from=builder /src/docs/language.zip /app/language.zip
ENV LANGUAGE_PATH=/app/language.zip
ENV REGISTRY=NO

WORKDIR /app

EXPOSE 8080
EXPOSE 9090
VOLUME /data/conf

CMD ["./app", "-conf", "/data/conf"]
