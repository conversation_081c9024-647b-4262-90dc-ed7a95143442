// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: news/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceFindNews = "/api.news.v1.Service/FindNews"
const OperationServiceFindNewsByIds = "/api.news.v1.Service/FindNewsByIds"
const OperationServiceGetNewsById = "/api.news.v1.Service/GetNewsById"
const OperationServiceHealthy = "/api.news.v1.Service/Healthy"

type ServiceHTTPServer interface {
	// FindNews 获取快讯列表
	FindNews(context.Context, *FindNewsRequest) (*FindNewsReply, error)
	// FindNewsByIds 批量获取快讯列表
	FindNewsByIds(context.Context, *FindNewsByIdsRequest) (*FindNewsByIdsReply, error)
	// GetNewsById 获取快讯详情
	GetNewsById(context.Context, *GetNewsByIdRequest) (*NewsInfo, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy5_HTTP_Handler(srv))
	r.GET("/v1/news", _Service_GetNewsById0_HTTP_Handler(srv))
	r.GET("/v1/news/list", _Service_FindNews0_HTTP_Handler(srv))
	r.POST("/v1/news/batch", _Service_FindNewsByIds0_HTTP_Handler(srv))
}

func _Service_Healthy5_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetNewsById0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetNewsByIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetNewsById)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetNewsById(ctx, req.(*GetNewsByIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*NewsInfo)
		return ctx.Result(200, reply)
	}
}

func _Service_FindNews0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindNewsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindNews)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindNews(ctx, req.(*FindNewsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindNewsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FindNewsByIds0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FindNewsByIdsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFindNewsByIds)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FindNewsByIds(ctx, req.(*FindNewsByIdsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FindNewsByIdsReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	FindNews(ctx context.Context, req *FindNewsRequest, opts ...http.CallOption) (rsp *FindNewsReply, err error)
	FindNewsByIds(ctx context.Context, req *FindNewsByIdsRequest, opts ...http.CallOption) (rsp *FindNewsByIdsReply, err error)
	GetNewsById(ctx context.Context, req *GetNewsByIdRequest, opts ...http.CallOption) (rsp *NewsInfo, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) FindNews(ctx context.Context, in *FindNewsRequest, opts ...http.CallOption) (*FindNewsReply, error) {
	var out FindNewsReply
	pattern := "/v1/news/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFindNews))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FindNewsByIds(ctx context.Context, in *FindNewsByIdsRequest, opts ...http.CallOption) (*FindNewsByIdsReply, error) {
	var out FindNewsByIdsReply
	pattern := "/v1/news/batch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceFindNewsByIds))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetNewsById(ctx context.Context, in *GetNewsByIdRequest, opts ...http.CallOption) (*NewsInfo, error) {
	var out NewsInfo
	pattern := "/v1/news"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetNewsById))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
