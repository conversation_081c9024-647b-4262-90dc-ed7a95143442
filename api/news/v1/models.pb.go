// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: news/v1/models.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNewsByIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetNewsByIdRequest) Reset() {
	*x = GetNewsByIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_news_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNewsByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNewsByIdRequest) ProtoMessage() {}

func (x *GetNewsByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_news_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNewsByIdRequest.ProtoReflect.Descriptor instead.
func (*GetNewsByIdRequest) Descriptor() ([]byte, []int) {
	return file_news_v1_models_proto_rawDescGZIP(), []int{0}
}

func (x *GetNewsByIdRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type NewsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`                                           // 新闻ID
	Action          int64  `protobuf:"varint,3,opt,name=action,json=action,proto3" json:"action"`                              // 0 --- 新增   1 --- 修改
	Important       int64  `protobuf:"varint,4,opt,name=important,json=important,proto3" json:"important"`                     // 重要性  1 --- 低   2 --- 中  3 --- 高
	NewsTime        int64  `protobuf:"varint,5,opt,name=news_time,json=newsTime,proto3" json:"news_time"`                      // 中文（快讯时间 北京时间减去8小时=格林尼治时间 推送的时间戳也是格林尼治时间）；英文（注意：这里是东八区时间，需要加8小时才是北京时间）
	NewsTitle       string `protobuf:"bytes,6,opt,name=news_title,json=newsTitle,proto3" json:"news_title"`                    // title
	UpdateTime      int64  `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time"`                // 更新时间
	NewType         int64  `protobuf:"varint,8,opt,name=new_type,json=newType,proto3" json:"new_type"`                         // 中文特有字段（类型：1=快讯 2=财经日历）
	Scountry        string `protobuf:"bytes,9,opt,name=scountry,json=scountry,proto3" json:"scountry"`                         // 英文特有字段（国家）
	Scategory       string `protobuf:"bytes,10,opt,name=scategory,json=scategory,proto3" json:"scategory"`                     // 英文特有字段
	NewsDescription string `protobuf:"bytes,11,opt,name=news_description,json=newsDescription,proto3" json:"news_description"` // 英文特有字段
	Image           string `protobuf:"bytes,12,opt,name=image,json=image,proto3" json:"image"`                                 // 图片地址
	ImageWidth      int64  `protobuf:"varint,13,opt,name=image_width,json=imageWidth,proto3" json:"image_width"`               // 图片宽
	ImageHeight     int64  `protobuf:"varint,14,opt,name=image_height,json=imageHeight,proto3" json:"image_height"`            // 图片高
	LabelIsShow     bool   `protobuf:"varint,15,opt,name=label_is_show,json=labelIsShow,proto3" json:"label_is_show"`          // 标签是否显示
	Label           bool   `protobuf:"varint,16,opt,name=label,json=label,proto3" json:"label"`                                // 标签
	LabelBgColor    bool   `protobuf:"varint,17,opt,name=label_bg_color,json=labelBgColor,proto3" json:"label_bg_color"`       // 标签背景色
}

func (x *NewsInfo) Reset() {
	*x = NewsInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_news_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NewsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsInfo) ProtoMessage() {}

func (x *NewsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_news_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsInfo.ProtoReflect.Descriptor instead.
func (*NewsInfo) Descriptor() ([]byte, []int) {
	return file_news_v1_models_proto_rawDescGZIP(), []int{1}
}

func (x *NewsInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *NewsInfo) GetAction() int64 {
	if x != nil {
		return x.Action
	}
	return 0
}

func (x *NewsInfo) GetImportant() int64 {
	if x != nil {
		return x.Important
	}
	return 0
}

func (x *NewsInfo) GetNewsTime() int64 {
	if x != nil {
		return x.NewsTime
	}
	return 0
}

func (x *NewsInfo) GetNewsTitle() string {
	if x != nil {
		return x.NewsTitle
	}
	return ""
}

func (x *NewsInfo) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsInfo) GetNewType() int64 {
	if x != nil {
		return x.NewType
	}
	return 0
}

func (x *NewsInfo) GetScountry() string {
	if x != nil {
		return x.Scountry
	}
	return ""
}

func (x *NewsInfo) GetScategory() string {
	if x != nil {
		return x.Scategory
	}
	return ""
}

func (x *NewsInfo) GetNewsDescription() string {
	if x != nil {
		return x.NewsDescription
	}
	return ""
}

func (x *NewsInfo) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *NewsInfo) GetImageWidth() int64 {
	if x != nil {
		return x.ImageWidth
	}
	return 0
}

func (x *NewsInfo) GetImageHeight() int64 {
	if x != nil {
		return x.ImageHeight
	}
	return 0
}

func (x *NewsInfo) GetLabelIsShow() bool {
	if x != nil {
		return x.LabelIsShow
	}
	return false
}

func (x *NewsInfo) GetLabel() bool {
	if x != nil {
		return x.Label
	}
	return false
}

func (x *NewsInfo) GetLabelBgColor() bool {
	if x != nil {
		return x.LabelBgColor
	}
	return false
}

type FindNewsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int64 `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
	Page int64 `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
}

func (x *FindNewsRequest) Reset() {
	*x = FindNewsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_news_v1_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindNewsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindNewsRequest) ProtoMessage() {}

func (x *FindNewsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_news_v1_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindNewsRequest.ProtoReflect.Descriptor instead.
func (*FindNewsRequest) Descriptor() ([]byte, []int) {
	return file_news_v1_models_proto_rawDescGZIP(), []int{2}
}

func (x *FindNewsRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FindNewsRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type FindNewsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*NewsInfo `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total int64       `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *FindNewsReply) Reset() {
	*x = FindNewsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_news_v1_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindNewsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindNewsReply) ProtoMessage() {}

func (x *FindNewsReply) ProtoReflect() protoreflect.Message {
	mi := &file_news_v1_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindNewsReply.ProtoReflect.Descriptor instead.
func (*FindNewsReply) Descriptor() ([]byte, []int) {
	return file_news_v1_models_proto_rawDescGZIP(), []int{3}
}

func (x *FindNewsReply) GetItems() []*NewsInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FindNewsReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type FindNewsByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []string `protobuf:"bytes,1,rep,name=ids,json=ids,proto3" json:"ids"`
}

func (x *FindNewsByIdsRequest) Reset() {
	*x = FindNewsByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_news_v1_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindNewsByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindNewsByIdsRequest) ProtoMessage() {}

func (x *FindNewsByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_news_v1_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindNewsByIdsRequest.ProtoReflect.Descriptor instead.
func (*FindNewsByIdsRequest) Descriptor() ([]byte, []int) {
	return file_news_v1_models_proto_rawDescGZIP(), []int{4}
}

func (x *FindNewsByIdsRequest) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type FindNewsByIdsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*NewsInfo `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *FindNewsByIdsReply) Reset() {
	*x = FindNewsByIdsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_news_v1_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindNewsByIdsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindNewsByIdsReply) ProtoMessage() {}

func (x *FindNewsByIdsReply) ProtoReflect() protoreflect.Message {
	mi := &file_news_v1_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindNewsByIdsReply.ProtoReflect.Descriptor instead.
func (*FindNewsByIdsReply) Descriptor() ([]byte, []int) {
	return file_news_v1_models_proto_rawDescGZIP(), []int{5}
}

func (x *FindNewsByIdsReply) GetItems() []*NewsInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_news_v1_models_proto protoreflect.FileDescriptor

var file_news_v1_models_proto_rawDesc = []byte{
	0x0a, 0x14, 0x6e, 0x65, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73,
	0x2e, 0x76, 0x31, 0x22, 0x24, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x77, 0x73, 0x42, 0x79,
	0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe7, 0x03, 0x0a, 0x08, 0x4e, 0x65,
	0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x6e, 0x65, 0x77, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6e, 0x65, 0x77, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x77,
	0x73, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e,
	0x65, 0x77, 0x73, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x65, 0x77,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6e, 0x65, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x1c, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29,
	0x0a, 0x10, 0x6e, 0x65, 0x77, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6e, 0x65, 0x77, 0x73, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x69, 0x73, 0x5f,
	0x73, 0x68, 0x6f, 0x77, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x24, 0x0a,
	0x0e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x67, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x22, 0x39, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x52,
	0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x77,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0x28, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x42, 0x79,
	0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x41, 0x0a, 0x12,
	0x46, 0x69, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x65, 0x77, 0x73, 0x2e, 0x76, 0x31, 0x2e,
	0x4e, 0x65, 0x77, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x42,
	0x10, 0x5a, 0x0e, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x77, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_news_v1_models_proto_rawDescOnce sync.Once
	file_news_v1_models_proto_rawDescData = file_news_v1_models_proto_rawDesc
)

func file_news_v1_models_proto_rawDescGZIP() []byte {
	file_news_v1_models_proto_rawDescOnce.Do(func() {
		file_news_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_news_v1_models_proto_rawDescData)
	})
	return file_news_v1_models_proto_rawDescData
}

var file_news_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_news_v1_models_proto_goTypes = []interface{}{
	(*GetNewsByIdRequest)(nil),   // 0: api.news.v1.GetNewsByIdRequest
	(*NewsInfo)(nil),             // 1: api.news.v1.NewsInfo
	(*FindNewsRequest)(nil),      // 2: api.news.v1.FindNewsRequest
	(*FindNewsReply)(nil),        // 3: api.news.v1.FindNewsReply
	(*FindNewsByIdsRequest)(nil), // 4: api.news.v1.FindNewsByIdsRequest
	(*FindNewsByIdsReply)(nil),   // 5: api.news.v1.FindNewsByIdsReply
}
var file_news_v1_models_proto_depIdxs = []int32{
	1, // 0: api.news.v1.FindNewsReply.items:type_name -> api.news.v1.NewsInfo
	1, // 1: api.news.v1.FindNewsByIdsReply.items:type_name -> api.news.v1.NewsInfo
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_news_v1_models_proto_init() }
func file_news_v1_models_proto_init() {
	if File_news_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_news_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNewsByIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_news_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NewsInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_news_v1_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindNewsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_news_v1_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindNewsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_news_v1_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindNewsByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_news_v1_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindNewsByIdsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_news_v1_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_news_v1_models_proto_goTypes,
		DependencyIndexes: file_news_v1_models_proto_depIdxs,
		MessageInfos:      file_news_v1_models_proto_msgTypes,
	}.Build()
	File_news_v1_models_proto = out.File
	file_news_v1_models_proto_rawDesc = nil
	file_news_v1_models_proto_goTypes = nil
	file_news_v1_models_proto_depIdxs = nil
}
