// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: news/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName       = "/api.news.v1.Service/Healthy"
	Service_GetNewsById_FullMethodName   = "/api.news.v1.Service/GetNewsById"
	Service_FindNews_FullMethodName      = "/api.news.v1.Service/FindNews"
	Service_FindNewsByIds_FullMethodName = "/api.news.v1.Service/FindNewsByIds"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// 获取快讯详情
	GetNewsById(ctx context.Context, in *GetNewsByIdRequest, opts ...grpc.CallOption) (*NewsInfo, error)
	// 获取快讯列表
	FindNews(ctx context.Context, in *FindNewsRequest, opts ...grpc.CallOption) (*FindNewsReply, error)
	// 批量获取快讯列表
	FindNewsByIds(ctx context.Context, in *FindNewsByIdsRequest, opts ...grpc.CallOption) (*FindNewsByIdsReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetNewsById(ctx context.Context, in *GetNewsByIdRequest, opts ...grpc.CallOption) (*NewsInfo, error) {
	out := new(NewsInfo)
	err := c.cc.Invoke(ctx, Service_GetNewsById_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindNews(ctx context.Context, in *FindNewsRequest, opts ...grpc.CallOption) (*FindNewsReply, error) {
	out := new(FindNewsReply)
	err := c.cc.Invoke(ctx, Service_FindNews_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindNewsByIds(ctx context.Context, in *FindNewsByIdsRequest, opts ...grpc.CallOption) (*FindNewsByIdsReply, error) {
	out := new(FindNewsByIdsReply)
	err := c.cc.Invoke(ctx, Service_FindNewsByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// 获取快讯详情
	GetNewsById(context.Context, *GetNewsByIdRequest) (*NewsInfo, error)
	// 获取快讯列表
	FindNews(context.Context, *FindNewsRequest) (*FindNewsReply, error)
	// 批量获取快讯列表
	FindNewsByIds(context.Context, *FindNewsByIdsRequest) (*FindNewsByIdsReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) GetNewsById(context.Context, *GetNewsByIdRequest) (*NewsInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNewsById not implemented")
}
func (UnimplementedServiceServer) FindNews(context.Context, *FindNewsRequest) (*FindNewsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNews not implemented")
}
func (UnimplementedServiceServer) FindNewsByIds(context.Context, *FindNewsByIdsRequest) (*FindNewsByIdsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindNewsByIds not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetNewsById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewsByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetNewsById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetNewsById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetNewsById(ctx, req.(*GetNewsByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindNews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNewsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindNews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindNews_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindNews(ctx, req.(*FindNewsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindNewsByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindNewsByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindNewsByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindNewsByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindNewsByIds(ctx, req.(*FindNewsByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.news.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "GetNewsById",
			Handler:    _Service_GetNewsById_Handler,
		},
		{
			MethodName: "FindNews",
			Handler:    _Service_FindNews_Handler,
		},
		{
			MethodName: "FindNewsByIds",
			Handler:    _Service_FindNewsByIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "news/v1/service.proto",
}
