// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: survey/SurveyPush.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SurveyPushService_QuerySurveyDetail_FullMethodName      = "/Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService/QuerySurveyDetail"
	SurveyPushService_QuerySurveyDetailByIds_FullMethodName = "/Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService/QuerySurveyDetailByIds"
	SurveyPushService_QuerySurveyList_FullMethodName        = "/Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService/QuerySurveyList"
	SurveyPushService_QuerySurveyTop100_FullMethodName      = "/Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService/QuerySurveyTop100"
)

// SurveyPushServiceClient is the client API for SurveyPushService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SurveyPushServiceClient interface {
	QuerySurveyDetail(ctx context.Context, in *SurveyDetailQueryRequest, opts ...grpc.CallOption) (*UnityReply_SurveyDetail, error)
	QuerySurveyDetailByIds(ctx context.Context, in *SurveyQueryByIdsRequest, opts ...grpc.CallOption) (*UnityReply_List_SurveyList, error)
	QuerySurveyList(ctx context.Context, in *QuerySurveyListRequest, opts ...grpc.CallOption) (*UnityReply_ContainerData_SurveyList, error)
	QuerySurveyTop100(ctx context.Context, in *QuerySurveyListRequest, opts ...grpc.CallOption) (*UnityReply_List_SurveyList, error)
}

type surveyPushServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSurveyPushServiceClient(cc grpc.ClientConnInterface) SurveyPushServiceClient {
	return &surveyPushServiceClient{cc}
}

func (c *surveyPushServiceClient) QuerySurveyDetail(ctx context.Context, in *SurveyDetailQueryRequest, opts ...grpc.CallOption) (*UnityReply_SurveyDetail, error) {
	out := new(UnityReply_SurveyDetail)
	err := c.cc.Invoke(ctx, SurveyPushService_QuerySurveyDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyPushServiceClient) QuerySurveyDetailByIds(ctx context.Context, in *SurveyQueryByIdsRequest, opts ...grpc.CallOption) (*UnityReply_List_SurveyList, error) {
	out := new(UnityReply_List_SurveyList)
	err := c.cc.Invoke(ctx, SurveyPushService_QuerySurveyDetailByIds_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyPushServiceClient) QuerySurveyList(ctx context.Context, in *QuerySurveyListRequest, opts ...grpc.CallOption) (*UnityReply_ContainerData_SurveyList, error) {
	out := new(UnityReply_ContainerData_SurveyList)
	err := c.cc.Invoke(ctx, SurveyPushService_QuerySurveyList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyPushServiceClient) QuerySurveyTop100(ctx context.Context, in *QuerySurveyListRequest, opts ...grpc.CallOption) (*UnityReply_List_SurveyList, error) {
	out := new(UnityReply_List_SurveyList)
	err := c.cc.Invoke(ctx, SurveyPushService_QuerySurveyTop100_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurveyPushServiceServer is the server API for SurveyPushService service.
// All implementations must embed UnimplementedSurveyPushServiceServer
// for forward compatibility
type SurveyPushServiceServer interface {
	QuerySurveyDetail(context.Context, *SurveyDetailQueryRequest) (*UnityReply_SurveyDetail, error)
	QuerySurveyDetailByIds(context.Context, *SurveyQueryByIdsRequest) (*UnityReply_List_SurveyList, error)
	QuerySurveyList(context.Context, *QuerySurveyListRequest) (*UnityReply_ContainerData_SurveyList, error)
	QuerySurveyTop100(context.Context, *QuerySurveyListRequest) (*UnityReply_List_SurveyList, error)
	mustEmbedUnimplementedSurveyPushServiceServer()
}

// UnimplementedSurveyPushServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSurveyPushServiceServer struct {
}

func (UnimplementedSurveyPushServiceServer) QuerySurveyDetail(context.Context, *SurveyDetailQueryRequest) (*UnityReply_SurveyDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySurveyDetail not implemented")
}
func (UnimplementedSurveyPushServiceServer) QuerySurveyDetailByIds(context.Context, *SurveyQueryByIdsRequest) (*UnityReply_List_SurveyList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySurveyDetailByIds not implemented")
}
func (UnimplementedSurveyPushServiceServer) QuerySurveyList(context.Context, *QuerySurveyListRequest) (*UnityReply_ContainerData_SurveyList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySurveyList not implemented")
}
func (UnimplementedSurveyPushServiceServer) QuerySurveyTop100(context.Context, *QuerySurveyListRequest) (*UnityReply_List_SurveyList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySurveyTop100 not implemented")
}
func (UnimplementedSurveyPushServiceServer) mustEmbedUnimplementedSurveyPushServiceServer() {}

// UnsafeSurveyPushServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurveyPushServiceServer will
// result in compilation errors.
type UnsafeSurveyPushServiceServer interface {
	mustEmbedUnimplementedSurveyPushServiceServer()
}

func RegisterSurveyPushServiceServer(s grpc.ServiceRegistrar, srv SurveyPushServiceServer) {
	s.RegisterService(&SurveyPushService_ServiceDesc, srv)
}

func _SurveyPushService_QuerySurveyDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyDetailQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyPushServiceServer).QuerySurveyDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyPushService_QuerySurveyDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyPushServiceServer).QuerySurveyDetail(ctx, req.(*SurveyDetailQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyPushService_QuerySurveyDetailByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyQueryByIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyPushServiceServer).QuerySurveyDetailByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyPushService_QuerySurveyDetailByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyPushServiceServer).QuerySurveyDetailByIds(ctx, req.(*SurveyQueryByIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyPushService_QuerySurveyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySurveyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyPushServiceServer).QuerySurveyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyPushService_QuerySurveyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyPushServiceServer).QuerySurveyList(ctx, req.(*QuerySurveyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyPushService_QuerySurveyTop100_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySurveyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyPushServiceServer).QuerySurveyTop100(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyPushService_QuerySurveyTop100_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyPushServiceServer).QuerySurveyTop100(ctx, req.(*QuerySurveyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SurveyPushService_ServiceDesc is the grpc.ServiceDesc for SurveyPushService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurveyPushService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService",
	HandlerType: (*SurveyPushServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QuerySurveyDetail",
			Handler:    _SurveyPushService_QuerySurveyDetail_Handler,
		},
		{
			MethodName: "QuerySurveyDetailByIds",
			Handler:    _SurveyPushService_QuerySurveyDetailByIds_Handler,
		},
		{
			MethodName: "QuerySurveyList",
			Handler:    _SurveyPushService_QuerySurveyList_Handler,
		},
		{
			MethodName: "QuerySurveyTop100",
			Handler:    _SurveyPushService_QuerySurveyTop100_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "survey/SurveyPush.proto",
}
