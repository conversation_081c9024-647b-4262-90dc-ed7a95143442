// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: survey/SurveyPush.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ContainerData_SurveyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64         `protobuf:"varint,1,opt,name=Total,json=Total,proto3" json:"Total"`
	Items []*SurveyList `protobuf:"bytes,2,rep,name=Items,json=Items,proto3" json:"Items"`
}

func (x *ContainerData_SurveyList) Reset() {
	*x = ContainerData_SurveyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContainerData_SurveyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContainerData_SurveyList) ProtoMessage() {}

func (x *ContainerData_SurveyList) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContainerData_SurveyList.ProtoReflect.Descriptor instead.
func (*ContainerData_SurveyList) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{0}
}

func (x *ContainerData_SurveyList) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ContainerData_SurveyList) GetItems() []*SurveyList {
	if x != nil {
		return x.Items
	}
	return nil
}

type ExhibitionSurvey struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExhibitionId        string `protobuf:"bytes,1,opt,name=ExhibitionId,json=ExhibitionId,proto3" json:"ExhibitionId"`                       // 展会id
	Level               int32  `protobuf:"varint,2,opt,name=Level,json=Level,proto3" json:"Level"`                                           // 展会等级
	Position            string `protobuf:"bytes,3,opt,name=Position,json=Position,proto3" json:"Position"`                                   // 展会位置
	ExpoLogo            string `protobuf:"bytes,4,opt,name=ExpoLogo,json=ExpoLogo,proto3" json:"ExpoLogo"`                                   // logo
	ExpomapColor        string `protobuf:"bytes,5,opt,name=ExpomapColor,json=ExpomapColor,proto3" json:"ExpomapColor"`                       // 颜色
	ExpoBgColor         string `protobuf:"bytes,6,opt,name=ExpoBgColor,json=ExpoBgColor,proto3" json:"ExpoBgColor"`                          // 背景色
	ExpoId              string `protobuf:"bytes,7,opt,name=ExpoId,json=ExpoId,proto3" json:"ExpoId"`                                         // id
	ExpoName            string `protobuf:"bytes,8,opt,name=ExpoName,json=ExpoName,proto3" json:"ExpoName"`                                   // 名称
	SurveyDateTimestamp int64  `protobuf:"varint,9,opt,name=SurveyDateTimestamp,json=SurveyDateTimestamp,proto3" json:"SurveyDateTimestamp"` // 实勘时间时间戳
}

func (x *ExhibitionSurvey) Reset() {
	*x = ExhibitionSurvey{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitionSurvey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitionSurvey) ProtoMessage() {}

func (x *ExhibitionSurvey) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitionSurvey.ProtoReflect.Descriptor instead.
func (*ExhibitionSurvey) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{1}
}

func (x *ExhibitionSurvey) GetExhibitionId() string {
	if x != nil {
		return x.ExhibitionId
	}
	return ""
}

func (x *ExhibitionSurvey) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ExhibitionSurvey) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *ExhibitionSurvey) GetExpoLogo() string {
	if x != nil {
		return x.ExpoLogo
	}
	return ""
}

func (x *ExhibitionSurvey) GetExpomapColor() string {
	if x != nil {
		return x.ExpomapColor
	}
	return ""
}

func (x *ExhibitionSurvey) GetExpoBgColor() string {
	if x != nil {
		return x.ExpoBgColor
	}
	return ""
}

func (x *ExhibitionSurvey) GetExpoId() string {
	if x != nil {
		return x.ExpoId
	}
	return ""
}

func (x *ExhibitionSurvey) GetExpoName() string {
	if x != nil {
		return x.ExpoName
	}
	return ""
}

func (x *ExhibitionSurvey) GetSurveyDateTimestamp() int64 {
	if x != nil {
		return x.SurveyDateTimestamp
	}
	return 0
}

type ImageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url    string `protobuf:"bytes,1,opt,name=Url,json=Url,proto3" json:"Url"`           // 图片url
	Width  int32  `protobuf:"varint,2,opt,name=Width,json=Width,proto3" json:"Width"`    // 宽
	Height int32  `protobuf:"varint,3,opt,name=Height,json=Height,proto3" json:"Height"` // 高
}

func (x *ImageInfo) Reset() {
	*x = ImageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageInfo) ProtoMessage() {}

func (x *ImageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageInfo.ProtoReflect.Descriptor instead.
func (*ImageInfo) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{2}
}

func (x *ImageInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ImageInfo) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ImageInfo) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type QuerySurveyListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pageindex int32 `protobuf:"varint,1,opt,name=Pageindex,json=Pageindex,proto3" json:"Pageindex"` // 页数
	PageSize  int32 `protobuf:"varint,2,opt,name=PageSize,json=PageSize,proto3" json:"PageSize"`    // 页大小
}

func (x *QuerySurveyListRequest) Reset() {
	*x = QuerySurveyListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySurveyListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySurveyListRequest) ProtoMessage() {}

func (x *QuerySurveyListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySurveyListRequest.ProtoReflect.Descriptor instead.
func (*QuerySurveyListRequest) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{3}
}

func (x *QuerySurveyListRequest) GetPageindex() int32 {
	if x != nil {
		return x.Pageindex
	}
	return 0
}

func (x *QuerySurveyListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type SurveyDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveyId      string `protobuf:"bytes,1,opt,name=SurveyId,json=SurveyId,proto3" json:"SurveyId"`                // 实勘id
	TraderCode    string `protobuf:"bytes,2,opt,name=TraderCode,json=TraderCode,proto3" json:"TraderCode"`          // 实勘交易商、券商Code
	Seal          string `protobuf:"bytes,3,opt,name=Seal,json=Seal,proto3" json:"Seal"`                            // 印章
	Surveyor      string `protobuf:"bytes,4,opt,name=Surveyor,json=Surveyor,proto3" json:"Surveyor"`                // 实勘人
	Flag          string `protobuf:"bytes,5,opt,name=Flag,json=Flag,proto3" json:"Flag"`                            // 实勘到访地区旗帜
	CountryName   string `protobuf:"bytes,6,opt,name=CountryName,json=CountryName,proto3" json:"CountryName"`       // 当前实勘访问公司所在国家名称
	Evaluate      string `protobuf:"bytes,7,opt,name=Evaluate,json=Evaluate,proto3" json:"Evaluate"`                // 实勘评价
	EvaluateColor string `protobuf:"bytes,8,opt,name=EvaluateColor,json=EvaluateColor,proto3" json:"EvaluateColor"` // 评价颜色
	BaiduImg      string `protobuf:"bytes,9,opt,name=BaiduImg,json=BaiduImg,proto3" json:"BaiduImg"`                // 百度坐标缩略图
	GoogleImg     string `protobuf:"bytes,10,opt,name=GoogleImg,json=GoogleImg,proto3" json:"GoogleImg"`            // 谷歌坐标缩略图
	Content       string `protobuf:"bytes,11,opt,name=Content,json=Content,proto3" json:"Content"`                  // 实勘正文
	VideoCover    string `protobuf:"bytes,12,opt,name=VideoCover,json=VideoCover,proto3" json:"VideoCover"`         // 实勘视频封面图片地址
	Cover         string `protobuf:"bytes,13,opt,name=Cover,json=Cover,proto3" json:"Cover"`                        // 实勘的封面图片地址
	Type          int32  `protobuf:"varint,14,opt,name=Type,json=Type,proto3" json:"Type"`                          // 实勘类型
}

func (x *SurveyDetail) Reset() {
	*x = SurveyDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyDetail) ProtoMessage() {}

func (x *SurveyDetail) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyDetail.ProtoReflect.Descriptor instead.
func (*SurveyDetail) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{4}
}

func (x *SurveyDetail) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *SurveyDetail) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *SurveyDetail) GetSeal() string {
	if x != nil {
		return x.Seal
	}
	return ""
}

func (x *SurveyDetail) GetSurveyor() string {
	if x != nil {
		return x.Surveyor
	}
	return ""
}

func (x *SurveyDetail) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *SurveyDetail) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *SurveyDetail) GetEvaluate() string {
	if x != nil {
		return x.Evaluate
	}
	return ""
}

func (x *SurveyDetail) GetEvaluateColor() string {
	if x != nil {
		return x.EvaluateColor
	}
	return ""
}

func (x *SurveyDetail) GetBaiduImg() string {
	if x != nil {
		return x.BaiduImg
	}
	return ""
}

func (x *SurveyDetail) GetGoogleImg() string {
	if x != nil {
		return x.GoogleImg
	}
	return ""
}

func (x *SurveyDetail) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SurveyDetail) GetVideoCover() string {
	if x != nil {
		return x.VideoCover
	}
	return ""
}

func (x *SurveyDetail) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *SurveyDetail) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type SurveyDetailQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sid string `protobuf:"bytes,1,opt,name=Sid,json=Sid,proto3" json:"Sid"` // 实勘id
}

func (x *SurveyDetailQueryRequest) Reset() {
	*x = SurveyDetailQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyDetailQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyDetailQueryRequest) ProtoMessage() {}

func (x *SurveyDetailQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyDetailQueryRequest.ProtoReflect.Descriptor instead.
func (*SurveyDetailQueryRequest) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{5}
}

func (x *SurveyDetailQueryRequest) GetSid() string {
	if x != nil {
		return x.Sid
	}
	return ""
}

type SurveyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SurveyId            string            `protobuf:"bytes,1,opt,name=SurveyId,json=SurveyId,proto3" json:"SurveyId"`                                   // 实勘id
	TraderCode          string            `protobuf:"bytes,2,opt,name=TraderCode,json=TraderCode,proto3" json:"TraderCode"`                             // 实勘交易商、服务商code
	Evaluate            string            `protobuf:"bytes,3,opt,name=Evaluate,json=Evaluate,proto3" json:"Evaluate"`                                   // 实勘评价
	Type                int32             `protobuf:"varint,4,opt,name=Type,json=Type,proto3" json:"Type"`                                              // 实勘类型
	EvaluateColor       string            `protobuf:"bytes,5,opt,name=EvaluateColor,json=EvaluateColor,proto3" json:"EvaluateColor"`                    // 评价颜色
	Level               int32             `protobuf:"varint,6,opt,name=Level,json=Level,proto3" json:"Level"`                                           // 实勘评价的等级，数字由小到大对应等级依次递减。
	Address             string            `protobuf:"bytes,7,opt,name=Address,json=Address,proto3" json:"Address"`                                      // 展会地址
	SurveyDateTimestamp int64             `protobuf:"varint,8,opt,name=SurveyDateTimestamp,json=SurveyDateTimestamp,proto3" json:"SurveyDateTimestamp"` // 实勘时间时间戳
	Images              []string          `protobuf:"bytes,10,rep,name=Images,json=Images,proto3" json:"Images"`
	Cover               string            `protobuf:"bytes,12,opt,name=Cover,json=Cover,proto3" json:"Cover"`                      // 实勘的封面图片地址
	Ico                 string            `protobuf:"bytes,13,opt,name=Ico,json=Ico,proto3" json:"Ico"`                            // 交易商ICO图标地址
	Flag                string            `protobuf:"bytes,14,opt,name=Flag,json=Flag,proto3" json:"Flag"`                         // 实勘到访地区旗帜
	Company             string            `protobuf:"bytes,15,opt,name=Company,json=Company,proto3" json:"Company"`                // 当前实勘交易商的名称
	LocalCompany        string            `protobuf:"bytes,16,opt,name=LocalCompany,json=LocalCompany,proto3" json:"LocalCompany"` // 当前实勘交易商、券商的名称
	VRCode              string            `protobuf:"bytes,17,opt,name=VRCode,json=VRCode,proto3" json:"VRCode"`
	VisitCountryCode    string            `protobuf:"bytes,18,opt,name=VisitCountryCode,json=VisitCountryCode,proto3" json:"VisitCountryCode"` // 实勘到访地区代码
	VR                  *SurveyVR         `protobuf:"bytes,19,opt,name=VR,json=VR,proto3" json:"VR"`
	CountryName         string            `protobuf:"bytes,20,opt,name=CountryName,json=CountryName,proto3" json:"CountryName"`                    // 当前实勘访问公司所在国家名称
	CityName            string            `protobuf:"bytes,21,opt,name=CityName,json=CityName,proto3" json:"CityName"`                             // 当前实勘访问公司所在城市名称
	Title               string            `protobuf:"bytes,22,opt,name=Title,json=Title,proto3" json:"Title"`                                      // 实勘标题
	Summary             string            `protobuf:"bytes,23,opt,name=Summary,json=Summary,proto3" json:"Summary"`                                // 实勘摘要
	ExhibitionSurvey    *ExhibitionSurvey `protobuf:"bytes,24,opt,name=ExhibitionSurvey,json=ExhibitionSurvey,proto3" json:"ExhibitionSurvey"`     // 展会信息
	Banner              *ImageInfo        `protobuf:"bytes,25,opt,name=Banner,json=Banner,proto3" json:"Banner"`                                   // 头图
	FirstImage          *ImageInfo        `protobuf:"bytes,26,opt,name=FirstImage,json=FirstImage,proto3" json:"FirstImage"`                       // 内容里面的第一张图
	Seal                string            `protobuf:"bytes,27,opt,name=Seal,json=Seal,proto3" json:"Seal"`                                         // 印章
	Surveyor            string            `protobuf:"bytes,28,opt,name=Surveyor,json=Surveyor,proto3" json:"Surveyor"`                             // 实勘人
	BaiduImg            string            `protobuf:"bytes,29,opt,name=BaiduImg,json=BaiduImg,proto3" json:"BaiduImg"`                             // 百度坐标缩略图
	GoogleImg           string            `protobuf:"bytes,30,opt,name=GoogleImg,json=GoogleImg,proto3" json:"GoogleImg"`                          // 谷歌坐标缩略图
	IsServiceProvider   bool              `protobuf:"varint,31,opt,name=IsServiceProvider,json=IsServiceProvider,proto3" json:"IsServiceProvider"` // 是否服务商
	TextPart            string            `protobuf:"bytes,32,opt,name=TextPart,json=TextPart,proto3" json:"TextPart"`                             // 部分正文内容，暂时取120个字，web端使用
	AllImage            []*ImageInfo      `protobuf:"bytes,33,rep,name=AllImage,json=AllImage,proto3" json:"AllImage"`
}

func (x *SurveyList) Reset() {
	*x = SurveyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyList) ProtoMessage() {}

func (x *SurveyList) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyList.ProtoReflect.Descriptor instead.
func (*SurveyList) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{6}
}

func (x *SurveyList) GetSurveyId() string {
	if x != nil {
		return x.SurveyId
	}
	return ""
}

func (x *SurveyList) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *SurveyList) GetEvaluate() string {
	if x != nil {
		return x.Evaluate
	}
	return ""
}

func (x *SurveyList) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SurveyList) GetEvaluateColor() string {
	if x != nil {
		return x.EvaluateColor
	}
	return ""
}

func (x *SurveyList) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *SurveyList) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SurveyList) GetSurveyDateTimestamp() int64 {
	if x != nil {
		return x.SurveyDateTimestamp
	}
	return 0
}

func (x *SurveyList) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *SurveyList) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *SurveyList) GetIco() string {
	if x != nil {
		return x.Ico
	}
	return ""
}

func (x *SurveyList) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *SurveyList) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *SurveyList) GetLocalCompany() string {
	if x != nil {
		return x.LocalCompany
	}
	return ""
}

func (x *SurveyList) GetVRCode() string {
	if x != nil {
		return x.VRCode
	}
	return ""
}

func (x *SurveyList) GetVisitCountryCode() string {
	if x != nil {
		return x.VisitCountryCode
	}
	return ""
}

func (x *SurveyList) GetVR() *SurveyVR {
	if x != nil {
		return x.VR
	}
	return nil
}

func (x *SurveyList) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *SurveyList) GetCityName() string {
	if x != nil {
		return x.CityName
	}
	return ""
}

func (x *SurveyList) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SurveyList) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *SurveyList) GetExhibitionSurvey() *ExhibitionSurvey {
	if x != nil {
		return x.ExhibitionSurvey
	}
	return nil
}

func (x *SurveyList) GetBanner() *ImageInfo {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *SurveyList) GetFirstImage() *ImageInfo {
	if x != nil {
		return x.FirstImage
	}
	return nil
}

func (x *SurveyList) GetSeal() string {
	if x != nil {
		return x.Seal
	}
	return ""
}

func (x *SurveyList) GetSurveyor() string {
	if x != nil {
		return x.Surveyor
	}
	return ""
}

func (x *SurveyList) GetBaiduImg() string {
	if x != nil {
		return x.BaiduImg
	}
	return ""
}

func (x *SurveyList) GetGoogleImg() string {
	if x != nil {
		return x.GoogleImg
	}
	return ""
}

func (x *SurveyList) GetIsServiceProvider() bool {
	if x != nil {
		return x.IsServiceProvider
	}
	return false
}

func (x *SurveyList) GetTextPart() string {
	if x != nil {
		return x.TextPart
	}
	return ""
}

func (x *SurveyList) GetAllImage() []*ImageInfo {
	if x != nil {
		return x.AllImage
	}
	return nil
}

type SurveyQueryByIdsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sids []string `protobuf:"bytes,1,rep,name=Sids,json=Sids,proto3" json:"Sids"`
}

func (x *SurveyQueryByIdsRequest) Reset() {
	*x = SurveyQueryByIdsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyQueryByIdsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyQueryByIdsRequest) ProtoMessage() {}

func (x *SurveyQueryByIdsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyQueryByIdsRequest.ProtoReflect.Descriptor instead.
func (*SurveyQueryByIdsRequest) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{7}
}

func (x *SurveyQueryByIdsRequest) GetSids() []string {
	if x != nil {
		return x.Sids
	}
	return nil
}

type SurveyVR struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    string `protobuf:"bytes,1,opt,name=Code,json=Code,proto3" json:"Code"`          // VR唯一标识Code
	Cover   string `protobuf:"bytes,2,opt,name=Cover,json=Cover,proto3" json:"Cover"`       // VR封面图
	Loading string `protobuf:"bytes,3,opt,name=Loading,json=Loading,proto3" json:"Loading"` // VR加载图
	Area    string `protobuf:"bytes,4,opt,name=Area,json=Area,proto3" json:"Area"`          // 实勘所在区域
	Flag    string `protobuf:"bytes,5,opt,name=Flag,json=Flag,proto3" json:"Flag"`          // 勘察地区旗帜
	Time    int32  `protobuf:"varint,6,opt,name=Time,json=Time,proto3" json:"Time"`         // 实勘时间
	Url     string `protobuf:"bytes,9,opt,name=Url,json=Url,proto3" json:"Url"`             // VR详情地址
}

func (x *SurveyVR) Reset() {
	*x = SurveyVR{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SurveyVR) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurveyVR) ProtoMessage() {}

func (x *SurveyVR) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurveyVR.ProtoReflect.Descriptor instead.
func (*SurveyVR) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{8}
}

func (x *SurveyVR) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SurveyVR) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *SurveyVR) GetLoading() string {
	if x != nil {
		return x.Loading
	}
	return ""
}

func (x *SurveyVR) GetArea() string {
	if x != nil {
		return x.Area
	}
	return ""
}

func (x *SurveyVR) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *SurveyVR) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *SurveyVR) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type UnityReply_ContainerData_SurveyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                      `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string                    `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *ContainerData_SurveyList `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_ContainerData_SurveyList) Reset() {
	*x = UnityReply_ContainerData_SurveyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_ContainerData_SurveyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_ContainerData_SurveyList) ProtoMessage() {}

func (x *UnityReply_ContainerData_SurveyList) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_ContainerData_SurveyList.ProtoReflect.Descriptor instead.
func (*UnityReply_ContainerData_SurveyList) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{9}
}

func (x *UnityReply_ContainerData_SurveyList) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_ContainerData_SurveyList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_ContainerData_SurveyList) GetResult() *ContainerData_SurveyList {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_List_SurveyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool          `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string        `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    []*SurveyList `protobuf:"bytes,3,rep,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_List_SurveyList) Reset() {
	*x = UnityReply_List_SurveyList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_List_SurveyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_List_SurveyList) ProtoMessage() {}

func (x *UnityReply_List_SurveyList) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_List_SurveyList.ProtoReflect.Descriptor instead.
func (*UnityReply_List_SurveyList) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{10}
}

func (x *UnityReply_List_SurveyList) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_List_SurveyList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_List_SurveyList) GetResult() []*SurveyList {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_SurveyDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool          `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string        `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *SurveyDetail `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_SurveyDetail) Reset() {
	*x = UnityReply_SurveyDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_survey_SurveyPush_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_SurveyDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_SurveyDetail) ProtoMessage() {}

func (x *UnityReply_SurveyDetail) ProtoReflect() protoreflect.Message {
	mi := &file_survey_SurveyPush_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_SurveyDetail.ProtoReflect.Descriptor instead.
func (*UnityReply_SurveyDetail) Descriptor() ([]byte, []int) {
	return file_survey_SurveyPush_proto_rawDescGZIP(), []int{11}
}

func (x *UnityReply_SurveyDetail) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_SurveyDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_SurveyDetail) GetResult() *SurveyDetail {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_survey_SurveyPush_proto protoreflect.FileDescriptor

var file_survey_SurveyPush_proto_rawDesc = []byte{
	0x0a, 0x17, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50,
	0x75, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x33, 0x53, 0x65, 0x70, 0x61, 0x74,
	0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x22, 0x87,
	0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x54,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x55, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3f, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63,
	0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xb0, 0x02, 0x0a, 0x10, 0x45, 0x78, 0x68,
	0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x22, 0x0a,
	0x0c, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x6f, 0x67, 0x6f, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x45, 0x78, 0x70, 0x6f, 0x4c, 0x6f, 0x67, 0x6f, 0x12,
	0x22, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x6d, 0x61, 0x70, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x45, 0x78, 0x70, 0x6f, 0x6d, 0x61, 0x70, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x42, 0x67, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x45, 0x78, 0x70, 0x6f, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x45, 0x78, 0x70, 0x6f, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x45, 0x78, 0x70, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x45, 0x78, 0x70, 0x6f, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x4b, 0x0a, 0x09, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69,
	0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x52, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x67, 0x65, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x50, 0x61, 0x67, 0x65, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x90, 0x03, 0x0a,
	0x0c, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x65, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x65, 0x61, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x20, 0x0a,
	0x0b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x45,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6d, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6d, 0x67, 0x12, 0x1c, 0x0a,
	0x09, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x56, 0x69, 0x64, 0x65, 0x6f, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x2c, 0x0a, 0x18, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x53,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x53, 0x69, 0x64, 0x22, 0xce, 0x09,
	0x0a, 0x0a, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x30,
	0x0a, 0x13, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x16, 0x0a, 0x06, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x76, 0x65,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x49, 0x63, 0x6f, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x49, 0x63, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x22,
	0x0a, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x56, 0x52, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x56, 0x52, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x56, 0x69,
	0x73, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x56, 0x69, 0x73, 0x69, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x02, 0x56, 0x52, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72,
	0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56,
	0x52, 0x52, 0x02, 0x56, 0x52, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x69, 0x74, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x69, 0x74, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x12, 0x71, 0x0a, 0x10, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69,
	0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x52, 0x10, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x56, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x5e,
	0x0a, 0x0a, 0x46, 0x69, 0x72, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72,
	0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x46, 0x69, 0x72, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x53, 0x65, 0x61, 0x6c, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x65,
	0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x6f, 0x72, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x6f, 0x72, 0x12, 0x1a,
	0x0a, 0x08, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6d, 0x67, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x42, 0x61, 0x69, 0x64, 0x75, 0x49, 0x6d, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x47,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x2c, 0x0a, 0x11, 0x49, 0x73, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x11, 0x49, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61,
	0x72, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61,
	0x72, 0x74, 0x12, 0x5a, 0x0a, 0x08, 0x41, 0x6c, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x21,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x41, 0x6c, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x2d,
	0x0a, 0x17, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x53, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x53, 0x69, 0x64, 0x73, 0x22, 0x9c, 0x01,
	0x0a, 0x08, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x56, 0x52, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x43,
	0x6f, 0x76, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x41, 0x72, 0x65, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x41, 0x72,
	0x65, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72,
	0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x22, 0xc4, 0x01, 0x0a,
	0x23, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x43, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x65, 0x0a, 0x06,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x53,
	0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b,
	0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xad, 0x01, 0x0a, 0x1a, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x5f, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x57, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x53, 0x65, 0x70,
	0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x17, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x59, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x53, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x32, 0xef, 0x05, 0x0a, 0x11, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x50, 0x75, 0x73,
	0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xb0, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4d,
	0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57,
	0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x73, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4c, 0x2e,
	0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69,
	0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xb7, 0x01, 0x0a, 0x16,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x4c, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x53, 0x75, 0x72,
	0x76, 0x65, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x79, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x4f, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0xb8, 0x01, 0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x2e, 0x53, 0x65, 0x70, 0x61,
	0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78,
	0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x58, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69,
	0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0xb1, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x54, 0x6f, 0x70, 0x31, 0x30, 0x30, 0x12, 0x4b, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x4f, 0x2e, 0x53, 0x65, 0x70, 0x61, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x47,
	0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x5f, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x16, 0x5a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x75, 0x72, 0x76,
	0x65, 0x79, 0x70, 0x75, 0x73, 0x68, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_survey_SurveyPush_proto_rawDescOnce sync.Once
	file_survey_SurveyPush_proto_rawDescData = file_survey_SurveyPush_proto_rawDesc
)

func file_survey_SurveyPush_proto_rawDescGZIP() []byte {
	file_survey_SurveyPush_proto_rawDescOnce.Do(func() {
		file_survey_SurveyPush_proto_rawDescData = protoimpl.X.CompressGZIP(file_survey_SurveyPush_proto_rawDescData)
	})
	return file_survey_SurveyPush_proto_rawDescData
}

var file_survey_SurveyPush_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_survey_SurveyPush_proto_goTypes = []interface{}{
	(*ContainerData_SurveyList)(nil),            // 0: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ContainerData_SurveyList
	(*ExhibitionSurvey)(nil),                    // 1: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ExhibitionSurvey
	(*ImageInfo)(nil),                           // 2: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ImageInfo
	(*QuerySurveyListRequest)(nil),              // 3: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.QuerySurveyListRequest
	(*SurveyDetail)(nil),                        // 4: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyDetail
	(*SurveyDetailQueryRequest)(nil),            // 5: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyDetailQueryRequest
	(*SurveyList)(nil),                          // 6: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList
	(*SurveyQueryByIdsRequest)(nil),             // 7: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyQueryByIdsRequest
	(*SurveyVR)(nil),                            // 8: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyVR
	(*UnityReply_ContainerData_SurveyList)(nil), // 9: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_ContainerData_SurveyList
	(*UnityReply_List_SurveyList)(nil),          // 10: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_List_SurveyList
	(*UnityReply_SurveyDetail)(nil),             // 11: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_SurveyDetail
}
var file_survey_SurveyPush_proto_depIdxs = []int32{
	6,  // 0: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ContainerData_SurveyList.Items:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList
	8,  // 1: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList.VR:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyVR
	1,  // 2: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList.ExhibitionSurvey:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ExhibitionSurvey
	2,  // 3: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList.Banner:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ImageInfo
	2,  // 4: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList.FirstImage:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ImageInfo
	2,  // 5: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList.AllImage:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ImageInfo
	0,  // 6: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_ContainerData_SurveyList.Result:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.ContainerData_SurveyList
	6,  // 7: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_List_SurveyList.Result:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyList
	4,  // 8: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_SurveyDetail.Result:type_name -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyDetail
	5,  // 9: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyDetail:input_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyDetailQueryRequest
	7,  // 10: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyDetailByIds:input_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyQueryByIdsRequest
	3,  // 11: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyList:input_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.QuerySurveyListRequest
	3,  // 12: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyTop100:input_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.QuerySurveyListRequest
	11, // 13: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyDetail:output_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_SurveyDetail
	10, // 14: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyDetailByIds:output_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_List_SurveyList
	9,  // 15: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyList:output_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_ContainerData_SurveyList
	10, // 16: Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.SurveyPushService.QuerySurveyTop100:output_type -> Sepatate.Survey.Services.Grpc.WikiFx.ProtoContracts.UnityReply_List_SurveyList
	13, // [13:17] is the sub-list for method output_type
	9,  // [9:13] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_survey_SurveyPush_proto_init() }
func file_survey_SurveyPush_proto_init() {
	if File_survey_SurveyPush_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_survey_SurveyPush_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContainerData_SurveyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitionSurvey); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySurveyListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurveyDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurveyDetailQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurveyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurveyQueryByIdsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SurveyVR); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_ContainerData_SurveyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_List_SurveyList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_survey_SurveyPush_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_SurveyDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_survey_SurveyPush_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_survey_SurveyPush_proto_goTypes,
		DependencyIndexes: file_survey_SurveyPush_proto_depIdxs,
		MessageInfos:      file_survey_SurveyPush_proto_msgTypes,
	}.Build()
	File_survey_SurveyPush_proto = out.File
	file_survey_SurveyPush_proto_rawDesc = nil
	file_survey_SurveyPush_proto_goTypes = nil
	file_survey_SurveyPush_proto_depIdxs = nil
}
