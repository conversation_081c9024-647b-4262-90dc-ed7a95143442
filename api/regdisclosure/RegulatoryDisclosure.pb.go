// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: regdisclosure/RegulatoryDisclosure.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CategoryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryName string `protobuf:"bytes,1,opt,name=CategoryName,json=CategoryName,proto3" json:"CategoryName"` // 分类名称
	CategoryId   string `protobuf:"bytes,2,opt,name=CategoryId,json=CategoryId,proto3" json:"CategoryId"`       // 分类id
}

func (x *CategoryInfo) Reset() {
	*x = CategoryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryInfo) ProtoMessage() {}

func (x *CategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryInfo.ProtoReflect.Descriptor instead.
func (*CategoryInfo) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{0}
}

func (x *CategoryInfo) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *CategoryInfo) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

type ContainerData_RegulatoryDisclosureReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                        `protobuf:"varint,1,opt,name=Total,json=Total,proto3" json:"Total"`
	Items []*RegulatoryDisclosureReply `protobuf:"bytes,2,rep,name=Items,json=Items,proto3" json:"Items"`
}

func (x *ContainerData_RegulatoryDisclosureReply) Reset() {
	*x = ContainerData_RegulatoryDisclosureReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContainerData_RegulatoryDisclosureReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContainerData_RegulatoryDisclosureReply) ProtoMessage() {}

func (x *ContainerData_RegulatoryDisclosureReply) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContainerData_RegulatoryDisclosureReply.ProtoReflect.Descriptor instead.
func (*ContainerData_RegulatoryDisclosureReply) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{1}
}

func (x *ContainerData_RegulatoryDisclosureReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ContainerData_RegulatoryDisclosureReply) GetItems() []*RegulatoryDisclosureReply {
	if x != nil {
		return x.Items
	}
	return nil
}

type ImageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url         string `protobuf:"bytes,1,opt,name=Url,json=Url,proto3" json:"Url"`                          // url
	ImageWidth  int32  `protobuf:"varint,2,opt,name=ImageWidth,json=ImageWidth,proto3" json:"ImageWidth"`    // 图片宽度
	ImageHeight int32  `protobuf:"varint,3,opt,name=ImageHeight,json=ImageHeight,proto3" json:"ImageHeight"` // 图片高度
}

func (x *ImageInfo) Reset() {
	*x = ImageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageInfo) ProtoMessage() {}

func (x *ImageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageInfo.ProtoReflect.Descriptor instead.
func (*ImageInfo) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{2}
}

func (x *ImageInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ImageInfo) GetImageWidth() int32 {
	if x != nil {
		return x.ImageWidth
	}
	return 0
}

func (x *ImageInfo) GetImageHeight() int32 {
	if x != nil {
		return x.ImageHeight
	}
	return 0
}

type RegulatoryDisclosureCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int32 `protobuf:"varint,1,opt,name=Count,json=Count,proto3" json:"Count"` // 数量
}

func (x *RegulatoryDisclosureCountRequest) Reset() {
	*x = RegulatoryDisclosureCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegulatoryDisclosureCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegulatoryDisclosureCountRequest) ProtoMessage() {}

func (x *RegulatoryDisclosureCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegulatoryDisclosureCountRequest.ProtoReflect.Descriptor instead.
func (*RegulatoryDisclosureCountRequest) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{3}
}

func (x *RegulatoryDisclosureCountRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type RegulatoryDisclosureListByRegulatorCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegulatorCode string `protobuf:"bytes,1,opt,name=RegulatorCode,json=RegulatorCode,proto3" json:"RegulatorCode"` // 监管机构Code
	PageIndex     int32  `protobuf:"varint,2,opt,name=PageIndex,json=PageIndex,proto3" json:"PageIndex"`            // 页码
	PageSize      int32  `protobuf:"varint,3,opt,name=PageSize,json=PageSize,proto3" json:"PageSize"`               // 数量
}

func (x *RegulatoryDisclosureListByRegulatorCodeRequest) Reset() {
	*x = RegulatoryDisclosureListByRegulatorCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegulatoryDisclosureListByRegulatorCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegulatoryDisclosureListByRegulatorCodeRequest) ProtoMessage() {}

func (x *RegulatoryDisclosureListByRegulatorCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegulatoryDisclosureListByRegulatorCodeRequest.ProtoReflect.Descriptor instead.
func (*RegulatoryDisclosureListByRegulatorCodeRequest) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{4}
}

func (x *RegulatoryDisclosureListByRegulatorCodeRequest) GetRegulatorCode() string {
	if x != nil {
		return x.RegulatorCode
	}
	return ""
}

func (x *RegulatoryDisclosureListByRegulatorCodeRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *RegulatoryDisclosureListByRegulatorCodeRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type RegulatoryDisclosureReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegId           string        `protobuf:"bytes,1,opt,name=RegId,json=RegId,proto3" json:"RegId"`                                // 标识
	Traders         []string      `protobuf:"bytes,2,rep,name=Traders,json=Traders,proto3" json:"Traders"`                          // 披露的交易商信息
	CopyTraders     []string      `protobuf:"bytes,3,rep,name=CopyTraders,json=CopyTraders,proto3" json:"CopyTraders"`              // 被克隆的交易商
	RegulatorCode   string        `protobuf:"bytes,4,opt,name=RegulatorCode,json=RegulatorCode,proto3" json:"RegulatorCode"`        // 机构code
	Tag             *TagInfo      `protobuf:"bytes,5,opt,name=Tag,json=Tag,proto3" json:"Tag"`                                      // 标签
	Category        *CategoryInfo `protobuf:"bytes,6,opt,name=Category,json=Category,proto3" json:"Category"`                       // 分类
	Summary         *SummaryInfo  `protobuf:"bytes,7,opt,name=Summary,json=Summary,proto3" json:"Summary"`                          // 摘要信息
	Title           string        `protobuf:"bytes,8,opt,name=Title,json=Title,proto3" json:"Title"`                                // 标题
	Content         string        `protobuf:"bytes,9,opt,name=Content,json=Content,proto3" json:"Content"`                          // 内容
	Seal            string        `protobuf:"bytes,10,opt,name=Seal,json=Seal,proto3" json:"Seal"`                                  // 章
	Image           *ImageInfo    `protobuf:"bytes,11,opt,name=Image,json=Image,proto3" json:"Image"`                               // 图片
	ContentLanguage string        `protobuf:"bytes,12,opt,name=ContentLanguage,json=ContentLanguage,proto3" json:"ContentLanguage"` // 内容语言
}

func (x *RegulatoryDisclosureReply) Reset() {
	*x = RegulatoryDisclosureReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegulatoryDisclosureReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegulatoryDisclosureReply) ProtoMessage() {}

func (x *RegulatoryDisclosureReply) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegulatoryDisclosureReply.ProtoReflect.Descriptor instead.
func (*RegulatoryDisclosureReply) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{5}
}

func (x *RegulatoryDisclosureReply) GetRegId() string {
	if x != nil {
		return x.RegId
	}
	return ""
}

func (x *RegulatoryDisclosureReply) GetTraders() []string {
	if x != nil {
		return x.Traders
	}
	return nil
}

func (x *RegulatoryDisclosureReply) GetCopyTraders() []string {
	if x != nil {
		return x.CopyTraders
	}
	return nil
}

func (x *RegulatoryDisclosureReply) GetRegulatorCode() string {
	if x != nil {
		return x.RegulatorCode
	}
	return ""
}

func (x *RegulatoryDisclosureReply) GetTag() *TagInfo {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *RegulatoryDisclosureReply) GetCategory() *CategoryInfo {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *RegulatoryDisclosureReply) GetSummary() *SummaryInfo {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *RegulatoryDisclosureReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *RegulatoryDisclosureReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *RegulatoryDisclosureReply) GetSeal() string {
	if x != nil {
		return x.Seal
	}
	return ""
}

func (x *RegulatoryDisclosureReply) GetImage() *ImageInfo {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *RegulatoryDisclosureReply) GetContentLanguage() string {
	if x != nil {
		return x.ContentLanguage
	}
	return ""
}

type RegulatoryDisclosureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegulatoryDisclosureIdList []string `protobuf:"bytes,1,rep,name=RegulatoryDisclosureIdList,json=RegulatoryDisclosureIdList,proto3" json:"RegulatoryDisclosureIdList"` // 监管披露Id集合
}

func (x *RegulatoryDisclosureRequest) Reset() {
	*x = RegulatoryDisclosureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegulatoryDisclosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegulatoryDisclosureRequest) ProtoMessage() {}

func (x *RegulatoryDisclosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegulatoryDisclosureRequest.ProtoReflect.Descriptor instead.
func (*RegulatoryDisclosureRequest) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{6}
}

func (x *RegulatoryDisclosureRequest) GetRegulatoryDisclosureIdList() []string {
	if x != nil {
		return x.RegulatoryDisclosureIdList
	}
	return nil
}

type RegulatoryInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegulatorCode string `protobuf:"bytes,1,opt,name=RegulatorCode,json=RegulatorCode,proto3" json:"RegulatorCode"` // 机构code
}

func (x *RegulatoryInfoReply) Reset() {
	*x = RegulatoryInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegulatoryInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegulatoryInfoReply) ProtoMessage() {}

func (x *RegulatoryInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegulatoryInfoReply.ProtoReflect.Descriptor instead.
func (*RegulatoryInfoReply) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{7}
}

func (x *RegulatoryInfoReply) GetRegulatorCode() string {
	if x != nil {
		return x.RegulatorCode
	}
	return ""
}

type RegulatoryInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegulatoryDisclosureId string `protobuf:"bytes,1,opt,name=RegulatoryDisclosureId,json=RegulatoryDisclosureId,proto3" json:"RegulatoryDisclosureId"` // 监管披露Id
}

func (x *RegulatoryInfoRequest) Reset() {
	*x = RegulatoryInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegulatoryInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegulatoryInfoRequest) ProtoMessage() {}

func (x *RegulatoryInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegulatoryInfoRequest.ProtoReflect.Descriptor instead.
func (*RegulatoryInfoRequest) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{8}
}

func (x *RegulatoryInfoRequest) GetRegulatoryDisclosureId() string {
	if x != nil {
		return x.RegulatoryDisclosureId
	}
	return ""
}

type SummaryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule          int32  `protobuf:"varint,1,opt,name=Rule,json=Rule,proto3" json:"Rule"`                              // 匹配规则
	RuleName      string `protobuf:"bytes,2,opt,name=RuleName,json=RuleName,proto3" json:"RuleName"`                   // 规则名称
	WikiTimestamp int64  `protobuf:"varint,3,opt,name=wiki_timestamp,json=wikiTimestamp,proto3" json:"wiki_timestamp"` // 披露时间戳
	PenaltyAmount string `protobuf:"bytes,4,opt,name=PenaltyAmount,json=PenaltyAmount,proto3" json:"PenaltyAmount"`    // 处罚金额(完整)
	AmountSymbol  string `protobuf:"bytes,5,opt,name=AmountSymbol,json=AmountSymbol,proto3" json:"AmountSymbol"`       // 处罚金额符号
	Reason        string `protobuf:"bytes,6,opt,name=Reason,json=Reason,proto3" json:"Reason"`                         // 处罚原因
}

func (x *SummaryInfo) Reset() {
	*x = SummaryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SummaryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SummaryInfo) ProtoMessage() {}

func (x *SummaryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SummaryInfo.ProtoReflect.Descriptor instead.
func (*SummaryInfo) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{9}
}

func (x *SummaryInfo) GetRule() int32 {
	if x != nil {
		return x.Rule
	}
	return 0
}

func (x *SummaryInfo) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *SummaryInfo) GetWikiTimestamp() int64 {
	if x != nil {
		return x.WikiTimestamp
	}
	return 0
}

func (x *SummaryInfo) GetPenaltyAmount() string {
	if x != nil {
		return x.PenaltyAmount
	}
	return ""
}

func (x *SummaryInfo) GetAmountSymbol() string {
	if x != nil {
		return x.AmountSymbol
	}
	return ""
}

func (x *SummaryInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type TagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagName      string `protobuf:"bytes,1,opt,name=TagName,json=TagName,proto3" json:"TagName"`                // 标签名称
	Color        string `protobuf:"bytes,2,opt,name=Color,json=Color,proto3" json:"Color"`                      // 颜色
	TagCode      string `protobuf:"bytes,3,opt,name=TagCode,json=TagCode,proto3" json:"TagCode"`                // code
	LanguageCode string `protobuf:"bytes,4,opt,name=LanguageCode,json=LanguageCode,proto3" json:"LanguageCode"` // 语言code
	TagLogo      string `protobuf:"bytes,5,opt,name=TagLogo,json=TagLogo,proto3" json:"TagLogo"`                // logo
}

func (x *TagInfo) Reset() {
	*x = TagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TagInfo) ProtoMessage() {}

func (x *TagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TagInfo.ProtoReflect.Descriptor instead.
func (*TagInfo) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{10}
}

func (x *TagInfo) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

func (x *TagInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *TagInfo) GetTagCode() string {
	if x != nil {
		return x.TagCode
	}
	return ""
}

func (x *TagInfo) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *TagInfo) GetTagLogo() string {
	if x != nil {
		return x.TagLogo
	}
	return ""
}

type UnityReply_ContainerData_RegulatoryDisclosureReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                                     `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string                                   `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *ContainerData_RegulatoryDisclosureReply `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_ContainerData_RegulatoryDisclosureReply) Reset() {
	*x = UnityReply_ContainerData_RegulatoryDisclosureReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_ContainerData_RegulatoryDisclosureReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_ContainerData_RegulatoryDisclosureReply) ProtoMessage() {}

func (x *UnityReply_ContainerData_RegulatoryDisclosureReply) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_ContainerData_RegulatoryDisclosureReply.ProtoReflect.Descriptor instead.
func (*UnityReply_ContainerData_RegulatoryDisclosureReply) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{11}
}

func (x *UnityReply_ContainerData_RegulatoryDisclosureReply) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_ContainerData_RegulatoryDisclosureReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_ContainerData_RegulatoryDisclosureReply) GetResult() *ContainerData_RegulatoryDisclosureReply {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_List_RegulatoryDisclosureReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                         `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string                       `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    []*RegulatoryDisclosureReply `protobuf:"bytes,3,rep,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_List_RegulatoryDisclosureReply) Reset() {
	*x = UnityReply_List_RegulatoryDisclosureReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_List_RegulatoryDisclosureReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_List_RegulatoryDisclosureReply) ProtoMessage() {}

func (x *UnityReply_List_RegulatoryDisclosureReply) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_List_RegulatoryDisclosureReply.ProtoReflect.Descriptor instead.
func (*UnityReply_List_RegulatoryDisclosureReply) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{12}
}

func (x *UnityReply_List_RegulatoryDisclosureReply) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_List_RegulatoryDisclosureReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_List_RegulatoryDisclosureReply) GetResult() []*RegulatoryDisclosureReply {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_RegulatoryInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                 `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string               `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *RegulatoryInfoReply `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_RegulatoryInfoReply) Reset() {
	*x = UnityReply_RegulatoryInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_RegulatoryInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_RegulatoryInfoReply) ProtoMessage() {}

func (x *UnityReply_RegulatoryInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_RegulatoryInfoReply.ProtoReflect.Descriptor instead.
func (*UnityReply_RegulatoryInfoReply) Descriptor() ([]byte, []int) {
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP(), []int{13}
}

func (x *UnityReply_RegulatoryInfoReply) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_RegulatoryInfoReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_RegulatoryInfoReply) GetResult() *RegulatoryInfoReply {
	if x != nil {
		return x.Result
	}
	return nil
}

var File_regdisclosure_RegulatoryDisclosure_proto protoreflect.FileDescriptor

var file_regdisclosure_RegulatoryDisclosure_proto_rawDesc = []byte{
	0x0a, 0x28, 0x72, 0x65, 0x67, 0x64, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2f,
	0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2d, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x22, 0x52, 0x0a, 0x0c, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x22, 0x9f, 0x01,
	0x0a, 0x27, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x5f,
	0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x5e, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48,
	0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52,
	0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22,
	0x5f, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x1e,
	0x0a, 0x0a, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x20,
	0x0a, 0x0b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x22, 0x38, 0x0a, 0x20, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69,
	0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x90, 0x01, 0x0a, 0x2e, 0x52,
	0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a,
	0x0d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xca, 0x04,
	0x0a, 0x19, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x52,
	0x65, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x52, 0x65, 0x67, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x43,
	0x6f, 0x70, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0b, 0x43, 0x6f, 0x70, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x24, 0x0a,
	0x0d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x48, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70,
	0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x54, 0x61, 0x67, 0x12, 0x57, 0x0a,
	0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x3b, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63,
	0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x54, 0x0a, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x53, 0x65, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x53, 0x65, 0x61, 0x6c,
	0x12, 0x4e, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x38, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63,
	0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x22, 0x5d, 0x0a, 0x1b, 0x52, 0x65,
	0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x1a, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x1a, 0x52,
	0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3b, 0x0a, 0x13, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x24, 0x0a, 0x0d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x4f, 0x0a, 0x15, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x36, 0x0a, 0x16, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x49, 0x64, 0x22, 0xc6, 0x01, 0x0a, 0x0b, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x52,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x77, 0x69, 0x6b, 0x69, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x77, 0x69, 0x6b, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x24,
	0x0a, 0x0d, 0x50, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x65, 0x6e, 0x61, 0x6c, 0x74, 0x79, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x79,
	0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0x91, 0x01, 0x0a, 0x07, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x54, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54,
	0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x54, 0x61, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54,
	0x61, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x61,
	0x67, 0x4c, 0x6f, 0x67, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54, 0x61, 0x67,
	0x4c, 0x6f, 0x67, 0x6f, 0x22, 0xdc, 0x01, 0x0a, 0x32, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x5f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x49,
	0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x6e, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79,
	0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e,
	0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0xc5, 0x01, 0x0a, 0x29, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x5f, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12,
	0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x60, 0x0a, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x1e,
	0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c,
	0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5a, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f,
	0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x32, 0xd0, 0x06, 0x0a, 0x1b, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x44, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e,
	0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74,
	0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4d,
	0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55,
	0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0xc3, 0x01,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44,
	0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4a, 0x2e,
	0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65,
	0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x58, 0x2e, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x5f, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61,
	0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0xee, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x79, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x5d, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44,
	0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47,
	0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x61, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69,
	0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72,
	0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0xce, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x30, 0x12, 0x4f, 0x2e, 0x52, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x75, 0x6c,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x58, 0x2e, 0x52, 0x65,
	0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x20, 0x5a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x67,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x64, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_regdisclosure_RegulatoryDisclosure_proto_rawDescOnce sync.Once
	file_regdisclosure_RegulatoryDisclosure_proto_rawDescData = file_regdisclosure_RegulatoryDisclosure_proto_rawDesc
)

func file_regdisclosure_RegulatoryDisclosure_proto_rawDescGZIP() []byte {
	file_regdisclosure_RegulatoryDisclosure_proto_rawDescOnce.Do(func() {
		file_regdisclosure_RegulatoryDisclosure_proto_rawDescData = protoimpl.X.CompressGZIP(file_regdisclosure_RegulatoryDisclosure_proto_rawDescData)
	})
	return file_regdisclosure_RegulatoryDisclosure_proto_rawDescData
}

var file_regdisclosure_RegulatoryDisclosure_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_regdisclosure_RegulatoryDisclosure_proto_goTypes = []interface{}{
	(*CategoryInfo)(nil),                                       // 0: RegulatoryDisclosure.Core.Grpc.ProtoContracts.CategoryInfo
	(*ContainerData_RegulatoryDisclosureReply)(nil),            // 1: RegulatoryDisclosure.Core.Grpc.ProtoContracts.ContainerData_RegulatoryDisclosureReply
	(*ImageInfo)(nil),                                          // 2: RegulatoryDisclosure.Core.Grpc.ProtoContracts.ImageInfo
	(*RegulatoryDisclosureCountRequest)(nil),                   // 3: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureCountRequest
	(*RegulatoryDisclosureListByRegulatorCodeRequest)(nil),     // 4: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureListByRegulatorCodeRequest
	(*RegulatoryDisclosureReply)(nil),                          // 5: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply
	(*RegulatoryDisclosureRequest)(nil),                        // 6: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureRequest
	(*RegulatoryInfoReply)(nil),                                // 7: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryInfoReply
	(*RegulatoryInfoRequest)(nil),                              // 8: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryInfoRequest
	(*SummaryInfo)(nil),                                        // 9: RegulatoryDisclosure.Core.Grpc.ProtoContracts.SummaryInfo
	(*TagInfo)(nil),                                            // 10: RegulatoryDisclosure.Core.Grpc.ProtoContracts.TagInfo
	(*UnityReply_ContainerData_RegulatoryDisclosureReply)(nil), // 11: RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_ContainerData_RegulatoryDisclosureReply
	(*UnityReply_List_RegulatoryDisclosureReply)(nil),          // 12: RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_List_RegulatoryDisclosureReply
	(*UnityReply_RegulatoryInfoReply)(nil),                     // 13: RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_RegulatoryInfoReply
}
var file_regdisclosure_RegulatoryDisclosure_proto_depIdxs = []int32{
	5,  // 0: RegulatoryDisclosure.Core.Grpc.ProtoContracts.ContainerData_RegulatoryDisclosureReply.Items:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply
	10, // 1: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply.Tag:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.TagInfo
	0,  // 2: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply.Category:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.CategoryInfo
	9,  // 3: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply.Summary:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.SummaryInfo
	2,  // 4: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply.Image:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.ImageInfo
	1,  // 5: RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_ContainerData_RegulatoryDisclosureReply.Result:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.ContainerData_RegulatoryDisclosureReply
	5,  // 6: RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_List_RegulatoryDisclosureReply.Result:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureReply
	7,  // 7: RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_RegulatoryInfoReply.Result:type_name -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryInfoReply
	8,  // 8: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryCode:input_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryInfoRequest
	6,  // 9: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryDisclosureList:input_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureRequest
	4,  // 10: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryDisclosureListByRegulatorCode:input_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureListByRegulatorCodeRequest
	3,  // 11: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryDisclosureListTop100:input_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureCountRequest
	13, // 12: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryCode:output_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_RegulatoryInfoReply
	12, // 13: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryDisclosureList:output_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_List_RegulatoryDisclosureReply
	11, // 14: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryDisclosureListByRegulatorCode:output_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_ContainerData_RegulatoryDisclosureReply
	12, // 15: RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService.GetRegulatoryDisclosureListTop100:output_type -> RegulatoryDisclosure.Core.Grpc.ProtoContracts.UnityReply_List_RegulatoryDisclosureReply
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_regdisclosure_RegulatoryDisclosure_proto_init() }
func file_regdisclosure_RegulatoryDisclosure_proto_init() {
	if File_regdisclosure_RegulatoryDisclosure_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContainerData_RegulatoryDisclosureReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegulatoryDisclosureCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegulatoryDisclosureListByRegulatorCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegulatoryDisclosureReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegulatoryDisclosureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegulatoryInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegulatoryInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SummaryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_ContainerData_RegulatoryDisclosureReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_List_RegulatoryDisclosureReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_regdisclosure_RegulatoryDisclosure_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_RegulatoryInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_regdisclosure_RegulatoryDisclosure_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_regdisclosure_RegulatoryDisclosure_proto_goTypes,
		DependencyIndexes: file_regdisclosure_RegulatoryDisclosure_proto_depIdxs,
		MessageInfos:      file_regdisclosure_RegulatoryDisclosure_proto_msgTypes,
	}.Build()
	File_regdisclosure_RegulatoryDisclosure_proto = out.File
	file_regdisclosure_RegulatoryDisclosure_proto_rawDesc = nil
	file_regdisclosure_RegulatoryDisclosure_proto_goTypes = nil
	file_regdisclosure_RegulatoryDisclosure_proto_depIdxs = nil
}
