// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: regdisclosure/RegulatoryDisclosure.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RegulatoryDisclosureService_GetRegulatoryCode_FullMethodName                          = "/RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService/GetRegulatoryCode"
	RegulatoryDisclosureService_GetRegulatoryDisclosureList_FullMethodName                = "/RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService/GetRegulatoryDisclosureList"
	RegulatoryDisclosureService_GetRegulatoryDisclosureListByRegulatorCode_FullMethodName = "/RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService/GetRegulatoryDisclosureListByRegulatorCode"
	RegulatoryDisclosureService_GetRegulatoryDisclosureListTop100_FullMethodName          = "/RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService/GetRegulatoryDisclosureListTop100"
)

// RegulatoryDisclosureServiceClient is the client API for RegulatoryDisclosureService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RegulatoryDisclosureServiceClient interface {
	GetRegulatoryCode(ctx context.Context, in *RegulatoryInfoRequest, opts ...grpc.CallOption) (*UnityReply_RegulatoryInfoReply, error)
	GetRegulatoryDisclosureList(ctx context.Context, in *RegulatoryDisclosureRequest, opts ...grpc.CallOption) (*UnityReply_List_RegulatoryDisclosureReply, error)
	GetRegulatoryDisclosureListByRegulatorCode(ctx context.Context, in *RegulatoryDisclosureListByRegulatorCodeRequest, opts ...grpc.CallOption) (*UnityReply_ContainerData_RegulatoryDisclosureReply, error)
	GetRegulatoryDisclosureListTop100(ctx context.Context, in *RegulatoryDisclosureCountRequest, opts ...grpc.CallOption) (*UnityReply_List_RegulatoryDisclosureReply, error)
}

type regulatoryDisclosureServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRegulatoryDisclosureServiceClient(cc grpc.ClientConnInterface) RegulatoryDisclosureServiceClient {
	return &regulatoryDisclosureServiceClient{cc}
}

func (c *regulatoryDisclosureServiceClient) GetRegulatoryCode(ctx context.Context, in *RegulatoryInfoRequest, opts ...grpc.CallOption) (*UnityReply_RegulatoryInfoReply, error) {
	out := new(UnityReply_RegulatoryInfoReply)
	err := c.cc.Invoke(ctx, RegulatoryDisclosureService_GetRegulatoryCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regulatoryDisclosureServiceClient) GetRegulatoryDisclosureList(ctx context.Context, in *RegulatoryDisclosureRequest, opts ...grpc.CallOption) (*UnityReply_List_RegulatoryDisclosureReply, error) {
	out := new(UnityReply_List_RegulatoryDisclosureReply)
	err := c.cc.Invoke(ctx, RegulatoryDisclosureService_GetRegulatoryDisclosureList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regulatoryDisclosureServiceClient) GetRegulatoryDisclosureListByRegulatorCode(ctx context.Context, in *RegulatoryDisclosureListByRegulatorCodeRequest, opts ...grpc.CallOption) (*UnityReply_ContainerData_RegulatoryDisclosureReply, error) {
	out := new(UnityReply_ContainerData_RegulatoryDisclosureReply)
	err := c.cc.Invoke(ctx, RegulatoryDisclosureService_GetRegulatoryDisclosureListByRegulatorCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *regulatoryDisclosureServiceClient) GetRegulatoryDisclosureListTop100(ctx context.Context, in *RegulatoryDisclosureCountRequest, opts ...grpc.CallOption) (*UnityReply_List_RegulatoryDisclosureReply, error) {
	out := new(UnityReply_List_RegulatoryDisclosureReply)
	err := c.cc.Invoke(ctx, RegulatoryDisclosureService_GetRegulatoryDisclosureListTop100_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RegulatoryDisclosureServiceServer is the server API for RegulatoryDisclosureService service.
// All implementations must embed UnimplementedRegulatoryDisclosureServiceServer
// for forward compatibility
type RegulatoryDisclosureServiceServer interface {
	GetRegulatoryCode(context.Context, *RegulatoryInfoRequest) (*UnityReply_RegulatoryInfoReply, error)
	GetRegulatoryDisclosureList(context.Context, *RegulatoryDisclosureRequest) (*UnityReply_List_RegulatoryDisclosureReply, error)
	GetRegulatoryDisclosureListByRegulatorCode(context.Context, *RegulatoryDisclosureListByRegulatorCodeRequest) (*UnityReply_ContainerData_RegulatoryDisclosureReply, error)
	GetRegulatoryDisclosureListTop100(context.Context, *RegulatoryDisclosureCountRequest) (*UnityReply_List_RegulatoryDisclosureReply, error)
	mustEmbedUnimplementedRegulatoryDisclosureServiceServer()
}

// UnimplementedRegulatoryDisclosureServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRegulatoryDisclosureServiceServer struct {
}

func (UnimplementedRegulatoryDisclosureServiceServer) GetRegulatoryCode(context.Context, *RegulatoryInfoRequest) (*UnityReply_RegulatoryInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegulatoryCode not implemented")
}
func (UnimplementedRegulatoryDisclosureServiceServer) GetRegulatoryDisclosureList(context.Context, *RegulatoryDisclosureRequest) (*UnityReply_List_RegulatoryDisclosureReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegulatoryDisclosureList not implemented")
}
func (UnimplementedRegulatoryDisclosureServiceServer) GetRegulatoryDisclosureListByRegulatorCode(context.Context, *RegulatoryDisclosureListByRegulatorCodeRequest) (*UnityReply_ContainerData_RegulatoryDisclosureReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegulatoryDisclosureListByRegulatorCode not implemented")
}
func (UnimplementedRegulatoryDisclosureServiceServer) GetRegulatoryDisclosureListTop100(context.Context, *RegulatoryDisclosureCountRequest) (*UnityReply_List_RegulatoryDisclosureReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRegulatoryDisclosureListTop100 not implemented")
}
func (UnimplementedRegulatoryDisclosureServiceServer) mustEmbedUnimplementedRegulatoryDisclosureServiceServer() {
}

// UnsafeRegulatoryDisclosureServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RegulatoryDisclosureServiceServer will
// result in compilation errors.
type UnsafeRegulatoryDisclosureServiceServer interface {
	mustEmbedUnimplementedRegulatoryDisclosureServiceServer()
}

func RegisterRegulatoryDisclosureServiceServer(s grpc.ServiceRegistrar, srv RegulatoryDisclosureServiceServer) {
	s.RegisterService(&RegulatoryDisclosureService_ServiceDesc, srv)
}

func _RegulatoryDisclosureService_GetRegulatoryCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegulatoryInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegulatoryDisclosureService_GetRegulatoryCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryCode(ctx, req.(*RegulatoryInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegulatoryDisclosureService_GetRegulatoryDisclosureList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegulatoryDisclosureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryDisclosureList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegulatoryDisclosureService_GetRegulatoryDisclosureList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryDisclosureList(ctx, req.(*RegulatoryDisclosureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegulatoryDisclosureService_GetRegulatoryDisclosureListByRegulatorCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegulatoryDisclosureListByRegulatorCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryDisclosureListByRegulatorCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegulatoryDisclosureService_GetRegulatoryDisclosureListByRegulatorCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryDisclosureListByRegulatorCode(ctx, req.(*RegulatoryDisclosureListByRegulatorCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RegulatoryDisclosureService_GetRegulatoryDisclosureListTop100_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegulatoryDisclosureCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryDisclosureListTop100(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RegulatoryDisclosureService_GetRegulatoryDisclosureListTop100_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RegulatoryDisclosureServiceServer).GetRegulatoryDisclosureListTop100(ctx, req.(*RegulatoryDisclosureCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RegulatoryDisclosureService_ServiceDesc is the grpc.ServiceDesc for RegulatoryDisclosureService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RegulatoryDisclosureService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "RegulatoryDisclosure.Core.Grpc.ProtoContracts.RegulatoryDisclosureService",
	HandlerType: (*RegulatoryDisclosureServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRegulatoryCode",
			Handler:    _RegulatoryDisclosureService_GetRegulatoryCode_Handler,
		},
		{
			MethodName: "GetRegulatoryDisclosureList",
			Handler:    _RegulatoryDisclosureService_GetRegulatoryDisclosureList_Handler,
		},
		{
			MethodName: "GetRegulatoryDisclosureListByRegulatorCode",
			Handler:    _RegulatoryDisclosureService_GetRegulatoryDisclosureListByRegulatorCode_Handler,
		},
		{
			MethodName: "GetRegulatoryDisclosureListTop100",
			Handler:    _RegulatoryDisclosureService_GetRegulatoryDisclosureListTop100_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "regdisclosure/RegulatoryDisclosure.proto",
}
