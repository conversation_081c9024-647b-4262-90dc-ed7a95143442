// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/common.proto

package common

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on EmptyRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmptyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmptyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmptyRequestMultiError, or
// nil if none found.
func (m *EmptyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EmptyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyRequestMultiError(errors)
	}

	return nil
}

// EmptyRequestMultiError is an error wrapping multiple validation errors
// returned by EmptyRequest.ValidateAll() if the designated constraints aren't met.
type EmptyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyRequestMultiError) AllErrors() []error { return m }

// EmptyRequestValidationError is the validation error returned by
// EmptyRequest.Validate if the designated constraints aren't met.
type EmptyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyRequestValidationError) ErrorName() string { return "EmptyRequestValidationError" }

// Error satisfies the builtin error interface
func (e EmptyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmptyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyRequestValidationError{}

// Validate checks the field values on EmptyReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EmptyReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmptyReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EmptyReplyMultiError, or
// nil if none found.
func (m *EmptyReply) ValidateAll() error {
	return m.validate(true)
}

func (m *EmptyReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return EmptyReplyMultiError(errors)
	}

	return nil
}

// EmptyReplyMultiError is an error wrapping multiple validation errors
// returned by EmptyReply.ValidateAll() if the designated constraints aren't met.
type EmptyReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmptyReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmptyReplyMultiError) AllErrors() []error { return m }

// EmptyReplyValidationError is the validation error returned by
// EmptyReply.Validate if the designated constraints aren't met.
type EmptyReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmptyReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmptyReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmptyReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmptyReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmptyReplyValidationError) ErrorName() string { return "EmptyReplyValidationError" }

// Error satisfies the builtin error interface
func (e EmptyReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmptyReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmptyReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmptyReplyValidationError{}

// Validate checks the field values on StringReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StringReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StringReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StringReplyMultiError, or
// nil if none found.
func (m *StringReply) ValidateAll() error {
	return m.validate(true)
}

func (m *StringReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Body

	if len(errors) > 0 {
		return StringReplyMultiError(errors)
	}

	return nil
}

// StringReplyMultiError is an error wrapping multiple validation errors
// returned by StringReply.ValidateAll() if the designated constraints aren't met.
type StringReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StringReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StringReplyMultiError) AllErrors() []error { return m }

// StringReplyValidationError is the validation error returned by
// StringReply.Validate if the designated constraints aren't met.
type StringReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StringReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StringReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StringReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StringReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StringReplyValidationError) ErrorName() string { return "StringReplyValidationError" }

// Error satisfies the builtin error interface
func (e StringReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStringReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StringReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StringReplyValidationError{}

// Validate checks the field values on ErrorReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorReplyMultiError, or
// nil if none found.
func (m *ErrorReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Reason

	// no validation rules for Message

	// no validation rules for Time

	if len(errors) > 0 {
		return ErrorReplyMultiError(errors)
	}

	return nil
}

// ErrorReplyMultiError is an error wrapping multiple validation errors
// returned by ErrorReply.ValidateAll() if the designated constraints aren't met.
type ErrorReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorReplyMultiError) AllErrors() []error { return m }

// ErrorReplyValidationError is the validation error returned by
// ErrorReply.Validate if the designated constraints aren't met.
type ErrorReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorReplyValidationError) ErrorName() string { return "ErrorReplyValidationError" }

// Error satisfies the builtin error interface
func (e ErrorReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorReplyValidationError{}

// Validate checks the field values on HealthyReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HealthyReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthyReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HealthyReplyMultiError, or
// nil if none found.
func (m *HealthyReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthyReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return HealthyReplyMultiError(errors)
	}

	return nil
}

// HealthyReplyMultiError is an error wrapping multiple validation errors
// returned by HealthyReply.ValidateAll() if the designated constraints aren't met.
type HealthyReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthyReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthyReplyMultiError) AllErrors() []error { return m }

// HealthyReplyValidationError is the validation error returned by
// HealthyReply.Validate if the designated constraints aren't met.
type HealthyReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthyReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthyReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthyReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthyReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthyReplyValidationError) ErrorName() string { return "HealthyReplyValidationError" }

// Error satisfies the builtin error interface
func (e HealthyReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthyReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthyReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthyReplyValidationError{}
