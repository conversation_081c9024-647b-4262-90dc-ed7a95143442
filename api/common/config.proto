syntax = "proto3";

package common;

option go_package = "api/common;common";

message ServerConfig {
  message HTTP {
    string network = 1;
    string addr = 2;
    int64 timeout_seconds = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    int64 timeout_seconds = 3;
  }
  message Trace {
    string kind = 1;
    string endpoint = 2;
    string fraction = 3;
  }
  HTTP http = 1; // http信息
  GRPC grpc = 2; // grpc信息
  Trace trace = 3; // 链路追踪信息
  string oss_domain = 4; // 对象存储domain
}

message DataConfig {
  message Database {
    string driver = 1;
    string source = 2;
    int32 level = 3;
    int32 max_open = 4;
    int32 max_idle = 5;
    int32 max_life_time_seconds = 6;
  }
  message Redis {
    string address = 1;
    string password = 2;
    int32 db = 3;
    int32 max_idle = 4;
    int64 read_timeout_seconds = 5;
    int64 write_timeout_seconds = 6;
  }
  Database database = 1;
  Redis redis = 2;
}

message GRPCClient {
  string endpoint = 1;
  int64 timeout_seconds = 2;
}
