// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: common/config.proto

package common

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ServerConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServerConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServerConfigMultiError, or
// nil if none found.
func (m *ServerConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHttp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerConfigValidationError{
					field:  "Http",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerConfigValidationError{
					field:  "Http",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHttp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerConfigValidationError{
				field:  "Http",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGrpc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerConfigValidationError{
					field:  "Grpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerConfigValidationError{
					field:  "Grpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrpc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerConfigValidationError{
				field:  "Grpc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTrace()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerConfigValidationError{
					field:  "Trace",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerConfigValidationError{
					field:  "Trace",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTrace()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerConfigValidationError{
				field:  "Trace",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OssDomain

	if len(errors) > 0 {
		return ServerConfigMultiError(errors)
	}

	return nil
}

// ServerConfigMultiError is an error wrapping multiple validation errors
// returned by ServerConfig.ValidateAll() if the designated constraints aren't met.
type ServerConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerConfigMultiError) AllErrors() []error { return m }

// ServerConfigValidationError is the validation error returned by
// ServerConfig.Validate if the designated constraints aren't met.
type ServerConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerConfigValidationError) ErrorName() string { return "ServerConfigValidationError" }

// Error satisfies the builtin error interface
func (e ServerConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerConfigValidationError{}

// Validate checks the field values on DataConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DataConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DataConfigMultiError, or
// nil if none found.
func (m *DataConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DataConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDatabase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataConfigValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataConfigValidationError{
					field:  "Database",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDatabase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataConfigValidationError{
				field:  "Database",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRedis()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataConfigValidationError{
					field:  "Redis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataConfigValidationError{
					field:  "Redis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedis()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataConfigValidationError{
				field:  "Redis",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DataConfigMultiError(errors)
	}

	return nil
}

// DataConfigMultiError is an error wrapping multiple validation errors
// returned by DataConfig.ValidateAll() if the designated constraints aren't met.
type DataConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataConfigMultiError) AllErrors() []error { return m }

// DataConfigValidationError is the validation error returned by
// DataConfig.Validate if the designated constraints aren't met.
type DataConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataConfigValidationError) ErrorName() string { return "DataConfigValidationError" }

// Error satisfies the builtin error interface
func (e DataConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataConfigValidationError{}

// Validate checks the field values on GRPCClient with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GRPCClient) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GRPCClient with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GRPCClientMultiError, or
// nil if none found.
func (m *GRPCClient) ValidateAll() error {
	return m.validate(true)
}

func (m *GRPCClient) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Endpoint

	// no validation rules for TimeoutSeconds

	if len(errors) > 0 {
		return GRPCClientMultiError(errors)
	}

	return nil
}

// GRPCClientMultiError is an error wrapping multiple validation errors
// returned by GRPCClient.ValidateAll() if the designated constraints aren't met.
type GRPCClientMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GRPCClientMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GRPCClientMultiError) AllErrors() []error { return m }

// GRPCClientValidationError is the validation error returned by
// GRPCClient.Validate if the designated constraints aren't met.
type GRPCClientValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GRPCClientValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GRPCClientValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GRPCClientValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GRPCClientValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GRPCClientValidationError) ErrorName() string { return "GRPCClientValidationError" }

// Error satisfies the builtin error interface
func (e GRPCClientValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGRPCClient.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GRPCClientValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GRPCClientValidationError{}

// Validate checks the field values on ServerConfig_HTTP with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServerConfig_HTTP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerConfig_HTTP with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServerConfig_HTTPMultiError, or nil if none found.
func (m *ServerConfig_HTTP) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerConfig_HTTP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Network

	// no validation rules for Addr

	// no validation rules for TimeoutSeconds

	if len(errors) > 0 {
		return ServerConfig_HTTPMultiError(errors)
	}

	return nil
}

// ServerConfig_HTTPMultiError is an error wrapping multiple validation errors
// returned by ServerConfig_HTTP.ValidateAll() if the designated constraints
// aren't met.
type ServerConfig_HTTPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerConfig_HTTPMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerConfig_HTTPMultiError) AllErrors() []error { return m }

// ServerConfig_HTTPValidationError is the validation error returned by
// ServerConfig_HTTP.Validate if the designated constraints aren't met.
type ServerConfig_HTTPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerConfig_HTTPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerConfig_HTTPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerConfig_HTTPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerConfig_HTTPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerConfig_HTTPValidationError) ErrorName() string {
	return "ServerConfig_HTTPValidationError"
}

// Error satisfies the builtin error interface
func (e ServerConfig_HTTPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerConfig_HTTP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerConfig_HTTPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerConfig_HTTPValidationError{}

// Validate checks the field values on ServerConfig_GRPC with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServerConfig_GRPC) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerConfig_GRPC with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServerConfig_GRPCMultiError, or nil if none found.
func (m *ServerConfig_GRPC) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerConfig_GRPC) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Network

	// no validation rules for Addr

	// no validation rules for TimeoutSeconds

	if len(errors) > 0 {
		return ServerConfig_GRPCMultiError(errors)
	}

	return nil
}

// ServerConfig_GRPCMultiError is an error wrapping multiple validation errors
// returned by ServerConfig_GRPC.ValidateAll() if the designated constraints
// aren't met.
type ServerConfig_GRPCMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerConfig_GRPCMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerConfig_GRPCMultiError) AllErrors() []error { return m }

// ServerConfig_GRPCValidationError is the validation error returned by
// ServerConfig_GRPC.Validate if the designated constraints aren't met.
type ServerConfig_GRPCValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerConfig_GRPCValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerConfig_GRPCValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerConfig_GRPCValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerConfig_GRPCValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerConfig_GRPCValidationError) ErrorName() string {
	return "ServerConfig_GRPCValidationError"
}

// Error satisfies the builtin error interface
func (e ServerConfig_GRPCValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerConfig_GRPC.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerConfig_GRPCValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerConfig_GRPCValidationError{}

// Validate checks the field values on ServerConfig_Trace with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServerConfig_Trace) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerConfig_Trace with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServerConfig_TraceMultiError, or nil if none found.
func (m *ServerConfig_Trace) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerConfig_Trace) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Endpoint

	// no validation rules for Fraction

	if len(errors) > 0 {
		return ServerConfig_TraceMultiError(errors)
	}

	return nil
}

// ServerConfig_TraceMultiError is an error wrapping multiple validation errors
// returned by ServerConfig_Trace.ValidateAll() if the designated constraints
// aren't met.
type ServerConfig_TraceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerConfig_TraceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerConfig_TraceMultiError) AllErrors() []error { return m }

// ServerConfig_TraceValidationError is the validation error returned by
// ServerConfig_Trace.Validate if the designated constraints aren't met.
type ServerConfig_TraceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerConfig_TraceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerConfig_TraceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerConfig_TraceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerConfig_TraceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerConfig_TraceValidationError) ErrorName() string {
	return "ServerConfig_TraceValidationError"
}

// Error satisfies the builtin error interface
func (e ServerConfig_TraceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerConfig_Trace.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerConfig_TraceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerConfig_TraceValidationError{}

// Validate checks the field values on DataConfig_Database with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataConfig_Database) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataConfig_Database with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataConfig_DatabaseMultiError, or nil if none found.
func (m *DataConfig_Database) ValidateAll() error {
	return m.validate(true)
}

func (m *DataConfig_Database) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Driver

	// no validation rules for Source

	// no validation rules for Level

	// no validation rules for MaxOpen

	// no validation rules for MaxIdle

	// no validation rules for MaxLifeTimeSeconds

	if len(errors) > 0 {
		return DataConfig_DatabaseMultiError(errors)
	}

	return nil
}

// DataConfig_DatabaseMultiError is an error wrapping multiple validation
// errors returned by DataConfig_Database.ValidateAll() if the designated
// constraints aren't met.
type DataConfig_DatabaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataConfig_DatabaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataConfig_DatabaseMultiError) AllErrors() []error { return m }

// DataConfig_DatabaseValidationError is the validation error returned by
// DataConfig_Database.Validate if the designated constraints aren't met.
type DataConfig_DatabaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataConfig_DatabaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataConfig_DatabaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataConfig_DatabaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataConfig_DatabaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataConfig_DatabaseValidationError) ErrorName() string {
	return "DataConfig_DatabaseValidationError"
}

// Error satisfies the builtin error interface
func (e DataConfig_DatabaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataConfig_Database.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataConfig_DatabaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataConfig_DatabaseValidationError{}

// Validate checks the field values on DataConfig_Redis with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DataConfig_Redis) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataConfig_Redis with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataConfig_RedisMultiError, or nil if none found.
func (m *DataConfig_Redis) ValidateAll() error {
	return m.validate(true)
}

func (m *DataConfig_Redis) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Address

	// no validation rules for Password

	// no validation rules for Db

	// no validation rules for MaxIdle

	// no validation rules for ReadTimeoutSeconds

	// no validation rules for WriteTimeoutSeconds

	if len(errors) > 0 {
		return DataConfig_RedisMultiError(errors)
	}

	return nil
}

// DataConfig_RedisMultiError is an error wrapping multiple validation errors
// returned by DataConfig_Redis.ValidateAll() if the designated constraints
// aren't met.
type DataConfig_RedisMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataConfig_RedisMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataConfig_RedisMultiError) AllErrors() []error { return m }

// DataConfig_RedisValidationError is the validation error returned by
// DataConfig_Redis.Validate if the designated constraints aren't met.
type DataConfig_RedisValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataConfig_RedisValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataConfig_RedisValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataConfig_RedisValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataConfig_RedisValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataConfig_RedisValidationError) ErrorName() string { return "DataConfig_RedisValidationError" }

// Error satisfies the builtin error interface
func (e DataConfig_RedisValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataConfig_Redis.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataConfig_RedisValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataConfig_RedisValidationError{}
