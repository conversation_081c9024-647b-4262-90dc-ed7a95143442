// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: common/config.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Http      *ServerConfig_HTTP  `protobuf:"bytes,1,opt,name=http,json=http,proto3" json:"http"`                  // http信息
	Grpc      *ServerConfig_GRPC  `protobuf:"bytes,2,opt,name=grpc,json=grpc,proto3" json:"grpc"`                  // grpc信息
	Trace     *ServerConfig_Trace `protobuf:"bytes,3,opt,name=trace,json=trace,proto3" json:"trace"`               // 链路追踪信息
	OssDomain string              `protobuf:"bytes,4,opt,name=oss_domain,json=ossDomain,proto3" json:"oss_domain"` // 对象存储domain
	Alarm     *ServerConfig_Alarm `protobuf:"bytes,5,opt,name=alarm,json=alarm,proto3" json:"alarm"`               // 报警
}

func (x *ServerConfig) Reset() {
	*x = ServerConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig) ProtoMessage() {}

func (x *ServerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig.ProtoReflect.Descriptor instead.
func (*ServerConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{0}
}

func (x *ServerConfig) GetHttp() *ServerConfig_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *ServerConfig) GetGrpc() *ServerConfig_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *ServerConfig) GetTrace() *ServerConfig_Trace {
	if x != nil {
		return x.Trace
	}
	return nil
}

func (x *ServerConfig) GetOssDomain() string {
	if x != nil {
		return x.OssDomain
	}
	return ""
}

func (x *ServerConfig) GetAlarm() *ServerConfig_Alarm {
	if x != nil {
		return x.Alarm
	}
	return nil
}

type DataConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database *DataConfig_Database `protobuf:"bytes,1,opt,name=database,json=database,proto3" json:"database"` // MySQL
	Redis    *DataConfig_Redis    `protobuf:"bytes,2,opt,name=redis,json=redis,proto3" json:"redis"`          // redis
	Elastic  *DataConfig_Elastic  `protobuf:"bytes,3,opt,name=elastic,json=elastic,proto3" json:"elastic"`    // es
	Postgres *DataConfig_Database `protobuf:"bytes,4,opt,name=postgres,json=postgres,proto3" json:"postgres"` // hologres
	Odps     *DataConfig_ODPS     `protobuf:"bytes,5,opt,name=odps,json=odps,proto3" json:"odps"`             // maxcompute
	Mongo    *DataConfig_Database `protobuf:"bytes,6,opt,name=mongo,json=mongo,proto3" json:"mongo"`          // mongodb
}

func (x *DataConfig) Reset() {
	*x = DataConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConfig) ProtoMessage() {}

func (x *DataConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConfig.ProtoReflect.Descriptor instead.
func (*DataConfig) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{1}
}

func (x *DataConfig) GetDatabase() *DataConfig_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *DataConfig) GetRedis() *DataConfig_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *DataConfig) GetElastic() *DataConfig_Elastic {
	if x != nil {
		return x.Elastic
	}
	return nil
}

func (x *DataConfig) GetPostgres() *DataConfig_Database {
	if x != nil {
		return x.Postgres
	}
	return nil
}

func (x *DataConfig) GetOdps() *DataConfig_ODPS {
	if x != nil {
		return x.Odps
	}
	return nil
}

func (x *DataConfig) GetMongo() *DataConfig_Database {
	if x != nil {
		return x.Mongo
	}
	return nil
}

type GRPCClient struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Endpoint       string `protobuf:"bytes,1,opt,name=endpoint,json=endpoint,proto3" json:"endpoint"`
	TimeoutSeconds int64  `protobuf:"varint,2,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds"`
}

func (x *GRPCClient) Reset() {
	*x = GRPCClient{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GRPCClient) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GRPCClient) ProtoMessage() {}

func (x *GRPCClient) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GRPCClient.ProtoReflect.Descriptor instead.
func (*GRPCClient) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{2}
}

func (x *GRPCClient) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *GRPCClient) GetTimeoutSeconds() int64 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

type ServerConfig_HTTP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network        string `protobuf:"bytes,1,opt,name=network,json=network,proto3" json:"network"`
	Addr           string `protobuf:"bytes,2,opt,name=addr,json=addr,proto3" json:"addr"`
	TimeoutSeconds int64  `protobuf:"varint,3,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds"`
}

func (x *ServerConfig_HTTP) Reset() {
	*x = ServerConfig_HTTP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerConfig_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig_HTTP) ProtoMessage() {}

func (x *ServerConfig_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig_HTTP.ProtoReflect.Descriptor instead.
func (*ServerConfig_HTTP) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ServerConfig_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ServerConfig_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *ServerConfig_HTTP) GetTimeoutSeconds() int64 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

type ServerConfig_GRPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Network        string `protobuf:"bytes,1,opt,name=network,json=network,proto3" json:"network"`
	Addr           string `protobuf:"bytes,2,opt,name=addr,json=addr,proto3" json:"addr"`
	TimeoutSeconds int64  `protobuf:"varint,3,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds"`
}

func (x *ServerConfig_GRPC) Reset() {
	*x = ServerConfig_GRPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerConfig_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig_GRPC) ProtoMessage() {}

func (x *ServerConfig_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig_GRPC.ProtoReflect.Descriptor instead.
func (*ServerConfig_GRPC) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ServerConfig_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *ServerConfig_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *ServerConfig_GRPC) GetTimeoutSeconds() int64 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

type ServerConfig_Trace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind     string `protobuf:"bytes,1,opt,name=kind,json=kind,proto3" json:"kind"`
	Endpoint string `protobuf:"bytes,2,opt,name=endpoint,json=endpoint,proto3" json:"endpoint"`
	Fraction string `protobuf:"bytes,3,opt,name=fraction,json=fraction,proto3" json:"fraction"`
}

func (x *ServerConfig_Trace) Reset() {
	*x = ServerConfig_Trace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerConfig_Trace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig_Trace) ProtoMessage() {}

func (x *ServerConfig_Trace) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig_Trace.ProtoReflect.Descriptor instead.
func (*ServerConfig_Trace) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ServerConfig_Trace) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ServerConfig_Trace) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *ServerConfig_Trace) GetFraction() string {
	if x != nil {
		return x.Fraction
	}
	return ""
}

type ServerConfig_Alarm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Webhook      string   `protobuf:"bytes,1,opt,name=webhook,json=webhook,proto3" json:"webhook"`                     // webhook
	DelayMinutes int64    `protobuf:"varint,2,opt,name=delay_minutes,json=delayMinutes,proto3" json:"delay_minutes"`   // 默认值 2
	NoDelayCount int64    `protobuf:"varint,3,opt,name=no_delay_count,json=noDelayCount,proto3" json:"no_delay_count"` // 默认值 10
	ServiceName  string   `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name"`       // 服务名称，如果不传取环境变量的数据
	Users        []string `protobuf:"bytes,5,rep,name=users,json=users,proto3" json:"users"`                           // 报警at人名称
}

func (x *ServerConfig_Alarm) Reset() {
	*x = ServerConfig_Alarm{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerConfig_Alarm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig_Alarm) ProtoMessage() {}

func (x *ServerConfig_Alarm) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig_Alarm.ProtoReflect.Descriptor instead.
func (*ServerConfig_Alarm) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{0, 3}
}

func (x *ServerConfig_Alarm) GetWebhook() string {
	if x != nil {
		return x.Webhook
	}
	return ""
}

func (x *ServerConfig_Alarm) GetDelayMinutes() int64 {
	if x != nil {
		return x.DelayMinutes
	}
	return 0
}

func (x *ServerConfig_Alarm) GetNoDelayCount() int64 {
	if x != nil {
		return x.NoDelayCount
	}
	return 0
}

func (x *ServerConfig_Alarm) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ServerConfig_Alarm) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type DataConfig_Database struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Driver             string `protobuf:"bytes,1,opt,name=driver,json=driver,proto3" json:"driver"`
	Source             string `protobuf:"bytes,2,opt,name=source,json=source,proto3" json:"source"`
	Level              int32  `protobuf:"varint,3,opt,name=level,json=level,proto3" json:"level"`
	MaxOpen            int32  `protobuf:"varint,4,opt,name=max_open,json=maxOpen,proto3" json:"max_open"`
	MaxIdle            int32  `protobuf:"varint,5,opt,name=max_idle,json=maxIdle,proto3" json:"max_idle"`
	MaxLifeTimeSeconds int32  `protobuf:"varint,6,opt,name=max_life_time_seconds,json=maxLifeTimeSeconds,proto3" json:"max_life_time_seconds"`
}

func (x *DataConfig_Database) Reset() {
	*x = DataConfig_Database{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConfig_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConfig_Database) ProtoMessage() {}

func (x *DataConfig_Database) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConfig_Database.ProtoReflect.Descriptor instead.
func (*DataConfig_Database) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{1, 0}
}

func (x *DataConfig_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *DataConfig_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *DataConfig_Database) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *DataConfig_Database) GetMaxOpen() int32 {
	if x != nil {
		return x.MaxOpen
	}
	return 0
}

func (x *DataConfig_Database) GetMaxIdle() int32 {
	if x != nil {
		return x.MaxIdle
	}
	return 0
}

func (x *DataConfig_Database) GetMaxLifeTimeSeconds() int32 {
	if x != nil {
		return x.MaxLifeTimeSeconds
	}
	return 0
}

type DataConfig_Redis struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address             string `protobuf:"bytes,1,opt,name=address,json=address,proto3" json:"address"`
	Password            string `protobuf:"bytes,2,opt,name=password,json=password,proto3" json:"password"`
	Db                  int32  `protobuf:"varint,3,opt,name=db,json=db,proto3" json:"db"`
	MaxIdle             int32  `protobuf:"varint,4,opt,name=max_idle,json=maxIdle,proto3" json:"max_idle"`
	ReadTimeoutSeconds  int64  `protobuf:"varint,5,opt,name=read_timeout_seconds,json=readTimeoutSeconds,proto3" json:"read_timeout_seconds"`
	WriteTimeoutSeconds int64  `protobuf:"varint,6,opt,name=write_timeout_seconds,json=writeTimeoutSeconds,proto3" json:"write_timeout_seconds"`
	Username            string `protobuf:"bytes,7,opt,name=username,json=username,proto3" json:"username"`
}

func (x *DataConfig_Redis) Reset() {
	*x = DataConfig_Redis{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConfig_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConfig_Redis) ProtoMessage() {}

func (x *DataConfig_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConfig_Redis.ProtoReflect.Descriptor instead.
func (*DataConfig_Redis) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{1, 1}
}

func (x *DataConfig_Redis) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *DataConfig_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DataConfig_Redis) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

func (x *DataConfig_Redis) GetMaxIdle() int32 {
	if x != nil {
		return x.MaxIdle
	}
	return 0
}

func (x *DataConfig_Redis) GetReadTimeoutSeconds() int64 {
	if x != nil {
		return x.ReadTimeoutSeconds
	}
	return 0
}

func (x *DataConfig_Redis) GetWriteTimeoutSeconds() int64 {
	if x != nil {
		return x.WriteTimeoutSeconds
	}
	return 0
}

func (x *DataConfig_Redis) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

type DataConfig_Elastic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Source   string `protobuf:"bytes,1,opt,name=source,json=source,proto3" json:"source"`
	Username string `protobuf:"bytes,2,opt,name=username,json=username,proto3" json:"username"`
	Password string `protobuf:"bytes,3,opt,name=password,json=password,proto3" json:"password"`
	Sniff    bool   `protobuf:"varint,4,opt,name=sniff,json=sniff,proto3" json:"sniff"`
}

func (x *DataConfig_Elastic) Reset() {
	*x = DataConfig_Elastic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConfig_Elastic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConfig_Elastic) ProtoMessage() {}

func (x *DataConfig_Elastic) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConfig_Elastic.ProtoReflect.Descriptor instead.
func (*DataConfig_Elastic) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{1, 2}
}

func (x *DataConfig_Elastic) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *DataConfig_Elastic) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *DataConfig_Elastic) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DataConfig_Elastic) GetSniff() bool {
	if x != nil {
		return x.Sniff
	}
	return false
}

type DataConfig_ODPS struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId          string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	AccountKey         string `protobuf:"bytes,2,opt,name=account_key,json=accountKey,proto3" json:"account_key"`
	Endpoint           string `protobuf:"bytes,3,opt,name=endpoint,json=endpoint,proto3" json:"endpoint"`
	DefaultProjectName string `protobuf:"bytes,4,opt,name=default_project_name,json=defaultProjectName,proto3" json:"default_project_name"`
}

func (x *DataConfig_ODPS) Reset() {
	*x = DataConfig_ODPS{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataConfig_ODPS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataConfig_ODPS) ProtoMessage() {}

func (x *DataConfig_ODPS) ProtoReflect() protoreflect.Message {
	mi := &file_common_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataConfig_ODPS.ProtoReflect.Descriptor instead.
func (*DataConfig_ODPS) Descriptor() ([]byte, []int) {
	return file_common_config_proto_rawDescGZIP(), []int{1, 3}
}

func (x *DataConfig_ODPS) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *DataConfig_ODPS) GetAccountKey() string {
	if x != nil {
		return x.AccountKey
	}
	return ""
}

func (x *DataConfig_ODPS) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *DataConfig_ODPS) GetDefaultProjectName() string {
	if x != nil {
		return x.DefaultProjectName
	}
	return ""
}

var File_common_config_proto protoreflect.FileDescriptor

var file_common_config_proto_rawDesc = []byte{
	0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xaa, 0x05,
	0x0a, 0x0c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2d,
	0x0a, 0x04, 0x68, 0x74, 0x74, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x04, 0x68, 0x74, 0x74, 0x70, 0x12, 0x2d, 0x0a,
	0x04, 0x67, 0x72, 0x70, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x47, 0x52, 0x50, 0x43, 0x52, 0x04, 0x67, 0x72, 0x70, 0x63, 0x12, 0x30, 0x0a, 0x05,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x6f, 0x73, 0x73, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6f, 0x73, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x30, 0x0a,
	0x05, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x05, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x1a,
	0x5d, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x5d,
	0x0a, 0x04, 0x47, 0x52, 0x50, 0x43, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x64, 0x64, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0x53, 0x0a,
	0x05, 0x54, 0x72, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x72, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0xa5, 0x01, 0x0a, 0x05, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x12, 0x18, 0x0a, 0x07,
	0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77,
	0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f,
	0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64,
	0x65, 0x6c, 0x61, 0x79, 0x4d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x6e,
	0x6f, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0c, 0x6e, 0x6f, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0xf5, 0x07, 0x0a, 0x0a, 0x44,
	0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61,
	0x73, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x65, 0x64, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x52, 0x65, 0x64, 0x69, 0x73, 0x52, 0x05, 0x72, 0x65, 0x64,
	0x69, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x45, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x52,
	0x07, 0x65, 0x6c, 0x61, 0x73, 0x74, 0x69, 0x63, 0x12, 0x37, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65,
	0x73, 0x12, 0x2b, 0x0a, 0x04, 0x6f, 0x64, 0x70, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4f, 0x44, 0x50, 0x53, 0x52, 0x04, 0x6f, 0x64, 0x70, 0x73, 0x12, 0x31,
	0x0a, 0x05, 0x6d, 0x6f, 0x6e, 0x67, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x67,
	0x6f, 0x1a, 0xb9, 0x01, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x6f, 0x70, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x4f, 0x70, 0x65, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6d, 0x61, 0x78, 0x49, 0x64, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x6d, 0x61,
	0x78, 0x5f, 0x6c, 0x69, 0x66, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x6d, 0x61, 0x78, 0x4c, 0x69,
	0x66, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x1a, 0xea, 0x01,
	0x0a, 0x05, 0x52, 0x65, 0x64, 0x69, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a,
	0x02, 0x64, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x64, 0x62, 0x12, 0x19, 0x0a,
	0x08, 0x6d, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x6d, 0x61, 0x78, 0x49, 0x64, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x61, 0x64,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x61, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x1a, 0x6f, 0x0a, 0x07, 0x45, 0x6c,
	0x61, 0x73, 0x74, 0x69, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x6e, 0x69, 0x66, 0x66, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x73, 0x6e, 0x69, 0x66, 0x66, 0x1a, 0x94, 0x01, 0x0a, 0x04,
	0x4f, 0x44, 0x50, 0x53, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x30, 0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x51, 0x0a, 0x0a, 0x47, 0x52, 0x50, 0x43, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x53, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x42, 0x13, 0x5a, 0x11, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_common_config_proto_rawDescOnce sync.Once
	file_common_config_proto_rawDescData = file_common_config_proto_rawDesc
)

func file_common_config_proto_rawDescGZIP() []byte {
	file_common_config_proto_rawDescOnce.Do(func() {
		file_common_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_config_proto_rawDescData)
	})
	return file_common_config_proto_rawDescData
}

var file_common_config_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_common_config_proto_goTypes = []interface{}{
	(*ServerConfig)(nil),        // 0: common.ServerConfig
	(*DataConfig)(nil),          // 1: common.DataConfig
	(*GRPCClient)(nil),          // 2: common.GRPCClient
	(*ServerConfig_HTTP)(nil),   // 3: common.ServerConfig.HTTP
	(*ServerConfig_GRPC)(nil),   // 4: common.ServerConfig.GRPC
	(*ServerConfig_Trace)(nil),  // 5: common.ServerConfig.Trace
	(*ServerConfig_Alarm)(nil),  // 6: common.ServerConfig.Alarm
	(*DataConfig_Database)(nil), // 7: common.DataConfig.Database
	(*DataConfig_Redis)(nil),    // 8: common.DataConfig.Redis
	(*DataConfig_Elastic)(nil),  // 9: common.DataConfig.Elastic
	(*DataConfig_ODPS)(nil),     // 10: common.DataConfig.ODPS
}
var file_common_config_proto_depIdxs = []int32{
	3,  // 0: common.ServerConfig.http:type_name -> common.ServerConfig.HTTP
	4,  // 1: common.ServerConfig.grpc:type_name -> common.ServerConfig.GRPC
	5,  // 2: common.ServerConfig.trace:type_name -> common.ServerConfig.Trace
	6,  // 3: common.ServerConfig.alarm:type_name -> common.ServerConfig.Alarm
	7,  // 4: common.DataConfig.database:type_name -> common.DataConfig.Database
	8,  // 5: common.DataConfig.redis:type_name -> common.DataConfig.Redis
	9,  // 6: common.DataConfig.elastic:type_name -> common.DataConfig.Elastic
	7,  // 7: common.DataConfig.postgres:type_name -> common.DataConfig.Database
	10, // 8: common.DataConfig.odps:type_name -> common.DataConfig.ODPS
	7,  // 9: common.DataConfig.mongo:type_name -> common.DataConfig.Database
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_common_config_proto_init() }
func file_common_config_proto_init() {
	if File_common_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_common_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GRPCClient); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerConfig_HTTP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerConfig_GRPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerConfig_Trace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerConfig_Alarm); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConfig_Database); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConfig_Redis); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConfig_Elastic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataConfig_ODPS); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_config_proto_goTypes,
		DependencyIndexes: file_common_config_proto_depIdxs,
		MessageInfos:      file_common_config_proto_msgTypes,
	}.Build()
	File_common_config_proto = out.File
	file_common_config_proto_rawDesc = nil
	file_common_config_proto_goTypes = nil
	file_common_config_proto_depIdxs = nil
}
