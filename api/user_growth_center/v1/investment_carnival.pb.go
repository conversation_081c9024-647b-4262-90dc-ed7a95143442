// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: user_growth_center/v1/investment_carnival.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetActivityMainRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetActivityMainRequest) Reset() {
	*x = GetActivityMainRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityMainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityMainRequest) ProtoMessage() {}

func (x *GetActivityMainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityMainRequest.ProtoReflect.Descriptor instead.
func (*GetActivityMainRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{0}
}

func (x *GetActivityMainRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetActivityMainReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Banner            string            `protobuf:"bytes,1,opt,name=banner,json=banner,proto3" json:"banner"`
	ActivityStatus    int64             `protobuf:"varint,2,opt,name=activityStatus,json=activityStatus,proto3" json:"activityStatus"`
	ActivityTimeStamp int64             `protobuf:"varint,3,opt,name=activityTimeStamp,json=activityTimeStamp,proto3" json:"activityTimeStamp"`
	PopularTraderCol  []*TraderBaseInfo `protobuf:"bytes,4,rep,name=popularTraderCol,json=popularTraderCol,proto3" json:"popularTraderCol"`
	BestTraderCol     []*CategoryTrader `protobuf:"bytes,5,rep,name=bestTraderCol,json=bestTraderCol,proto3" json:"bestTraderCol"`
	RecommenderCol    []*Recommender    `protobuf:"bytes,6,rep,name=recommenderCol,json=recommenderCol,proto3" json:"recommenderCol"`
}

func (x *GetActivityMainReply) Reset() {
	*x = GetActivityMainReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityMainReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityMainReply) ProtoMessage() {}

func (x *GetActivityMainReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityMainReply.ProtoReflect.Descriptor instead.
func (*GetActivityMainReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{1}
}

func (x *GetActivityMainReply) GetBanner() string {
	if x != nil {
		return x.Banner
	}
	return ""
}

func (x *GetActivityMainReply) GetActivityStatus() int64 {
	if x != nil {
		return x.ActivityStatus
	}
	return 0
}

func (x *GetActivityMainReply) GetActivityTimeStamp() int64 {
	if x != nil {
		return x.ActivityTimeStamp
	}
	return 0
}

func (x *GetActivityMainReply) GetPopularTraderCol() []*TraderBaseInfo {
	if x != nil {
		return x.PopularTraderCol
	}
	return nil
}

func (x *GetActivityMainReply) GetBestTraderCol() []*CategoryTrader {
	if x != nil {
		return x.BestTraderCol
	}
	return nil
}

func (x *GetActivityMainReply) GetRecommenderCol() []*Recommender {
	if x != nil {
		return x.RecommenderCol
	}
	return nil
}

type CategoryTrader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category  string            `protobuf:"bytes,1,opt,name=category,json=category,proto3" json:"category"`
	TraderCol []*TraderBaseInfo `protobuf:"bytes,2,rep,name=traderCol,json=traderCol,proto3" json:"traderCol"`
}

func (x *CategoryTrader) Reset() {
	*x = CategoryTrader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryTrader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryTrader) ProtoMessage() {}

func (x *CategoryTrader) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryTrader.ProtoReflect.Descriptor instead.
func (*CategoryTrader) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{2}
}

func (x *CategoryTrader) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *CategoryTrader) GetTraderCol() []*TraderBaseInfo {
	if x != nil {
		return x.TraderCol
	}
	return nil
}

type TraderBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode  string       `protobuf:"bytes,1,opt,name=traderCode,json=traderCode,proto3" json:"traderCode"`
	Logo        string       `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
	Ico         string       `protobuf:"bytes,3,opt,name=ico,json=ico,proto3" json:"ico"`
	TraderName  string       `protobuf:"bytes,4,opt,name=traderName,json=traderName,proto3" json:"traderName"`
	WelfareCol  []string     `protobuf:"bytes,5,rep,name=welfareCol,json=welfareCol,proto3" json:"welfareCol"`
	AdFlag      bool         `protobuf:"varint,6,opt,name=adFlag,json=adFlag,proto3" json:"adFlag"`
	Annotation  string       `protobuf:"bytes,7,opt,name=annotation,json=annotation,proto3" json:"annotation"`
	Participant int64        `protobuf:"varint,8,opt,name=participant,json=participant,proto3" json:"participant"`
	Score       string       `protobuf:"bytes,9,opt,name=score,json=score,proto3" json:"score"`
	CountryFlag string       `protobuf:"bytes,10,opt,name=countryFlag,json=countryFlag,proto3" json:"countryFlag"`
	LabelCol    []*LabelItem `protobuf:"bytes,11,rep,name=labelCol,json=labelCol,proto3" json:"labelCol"`
	VrFlag      bool         `protobuf:"varint,12,opt,name=vrFlag,json=vrFlag,proto3" json:"vrFlag"`
	Color       string       `protobuf:"bytes,13,opt,name=color,json=color,proto3" json:"color"`
}

func (x *TraderBaseInfo) Reset() {
	*x = TraderBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderBaseInfo) ProtoMessage() {}

func (x *TraderBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderBaseInfo.ProtoReflect.Descriptor instead.
func (*TraderBaseInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{3}
}

func (x *TraderBaseInfo) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *TraderBaseInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *TraderBaseInfo) GetIco() string {
	if x != nil {
		return x.Ico
	}
	return ""
}

func (x *TraderBaseInfo) GetTraderName() string {
	if x != nil {
		return x.TraderName
	}
	return ""
}

func (x *TraderBaseInfo) GetWelfareCol() []string {
	if x != nil {
		return x.WelfareCol
	}
	return nil
}

func (x *TraderBaseInfo) GetAdFlag() bool {
	if x != nil {
		return x.AdFlag
	}
	return false
}

func (x *TraderBaseInfo) GetAnnotation() string {
	if x != nil {
		return x.Annotation
	}
	return ""
}

func (x *TraderBaseInfo) GetParticipant() int64 {
	if x != nil {
		return x.Participant
	}
	return 0
}

func (x *TraderBaseInfo) GetScore() string {
	if x != nil {
		return x.Score
	}
	return ""
}

func (x *TraderBaseInfo) GetCountryFlag() string {
	if x != nil {
		return x.CountryFlag
	}
	return ""
}

func (x *TraderBaseInfo) GetLabelCol() []*LabelItem {
	if x != nil {
		return x.LabelCol
	}
	return nil
}

func (x *TraderBaseInfo) GetVrFlag() bool {
	if x != nil {
		return x.VrFlag
	}
	return false
}

func (x *TraderBaseInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type Recommender struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatar            string   `protobuf:"bytes,1,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	RecommenderName   string   `protobuf:"bytes,2,opt,name=recommenderName,json=recommenderName,proto3" json:"recommenderName"`
	RecommenderTagCol []string `protobuf:"bytes,3,rep,name=recommenderTagCol,json=recommenderTagCol,proto3" json:"recommenderTagCol"`
	LiveTitle         string   `protobuf:"bytes,4,opt,name=liveTitle,json=liveTitle,proto3" json:"liveTitle"`
	LiveTimeStamp     int64    `protobuf:"varint,5,opt,name=liveTimeStamp,json=liveTimeStamp,proto3" json:"liveTimeStamp"`
	LiveStatus        int32    `protobuf:"varint,6,opt,name=liveStatus,json=liveStatus,proto3" json:"liveStatus"`
	ReviewUrl         string   `protobuf:"bytes,7,opt,name=reviewUrl,json=reviewUrl,proto3" json:"reviewUrl"`
	TraderName        string   `protobuf:"bytes,8,opt,name=traderName,json=traderName,proto3" json:"traderName"`
	LiveEndTimeStamp  string   `protobuf:"bytes,9,opt,name=liveEndTimeStamp,json=liveEndTimeStamp,proto3" json:"liveEndTimeStamp"`
}

func (x *Recommender) Reset() {
	*x = Recommender{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Recommender) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Recommender) ProtoMessage() {}

func (x *Recommender) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Recommender.ProtoReflect.Descriptor instead.
func (*Recommender) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{4}
}

func (x *Recommender) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Recommender) GetRecommenderName() string {
	if x != nil {
		return x.RecommenderName
	}
	return ""
}

func (x *Recommender) GetRecommenderTagCol() []string {
	if x != nil {
		return x.RecommenderTagCol
	}
	return nil
}

func (x *Recommender) GetLiveTitle() string {
	if x != nil {
		return x.LiveTitle
	}
	return ""
}

func (x *Recommender) GetLiveTimeStamp() int64 {
	if x != nil {
		return x.LiveTimeStamp
	}
	return 0
}

func (x *Recommender) GetLiveStatus() int32 {
	if x != nil {
		return x.LiveStatus
	}
	return 0
}

func (x *Recommender) GetReviewUrl() string {
	if x != nil {
		return x.ReviewUrl
	}
	return ""
}

func (x *Recommender) GetTraderName() string {
	if x != nil {
		return x.TraderName
	}
	return ""
}

func (x *Recommender) GetLiveEndTimeStamp() string {
	if x != nil {
		return x.LiveEndTimeStamp
	}
	return ""
}

type GetContentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type int32 `protobuf:"varint,1,opt,name=type,json=type,proto3" json:"type"`
}

func (x *GetContentRequest) Reset() {
	*x = GetContentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContentRequest) ProtoMessage() {}

func (x *GetContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContentRequest.ProtoReflect.Descriptor instead.
func (*GetContentRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{5}
}

func (x *GetContentRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type GetContentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"`
}

func (x *GetContentReply) Reset() {
	*x = GetContentReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetContentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetContentReply) ProtoMessage() {}

func (x *GetContentReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetContentReply.ProtoReflect.Descriptor instead.
func (*GetContentReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{6}
}

func (x *GetContentReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type GetRecommendTraderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number int32 `protobuf:"varint,1,opt,name=number,json=number,proto3" json:"number"`
}

func (x *GetRecommendTraderRequest) Reset() {
	*x = GetRecommendTraderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendTraderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendTraderRequest) ProtoMessage() {}

func (x *GetRecommendTraderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendTraderRequest.ProtoReflect.Descriptor instead.
func (*GetRecommendTraderRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{7}
}

func (x *GetRecommendTraderRequest) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

type GetRecommendTraderReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCol []*TraderBaseInfo `protobuf:"bytes,1,rep,name=traderCol,json=traderCol,proto3" json:"traderCol"`
}

func (x *GetRecommendTraderReply) Reset() {
	*x = GetRecommendTraderReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommendTraderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommendTraderReply) ProtoMessage() {}

func (x *GetRecommendTraderReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommendTraderReply.ProtoReflect.Descriptor instead.
func (*GetRecommendTraderReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{8}
}

func (x *GetRecommendTraderReply) GetTraderCol() []*TraderBaseInfo {
	if x != nil {
		return x.TraderCol
	}
	return nil
}

type GetGlobalTraderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetGlobalTraderRequest) Reset() {
	*x = GetGlobalTraderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGlobalTraderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGlobalTraderRequest) ProtoMessage() {}

func (x *GetGlobalTraderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGlobalTraderRequest.ProtoReflect.Descriptor instead.
func (*GetGlobalTraderRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{9}
}

type GetGlobalTraderReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCol []*CategoryTrader `protobuf:"bytes,1,rep,name=traderCol,json=traderCol,proto3" json:"traderCol"`
}

func (x *GetGlobalTraderReply) Reset() {
	*x = GetGlobalTraderReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGlobalTraderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGlobalTraderReply) ProtoMessage() {}

func (x *GetGlobalTraderReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGlobalTraderReply.ProtoReflect.Descriptor instead.
func (*GetGlobalTraderReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{10}
}

func (x *GetGlobalTraderReply) GetTraderCol() []*CategoryTrader {
	if x != nil {
		return x.TraderCol
	}
	return nil
}

type GetTraderActivityPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode string `protobuf:"bytes,1,opt,name=traderCode,json=traderCode,proto3" json:"traderCode"`
}

func (x *GetTraderActivityPageRequest) Reset() {
	*x = GetTraderActivityPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTraderActivityPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTraderActivityPageRequest) ProtoMessage() {}

func (x *GetTraderActivityPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTraderActivityPageRequest.ProtoReflect.Descriptor instead.
func (*GetTraderActivityPageRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{11}
}

func (x *GetTraderActivityPageRequest) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

type GetTraderActivityPageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode        string               `protobuf:"bytes,1,opt,name=traderCode,json=traderCode,proto3" json:"traderCode"`
	CooperationFlag   bool                 `protobuf:"varint,2,opt,name=cooperationFlag,json=cooperationFlag,proto3" json:"cooperationFlag"`
	AdvertisementCol  []*AdvertisementInfo `protobuf:"bytes,3,rep,name=advertisementCol,json=advertisementCol,proto3" json:"advertisementCol"`
	TraderData        *TraderBaseInfo      `protobuf:"bytes,4,opt,name=traderData,json=traderData,proto3" json:"traderData"`
	ContactData       *TraderContactInfo   `protobuf:"bytes,5,opt,name=contactData,json=contactData,proto3" json:"contactData"`
	EventDiscountData *TraderEventDiscount `protobuf:"bytes,6,opt,name=eventDiscountData,json=eventDiscountData,proto3" json:"eventDiscountData"`
	BrandDisplayData  *TraderBrandDisplay  `protobuf:"bytes,7,opt,name=brandDisplayData,json=brandDisplayData,proto3" json:"brandDisplayData"`
	TraderProfile     string               `protobuf:"bytes,8,opt,name=traderProfile,json=traderProfile,proto3" json:"traderProfile"`
	LiveFlag          bool                 `protobuf:"varint,9,opt,name=liveFlag,json=liveFlag,proto3" json:"liveFlag"`
	LiveUrl           string               `protobuf:"bytes,10,opt,name=liveUrl,json=liveUrl,proto3" json:"liveUrl"`
	OpenAccountUrl    string               `protobuf:"bytes,11,opt,name=openAccountUrl,json=openAccountUrl,proto3" json:"openAccountUrl"`
}

func (x *GetTraderActivityPageReply) Reset() {
	*x = GetTraderActivityPageReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTraderActivityPageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTraderActivityPageReply) ProtoMessage() {}

func (x *GetTraderActivityPageReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTraderActivityPageReply.ProtoReflect.Descriptor instead.
func (*GetTraderActivityPageReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{12}
}

func (x *GetTraderActivityPageReply) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *GetTraderActivityPageReply) GetCooperationFlag() bool {
	if x != nil {
		return x.CooperationFlag
	}
	return false
}

func (x *GetTraderActivityPageReply) GetAdvertisementCol() []*AdvertisementInfo {
	if x != nil {
		return x.AdvertisementCol
	}
	return nil
}

func (x *GetTraderActivityPageReply) GetTraderData() *TraderBaseInfo {
	if x != nil {
		return x.TraderData
	}
	return nil
}

func (x *GetTraderActivityPageReply) GetContactData() *TraderContactInfo {
	if x != nil {
		return x.ContactData
	}
	return nil
}

func (x *GetTraderActivityPageReply) GetEventDiscountData() *TraderEventDiscount {
	if x != nil {
		return x.EventDiscountData
	}
	return nil
}

func (x *GetTraderActivityPageReply) GetBrandDisplayData() *TraderBrandDisplay {
	if x != nil {
		return x.BrandDisplayData
	}
	return nil
}

func (x *GetTraderActivityPageReply) GetTraderProfile() string {
	if x != nil {
		return x.TraderProfile
	}
	return ""
}

func (x *GetTraderActivityPageReply) GetLiveFlag() bool {
	if x != nil {
		return x.LiveFlag
	}
	return false
}

func (x *GetTraderActivityPageReply) GetLiveUrl() string {
	if x != nil {
		return x.LiveUrl
	}
	return ""
}

func (x *GetTraderActivityPageReply) GetOpenAccountUrl() string {
	if x != nil {
		return x.OpenAccountUrl
	}
	return ""
}

type TraderContactInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Site  []string `protobuf:"bytes,1,rep,name=site,json=site,proto3" json:"site"`
	Phone string   `protobuf:"bytes,2,opt,name=phone,json=phone,proto3" json:"phone"`
	Email string   `protobuf:"bytes,3,opt,name=email,json=email,proto3" json:"email"`
}

func (x *TraderContactInfo) Reset() {
	*x = TraderContactInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderContactInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderContactInfo) ProtoMessage() {}

func (x *TraderContactInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderContactInfo.ProtoReflect.Descriptor instead.
func (*TraderContactInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{13}
}

func (x *TraderContactInfo) GetSite() []string {
	if x != nil {
		return x.Site
	}
	return nil
}

func (x *TraderContactInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *TraderContactInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type AdvertisementInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl    string `protobuf:"bytes,1,opt,name=imageUrl,json=imageUrl,proto3" json:"imageUrl"`
	JumpAddress string `protobuf:"bytes,2,opt,name=jumpAddress,json=jumpAddress,proto3" json:"jumpAddress"`
}

func (x *AdvertisementInfo) Reset() {
	*x = AdvertisementInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdvertisementInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvertisementInfo) ProtoMessage() {}

func (x *AdvertisementInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvertisementInfo.ProtoReflect.Descriptor instead.
func (*AdvertisementInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{14}
}

func (x *AdvertisementInfo) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *AdvertisementInfo) GetJumpAddress() string {
	if x != nil {
		return x.JumpAddress
	}
	return ""
}

type TraderEventDiscount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl []string `protobuf:"bytes,1,rep,name=imageUrl,json=imageUrl,proto3" json:"imageUrl"`
}

func (x *TraderEventDiscount) Reset() {
	*x = TraderEventDiscount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderEventDiscount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderEventDiscount) ProtoMessage() {}

func (x *TraderEventDiscount) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderEventDiscount.ProtoReflect.Descriptor instead.
func (*TraderEventDiscount) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{15}
}

func (x *TraderEventDiscount) GetImageUrl() []string {
	if x != nil {
		return x.ImageUrl
	}
	return nil
}

type TraderBrandDisplay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content        string   `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"`
	ImageCol       []string `protobuf:"bytes,2,rep,name=imageCol,json=imageCol,proto3" json:"imageCol"`
	DescriptionImg string   `protobuf:"bytes,3,opt,name=descriptionImg,json=descriptionImg,proto3" json:"descriptionImg"`
}

func (x *TraderBrandDisplay) Reset() {
	*x = TraderBrandDisplay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderBrandDisplay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderBrandDisplay) ProtoMessage() {}

func (x *TraderBrandDisplay) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderBrandDisplay.ProtoReflect.Descriptor instead.
func (*TraderBrandDisplay) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{16}
}

func (x *TraderBrandDisplay) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *TraderBrandDisplay) GetImageCol() []string {
	if x != nil {
		return x.ImageCol
	}
	return nil
}

func (x *TraderBrandDisplay) GetDescriptionImg() string {
	if x != nil {
		return x.DescriptionImg
	}
	return ""
}

type GetRecommenderListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number int32 `protobuf:"varint,1,opt,name=number,json=number,proto3" json:"number"`
}

func (x *GetRecommenderListRequest) Reset() {
	*x = GetRecommenderListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommenderListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommenderListRequest) ProtoMessage() {}

func (x *GetRecommenderListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommenderListRequest.ProtoReflect.Descriptor instead.
func (*GetRecommenderListRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{17}
}

func (x *GetRecommenderListRequest) GetNumber() int32 {
	if x != nil {
		return x.Number
	}
	return 0
}

type GetRecommenderListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecommenderCol []*Recommender `protobuf:"bytes,1,rep,name=recommenderCol,json=recommenderCol,proto3" json:"recommenderCol"`
}

func (x *GetRecommenderListReply) Reset() {
	*x = GetRecommenderListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRecommenderListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRecommenderListReply) ProtoMessage() {}

func (x *GetRecommenderListReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRecommenderListReply.ProtoReflect.Descriptor instead.
func (*GetRecommenderListReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{18}
}

func (x *GetRecommenderListReply) GetRecommenderCol() []*Recommender {
	if x != nil {
		return x.RecommenderCol
	}
	return nil
}

type GetRewordPoolDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetRewordPoolDetailRequest) Reset() {
	*x = GetRewordPoolDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewordPoolDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewordPoolDetailRequest) ProtoMessage() {}

func (x *GetRewordPoolDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewordPoolDetailRequest.ProtoReflect.Descriptor instead.
func (*GetRewordPoolDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{19}
}

func (x *GetRewordPoolDetailRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetRewordPoolDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Banner           string                   `protobuf:"bytes,1,opt,name=banner,json=banner,proto3" json:"banner"`
	AdvertisementCol []*AdvertisementInfo     `protobuf:"bytes,2,rep,name=advertisementCol,json=advertisementCol,proto3" json:"advertisementCol"`
	CheckInCol       []*CheckInInfo           `protobuf:"bytes,3,rep,name=checkInCol,json=checkInCol,proto3" json:"checkInCol"`
	RewordPoolCol    []*RewordPoolSummaryInfo `protobuf:"bytes,4,rep,name=rewordPoolCol,json=rewordPoolCol,proto3" json:"rewordPoolCol"`
}

func (x *GetRewordPoolDetailReply) Reset() {
	*x = GetRewordPoolDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewordPoolDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewordPoolDetailReply) ProtoMessage() {}

func (x *GetRewordPoolDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewordPoolDetailReply.ProtoReflect.Descriptor instead.
func (*GetRewordPoolDetailReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{20}
}

func (x *GetRewordPoolDetailReply) GetBanner() string {
	if x != nil {
		return x.Banner
	}
	return ""
}

func (x *GetRewordPoolDetailReply) GetAdvertisementCol() []*AdvertisementInfo {
	if x != nil {
		return x.AdvertisementCol
	}
	return nil
}

func (x *GetRewordPoolDetailReply) GetCheckInCol() []*CheckInInfo {
	if x != nil {
		return x.CheckInCol
	}
	return nil
}

func (x *GetRewordPoolDetailReply) GetRewordPoolCol() []*RewordPoolSummaryInfo {
	if x != nil {
		return x.RewordPoolCol
	}
	return nil
}

type CheckInInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckInTitle  string `protobuf:"bytes,1,opt,name=checkInTitle,json=checkInTitle,proto3" json:"checkInTitle"`
	CheckInStatus int32  `protobuf:"varint,2,opt,name=checkInStatus,json=checkInStatus,proto3" json:"checkInStatus"`
}

func (x *CheckInInfo) Reset() {
	*x = CheckInInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckInInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckInInfo) ProtoMessage() {}

func (x *CheckInInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckInInfo.ProtoReflect.Descriptor instead.
func (*CheckInInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{21}
}

func (x *CheckInInfo) GetCheckInTitle() string {
	if x != nil {
		return x.CheckInTitle
	}
	return ""
}

func (x *CheckInInfo) GetCheckInStatus() int32 {
	if x != nil {
		return x.CheckInStatus
	}
	return 0
}

type RewordPoolSummaryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PoolTitle   string `protobuf:"bytes,1,opt,name=poolTitle,json=poolTitle,proto3" json:"poolTitle"`
	PoolContent string `protobuf:"bytes,2,opt,name=poolContent,json=poolContent,proto3" json:"poolContent"`
	Participant int64  `protobuf:"varint,3,opt,name=participant,json=participant,proto3" json:"participant"`
	Finished    int64  `protobuf:"varint,4,opt,name=finished,json=finished,proto3" json:"finished"`
	Progress    int32  `protobuf:"varint,5,opt,name=progress,json=progress,proto3" json:"progress"`
	Total       int32  `protobuf:"varint,6,opt,name=total,json=total,proto3" json:"total"`
}

func (x *RewordPoolSummaryInfo) Reset() {
	*x = RewordPoolSummaryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewordPoolSummaryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewordPoolSummaryInfo) ProtoMessage() {}

func (x *RewordPoolSummaryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewordPoolSummaryInfo.ProtoReflect.Descriptor instead.
func (*RewordPoolSummaryInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{22}
}

func (x *RewordPoolSummaryInfo) GetPoolTitle() string {
	if x != nil {
		return x.PoolTitle
	}
	return ""
}

func (x *RewordPoolSummaryInfo) GetPoolContent() string {
	if x != nil {
		return x.PoolContent
	}
	return ""
}

func (x *RewordPoolSummaryInfo) GetParticipant() int64 {
	if x != nil {
		return x.Participant
	}
	return 0
}

func (x *RewordPoolSummaryInfo) GetFinished() int64 {
	if x != nil {
		return x.Finished
	}
	return 0
}

func (x *RewordPoolSummaryInfo) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *RewordPoolSummaryInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

type RewordPoolInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PoolTitle string        `protobuf:"bytes,1,opt,name=poolTitle,json=poolTitle,proto3" json:"poolTitle"`
	TaskCol   []*TaskDetail `protobuf:"bytes,2,rep,name=taskCol,json=taskCol,proto3" json:"taskCol"`
}

func (x *RewordPoolInfo) Reset() {
	*x = RewordPoolInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewordPoolInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewordPoolInfo) ProtoMessage() {}

func (x *RewordPoolInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewordPoolInfo.ProtoReflect.Descriptor instead.
func (*RewordPoolInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{23}
}

func (x *RewordPoolInfo) GetPoolTitle() string {
	if x != nil {
		return x.PoolTitle
	}
	return ""
}

func (x *RewordPoolInfo) GetTaskCol() []*TaskDetail {
	if x != nil {
		return x.TaskCol
	}
	return nil
}

type TaskDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskTitle   string `protobuf:"bytes,1,opt,name=taskTitle,json=taskTitle,proto3" json:"taskTitle"`
	TaskContent string `protobuf:"bytes,2,opt,name=taskContent,json=taskContent,proto3" json:"taskContent"`
	TaskIco     string `protobuf:"bytes,3,opt,name=taskIco,json=taskIco,proto3" json:"taskIco"`
	FinishFlag  bool   `protobuf:"varint,4,opt,name=finishFlag,json=finishFlag,proto3" json:"finishFlag"`
	ActionName  string `protobuf:"bytes,5,opt,name=actionName,json=actionName,proto3" json:"actionName"`
}

func (x *TaskDetail) Reset() {
	*x = TaskDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskDetail) ProtoMessage() {}

func (x *TaskDetail) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskDetail.ProtoReflect.Descriptor instead.
func (*TaskDetail) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{24}
}

func (x *TaskDetail) GetTaskTitle() string {
	if x != nil {
		return x.TaskTitle
	}
	return ""
}

func (x *TaskDetail) GetTaskContent() string {
	if x != nil {
		return x.TaskContent
	}
	return ""
}

func (x *TaskDetail) GetTaskIco() string {
	if x != nil {
		return x.TaskIco
	}
	return ""
}

func (x *TaskDetail) GetFinishFlag() bool {
	if x != nil {
		return x.FinishFlag
	}
	return false
}

func (x *TaskDetail) GetActionName() string {
	if x != nil {
		return x.ActionName
	}
	return ""
}

type UserCheckInRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *UserCheckInRequest) Reset() {
	*x = UserCheckInRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCheckInRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCheckInRequest) ProtoMessage() {}

func (x *UserCheckInRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCheckInRequest.ProtoReflect.Descriptor instead.
func (*UserCheckInRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{25}
}

func (x *UserCheckInRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UserCheckInReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool `protobuf:"varint,1,opt,name=success,json=success,proto3" json:"success"`
}

func (x *UserCheckInReply) Reset() {
	*x = UserCheckInReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCheckInReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCheckInReply) ProtoMessage() {}

func (x *UserCheckInReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCheckInReply.ProtoReflect.Descriptor instead.
func (*UserCheckInReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{26}
}

func (x *UserCheckInReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetRewordDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetRewordDetailRequest) Reset() {
	*x = GetRewordDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewordDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewordDetailRequest) ProtoMessage() {}

func (x *GetRewordDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewordDetailRequest.ProtoReflect.Descriptor instead.
func (*GetRewordDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{27}
}

func (x *GetRewordDetailRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetRewordDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reword              float32                  `protobuf:"fixed32,1,opt,name=reword,json=reword,proto3" json:"reword"`
	PoolSummaryCol      []*SharePrizePoolSummary `protobuf:"bytes,2,rep,name=poolSummaryCol,json=poolSummaryCol,proto3" json:"poolSummaryCol"`
	DepositCashBackData *DepositCashBack         `protobuf:"bytes,3,opt,name=depositCashBackData,json=depositCashBackData,proto3" json:"depositCashBackData"`
	LuckyDrawCol        []*GrandLuckyDraw        `protobuf:"bytes,4,rep,name=luckyDrawCol,json=luckyDrawCol,proto3" json:"luckyDrawCol"`
}

func (x *GetRewordDetailReply) Reset() {
	*x = GetRewordDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewordDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewordDetailReply) ProtoMessage() {}

func (x *GetRewordDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewordDetailReply.ProtoReflect.Descriptor instead.
func (*GetRewordDetailReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{28}
}

func (x *GetRewordDetailReply) GetReword() float32 {
	if x != nil {
		return x.Reword
	}
	return 0
}

func (x *GetRewordDetailReply) GetPoolSummaryCol() []*SharePrizePoolSummary {
	if x != nil {
		return x.PoolSummaryCol
	}
	return nil
}

func (x *GetRewordDetailReply) GetDepositCashBackData() *DepositCashBack {
	if x != nil {
		return x.DepositCashBackData
	}
	return nil
}

func (x *GetRewordDetailReply) GetLuckyDrawCol() []*GrandLuckyDraw {
	if x != nil {
		return x.LuckyDrawCol
	}
	return nil
}

type SharePrizePoolSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PoolTitle    string `protobuf:"bytes,1,opt,name=poolTitle,json=poolTitle,proto3" json:"poolTitle"`
	PoolSchedule string `protobuf:"bytes,2,opt,name=poolSchedule,json=poolSchedule,proto3" json:"poolSchedule"`
}

func (x *SharePrizePoolSummary) Reset() {
	*x = SharePrizePoolSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharePrizePoolSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharePrizePoolSummary) ProtoMessage() {}

func (x *SharePrizePoolSummary) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharePrizePoolSummary.ProtoReflect.Descriptor instead.
func (*SharePrizePoolSummary) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{29}
}

func (x *SharePrizePoolSummary) GetPoolTitle() string {
	if x != nil {
		return x.PoolTitle
	}
	return ""
}

func (x *SharePrizePoolSummary) GetPoolSchedule() string {
	if x != nil {
		return x.PoolSchedule
	}
	return ""
}

type DepositCashBack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DepositFlag bool    `protobuf:"varint,1,opt,name=depositFlag,json=depositFlag,proto3" json:"depositFlag"`
	CashBack    float32 `protobuf:"fixed32,2,opt,name=cashBack,json=cashBack,proto3" json:"cashBack"`
}

func (x *DepositCashBack) Reset() {
	*x = DepositCashBack{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositCashBack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositCashBack) ProtoMessage() {}

func (x *DepositCashBack) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositCashBack.ProtoReflect.Descriptor instead.
func (*DepositCashBack) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{30}
}

func (x *DepositCashBack) GetDepositFlag() bool {
	if x != nil {
		return x.DepositFlag
	}
	return false
}

func (x *DepositCashBack) GetCashBack() float32 {
	if x != nil {
		return x.CashBack
	}
	return 0
}

type GrandLuckyDraw struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Award           string `protobuf:"bytes,1,opt,name=award,json=award,proto3" json:"award"`
	Type            int32  `protobuf:"varint,2,opt,name=type,json=type,proto3" json:"type"`
	RedemptionCodes string `protobuf:"bytes,3,opt,name=redemptionCodes,json=redemptionCodes,proto3" json:"redemptionCodes"`
}

func (x *GrandLuckyDraw) Reset() {
	*x = GrandLuckyDraw{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrandLuckyDraw) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrandLuckyDraw) ProtoMessage() {}

func (x *GrandLuckyDraw) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrandLuckyDraw.ProtoReflect.Descriptor instead.
func (*GrandLuckyDraw) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{31}
}

func (x *GrandLuckyDraw) GetAward() string {
	if x != nil {
		return x.Award
	}
	return ""
}

func (x *GrandLuckyDraw) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GrandLuckyDraw) GetRedemptionCodes() string {
	if x != nil {
		return x.RedemptionCodes
	}
	return ""
}

type GrandLuckyDrawRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GrandLuckyDrawRequest) Reset() {
	*x = GrandLuckyDrawRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrandLuckyDrawRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrandLuckyDrawRequest) ProtoMessage() {}

func (x *GrandLuckyDrawRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrandLuckyDrawRequest.ProtoReflect.Descriptor instead.
func (*GrandLuckyDrawRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{32}
}

func (x *GrandLuckyDrawRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GrandLuckyDrawReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chances   int32             `protobuf:"varint,1,opt,name=chances,json=chances,proto3" json:"chances"`
	AwardCol  []*AwardInfo      `protobuf:"bytes,2,rep,name=awardCol,json=awardCol,proto3" json:"awardCol"`
	TraderCol []*TraderBaseInfo `protobuf:"bytes,3,rep,name=traderCol,json=traderCol,proto3" json:"traderCol"`
	Record    []*RewordRecords  `protobuf:"bytes,4,rep,name=record,json=record,proto3" json:"record"`
	Task      []*TaskDetail     `protobuf:"bytes,5,rep,name=task,json=task,proto3" json:"task"`
}

func (x *GrandLuckyDrawReply) Reset() {
	*x = GrandLuckyDrawReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrandLuckyDrawReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrandLuckyDrawReply) ProtoMessage() {}

func (x *GrandLuckyDrawReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrandLuckyDrawReply.ProtoReflect.Descriptor instead.
func (*GrandLuckyDrawReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{33}
}

func (x *GrandLuckyDrawReply) GetChances() int32 {
	if x != nil {
		return x.Chances
	}
	return 0
}

func (x *GrandLuckyDrawReply) GetAwardCol() []*AwardInfo {
	if x != nil {
		return x.AwardCol
	}
	return nil
}

func (x *GrandLuckyDrawReply) GetTraderCol() []*TraderBaseInfo {
	if x != nil {
		return x.TraderCol
	}
	return nil
}

func (x *GrandLuckyDrawReply) GetRecord() []*RewordRecords {
	if x != nil {
		return x.Record
	}
	return nil
}

func (x *GrandLuckyDrawReply) GetTask() []*TaskDetail {
	if x != nil {
		return x.Task
	}
	return nil
}

type AwardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Logo string `protobuf:"bytes,2,opt,name=logo,json=logo,proto3" json:"logo"`
}

func (x *AwardInfo) Reset() {
	*x = AwardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AwardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AwardInfo) ProtoMessage() {}

func (x *AwardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AwardInfo.ProtoReflect.Descriptor instead.
func (*AwardInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{34}
}

func (x *AwardInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AwardInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

type RewordRecords struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NickName        string `protobuf:"bytes,1,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	Award           string `protobuf:"bytes,2,opt,name=award,json=award,proto3" json:"award"`
	RewordTimeStamp int64  `protobuf:"varint,3,opt,name=rewordTimeStamp,json=rewordTimeStamp,proto3" json:"rewordTimeStamp"`
}

func (x *RewordRecords) Reset() {
	*x = RewordRecords{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewordRecords) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewordRecords) ProtoMessage() {}

func (x *RewordRecords) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewordRecords.ProtoReflect.Descriptor instead.
func (*RewordRecords) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{35}
}

func (x *RewordRecords) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *RewordRecords) GetAward() string {
	if x != nil {
		return x.Award
	}
	return ""
}

func (x *RewordRecords) GetRewordTimeStamp() int64 {
	if x != nil {
		return x.RewordTimeStamp
	}
	return 0
}

type StartDrawReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessFlag  bool       `protobuf:"varint,1,opt,name=successFlag,json=successFlag,proto3" json:"successFlag"`
	Tips         string     `protobuf:"bytes,2,opt,name=tips,json=tips,proto3" json:"tips"`
	AwardData    *AwardInfo `protobuf:"bytes,3,opt,name=awardData,json=awardData,proto3" json:"awardData"`
	Description  string     `protobuf:"bytes,4,opt,name=description,json=description,proto3" json:"description"`
	ActionName   string     `protobuf:"bytes,5,opt,name=actionName,json=actionName,proto3" json:"actionName"`
	ExchangeDesc string     `protobuf:"bytes,6,opt,name=exchangeDesc,json=exchangeDesc,proto3" json:"exchangeDesc"`
}

func (x *StartDrawReply) Reset() {
	*x = StartDrawReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartDrawReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDrawReply) ProtoMessage() {}

func (x *StartDrawReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDrawReply.ProtoReflect.Descriptor instead.
func (*StartDrawReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{36}
}

func (x *StartDrawReply) GetSuccessFlag() bool {
	if x != nil {
		return x.SuccessFlag
	}
	return false
}

func (x *StartDrawReply) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *StartDrawReply) GetAwardData() *AwardInfo {
	if x != nil {
		return x.AwardData
	}
	return nil
}

func (x *StartDrawReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *StartDrawReply) GetActionName() string {
	if x != nil {
		return x.ActionName
	}
	return ""
}

func (x *StartDrawReply) GetExchangeDesc() string {
	if x != nil {
		return x.ExchangeDesc
	}
	return ""
}

type GetDepositDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetDepositDetailRequest) Reset() {
	*x = GetDepositDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepositDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepositDetailRequest) ProtoMessage() {}

func (x *GetDepositDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepositDetailRequest.ProtoReflect.Descriptor instead.
func (*GetDepositDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{37}
}

type GetDepositDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Participant int64 `protobuf:"varint,1,opt,name=participant,json=participant,proto3" json:"participant"`
}

func (x *GetDepositDetailReply) Reset() {
	*x = GetDepositDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDepositDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDepositDetailReply) ProtoMessage() {}

func (x *GetDepositDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDepositDetailReply.ProtoReflect.Descriptor instead.
func (*GetDepositDetailReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{38}
}

func (x *GetDepositDetailReply) GetParticipant() int64 {
	if x != nil {
		return x.Participant
	}
	return 0
}

type SearchTraderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"`
}

func (x *SearchTraderRequest) Reset() {
	*x = SearchTraderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTraderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTraderRequest) ProtoMessage() {}

func (x *SearchTraderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTraderRequest.ProtoReflect.Descriptor instead.
func (*SearchTraderRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{39}
}

func (x *SearchTraderRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SearchTraderReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCol []*TraderBaseInfo `protobuf:"bytes,1,rep,name=traderCol,json=traderCol,proto3" json:"traderCol"`
}

func (x *SearchTraderReply) Reset() {
	*x = SearchTraderReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTraderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTraderReply) ProtoMessage() {}

func (x *SearchTraderReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTraderReply.ProtoReflect.Descriptor instead.
func (*SearchTraderReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{40}
}

func (x *SearchTraderReply) GetTraderCol() []*TraderBaseInfo {
	if x != nil {
		return x.TraderCol
	}
	return nil
}

type GetActivityShareRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetActivityShareRequest) Reset() {
	*x = GetActivityShareRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityShareRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityShareRequest) ProtoMessage() {}

func (x *GetActivityShareRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityShareRequest.ProtoReflect.Descriptor instead.
func (*GetActivityShareRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{41}
}

type GetActivityShareReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,json=url,proto3" json:"url"`
}

func (x *GetActivityShareReply) Reset() {
	*x = GetActivityShareReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityShareReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityShareReply) ProtoMessage() {}

func (x *GetActivityShareReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityShareReply.ProtoReflect.Descriptor instead.
func (*GetActivityShareReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{42}
}

func (x *GetActivityShareReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetBannerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetBannerRequest) Reset() {
	*x = GetBannerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBannerRequest) ProtoMessage() {}

func (x *GetBannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBannerRequest.ProtoReflect.Descriptor instead.
func (*GetBannerRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{43}
}

type GetBannerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Banner           string               `protobuf:"bytes,1,opt,name=banner,json=banner,proto3" json:"banner"`
	AdvertisementCol []*AdvertisementInfo `protobuf:"bytes,2,rep,name=advertisementCol,json=advertisementCol,proto3" json:"advertisementCol"`
}

func (x *GetBannerReply) Reset() {
	*x = GetBannerReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBannerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBannerReply) ProtoMessage() {}

func (x *GetBannerReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBannerReply.ProtoReflect.Descriptor instead.
func (*GetBannerReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{44}
}

func (x *GetBannerReply) GetBanner() string {
	if x != nil {
		return x.Banner
	}
	return ""
}

func (x *GetBannerReply) GetAdvertisementCol() []*AdvertisementInfo {
	if x != nil {
		return x.AdvertisementCol
	}
	return nil
}

type AssistRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	AssistedUserId string `protobuf:"bytes,2,opt,name=assistedUserId,json=assistedUserId,proto3" json:"assistedUserId"`
}

func (x *AssistRequest) Reset() {
	*x = AssistRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssistRequest) ProtoMessage() {}

func (x *AssistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssistRequest.ProtoReflect.Descriptor instead.
func (*AssistRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{45}
}

func (x *AssistRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AssistRequest) GetAssistedUserId() string {
	if x != nil {
		return x.AssistedUserId
	}
	return ""
}

type AssistReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AssistReply) Reset() {
	*x = AssistReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssistReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssistReply) ProtoMessage() {}

func (x *AssistReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssistReply.ProtoReflect.Descriptor instead.
func (*AssistReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{46}
}

type WatchLiveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *WatchLiveRequest) Reset() {
	*x = WatchLiveRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchLiveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchLiveRequest) ProtoMessage() {}

func (x *WatchLiveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchLiveRequest.ProtoReflect.Descriptor instead.
func (*WatchLiveRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{47}
}

func (x *WatchLiveRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type WatchLiveReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WatchLiveReply) Reset() {
	*x = WatchLiveReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WatchLiveReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchLiveReply) ProtoMessage() {}

func (x *WatchLiveReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchLiveReply.ProtoReflect.Descriptor instead.
func (*WatchLiveReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{48}
}

type FriendAssistanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *FriendAssistanceRequest) Reset() {
	*x = FriendAssistanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FriendAssistanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendAssistanceRequest) ProtoMessage() {}

func (x *FriendAssistanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendAssistanceRequest.ProtoReflect.Descriptor instead.
func (*FriendAssistanceRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{49}
}

func (x *FriendAssistanceRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type FriendAssistanceReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Progress    int32    `protobuf:"varint,1,opt,name=progress,json=progress,proto3" json:"progress"`
	Total       int32    `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
	Invitations []string `protobuf:"bytes,3,rep,name=invitations,json=invitations,proto3" json:"invitations"`
}

func (x *FriendAssistanceReply) Reset() {
	*x = FriendAssistanceReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FriendAssistanceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendAssistanceReply) ProtoMessage() {}

func (x *FriendAssistanceReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendAssistanceReply.ProtoReflect.Descriptor instead.
func (*FriendAssistanceReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{50}
}

func (x *FriendAssistanceReply) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *FriendAssistanceReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *FriendAssistanceReply) GetInvitations() []string {
	if x != nil {
		return x.Invitations
	}
	return nil
}

type LabelItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      int64  `protobuf:"varint,1,opt,name=type,json=type,proto3" json:"type"`
	LabelName string `protobuf:"bytes,2,opt,name=labelName,json=labelName,proto3" json:"labelName"`
}

func (x *LabelItem) Reset() {
	*x = LabelItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelItem) ProtoMessage() {}

func (x *LabelItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_investment_carnival_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelItem.ProtoReflect.Descriptor instead.
func (*LabelItem) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP(), []int{51}
}

func (x *LabelItem) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *LabelItem) GetLabelName() string {
	if x != nil {
		return x.LabelName
	}
	return ""
}

var File_user_growth_center_v1_investment_carnival_proto protoreflect.FileDescriptor

var file_user_growth_center_v1_investment_carnival_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x6e, 0x69, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x19, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77,
	0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x48, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x69, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0x49, 0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xa1, 0x04, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x69, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x26, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0xe5, 0x9b, 0xbe, 0x52,
	0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x35, 0x92, 0x41, 0x32, 0x2a, 0x2d, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x20, 0x30, 0x3a, 0xe6, 0x9c, 0xaa, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x20, 0x31,
	0x3a, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x20, 0x32, 0x3a, 0xe5, 0xb7, 0xb2, 0xe7, 0xbb, 0x93,
	0xe6, 0x9d, 0x9f, 0x3a, 0x01, 0x30, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x12, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe5, 0xbc,
	0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x3a, 0x01, 0x30, 0x52, 0x11, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70,
	0x12, 0x6b, 0x0a, 0x10, 0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x72, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x8e, 0xa8, 0xe8,
	0x8d, 0x90, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x52, 0x10, 0x70, 0x6f, 0x70,
	0x75, 0x6c, 0x61, 0x72, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x12, 0x65, 0x0a,
	0x0d, 0x62, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0x52, 0x0d, 0x62, 0x65, 0x73, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6c, 0x12, 0x64, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0x8e, 0x8b, 0xe7, 0x89,
	0x8c, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe5, 0xae, 0x98, 0x52, 0x0e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x22, 0x98, 0x01, 0x0a, 0x0e, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x12, 0x27, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xb1, 0xbb, 0xe5, 0x88, 0xab, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x5d, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x43, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98,
	0x93, 0xe5, 0x95, 0x86, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x6c, 0x22, 0xad, 0x05, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41,
	0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x04,
	0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a,
	0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x6c, 0x6f, 0x67, 0x6f, 0x52, 0x04,
	0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x23, 0x0a, 0x03, 0x69, 0x63, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95,
	0x86, 0x69, 0x63, 0x6f, 0x52, 0x03, 0x69, 0x63, 0x6f, 0x12, 0x34, 0x0a, 0x0a, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe7, 0xae, 0x80,
	0xe7, 0xa7, 0xb0, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2b, 0x0a, 0x0a, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xa6, 0x8f, 0xe5, 0x88, 0xa9,
	0x52, 0x0a, 0x77, 0x65, 0x6c, 0x66, 0x61, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x12, 0x30, 0x0a, 0x06,
	0x61, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x18, 0x92, 0x41,
	0x15, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x3a,
	0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x61, 0x64, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x34,
	0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5,
	0x95, 0x86, 0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0x52, 0x0a, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x8f, 0x82, 0xe4, 0xb8, 0x8e, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0b, 0x70, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0xe8, 0xaf, 0x84, 0xe5, 0x88, 0x86, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xb3,
	0xa8, 0xe5, 0x86, 0x8c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x9b, 0xbd, 0xe6, 0x97, 0x97,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x5c, 0x0a,
	0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6c, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x52, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x06, 0x76,
	0x72, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x0b, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0x56, 0x52, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x06, 0x76, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x27, 0x0a, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xbe, 0x04, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xa4, 0xb4, 0xe5,
	0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x35, 0x0a, 0x0f, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xa7, 0x93, 0xe5, 0x90, 0x8d,
	0x52, 0x0f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x54, 0x0a, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x43, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x26, 0x92, 0x41,
	0x23, 0x2a, 0x21, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x2c, 0xe4, 0xbe, 0x8b, 0xe5, 0xa6, 0x82,
	0x3a, 0xe4, 0xbb, 0x8e, 0xe4, 0xb8, 0x9a, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x2c, 0xe8, 0x81,
	0x8c, 0xe4, 0xbd, 0x8d, 0x52, 0x11, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x54, 0x61, 0x67, 0x43, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x09, 0x6c, 0x69, 0x76, 0x65, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x09, 0x6c,
	0x69, 0x76, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x6c, 0x69, 0x76, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5, 0xbc, 0x80, 0xe5,
	0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0d, 0x6c, 0x69, 0x76, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x5b, 0x0a, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3b, 0x92, 0x41, 0x38,
	0x2a, 0x33, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x2c, 0x30,
	0x3a, 0xe6, 0x9c, 0xaa, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0x20, 0x31, 0x20, 0xe7, 0x9b, 0xb4,
	0xe6, 0x92, 0xad, 0xe4, 0xb8, 0xad, 0x20, 0x32, 0x20, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe7,
	0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0x3a, 0x01, 0x30, 0x52, 0x0a, 0x6c, 0x69, 0x76, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72,
	0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x9b,
	0xb4, 0xe6, 0x92, 0xad, 0xe5, 0x9b, 0x9e, 0xe6, 0x94, 0xbe, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80,
	0x52, 0x09, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x34, 0x0a, 0x0a, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x89, 0x80, 0xe5, 0xb1, 0x9e, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x43, 0x0a, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x10, 0x6c, 0x69, 0x76, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x42, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x11,
	0x31, 0x20, 0xe8, 0xa7, 0x84, 0xe5, 0x88, 0x99, 0x2c, 0x32, 0x20, 0xe8, 0xaf, 0xb4, 0xe6, 0x98,
	0x8e, 0x3a, 0x01, 0x31, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x38, 0x0a, 0x0f, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x25, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x22, 0x44, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x27, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x06, 0xe6, 0x9d, 0xa1, 0xe6, 0x95, 0xb0, 0x3a, 0x02,
	0x31, 0x30, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x7e, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x63, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52,
	0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x22, 0x18, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x7b, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x47, 0x6c, 0x6f, 0x62, 0x61,
	0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x63, 0x0a, 0x09,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x6c, 0x22, 0x5f, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3f, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f, 0x92, 0x41, 0x1c, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0x43, 0x6f, 0x64, 0x65, 0xd2, 0x01, 0x0a, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x81, 0x07, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x32, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42, 0x18,
	0x92, 0x41, 0x15, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x90, 0x88, 0xe4, 0xbd,
	0x9c, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x65, 0x0a, 0x10, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x52, 0x10,
	0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c,
	0x12, 0x5f, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe4,
	0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x6a, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe4, 0xba, 0xa4, 0xe6, 0x98,
	0x93, 0xe5, 0x95, 0x86, 0xe8, 0x81, 0x94, 0xe7, 0xb3, 0xbb, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6f, 0x0a,
	0x11, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe4, 0xbc, 0x98, 0xe6, 0x83, 0xa0, 0x52, 0x11, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x6c,
	0x0a, 0x10, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x72, 0x61, 0x6e, 0x64,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x93,
	0x81, 0xe7, 0x89, 0x8c, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x10, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0d,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8,
	0xe7, 0xae, 0x80, 0xe4, 0xbb, 0x8b, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x0f, 0xe6, 0x98,
	0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0x3a, 0x05, 0x66,
	0x61, 0x6c, 0x73, 0x65, 0x52, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x2b,
	0x0a, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x52, 0x07, 0x6c, 0x69, 0x76, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x39, 0x0a, 0x0e, 0x6f,
	0x70, 0x65, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe6, 0x88, 0xb7,
	0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x80, 0x01, 0x0a, 0x11, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x04,
	0x73, 0x69, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0xbd, 0x91, 0xe5, 0x9d, 0x80, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x04, 0x73,
	0x69, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0x94, 0xb5, 0xe8, 0xaf, 0x9d, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7,
	0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x77, 0x0a, 0x11, 0x41, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d,
	0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x9c, 0xb0,
	0xe5, 0x9d, 0x80, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x33, 0x0a,
	0x0b, 0x6a, 0x75, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x0b, 0x6a, 0x75, 0x6d, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0x44, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22, 0xab, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x12,
	0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x96, 0x87, 0xe6, 0xa1, 0x88, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0x93, 0x81, 0xe7, 0x89, 0x8c, 0xe8, 0xb5, 0x84, 0xe8, 0xb4, 0xa8, 0xe5, 0x9b, 0xbe, 0xe7, 0x89,
	0x87, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6c, 0x12, 0x39, 0x0a, 0x0e, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x95, 0xbf, 0xe5, 0x9b, 0xbe,
	0xe4, 0xbb, 0x8b, 0xe7, 0xbb, 0x8d, 0x52, 0x0e, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6d, 0x67, 0x22, 0x44, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x06, 0xe6, 0x9d, 0xa1, 0xe6, 0x95, 0xb0,
	0x3a, 0x02, 0x31, 0x30, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x7f, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x64, 0x0a, 0x0e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x8e,
	0xa8, 0xe8, 0x8d, 0x90, 0xe5, 0xae, 0x98, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x22, 0x4c, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13,
	0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xfc, 0x02, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2f, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0xe4, 0xbd, 0x8d, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe5, 0x9b,
	0xbe, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x69, 0x0a, 0x10, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x6c, 0x6f,
	0x67, 0x6f, 0x52, 0x10, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6c, 0x12, 0x59, 0x0a, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x43,
	0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0x88, 0x97,
	0xe8, 0xa1, 0xa8, 0x52, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x43, 0x6f, 0x6c, 0x12,
	0x69, 0x0a, 0x0d, 0x72, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6c,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0d, 0x72, 0x65, 0x77,
	0x6f, 0x72, 0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6c, 0x22, 0xb4, 0x01, 0x0a, 0x0b, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x51, 0x0a, 0x0c, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2d, 0x92, 0x41, 0x2a, 0x2a, 0x28, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe6, 0xa0, 0x87,
	0xe9, 0xa2, 0x98, 0x2c, 0xe4, 0xbe, 0x8b, 0xe5, 0xa6, 0x82, 0xef, 0xbc, 0x9a, 0x64, 0x61, 0x79,
	0x31, 0xe3, 0x80, 0x81, 0x64, 0x61, 0x79, 0x32, 0xe3, 0x80, 0x81, 0x64, 0x61, 0x79, 0x33, 0x52,
	0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x52, 0x0a,
	0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x2a, 0x24, 0xe7, 0xad, 0xbe, 0xe5, 0x88,
	0xb0, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x2c, 0x30, 0x3a, 0xe6, 0x9c, 0xaa, 0xe7, 0xad, 0xbe,
	0xe5, 0x88, 0xb0, 0x20, 0x31, 0x3a, 0xe5, 0xb7, 0xb2, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0x3a,
	0x01, 0x30, 0x52, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xc2, 0x02, 0x0a, 0x15, 0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x50, 0x6f, 0x6f, 0x6c,
	0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x09, 0x70,
	0x6f, 0x6f, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe6, 0xb1, 0xa0, 0xe6, 0xa0, 0x87, 0xe9, 0xa2,
	0x98, 0x52, 0x09, 0x70, 0x6f, 0x6f, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x0b,
	0x70, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe6, 0xb1, 0xa0, 0xe5, 0x86,
	0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0b, 0x70, 0x6f, 0x6f, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8f, 0x82,
	0xe4, 0xb8, 0x8e, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x69,
	0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xae,
	0x8c, 0xe6, 0x88, 0x90, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe6, 0x80, 0xbb, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x95, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x6f, 0x72,
	0x64, 0x50, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x6f, 0x6f,
	0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe6, 0xb1, 0xa0, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52,
	0x09, 0x70, 0x6f, 0x6f, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x52, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x43, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe6, 0xb1, 0xa0, 0xe4,
	0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6c, 0x22, 0x89,
	0x02, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2f, 0x0a,
	0x09, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0xa0, 0x87,
	0xe9, 0xa2, 0x98, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33,
	0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x63, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x49, 0x63, 0x6f, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x63, 0x6f, 0x12, 0x38, 0x0a,
	0x0a, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xae,
	0x8c, 0xe6, 0x88, 0x90, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x0a, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x31, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8c, 0x89, 0xe9, 0x92, 0xae, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0a,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x44, 0x0a, 0x12, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0xd2,
	0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x4c, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x12, 0xe7, 0xad, 0xbe, 0xe5,
	0x88, 0xb0, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x3a, 0x05,
	0x66, 0x61, 0x6c, 0x73, 0x65, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x48,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x84, 0x03, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe9, 0x87,
	0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x06, 0x72, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x6b, 0x0a, 0x0e,
	0x70, 0x6f, 0x6f, 0x6c, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x68, 0x61, 0x72, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x50, 0x6f, 0x6f, 0x6c, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96,
	0xe6, 0xb1, 0xa0, 0xe6, 0x80, 0xbb, 0xe8, 0xa7, 0x88, 0x52, 0x0e, 0x70, 0x6f, 0x6f, 0x6c, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x6f, 0x6c, 0x12, 0x6f, 0x0a, 0x13, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x43, 0x61, 0x73, 0x68, 0x42, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x43, 0x61, 0x73, 0x68, 0x42, 0x61,
	0x63, 0x6b, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x8e, 0xb0, 0xe9, 0x87, 0x91, 0xe6,
	0x8f, 0x90, 0xe5, 0x8f, 0x96, 0x52, 0x13, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x43, 0x61,
	0x73, 0x68, 0x42, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x63, 0x0a, 0x0c, 0x6c, 0x75,
	0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x43, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77,
	0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x61,
	0x6e, 0x64, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe5, 0xb9, 0xb8, 0xe8, 0xbf, 0x90, 0xe5, 0xa4, 0xa7, 0xe6, 0x8a, 0xbd, 0xe5, 0xa5,
	0x96, 0x52, 0x0c, 0x6c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x43, 0x6f, 0x6c, 0x22,
	0x7f, 0x0a, 0x15, 0x53, 0x68, 0x61, 0x72, 0x65, 0x50, 0x72, 0x69, 0x7a, 0x65, 0x50, 0x6f, 0x6f,
	0x6c, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x6f, 0x6f, 0x6c,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe6, 0xb1, 0xa0, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x09,
	0x70, 0x6f, 0x6f, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x70, 0x6f, 0x6f,
	0x6c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe6, 0xb1, 0xa0, 0xe8, 0xbf, 0x9b, 0xe5,
	0xba, 0xa6, 0x52, 0x0c, 0x70, 0x6f, 0x6f, 0x6c, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x22, 0x7c, 0x0a, 0x0f, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x43, 0x61, 0x73, 0x68, 0x42,
	0x61, 0x63, 0x6b, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x46, 0x6c,
	0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x0c, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0xbf, 0x94, 0xe7, 0x8e, 0xb0, 0x3a, 0x05, 0x66, 0x61, 0x6c,
	0x73, 0x65, 0x52, 0x0b, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x2d, 0x0a, 0x08, 0x63, 0x61, 0x73, 0x68, 0x42, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0x94, 0xe7, 0x8e, 0xb0, 0xe9, 0x87,
	0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x08, 0x63, 0x61, 0x73, 0x68, 0x42, 0x61, 0x63, 0x6b, 0x22, 0xa8,
	0x01, 0x0a, 0x0e, 0x47, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61,
	0x77, 0x12, 0x21, 0x0a, 0x05, 0x61, 0x77, 0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xa5, 0x96, 0xe5, 0x93, 0x81, 0x52, 0x05, 0x61,
	0x77, 0x61, 0x72, 0x64, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x20, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef,
	0xbc, 0x8c, 0x31, 0x3a, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9, 0x2c, 0x32, 0x3a, 0xe5, 0xa5, 0x96,
	0xe9, 0x87, 0x91, 0x2c, 0x33, 0x3a, 0x56, 0x50, 0x53, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x38, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5,
	0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe7, 0xa0, 0x81, 0x52, 0x0f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x15, 0x47, 0x72, 0x61,
	0x6e, 0x64, 0x4c, 0x75, 0x63, 0x6b, 0x79, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49,
	0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0xba, 0x03, 0x0a, 0x13, 0x47, 0x72, 0x61, 0x6e, 0x64, 0x4c, 0x75, 0x63, 0x6b,
	0x79, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x12, 0xe5, 0x89, 0xa9, 0xe4, 0xbd, 0x99, 0xe6, 0x8a, 0xbd, 0xe5, 0xa5, 0x96, 0xe6, 0xac,
	0xa1, 0xe6, 0x95, 0xb0, 0x3a, 0x01, 0x30, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x73,
	0x12, 0x53, 0x0a, 0x08, 0x61, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72,
	0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xa5, 0x96, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08, 0x61, 0x77, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6c, 0x12, 0x63, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0x90, 0x88, 0xe4, 0xbd, 0x9c,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52,
	0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x12, 0x53, 0x0a, 0x06, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x8e, 0xb7, 0xe5, 0xa5,
	0x96, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x52, 0x06, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x5e, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe6, 0x8a, 0xbd, 0xe5, 0xa5, 0x96, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x22,
	0x57, 0x0a, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xa5, 0x96, 0xe5, 0x93, 0x81, 0x6c, 0x6f,
	0x67, 0x6f, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x22, 0x9e, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x77,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52,
	0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x61, 0x77, 0x61,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0xa5, 0x96, 0xe5, 0x93, 0x81, 0x52, 0x05, 0x61, 0x77, 0x61, 0x72, 0x64, 0x12, 0x3b, 0x0a, 0x0f,
	0x72, 0x65, 0x77, 0x6f, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x8e, 0xb7, 0xe5,
	0xa5, 0x96, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0f, 0x72, 0x65, 0x77, 0x6f, 0x72, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x84, 0x03, 0x0a, 0x0e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x72, 0x61, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x40, 0x0a, 0x0b,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x8a,
	0xbd, 0xe5, 0xa5, 0x96, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73,
	0x65, 0x52, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x28,
	0x0a, 0x04, 0x74, 0x69, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x2a, 0x0f, 0xe6, 0x8a, 0xbd, 0xe5, 0xa5, 0x96, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0xe8,
	0xaf, 0x8d, 0x52, 0x04, 0x74, 0x69, 0x70, 0x73, 0x12, 0x55, 0x0a, 0x09, 0x61, 0x77, 0x61, 0x72,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xb8, 0xad, 0xe5, 0xa5, 0x96, 0xe5, 0xa5,
	0x96, 0xe5, 0x93, 0x81, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x39, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x8a, 0xbd, 0xe5, 0xa5,
	0x96, 0xe7, 0xbb, 0x93, 0xe6, 0x9e, 0x9c, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xb8, 0xad, 0xe5, 0xa5, 0x96, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd,
	0xac, 0xe6, 0x8c, 0x89, 0xe9, 0x92, 0xae, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44,
	0x65, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe5, 0xa5, 0x96, 0xe5, 0x93, 0x81, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe6, 0x8f, 0x8f, 0xe8,
	0xbf, 0xb0, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63,
	0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4c, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x33, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70,
	0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x8f, 0x82, 0xe4, 0xb8, 0x8e, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0b, 0x70, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x22, 0x4c, 0x0a, 0x13, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x35, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x0c, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe5, 0x86,
	0x85, 0xe5, 0xae, 0xb9, 0xd2, 0x01, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x6f, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x5a, 0x0a, 0x09,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe7, 0xbb, 0x93, 0xe6, 0x9e, 0x9c, 0x52, 0x09, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x3c, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x23, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x22, 0x12, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xac, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2f, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0xe4, 0xbd, 0x8d, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe5, 0x9b,
	0xbe, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x69, 0x0a, 0x10, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x6c, 0x6f,
	0x67, 0x6f, 0x52, 0x10, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6c, 0x22, 0x8d, 0x01, 0x0a, 0x0d, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0x49, 0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74,
	0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24,
	0x92, 0x41, 0x21, 0x2a, 0x0e, 0xe5, 0x8a, 0xa9, 0xe5, 0x8a, 0x9b, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0xd2, 0x01, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x65, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x65, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x0d, 0x0a, 0x0b, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x42, 0x0a, 0x10, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x10, 0x0a, 0x0e, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x4c, 0x69, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x49, 0x0a, 0x17, 0x46, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x41, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0xd2, 0x01, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x15, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x41,
	0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2d,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x82, 0x80, 0xe8, 0xaf, 0xb7, 0xe4, 0xba, 0xba,
	0xe6, 0x95, 0xb0, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe6, 0x80, 0xbb, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x33, 0x0a, 0x0b, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9,
	0x82, 0x80, 0xe8, 0xaf, 0xb7, 0xe6, 0x98, 0x8e, 0xe7, 0xbb, 0x86, 0x52, 0x0b, 0x69, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x5d, 0x0a, 0x09, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x1e, 0x5a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_growth_center_v1_investment_carnival_proto_rawDescOnce sync.Once
	file_user_growth_center_v1_investment_carnival_proto_rawDescData = file_user_growth_center_v1_investment_carnival_proto_rawDesc
)

func file_user_growth_center_v1_investment_carnival_proto_rawDescGZIP() []byte {
	file_user_growth_center_v1_investment_carnival_proto_rawDescOnce.Do(func() {
		file_user_growth_center_v1_investment_carnival_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_growth_center_v1_investment_carnival_proto_rawDescData)
	})
	return file_user_growth_center_v1_investment_carnival_proto_rawDescData
}

var file_user_growth_center_v1_investment_carnival_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_user_growth_center_v1_investment_carnival_proto_goTypes = []interface{}{
	(*GetActivityMainRequest)(nil),       // 0: api.user_growth_center.v1.GetActivityMainRequest
	(*GetActivityMainReply)(nil),         // 1: api.user_growth_center.v1.GetActivityMainReply
	(*CategoryTrader)(nil),               // 2: api.user_growth_center.v1.CategoryTrader
	(*TraderBaseInfo)(nil),               // 3: api.user_growth_center.v1.TraderBaseInfo
	(*Recommender)(nil),                  // 4: api.user_growth_center.v1.Recommender
	(*GetContentRequest)(nil),            // 5: api.user_growth_center.v1.GetContentRequest
	(*GetContentReply)(nil),              // 6: api.user_growth_center.v1.GetContentReply
	(*GetRecommendTraderRequest)(nil),    // 7: api.user_growth_center.v1.GetRecommendTraderRequest
	(*GetRecommendTraderReply)(nil),      // 8: api.user_growth_center.v1.GetRecommendTraderReply
	(*GetGlobalTraderRequest)(nil),       // 9: api.user_growth_center.v1.GetGlobalTraderRequest
	(*GetGlobalTraderReply)(nil),         // 10: api.user_growth_center.v1.GetGlobalTraderReply
	(*GetTraderActivityPageRequest)(nil), // 11: api.user_growth_center.v1.GetTraderActivityPageRequest
	(*GetTraderActivityPageReply)(nil),   // 12: api.user_growth_center.v1.GetTraderActivityPageReply
	(*TraderContactInfo)(nil),            // 13: api.user_growth_center.v1.TraderContactInfo
	(*AdvertisementInfo)(nil),            // 14: api.user_growth_center.v1.AdvertisementInfo
	(*TraderEventDiscount)(nil),          // 15: api.user_growth_center.v1.TraderEventDiscount
	(*TraderBrandDisplay)(nil),           // 16: api.user_growth_center.v1.TraderBrandDisplay
	(*GetRecommenderListRequest)(nil),    // 17: api.user_growth_center.v1.GetRecommenderListRequest
	(*GetRecommenderListReply)(nil),      // 18: api.user_growth_center.v1.GetRecommenderListReply
	(*GetRewordPoolDetailRequest)(nil),   // 19: api.user_growth_center.v1.GetRewordPoolDetailRequest
	(*GetRewordPoolDetailReply)(nil),     // 20: api.user_growth_center.v1.GetRewordPoolDetailReply
	(*CheckInInfo)(nil),                  // 21: api.user_growth_center.v1.CheckInInfo
	(*RewordPoolSummaryInfo)(nil),        // 22: api.user_growth_center.v1.RewordPoolSummaryInfo
	(*RewordPoolInfo)(nil),               // 23: api.user_growth_center.v1.RewordPoolInfo
	(*TaskDetail)(nil),                   // 24: api.user_growth_center.v1.TaskDetail
	(*UserCheckInRequest)(nil),           // 25: api.user_growth_center.v1.UserCheckInRequest
	(*UserCheckInReply)(nil),             // 26: api.user_growth_center.v1.UserCheckInReply
	(*GetRewordDetailRequest)(nil),       // 27: api.user_growth_center.v1.GetRewordDetailRequest
	(*GetRewordDetailReply)(nil),         // 28: api.user_growth_center.v1.GetRewordDetailReply
	(*SharePrizePoolSummary)(nil),        // 29: api.user_growth_center.v1.SharePrizePoolSummary
	(*DepositCashBack)(nil),              // 30: api.user_growth_center.v1.DepositCashBack
	(*GrandLuckyDraw)(nil),               // 31: api.user_growth_center.v1.GrandLuckyDraw
	(*GrandLuckyDrawRequest)(nil),        // 32: api.user_growth_center.v1.GrandLuckyDrawRequest
	(*GrandLuckyDrawReply)(nil),          // 33: api.user_growth_center.v1.GrandLuckyDrawReply
	(*AwardInfo)(nil),                    // 34: api.user_growth_center.v1.AwardInfo
	(*RewordRecords)(nil),                // 35: api.user_growth_center.v1.RewordRecords
	(*StartDrawReply)(nil),               // 36: api.user_growth_center.v1.StartDrawReply
	(*GetDepositDetailRequest)(nil),      // 37: api.user_growth_center.v1.GetDepositDetailRequest
	(*GetDepositDetailReply)(nil),        // 38: api.user_growth_center.v1.GetDepositDetailReply
	(*SearchTraderRequest)(nil),          // 39: api.user_growth_center.v1.SearchTraderRequest
	(*SearchTraderReply)(nil),            // 40: api.user_growth_center.v1.SearchTraderReply
	(*GetActivityShareRequest)(nil),      // 41: api.user_growth_center.v1.GetActivityShareRequest
	(*GetActivityShareReply)(nil),        // 42: api.user_growth_center.v1.GetActivityShareReply
	(*GetBannerRequest)(nil),             // 43: api.user_growth_center.v1.GetBannerRequest
	(*GetBannerReply)(nil),               // 44: api.user_growth_center.v1.GetBannerReply
	(*AssistRequest)(nil),                // 45: api.user_growth_center.v1.AssistRequest
	(*AssistReply)(nil),                  // 46: api.user_growth_center.v1.AssistReply
	(*WatchLiveRequest)(nil),             // 47: api.user_growth_center.v1.WatchLiveRequest
	(*WatchLiveReply)(nil),               // 48: api.user_growth_center.v1.WatchLiveReply
	(*FriendAssistanceRequest)(nil),      // 49: api.user_growth_center.v1.FriendAssistanceRequest
	(*FriendAssistanceReply)(nil),        // 50: api.user_growth_center.v1.FriendAssistanceReply
	(*LabelItem)(nil),                    // 51: api.user_growth_center.v1.LabelItem
}
var file_user_growth_center_v1_investment_carnival_proto_depIdxs = []int32{
	3,  // 0: api.user_growth_center.v1.GetActivityMainReply.popularTraderCol:type_name -> api.user_growth_center.v1.TraderBaseInfo
	2,  // 1: api.user_growth_center.v1.GetActivityMainReply.bestTraderCol:type_name -> api.user_growth_center.v1.CategoryTrader
	4,  // 2: api.user_growth_center.v1.GetActivityMainReply.recommenderCol:type_name -> api.user_growth_center.v1.Recommender
	3,  // 3: api.user_growth_center.v1.CategoryTrader.traderCol:type_name -> api.user_growth_center.v1.TraderBaseInfo
	51, // 4: api.user_growth_center.v1.TraderBaseInfo.labelCol:type_name -> api.user_growth_center.v1.LabelItem
	3,  // 5: api.user_growth_center.v1.GetRecommendTraderReply.traderCol:type_name -> api.user_growth_center.v1.TraderBaseInfo
	2,  // 6: api.user_growth_center.v1.GetGlobalTraderReply.traderCol:type_name -> api.user_growth_center.v1.CategoryTrader
	14, // 7: api.user_growth_center.v1.GetTraderActivityPageReply.advertisementCol:type_name -> api.user_growth_center.v1.AdvertisementInfo
	3,  // 8: api.user_growth_center.v1.GetTraderActivityPageReply.traderData:type_name -> api.user_growth_center.v1.TraderBaseInfo
	13, // 9: api.user_growth_center.v1.GetTraderActivityPageReply.contactData:type_name -> api.user_growth_center.v1.TraderContactInfo
	15, // 10: api.user_growth_center.v1.GetTraderActivityPageReply.eventDiscountData:type_name -> api.user_growth_center.v1.TraderEventDiscount
	16, // 11: api.user_growth_center.v1.GetTraderActivityPageReply.brandDisplayData:type_name -> api.user_growth_center.v1.TraderBrandDisplay
	4,  // 12: api.user_growth_center.v1.GetRecommenderListReply.recommenderCol:type_name -> api.user_growth_center.v1.Recommender
	14, // 13: api.user_growth_center.v1.GetRewordPoolDetailReply.advertisementCol:type_name -> api.user_growth_center.v1.AdvertisementInfo
	21, // 14: api.user_growth_center.v1.GetRewordPoolDetailReply.checkInCol:type_name -> api.user_growth_center.v1.CheckInInfo
	22, // 15: api.user_growth_center.v1.GetRewordPoolDetailReply.rewordPoolCol:type_name -> api.user_growth_center.v1.RewordPoolSummaryInfo
	24, // 16: api.user_growth_center.v1.RewordPoolInfo.taskCol:type_name -> api.user_growth_center.v1.TaskDetail
	29, // 17: api.user_growth_center.v1.GetRewordDetailReply.poolSummaryCol:type_name -> api.user_growth_center.v1.SharePrizePoolSummary
	30, // 18: api.user_growth_center.v1.GetRewordDetailReply.depositCashBackData:type_name -> api.user_growth_center.v1.DepositCashBack
	31, // 19: api.user_growth_center.v1.GetRewordDetailReply.luckyDrawCol:type_name -> api.user_growth_center.v1.GrandLuckyDraw
	34, // 20: api.user_growth_center.v1.GrandLuckyDrawReply.awardCol:type_name -> api.user_growth_center.v1.AwardInfo
	3,  // 21: api.user_growth_center.v1.GrandLuckyDrawReply.traderCol:type_name -> api.user_growth_center.v1.TraderBaseInfo
	35, // 22: api.user_growth_center.v1.GrandLuckyDrawReply.record:type_name -> api.user_growth_center.v1.RewordRecords
	24, // 23: api.user_growth_center.v1.GrandLuckyDrawReply.task:type_name -> api.user_growth_center.v1.TaskDetail
	34, // 24: api.user_growth_center.v1.StartDrawReply.awardData:type_name -> api.user_growth_center.v1.AwardInfo
	3,  // 25: api.user_growth_center.v1.SearchTraderReply.traderCol:type_name -> api.user_growth_center.v1.TraderBaseInfo
	14, // 26: api.user_growth_center.v1.GetBannerReply.advertisementCol:type_name -> api.user_growth_center.v1.AdvertisementInfo
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_user_growth_center_v1_investment_carnival_proto_init() }
func file_user_growth_center_v1_investment_carnival_proto_init() {
	if File_user_growth_center_v1_investment_carnival_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityMainRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityMainReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryTrader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Recommender); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetContentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetContentReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendTraderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommendTraderReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGlobalTraderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGlobalTraderReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTraderActivityPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTraderActivityPageReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderContactInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdvertisementInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderEventDiscount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderBrandDisplay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommenderListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRecommenderListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewordPoolDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewordPoolDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckInInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewordPoolSummaryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewordPoolInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCheckInRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCheckInReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewordDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewordDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharePrizePoolSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositCashBack); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrandLuckyDraw); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrandLuckyDrawRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrandLuckyDrawReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AwardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewordRecords); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartDrawReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepositDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDepositDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTraderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTraderReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityShareRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityShareReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBannerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBannerReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssistRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssistReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchLiveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WatchLiveReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FriendAssistanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FriendAssistanceReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_investment_carnival_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_growth_center_v1_investment_carnival_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_growth_center_v1_investment_carnival_proto_goTypes,
		DependencyIndexes: file_user_growth_center_v1_investment_carnival_proto_depIdxs,
		MessageInfos:      file_user_growth_center_v1_investment_carnival_proto_msgTypes,
	}.Build()
	File_user_growth_center_v1_investment_carnival_proto = out.File
	file_user_growth_center_v1_investment_carnival_proto_rawDesc = nil
	file_user_growth_center_v1_investment_carnival_proto_goTypes = nil
	file_user_growth_center_v1_investment_carnival_proto_depIdxs = nil
}
