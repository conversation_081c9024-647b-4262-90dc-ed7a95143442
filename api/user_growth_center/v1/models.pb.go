// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: user_growth_center/v1/models.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 题目类型枚举
type QuestionType int32

const (
	QuestionType_UNSPECIFIED     QuestionType = 0 // 未指定
	QuestionType_SINGLE_CHOICE   QuestionType = 1 // 单选
	QuestionType_MULTIPLE_CHOICE QuestionType = 2 // 多选
)

// Enum value maps for QuestionType.
var (
	QuestionType_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "SINGLE_CHOICE",
		2: "MULTIPLE_CHOICE",
	}
	QuestionType_value = map[string]int32{
		"UNSPECIFIED":     0,
		"SINGLE_CHOICE":   1,
		"MULTIPLE_CHOICE": 2,
	}
)

func (x QuestionType) Enum() *QuestionType {
	p := new(QuestionType)
	*p = x
	return p
}

func (x QuestionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_growth_center_v1_models_proto_enumTypes[0].Descriptor()
}

func (QuestionType) Type() protoreflect.EnumType {
	return &file_user_growth_center_v1_models_proto_enumTypes[0]
}

func (x QuestionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionType.Descriptor instead.
func (QuestionType) EnumDescriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{0}
}

type GetUserInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetUserInfoRequest) Reset() {
	*x = GetUserInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoRequest) ProtoMessage() {}

func (x *GetUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{0}
}

type GetUserInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
}

func (x *GetUserInfoReply) Reset() {
	*x = GetUserInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoReply) ProtoMessage() {}

func (x *GetUserInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserInfoReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserInfoReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UpdateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{2}
}

type UpdateUserReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateUserReply) Reset() {
	*x = UpdateUserReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserReply) ProtoMessage() {}

func (x *UpdateUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserReply.ProtoReflect.Descriptor instead.
func (*UpdateUserReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{3}
}

type StringReplyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StringReplyRequest) Reset() {
	*x = StringReplyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringReplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringReplyRequest) ProtoMessage() {}

func (x *StringReplyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringReplyRequest.ProtoReflect.Descriptor instead.
func (*StringReplyRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{4}
}

type StringReplyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StringReplyReply) Reset() {
	*x = StringReplyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StringReplyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringReplyReply) ProtoMessage() {}

func (x *StringReplyReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringReplyReply.ProtoReflect.Descriptor instead.
func (*StringReplyReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{5}
}

// 获取邀请奖励弹窗数据
type GetInvitationPopupDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetInvitationPopupDataRequest) Reset() {
	*x = GetInvitationPopupDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitationPopupDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitationPopupDataRequest) ProtoMessage() {}

func (x *GetInvitationPopupDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitationPopupDataRequest.ProtoReflect.Descriptor instead.
func (*GetInvitationPopupDataRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{6}
}

func (x *GetInvitationPopupDataRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetInvitationPopupDataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title      string    `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	UserData   *UserData `protobuf:"bytes,2,opt,name=userData,json=userData,proto3" json:"userData"`
	ContentCol []string  `protobuf:"bytes,3,rep,name=contentCol,json=contentCol,proto3" json:"contentCol"`
}

func (x *GetInvitationPopupDataReply) Reset() {
	*x = GetInvitationPopupDataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitationPopupDataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitationPopupDataReply) ProtoMessage() {}

func (x *GetInvitationPopupDataReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitationPopupDataReply.ProtoReflect.Descriptor instead.
func (*GetInvitationPopupDataReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{7}
}

func (x *GetInvitationPopupDataReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetInvitationPopupDataReply) GetUserData() *UserData {
	if x != nil {
		return x.UserData
	}
	return nil
}

func (x *GetInvitationPopupDataReply) GetContentCol() []string {
	if x != nil {
		return x.ContentCol
	}
	return nil
}

// 获取邀请奖励领取banner数据
type GetInviteRewardBannerDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetInviteRewardBannerDataRequest) Reset() {
	*x = GetInviteRewardBannerDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInviteRewardBannerDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInviteRewardBannerDataRequest) ProtoMessage() {}

func (x *GetInviteRewardBannerDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInviteRewardBannerDataRequest.ProtoReflect.Descriptor instead.
func (*GetInviteRewardBannerDataRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{8}
}

func (x *GetInviteRewardBannerDataRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetInviteRewardBannerDataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title            string    `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	UserData         *UserData `protobuf:"bytes,2,opt,name=userData,json=userData,proto3" json:"userData"`
	ContentCol       []string  `protobuf:"bytes,3,rep,name=contentCol,json=contentCol,proto3" json:"contentCol"`
	VpsVerifyImgUrl  string    `protobuf:"bytes,4,opt,name=vpsVerifyImgUrl,json=vpsVerifyImgUrl,proto3" json:"vpsVerifyImgUrl"`
	VpsVerifyContent string    `protobuf:"bytes,5,opt,name=vpsVerifyContent,json=vpsVerifyContent,proto3" json:"vpsVerifyContent"`
	VpsVerifyBgColor string    `protobuf:"bytes,6,opt,name=vpsVerifyBgColor,json=vpsVerifyBgColor,proto3" json:"vpsVerifyBgColor"`
}

func (x *GetInviteRewardBannerDataReply) Reset() {
	*x = GetInviteRewardBannerDataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInviteRewardBannerDataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInviteRewardBannerDataReply) ProtoMessage() {}

func (x *GetInviteRewardBannerDataReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInviteRewardBannerDataReply.ProtoReflect.Descriptor instead.
func (*GetInviteRewardBannerDataReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{9}
}

func (x *GetInviteRewardBannerDataReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetInviteRewardBannerDataReply) GetUserData() *UserData {
	if x != nil {
		return x.UserData
	}
	return nil
}

func (x *GetInviteRewardBannerDataReply) GetContentCol() []string {
	if x != nil {
		return x.ContentCol
	}
	return nil
}

func (x *GetInviteRewardBannerDataReply) GetVpsVerifyImgUrl() string {
	if x != nil {
		return x.VpsVerifyImgUrl
	}
	return ""
}

func (x *GetInviteRewardBannerDataReply) GetVpsVerifyContent() string {
	if x != nil {
		return x.VpsVerifyContent
	}
	return ""
}

func (x *GetInviteRewardBannerDataReply) GetVpsVerifyBgColor() string {
	if x != nil {
		return x.VpsVerifyBgColor
	}
	return ""
}

// 获取分享推广链接数据
type GetShareLinkDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetShareLinkDataRequest) Reset() {
	*x = GetShareLinkDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetShareLinkDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShareLinkDataRequest) ProtoMessage() {}

func (x *GetShareLinkDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShareLinkDataRequest.ProtoReflect.Descriptor instead.
func (*GetShareLinkDataRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{10}
}

func (x *GetShareLinkDataRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetShareLinkDataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserData      *UserData                            `protobuf:"bytes,1,opt,name=userData,json=userData,proto3" json:"userData"`
	ShareLinkData *GetShareLinkDataReply_ShareLinkData `protobuf:"bytes,2,opt,name=shareLinkData,json=shareLinkData,proto3" json:"shareLinkData"`
}

func (x *GetShareLinkDataReply) Reset() {
	*x = GetShareLinkDataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetShareLinkDataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShareLinkDataReply) ProtoMessage() {}

func (x *GetShareLinkDataReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShareLinkDataReply.ProtoReflect.Descriptor instead.
func (*GetShareLinkDataReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{11}
}

func (x *GetShareLinkDataReply) GetUserData() *UserData {
	if x != nil {
		return x.UserData
	}
	return nil
}

func (x *GetShareLinkDataReply) GetShareLinkData() *GetShareLinkDataReply_ShareLinkData {
	if x != nil {
		return x.ShareLinkData
	}
	return nil
}

// 获取邀请记录数据
type GetInvitedRecordDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32 `protobuf:"varint,2,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetInvitedRecordDataRequest) Reset() {
	*x = GetInvitedRecordDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitedRecordDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitedRecordDataRequest) ProtoMessage() {}

func (x *GetInvitedRecordDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitedRecordDataRequest.ProtoReflect.Descriptor instead.
func (*GetInvitedRecordDataRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{12}
}

func (x *GetInvitedRecordDataRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetInvitedRecordDataRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetInvitedRecordDataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 邀请总人数
	InvitedCount int32 `protobuf:"varint,1,opt,name=invitedCount,json=invitedCount,proto3" json:"invitedCount"`
	// 升级次数
	UpgradedCount        int32                                          `protobuf:"varint,2,opt,name=upgradedCount,json=upgradedCount,proto3" json:"upgradedCount"`
	InvitedRecordDataCol []*GetInvitedRecordDataReply_InvitedRecordData `protobuf:"bytes,3,rep,name=invitedRecordDataCol,json=invitedRecordDataCol,proto3" json:"invitedRecordDataCol"`
}

func (x *GetInvitedRecordDataReply) Reset() {
	*x = GetInvitedRecordDataReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitedRecordDataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitedRecordDataReply) ProtoMessage() {}

func (x *GetInvitedRecordDataReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitedRecordDataReply.ProtoReflect.Descriptor instead.
func (*GetInvitedRecordDataReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{13}
}

func (x *GetInvitedRecordDataReply) GetInvitedCount() int32 {
	if x != nil {
		return x.InvitedCount
	}
	return 0
}

func (x *GetInvitedRecordDataReply) GetUpgradedCount() int32 {
	if x != nil {
		return x.UpgradedCount
	}
	return 0
}

func (x *GetInvitedRecordDataReply) GetInvitedRecordDataCol() []*GetInvitedRecordDataReply_InvitedRecordData {
	if x != nil {
		return x.InvitedRecordDataCol
	}
	return nil
}

type GetVpsLevelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level int32 `protobuf:"varint,1,opt,name=level,json=level,proto3" json:"level"`
}

func (x *GetVpsLevelReply) Reset() {
	*x = GetVpsLevelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVpsLevelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVpsLevelReply) ProtoMessage() {}

func (x *GetVpsLevelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVpsLevelReply.ProtoReflect.Descriptor instead.
func (*GetVpsLevelReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{14}
}

func (x *GetVpsLevelReply) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// ----------------------------------答题模块----------------------------------//
type UserData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId        string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	AvatarAddress string `protobuf:"bytes,2,opt,name=avatarAddress,json=avatarAddress,proto3" json:"avatarAddress"`
	NickName      string `protobuf:"bytes,3,opt,name=nickName,json=nickName,proto3" json:"nickName"`
	WikiId        string `protobuf:"bytes,4,opt,name=wikiId,json=wikiId,proto3" json:"wikiId"`
}

func (x *UserData) Reset() {
	*x = UserData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserData) ProtoMessage() {}

func (x *UserData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserData.ProtoReflect.Descriptor instead.
func (*UserData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{15}
}

func (x *UserData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserData) GetAvatarAddress() string {
	if x != nil {
		return x.AvatarAddress
	}
	return ""
}

func (x *UserData) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserData) GetWikiId() string {
	if x != nil {
		return x.WikiId
	}
	return ""
}

// 获取试卷信息
type QuizInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionId   string             `protobuf:"bytes,1,opt,name=questionId,json=questionId,proto3" json:"questionId"`
	QuestionType QuestionType       `protobuf:"varint,2,opt,name=questionType,json=questionType,proto3,enum=api.user_growth_center.v1.QuestionType" json:"questionType"`
	Content      string             `protobuf:"bytes,3,opt,name=content,json=content,proto3" json:"content"`
	Options      []*QuizInfo_Option `protobuf:"bytes,4,rep,name=options,json=options,proto3" json:"options"`
}

func (x *QuizInfo) Reset() {
	*x = QuizInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuizInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuizInfo) ProtoMessage() {}

func (x *QuizInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuizInfo.ProtoReflect.Descriptor instead.
func (*QuizInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{16}
}

func (x *QuizInfo) GetQuestionId() string {
	if x != nil {
		return x.QuestionId
	}
	return ""
}

func (x *QuizInfo) GetQuestionType() QuestionType {
	if x != nil {
		return x.QuestionType
	}
	return QuestionType_UNSPECIFIED
}

func (x *QuizInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *QuizInfo) GetOptions() []*QuizInfo_Option {
	if x != nil {
		return x.Options
	}
	return nil
}

type GetQuizInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuizInfo []*QuizInfo `protobuf:"bytes,1,rep,name=quizInfo,json=quizInfo,proto3" json:"quizInfo"`
}

func (x *GetQuizInfoReply) Reset() {
	*x = GetQuizInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuizInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuizInfoReply) ProtoMessage() {}

func (x *GetQuizInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuizInfoReply.ProtoReflect.Descriptor instead.
func (*GetQuizInfoReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{17}
}

func (x *GetQuizInfoReply) GetQuizInfo() []*QuizInfo {
	if x != nil {
		return x.QuizInfo
	}
	return nil
}

// 用户提交试卷
type SubmitQuizRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Answers []*SubmitQuizRequest_UserAnswer `protobuf:"bytes,1,rep,name=answers,json=answers,proto3" json:"answers"`
}

func (x *SubmitQuizRequest) Reset() {
	*x = SubmitQuizRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitQuizRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitQuizRequest) ProtoMessage() {}

func (x *SubmitQuizRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitQuizRequest.ProtoReflect.Descriptor instead.
func (*SubmitQuizRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{18}
}

func (x *SubmitQuizRequest) GetAnswers() []*SubmitQuizRequest_UserAnswer {
	if x != nil {
		return x.Answers
	}
	return nil
}

type SubmitQuizReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsPass               bool     `protobuf:"varint,1,opt,name=isPass,json=isPass,proto3" json:"isPass"`
	IncorrectQuestionIds []string `protobuf:"bytes,2,rep,name=incorrectQuestionIds,json=incorrectQuestionIds,proto3" json:"incorrectQuestionIds"`
}

func (x *SubmitQuizReply) Reset() {
	*x = SubmitQuizReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitQuizReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitQuizReply) ProtoMessage() {}

func (x *SubmitQuizReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitQuizReply.ProtoReflect.Descriptor instead.
func (*SubmitQuizReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{19}
}

func (x *SubmitQuizReply) GetIsPass() bool {
	if x != nil {
		return x.IsPass
	}
	return false
}

func (x *SubmitQuizReply) GetIncorrectQuestionIds() []string {
	if x != nil {
		return x.IncorrectQuestionIds
	}
	return nil
}

// 获取试卷记录
type GetQuizRecordReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsPass  bool                             `protobuf:"varint,1,opt,name=isPass,json=isPass,proto3" json:"isPass"`
	Results []*GetQuizRecordReply_QuizResult `protobuf:"bytes,2,rep,name=results,json=results,proto3" json:"results"`
}

func (x *GetQuizRecordReply) Reset() {
	*x = GetQuizRecordReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuizRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuizRecordReply) ProtoMessage() {}

func (x *GetQuizRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuizRecordReply.ProtoReflect.Descriptor instead.
func (*GetQuizRecordReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{20}
}

func (x *GetQuizRecordReply) GetIsPass() bool {
	if x != nil {
		return x.IsPass
	}
	return false
}

func (x *GetQuizRecordReply) GetResults() []*GetQuizRecordReply_QuizResult {
	if x != nil {
		return x.Results
	}
	return nil
}

// ----------------------------------答题模块----------------------------------//
// ----------------------------------配置奖励层级----------------------------------//
// 活动时间
type InviterActivityTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EffectiveDate int64 `protobuf:"varint,1,opt,name=EffectiveDate,json=EffectiveDate,proto3" json:"EffectiveDate"`
	ExpireDate    int64 `protobuf:"varint,2,opt,name=ExpireDate,json=ExpireDate,proto3" json:"ExpireDate"`
}

func (x *InviterActivityTime) Reset() {
	*x = InviterActivityTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InviterActivityTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InviterActivityTime) ProtoMessage() {}

func (x *InviterActivityTime) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InviterActivityTime.ProtoReflect.Descriptor instead.
func (*InviterActivityTime) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{21}
}

func (x *InviterActivityTime) GetEffectiveDate() int64 {
	if x != nil {
		return x.EffectiveDate
	}
	return 0
}

func (x *InviterActivityTime) GetExpireDate() int64 {
	if x != nil {
		return x.ExpireDate
	}
	return 0
}

// 获取活动时间
type GetInviterActivityTimeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterActivityTime *InviterActivityTime `protobuf:"bytes,1,opt,name=InviterActivityTime,json=InviterActivityTime,proto3" json:"InviterActivityTime"`
}

func (x *GetInviterActivityTimeReply) Reset() {
	*x = GetInviterActivityTimeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInviterActivityTimeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInviterActivityTimeReply) ProtoMessage() {}

func (x *GetInviterActivityTimeReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInviterActivityTimeReply.ProtoReflect.Descriptor instead.
func (*GetInviterActivityTimeReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{22}
}

func (x *GetInviterActivityTimeReply) GetInviterActivityTime() *InviterActivityTime {
	if x != nil {
		return x.InviterActivityTime
	}
	return nil
}

// 修改活动时间
type UpdateInviterActivityTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterActivityTime *InviterActivityTime `protobuf:"bytes,1,opt,name=InviterActivityTime,json=InviterActivityTime,proto3" json:"InviterActivityTime"`
}

func (x *UpdateInviterActivityTimeRequest) Reset() {
	*x = UpdateInviterActivityTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInviterActivityTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInviterActivityTimeRequest) ProtoMessage() {}

func (x *UpdateInviterActivityTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInviterActivityTimeRequest.ProtoReflect.Descriptor instead.
func (*UpdateInviterActivityTimeRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateInviterActivityTimeRequest) GetInviterActivityTime() *InviterActivityTime {
	if x != nil {
		return x.InviterActivityTime
	}
	return nil
}

type UpdateInviterActivityTimeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterActivityTime *InviterActivityTime `protobuf:"bytes,1,opt,name=InviterActivityTime,json=InviterActivityTime,proto3" json:"InviterActivityTime"`
}

func (x *UpdateInviterActivityTimeReply) Reset() {
	*x = UpdateInviterActivityTimeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInviterActivityTimeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInviterActivityTimeReply) ProtoMessage() {}

func (x *UpdateInviterActivityTimeReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInviterActivityTimeReply.ProtoReflect.Descriptor instead.
func (*UpdateInviterActivityTimeReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateInviterActivityTimeReply) GetInviterActivityTime() *InviterActivityTime {
	if x != nil {
		return x.InviterActivityTime
	}
	return nil
}

// 获取奖励层级
type GetUserDivisionRewardLevelInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardLevelCol []*GetUserDivisionRewardLevelInfoReply_RewardLevel `protobuf:"bytes,1,rep,name=RewardLevelCol,json=rewardLevelCol,proto3" json:"RewardLevelCol"`
}

func (x *GetUserDivisionRewardLevelInfoReply) Reset() {
	*x = GetUserDivisionRewardLevelInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionRewardLevelInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionRewardLevelInfoReply) ProtoMessage() {}

func (x *GetUserDivisionRewardLevelInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionRewardLevelInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserDivisionRewardLevelInfoReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{25}
}

func (x *GetUserDivisionRewardLevelInfoReply) GetRewardLevelCol() []*GetUserDivisionRewardLevelInfoReply_RewardLevel {
	if x != nil {
		return x.RewardLevelCol
	}
	return nil
}

// 新增用户分裂奖励配置
type CreateUserDivisionRewardLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Threshold int32 `protobuf:"varint,1,opt,name=threshold,json=threshold,proto3" json:"threshold"`
	Level     int32 `protobuf:"varint,2,opt,name=level,json=level,proto3" json:"level"`
}

func (x *CreateUserDivisionRewardLevelRequest) Reset() {
	*x = CreateUserDivisionRewardLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserDivisionRewardLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserDivisionRewardLevelRequest) ProtoMessage() {}

func (x *CreateUserDivisionRewardLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserDivisionRewardLevelRequest.ProtoReflect.Descriptor instead.
func (*CreateUserDivisionRewardLevelRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{26}
}

func (x *CreateUserDivisionRewardLevelRequest) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *CreateUserDivisionRewardLevelRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type CreateUserDivisionRewardLevelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *CreateUserDivisionRewardLevelReply) Reset() {
	*x = CreateUserDivisionRewardLevelReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserDivisionRewardLevelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserDivisionRewardLevelReply) ProtoMessage() {}

func (x *CreateUserDivisionRewardLevelReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserDivisionRewardLevelReply.ProtoReflect.Descriptor instead.
func (*CreateUserDivisionRewardLevelReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{27}
}

func (x *CreateUserDivisionRewardLevelReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 修改用户分裂奖励配置
type UpdateUserDivisionRewardLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Threshold int32  `protobuf:"varint,2,opt,name=threshold,json=threshold,proto3" json:"threshold"`
	Level     int32  `protobuf:"varint,3,opt,name=level,json=level,proto3" json:"level"`
}

func (x *UpdateUserDivisionRewardLevelRequest) Reset() {
	*x = UpdateUserDivisionRewardLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserDivisionRewardLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDivisionRewardLevelRequest) ProtoMessage() {}

func (x *UpdateUserDivisionRewardLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDivisionRewardLevelRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserDivisionRewardLevelRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateUserDivisionRewardLevelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateUserDivisionRewardLevelRequest) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *UpdateUserDivisionRewardLevelRequest) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// 删除用户分裂奖励配置
type DeleteUserDivisionRewardLevelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteUserDivisionRewardLevelRequest) Reset() {
	*x = DeleteUserDivisionRewardLevelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteUserDivisionRewardLevelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserDivisionRewardLevelRequest) ProtoMessage() {}

func (x *DeleteUserDivisionRewardLevelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserDivisionRewardLevelRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserDivisionRewardLevelRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{29}
}

func (x *DeleteUserDivisionRewardLevelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 新增邀请记录
type CreateUserDivisionInvitationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviteeId       string `protobuf:"bytes,1,opt,name=inviteeId,json=inviteeId,proto3" json:"inviteeId"`
	InviteeDeviceId string `protobuf:"bytes,3,opt,name=inviteeDeviceId,json=inviteeDeviceId,proto3" json:"inviteeDeviceId"`
	InviterId       string `protobuf:"bytes,4,opt,name=inviterId,json=inviterId,proto3" json:"inviterId"`
}

func (x *CreateUserDivisionInvitationRequest) Reset() {
	*x = CreateUserDivisionInvitationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserDivisionInvitationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserDivisionInvitationRequest) ProtoMessage() {}

func (x *CreateUserDivisionInvitationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserDivisionInvitationRequest.ProtoReflect.Descriptor instead.
func (*CreateUserDivisionInvitationRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{30}
}

func (x *CreateUserDivisionInvitationRequest) GetInviteeId() string {
	if x != nil {
		return x.InviteeId
	}
	return ""
}

func (x *CreateUserDivisionInvitationRequest) GetInviteeDeviceId() string {
	if x != nil {
		return x.InviteeDeviceId
	}
	return ""
}

func (x *CreateUserDivisionInvitationRequest) GetInviterId() string {
	if x != nil {
		return x.InviterId
	}
	return ""
}

type CreateUserDivisionInvitationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *CreateUserDivisionInvitationReply) Reset() {
	*x = CreateUserDivisionInvitationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserDivisionInvitationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserDivisionInvitationReply) ProtoMessage() {}

func (x *CreateUserDivisionInvitationReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserDivisionInvitationReply.ProtoReflect.Descriptor instead.
func (*CreateUserDivisionInvitationReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{31}
}

func (x *CreateUserDivisionInvitationReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 获取邀请人统计列表
type GetUserDivisionInviterStatisticsInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keywords  string `protobuf:"bytes,1,opt,name=keywords,json=keywords,proto3" json:"keywords"`
	PageIndex int32  `protobuf:"varint,3,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32  `protobuf:"varint,4,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserDivisionInviterStatisticsInfoRequest) Reset() {
	*x = GetUserDivisionInviterStatisticsInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviterStatisticsInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviterStatisticsInfoRequest) ProtoMessage() {}

func (x *GetUserDivisionInviterStatisticsInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviterStatisticsInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviterStatisticsInfoRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{32}
}

func (x *GetUserDivisionInviterStatisticsInfoRequest) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *GetUserDivisionInviterStatisticsInfoRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserDivisionInviterStatisticsInfoRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserDivisionInviterStatisticsInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterStatisticsCol []*GetUserDivisionInviterStatisticsInfoReply_InviterStatistics `protobuf:"bytes,1,rep,name=inviterStatisticsCol,json=inviterStatisticsCol,proto3" json:"inviterStatisticsCol"`
}

func (x *GetUserDivisionInviterStatisticsInfoReply) Reset() {
	*x = GetUserDivisionInviterStatisticsInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviterStatisticsInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviterStatisticsInfoReply) ProtoMessage() {}

func (x *GetUserDivisionInviterStatisticsInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviterStatisticsInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviterStatisticsInfoReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{33}
}

func (x *GetUserDivisionInviterStatisticsInfoReply) GetInviterStatisticsCol() []*GetUserDivisionInviterStatisticsInfoReply_InviterStatistics {
	if x != nil {
		return x.InviterStatisticsCol
	}
	return nil
}

// 获取受邀人信息列表
type GetUserDivisionInviteeInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterId     string `protobuf:"bytes,1,opt,name=inviterId,json=inviterId,proto3" json:"inviterId"`
	BindingStatus int32  `protobuf:"varint,2,opt,name=bindingStatus,json=bindingStatus,proto3" json:"bindingStatus"`
	PageIndex     int32  `protobuf:"varint,3,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize      int32  `protobuf:"varint,4,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserDivisionInviteeInfoRequest) Reset() {
	*x = GetUserDivisionInviteeInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviteeInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviteeInfoRequest) ProtoMessage() {}

func (x *GetUserDivisionInviteeInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviteeInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviteeInfoRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{34}
}

func (x *GetUserDivisionInviteeInfoRequest) GetInviterId() string {
	if x != nil {
		return x.InviterId
	}
	return ""
}

func (x *GetUserDivisionInviteeInfoRequest) GetBindingStatus() int32 {
	if x != nil {
		return x.BindingStatus
	}
	return 0
}

func (x *GetUserDivisionInviteeInfoRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserDivisionInviteeInfoRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserDivisionInviteeInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviterInfoCol []*GetUserDivisionInviteeInfoReply_InviterInfo `protobuf:"bytes,1,rep,name=inviterInfoCol,json=inviterInfoCol,proto3" json:"inviterInfoCol"`
}

func (x *GetUserDivisionInviteeInfoReply) Reset() {
	*x = GetUserDivisionInviteeInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviteeInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviteeInfoReply) ProtoMessage() {}

func (x *GetUserDivisionInviteeInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviteeInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviteeInfoReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{35}
}

func (x *GetUserDivisionInviteeInfoReply) GetInviterInfoCol() []*GetUserDivisionInviteeInfoReply_InviterInfo {
	if x != nil {
		return x.InviterInfoCol
	}
	return nil
}

// 获取用户分裂邀请活动VPS列表
type GetUserDivisionActivityListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keywords  string `protobuf:"bytes,1,opt,name=keywords,json=keywords,proto3" json:"keywords"`
	Status    int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"`
	PageIndex int32  `protobuf:"varint,3,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32  `protobuf:"varint,4,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserDivisionActivityListRequest) Reset() {
	*x = GetUserDivisionActivityListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionActivityListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionActivityListRequest) ProtoMessage() {}

func (x *GetUserDivisionActivityListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionActivityListRequest.ProtoReflect.Descriptor instead.
func (*GetUserDivisionActivityListRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{36}
}

func (x *GetUserDivisionActivityListRequest) GetKeywords() string {
	if x != nil {
		return x.Keywords
	}
	return ""
}

func (x *GetUserDivisionActivityListRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetUserDivisionActivityListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserDivisionActivityListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserDivisionActivityListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VpsUpgradeRecordCol []*GetUserDivisionActivityListReply_VPSUpgradeRecord `protobuf:"bytes,1,rep,name=vpsUpgradeRecordCol,json=vpsUpgradeRecordCol,proto3" json:"vpsUpgradeRecordCol"`
}

func (x *GetUserDivisionActivityListReply) Reset() {
	*x = GetUserDivisionActivityListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionActivityListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionActivityListReply) ProtoMessage() {}

func (x *GetUserDivisionActivityListReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionActivityListReply.ProtoReflect.Descriptor instead.
func (*GetUserDivisionActivityListReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{37}
}

func (x *GetUserDivisionActivityListReply) GetVpsUpgradeRecordCol() []*GetUserDivisionActivityListReply_VPSUpgradeRecord {
	if x != nil {
		return x.VpsUpgradeRecordCol
	}
	return nil
}

// ----------------------------------配置奖励层级----------------------------------//
// 裂变活动详情
type GetUserDivisionActivityInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    int32                                       `protobuf:"varint,1,opt,name=status,json=status,proto3" json:"status"`
	AwardData *GetUserDivisionActivityInfoReply_AwardData `protobuf:"bytes,2,opt,name=awardData,json=awardData,proto3" json:"awardData"`
}

func (x *GetUserDivisionActivityInfoReply) Reset() {
	*x = GetUserDivisionActivityInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionActivityInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionActivityInfoReply) ProtoMessage() {}

func (x *GetUserDivisionActivityInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionActivityInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserDivisionActivityInfoReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{38}
}

func (x *GetUserDivisionActivityInfoReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetUserDivisionActivityInfoReply) GetAwardData() *GetUserDivisionActivityInfoReply_AwardData {
	if x != nil {
		return x.AwardData
	}
	return nil
}

// 裂变活动入口
type GetUserDivisionEntryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShowEntryFlag bool     `protobuf:"varint,1,opt,name=showEntryFlag,json=showEntryFlag,proto3" json:"showEntryFlag"`
	Image         string   `protobuf:"bytes,2,opt,name=image,json=image,proto3" json:"image"`
	Address       string   `protobuf:"bytes,3,opt,name=address,json=address,proto3" json:"address"`
	Title         string   `protobuf:"bytes,4,opt,name=title,json=title,proto3" json:"title"`
	Slogon        string   `protobuf:"bytes,5,opt,name=slogon,json=slogon,proto3" json:"slogon"`
	Color         []string `protobuf:"bytes,6,rep,name=color,json=color,proto3" json:"color"`
}

func (x *GetUserDivisionEntryReply) Reset() {
	*x = GetUserDivisionEntryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionEntryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionEntryReply) ProtoMessage() {}

func (x *GetUserDivisionEntryReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionEntryReply.ProtoReflect.Descriptor instead.
func (*GetUserDivisionEntryReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{39}
}

func (x *GetUserDivisionEntryReply) GetShowEntryFlag() bool {
	if x != nil {
		return x.ShowEntryFlag
	}
	return false
}

func (x *GetUserDivisionEntryReply) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *GetUserDivisionEntryReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GetUserDivisionEntryReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetUserDivisionEntryReply) GetSlogon() string {
	if x != nil {
		return x.Slogon
	}
	return ""
}

func (x *GetUserDivisionEntryReply) GetColor() []string {
	if x != nil {
		return x.Color
	}
	return nil
}

// 升级VPS
type UpgradeVPSRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId    string `protobuf:"bytes,1,opt,name=taskId,json=taskId,proto3" json:"taskId"`
	TaskLevel int32  `protobuf:"varint,2,opt,name=taskLevel,json=taskLevel,proto3" json:"taskLevel"`
}

func (x *UpgradeVPSRequest) Reset() {
	*x = UpgradeVPSRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeVPSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeVPSRequest) ProtoMessage() {}

func (x *UpgradeVPSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeVPSRequest.ProtoReflect.Descriptor instead.
func (*UpgradeVPSRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{40}
}

func (x *UpgradeVPSRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UpgradeVPSRequest) GetTaskLevel() int32 {
	if x != nil {
		return x.TaskLevel
	}
	return 0
}

// 升级VPS返回
type UpgradeVPSReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpgradeSucceed bool `protobuf:"varint,1,opt,name=upgradeSucceed,json=upgradeSucceed,proto3" json:"upgradeSucceed"`
}

func (x *UpgradeVPSReply) Reset() {
	*x = UpgradeVPSReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpgradeVPSReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeVPSReply) ProtoMessage() {}

func (x *UpgradeVPSReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeVPSReply.ProtoReflect.Descriptor instead.
func (*UpgradeVPSReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{41}
}

func (x *UpgradeVPSReply) GetUpgradeSucceed() bool {
	if x != nil {
		return x.UpgradeSucceed
	}
	return false
}

// VPS升级回调接口
type PostUpgradeVPSStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	TaskId         string `protobuf:"bytes,2,opt,name=taskId,json=taskId,proto3" json:"taskId"`
	UpgradeSucceed bool   `protobuf:"varint,3,opt,name=upgradeSucceed,json=upgradeSucceed,proto3" json:"upgradeSucceed"`
}

func (x *PostUpgradeVPSStatusRequest) Reset() {
	*x = PostUpgradeVPSStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUpgradeVPSStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUpgradeVPSStatusRequest) ProtoMessage() {}

func (x *PostUpgradeVPSStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUpgradeVPSStatusRequest.ProtoReflect.Descriptor instead.
func (*PostUpgradeVPSStatusRequest) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{42}
}

func (x *PostUpgradeVPSStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PostUpgradeVPSStatusRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PostUpgradeVPSStatusRequest) GetUpgradeSucceed() bool {
	if x != nil {
		return x.UpgradeSucceed
	}
	return false
}

// VPS升级回调接口返回
type PostUpgradeVPSStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdateSucceed bool `protobuf:"varint,1,opt,name=updateSucceed,json=updateSucceed,proto3" json:"updateSucceed"`
}

func (x *PostUpgradeVPSStatusReply) Reset() {
	*x = PostUpgradeVPSStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostUpgradeVPSStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostUpgradeVPSStatusReply) ProtoMessage() {}

func (x *PostUpgradeVPSStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostUpgradeVPSStatusReply.ProtoReflect.Descriptor instead.
func (*PostUpgradeVPSStatusReply) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{43}
}

func (x *PostUpgradeVPSStatusReply) GetUpdateSucceed() bool {
	if x != nil {
		return x.UpdateSucceed
	}
	return false
}

// ----------------------------------MQ消息模型----------------------------------//
// 用户出入金(账户余额)
type TraderDepositWithdrawMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database string                                              `protobuf:"bytes,1,opt,name=database,json=database,proto3" json:"database"`
	Table    string                                              `protobuf:"bytes,2,opt,name=table,json=table,proto3" json:"table"`
	Type     string                                              `protobuf:"bytes,3,opt,name=type,json=type,proto3" json:"type"`
	Data     *TraderDepositWithdrawMsg_TraderDepositWithdrawData `protobuf:"bytes,4,opt,name=data,json=data,proto3" json:"data"`
	Old      *TraderDepositWithdrawMsg_TraderDepositWithdrawData `protobuf:"bytes,5,opt,name=old,json=old,proto3" json:"old"`
}

func (x *TraderDepositWithdrawMsg) Reset() {
	*x = TraderDepositWithdrawMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderDepositWithdrawMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderDepositWithdrawMsg) ProtoMessage() {}

func (x *TraderDepositWithdrawMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderDepositWithdrawMsg.ProtoReflect.Descriptor instead.
func (*TraderDepositWithdrawMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{44}
}

func (x *TraderDepositWithdrawMsg) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *TraderDepositWithdrawMsg) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *TraderDepositWithdrawMsg) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TraderDepositWithdrawMsg) GetData() *TraderDepositWithdrawMsg_TraderDepositWithdrawData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TraderDepositWithdrawMsg) GetOld() *TraderDepositWithdrawMsg_TraderDepositWithdrawData {
	if x != nil {
		return x.Old
	}
	return nil
}

// 账户映射（实盘绑定）
type TraderAccountMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database string                              `protobuf:"bytes,1,opt,name=database,json=database,proto3" json:"database"`
	Table    string                              `protobuf:"bytes,2,opt,name=table,json=table,proto3" json:"table"`
	Type     string                              `protobuf:"bytes,3,opt,name=type,json=type,proto3" json:"type"`
	Data     *TraderAccountMsg_TraderAccountData `protobuf:"bytes,4,opt,name=data,json=data,proto3" json:"data"`
	Old      *TraderAccountMsg_TraderAccountData `protobuf:"bytes,5,opt,name=old,json=old,proto3" json:"old"`
}

func (x *TraderAccountMsg) Reset() {
	*x = TraderAccountMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderAccountMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderAccountMsg) ProtoMessage() {}

func (x *TraderAccountMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderAccountMsg.ProtoReflect.Descriptor instead.
func (*TraderAccountMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{45}
}

func (x *TraderAccountMsg) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *TraderAccountMsg) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *TraderAccountMsg) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TraderAccountMsg) GetData() *TraderAccountMsg_TraderAccountData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TraderAccountMsg) GetOld() *TraderAccountMsg_TraderAccountData {
	if x != nil {
		return x.Old
	}
	return nil
}

// 订单（交易记录）
type TraderOrdersMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database string                            `protobuf:"bytes,1,opt,name=database,json=database,proto3" json:"database"`
	Table    string                            `protobuf:"bytes,2,opt,name=table,json=table,proto3" json:"table"`
	Type     string                            `protobuf:"bytes,3,opt,name=type,json=type,proto3" json:"type"`
	Data     *TraderOrdersMsg_TraderOrdersData `protobuf:"bytes,4,opt,name=data,json=data,proto3" json:"data"`
	Old      *TraderOrdersMsg_TraderOrdersData `protobuf:"bytes,5,opt,name=old,json=old,proto3" json:"old"`
}

func (x *TraderOrdersMsg) Reset() {
	*x = TraderOrdersMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderOrdersMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderOrdersMsg) ProtoMessage() {}

func (x *TraderOrdersMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderOrdersMsg.ProtoReflect.Descriptor instead.
func (*TraderOrdersMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{46}
}

func (x *TraderOrdersMsg) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *TraderOrdersMsg) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *TraderOrdersMsg) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TraderOrdersMsg) GetData() *TraderOrdersMsg_TraderOrdersData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TraderOrdersMsg) GetOld() *TraderOrdersMsg_TraderOrdersData {
	if x != nil {
		return x.Old
	}
	return nil
}

// 用户变更（用户注册）
type UserMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database string            `protobuf:"bytes,1,opt,name=database,json=database,proto3" json:"database"`
	Table    string            `protobuf:"bytes,2,opt,name=table,json=table,proto3" json:"table"`
	Type     string            `protobuf:"bytes,3,opt,name=type,json=type,proto3" json:"type"`
	Data     *UserMsg_UserData `protobuf:"bytes,4,opt,name=data,json=data,proto3" json:"data"`
	Old      *UserMsg_UserData `protobuf:"bytes,5,opt,name=old,json=old,proto3" json:"old"`
}

func (x *UserMsg) Reset() {
	*x = UserMsg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserMsg) ProtoMessage() {}

func (x *UserMsg) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserMsg.ProtoReflect.Descriptor instead.
func (*UserMsg) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{47}
}

func (x *UserMsg) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *UserMsg) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *UserMsg) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserMsg) GetData() *UserMsg_UserData {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UserMsg) GetOld() *UserMsg_UserData {
	if x != nil {
		return x.Old
	}
	return nil
}

type GetShareLinkDataReply_ShareLinkData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShareUrl   string   `protobuf:"bytes,1,opt,name=shareUrl,json=shareUrl,proto3" json:"shareUrl"`
	Title      string   `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	ContentCol []string `protobuf:"bytes,3,rep,name=contentCol,json=contentCol,proto3" json:"contentCol"`
	ImgUrl     string   `protobuf:"bytes,4,opt,name=imgUrl,json=imgUrl,proto3" json:"imgUrl"`
}

func (x *GetShareLinkDataReply_ShareLinkData) Reset() {
	*x = GetShareLinkDataReply_ShareLinkData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetShareLinkDataReply_ShareLinkData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetShareLinkDataReply_ShareLinkData) ProtoMessage() {}

func (x *GetShareLinkDataReply_ShareLinkData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetShareLinkDataReply_ShareLinkData.ProtoReflect.Descriptor instead.
func (*GetShareLinkDataReply_ShareLinkData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetShareLinkDataReply_ShareLinkData) GetShareUrl() string {
	if x != nil {
		return x.ShareUrl
	}
	return ""
}

func (x *GetShareLinkDataReply_ShareLinkData) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetShareLinkDataReply_ShareLinkData) GetContentCol() []string {
	if x != nil {
		return x.ContentCol
	}
	return nil
}

func (x *GetShareLinkDataReply_ShareLinkData) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

type GetInvitedRecordDataReply_InvitedRecordData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserData      *UserData `protobuf:"bytes,1,opt,name=userData,json=userData,proto3" json:"userData"`
	BindingStatus int32     `protobuf:"varint,2,opt,name=bindingStatus,json=bindingStatus,proto3" json:"bindingStatus"`
	UpdateTime    string    `protobuf:"bytes,3,opt,name=updateTime,json=updateTimeupdateTime,proto3" json:"updateTime"`
}

func (x *GetInvitedRecordDataReply_InvitedRecordData) Reset() {
	*x = GetInvitedRecordDataReply_InvitedRecordData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetInvitedRecordDataReply_InvitedRecordData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetInvitedRecordDataReply_InvitedRecordData) ProtoMessage() {}

func (x *GetInvitedRecordDataReply_InvitedRecordData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetInvitedRecordDataReply_InvitedRecordData.ProtoReflect.Descriptor instead.
func (*GetInvitedRecordDataReply_InvitedRecordData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{13, 0}
}

func (x *GetInvitedRecordDataReply_InvitedRecordData) GetUserData() *UserData {
	if x != nil {
		return x.UserData
	}
	return nil
}

func (x *GetInvitedRecordDataReply_InvitedRecordData) GetBindingStatus() int32 {
	if x != nil {
		return x.BindingStatus
	}
	return 0
}

func (x *GetInvitedRecordDataReply_InvitedRecordData) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

type QuizInfo_Option struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Option  string `protobuf:"bytes,1,opt,name=option,json=option,proto3" json:"option"`
	Content string `protobuf:"bytes,2,opt,name=content,json=content,proto3" json:"content"`
}

func (x *QuizInfo_Option) Reset() {
	*x = QuizInfo_Option{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuizInfo_Option) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuizInfo_Option) ProtoMessage() {}

func (x *QuizInfo_Option) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuizInfo_Option.ProtoReflect.Descriptor instead.
func (*QuizInfo_Option) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{16, 0}
}

func (x *QuizInfo_Option) GetOption() string {
	if x != nil {
		return x.Option
	}
	return ""
}

func (x *QuizInfo_Option) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type SubmitQuizRequest_UserAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionId      string   `protobuf:"bytes,1,opt,name=questionId,json=questionId,proto3" json:"questionId"`
	SelectedOptions []string `protobuf:"bytes,2,rep,name=selectedOptions,json=selectedOptions,proto3" json:"selectedOptions"`
}

func (x *SubmitQuizRequest_UserAnswer) Reset() {
	*x = SubmitQuizRequest_UserAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitQuizRequest_UserAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitQuizRequest_UserAnswer) ProtoMessage() {}

func (x *SubmitQuizRequest_UserAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitQuizRequest_UserAnswer.ProtoReflect.Descriptor instead.
func (*SubmitQuizRequest_UserAnswer) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{18, 0}
}

func (x *SubmitQuizRequest_UserAnswer) GetQuestionId() string {
	if x != nil {
		return x.QuestionId
	}
	return ""
}

func (x *SubmitQuizRequest_UserAnswer) GetSelectedOptions() []string {
	if x != nil {
		return x.SelectedOptions
	}
	return nil
}

type GetQuizRecordReply_QuizResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionId      string   `protobuf:"bytes,1,opt,name=questionId,json=questionId,proto3" json:"questionId"`
	SelectedOptions []string `protobuf:"bytes,2,rep,name=selectedOptions,json=selectedOptions,proto3" json:"selectedOptions"`
	IsCorrect       bool     `protobuf:"varint,3,opt,name=isCorrect,json=isCorrect,proto3" json:"isCorrect"`
}

func (x *GetQuizRecordReply_QuizResult) Reset() {
	*x = GetQuizRecordReply_QuizResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetQuizRecordReply_QuizResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQuizRecordReply_QuizResult) ProtoMessage() {}

func (x *GetQuizRecordReply_QuizResult) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQuizRecordReply_QuizResult.ProtoReflect.Descriptor instead.
func (*GetQuizRecordReply_QuizResult) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{20, 0}
}

func (x *GetQuizRecordReply_QuizResult) GetQuestionId() string {
	if x != nil {
		return x.QuestionId
	}
	return ""
}

func (x *GetQuizRecordReply_QuizResult) GetSelectedOptions() []string {
	if x != nil {
		return x.SelectedOptions
	}
	return nil
}

func (x *GetQuizRecordReply_QuizResult) GetIsCorrect() bool {
	if x != nil {
		return x.IsCorrect
	}
	return false
}

type GetUserDivisionRewardLevelInfoReply_RewardLevel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Threshold     int32  `protobuf:"varint,2,opt,name=threshold,json=threshold,proto3" json:"threshold"`
	Level         int32  `protobuf:"varint,3,opt,name=level,json=level,proto3" json:"level"`
	Reward        string `protobuf:"bytes,4,opt,name=reward,json=reward,proto3" json:"reward"`
	RewardDetails string `protobuf:"bytes,5,opt,name=rewardDetails,json=rewardDetails,proto3" json:"rewardDetails"`
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) Reset() {
	*x = GetUserDivisionRewardLevelInfoReply_RewardLevel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionRewardLevelInfoReply_RewardLevel) ProtoMessage() {}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionRewardLevelInfoReply_RewardLevel.ProtoReflect.Descriptor instead.
func (*GetUserDivisionRewardLevelInfoReply_RewardLevel) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{25, 0}
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) GetReward() string {
	if x != nil {
		return x.Reward
	}
	return ""
}

func (x *GetUserDivisionRewardLevelInfoReply_RewardLevel) GetRewardDetails() string {
	if x != nil {
		return x.RewardDetails
	}
	return ""
}

type GetUserDivisionInviterStatisticsInfoReply_InviterStatistics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo     *UserData `protobuf:"bytes,1,opt,name=userInfo,json=userInfo,proto3" json:"userInfo"`
	InvitedCount int32     `protobuf:"varint,2,opt,name=invitedCount,json=invitedCount,proto3" json:"invitedCount"`
}

func (x *GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) Reset() {
	*x = GetUserDivisionInviterStatisticsInfoReply_InviterStatistics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) ProtoMessage() {}

func (x *GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviterStatisticsInfoReply_InviterStatistics.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{33, 0}
}

func (x *GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) GetUserInfo() *UserData {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetUserDivisionInviterStatisticsInfoReply_InviterStatistics) GetInvitedCount() int32 {
	if x != nil {
		return x.InvitedCount
	}
	return 0
}

type GetUserDivisionInviteeInfoReply_InviteeExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InviteeIp         string `protobuf:"bytes,1,opt,name=inviteeIp,json=inviteeIp,proto3" json:"inviteeIp"`
	InviteeRegisterIp string `protobuf:"bytes,2,opt,name=inviteeRegisterIp,json=inviteeRegisterIp,proto3" json:"inviteeRegisterIp"`
	InviteeDeviceId   string `protobuf:"bytes,3,opt,name=inviteeDeviceId,json=inviteeDeviceId,proto3" json:"inviteeDeviceId"`
	BindingStatus     int32  `protobuf:"varint,4,opt,name=bindingStatus,json=bindingStatus,proto3" json:"bindingStatus"`
}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) Reset() {
	*x = GetUserDivisionInviteeInfoReply_InviteeExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviteeInfoReply_InviteeExtraInfo) ProtoMessage() {}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviteeInfoReply_InviteeExtraInfo.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviteeInfoReply_InviteeExtraInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{35, 0}
}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) GetInviteeIp() string {
	if x != nil {
		return x.InviteeIp
	}
	return ""
}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) GetInviteeRegisterIp() string {
	if x != nil {
		return x.InviteeRegisterIp
	}
	return ""
}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) GetInviteeDeviceId() string {
	if x != nil {
		return x.InviteeDeviceId
	}
	return ""
}

func (x *GetUserDivisionInviteeInfoReply_InviteeExtraInfo) GetBindingStatus() int32 {
	if x != nil {
		return x.BindingStatus
	}
	return 0
}

type GetUserDivisionInviteeInfoReply_InviterInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo         *UserData                                         `protobuf:"bytes,1,opt,name=userInfo,json=userInfo,proto3" json:"userInfo"`
	InviteeExtraInfo *GetUserDivisionInviteeInfoReply_InviteeExtraInfo `protobuf:"bytes,2,opt,name=inviteeExtraInfo,json=inviteeExtraInfo,proto3" json:"inviteeExtraInfo"`
}

func (x *GetUserDivisionInviteeInfoReply_InviterInfo) Reset() {
	*x = GetUserDivisionInviteeInfoReply_InviterInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionInviteeInfoReply_InviterInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionInviteeInfoReply_InviterInfo) ProtoMessage() {}

func (x *GetUserDivisionInviteeInfoReply_InviterInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionInviteeInfoReply_InviterInfo.ProtoReflect.Descriptor instead.
func (*GetUserDivisionInviteeInfoReply_InviterInfo) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{35, 1}
}

func (x *GetUserDivisionInviteeInfoReply_InviterInfo) GetUserInfo() *UserData {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetUserDivisionInviteeInfoReply_InviterInfo) GetInviteeExtraInfo() *GetUserDivisionInviteeInfoReply_InviteeExtraInfo {
	if x != nil {
		return x.InviteeExtraInfo
	}
	return nil
}

type GetUserDivisionActivityListReply_VPSUpgradeRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo  *UserData `protobuf:"bytes,1,opt,name=userInfo,json=keywords,proto3" json:"userInfo"`
	Threshold int32     `protobuf:"varint,2,opt,name=threshold,json=threshold,proto3" json:"threshold"`
	Reward    string    `protobuf:"bytes,3,opt,name=reward,json=reward,proto3" json:"reward"`
	Status    int32     `protobuf:"varint,4,opt,name=status,json=status,proto3" json:"status"`
	AwardTime string    `protobuf:"bytes,5,opt,name=awardTime,json=awardTime,proto3" json:"awardTime"`
	UpdateBy  string    `protobuf:"bytes,6,opt,name=updateBy,json=updateBy,proto3" json:"updateBy"`
	UpdateAt  string    `protobuf:"bytes,7,opt,name=updateAt,json=updateAt,proto3" json:"updateAt"`
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) Reset() {
	*x = GetUserDivisionActivityListReply_VPSUpgradeRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionActivityListReply_VPSUpgradeRecord) ProtoMessage() {}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionActivityListReply_VPSUpgradeRecord.ProtoReflect.Descriptor instead.
func (*GetUserDivisionActivityListReply_VPSUpgradeRecord) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{37, 0}
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetUserInfo() *UserData {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetThreshold() int32 {
	if x != nil {
		return x.Threshold
	}
	return 0
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetReward() string {
	if x != nil {
		return x.Reward
	}
	return ""
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetAwardTime() string {
	if x != nil {
		return x.AwardTime
	}
	return ""
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetUpdateBy() string {
	if x != nil {
		return x.UpdateBy
	}
	return ""
}

func (x *GetUserDivisionActivityListReply_VPSUpgradeRecord) GetUpdateAt() string {
	if x != nil {
		return x.UpdateAt
	}
	return ""
}

type GetUserDivisionActivityInfoReply_AwardTaskItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId        string `protobuf:"bytes,1,opt,name=taskId,json=taskId,proto3" json:"taskId"`
	RecommendUser int64  `protobuf:"varint,2,opt,name=recommendUser,json=recommendUser,proto3" json:"recommendUser"`
	TaskContent   string `protobuf:"bytes,3,opt,name=taskContent,json=taskContent,proto3" json:"taskContent"`
	TaskStatus    int32  `protobuf:"varint,4,opt,name=taskStatus,json=taskStatus,proto3" json:"taskStatus"`
	TaskProgress  string `protobuf:"bytes,5,opt,name=taskProgress,json=taskProgress,proto3" json:"taskProgress"`
	TaskLevel     int32  `protobuf:"varint,6,opt,name=taskLevel,json=taskLevel,proto3" json:"taskLevel"`
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) Reset() {
	*x = GetUserDivisionActivityInfoReply_AwardTaskItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionActivityInfoReply_AwardTaskItem) ProtoMessage() {}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionActivityInfoReply_AwardTaskItem.ProtoReflect.Descriptor instead.
func (*GetUserDivisionActivityInfoReply_AwardTaskItem) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{38, 0}
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) GetRecommendUser() int64 {
	if x != nil {
		return x.RecommendUser
	}
	return 0
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) GetTaskContent() string {
	if x != nil {
		return x.TaskContent
	}
	return ""
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) GetTaskStatus() int32 {
	if x != nil {
		return x.TaskStatus
	}
	return 0
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) GetTaskProgress() string {
	if x != nil {
		return x.TaskProgress
	}
	return ""
}

func (x *GetUserDivisionActivityInfoReply_AwardTaskItem) GetTaskLevel() int32 {
	if x != nil {
		return x.TaskLevel
	}
	return 0
}

type GetUserDivisionActivityInfoReply_AwardData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AwardType     int32                                             `protobuf:"varint,1,opt,name=awardType,json=awardType,proto3" json:"awardType"`
	RecommendUser int64                                             `protobuf:"varint,2,opt,name=recommendUser,json=recommendUser,proto3" json:"recommendUser"`
	UpgradeTimes  int64                                             `protobuf:"varint,3,opt,name=upgradeTimes,json=upgradeTimes,proto3" json:"upgradeTimes"`
	TaskCol       []*GetUserDivisionActivityInfoReply_AwardTaskItem `protobuf:"bytes,4,rep,name=taskCol,json=taskCol,proto3" json:"taskCol"`
}

func (x *GetUserDivisionActivityInfoReply_AwardData) Reset() {
	*x = GetUserDivisionActivityInfoReply_AwardData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserDivisionActivityInfoReply_AwardData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDivisionActivityInfoReply_AwardData) ProtoMessage() {}

func (x *GetUserDivisionActivityInfoReply_AwardData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDivisionActivityInfoReply_AwardData.ProtoReflect.Descriptor instead.
func (*GetUserDivisionActivityInfoReply_AwardData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{38, 1}
}

func (x *GetUserDivisionActivityInfoReply_AwardData) GetAwardType() int32 {
	if x != nil {
		return x.AwardType
	}
	return 0
}

func (x *GetUserDivisionActivityInfoReply_AwardData) GetRecommendUser() int64 {
	if x != nil {
		return x.RecommendUser
	}
	return 0
}

func (x *GetUserDivisionActivityInfoReply_AwardData) GetUpgradeTimes() int64 {
	if x != nil {
		return x.UpgradeTimes
	}
	return 0
}

func (x *GetUserDivisionActivityInfoReply_AwardData) GetTaskCol() []*GetUserDivisionActivityInfoReply_AwardTaskItem {
	if x != nil {
		return x.TaskCol
	}
	return nil
}

type TraderDepositWithdrawMsg_TraderDepositWithdrawData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	MtUserNumber    int64  `protobuf:"varint,2,opt,name=mt_user_number,json=mtUserNumber,proto3" json:"mt_user_number"`
	MtType          string `protobuf:"bytes,3,opt,name=mt_type,json=mtType,proto3" json:"mt_type"`
	Amount          int64  `protobuf:"varint,4,opt,name=amount,json=amount,proto3" json:"amount"`
	Currency        string `protobuf:"bytes,5,opt,name=currency,json=currency,proto3" json:"currency"`
	OperationTime   int64  `protobuf:"varint,6,opt,name=operation_time,json=operationTime,proto3" json:"operation_time"`
	CreateTimestamp int64  `protobuf:"varint,7,opt,name=create_timestamp,json=createTimestamp,proto3" json:"create_timestamp"`
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) Reset() {
	*x = TraderDepositWithdrawMsg_TraderDepositWithdrawData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderDepositWithdrawMsg_TraderDepositWithdrawData) ProtoMessage() {}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderDepositWithdrawMsg_TraderDepositWithdrawData.ProtoReflect.Descriptor instead.
func (*TraderDepositWithdrawMsg_TraderDepositWithdrawData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{44, 0}
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetMtUserNumber() int64 {
	if x != nil {
		return x.MtUserNumber
	}
	return 0
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetMtType() string {
	if x != nil {
		return x.MtType
	}
	return ""
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetOperationTime() int64 {
	if x != nil {
		return x.OperationTime
	}
	return 0
}

func (x *TraderDepositWithdrawMsg_TraderDepositWithdrawData) GetCreateTimestamp() int64 {
	if x != nil {
		return x.CreateTimestamp
	}
	return 0
}

type TraderAccountMsg_TraderAccountData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	UserId          string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	AccountId       string `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	ServiceId       int64  `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3" json:"service_id"`
	MtUserNumber    int64  `protobuf:"varint,5,opt,name=mt_user_number,json=mtUserNumber,proto3" json:"mt_user_number"`
	Password        string `protobuf:"bytes,6,opt,name=password,json=password,proto3" json:"password"`
	Currency        string `protobuf:"bytes,7,opt,name=currency,json=currency,proto3" json:"currency"`
	MtType          string `protobuf:"bytes,8,opt,name=mt_type,json=mtType,proto3" json:"mt_type"`
	RealType        int64  `protobuf:"varint,9,opt,name=real_type,json=realType,proto3" json:"real_type"`
	ReadType        int64  `protobuf:"varint,10,opt,name=read_type,json=readType,proto3" json:"read_type"`
	Leverage        int64  `protobuf:"varint,11,opt,name=leverage,json=leverage,proto3" json:"leverage"`
	IsDelete        int32  `protobuf:"varint,12,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	CreateTimestamp int64  `protobuf:"varint,13,opt,name=create_timestamp,json=createTimestamp,proto3" json:"create_timestamp"`
}

func (x *TraderAccountMsg_TraderAccountData) Reset() {
	*x = TraderAccountMsg_TraderAccountData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderAccountMsg_TraderAccountData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderAccountMsg_TraderAccountData) ProtoMessage() {}

func (x *TraderAccountMsg_TraderAccountData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderAccountMsg_TraderAccountData.ProtoReflect.Descriptor instead.
func (*TraderAccountMsg_TraderAccountData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{45, 0}
}

func (x *TraderAccountMsg_TraderAccountData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *TraderAccountMsg_TraderAccountData) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *TraderAccountMsg_TraderAccountData) GetServiceId() int64 {
	if x != nil {
		return x.ServiceId
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetMtUserNumber() int64 {
	if x != nil {
		return x.MtUserNumber
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *TraderAccountMsg_TraderAccountData) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *TraderAccountMsg_TraderAccountData) GetMtType() string {
	if x != nil {
		return x.MtType
	}
	return ""
}

func (x *TraderAccountMsg_TraderAccountData) GetRealType() int64 {
	if x != nil {
		return x.RealType
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetReadType() int64 {
	if x != nil {
		return x.ReadType
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetLeverage() int64 {
	if x != nil {
		return x.Leverage
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetIsDelete() int32 {
	if x != nil {
		return x.IsDelete
	}
	return 0
}

func (x *TraderAccountMsg_TraderAccountData) GetCreateTimestamp() int64 {
	if x != nil {
		return x.CreateTimestamp
	}
	return 0
}

type TraderOrdersMsg_TraderOrdersData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int32   `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	MtUserNumber    int64   `protobuf:"varint,2,opt,name=mt_user_number,json=mtUserNumber,proto3" json:"mt_user_number"`
	MtType          string  `protobuf:"bytes,3,opt,name=mt_type,json=mtType,proto3" json:"mt_type"`
	Ticket          int64   `protobuf:"varint,4,opt,name=ticket,json=ticket,proto3" json:"ticket"`
	Symbol          string  `protobuf:"bytes,5,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	Operation       int32   `protobuf:"varint,6,opt,name=operation,json=operation,proto3" json:"operation"`
	Slippage        float64 `protobuf:"fixed64,7,opt,name=slippage,json=slippage,proto3" json:"slippage"`
	StopLoss        float64 `protobuf:"fixed64,8,opt,name=stop_loss,json=stopLoss,proto3" json:"stop_loss"`
	TakeProfit      float64 `protobuf:"fixed64,9,opt,name=take_profit,json=takeProfit,proto3" json:"take_profit"`
	Swap            float64 `protobuf:"fixed64,10,opt,name=swap,json=swap,proto3" json:"swap"`
	Volume          float64 `protobuf:"fixed64,11,opt,name=volume,json=volume,proto3" json:"volume"`
	ClosePrice      float64 `protobuf:"fixed64,12,opt,name=close_price,json=closePrice,proto3" json:"close_price"`
	CloseTime       int64   `protobuf:"varint,13,opt,name=close_time,json=closeTime,proto3" json:"close_time"`
	Commission      float64 `protobuf:"fixed64,14,opt,name=commission,json=commission,proto3" json:"commission"`
	Expiration      int64   `protobuf:"varint,15,opt,name=expiration,json=expiration,proto3" json:"expiration"`
	Lots            float64 `protobuf:"fixed64,16,opt,name=lots,json=lots,proto3" json:"lots"`
	MagicNumber     int64   `protobuf:"varint,17,opt,name=magic_number,json=magicNumber,proto3" json:"magic_number"`
	OpenPrice       float64 `protobuf:"fixed64,18,opt,name=open_price,json=openPrice,proto3" json:"open_price"`
	OpenTime        int64   `protobuf:"varint,19,opt,name=open_time,json=openTime,proto3" json:"open_time"`
	Profit          float64 `protobuf:"fixed64,20,opt,name=profit,json=profit,proto3" json:"profit"`
	RateClose       float64 `protobuf:"fixed64,21,opt,name=rate_close,json=rateClose,proto3" json:"rate_close"`
	RateMargin      float64 `protobuf:"fixed64,22,opt,name=rate_margin,json=rateMargin,proto3" json:"rate_margin"`
	RateOpen        float64 `protobuf:"fixed64,23,opt,name=rate_open,json=rateOpen,proto3" json:"rate_open"`
	Fee             float64 `protobuf:"fixed64,24,opt,name=fee,json=fee,proto3" json:"fee"`
	CloseLots       float64 `protobuf:"fixed64,25,opt,name=close_lots,json=closeLots,proto3" json:"close_lots"`
	CloseComment    string  `protobuf:"bytes,26,opt,name=close_comment,json=closeComment,proto3" json:"close_comment"`
	DealType        int32   `protobuf:"varint,27,opt,name=deal_type,json=dealType,proto3" json:"deal_type"`
	CreateTimestamp int64   `protobuf:"varint,28,opt,name=create_timestamp,json=createTimestamp,proto3" json:"create_timestamp"`
}

func (x *TraderOrdersMsg_TraderOrdersData) Reset() {
	*x = TraderOrdersMsg_TraderOrdersData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderOrdersMsg_TraderOrdersData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderOrdersMsg_TraderOrdersData) ProtoMessage() {}

func (x *TraderOrdersMsg_TraderOrdersData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderOrdersMsg_TraderOrdersData.ProtoReflect.Descriptor instead.
func (*TraderOrdersMsg_TraderOrdersData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{46, 0}
}

func (x *TraderOrdersMsg_TraderOrdersData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetMtUserNumber() int64 {
	if x != nil {
		return x.MtUserNumber
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetMtType() string {
	if x != nil {
		return x.MtType
	}
	return ""
}

func (x *TraderOrdersMsg_TraderOrdersData) GetTicket() int64 {
	if x != nil {
		return x.Ticket
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TraderOrdersMsg_TraderOrdersData) GetOperation() int32 {
	if x != nil {
		return x.Operation
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetSlippage() float64 {
	if x != nil {
		return x.Slippage
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetStopLoss() float64 {
	if x != nil {
		return x.StopLoss
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetTakeProfit() float64 {
	if x != nil {
		return x.TakeProfit
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetSwap() float64 {
	if x != nil {
		return x.Swap
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetVolume() float64 {
	if x != nil {
		return x.Volume
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetClosePrice() float64 {
	if x != nil {
		return x.ClosePrice
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetCloseTime() int64 {
	if x != nil {
		return x.CloseTime
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetCommission() float64 {
	if x != nil {
		return x.Commission
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetExpiration() int64 {
	if x != nil {
		return x.Expiration
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetLots() float64 {
	if x != nil {
		return x.Lots
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetMagicNumber() int64 {
	if x != nil {
		return x.MagicNumber
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetOpenPrice() float64 {
	if x != nil {
		return x.OpenPrice
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetOpenTime() int64 {
	if x != nil {
		return x.OpenTime
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetProfit() float64 {
	if x != nil {
		return x.Profit
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetRateClose() float64 {
	if x != nil {
		return x.RateClose
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetRateMargin() float64 {
	if x != nil {
		return x.RateMargin
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetRateOpen() float64 {
	if x != nil {
		return x.RateOpen
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetFee() float64 {
	if x != nil {
		return x.Fee
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetCloseLots() float64 {
	if x != nil {
		return x.CloseLots
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetCloseComment() string {
	if x != nil {
		return x.CloseComment
	}
	return ""
}

func (x *TraderOrdersMsg_TraderOrdersData) GetDealType() int32 {
	if x != nil {
		return x.DealType
	}
	return 0
}

func (x *TraderOrdersMsg_TraderOrdersData) GetCreateTimestamp() int64 {
	if x != nil {
		return x.CreateTimestamp
	}
	return 0
}

type UserMsg_UserData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId           string `protobuf:"bytes,1,opt,name=UserId,json=UserId,proto3" json:"UserId"`
	RegistrationIp   string `protobuf:"bytes,2,opt,name=RegistrationIp,json=RegistrationIp,proto3" json:"RegistrationIp"`
	RegistrationTime string `protobuf:"bytes,3,opt,name=RegistrationTime,json=RegistrationTime,proto3" json:"RegistrationTime"`
	DeviceCode       string `protobuf:"bytes,4,opt,name=DeviceCode,json=DeviceCode,proto3" json:"DeviceCode"`
}

func (x *UserMsg_UserData) Reset() {
	*x = UserMsg_UserData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_growth_center_v1_models_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserMsg_UserData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserMsg_UserData) ProtoMessage() {}

func (x *UserMsg_UserData) ProtoReflect() protoreflect.Message {
	mi := &file_user_growth_center_v1_models_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserMsg_UserData.ProtoReflect.Descriptor instead.
func (*UserMsg_UserData) Descriptor() ([]byte, []int) {
	return file_user_growth_center_v1_models_proto_rawDescGZIP(), []int{47, 0}
}

func (x *UserMsg_UserData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserMsg_UserData) GetRegistrationIp() string {
	if x != nil {
		return x.RegistrationIp
	}
	return ""
}

func (x *UserMsg_UserData) GetRegistrationTime() string {
	if x != nil {
		return x.RegistrationTime
	}
	return ""
}

func (x *UserMsg_UserData) GetDeviceCode() string {
	if x != nil {
		return x.DeviceCode
	}
	return ""
}

var File_user_growth_center_v1_models_proto protoreflect.FileDescriptor

var file_user_growth_center_v1_models_proto_rawDesc = []byte{
	0x0a, 0x22, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x2b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x11, 0x0a, 0x0f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x14, 0x0a, 0x12,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x70, 0x75, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xd0,
	0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x50, 0x6f, 0x70, 0x75, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x52, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15,
	0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe5, 0xad, 0x97, 0xe7, 0xac, 0xa6, 0xe4, 0xb8, 0xb2, 0xe6,
	0x95, 0xb0, 0xe7, 0xbb, 0x84, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6c, 0x22, 0x49, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb2, 0x03, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x52, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe5, 0xad, 0x97, 0xe7, 0xac, 0xa6, 0xe4, 0xb8, 0xb2,
	0xe6, 0x95, 0xb0, 0xe7, 0xbb, 0x84, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x6c, 0x12, 0x41, 0x0a, 0x0f, 0x76, 0x70, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x49,
	0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0x76, 0x70, 0x73, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe9, 0xaa, 0x8c, 0xe8, 0xaf,
	0x81, 0x55, 0x72, 0x6c, 0x52, 0x0f, 0x76, 0x70, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x49,
	0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x4f, 0x0a, 0x10, 0x76, 0x70, 0x73, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0x76, 0x70, 0x73, 0xe9, 0xaa, 0x8c, 0xe8, 0xaf, 0x81, 0xe6,
	0x8c, 0x89, 0xe9, 0x92, 0xae, 0xe5, 0xa4, 0x9a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe5, 0x86,
	0x85, 0xe5, 0xae, 0xb9, 0x52, 0x10, 0x76, 0x70, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x10, 0x76, 0x70, 0x73, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0x76, 0x70, 0x73, 0xe9, 0xaa, 0x8c, 0xe8, 0xaf, 0x81,
	0xe6, 0x8c, 0x89, 0xe9, 0x92, 0xae, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe8, 0x89, 0xb2, 0x52,
	0x10, 0x76, 0x70, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x22, 0x40, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x22, 0xbb, 0x03, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x52, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x7d, 0x0a, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0x88, 0x86, 0xe4, 0xba, 0xab, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0x52, 0x0d, 0x73, 0x68, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0xce, 0x01, 0x0a, 0x0d, 0x53, 0x68, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x30, 0x0a, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x88, 0x86, 0xe4, 0xba,
	0xab, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x75, 0x72, 0x6c, 0x52, 0x08, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x55, 0x72, 0x6c, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe5, 0xad, 0x97, 0xe7, 0xac, 0xa6, 0xe4, 0xb8,
	0xb2, 0xe6, 0x95, 0xb0, 0xe7, 0xbb, 0x84, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x43, 0x6f, 0x6c, 0x12, 0x2c, 0x0a, 0x06, 0x69, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab,
	0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x75, 0x72, 0x6c, 0x52, 0x06, 0x69, 0x6d, 0x67, 0x55, 0x72,
	0x6c, 0x22, 0x77, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x29, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2d, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xa1, 0x04, 0x0a, 0x19, 0x47,
	0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14,
	0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe9, 0x82, 0x80, 0xe8, 0xaf, 0xb7, 0xe6, 0x80, 0xbb, 0xe4, 0xba,
	0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x75, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x99, 0x01, 0x0a, 0x14,
	0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x43, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe9, 0x82, 0x80, 0xe8, 0xaf, 0xb7, 0xe8,
	0xae, 0xb0, 0xe5, 0xbd, 0x95, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x9b, 0x86, 0xe5, 0x90,
	0x88, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x43, 0x6f, 0x6c, 0x1a, 0xf4, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x52, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x4e, 0x0a, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x28, 0x92, 0x41, 0x25, 0x2a, 0x23, 0xe7,
	0xbb, 0x91, 0xe5, 0xae, 0x9a, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x20, 0x30, 0x20, 0xe6, 0x9c,
	0xaa, 0xe7, 0xbb, 0x91, 0xe5, 0xae, 0x9a, 0x20, 0x31, 0xe5, 0xb7, 0xb2, 0xe7, 0xbb, 0x91, 0xe5,
	0xae, 0x9a, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3b, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6,
	0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x14, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x38,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x56, 0x70, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x24, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0x76, 0x70, 0x73, 0xe7, 0xad, 0x89, 0xe7, 0xba,
	0xa7, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0xba, 0x01, 0x0a, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0d,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x0d, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x98,
	0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x06, 0x77, 0x69, 0x6b, 0x69, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0x69, 0x64, 0x52, 0x06, 0x77,
	0x69, 0x6b, 0x69, 0x49, 0x64, 0x22, 0x81, 0x03, 0x0a, 0x08, 0x51, 0x75, 0x69, 0x7a, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe9, 0xa2, 0x98,
	0xe7, 0x9b, 0xae, 0x69, 0x64, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x5e, 0x0a, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0xa2, 0x98, 0xe7, 0x9b, 0xae, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0xa2, 0x98, 0xe7, 0x9b, 0xae, 0xe5,
	0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x57,
	0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x69, 0x7a,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe9, 0x80, 0x89, 0xe9, 0xa1, 0xb9, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x60, 0x0a, 0x06, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x29, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x80, 0x89, 0xe9, 0xa1, 0xb9, 0xe6, 0xa0,
	0x87, 0xe8, 0xaf, 0x86, 0x52, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x80, 0x89, 0xe9, 0xa1, 0xb9, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9,
	0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x66, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x51, 0x75, 0x69, 0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x52, 0x0a,
	0x08, 0x71, 0x75, 0x69, 0x7a, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x69, 0x7a,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0x95, 0xe9, 0xa2,
	0x98, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x08, 0x71, 0x75, 0x69, 0x7a, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0xf9, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x51, 0x75, 0x69, 0x7a,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6a, 0x0a, 0x07, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x51, 0x75, 0x69, 0x7a, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x80,
	0x89, 0xe6, 0x8b, 0xa9, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x73, 0x1a, 0x78, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x41, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x12, 0x2d, 0x0a, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe9, 0xa2, 0x98, 0xe7,
	0x9b, 0xae, 0x69, 0x64, 0x52, 0x0a, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x80, 0x89, 0xe9, 0xa1, 0xb9, 0x52, 0x0f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8b, 0x01,
	0x0a, 0x0f, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x51, 0x75, 0x69, 0x7a, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x29, 0x0a, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0x80,
	0x9a, 0xe8, 0xbf, 0x87, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x12, 0x4d, 0x0a, 0x14,
	0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a,
	0x14, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0xe9, 0x80, 0x89, 0xe9, 0xa1, 0xb9, 0x69, 0x64, 0xe9,
	0x9b, 0x86, 0xe5, 0x90, 0x88, 0x52, 0x14, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0xc2, 0x02, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x51, 0x75, 0x69, 0x7a, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x25, 0x0a, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x82, 0xe7, 0xba, 0xa7, 0x49,
	0x44, 0x52, 0x06, 0x69, 0x73, 0x50, 0x61, 0x73, 0x73, 0x12, 0x61, 0x0a, 0x07, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x75, 0x69, 0x7a, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x51, 0x75, 0x69, 0x7a, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x82, 0xe7, 0xba,
	0xa7, 0x49, 0x44, 0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x1a, 0xa1, 0x01, 0x0a,
	0x0a, 0x51, 0x75, 0x69, 0x7a, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x0a, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x82, 0xe7, 0xba, 0xa7, 0x49, 0x44, 0x52, 0x0a,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x82, 0xe7, 0xba, 0xa7,
	0x49, 0x44, 0x52, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb1, 0x82,
	0xe7, 0xba, 0xa7, 0x49, 0x44, 0x52, 0x09, 0x69, 0x73, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74,
	0x22, 0x8d, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe5, 0xbc, 0x80, 0xe5,
	0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0d, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74,
	0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x22, 0x92, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x73, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x13, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x73, 0x0a, 0x13, 0x49, 0x6e,
	0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb4,
	0xbb, 0xe5, 0x8a, 0xa8, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x13, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x95, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x73, 0x0a, 0x13, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x52, 0x13, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xa0, 0x03, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x8b, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43,
	0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xa5, 0x96, 0xe5, 0x8a,
	0xb1, 0xe5, 0xb1, 0x82, 0xe7, 0xba, 0xa7, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x0e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f, 0x6c, 0x1a, 0xea, 0x01,
	0x0a, 0x0b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02,
	0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe6, 0xa2, 0xaf, 0xe5, 0xba, 0xa6, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0xe9, 0x98, 0x88, 0xe5,
	0x80, 0xbc, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x21, 0x0a,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x3d, 0x0a, 0x0d, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8,
	0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0d, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x80, 0x01, 0x0a, 0x24, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xa2, 0xaf,
	0xe5, 0xba, 0xa6, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0xe9, 0x98, 0x88, 0xe5, 0x80, 0xbc, 0x52,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x21, 0x0a, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x3d, 0x0a,
	0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x99, 0x01, 0x0a,
	0x24, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35,
	0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xa2, 0xaf, 0xe5, 0xba, 0xa6, 0xe4, 0xba,
	0xba, 0xe6, 0x95, 0xb0, 0xe9, 0x98, 0x88, 0xe5, 0x80, 0xbc, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x21, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xad, 0x89, 0xe7, 0xba,
	0xa7, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x3f, 0x0a, 0x24, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41,
	0x04, 0x2a, 0x02, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0xc7, 0x01, 0x0a, 0x23, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2e, 0x0a, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe5, 0x8f, 0x97, 0xe9, 0x82,
	0x80, 0xe4, 0xba, 0xba, 0x69, 0x64, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a,
	0x11, 0xe5, 0x8f, 0x97, 0xe9, 0x82, 0x80, 0xe4, 0xba, 0xba, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87,
	0x69, 0x64, 0x52, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe9, 0x82, 0x80,
	0xe8, 0xaf, 0xb7, 0xe4, 0xba, 0xba, 0x69, 0x64, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x23, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe9, 0x82, 0x80, 0xe8, 0xaf,
	0xb7, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0xb0, 0x01,
	0x0a, 0x2b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2a, 0x0a,
	0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x85, 0xb3, 0xe9, 0x94, 0xae, 0xe8, 0xaf, 0x8d, 0x52,
	0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x29, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0xa1, 0xb5,
	0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0xf5, 0x02, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0xa6,
	0x01, 0x0a, 0x14, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe9, 0x82, 0x80, 0xe8,
	0xaf, 0xb7, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x9b, 0x86, 0xe5, 0x90,
	0x88, 0x52, 0x14, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x73, 0x43, 0x6f, 0x6c, 0x1a, 0x9e, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x52, 0x0a,
	0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x35, 0x0a, 0x0c, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x82,
	0x80, 0xe8, 0xaf, 0xb7, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x69, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xe3, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e,
	0x0a, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe9, 0x82, 0x80, 0xe8, 0xaf, 0xb7, 0xe4, 0xba,
	0xba, 0x69, 0x64, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37,
	0x0a, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0x9e, 0xe7,
	0x9b, 0x98, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4,
	0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xba,
	0x05, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x8a, 0x01, 0x0a, 0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0x8f, 0x97, 0xe9, 0x82, 0x80,
	0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x9b, 0x86, 0xe5, 0x90, 0x88, 0x52,
	0x0e, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x6f, 0x6c, 0x1a,
	0x8c, 0x02, 0x0a, 0x10, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37, 0x0a, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x49,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe6, 0x89,
	0x93, 0xe5, 0xbc, 0x80, 0xe9, 0x82, 0x80, 0xe8, 0xaf, 0xb7, 0xe6, 0x97, 0xb6, 0xe7, 0x9a, 0x84,
	0x69, 0x70, 0x52, 0x09, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x49, 0x70, 0x12, 0x44, 0x0a,
	0x11, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x11, 0xe6,
	0x96, 0xb0, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0xb3, 0xa8, 0xe5, 0x86, 0x8c, 0x69, 0x70,
	0x52, 0x11, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x70, 0x12, 0x40, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x16, 0x92, 0x41,
	0x13, 0x2a, 0x11, 0xe6, 0x96, 0xb0, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0xae, 0xbe, 0xe5,
	0xa4, 0x87, 0x69, 0x64, 0x52, 0x0f, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x65, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0x9e, 0xe7, 0x9b, 0x98, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52,
	0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x1a, 0xfa,
	0x01, 0x0a, 0x0b, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x55,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77,
	0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x8f, 0x97, 0xe9,
	0x82, 0x80, 0xe4, 0xba, 0xba, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x93, 0x01, 0x0a, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74, 0x65,
	0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x4b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77,
	0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x76, 0x69,
	0x74, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x49, 0x6e, 0x76,
	0x69, 0x74, 0x65, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x1a, 0x92,
	0x41, 0x17, 0x2a, 0x15, 0xe5, 0x8f, 0x97, 0xe9, 0x82, 0x80, 0xe4, 0xba, 0xba, 0xe9, 0xa2, 0x9d,
	0xe5, 0xa4, 0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x10, 0x69, 0x6e, 0x76, 0x69, 0x74,
	0x65, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xfc, 0x01, 0x0a, 0x22,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x85, 0xb3, 0xe9, 0x94,
	0xae, 0xe8, 0xaf, 0x8d, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x53,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3b,
	0x92, 0x41, 0x38, 0x2a, 0x36, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x20, 0x30, 0x3d, 0xe6, 0x9c,
	0xaa, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x20, 0x31, 0x3d, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86,
	0xe5, 0x8f, 0x96, 0x20, 0x32, 0x3d, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe4, 0xb8, 0xad, 0x20,
	0x33, 0x3d, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5,
	0xe7, 0xa0, 0x81, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xf7, 0x04, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x9a, 0x01, 0x0a, 0x13, 0x76, 0x70, 0x73, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x56, 0x50, 0x53, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0x76, 0x70, 0x73, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe8, 0xae, 0xb0, 0xe5, 0xbd,
	0x95, 0xe9, 0x9b, 0x86, 0xe5, 0x90, 0x88, 0x52, 0x13, 0x76, 0x70, 0x73, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6c, 0x1a, 0xb5, 0x03, 0x0a,
	0x10, 0x56, 0x50, 0x53, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x52, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x08, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f,
	0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6,
	0x89, 0x80, 0xe9, 0x9c, 0x80, 0xe5, 0xae, 0x9e, 0xe7, 0x9b, 0x98, 0xe4, 0xba, 0xba, 0xe6, 0x95,
	0xb0, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x2f, 0x0a, 0x06,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe9, 0x85, 0x8d, 0xe7, 0xbd, 0xae, 0xe7,
	0x82, 0xb9, 0xe5, 0x87, 0xbb, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x53, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3b, 0x92,
	0x41, 0x38, 0x2a, 0x36, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x20, 0x30, 0x3d, 0xe6, 0x9c, 0xaa,
	0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x20, 0x31, 0x3d, 0xe6, 0x9c, 0xaa, 0xe9, 0xa2, 0x86, 0xe5,
	0x8f, 0x96, 0x20, 0x32, 0x3d, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe4, 0xb8, 0xad, 0x20, 0x33,
	0x3d, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x35, 0x0a, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0x8e, 0xb7, 0xe5,
	0xbe, 0x97, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09,
	0x61, 0x77, 0x61, 0x72, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe4, 0xba, 0xba, 0x52, 0x08, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x42, 0x79, 0x12, 0x2d, 0x0a, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbf,
	0xae, 0xe6, 0x94, 0xb9, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x22, 0xc9, 0x07, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x5f, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x47, 0x92, 0x41, 0x44, 0x2a, 0x42,
	0xe5, 0xae, 0x89, 0xe5, 0x85, 0xa8, 0xe5, 0xa4, 0xa7, 0xe4, 0xbd, 0xbf, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x20, 0x30, 0x3a, 0xe6, 0x9c, 0xaa, 0xe6, 0x88, 0x90, 0xe4, 0xb8, 0xba, 0xe5, 0xae,
	0x89, 0xe5, 0x85, 0xa8, 0xe5, 0xa4, 0xa7, 0xe4, 0xbd, 0xbf, 0x20, 0x31, 0x3a, 0xe5, 0xb7, 0xb2,
	0xe6, 0x88, 0x90, 0xe4, 0xb8, 0xba, 0xe5, 0xae, 0x89, 0xe5, 0x85, 0xa8, 0xe5, 0xa4, 0xa7, 0xe4,
	0xbd, 0xbf, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x76, 0x0a, 0x09, 0x61, 0x77,
	0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x77, 0x61, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa5, 0x96, 0xe5, 0x8a,
	0xb1, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x1a, 0xfe, 0x02, 0x0a, 0x0d, 0x41, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x25, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0d, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe4,
	0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0b, 0x74, 0x61,
	0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x70, 0x0a, 0x0a, 0x74, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x50, 0x92,
	0x41, 0x4d, 0x2a, 0x4b, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0x20, 0x30, 0x20, 0xe6, 0x9c, 0xaa, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x20, 0x31, 0x20, 0xe5,
	0xbe, 0x85, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x20, 0x32, 0x20, 0xe5, 0xae, 0xa1, 0xe6, 0xa0,
	0xb8, 0xe4, 0xb8, 0xad, 0x20, 0x33, 0x20, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0xe5, 0xa4, 0xb1,
	0xe8, 0xb4, 0xa5, 0x20, 0x34, 0x20, 0xe5, 0xb7, 0xb2, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0x52,
	0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x74,
	0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xbf,
	0x9b, 0xe5, 0xba, 0xa6, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x1a, 0xca, 0x02, 0x0a, 0x09, 0x41, 0x77, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x4c, 0x0a, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x2a, 0x29, 0xe5, 0xa5, 0x96, 0xe5, 0x8a,
	0xb1, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0xef, 0xbc, 0x8c, 0x31, 0x3a, 0x56, 0x50, 0x53, 0xe5,
	0x8d, 0x87, 0xe7, 0xba, 0xa7, 0x20, 0x32, 0x3a, 0xe7, 0xad, 0xbe, 0xe7, 0xba, 0xa6, 0xe5, 0xa5,
	0x96, 0xe5, 0x8a, 0xb1, 0x52, 0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x8e, 0xa8,
	0xe8, 0x8d, 0x90, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x0c, 0x75,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0xac,
	0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x12, 0x7c, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6c, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x41, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6c,
	0x22, 0xf1, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x69, 0x76, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3d,
	0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95, 0xe7,
	0xa4, 0xba, 0xe5, 0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0x52, 0x0d,
	0x73, 0x68, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x27, 0x0a,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x85,
	0xa5, 0xe5, 0x8f, 0xa3, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x41, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x26, 0xe5,
	0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x2c, 0xe4, 0xbe, 0x8b, 0xe5,
	0xa6, 0x82, 0x3a, 0xe5, 0xae, 0x89, 0xe5, 0x85, 0xa8, 0xe5, 0xa4, 0xa7, 0xe4, 0xbd, 0xbf, 0xe8,
	0xae, 0xa1, 0xe5, 0x88, 0x92, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x06,
	0x73, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0x92, 0x41,
	0x29, 0x2a, 0x27, 0xe5, 0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0xe5, 0xae, 0xa3, 0xe4, 0xbc, 0xa0, 0xe8,
	0xaf, 0xad, 0x2c, 0xe4, 0xbe, 0x8b, 0xe5, 0xa6, 0x82, 0x3a, 0x57, 0x69, 0x6b, 0x69, 0xe4, 0xb8,
	0x93, 0xe5, 0xb1, 0x9e, 0xe6, 0x8b, 0x9b, 0xe5, 0x8b, 0x9f, 0x52, 0x06, 0x73, 0x6c, 0x6f, 0x67,
	0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0xe5, 0xae,
	0xa3, 0xe4, 0xbc, 0xa0, 0xe8, 0xaf, 0xad, 0xe5, 0xba, 0x95, 0xe8, 0x89, 0xb2, 0x52, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x6b, 0x0a, 0x11, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x56,
	0x50, 0x53, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x22, 0x4c, 0x0a, 0x0f, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x56, 0x50, 0x53, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x0e, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x52,
	0x0e, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x22,
	0xa6, 0x01, 0x0a, 0x1b, 0x50, 0x6f, 0x73, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x56,
	0x50, 0x53, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x39, 0x0a,
	0x0e, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8d, 0x87, 0xe7,
	0xba, 0xa7, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x52, 0x0e, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x53, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x22, 0x5a, 0x0a, 0x19, 0x50, 0x6f, 0x73, 0x74,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x56, 0x50, 0x53, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3d, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x65, 0x64, 0x22, 0x97, 0x04, 0x0a, 0x18, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x4d, 0x73,
	0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61,
	0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x61, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x4d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x5f, 0x0a, 0x03, 0x6f, 0x6c,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x4d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72,
	0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x1a, 0xf0, 0x01, 0x0a, 0x19,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x57, 0x69, 0x74,
	0x68, 0x64, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x74, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x6d, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6d, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x25, 0x0a, 0x0e,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x8e,
	0x05, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x51, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x03,
	0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x1a, 0x8f, 0x03,
	0x0a, 0x11, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x74,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x6d, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c,
	0x65, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c,
	0x65, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x73, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0xb5, 0x08, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x4d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x4d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x4d, 0x0a, 0x03, 0x6f, 0x6c,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x4d, 0x73, 0x67, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x1a, 0xbb, 0x06, 0x0a, 0x10, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24,
	0x0a, 0x0e, 0x6d, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x73,
	0x6c, 0x69, 0x70, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f,
	0x6c, 0x6f, 0x73, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70,
	0x4c, 0x6f, 0x73, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x74, 0x61, 0x6b, 0x65, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x77, 0x61, 0x70, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x04, 0x73, 0x77, 0x61, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x04, 0x6c, 0x6f, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6d, 0x61, 0x67,
	0x69, 0x63, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x6e,
	0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6f, 0x70,
	0x65, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x6e,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x72, 0x61, 0x74, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72,
	0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x08, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x6c, 0x6f, 0x74, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x4c, 0x6f, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x64, 0x65, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xe8, 0x02, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72,
	0x4d, 0x73, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x03, 0x6f, 0x6c,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x73, 0x67, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x6f, 0x6c, 0x64, 0x1a, 0x96, 0x01, 0x0a, 0x08, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26,
	0x0a, 0x0e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x70, 0x12, 0x2a, 0x0a, 0x10, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x2a, 0x47, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x43, 0x48,
	0x4f, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50,
	0x4c, 0x45, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x02, 0x42, 0x1e, 0x5a, 0x1c, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_user_growth_center_v1_models_proto_rawDescOnce sync.Once
	file_user_growth_center_v1_models_proto_rawDescData = file_user_growth_center_v1_models_proto_rawDesc
)

func file_user_growth_center_v1_models_proto_rawDescGZIP() []byte {
	file_user_growth_center_v1_models_proto_rawDescOnce.Do(func() {
		file_user_growth_center_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_growth_center_v1_models_proto_rawDescData)
	})
	return file_user_growth_center_v1_models_proto_rawDescData
}

var file_user_growth_center_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_user_growth_center_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 64)
var file_user_growth_center_v1_models_proto_goTypes = []interface{}{
	(QuestionType)(0),                                                   // 0: api.user_growth_center.v1.QuestionType
	(*GetUserInfoRequest)(nil),                                          // 1: api.user_growth_center.v1.GetUserInfoRequest
	(*GetUserInfoReply)(nil),                                            // 2: api.user_growth_center.v1.GetUserInfoReply
	(*UpdateUserRequest)(nil),                                           // 3: api.user_growth_center.v1.UpdateUserRequest
	(*UpdateUserReply)(nil),                                             // 4: api.user_growth_center.v1.UpdateUserReply
	(*StringReplyRequest)(nil),                                          // 5: api.user_growth_center.v1.StringReplyRequest
	(*StringReplyReply)(nil),                                            // 6: api.user_growth_center.v1.StringReplyReply
	(*GetInvitationPopupDataRequest)(nil),                               // 7: api.user_growth_center.v1.GetInvitationPopupDataRequest
	(*GetInvitationPopupDataReply)(nil),                                 // 8: api.user_growth_center.v1.GetInvitationPopupDataReply
	(*GetInviteRewardBannerDataRequest)(nil),                            // 9: api.user_growth_center.v1.GetInviteRewardBannerDataRequest
	(*GetInviteRewardBannerDataReply)(nil),                              // 10: api.user_growth_center.v1.GetInviteRewardBannerDataReply
	(*GetShareLinkDataRequest)(nil),                                     // 11: api.user_growth_center.v1.GetShareLinkDataRequest
	(*GetShareLinkDataReply)(nil),                                       // 12: api.user_growth_center.v1.GetShareLinkDataReply
	(*GetInvitedRecordDataRequest)(nil),                                 // 13: api.user_growth_center.v1.GetInvitedRecordDataRequest
	(*GetInvitedRecordDataReply)(nil),                                   // 14: api.user_growth_center.v1.GetInvitedRecordDataReply
	(*GetVpsLevelReply)(nil),                                            // 15: api.user_growth_center.v1.GetVpsLevelReply
	(*UserData)(nil),                                                    // 16: api.user_growth_center.v1.UserData
	(*QuizInfo)(nil),                                                    // 17: api.user_growth_center.v1.QuizInfo
	(*GetQuizInfoReply)(nil),                                            // 18: api.user_growth_center.v1.GetQuizInfoReply
	(*SubmitQuizRequest)(nil),                                           // 19: api.user_growth_center.v1.SubmitQuizRequest
	(*SubmitQuizReply)(nil),                                             // 20: api.user_growth_center.v1.SubmitQuizReply
	(*GetQuizRecordReply)(nil),                                          // 21: api.user_growth_center.v1.GetQuizRecordReply
	(*InviterActivityTime)(nil),                                         // 22: api.user_growth_center.v1.InviterActivityTime
	(*GetInviterActivityTimeReply)(nil),                                 // 23: api.user_growth_center.v1.GetInviterActivityTimeReply
	(*UpdateInviterActivityTimeRequest)(nil),                            // 24: api.user_growth_center.v1.UpdateInviterActivityTimeRequest
	(*UpdateInviterActivityTimeReply)(nil),                              // 25: api.user_growth_center.v1.UpdateInviterActivityTimeReply
	(*GetUserDivisionRewardLevelInfoReply)(nil),                         // 26: api.user_growth_center.v1.GetUserDivisionRewardLevelInfoReply
	(*CreateUserDivisionRewardLevelRequest)(nil),                        // 27: api.user_growth_center.v1.CreateUserDivisionRewardLevelRequest
	(*CreateUserDivisionRewardLevelReply)(nil),                          // 28: api.user_growth_center.v1.CreateUserDivisionRewardLevelReply
	(*UpdateUserDivisionRewardLevelRequest)(nil),                        // 29: api.user_growth_center.v1.UpdateUserDivisionRewardLevelRequest
	(*DeleteUserDivisionRewardLevelRequest)(nil),                        // 30: api.user_growth_center.v1.DeleteUserDivisionRewardLevelRequest
	(*CreateUserDivisionInvitationRequest)(nil),                         // 31: api.user_growth_center.v1.CreateUserDivisionInvitationRequest
	(*CreateUserDivisionInvitationReply)(nil),                           // 32: api.user_growth_center.v1.CreateUserDivisionInvitationReply
	(*GetUserDivisionInviterStatisticsInfoRequest)(nil),                 // 33: api.user_growth_center.v1.GetUserDivisionInviterStatisticsInfoRequest
	(*GetUserDivisionInviterStatisticsInfoReply)(nil),                   // 34: api.user_growth_center.v1.GetUserDivisionInviterStatisticsInfoReply
	(*GetUserDivisionInviteeInfoRequest)(nil),                           // 35: api.user_growth_center.v1.GetUserDivisionInviteeInfoRequest
	(*GetUserDivisionInviteeInfoReply)(nil),                             // 36: api.user_growth_center.v1.GetUserDivisionInviteeInfoReply
	(*GetUserDivisionActivityListRequest)(nil),                          // 37: api.user_growth_center.v1.GetUserDivisionActivityListRequest
	(*GetUserDivisionActivityListReply)(nil),                            // 38: api.user_growth_center.v1.GetUserDivisionActivityListReply
	(*GetUserDivisionActivityInfoReply)(nil),                            // 39: api.user_growth_center.v1.GetUserDivisionActivityInfoReply
	(*GetUserDivisionEntryReply)(nil),                                   // 40: api.user_growth_center.v1.GetUserDivisionEntryReply
	(*UpgradeVPSRequest)(nil),                                           // 41: api.user_growth_center.v1.UpgradeVPSRequest
	(*UpgradeVPSReply)(nil),                                             // 42: api.user_growth_center.v1.UpgradeVPSReply
	(*PostUpgradeVPSStatusRequest)(nil),                                 // 43: api.user_growth_center.v1.PostUpgradeVPSStatusRequest
	(*PostUpgradeVPSStatusReply)(nil),                                   // 44: api.user_growth_center.v1.PostUpgradeVPSStatusReply
	(*TraderDepositWithdrawMsg)(nil),                                    // 45: api.user_growth_center.v1.TraderDepositWithdrawMsg
	(*TraderAccountMsg)(nil),                                            // 46: api.user_growth_center.v1.TraderAccountMsg
	(*TraderOrdersMsg)(nil),                                             // 47: api.user_growth_center.v1.TraderOrdersMsg
	(*UserMsg)(nil),                                                     // 48: api.user_growth_center.v1.UserMsg
	(*GetShareLinkDataReply_ShareLinkData)(nil),                         // 49: api.user_growth_center.v1.GetShareLinkDataReply.ShareLinkData
	(*GetInvitedRecordDataReply_InvitedRecordData)(nil),                 // 50: api.user_growth_center.v1.GetInvitedRecordDataReply.InvitedRecordData
	(*QuizInfo_Option)(nil),                                             // 51: api.user_growth_center.v1.QuizInfo.Option
	(*SubmitQuizRequest_UserAnswer)(nil),                                // 52: api.user_growth_center.v1.SubmitQuizRequest.UserAnswer
	(*GetQuizRecordReply_QuizResult)(nil),                               // 53: api.user_growth_center.v1.GetQuizRecordReply.QuizResult
	(*GetUserDivisionRewardLevelInfoReply_RewardLevel)(nil),             // 54: api.user_growth_center.v1.GetUserDivisionRewardLevelInfoReply.RewardLevel
	(*GetUserDivisionInviterStatisticsInfoReply_InviterStatistics)(nil), // 55: api.user_growth_center.v1.GetUserDivisionInviterStatisticsInfoReply.InviterStatistics
	(*GetUserDivisionInviteeInfoReply_InviteeExtraInfo)(nil),            // 56: api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.InviteeExtraInfo
	(*GetUserDivisionInviteeInfoReply_InviterInfo)(nil),                 // 57: api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.InviterInfo
	(*GetUserDivisionActivityListReply_VPSUpgradeRecord)(nil),           // 58: api.user_growth_center.v1.GetUserDivisionActivityListReply.VPSUpgradeRecord
	(*GetUserDivisionActivityInfoReply_AwardTaskItem)(nil),              // 59: api.user_growth_center.v1.GetUserDivisionActivityInfoReply.AwardTaskItem
	(*GetUserDivisionActivityInfoReply_AwardData)(nil),                  // 60: api.user_growth_center.v1.GetUserDivisionActivityInfoReply.AwardData
	(*TraderDepositWithdrawMsg_TraderDepositWithdrawData)(nil),          // 61: api.user_growth_center.v1.TraderDepositWithdrawMsg.TraderDepositWithdrawData
	(*TraderAccountMsg_TraderAccountData)(nil),                          // 62: api.user_growth_center.v1.TraderAccountMsg.TraderAccountData
	(*TraderOrdersMsg_TraderOrdersData)(nil),                            // 63: api.user_growth_center.v1.TraderOrdersMsg.TraderOrdersData
	(*UserMsg_UserData)(nil),                                            // 64: api.user_growth_center.v1.UserMsg.UserData
}
var file_user_growth_center_v1_models_proto_depIdxs = []int32{
	16, // 0: api.user_growth_center.v1.GetInvitationPopupDataReply.userData:type_name -> api.user_growth_center.v1.UserData
	16, // 1: api.user_growth_center.v1.GetInviteRewardBannerDataReply.userData:type_name -> api.user_growth_center.v1.UserData
	16, // 2: api.user_growth_center.v1.GetShareLinkDataReply.userData:type_name -> api.user_growth_center.v1.UserData
	49, // 3: api.user_growth_center.v1.GetShareLinkDataReply.shareLinkData:type_name -> api.user_growth_center.v1.GetShareLinkDataReply.ShareLinkData
	50, // 4: api.user_growth_center.v1.GetInvitedRecordDataReply.invitedRecordDataCol:type_name -> api.user_growth_center.v1.GetInvitedRecordDataReply.InvitedRecordData
	0,  // 5: api.user_growth_center.v1.QuizInfo.questionType:type_name -> api.user_growth_center.v1.QuestionType
	51, // 6: api.user_growth_center.v1.QuizInfo.options:type_name -> api.user_growth_center.v1.QuizInfo.Option
	17, // 7: api.user_growth_center.v1.GetQuizInfoReply.quizInfo:type_name -> api.user_growth_center.v1.QuizInfo
	52, // 8: api.user_growth_center.v1.SubmitQuizRequest.answers:type_name -> api.user_growth_center.v1.SubmitQuizRequest.UserAnswer
	53, // 9: api.user_growth_center.v1.GetQuizRecordReply.results:type_name -> api.user_growth_center.v1.GetQuizRecordReply.QuizResult
	22, // 10: api.user_growth_center.v1.GetInviterActivityTimeReply.InviterActivityTime:type_name -> api.user_growth_center.v1.InviterActivityTime
	22, // 11: api.user_growth_center.v1.UpdateInviterActivityTimeRequest.InviterActivityTime:type_name -> api.user_growth_center.v1.InviterActivityTime
	22, // 12: api.user_growth_center.v1.UpdateInviterActivityTimeReply.InviterActivityTime:type_name -> api.user_growth_center.v1.InviterActivityTime
	54, // 13: api.user_growth_center.v1.GetUserDivisionRewardLevelInfoReply.RewardLevelCol:type_name -> api.user_growth_center.v1.GetUserDivisionRewardLevelInfoReply.RewardLevel
	55, // 14: api.user_growth_center.v1.GetUserDivisionInviterStatisticsInfoReply.inviterStatisticsCol:type_name -> api.user_growth_center.v1.GetUserDivisionInviterStatisticsInfoReply.InviterStatistics
	57, // 15: api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.inviterInfoCol:type_name -> api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.InviterInfo
	58, // 16: api.user_growth_center.v1.GetUserDivisionActivityListReply.vpsUpgradeRecordCol:type_name -> api.user_growth_center.v1.GetUserDivisionActivityListReply.VPSUpgradeRecord
	60, // 17: api.user_growth_center.v1.GetUserDivisionActivityInfoReply.awardData:type_name -> api.user_growth_center.v1.GetUserDivisionActivityInfoReply.AwardData
	61, // 18: api.user_growth_center.v1.TraderDepositWithdrawMsg.data:type_name -> api.user_growth_center.v1.TraderDepositWithdrawMsg.TraderDepositWithdrawData
	61, // 19: api.user_growth_center.v1.TraderDepositWithdrawMsg.old:type_name -> api.user_growth_center.v1.TraderDepositWithdrawMsg.TraderDepositWithdrawData
	62, // 20: api.user_growth_center.v1.TraderAccountMsg.data:type_name -> api.user_growth_center.v1.TraderAccountMsg.TraderAccountData
	62, // 21: api.user_growth_center.v1.TraderAccountMsg.old:type_name -> api.user_growth_center.v1.TraderAccountMsg.TraderAccountData
	63, // 22: api.user_growth_center.v1.TraderOrdersMsg.data:type_name -> api.user_growth_center.v1.TraderOrdersMsg.TraderOrdersData
	63, // 23: api.user_growth_center.v1.TraderOrdersMsg.old:type_name -> api.user_growth_center.v1.TraderOrdersMsg.TraderOrdersData
	64, // 24: api.user_growth_center.v1.UserMsg.data:type_name -> api.user_growth_center.v1.UserMsg.UserData
	64, // 25: api.user_growth_center.v1.UserMsg.old:type_name -> api.user_growth_center.v1.UserMsg.UserData
	16, // 26: api.user_growth_center.v1.GetInvitedRecordDataReply.InvitedRecordData.userData:type_name -> api.user_growth_center.v1.UserData
	16, // 27: api.user_growth_center.v1.GetUserDivisionInviterStatisticsInfoReply.InviterStatistics.userInfo:type_name -> api.user_growth_center.v1.UserData
	16, // 28: api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.InviterInfo.userInfo:type_name -> api.user_growth_center.v1.UserData
	56, // 29: api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.InviterInfo.inviteeExtraInfo:type_name -> api.user_growth_center.v1.GetUserDivisionInviteeInfoReply.InviteeExtraInfo
	16, // 30: api.user_growth_center.v1.GetUserDivisionActivityListReply.VPSUpgradeRecord.userInfo:type_name -> api.user_growth_center.v1.UserData
	59, // 31: api.user_growth_center.v1.GetUserDivisionActivityInfoReply.AwardData.taskCol:type_name -> api.user_growth_center.v1.GetUserDivisionActivityInfoReply.AwardTaskItem
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_user_growth_center_v1_models_proto_init() }
func file_user_growth_center_v1_models_proto_init() {
	if File_user_growth_center_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_growth_center_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringReplyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StringReplyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitationPopupDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitationPopupDataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInviteRewardBannerDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInviteRewardBannerDataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetShareLinkDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetShareLinkDataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitedRecordDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitedRecordDataReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVpsLevelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuizInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuizInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitQuizRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitQuizReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuizRecordReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InviterActivityTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInviterActivityTimeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInviterActivityTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInviterActivityTimeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionRewardLevelInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserDivisionRewardLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserDivisionRewardLevelReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserDivisionRewardLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteUserDivisionRewardLevelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserDivisionInvitationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserDivisionInvitationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviterStatisticsInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviterStatisticsInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviteeInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviteeInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionActivityListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionActivityListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionActivityInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionEntryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeVPSRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpgradeVPSReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUpgradeVPSStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostUpgradeVPSStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderDepositWithdrawMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderAccountMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderOrdersMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserMsg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetShareLinkDataReply_ShareLinkData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetInvitedRecordDataReply_InvitedRecordData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuizInfo_Option); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitQuizRequest_UserAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetQuizRecordReply_QuizResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionRewardLevelInfoReply_RewardLevel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviterStatisticsInfoReply_InviterStatistics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviteeInfoReply_InviteeExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionInviteeInfoReply_InviterInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionActivityListReply_VPSUpgradeRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionActivityInfoReply_AwardTaskItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserDivisionActivityInfoReply_AwardData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderDepositWithdrawMsg_TraderDepositWithdrawData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderAccountMsg_TraderAccountData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderOrdersMsg_TraderOrdersData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_growth_center_v1_models_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserMsg_UserData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_growth_center_v1_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   64,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_growth_center_v1_models_proto_goTypes,
		DependencyIndexes: file_user_growth_center_v1_models_proto_depIdxs,
		EnumInfos:         file_user_growth_center_v1_models_proto_enumTypes,
		MessageInfos:      file_user_growth_center_v1_models_proto_msgTypes,
	}.Build()
	File_user_growth_center_v1_models_proto = out.File
	file_user_growth_center_v1_models_proto_rawDesc = nil
	file_user_growth_center_v1_models_proto_goTypes = nil
	file_user_growth_center_v1_models_proto_depIdxs = nil
}
