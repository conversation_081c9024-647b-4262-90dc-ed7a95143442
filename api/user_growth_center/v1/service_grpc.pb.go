// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: user_growth_center/v1/service.proto

package v1

import (
	context "context"
	common "gold_store/api/common"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName                              = "/api.user_growth_center.v1.Service/Healthy"
	Service_GetUserInfo_FullMethodName                          = "/api.user_growth_center.v1.Service/GetUserInfo"
	Service_StringReply_FullMethodName                          = "/api.user_growth_center.v1.Service/StringReply"
	Service_GetIdentityRule_FullMethodName                      = "/api.user_growth_center.v1.Service/GetIdentityRule"
	Service_GetUserGrowthDetail_FullMethodName                  = "/api.user_growth_center.v1.Service/GetUserGrowthDetail"
	Service_GetGrowthCenterEntry_FullMethodName                 = "/api.user_growth_center.v1.Service/GetGrowthCenterEntry"
	Service_GetIdentityCarousel_FullMethodName                  = "/api.user_growth_center.v1.Service/GetIdentityCarousel"
	Service_GetIdentityShare_FullMethodName                     = "/api.user_growth_center.v1.Service/GetIdentityShare"
	Service_GetUpgradeIdentity_FullMethodName                   = "/api.user_growth_center.v1.Service/GetUpgradeIdentity"
	Service_PostUserIdentitySwitch_FullMethodName               = "/api.user_growth_center.v1.Service/PostUserIdentitySwitch"
	Service_GetActivityMain_FullMethodName                      = "/api.user_growth_center.v1.Service/GetActivityMain"
	Service_GetActivityShare_FullMethodName                     = "/api.user_growth_center.v1.Service/GetActivityShare"
	Service_GetContent_FullMethodName                           = "/api.user_growth_center.v1.Service/GetContent"
	Service_GetRecommendTrader_FullMethodName                   = "/api.user_growth_center.v1.Service/GetRecommendTrader"
	Service_GetGlobalTrader_FullMethodName                      = "/api.user_growth_center.v1.Service/GetGlobalTrader"
	Service_GetTraderActivityPage_FullMethodName                = "/api.user_growth_center.v1.Service/GetTraderActivityPage"
	Service_GetRecommenderList_FullMethodName                   = "/api.user_growth_center.v1.Service/GetRecommenderList"
	Service_GetRewordPoolDetail_FullMethodName                  = "/api.user_growth_center.v1.Service/GetRewordPoolDetail"
	Service_UserCheckIn_FullMethodName                          = "/api.user_growth_center.v1.Service/UserCheckIn"
	Service_GetRewordDetail_FullMethodName                      = "/api.user_growth_center.v1.Service/GetRewordDetail"
	Service_GrandLuckyDraw_FullMethodName                       = "/api.user_growth_center.v1.Service/GrandLuckyDraw"
	Service_StartDraw_FullMethodName                            = "/api.user_growth_center.v1.Service/StartDraw"
	Service_GetDepositDetail_FullMethodName                     = "/api.user_growth_center.v1.Service/GetDepositDetail"
	Service_SearchTrader_FullMethodName                         = "/api.user_growth_center.v1.Service/SearchTrader"
	Service_GetBanner_FullMethodName                            = "/api.user_growth_center.v1.Service/GetBanner"
	Service_Assist_FullMethodName                               = "/api.user_growth_center.v1.Service/Assist"
	Service_WatchLiveCompleted_FullMethodName                   = "/api.user_growth_center.v1.Service/WatchLiveCompleted"
	Service_FriendAssistance_FullMethodName                     = "/api.user_growth_center.v1.Service/FriendAssistance"
	Service_GetInvitationPopupData_FullMethodName               = "/api.user_growth_center.v1.Service/GetInvitationPopupData"
	Service_GetInviteRewardBannerData_FullMethodName            = "/api.user_growth_center.v1.Service/GetInviteRewardBannerData"
	Service_GetShareLinkData_FullMethodName                     = "/api.user_growth_center.v1.Service/GetShareLinkData"
	Service_GetInvitedRecordData_FullMethodName                 = "/api.user_growth_center.v1.Service/GetInvitedRecordData"
	Service_GetVpsLevel_FullMethodName                          = "/api.user_growth_center.v1.Service/GetVpsLevel"
	Service_GetQuizInfo_FullMethodName                          = "/api.user_growth_center.v1.Service/GetQuizInfo"
	Service_SubmitQuiz_FullMethodName                           = "/api.user_growth_center.v1.Service/SubmitQuiz"
	Service_GetQuizRecord_FullMethodName                        = "/api.user_growth_center.v1.Service/GetQuizRecord"
	Service_GetInviterActivityTime_FullMethodName               = "/api.user_growth_center.v1.Service/GetInviterActivityTime"
	Service_UpdateInviterActivityTime_FullMethodName            = "/api.user_growth_center.v1.Service/UpdateInviterActivityTime"
	Service_GetUserDivisionRewardLevelInfo_FullMethodName       = "/api.user_growth_center.v1.Service/GetUserDivisionRewardLevelInfo"
	Service_CreateUserDivisionRewardLevel_FullMethodName        = "/api.user_growth_center.v1.Service/CreateUserDivisionRewardLevel"
	Service_UpdateUserDivisionRewardLevel_FullMethodName        = "/api.user_growth_center.v1.Service/UpdateUserDivisionRewardLevel"
	Service_DeleteUserDivisionRewardLevel_FullMethodName        = "/api.user_growth_center.v1.Service/DeleteUserDivisionRewardLevel"
	Service_CreateUserDivisionInvitation_FullMethodName         = "/api.user_growth_center.v1.Service/CreateUserDivisionInvitation"
	Service_GetUserDivisionInviterStatisticsInfo_FullMethodName = "/api.user_growth_center.v1.Service/GetUserDivisionInviterStatisticsInfo"
	Service_GetUserDivisionInviteeInfo_FullMethodName           = "/api.user_growth_center.v1.Service/GetUserDivisionInviteeInfo"
	Service_GetUserDivisionActivityList_FullMethodName          = "/api.user_growth_center.v1.Service/GetUserDivisionActivityList"
	Service_GetUserDivisionActivityInfo_FullMethodName          = "/api.user_growth_center.v1.Service/GetUserDivisionActivityInfo"
	Service_GetUserDivisionEntry_FullMethodName                 = "/api.user_growth_center.v1.Service/GetUserDivisionEntry"
	Service_UpgradeVPS_FullMethodName                           = "/api.user_growth_center.v1.Service/UpgradeVPS"
	Service_PostUpgradeVPSStatus_FullMethodName                 = "/api.user_growth_center.v1.Service/PostUpgradeVPSStatus"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoReply, error)
	StringReply(ctx context.Context, in *StringReplyRequest, opts ...grpc.CallOption) (*common.StringReply, error)
	// 身份升级规则
	GetIdentityRule(ctx context.Context, in *GetIdentityRuleRequest, opts ...grpc.CallOption) (*GetIdentityRuleReply, error)
	// 身份成长中心详情页
	GetUserGrowthDetail(ctx context.Context, in *GetUserGrowthDetailRequest, opts ...grpc.CallOption) (*GetUserGrowthDetailReply, error)
	// 成长中心入展示开关
	GetGrowthCenterEntry(ctx context.Context, in *GetGrowthCenterEntryRequest, opts ...grpc.CallOption) (*GetGrowthCenterEntryReply, error)
	// 获取我的页面banner位置轮播图（有屏蔽点位）
	GetIdentityCarousel(ctx context.Context, in *GetIdentityCarouselRequest, opts ...grpc.CallOption) (*GetIdentityCarouselReply, error)
	// 获取身份分享图
	GetIdentityShare(ctx context.Context, in *GetIdentityShareRequest, opts ...grpc.CallOption) (*GetIdentityShareReply, error)
	// 升级身份入口
	GetUpgradeIdentity(ctx context.Context, in *GetUpgradeIdentityRequest, opts ...grpc.CallOption) (*GetUpgradeIdentityReply, error)
	// APP 切换身份
	PostUserIdentitySwitch(ctx context.Context, in *PostUserIdentitySwitchRequest, opts ...grpc.CallOption) (*PostUserIdentitySwitchReply, error)
	// 活动主页
	GetActivityMain(ctx context.Context, in *GetActivityMainRequest, opts ...grpc.CallOption) (*GetActivityMainReply, error)
	// 活动主页分享
	GetActivityShare(ctx context.Context, in *GetActivityShareRequest, opts ...grpc.CallOption) (*GetActivityShareReply, error)
	// 规则和说明
	GetContent(ctx context.Context, in *GetContentRequest, opts ...grpc.CallOption) (*GetContentReply, error)
	// 搜索推荐交易商
	GetRecommendTrader(ctx context.Context, in *GetRecommendTraderRequest, opts ...grpc.CallOption) (*GetRecommendTraderReply, error)
	// 全球交易商列表
	GetGlobalTrader(ctx context.Context, in *GetGlobalTraderRequest, opts ...grpc.CallOption) (*GetGlobalTraderReply, error)
	// 交易商活动详情页
	GetTraderActivityPage(ctx context.Context, in *GetTraderActivityPageRequest, opts ...grpc.CallOption) (*GetTraderActivityPageReply, error)
	// 获取推荐官列表
	GetRecommenderList(ctx context.Context, in *GetRecommenderListRequest, opts ...grpc.CallOption) (*GetRecommenderListReply, error)
	// 瓜分现金奖池
	GetRewordPoolDetail(ctx context.Context, in *GetRewordPoolDetailRequest, opts ...grpc.CallOption) (*GetRewordPoolDetailReply, error)
	// 用户签到
	UserCheckIn(ctx context.Context, in *UserCheckInRequest, opts ...grpc.CallOption) (*UserCheckInReply, error)
	// 我的奖励
	GetRewordDetail(ctx context.Context, in *GetRewordDetailRequest, opts ...grpc.CallOption) (*GetRewordDetailReply, error)
	// 幸运大抽奖详情页
	GrandLuckyDraw(ctx context.Context, in *GrandLuckyDrawRequest, opts ...grpc.CallOption) (*GrandLuckyDrawReply, error)
	// 开始抽奖
	StartDraw(ctx context.Context, in *GrandLuckyDrawRequest, opts ...grpc.CallOption) (*StartDrawReply, error)
	// 入金一手
	GetDepositDetail(ctx context.Context, in *GetDepositDetailRequest, opts ...grpc.CallOption) (*GetDepositDetailReply, error)
	// 搜索交易商
	SearchTrader(ctx context.Context, in *SearchTraderRequest, opts ...grpc.CallOption) (*SearchTraderReply, error)
	// 轮播图和广告
	GetBanner(ctx context.Context, in *GetBannerRequest, opts ...grpc.CallOption) (*GetBannerReply, error)
	// 给用户助力
	Assist(ctx context.Context, in *AssistRequest, opts ...grpc.CallOption) (*AssistReply, error)
	// 观看直播任务完成
	WatchLiveCompleted(ctx context.Context, in *WatchLiveRequest, opts ...grpc.CallOption) (*WatchLiveReply, error)
	// 好友助力
	FriendAssistance(ctx context.Context, in *FriendAssistanceRequest, opts ...grpc.CallOption) (*FriendAssistanceReply, error)
	// 获取邀请奖励弹窗数据
	GetInvitationPopupData(ctx context.Context, in *GetInvitationPopupDataRequest, opts ...grpc.CallOption) (*GetInvitationPopupDataReply, error)
	// 获取邀请奖励领取banner数据
	GetInviteRewardBannerData(ctx context.Context, in *GetInviteRewardBannerDataRequest, opts ...grpc.CallOption) (*GetInviteRewardBannerDataReply, error)
	// 获取分享推广链接数据
	GetShareLinkData(ctx context.Context, in *GetShareLinkDataRequest, opts ...grpc.CallOption) (*GetShareLinkDataReply, error)
	// 获取邀请用户记录数据
	GetInvitedRecordData(ctx context.Context, in *GetInvitedRecordDataRequest, opts ...grpc.CallOption) (*GetInvitedRecordDataReply, error)
	// 获取用户vps等级
	GetVpsLevel(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetVpsLevelReply, error)
	// ----------------------------------答题模块----------------------------------//
	// 获取试题数据
	GetQuizInfo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetQuizInfoReply, error)
	// 提交试卷
	SubmitQuiz(ctx context.Context, in *SubmitQuizRequest, opts ...grpc.CallOption) (*SubmitQuizReply, error)
	// 获取试卷记录
	GetQuizRecord(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetQuizRecordReply, error)
	// ----------------------------------答题模块----------------------------------//
	// ----------------------------------奖励配置----------------------------------//
	GetInviterActivityTime(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetInviterActivityTimeReply, error)
	UpdateInviterActivityTime(ctx context.Context, in *UpdateInviterActivityTimeRequest, opts ...grpc.CallOption) (*UpdateInviterActivityTimeReply, error)
	GetUserDivisionRewardLevelInfo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetUserDivisionRewardLevelInfoReply, error)
	// 新增用户分裂奖励配置
	CreateUserDivisionRewardLevel(ctx context.Context, in *CreateUserDivisionRewardLevelRequest, opts ...grpc.CallOption) (*CreateUserDivisionRewardLevelReply, error)
	// 修改用户分裂奖励配置
	UpdateUserDivisionRewardLevel(ctx context.Context, in *UpdateUserDivisionRewardLevelRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// 删除用户分裂奖励配置
	DeleteUserDivisionRewardLevel(ctx context.Context, in *DeleteUserDivisionRewardLevelRequest, opts ...grpc.CallOption) (*common.EmptyReply, error)
	// ----------------------------------奖励配置----------------------------------//
	// ----------------------------------邀请----------------------------------//
	// 新增用户分裂邀请记录
	CreateUserDivisionInvitation(ctx context.Context, in *CreateUserDivisionInvitationRequest, opts ...grpc.CallOption) (*CreateUserDivisionInvitationReply, error)
	// 获取用户分裂邀请人统计数据列表
	GetUserDivisionInviterStatisticsInfo(ctx context.Context, in *GetUserDivisionInviterStatisticsInfoRequest, opts ...grpc.CallOption) (*GetUserDivisionInviterStatisticsInfoReply, error)
	// 获取用户分裂被邀请人数据列表
	GetUserDivisionInviteeInfo(ctx context.Context, in *GetUserDivisionInviteeInfoRequest, opts ...grpc.CallOption) (*GetUserDivisionInviteeInfoReply, error)
	// 获取邀请活动VPS升级记录列表
	GetUserDivisionActivityList(ctx context.Context, in *GetUserDivisionActivityListRequest, opts ...grpc.CallOption) (*GetUserDivisionActivityListReply, error)
	// ----------------------------------邀请----------------------------------//
	// ----------------------------------裂变流程----------------------------------//
	// 裂变活动详情
	GetUserDivisionActivityInfo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetUserDivisionActivityInfoReply, error)
	// 裂变活动入口
	GetUserDivisionEntry(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetUserDivisionEntryReply, error)
	// 升级VPS
	UpgradeVPS(ctx context.Context, in *UpgradeVPSRequest, opts ...grpc.CallOption) (*UpgradeVPSReply, error)
	// VPS升级回调接口
	PostUpgradeVPSStatus(ctx context.Context, in *PostUpgradeVPSStatusRequest, opts ...grpc.CallOption) (*PostUpgradeVPSStatusReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoReply, error) {
	out := new(GetUserInfoReply)
	err := c.cc.Invoke(ctx, Service_GetUserInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) StringReply(ctx context.Context, in *StringReplyRequest, opts ...grpc.CallOption) (*common.StringReply, error) {
	out := new(common.StringReply)
	err := c.cc.Invoke(ctx, Service_StringReply_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetIdentityRule(ctx context.Context, in *GetIdentityRuleRequest, opts ...grpc.CallOption) (*GetIdentityRuleReply, error) {
	out := new(GetIdentityRuleReply)
	err := c.cc.Invoke(ctx, Service_GetIdentityRule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserGrowthDetail(ctx context.Context, in *GetUserGrowthDetailRequest, opts ...grpc.CallOption) (*GetUserGrowthDetailReply, error) {
	out := new(GetUserGrowthDetailReply)
	err := c.cc.Invoke(ctx, Service_GetUserGrowthDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetGrowthCenterEntry(ctx context.Context, in *GetGrowthCenterEntryRequest, opts ...grpc.CallOption) (*GetGrowthCenterEntryReply, error) {
	out := new(GetGrowthCenterEntryReply)
	err := c.cc.Invoke(ctx, Service_GetGrowthCenterEntry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetIdentityCarousel(ctx context.Context, in *GetIdentityCarouselRequest, opts ...grpc.CallOption) (*GetIdentityCarouselReply, error) {
	out := new(GetIdentityCarouselReply)
	err := c.cc.Invoke(ctx, Service_GetIdentityCarousel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetIdentityShare(ctx context.Context, in *GetIdentityShareRequest, opts ...grpc.CallOption) (*GetIdentityShareReply, error) {
	out := new(GetIdentityShareReply)
	err := c.cc.Invoke(ctx, Service_GetIdentityShare_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUpgradeIdentity(ctx context.Context, in *GetUpgradeIdentityRequest, opts ...grpc.CallOption) (*GetUpgradeIdentityReply, error) {
	out := new(GetUpgradeIdentityReply)
	err := c.cc.Invoke(ctx, Service_GetUpgradeIdentity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) PostUserIdentitySwitch(ctx context.Context, in *PostUserIdentitySwitchRequest, opts ...grpc.CallOption) (*PostUserIdentitySwitchReply, error) {
	out := new(PostUserIdentitySwitchReply)
	err := c.cc.Invoke(ctx, Service_PostUserIdentitySwitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetActivityMain(ctx context.Context, in *GetActivityMainRequest, opts ...grpc.CallOption) (*GetActivityMainReply, error) {
	out := new(GetActivityMainReply)
	err := c.cc.Invoke(ctx, Service_GetActivityMain_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetActivityShare(ctx context.Context, in *GetActivityShareRequest, opts ...grpc.CallOption) (*GetActivityShareReply, error) {
	out := new(GetActivityShareReply)
	err := c.cc.Invoke(ctx, Service_GetActivityShare_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetContent(ctx context.Context, in *GetContentRequest, opts ...grpc.CallOption) (*GetContentReply, error) {
	out := new(GetContentReply)
	err := c.cc.Invoke(ctx, Service_GetContent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRecommendTrader(ctx context.Context, in *GetRecommendTraderRequest, opts ...grpc.CallOption) (*GetRecommendTraderReply, error) {
	out := new(GetRecommendTraderReply)
	err := c.cc.Invoke(ctx, Service_GetRecommendTrader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetGlobalTrader(ctx context.Context, in *GetGlobalTraderRequest, opts ...grpc.CallOption) (*GetGlobalTraderReply, error) {
	out := new(GetGlobalTraderReply)
	err := c.cc.Invoke(ctx, Service_GetGlobalTrader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetTraderActivityPage(ctx context.Context, in *GetTraderActivityPageRequest, opts ...grpc.CallOption) (*GetTraderActivityPageReply, error) {
	out := new(GetTraderActivityPageReply)
	err := c.cc.Invoke(ctx, Service_GetTraderActivityPage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRecommenderList(ctx context.Context, in *GetRecommenderListRequest, opts ...grpc.CallOption) (*GetRecommenderListReply, error) {
	out := new(GetRecommenderListReply)
	err := c.cc.Invoke(ctx, Service_GetRecommenderList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRewordPoolDetail(ctx context.Context, in *GetRewordPoolDetailRequest, opts ...grpc.CallOption) (*GetRewordPoolDetailReply, error) {
	out := new(GetRewordPoolDetailReply)
	err := c.cc.Invoke(ctx, Service_GetRewordPoolDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserCheckIn(ctx context.Context, in *UserCheckInRequest, opts ...grpc.CallOption) (*UserCheckInReply, error) {
	out := new(UserCheckInReply)
	err := c.cc.Invoke(ctx, Service_UserCheckIn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRewordDetail(ctx context.Context, in *GetRewordDetailRequest, opts ...grpc.CallOption) (*GetRewordDetailReply, error) {
	out := new(GetRewordDetailReply)
	err := c.cc.Invoke(ctx, Service_GetRewordDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GrandLuckyDraw(ctx context.Context, in *GrandLuckyDrawRequest, opts ...grpc.CallOption) (*GrandLuckyDrawReply, error) {
	out := new(GrandLuckyDrawReply)
	err := c.cc.Invoke(ctx, Service_GrandLuckyDraw_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) StartDraw(ctx context.Context, in *GrandLuckyDrawRequest, opts ...grpc.CallOption) (*StartDrawReply, error) {
	out := new(StartDrawReply)
	err := c.cc.Invoke(ctx, Service_StartDraw_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetDepositDetail(ctx context.Context, in *GetDepositDetailRequest, opts ...grpc.CallOption) (*GetDepositDetailReply, error) {
	out := new(GetDepositDetailReply)
	err := c.cc.Invoke(ctx, Service_GetDepositDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SearchTrader(ctx context.Context, in *SearchTraderRequest, opts ...grpc.CallOption) (*SearchTraderReply, error) {
	out := new(SearchTraderReply)
	err := c.cc.Invoke(ctx, Service_SearchTrader_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetBanner(ctx context.Context, in *GetBannerRequest, opts ...grpc.CallOption) (*GetBannerReply, error) {
	out := new(GetBannerReply)
	err := c.cc.Invoke(ctx, Service_GetBanner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) Assist(ctx context.Context, in *AssistRequest, opts ...grpc.CallOption) (*AssistReply, error) {
	out := new(AssistReply)
	err := c.cc.Invoke(ctx, Service_Assist_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) WatchLiveCompleted(ctx context.Context, in *WatchLiveRequest, opts ...grpc.CallOption) (*WatchLiveReply, error) {
	out := new(WatchLiveReply)
	err := c.cc.Invoke(ctx, Service_WatchLiveCompleted_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FriendAssistance(ctx context.Context, in *FriendAssistanceRequest, opts ...grpc.CallOption) (*FriendAssistanceReply, error) {
	out := new(FriendAssistanceReply)
	err := c.cc.Invoke(ctx, Service_FriendAssistance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInvitationPopupData(ctx context.Context, in *GetInvitationPopupDataRequest, opts ...grpc.CallOption) (*GetInvitationPopupDataReply, error) {
	out := new(GetInvitationPopupDataReply)
	err := c.cc.Invoke(ctx, Service_GetInvitationPopupData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInviteRewardBannerData(ctx context.Context, in *GetInviteRewardBannerDataRequest, opts ...grpc.CallOption) (*GetInviteRewardBannerDataReply, error) {
	out := new(GetInviteRewardBannerDataReply)
	err := c.cc.Invoke(ctx, Service_GetInviteRewardBannerData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetShareLinkData(ctx context.Context, in *GetShareLinkDataRequest, opts ...grpc.CallOption) (*GetShareLinkDataReply, error) {
	out := new(GetShareLinkDataReply)
	err := c.cc.Invoke(ctx, Service_GetShareLinkData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInvitedRecordData(ctx context.Context, in *GetInvitedRecordDataRequest, opts ...grpc.CallOption) (*GetInvitedRecordDataReply, error) {
	out := new(GetInvitedRecordDataReply)
	err := c.cc.Invoke(ctx, Service_GetInvitedRecordData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetVpsLevel(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetVpsLevelReply, error) {
	out := new(GetVpsLevelReply)
	err := c.cc.Invoke(ctx, Service_GetVpsLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetQuizInfo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetQuizInfoReply, error) {
	out := new(GetQuizInfoReply)
	err := c.cc.Invoke(ctx, Service_GetQuizInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SubmitQuiz(ctx context.Context, in *SubmitQuizRequest, opts ...grpc.CallOption) (*SubmitQuizReply, error) {
	out := new(SubmitQuizReply)
	err := c.cc.Invoke(ctx, Service_SubmitQuiz_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetQuizRecord(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetQuizRecordReply, error) {
	out := new(GetQuizRecordReply)
	err := c.cc.Invoke(ctx, Service_GetQuizRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetInviterActivityTime(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetInviterActivityTimeReply, error) {
	out := new(GetInviterActivityTimeReply)
	err := c.cc.Invoke(ctx, Service_GetInviterActivityTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UpdateInviterActivityTime(ctx context.Context, in *UpdateInviterActivityTimeRequest, opts ...grpc.CallOption) (*UpdateInviterActivityTimeReply, error) {
	out := new(UpdateInviterActivityTimeReply)
	err := c.cc.Invoke(ctx, Service_UpdateInviterActivityTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserDivisionRewardLevelInfo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetUserDivisionRewardLevelInfoReply, error) {
	out := new(GetUserDivisionRewardLevelInfoReply)
	err := c.cc.Invoke(ctx, Service_GetUserDivisionRewardLevelInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CreateUserDivisionRewardLevel(ctx context.Context, in *CreateUserDivisionRewardLevelRequest, opts ...grpc.CallOption) (*CreateUserDivisionRewardLevelReply, error) {
	out := new(CreateUserDivisionRewardLevelReply)
	err := c.cc.Invoke(ctx, Service_CreateUserDivisionRewardLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UpdateUserDivisionRewardLevel(ctx context.Context, in *UpdateUserDivisionRewardLevelRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Service_UpdateUserDivisionRewardLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) DeleteUserDivisionRewardLevel(ctx context.Context, in *DeleteUserDivisionRewardLevelRequest, opts ...grpc.CallOption) (*common.EmptyReply, error) {
	out := new(common.EmptyReply)
	err := c.cc.Invoke(ctx, Service_DeleteUserDivisionRewardLevel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CreateUserDivisionInvitation(ctx context.Context, in *CreateUserDivisionInvitationRequest, opts ...grpc.CallOption) (*CreateUserDivisionInvitationReply, error) {
	out := new(CreateUserDivisionInvitationReply)
	err := c.cc.Invoke(ctx, Service_CreateUserDivisionInvitation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserDivisionInviterStatisticsInfo(ctx context.Context, in *GetUserDivisionInviterStatisticsInfoRequest, opts ...grpc.CallOption) (*GetUserDivisionInviterStatisticsInfoReply, error) {
	out := new(GetUserDivisionInviterStatisticsInfoReply)
	err := c.cc.Invoke(ctx, Service_GetUserDivisionInviterStatisticsInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserDivisionInviteeInfo(ctx context.Context, in *GetUserDivisionInviteeInfoRequest, opts ...grpc.CallOption) (*GetUserDivisionInviteeInfoReply, error) {
	out := new(GetUserDivisionInviteeInfoReply)
	err := c.cc.Invoke(ctx, Service_GetUserDivisionInviteeInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserDivisionActivityList(ctx context.Context, in *GetUserDivisionActivityListRequest, opts ...grpc.CallOption) (*GetUserDivisionActivityListReply, error) {
	out := new(GetUserDivisionActivityListReply)
	err := c.cc.Invoke(ctx, Service_GetUserDivisionActivityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserDivisionActivityInfo(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetUserDivisionActivityInfoReply, error) {
	out := new(GetUserDivisionActivityInfoReply)
	err := c.cc.Invoke(ctx, Service_GetUserDivisionActivityInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserDivisionEntry(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*GetUserDivisionEntryReply, error) {
	out := new(GetUserDivisionEntryReply)
	err := c.cc.Invoke(ctx, Service_GetUserDivisionEntry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UpgradeVPS(ctx context.Context, in *UpgradeVPSRequest, opts ...grpc.CallOption) (*UpgradeVPSReply, error) {
	out := new(UpgradeVPSReply)
	err := c.cc.Invoke(ctx, Service_UpgradeVPS_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) PostUpgradeVPSStatus(ctx context.Context, in *PostUpgradeVPSStatusRequest, opts ...grpc.CallOption) (*PostUpgradeVPSStatusReply, error) {
	out := new(PostUpgradeVPSStatusReply)
	err := c.cc.Invoke(ctx, Service_PostUpgradeVPSStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoReply, error)
	StringReply(context.Context, *StringReplyRequest) (*common.StringReply, error)
	// 身份升级规则
	GetIdentityRule(context.Context, *GetIdentityRuleRequest) (*GetIdentityRuleReply, error)
	// 身份成长中心详情页
	GetUserGrowthDetail(context.Context, *GetUserGrowthDetailRequest) (*GetUserGrowthDetailReply, error)
	// 成长中心入展示开关
	GetGrowthCenterEntry(context.Context, *GetGrowthCenterEntryRequest) (*GetGrowthCenterEntryReply, error)
	// 获取我的页面banner位置轮播图（有屏蔽点位）
	GetIdentityCarousel(context.Context, *GetIdentityCarouselRequest) (*GetIdentityCarouselReply, error)
	// 获取身份分享图
	GetIdentityShare(context.Context, *GetIdentityShareRequest) (*GetIdentityShareReply, error)
	// 升级身份入口
	GetUpgradeIdentity(context.Context, *GetUpgradeIdentityRequest) (*GetUpgradeIdentityReply, error)
	// APP 切换身份
	PostUserIdentitySwitch(context.Context, *PostUserIdentitySwitchRequest) (*PostUserIdentitySwitchReply, error)
	// 活动主页
	GetActivityMain(context.Context, *GetActivityMainRequest) (*GetActivityMainReply, error)
	// 活动主页分享
	GetActivityShare(context.Context, *GetActivityShareRequest) (*GetActivityShareReply, error)
	// 规则和说明
	GetContent(context.Context, *GetContentRequest) (*GetContentReply, error)
	// 搜索推荐交易商
	GetRecommendTrader(context.Context, *GetRecommendTraderRequest) (*GetRecommendTraderReply, error)
	// 全球交易商列表
	GetGlobalTrader(context.Context, *GetGlobalTraderRequest) (*GetGlobalTraderReply, error)
	// 交易商活动详情页
	GetTraderActivityPage(context.Context, *GetTraderActivityPageRequest) (*GetTraderActivityPageReply, error)
	// 获取推荐官列表
	GetRecommenderList(context.Context, *GetRecommenderListRequest) (*GetRecommenderListReply, error)
	// 瓜分现金奖池
	GetRewordPoolDetail(context.Context, *GetRewordPoolDetailRequest) (*GetRewordPoolDetailReply, error)
	// 用户签到
	UserCheckIn(context.Context, *UserCheckInRequest) (*UserCheckInReply, error)
	// 我的奖励
	GetRewordDetail(context.Context, *GetRewordDetailRequest) (*GetRewordDetailReply, error)
	// 幸运大抽奖详情页
	GrandLuckyDraw(context.Context, *GrandLuckyDrawRequest) (*GrandLuckyDrawReply, error)
	// 开始抽奖
	StartDraw(context.Context, *GrandLuckyDrawRequest) (*StartDrawReply, error)
	// 入金一手
	GetDepositDetail(context.Context, *GetDepositDetailRequest) (*GetDepositDetailReply, error)
	// 搜索交易商
	SearchTrader(context.Context, *SearchTraderRequest) (*SearchTraderReply, error)
	// 轮播图和广告
	GetBanner(context.Context, *GetBannerRequest) (*GetBannerReply, error)
	// 给用户助力
	Assist(context.Context, *AssistRequest) (*AssistReply, error)
	// 观看直播任务完成
	WatchLiveCompleted(context.Context, *WatchLiveRequest) (*WatchLiveReply, error)
	// 好友助力
	FriendAssistance(context.Context, *FriendAssistanceRequest) (*FriendAssistanceReply, error)
	// 获取邀请奖励弹窗数据
	GetInvitationPopupData(context.Context, *GetInvitationPopupDataRequest) (*GetInvitationPopupDataReply, error)
	// 获取邀请奖励领取banner数据
	GetInviteRewardBannerData(context.Context, *GetInviteRewardBannerDataRequest) (*GetInviteRewardBannerDataReply, error)
	// 获取分享推广链接数据
	GetShareLinkData(context.Context, *GetShareLinkDataRequest) (*GetShareLinkDataReply, error)
	// 获取邀请用户记录数据
	GetInvitedRecordData(context.Context, *GetInvitedRecordDataRequest) (*GetInvitedRecordDataReply, error)
	// 获取用户vps等级
	GetVpsLevel(context.Context, *common.EmptyRequest) (*GetVpsLevelReply, error)
	// ----------------------------------答题模块----------------------------------//
	// 获取试题数据
	GetQuizInfo(context.Context, *common.EmptyRequest) (*GetQuizInfoReply, error)
	// 提交试卷
	SubmitQuiz(context.Context, *SubmitQuizRequest) (*SubmitQuizReply, error)
	// 获取试卷记录
	GetQuizRecord(context.Context, *common.EmptyRequest) (*GetQuizRecordReply, error)
	// ----------------------------------答题模块----------------------------------//
	// ----------------------------------奖励配置----------------------------------//
	GetInviterActivityTime(context.Context, *common.EmptyRequest) (*GetInviterActivityTimeReply, error)
	UpdateInviterActivityTime(context.Context, *UpdateInviterActivityTimeRequest) (*UpdateInviterActivityTimeReply, error)
	GetUserDivisionRewardLevelInfo(context.Context, *common.EmptyRequest) (*GetUserDivisionRewardLevelInfoReply, error)
	// 新增用户分裂奖励配置
	CreateUserDivisionRewardLevel(context.Context, *CreateUserDivisionRewardLevelRequest) (*CreateUserDivisionRewardLevelReply, error)
	// 修改用户分裂奖励配置
	UpdateUserDivisionRewardLevel(context.Context, *UpdateUserDivisionRewardLevelRequest) (*common.EmptyReply, error)
	// 删除用户分裂奖励配置
	DeleteUserDivisionRewardLevel(context.Context, *DeleteUserDivisionRewardLevelRequest) (*common.EmptyReply, error)
	// ----------------------------------奖励配置----------------------------------//
	// ----------------------------------邀请----------------------------------//
	// 新增用户分裂邀请记录
	CreateUserDivisionInvitation(context.Context, *CreateUserDivisionInvitationRequest) (*CreateUserDivisionInvitationReply, error)
	// 获取用户分裂邀请人统计数据列表
	GetUserDivisionInviterStatisticsInfo(context.Context, *GetUserDivisionInviterStatisticsInfoRequest) (*GetUserDivisionInviterStatisticsInfoReply, error)
	// 获取用户分裂被邀请人数据列表
	GetUserDivisionInviteeInfo(context.Context, *GetUserDivisionInviteeInfoRequest) (*GetUserDivisionInviteeInfoReply, error)
	// 获取邀请活动VPS升级记录列表
	GetUserDivisionActivityList(context.Context, *GetUserDivisionActivityListRequest) (*GetUserDivisionActivityListReply, error)
	// ----------------------------------邀请----------------------------------//
	// ----------------------------------裂变流程----------------------------------//
	// 裂变活动详情
	GetUserDivisionActivityInfo(context.Context, *common.EmptyRequest) (*GetUserDivisionActivityInfoReply, error)
	// 裂变活动入口
	GetUserDivisionEntry(context.Context, *common.EmptyRequest) (*GetUserDivisionEntryReply, error)
	// 升级VPS
	UpgradeVPS(context.Context, *UpgradeVPSRequest) (*UpgradeVPSReply, error)
	// VPS升级回调接口
	PostUpgradeVPSStatus(context.Context, *PostUpgradeVPSStatusRequest) (*PostUpgradeVPSStatusReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedServiceServer) StringReply(context.Context, *StringReplyRequest) (*common.StringReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StringReply not implemented")
}
func (UnimplementedServiceServer) GetIdentityRule(context.Context, *GetIdentityRuleRequest) (*GetIdentityRuleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentityRule not implemented")
}
func (UnimplementedServiceServer) GetUserGrowthDetail(context.Context, *GetUserGrowthDetailRequest) (*GetUserGrowthDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserGrowthDetail not implemented")
}
func (UnimplementedServiceServer) GetGrowthCenterEntry(context.Context, *GetGrowthCenterEntryRequest) (*GetGrowthCenterEntryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGrowthCenterEntry not implemented")
}
func (UnimplementedServiceServer) GetIdentityCarousel(context.Context, *GetIdentityCarouselRequest) (*GetIdentityCarouselReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentityCarousel not implemented")
}
func (UnimplementedServiceServer) GetIdentityShare(context.Context, *GetIdentityShareRequest) (*GetIdentityShareReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIdentityShare not implemented")
}
func (UnimplementedServiceServer) GetUpgradeIdentity(context.Context, *GetUpgradeIdentityRequest) (*GetUpgradeIdentityReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUpgradeIdentity not implemented")
}
func (UnimplementedServiceServer) PostUserIdentitySwitch(context.Context, *PostUserIdentitySwitchRequest) (*PostUserIdentitySwitchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostUserIdentitySwitch not implemented")
}
func (UnimplementedServiceServer) GetActivityMain(context.Context, *GetActivityMainRequest) (*GetActivityMainReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActivityMain not implemented")
}
func (UnimplementedServiceServer) GetActivityShare(context.Context, *GetActivityShareRequest) (*GetActivityShareReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActivityShare not implemented")
}
func (UnimplementedServiceServer) GetContent(context.Context, *GetContentRequest) (*GetContentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetContent not implemented")
}
func (UnimplementedServiceServer) GetRecommendTrader(context.Context, *GetRecommendTraderRequest) (*GetRecommendTraderReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommendTrader not implemented")
}
func (UnimplementedServiceServer) GetGlobalTrader(context.Context, *GetGlobalTraderRequest) (*GetGlobalTraderReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGlobalTrader not implemented")
}
func (UnimplementedServiceServer) GetTraderActivityPage(context.Context, *GetTraderActivityPageRequest) (*GetTraderActivityPageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTraderActivityPage not implemented")
}
func (UnimplementedServiceServer) GetRecommenderList(context.Context, *GetRecommenderListRequest) (*GetRecommenderListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecommenderList not implemented")
}
func (UnimplementedServiceServer) GetRewordPoolDetail(context.Context, *GetRewordPoolDetailRequest) (*GetRewordPoolDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewordPoolDetail not implemented")
}
func (UnimplementedServiceServer) UserCheckIn(context.Context, *UserCheckInRequest) (*UserCheckInReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserCheckIn not implemented")
}
func (UnimplementedServiceServer) GetRewordDetail(context.Context, *GetRewordDetailRequest) (*GetRewordDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRewordDetail not implemented")
}
func (UnimplementedServiceServer) GrandLuckyDraw(context.Context, *GrandLuckyDrawRequest) (*GrandLuckyDrawReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GrandLuckyDraw not implemented")
}
func (UnimplementedServiceServer) StartDraw(context.Context, *GrandLuckyDrawRequest) (*StartDrawReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDraw not implemented")
}
func (UnimplementedServiceServer) GetDepositDetail(context.Context, *GetDepositDetailRequest) (*GetDepositDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDepositDetail not implemented")
}
func (UnimplementedServiceServer) SearchTrader(context.Context, *SearchTraderRequest) (*SearchTraderReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTrader not implemented")
}
func (UnimplementedServiceServer) GetBanner(context.Context, *GetBannerRequest) (*GetBannerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBanner not implemented")
}
func (UnimplementedServiceServer) Assist(context.Context, *AssistRequest) (*AssistReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Assist not implemented")
}
func (UnimplementedServiceServer) WatchLiveCompleted(context.Context, *WatchLiveRequest) (*WatchLiveReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WatchLiveCompleted not implemented")
}
func (UnimplementedServiceServer) FriendAssistance(context.Context, *FriendAssistanceRequest) (*FriendAssistanceReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FriendAssistance not implemented")
}
func (UnimplementedServiceServer) GetInvitationPopupData(context.Context, *GetInvitationPopupDataRequest) (*GetInvitationPopupDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitationPopupData not implemented")
}
func (UnimplementedServiceServer) GetInviteRewardBannerData(context.Context, *GetInviteRewardBannerDataRequest) (*GetInviteRewardBannerDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInviteRewardBannerData not implemented")
}
func (UnimplementedServiceServer) GetShareLinkData(context.Context, *GetShareLinkDataRequest) (*GetShareLinkDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShareLinkData not implemented")
}
func (UnimplementedServiceServer) GetInvitedRecordData(context.Context, *GetInvitedRecordDataRequest) (*GetInvitedRecordDataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitedRecordData not implemented")
}
func (UnimplementedServiceServer) GetVpsLevel(context.Context, *common.EmptyRequest) (*GetVpsLevelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVpsLevel not implemented")
}
func (UnimplementedServiceServer) GetQuizInfo(context.Context, *common.EmptyRequest) (*GetQuizInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuizInfo not implemented")
}
func (UnimplementedServiceServer) SubmitQuiz(context.Context, *SubmitQuizRequest) (*SubmitQuizReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubmitQuiz not implemented")
}
func (UnimplementedServiceServer) GetQuizRecord(context.Context, *common.EmptyRequest) (*GetQuizRecordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQuizRecord not implemented")
}
func (UnimplementedServiceServer) GetInviterActivityTime(context.Context, *common.EmptyRequest) (*GetInviterActivityTimeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInviterActivityTime not implemented")
}
func (UnimplementedServiceServer) UpdateInviterActivityTime(context.Context, *UpdateInviterActivityTimeRequest) (*UpdateInviterActivityTimeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateInviterActivityTime not implemented")
}
func (UnimplementedServiceServer) GetUserDivisionRewardLevelInfo(context.Context, *common.EmptyRequest) (*GetUserDivisionRewardLevelInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDivisionRewardLevelInfo not implemented")
}
func (UnimplementedServiceServer) CreateUserDivisionRewardLevel(context.Context, *CreateUserDivisionRewardLevelRequest) (*CreateUserDivisionRewardLevelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserDivisionRewardLevel not implemented")
}
func (UnimplementedServiceServer) UpdateUserDivisionRewardLevel(context.Context, *UpdateUserDivisionRewardLevelRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserDivisionRewardLevel not implemented")
}
func (UnimplementedServiceServer) DeleteUserDivisionRewardLevel(context.Context, *DeleteUserDivisionRewardLevelRequest) (*common.EmptyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserDivisionRewardLevel not implemented")
}
func (UnimplementedServiceServer) CreateUserDivisionInvitation(context.Context, *CreateUserDivisionInvitationRequest) (*CreateUserDivisionInvitationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserDivisionInvitation not implemented")
}
func (UnimplementedServiceServer) GetUserDivisionInviterStatisticsInfo(context.Context, *GetUserDivisionInviterStatisticsInfoRequest) (*GetUserDivisionInviterStatisticsInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDivisionInviterStatisticsInfo not implemented")
}
func (UnimplementedServiceServer) GetUserDivisionInviteeInfo(context.Context, *GetUserDivisionInviteeInfoRequest) (*GetUserDivisionInviteeInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDivisionInviteeInfo not implemented")
}
func (UnimplementedServiceServer) GetUserDivisionActivityList(context.Context, *GetUserDivisionActivityListRequest) (*GetUserDivisionActivityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDivisionActivityList not implemented")
}
func (UnimplementedServiceServer) GetUserDivisionActivityInfo(context.Context, *common.EmptyRequest) (*GetUserDivisionActivityInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDivisionActivityInfo not implemented")
}
func (UnimplementedServiceServer) GetUserDivisionEntry(context.Context, *common.EmptyRequest) (*GetUserDivisionEntryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserDivisionEntry not implemented")
}
func (UnimplementedServiceServer) UpgradeVPS(context.Context, *UpgradeVPSRequest) (*UpgradeVPSReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeVPS not implemented")
}
func (UnimplementedServiceServer) PostUpgradeVPSStatus(context.Context, *PostUpgradeVPSStatusRequest) (*PostUpgradeVPSStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostUpgradeVPSStatus not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserInfo(ctx, req.(*GetUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_StringReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StringReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).StringReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_StringReply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).StringReply(ctx, req.(*StringReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetIdentityRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentityRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetIdentityRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetIdentityRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetIdentityRule(ctx, req.(*GetIdentityRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserGrowthDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGrowthDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserGrowthDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserGrowthDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserGrowthDetail(ctx, req.(*GetUserGrowthDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetGrowthCenterEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrowthCenterEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetGrowthCenterEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetGrowthCenterEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetGrowthCenterEntry(ctx, req.(*GetGrowthCenterEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetIdentityCarousel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentityCarouselRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetIdentityCarousel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetIdentityCarousel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetIdentityCarousel(ctx, req.(*GetIdentityCarouselRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetIdentityShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIdentityShareRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetIdentityShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetIdentityShare_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetIdentityShare(ctx, req.(*GetIdentityShareRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUpgradeIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUpgradeIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUpgradeIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUpgradeIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUpgradeIdentity(ctx, req.(*GetUpgradeIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_PostUserIdentitySwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostUserIdentitySwitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).PostUserIdentitySwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_PostUserIdentitySwitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).PostUserIdentitySwitch(ctx, req.(*PostUserIdentitySwitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetActivityMain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityMainRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetActivityMain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetActivityMain_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetActivityMain(ctx, req.(*GetActivityMainRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetActivityShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityShareRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetActivityShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetActivityShare_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetActivityShare(ctx, req.(*GetActivityShareRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetContentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetContent(ctx, req.(*GetContentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRecommendTrader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendTraderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRecommendTrader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRecommendTrader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRecommendTrader(ctx, req.(*GetRecommendTraderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetGlobalTrader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGlobalTraderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetGlobalTrader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetGlobalTrader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetGlobalTrader(ctx, req.(*GetGlobalTraderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetTraderActivityPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTraderActivityPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetTraderActivityPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetTraderActivityPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetTraderActivityPage(ctx, req.(*GetTraderActivityPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRecommenderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommenderListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRecommenderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRecommenderList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRecommenderList(ctx, req.(*GetRecommenderListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRewordPoolDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewordPoolDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRewordPoolDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRewordPoolDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRewordPoolDetail(ctx, req.(*GetRewordPoolDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserCheckInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserCheckIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserCheckIn(ctx, req.(*UserCheckInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRewordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRewordDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRewordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRewordDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRewordDetail(ctx, req.(*GetRewordDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GrandLuckyDraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrandLuckyDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GrandLuckyDraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GrandLuckyDraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GrandLuckyDraw(ctx, req.(*GrandLuckyDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_StartDraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrandLuckyDrawRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).StartDraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_StartDraw_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).StartDraw(ctx, req.(*GrandLuckyDrawRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetDepositDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDepositDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetDepositDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetDepositDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetDepositDetail(ctx, req.(*GetDepositDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SearchTrader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTraderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SearchTrader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SearchTrader_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SearchTrader(ctx, req.(*SearchTraderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetBanner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetBanner(ctx, req.(*GetBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_Assist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Assist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Assist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Assist(ctx, req.(*AssistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_WatchLiveCompleted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WatchLiveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).WatchLiveCompleted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_WatchLiveCompleted_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).WatchLiveCompleted(ctx, req.(*WatchLiveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FriendAssistance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FriendAssistanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FriendAssistance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FriendAssistance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FriendAssistance(ctx, req.(*FriendAssistanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInvitationPopupData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitationPopupDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInvitationPopupData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInvitationPopupData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInvitationPopupData(ctx, req.(*GetInvitationPopupDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInviteRewardBannerData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInviteRewardBannerDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInviteRewardBannerData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInviteRewardBannerData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInviteRewardBannerData(ctx, req.(*GetInviteRewardBannerDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetShareLinkData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetShareLinkDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetShareLinkData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetShareLinkData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetShareLinkData(ctx, req.(*GetShareLinkDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInvitedRecordData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitedRecordDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInvitedRecordData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInvitedRecordData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInvitedRecordData(ctx, req.(*GetInvitedRecordDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetVpsLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetVpsLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetVpsLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetVpsLevel(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetQuizInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetQuizInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetQuizInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetQuizInfo(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SubmitQuiz_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitQuizRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SubmitQuiz(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SubmitQuiz_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SubmitQuiz(ctx, req.(*SubmitQuizRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetQuizRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetQuizRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetQuizRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetQuizRecord(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetInviterActivityTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetInviterActivityTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetInviterActivityTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetInviterActivityTime(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UpdateInviterActivityTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateInviterActivityTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UpdateInviterActivityTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UpdateInviterActivityTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UpdateInviterActivityTime(ctx, req.(*UpdateInviterActivityTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserDivisionRewardLevelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserDivisionRewardLevelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserDivisionRewardLevelInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserDivisionRewardLevelInfo(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CreateUserDivisionRewardLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserDivisionRewardLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CreateUserDivisionRewardLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CreateUserDivisionRewardLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CreateUserDivisionRewardLevel(ctx, req.(*CreateUserDivisionRewardLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UpdateUserDivisionRewardLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserDivisionRewardLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UpdateUserDivisionRewardLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UpdateUserDivisionRewardLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UpdateUserDivisionRewardLevel(ctx, req.(*UpdateUserDivisionRewardLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_DeleteUserDivisionRewardLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserDivisionRewardLevelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).DeleteUserDivisionRewardLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_DeleteUserDivisionRewardLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).DeleteUserDivisionRewardLevel(ctx, req.(*DeleteUserDivisionRewardLevelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CreateUserDivisionInvitation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserDivisionInvitationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CreateUserDivisionInvitation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CreateUserDivisionInvitation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CreateUserDivisionInvitation(ctx, req.(*CreateUserDivisionInvitationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserDivisionInviterStatisticsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDivisionInviterStatisticsInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserDivisionInviterStatisticsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserDivisionInviterStatisticsInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserDivisionInviterStatisticsInfo(ctx, req.(*GetUserDivisionInviterStatisticsInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserDivisionInviteeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDivisionInviteeInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserDivisionInviteeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserDivisionInviteeInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserDivisionInviteeInfo(ctx, req.(*GetUserDivisionInviteeInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserDivisionActivityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserDivisionActivityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserDivisionActivityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserDivisionActivityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserDivisionActivityList(ctx, req.(*GetUserDivisionActivityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserDivisionActivityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserDivisionActivityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserDivisionActivityInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserDivisionActivityInfo(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserDivisionEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserDivisionEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserDivisionEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserDivisionEntry(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UpgradeVPS_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeVPSRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UpgradeVPS(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UpgradeVPS_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UpgradeVPS(ctx, req.(*UpgradeVPSRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_PostUpgradeVPSStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostUpgradeVPSStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).PostUpgradeVPSStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_PostUpgradeVPSStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).PostUpgradeVPSStatus(ctx, req.(*PostUpgradeVPSStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.user_growth_center.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _Service_GetUserInfo_Handler,
		},
		{
			MethodName: "StringReply",
			Handler:    _Service_StringReply_Handler,
		},
		{
			MethodName: "GetIdentityRule",
			Handler:    _Service_GetIdentityRule_Handler,
		},
		{
			MethodName: "GetUserGrowthDetail",
			Handler:    _Service_GetUserGrowthDetail_Handler,
		},
		{
			MethodName: "GetGrowthCenterEntry",
			Handler:    _Service_GetGrowthCenterEntry_Handler,
		},
		{
			MethodName: "GetIdentityCarousel",
			Handler:    _Service_GetIdentityCarousel_Handler,
		},
		{
			MethodName: "GetIdentityShare",
			Handler:    _Service_GetIdentityShare_Handler,
		},
		{
			MethodName: "GetUpgradeIdentity",
			Handler:    _Service_GetUpgradeIdentity_Handler,
		},
		{
			MethodName: "PostUserIdentitySwitch",
			Handler:    _Service_PostUserIdentitySwitch_Handler,
		},
		{
			MethodName: "GetActivityMain",
			Handler:    _Service_GetActivityMain_Handler,
		},
		{
			MethodName: "GetActivityShare",
			Handler:    _Service_GetActivityShare_Handler,
		},
		{
			MethodName: "GetContent",
			Handler:    _Service_GetContent_Handler,
		},
		{
			MethodName: "GetRecommendTrader",
			Handler:    _Service_GetRecommendTrader_Handler,
		},
		{
			MethodName: "GetGlobalTrader",
			Handler:    _Service_GetGlobalTrader_Handler,
		},
		{
			MethodName: "GetTraderActivityPage",
			Handler:    _Service_GetTraderActivityPage_Handler,
		},
		{
			MethodName: "GetRecommenderList",
			Handler:    _Service_GetRecommenderList_Handler,
		},
		{
			MethodName: "GetRewordPoolDetail",
			Handler:    _Service_GetRewordPoolDetail_Handler,
		},
		{
			MethodName: "UserCheckIn",
			Handler:    _Service_UserCheckIn_Handler,
		},
		{
			MethodName: "GetRewordDetail",
			Handler:    _Service_GetRewordDetail_Handler,
		},
		{
			MethodName: "GrandLuckyDraw",
			Handler:    _Service_GrandLuckyDraw_Handler,
		},
		{
			MethodName: "StartDraw",
			Handler:    _Service_StartDraw_Handler,
		},
		{
			MethodName: "GetDepositDetail",
			Handler:    _Service_GetDepositDetail_Handler,
		},
		{
			MethodName: "SearchTrader",
			Handler:    _Service_SearchTrader_Handler,
		},
		{
			MethodName: "GetBanner",
			Handler:    _Service_GetBanner_Handler,
		},
		{
			MethodName: "Assist",
			Handler:    _Service_Assist_Handler,
		},
		{
			MethodName: "WatchLiveCompleted",
			Handler:    _Service_WatchLiveCompleted_Handler,
		},
		{
			MethodName: "FriendAssistance",
			Handler:    _Service_FriendAssistance_Handler,
		},
		{
			MethodName: "GetInvitationPopupData",
			Handler:    _Service_GetInvitationPopupData_Handler,
		},
		{
			MethodName: "GetInviteRewardBannerData",
			Handler:    _Service_GetInviteRewardBannerData_Handler,
		},
		{
			MethodName: "GetShareLinkData",
			Handler:    _Service_GetShareLinkData_Handler,
		},
		{
			MethodName: "GetInvitedRecordData",
			Handler:    _Service_GetInvitedRecordData_Handler,
		},
		{
			MethodName: "GetVpsLevel",
			Handler:    _Service_GetVpsLevel_Handler,
		},
		{
			MethodName: "GetQuizInfo",
			Handler:    _Service_GetQuizInfo_Handler,
		},
		{
			MethodName: "SubmitQuiz",
			Handler:    _Service_SubmitQuiz_Handler,
		},
		{
			MethodName: "GetQuizRecord",
			Handler:    _Service_GetQuizRecord_Handler,
		},
		{
			MethodName: "GetInviterActivityTime",
			Handler:    _Service_GetInviterActivityTime_Handler,
		},
		{
			MethodName: "UpdateInviterActivityTime",
			Handler:    _Service_UpdateInviterActivityTime_Handler,
		},
		{
			MethodName: "GetUserDivisionRewardLevelInfo",
			Handler:    _Service_GetUserDivisionRewardLevelInfo_Handler,
		},
		{
			MethodName: "CreateUserDivisionRewardLevel",
			Handler:    _Service_CreateUserDivisionRewardLevel_Handler,
		},
		{
			MethodName: "UpdateUserDivisionRewardLevel",
			Handler:    _Service_UpdateUserDivisionRewardLevel_Handler,
		},
		{
			MethodName: "DeleteUserDivisionRewardLevel",
			Handler:    _Service_DeleteUserDivisionRewardLevel_Handler,
		},
		{
			MethodName: "CreateUserDivisionInvitation",
			Handler:    _Service_CreateUserDivisionInvitation_Handler,
		},
		{
			MethodName: "GetUserDivisionInviterStatisticsInfo",
			Handler:    _Service_GetUserDivisionInviterStatisticsInfo_Handler,
		},
		{
			MethodName: "GetUserDivisionInviteeInfo",
			Handler:    _Service_GetUserDivisionInviteeInfo_Handler,
		},
		{
			MethodName: "GetUserDivisionActivityList",
			Handler:    _Service_GetUserDivisionActivityList_Handler,
		},
		{
			MethodName: "GetUserDivisionActivityInfo",
			Handler:    _Service_GetUserDivisionActivityInfo_Handler,
		},
		{
			MethodName: "GetUserDivisionEntry",
			Handler:    _Service_GetUserDivisionEntry_Handler,
		},
		{
			MethodName: "UpgradeVPS",
			Handler:    _Service_UpgradeVPS_Handler,
		},
		{
			MethodName: "PostUpgradeVPSStatus",
			Handler:    _Service_PostUpgradeVPSStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user_growth_center/v1/service.proto",
}
