// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: wiki_biz_agg/v1/annual_report/service.proto

package annual_report

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "gold_store/api/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_wiki_biz_agg_v1_annual_report_service_proto protoreflect.FileDescriptor

var file_wiki_biz_agg_v1_annual_report_service_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x61, 0x67, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x62, 0x69, 0x7a, 0x5f, 0x61, 0x67, 0x67, 0x2e,
	0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x62, 0x69, 0x7a, 0x5f,
	0x61, 0x67, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x42, 0x31, 0x5a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x62, 0x69,
	0x7a, 0x5f, 0x61, 0x67, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x3b, 0x61, 0x6e, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_wiki_biz_agg_v1_annual_report_service_proto_goTypes = []interface{}{}
var file_wiki_biz_agg_v1_annual_report_service_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_wiki_biz_agg_v1_annual_report_service_proto_init() }
func file_wiki_biz_agg_v1_annual_report_service_proto_init() {
	if File_wiki_biz_agg_v1_annual_report_service_proto != nil {
		return
	}
	file_wiki_biz_agg_v1_annual_report_models_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_wiki_biz_agg_v1_annual_report_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wiki_biz_agg_v1_annual_report_service_proto_goTypes,
		DependencyIndexes: file_wiki_biz_agg_v1_annual_report_service_proto_depIdxs,
	}.Build()
	File_wiki_biz_agg_v1_annual_report_service_proto = out.File
	file_wiki_biz_agg_v1_annual_report_service_proto_rawDesc = nil
	file_wiki_biz_agg_v1_annual_report_service_proto_goTypes = nil
	file_wiki_biz_agg_v1_annual_report_service_proto_depIdxs = nil
}
