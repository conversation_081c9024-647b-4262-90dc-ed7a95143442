// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: wiki_biz_agg/v1/web/service.proto

package web

import (
	grpc "google.golang.org/grpc"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const ()

// WebServiceClient is the client API for WebService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebServiceClient interface {
}

type webServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWebServiceClient(cc grpc.ClientConnInterface) WebServiceClient {
	return &webServiceClient{cc}
}

// WebServiceServer is the server API for WebService service.
// All implementations must embed UnimplementedWebServiceServer
// for forward compatibility
type WebServiceServer interface {
	mustEmbedUnimplementedWebServiceServer()
}

// UnimplementedWebServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWebServiceServer struct {
}

func (UnimplementedWebServiceServer) mustEmbedUnimplementedWebServiceServer() {}

// UnsafeWebServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebServiceServer will
// result in compilation errors.
type UnsafeWebServiceServer interface {
	mustEmbedUnimplementedWebServiceServer()
}

func RegisterWebServiceServer(s grpc.ServiceRegistrar, srv WebServiceServer) {
	s.RegisterService(&WebService_ServiceDesc, srv)
}

// WebService_ServiceDesc is the grpc.ServiceDesc for WebService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WebService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wiki_biz_agg.v1.WebService",
	HandlerType: (*WebServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "wiki_biz_agg/v1/web/service.proto",
}
