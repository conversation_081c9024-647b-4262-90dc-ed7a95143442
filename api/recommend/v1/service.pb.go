// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: recommend/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ------
type ErrorReplyCopy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,json=code,proto3" json:"code"`
	Reason  string `protobuf:"bytes,2,opt,name=reason,json=reason,proto3" json:"reason"`
	Message string `protobuf:"bytes,3,opt,name=message,json=message,proto3" json:"message"`
	Time    int64  `protobuf:"varint,4,opt,name=time,json=time,proto3" json:"time"`
}

func (x *ErrorReplyCopy) Reset() {
	*x = ErrorReplyCopy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ErrorReplyCopy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorReplyCopy) ProtoMessage() {}

func (x *ErrorReplyCopy) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorReplyCopy.ProtoReflect.Descriptor instead.
func (*ErrorReplyCopy) Descriptor() ([]byte, []int) {
	return file_recommend_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorReplyCopy) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorReplyCopy) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ErrorReplyCopy) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorReplyCopy) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_recommend_v1_service_proto protoreflect.FileDescriptor

var file_recommend_v1_service_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69,
	0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x76, 0x31, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6a, 0x0a, 0x0e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x32, 0x98, 0x1e, 0x0a, 0x07, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x12, 0x68, 0x0a,
	0x09, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x6c, 0x0a, 0x0b, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x56, 0x32, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x15,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x8e, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65,
	0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63,
	0x65, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x14, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x12, 0x0c, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x64, 0x48,
	0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65,
	0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x12, 0x36,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x7b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x7d, 0x2f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x7b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0xa1, 0x01, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x48,
	0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x56, 0x32, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69,
	0x6e, 0x64, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64,
	0x4e, 0x65, 0x77, 0x56, 0x32, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x38, 0x12, 0x36, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x7b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x7d, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2f, 0x7b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x88, 0x01, 0x0a, 0x11, 0x46,
	0x69, 0x6e, 0x64, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x69, 0x6e, 0x64, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01,
	0x2a, 0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x2f, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x12, 0x70, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x48,
	0x6f, 0x6d, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x48, 0x6f, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x50,
	0x6f, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x5f, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x15, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x7d, 0x0a, 0x0f, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x7d, 0x0a, 0x0e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x75,
	0x73, 0x65, 0x72, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x79,
	0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e,
	0x64, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2f, 0x68, 0x6f, 0x74, 0x12, 0x79, 0x0a, 0x0d, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14,
	0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x12, 0x70, 0x0a, 0x0f, 0x53, 0x6b, 0x79, 0x4c, 0x69, 0x6e, 0x65, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x75, 0x0a, 0x0c, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f,
	0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x8f, 0x01,
	0x0a, 0x11, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12,
	0x8d, 0x01, 0x0a, 0x11, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x61,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x2f, 0x68, 0x6f, 0x74, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x78, 0x0a, 0x0c, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x61, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x2f, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x0e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x61, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x99, 0x01, 0x0a,
	0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4e,
	0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x2f, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x79,
	0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x12,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x12, 0x89, 0x01, 0x0a, 0x11, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x12,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61,
	0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f,
	0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x69, 0x74, 0x79, 0x12, 0x76, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x75, 0x0a,
	0x0c, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x13, 0x12, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x62, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x12, 0x8e, 0x01, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x12, 0x2b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2f, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x6d, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x17, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x76, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x61, 0x6c,
	0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x73, 0x65, 0x72, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2f, 0x72, 0x65, 0x61, 0x6c, 0x12, 0x84, 0x01, 0x0a,
	0x11, 0x52, 0x65, 0x73, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69,
	0x6f, 0x72, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13,
	0x2a, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x62, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x42, 0xd5, 0x05, 0x92, 0x41, 0xbc, 0x05, 0x12, 0x13, 0x0a, 0x0c, 0xe6, 0x8e,
	0xa8, 0xe8, 0x8d, 0x90, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0x32, 0x03, 0x31, 0x2e, 0x30, 0x2a,
	0x01, 0x01, 0x32, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x52, 0xd5, 0x01, 0x0a, 0x03, 0x34, 0x30, 0x30, 0x12, 0xcd,
	0x01, 0x0a, 0x3c, 0xe5, 0x8f, 0x82, 0xe6, 0x95, 0xb0, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xef,
	0xbc, 0x8c, 0xe6, 0xa3, 0x80, 0xe6, 0x9f, 0xa5, 0xe5, 0x8f, 0x82, 0xe6, 0x95, 0xb0, 0xe6, 0xa0,
	0xbc, 0xe5, 0xbc, 0x8f, 0xe8, 0xb7, 0x9f, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0xad, 0xa3, 0xe7, 0xa1, 0xae, 0x12,
	0x28, 0x0a, 0x26, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x43, 0x6f, 0x70, 0x79, 0x9a, 0x02, 0x01, 0x06, 0x22, 0x63, 0x0a, 0x10, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x4f, 0x7b,
	0x22, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3a, 0x34, 0x30, 0x30, 0x2c, 0x22, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0x3a, 0x22, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53, 0x49, 0x47,
	0x4e, 0x22, 0x2c, 0x22, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3a, 0x22, 0x69, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x20, 0x61, 0x72, 0x67, 0x73, 0x22, 0x2c, 0x22, 0x74, 0x69, 0x6d,
	0x65, 0x22, 0x3a, 0x31, 0x36, 0x39, 0x35, 0x37, 0x32, 0x30, 0x35, 0x38, 0x34, 0x7d, 0x52, 0xb4,
	0x01, 0x0a, 0x03, 0x34, 0x30, 0x34, 0x12, 0xac, 0x01, 0x0a, 0x0f, 0xe8, 0xb5, 0x84, 0xe6, 0xba,
	0x90, 0xe4, 0xb8, 0x8d, 0xe5, 0xad, 0x98, 0xe5, 0x9c, 0xa8, 0x12, 0x28, 0x0a, 0x26, 0x1a, 0x20,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x70, 0x79,
	0x9a, 0x02, 0x01, 0x06, 0x22, 0x6f, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x5b, 0x7b, 0x22, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x3a, 0x34, 0x30, 0x34, 0x2c, 0x22, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x3a, 0x22,
	0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x22, 0x2c, 0x22, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3a, 0x22, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x20, 0x6e, 0x6f, 0x74, 0x20, 0x66, 0x6f, 0x75, 0x6e,
	0x64, 0x22, 0x2c, 0x22, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x3a, 0x31, 0x36, 0x39, 0x35, 0x37, 0x32,
	0x30, 0x35, 0x38, 0x34, 0x7d, 0x52, 0xbd, 0x01, 0x0a, 0x03, 0x35, 0x30, 0x30, 0x12, 0xb5, 0x01,
	0x0a, 0x12, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x86, 0x85, 0xe9, 0x83, 0xa8, 0xe9, 0x94,
	0x99, 0xe8, 0xaf, 0xaf, 0x12, 0x28, 0x0a, 0x26, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x70, 0x79, 0x9a, 0x02, 0x01, 0x06, 0x22, 0x75,
	0x0a, 0x10, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6a, 0x73,
	0x6f, 0x6e, 0x12, 0x61, 0x7b, 0x22, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x3a, 0x35, 0x30, 0x30, 0x2c,
	0x22, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x3a, 0x22, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x22,
	0x2c, 0x22, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x3a, 0x22, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x20, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x22, 0x2c, 0x22, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x3a, 0x31, 0x36, 0x39, 0x35, 0x37, 0x32,
	0x30, 0x35, 0x38, 0x34, 0x7d, 0x5a, 0x1d, 0x0a, 0x1b, 0x0a, 0x0a, 0x41, 0x70, 0x69, 0x4b, 0x65,
	0x79, 0x41, 0x75, 0x74, 0x68, 0x12, 0x0d, 0x08, 0x02, 0x1a, 0x07, 0x78, 0x2d, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x20, 0x02, 0x62, 0x10, 0x0a, 0x0e, 0x0a, 0x0a, 0x41, 0x70, 0x69, 0x4b, 0x65, 0x79,
	0x41, 0x75, 0x74, 0x68, 0x12, 0x00, 0x5a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_recommend_v1_service_proto_rawDescOnce sync.Once
	file_recommend_v1_service_proto_rawDescData = file_recommend_v1_service_proto_rawDesc
)

func file_recommend_v1_service_proto_rawDescGZIP() []byte {
	file_recommend_v1_service_proto_rawDescOnce.Do(func() {
		file_recommend_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_recommend_v1_service_proto_rawDescData)
	})
	return file_recommend_v1_service_proto_rawDescData
}

var file_recommend_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_recommend_v1_service_proto_goTypes = []interface{}{
	(*ErrorReplyCopy)(nil),                 // 0: api.recommend.v1.ErrorReplyCopy
	(*common.EmptyRequest)(nil),            // 1: common.EmptyRequest
	(*RecommendRequest)(nil),               // 2: api.recommend.v1.RecommendRequest
	(*RecommendV2Request)(nil),             // 3: api.recommend.v1.RecommendV2Request
	(*FindCommerceByCategoryRequest)(nil),  // 4: api.recommend.v1.FindCommerceByCategoryRequest
	(*FindHotAndNewRequest)(nil),           // 5: api.recommend.v1.FindHotAndNewRequest
	(*FindFollowPublishRequest)(nil),       // 6: api.recommend.v1.FindFollowPublishRequest
	(*TraderHomeRequest)(nil),              // 7: api.recommend.v1.TraderHomeRequest
	(*TraderPostCountRequest)(nil),         // 8: api.recommend.v1.TraderPostCountRequest
	(*SearchRequest)(nil),                  // 9: api.recommend.v1.SearchRequest
	(*FindSearchTitleRequest)(nil),         // 10: api.recommend.v1.FindSearchTitleRequest
	(*InterestedUserRequest)(nil),          // 11: api.recommend.v1.InterestedUserRequest
	(*FindHotContentRequest)(nil),          // 12: api.recommend.v1.FindHotContentRequest
	(*RecommendUserRequest)(nil),           // 13: api.recommend.v1.RecommendUserRequest
	(*ActivityRequest)(nil),                // 14: api.recommend.v1.ActivityRequest
	(*YearlyReportRequest)(nil),            // 15: api.recommend.v1.YearlyReportRequest
	(*RecommendFeedbackRequest)(nil),       // 16: api.recommend.v1.RecommendFeedbackRequest
	(*HotContentRankingRequest)(nil),       // 17: api.recommend.v1.HotContentRankingRequest
	(*RankingScopeRequest)(nil),            // 18: api.recommend.v1.RankingScopeRequest
	(*CreatorRankingRequest)(nil),          // 19: api.recommend.v1.CreatorRankingRequest
	(*CreatorRankingNoticeRequest)(nil),    // 20: api.recommend.v1.CreatorRankingNoticeRequest
	(*SearchTitleAutoCompleteRequest)(nil), // 21: api.recommend.v1.SearchTitleAutoCompleteRequest
	(*RecommendTestRequest)(nil),           // 22: api.recommend.v1.RecommendTestRequest
	(*ContentSimilarityRequest)(nil),       // 23: api.recommend.v1.ContentSimilarityRequest
	(*UserContentRequest)(nil),             // 24: api.recommend.v1.UserContentRequest
	(*UserBehaviorRequest)(nil),            // 25: api.recommend.v1.UserBehaviorRequest
	(*UserOriginBehaviorRequest)(nil),      // 26: api.recommend.v1.UserOriginBehaviorRequest
	(*UserVectorRequest)(nil),              // 27: api.recommend.v1.UserVectorRequest
	(*ResetUserBehaviorRequest)(nil),       // 28: api.recommend.v1.ResetUserBehaviorRequest
	(*common.HealthyReply)(nil),            // 29: common.HealthyReply
	(*RecommendReply)(nil),                 // 30: api.recommend.v1.RecommendReply
	(*FindCommerceByCategoryReply)(nil),    // 31: api.recommend.v1.FindCommerceByCategoryReply
	(*FindHotAndNewReply)(nil),             // 32: api.recommend.v1.FindHotAndNewReply
	(*FindHotAndNewV2Reply)(nil),           // 33: api.recommend.v1.FindHotAndNewV2Reply
	(*FindFollowPublishReply)(nil),         // 34: api.recommend.v1.FindFollowPublishReply
	(*TraderHomeReply)(nil),                // 35: api.recommend.v1.TraderHomeReply
	(*TraderPostCountReply)(nil),           // 36: api.recommend.v1.TraderPostCountReply
	(*SearchReply)(nil),                    // 37: api.recommend.v1.SearchReply
	(*FindSearchTitleReply)(nil),           // 38: api.recommend.v1.FindSearchTitleReply
	(*InterestedUserReply)(nil),            // 39: api.recommend.v1.InterestedUserReply
	(*FindHotContentReply)(nil),            // 40: api.recommend.v1.FindHotContentReply
	(*RecommendUserReply)(nil),             // 41: api.recommend.v1.RecommendUserReply
	(*ActivityReply)(nil),                  // 42: api.recommend.v1.ActivityReply
	(*YearlyReportReply)(nil),              // 43: api.recommend.v1.YearlyReportReply
	(*RecommendFeedbackResponse)(nil),      // 44: api.recommend.v1.RecommendFeedbackResponse
	(*HotContentRankingResponse)(nil),      // 45: api.recommend.v1.HotContentRankingResponse
	(*RankingScopeResponse)(nil),           // 46: api.recommend.v1.RankingScopeResponse
	(*CreatorRankingResponse)(nil),         // 47: api.recommend.v1.CreatorRankingResponse
	(*CreatorRankingNoticeResponse)(nil),   // 48: api.recommend.v1.CreatorRankingNoticeResponse
	(*SearchTitleAutoCompleteReply)(nil),   // 49: api.recommend.v1.SearchTitleAutoCompleteReply
	(*RecommendTestReply)(nil),             // 50: api.recommend.v1.RecommendTestReply
	(*ContentSimilarityReply)(nil),         // 51: api.recommend.v1.ContentSimilarityReply
	(*UserBehaviorReply)(nil),              // 52: api.recommend.v1.UserBehaviorReply
	(*UserOriginBehaviorReply)(nil),        // 53: api.recommend.v1.UserOriginBehaviorReply
	(*UserVectorReply)(nil),                // 54: api.recommend.v1.UserVectorReply
	(*ResetUserBehaviorReply)(nil),         // 55: api.recommend.v1.ResetUserBehaviorReply
}
var file_recommend_v1_service_proto_depIdxs = []int32{
	1,  // 0: api.recommend.v1.Service.Healthy:input_type -> common.EmptyRequest
	2,  // 1: api.recommend.v1.Service.Recommend:input_type -> api.recommend.v1.RecommendRequest
	3,  // 2: api.recommend.v1.Service.RecommendV2:input_type -> api.recommend.v1.RecommendV2Request
	4,  // 3: api.recommend.v1.Service.FindCommerceByCategory:input_type -> api.recommend.v1.FindCommerceByCategoryRequest
	5,  // 4: api.recommend.v1.Service.FindHotAndNew:input_type -> api.recommend.v1.FindHotAndNewRequest
	5,  // 5: api.recommend.v1.Service.FindHotAndNewV2:input_type -> api.recommend.v1.FindHotAndNewRequest
	6,  // 6: api.recommend.v1.Service.FindFollowPublish:input_type -> api.recommend.v1.FindFollowPublishRequest
	7,  // 7: api.recommend.v1.Service.TraderHome:input_type -> api.recommend.v1.TraderHomeRequest
	8,  // 8: api.recommend.v1.Service.TraderPostCount:input_type -> api.recommend.v1.TraderPostCountRequest
	9,  // 9: api.recommend.v1.Service.Search:input_type -> api.recommend.v1.SearchRequest
	10, // 10: api.recommend.v1.Service.FindSearchTitle:input_type -> api.recommend.v1.FindSearchTitleRequest
	11, // 11: api.recommend.v1.Service.InterestedUser:input_type -> api.recommend.v1.InterestedUserRequest
	12, // 12: api.recommend.v1.Service.FindHotContent:input_type -> api.recommend.v1.FindHotContentRequest
	13, // 13: api.recommend.v1.Service.RecommendUser:input_type -> api.recommend.v1.RecommendUserRequest
	14, // 14: api.recommend.v1.Service.SkyLineActivity:input_type -> api.recommend.v1.ActivityRequest
	15, // 15: api.recommend.v1.Service.YearlyReport:input_type -> api.recommend.v1.YearlyReportRequest
	16, // 16: api.recommend.v1.Service.RecommendFeedback:input_type -> api.recommend.v1.RecommendFeedbackRequest
	17, // 17: api.recommend.v1.Service.HotContentRanking:input_type -> api.recommend.v1.HotContentRankingRequest
	18, // 18: api.recommend.v1.Service.RankingScope:input_type -> api.recommend.v1.RankingScopeRequest
	19, // 19: api.recommend.v1.Service.CreatorRanking:input_type -> api.recommend.v1.CreatorRankingRequest
	20, // 20: api.recommend.v1.Service.CreatorRankingNotice:input_type -> api.recommend.v1.CreatorRankingNoticeRequest
	21, // 21: api.recommend.v1.Service.SearchTitleAutoComplete:input_type -> api.recommend.v1.SearchTitleAutoCompleteRequest
	22, // 22: api.recommend.v1.Service.RecommendTest:input_type -> api.recommend.v1.RecommendTestRequest
	23, // 23: api.recommend.v1.Service.ContentSimilarity:input_type -> api.recommend.v1.ContentSimilarityRequest
	24, // 24: api.recommend.v1.Service.UserContent:input_type -> api.recommend.v1.UserContentRequest
	25, // 25: api.recommend.v1.Service.UserBehavior:input_type -> api.recommend.v1.UserBehaviorRequest
	26, // 26: api.recommend.v1.Service.UserOriginBehavior:input_type -> api.recommend.v1.UserOriginBehaviorRequest
	27, // 27: api.recommend.v1.Service.UserVector:input_type -> api.recommend.v1.UserVectorRequest
	27, // 28: api.recommend.v1.Service.UserRealVector:input_type -> api.recommend.v1.UserVectorRequest
	28, // 29: api.recommend.v1.Service.ResetUserBehavior:input_type -> api.recommend.v1.ResetUserBehaviorRequest
	29, // 30: api.recommend.v1.Service.Healthy:output_type -> common.HealthyReply
	30, // 31: api.recommend.v1.Service.Recommend:output_type -> api.recommend.v1.RecommendReply
	30, // 32: api.recommend.v1.Service.RecommendV2:output_type -> api.recommend.v1.RecommendReply
	31, // 33: api.recommend.v1.Service.FindCommerceByCategory:output_type -> api.recommend.v1.FindCommerceByCategoryReply
	32, // 34: api.recommend.v1.Service.FindHotAndNew:output_type -> api.recommend.v1.FindHotAndNewReply
	33, // 35: api.recommend.v1.Service.FindHotAndNewV2:output_type -> api.recommend.v1.FindHotAndNewV2Reply
	34, // 36: api.recommend.v1.Service.FindFollowPublish:output_type -> api.recommend.v1.FindFollowPublishReply
	35, // 37: api.recommend.v1.Service.TraderHome:output_type -> api.recommend.v1.TraderHomeReply
	36, // 38: api.recommend.v1.Service.TraderPostCount:output_type -> api.recommend.v1.TraderPostCountReply
	37, // 39: api.recommend.v1.Service.Search:output_type -> api.recommend.v1.SearchReply
	38, // 40: api.recommend.v1.Service.FindSearchTitle:output_type -> api.recommend.v1.FindSearchTitleReply
	39, // 41: api.recommend.v1.Service.InterestedUser:output_type -> api.recommend.v1.InterestedUserReply
	40, // 42: api.recommend.v1.Service.FindHotContent:output_type -> api.recommend.v1.FindHotContentReply
	41, // 43: api.recommend.v1.Service.RecommendUser:output_type -> api.recommend.v1.RecommendUserReply
	42, // 44: api.recommend.v1.Service.SkyLineActivity:output_type -> api.recommend.v1.ActivityReply
	43, // 45: api.recommend.v1.Service.YearlyReport:output_type -> api.recommend.v1.YearlyReportReply
	44, // 46: api.recommend.v1.Service.RecommendFeedback:output_type -> api.recommend.v1.RecommendFeedbackResponse
	45, // 47: api.recommend.v1.Service.HotContentRanking:output_type -> api.recommend.v1.HotContentRankingResponse
	46, // 48: api.recommend.v1.Service.RankingScope:output_type -> api.recommend.v1.RankingScopeResponse
	47, // 49: api.recommend.v1.Service.CreatorRanking:output_type -> api.recommend.v1.CreatorRankingResponse
	48, // 50: api.recommend.v1.Service.CreatorRankingNotice:output_type -> api.recommend.v1.CreatorRankingNoticeResponse
	49, // 51: api.recommend.v1.Service.SearchTitleAutoComplete:output_type -> api.recommend.v1.SearchTitleAutoCompleteReply
	50, // 52: api.recommend.v1.Service.RecommendTest:output_type -> api.recommend.v1.RecommendTestReply
	51, // 53: api.recommend.v1.Service.ContentSimilarity:output_type -> api.recommend.v1.ContentSimilarityReply
	50, // 54: api.recommend.v1.Service.UserContent:output_type -> api.recommend.v1.RecommendTestReply
	52, // 55: api.recommend.v1.Service.UserBehavior:output_type -> api.recommend.v1.UserBehaviorReply
	53, // 56: api.recommend.v1.Service.UserOriginBehavior:output_type -> api.recommend.v1.UserOriginBehaviorReply
	54, // 57: api.recommend.v1.Service.UserVector:output_type -> api.recommend.v1.UserVectorReply
	54, // 58: api.recommend.v1.Service.UserRealVector:output_type -> api.recommend.v1.UserVectorReply
	55, // 59: api.recommend.v1.Service.ResetUserBehavior:output_type -> api.recommend.v1.ResetUserBehaviorReply
	30, // [30:60] is the sub-list for method output_type
	0,  // [0:30] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_recommend_v1_service_proto_init() }
func file_recommend_v1_service_proto_init() {
	if File_recommend_v1_service_proto != nil {
		return
	}
	file_recommend_v1_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_recommend_v1_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ErrorReplyCopy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_recommend_v1_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_recommend_v1_service_proto_goTypes,
		DependencyIndexes: file_recommend_v1_service_proto_depIdxs,
		MessageInfos:      file_recommend_v1_service_proto_msgTypes,
	}.Build()
	File_recommend_v1_service_proto = out.File
	file_recommend_v1_service_proto_rawDesc = nil
	file_recommend_v1_service_proto_goTypes = nil
	file_recommend_v1_service_proto_depIdxs = nil
}
