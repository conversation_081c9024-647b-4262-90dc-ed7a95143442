// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: recommend/v1/models.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReleaseType int32

const (
	ReleaseType_ReleaseTypeUnknown ReleaseType = 0
	ReleaseType_Commerce           ReleaseType = 1 // 商业
	ReleaseType_Dynamic            ReleaseType = 2 // 动态
)

// Enum value maps for ReleaseType.
var (
	ReleaseType_name = map[int32]string{
		0: "ReleaseTypeUnknown",
		1: "Commerce",
		2: "Dynamic",
	}
	ReleaseType_value = map[string]int32{
		"ReleaseTypeUnknown": 0,
		"Commerce":           1,
		"Dynamic":            2,
	}
)

func (x ReleaseType) Enum() *ReleaseType {
	p := new(ReleaseType)
	*p = x
	return p
}

func (x ReleaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReleaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[0].Descriptor()
}

func (ReleaseType) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[0]
}

func (x ReleaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReleaseType.Descriptor instead.
func (ReleaseType) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{0}
}

type RecommendCategory int32

const (
	RecommendCategory_RecommendCategoryRecommend RecommendCategory = 0 // 全部内容推荐
	RecommendCategory_RecommendCategoryCommerce  RecommendCategory = 1 // 商业
	RecommendCategory_RecommendCategoryDynamic   RecommendCategory = 2 // 动态
	RecommendCategory_RecommendCategoryDiscover  RecommendCategory = 3 // 发现全部(商业+动态)
)

// Enum value maps for RecommendCategory.
var (
	RecommendCategory_name = map[int32]string{
		0: "RecommendCategoryRecommend",
		1: "RecommendCategoryCommerce",
		2: "RecommendCategoryDynamic",
		3: "RecommendCategoryDiscover",
	}
	RecommendCategory_value = map[string]int32{
		"RecommendCategoryRecommend": 0,
		"RecommendCategoryCommerce":  1,
		"RecommendCategoryDynamic":   2,
		"RecommendCategoryDiscover":  3,
	}
)

func (x RecommendCategory) Enum() *RecommendCategory {
	p := new(RecommendCategory)
	*p = x
	return p
}

func (x RecommendCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecommendCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[1].Descriptor()
}

func (RecommendCategory) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[1]
}

func (x RecommendCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecommendCategory.Descriptor instead.
func (RecommendCategory) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{1}
}

type RecommendPosition int32

const (
	RecommendPosition_RecommendPositionDiscoverPage RecommendPosition = 0 // 发现页
	RecommendPosition_RecommendPositionHomePage     RecommendPosition = 1 // 首页
)

// Enum value maps for RecommendPosition.
var (
	RecommendPosition_name = map[int32]string{
		0: "RecommendPositionDiscoverPage",
		1: "RecommendPositionHomePage",
	}
	RecommendPosition_value = map[string]int32{
		"RecommendPositionDiscoverPage": 0,
		"RecommendPositionHomePage":     1,
	}
)

func (x RecommendPosition) Enum() *RecommendPosition {
	p := new(RecommendPosition)
	*p = x
	return p
}

func (x RecommendPosition) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecommendPosition) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[2].Descriptor()
}

func (RecommendPosition) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[2]
}

func (x RecommendPosition) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecommendPosition.Descriptor instead.
func (RecommendPosition) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{2}
}

type UserIdentify int32

const (
	UserIdentify_UserIdentifyInvestor        UserIdentify = 0 // 投资者
	UserIdentify_UserIdentifyKOL             UserIdentify = 1 // KOL
	UserIdentify_UserIdentifyTrader          UserIdentify = 2 // 交易商
	UserIdentify_UserIdentifyServiceProvider UserIdentify = 3 // 服务商
)

// Enum value maps for UserIdentify.
var (
	UserIdentify_name = map[int32]string{
		0: "UserIdentifyInvestor",
		1: "UserIdentifyKOL",
		2: "UserIdentifyTrader",
		3: "UserIdentifyServiceProvider",
	}
	UserIdentify_value = map[string]int32{
		"UserIdentifyInvestor":        0,
		"UserIdentifyKOL":             1,
		"UserIdentifyTrader":          2,
		"UserIdentifyServiceProvider": 3,
	}
)

func (x UserIdentify) Enum() *UserIdentify {
	p := new(UserIdentify)
	*p = x
	return p
}

func (x UserIdentify) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserIdentify) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[3].Descriptor()
}

func (UserIdentify) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[3]
}

func (x UserIdentify) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserIdentify.Descriptor instead.
func (UserIdentify) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{3}
}

// 推荐类型
type RecommendItemType int32

const (
	RecommendItemType_RecommendItemTypeUnknown    RecommendItemType = 0  // 未知类型
	RecommendItemType_RecommendItemTypeArticle    RecommendItemType = 1  // 文章
	RecommendItemType_RecommendItemTypeExposure   RecommendItemType = 2  // 曝光
	RecommendItemType_RecommendItemTypeDiscover   RecommendItemType = 3  // 发现
	RecommendItemType_RecommendItemTypeTrader     RecommendItemType = 4  // 交易商
	RecommendItemType_RecommendItemTypeSurvey     RecommendItemType = 5  // 实勘
	RecommendItemType_RecommendItemTypeMediate    RecommendItemType = 6  // 调解
	RecommendItemType_RecommendItemTypeFlash      RecommendItemType = 7  // 快讯
	RecommendItemType_RecommendItemTypeDisclosure RecommendItemType = 8  // 披露
	RecommendItemType_RecommendItemTypeComment    RecommendItemType = 9  // 评价
	RecommendItemType_RecommendItemTypeService    RecommendItemType = 10 // 服务商
	RecommendItemType_RecommendItemTypeAdvertise  RecommendItemType = 11 // 广告
)

// Enum value maps for RecommendItemType.
var (
	RecommendItemType_name = map[int32]string{
		0:  "RecommendItemTypeUnknown",
		1:  "RecommendItemTypeArticle",
		2:  "RecommendItemTypeExposure",
		3:  "RecommendItemTypeDiscover",
		4:  "RecommendItemTypeTrader",
		5:  "RecommendItemTypeSurvey",
		6:  "RecommendItemTypeMediate",
		7:  "RecommendItemTypeFlash",
		8:  "RecommendItemTypeDisclosure",
		9:  "RecommendItemTypeComment",
		10: "RecommendItemTypeService",
		11: "RecommendItemTypeAdvertise",
	}
	RecommendItemType_value = map[string]int32{
		"RecommendItemTypeUnknown":    0,
		"RecommendItemTypeArticle":    1,
		"RecommendItemTypeExposure":   2,
		"RecommendItemTypeDiscover":   3,
		"RecommendItemTypeTrader":     4,
		"RecommendItemTypeSurvey":     5,
		"RecommendItemTypeMediate":    6,
		"RecommendItemTypeFlash":      7,
		"RecommendItemTypeDisclosure": 8,
		"RecommendItemTypeComment":    9,
		"RecommendItemTypeService":    10,
		"RecommendItemTypeAdvertise":  11,
	}
)

func (x RecommendItemType) Enum() *RecommendItemType {
	p := new(RecommendItemType)
	*p = x
	return p
}

func (x RecommendItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecommendItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[4].Descriptor()
}

func (RecommendItemType) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[4]
}

func (x RecommendItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecommendItemType.Descriptor instead.
func (RecommendItemType) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{4}
}

// 广告跳转类型
type AdvertiseJumpType int32

const (
	AdvertiseJumpType_AdvertiseJumpTypeUnknown          AdvertiseJumpType = 0
	AdvertiseJumpType_AdvertiseJumpTypeTradeDetail      AdvertiseJumpType = 1  // 交易商详情
	AdvertiseJumpType_AdvertiseJumpTypeOuterLink        AdvertiseJumpType = 2  // 外部链接
	AdvertiseJumpType_AdvertiseJumpTypeNewsDetail       AdvertiseJumpType = 3  // 新闻文章详情
	AdvertiseJumpType_AdvertiseJumpTypeSurveyDetail     AdvertiseJumpType = 4  // 实勘详情
	AdvertiseJumpType_AdvertiseJumpTypeVPS              AdvertiseJumpType = 6  // VPS
	AdvertiseJumpType_AdvertiseJumpTypeLiveDetail       AdvertiseJumpType = 10 // 直播详情
	AdvertiseJumpType_AdvertiseJumpTypeLiveList         AdvertiseJumpType = 11 // 直播列表
	AdvertiseJumpType_AdvertiseJumpTypeCommercial       AdvertiseJumpType = 13 // 商业
	AdvertiseJumpType_AdvertiseJumpTypePersonalHomepage AdvertiseJumpType = 14 // 个人主页
	AdvertiseJumpType_AdvertiseJumpTypeServiceProvider  AdvertiseJumpType = 15 // 服务商详情页
	AdvertiseJumpType_AdvertiseJumpTypeRank             AdvertiseJumpType = 16 // 排行榜
	AdvertiseJumpType_AdvertiseJumpTypeH5               AdvertiseJumpType = 17 // H5
	AdvertiseJumpType_AdvertiseJumpTypeTopic            AdvertiseJumpType = 55 // 话题
)

// Enum value maps for AdvertiseJumpType.
var (
	AdvertiseJumpType_name = map[int32]string{
		0:  "AdvertiseJumpTypeUnknown",
		1:  "AdvertiseJumpTypeTradeDetail",
		2:  "AdvertiseJumpTypeOuterLink",
		3:  "AdvertiseJumpTypeNewsDetail",
		4:  "AdvertiseJumpTypeSurveyDetail",
		6:  "AdvertiseJumpTypeVPS",
		10: "AdvertiseJumpTypeLiveDetail",
		11: "AdvertiseJumpTypeLiveList",
		13: "AdvertiseJumpTypeCommercial",
		14: "AdvertiseJumpTypePersonalHomepage",
		15: "AdvertiseJumpTypeServiceProvider",
		16: "AdvertiseJumpTypeRank",
		17: "AdvertiseJumpTypeH5",
		55: "AdvertiseJumpTypeTopic",
	}
	AdvertiseJumpType_value = map[string]int32{
		"AdvertiseJumpTypeUnknown":          0,
		"AdvertiseJumpTypeTradeDetail":      1,
		"AdvertiseJumpTypeOuterLink":        2,
		"AdvertiseJumpTypeNewsDetail":       3,
		"AdvertiseJumpTypeSurveyDetail":     4,
		"AdvertiseJumpTypeVPS":              6,
		"AdvertiseJumpTypeLiveDetail":       10,
		"AdvertiseJumpTypeLiveList":         11,
		"AdvertiseJumpTypeCommercial":       13,
		"AdvertiseJumpTypePersonalHomepage": 14,
		"AdvertiseJumpTypeServiceProvider":  15,
		"AdvertiseJumpTypeRank":             16,
		"AdvertiseJumpTypeH5":               17,
		"AdvertiseJumpTypeTopic":            55,
	}
)

func (x AdvertiseJumpType) Enum() *AdvertiseJumpType {
	p := new(AdvertiseJumpType)
	*p = x
	return p
}

func (x AdvertiseJumpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdvertiseJumpType) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[5].Descriptor()
}

func (AdvertiseJumpType) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[5]
}

func (x AdvertiseJumpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdvertiseJumpType.Descriptor instead.
func (AdvertiseJumpType) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{5}
}

// 广告商业-帖子类型
type AdvertisePostType int32

const (
	AdvertisePostType_AdvertisePostTypeNone       AdvertisePostType = 0 // 无
	AdvertisePostType_AdvertisePostTypeArticle    AdvertisePostType = 1 // 文章
	AdvertisePostType_AdvertisePostTypeExposure   AdvertisePostType = 2 // 曝光
	AdvertisePostType_AdvertisePostTypeDiscover   AdvertisePostType = 3 // 发现
	AdvertisePostType_AdvertisePostTypeTrader     AdvertisePostType = 4 // 交易商
	AdvertisePostType_AdvertisePostTypeSurvey     AdvertisePostType = 5 // 实勘
	AdvertisePostType_AdvertisePostTypeMediate    AdvertisePostType = 6 // 调解
	AdvertisePostType_AdvertisePostTypeFlash      AdvertisePostType = 7 // 快讯
	AdvertisePostType_AdvertisePostTypeDisclosure AdvertisePostType = 8 // 披露
	AdvertisePostType_AdvertisePostTypeComment    AdvertisePostType = 9 // 评价
)

// Enum value maps for AdvertisePostType.
var (
	AdvertisePostType_name = map[int32]string{
		0: "AdvertisePostTypeNone",
		1: "AdvertisePostTypeArticle",
		2: "AdvertisePostTypeExposure",
		3: "AdvertisePostTypeDiscover",
		4: "AdvertisePostTypeTrader",
		5: "AdvertisePostTypeSurvey",
		6: "AdvertisePostTypeMediate",
		7: "AdvertisePostTypeFlash",
		8: "AdvertisePostTypeDisclosure",
		9: "AdvertisePostTypeComment",
	}
	AdvertisePostType_value = map[string]int32{
		"AdvertisePostTypeNone":       0,
		"AdvertisePostTypeArticle":    1,
		"AdvertisePostTypeExposure":   2,
		"AdvertisePostTypeDiscover":   3,
		"AdvertisePostTypeTrader":     4,
		"AdvertisePostTypeSurvey":     5,
		"AdvertisePostTypeMediate":    6,
		"AdvertisePostTypeFlash":      7,
		"AdvertisePostTypeDisclosure": 8,
		"AdvertisePostTypeComment":    9,
	}
)

func (x AdvertisePostType) Enum() *AdvertisePostType {
	p := new(AdvertisePostType)
	*p = x
	return p
}

func (x AdvertisePostType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdvertisePostType) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[6].Descriptor()
}

func (AdvertisePostType) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[6]
}

func (x AdvertisePostType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdvertisePostType.Descriptor instead.
func (AdvertisePostType) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{6}
}

// 广告标签类型
type AdvertiseTagType int32

const (
	AdvertiseTagType_AdvertiseTypeUnknown     AdvertiseTagType = 0
	AdvertiseTagType_AdvertiseTypeNone        AdvertiseTagType = 1 // 不显示
	AdvertiseTagType_AdvertiseTypeTop         AdvertiseTagType = 2 // 置顶
	AdvertiseTagType_AdvertiseTypeRecommended AdvertiseTagType = 3 // 推荐
	AdvertiseTagType_AdvertiseTypeHot         AdvertiseTagType = 4 // 热门
	AdvertiseTagType_AdvertiseTypeAd          AdvertiseTagType = 5 // 浏览
)

// Enum value maps for AdvertiseTagType.
var (
	AdvertiseTagType_name = map[int32]string{
		0: "AdvertiseTypeUnknown",
		1: "AdvertiseTypeNone",
		2: "AdvertiseTypeTop",
		3: "AdvertiseTypeRecommended",
		4: "AdvertiseTypeHot",
		5: "AdvertiseTypeAd",
	}
	AdvertiseTagType_value = map[string]int32{
		"AdvertiseTypeUnknown":     0,
		"AdvertiseTypeNone":        1,
		"AdvertiseTypeTop":         2,
		"AdvertiseTypeRecommended": 3,
		"AdvertiseTypeHot":         4,
		"AdvertiseTypeAd":          5,
	}
)

func (x AdvertiseTagType) Enum() *AdvertiseTagType {
	p := new(AdvertiseTagType)
	*p = x
	return p
}

func (x AdvertiseTagType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AdvertiseTagType) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[7].Descriptor()
}

func (AdvertiseTagType) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[7]
}

func (x AdvertiseTagType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AdvertiseTagType.Descriptor instead.
func (AdvertiseTagType) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{7}
}

type YearlyReportUserTag int32

const (
	YearlyReportUserTag_YearlyReportUserTagUnknown    YearlyReportUserTag = 0
	YearlyReportUserTag_SafetyExerciser               YearlyReportUserTag = 1  // 安全演练家
	YearlyReportUserTag_ForeignExchangeEncyclopedia   YearlyReportUserTag = 2  // 外汇小百科
	YearlyReportUserTag_CommunityUpFlow               YearlyReportUserTag = 3  // 社区顶流
	YearlyReportUserTag_SuperhumanRightsDefender      YearlyReportUserTag = 4  // 维权超人
	YearlyReportUserTag_NewsSavvy                     YearlyReportUserTag = 5  // 新闻小灵通
	YearlyReportUserTag_TruthKeeper                   YearlyReportUserTag = 6  // 真相守护者
	YearlyReportUserTag_SearchWordSurfer              YearlyReportUserTag = 7  // 搜词冲浪者
	YearlyReportUserTag_GoodEatingMelons              YearlyReportUserTag = 8  // 吃瓜小能手
	YearlyReportUserTag_ChanceWatchers                YearlyReportUserTag = 9  // 机会守望者
	YearlyReportUserTag_SecurityTransactionAmbassador YearlyReportUserTag = 10 // 安全交易大使
	YearlyReportUserTag_SorosOfForex                  YearlyReportUserTag = 11 // 外汇界索罗斯
	YearlyReportUserTag_WarrenBuffett                 YearlyReportUserTag = 12 // 投资界巴菲特
	YearlyReportUserTag_ShelteredFlipper              YearlyReportUserTag = 13 // 被Flipper庇护者
	YearlyReportUserTag_MostFans                      YearlyReportUserTag = 14 // 粉丝收割机
)

// Enum value maps for YearlyReportUserTag.
var (
	YearlyReportUserTag_name = map[int32]string{
		0:  "YearlyReportUserTagUnknown",
		1:  "SafetyExerciser",
		2:  "ForeignExchangeEncyclopedia",
		3:  "CommunityUpFlow",
		4:  "SuperhumanRightsDefender",
		5:  "NewsSavvy",
		6:  "TruthKeeper",
		7:  "SearchWordSurfer",
		8:  "GoodEatingMelons",
		9:  "ChanceWatchers",
		10: "SecurityTransactionAmbassador",
		11: "SorosOfForex",
		12: "WarrenBuffett",
		13: "ShelteredFlipper",
		14: "MostFans",
	}
	YearlyReportUserTag_value = map[string]int32{
		"YearlyReportUserTagUnknown":    0,
		"SafetyExerciser":               1,
		"ForeignExchangeEncyclopedia":   2,
		"CommunityUpFlow":               3,
		"SuperhumanRightsDefender":      4,
		"NewsSavvy":                     5,
		"TruthKeeper":                   6,
		"SearchWordSurfer":              7,
		"GoodEatingMelons":              8,
		"ChanceWatchers":                9,
		"SecurityTransactionAmbassador": 10,
		"SorosOfForex":                  11,
		"WarrenBuffett":                 12,
		"ShelteredFlipper":              13,
		"MostFans":                      14,
	}
)

func (x YearlyReportUserTag) Enum() *YearlyReportUserTag {
	p := new(YearlyReportUserTag)
	*p = x
	return p
}

func (x YearlyReportUserTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (YearlyReportUserTag) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[8].Descriptor()
}

func (YearlyReportUserTag) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[8]
}

func (x YearlyReportUserTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use YearlyReportUserTag.Descriptor instead.
func (YearlyReportUserTag) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{8}
}

// 官方号类型
type OfficialNumberType int32

const (
	OfficialNumberType_OfficialNumber_Unknown OfficialNumberType = 0  //未知
	OfficialNumberType_Trader                 OfficialNumberType = 1  // 交易商号
	OfficialNumberType_WikiFXMediate          OfficialNumberType = 2  // 天眼调解
	OfficialNumberType_WikiFXNews             OfficialNumberType = 3  // WikiFX-新闻
	OfficialNumberType_WikiFXExpress          OfficialNumberType = 4  // WikiFX-快讯
	OfficialNumberType_WikiFXSurvey           OfficialNumberType = 5  // WikiFX-实勘
	OfficialNumberType_ServiceProvider        OfficialNumberType = 6  // 服务商号
	OfficialNumberType_Regulator              OfficialNumberType = 7  // 监管机构号
	OfficialNumberType_User                   OfficialNumberType = 8  // 用户号
	OfficialNumberType_WikiFXActivity         OfficialNumberType = 9  // WikiFX-活动
	OfficialNumberType_LemonX                 OfficialNumberType = 10 // LemonX官方号
	OfficialNumberType_Expo                   OfficialNumberType = 11 // WikiExpo官方号文章
	OfficialNumberType_WikiFx                 OfficialNumberType = 12 // WikiFx官方号
	OfficialNumberType_WikiFxEducation        OfficialNumberType = 13 // WikiFxEducation官方号
	OfficialNumberType_WikiFxElitesClub       OfficialNumberType = 14 // WikiFxElitesClub官方号
	OfficialNumberType_WikiFxSkylineGuide     OfficialNumberType = 15 // WikiFxSkylineGuide官方号
)

// Enum value maps for OfficialNumberType.
var (
	OfficialNumberType_name = map[int32]string{
		0:  "OfficialNumber_Unknown",
		1:  "Trader",
		2:  "WikiFXMediate",
		3:  "WikiFXNews",
		4:  "WikiFXExpress",
		5:  "WikiFXSurvey",
		6:  "ServiceProvider",
		7:  "Regulator",
		8:  "User",
		9:  "WikiFXActivity",
		10: "LemonX",
		11: "Expo",
		12: "WikiFx",
		13: "WikiFxEducation",
		14: "WikiFxElitesClub",
		15: "WikiFxSkylineGuide",
	}
	OfficialNumberType_value = map[string]int32{
		"OfficialNumber_Unknown": 0,
		"Trader":                 1,
		"WikiFXMediate":          2,
		"WikiFXNews":             3,
		"WikiFXExpress":          4,
		"WikiFXSurvey":           5,
		"ServiceProvider":        6,
		"Regulator":              7,
		"User":                   8,
		"WikiFXActivity":         9,
		"LemonX":                 10,
		"Expo":                   11,
		"WikiFx":                 12,
		"WikiFxEducation":        13,
		"WikiFxElitesClub":       14,
		"WikiFxSkylineGuide":     15,
	}
)

func (x OfficialNumberType) Enum() *OfficialNumberType {
	p := new(OfficialNumberType)
	*p = x
	return p
}

func (x OfficialNumberType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OfficialNumberType) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[9].Descriptor()
}

func (OfficialNumberType) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[9]
}

func (x OfficialNumberType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OfficialNumberType.Descriptor instead.
func (OfficialNumberType) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{9}
}

type RecommendFeedbackCategory int32

const (
	RecommendFeedbackCategory_RecommendFeedbackCategoryContent RecommendFeedbackCategory = 0 // 不喜欢内容
	RecommendFeedbackCategory_RecommendFeedbackCategoryAuthor  RecommendFeedbackCategory = 1 // 不喜欢作者
)

// Enum value maps for RecommendFeedbackCategory.
var (
	RecommendFeedbackCategory_name = map[int32]string{
		0: "RecommendFeedbackCategoryContent",
		1: "RecommendFeedbackCategoryAuthor",
	}
	RecommendFeedbackCategory_value = map[string]int32{
		"RecommendFeedbackCategoryContent": 0,
		"RecommendFeedbackCategoryAuthor":  1,
	}
)

func (x RecommendFeedbackCategory) Enum() *RecommendFeedbackCategory {
	p := new(RecommendFeedbackCategory)
	*p = x
	return p
}

func (x RecommendFeedbackCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecommendFeedbackCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[10].Descriptor()
}

func (RecommendFeedbackCategory) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[10]
}

func (x RecommendFeedbackCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecommendFeedbackCategory.Descriptor instead.
func (RecommendFeedbackCategory) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{10}
}

type HotContentLabel int32

const (
	HotContentLabel_HotContentLabelNone   HotContentLabel = 0 // 无标签
	HotContentLabel_HotContentLabelNew    HotContentLabel = 1 // 新上榜
	HotContentLabel_HotContentLabelHot    HotContentLabel = 2 // 热
	HotContentLabel_HotContentLabelUnique HotContentLabel = 3 // 独家
)

// Enum value maps for HotContentLabel.
var (
	HotContentLabel_name = map[int32]string{
		0: "HotContentLabelNone",
		1: "HotContentLabelNew",
		2: "HotContentLabelHot",
		3: "HotContentLabelUnique",
	}
	HotContentLabel_value = map[string]int32{
		"HotContentLabelNone":   0,
		"HotContentLabelNew":    1,
		"HotContentLabelHot":    2,
		"HotContentLabelUnique": 3,
	}
)

func (x HotContentLabel) Enum() *HotContentLabel {
	p := new(HotContentLabel)
	*p = x
	return p
}

func (x HotContentLabel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HotContentLabel) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[11].Descriptor()
}

func (HotContentLabel) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[11]
}

func (x HotContentLabel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HotContentLabel.Descriptor instead.
func (HotContentLabel) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{11}
}

type CreatorRankingCategory int32

const (
	CreatorRankingCategory_CreatorRankingCategoryWeek  CreatorRankingCategory = 0 // 周榜
	CreatorRankingCategory_CreatorRankingCategoryMonth CreatorRankingCategory = 1 // 月榜
)

// Enum value maps for CreatorRankingCategory.
var (
	CreatorRankingCategory_name = map[int32]string{
		0: "CreatorRankingCategoryWeek",
		1: "CreatorRankingCategoryMonth",
	}
	CreatorRankingCategory_value = map[string]int32{
		"CreatorRankingCategoryWeek":  0,
		"CreatorRankingCategoryMonth": 1,
	}
)

func (x CreatorRankingCategory) Enum() *CreatorRankingCategory {
	p := new(CreatorRankingCategory)
	*p = x
	return p
}

func (x CreatorRankingCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreatorRankingCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_recommend_v1_models_proto_enumTypes[12].Descriptor()
}

func (CreatorRankingCategory) Type() protoreflect.EnumType {
	return &file_recommend_v1_models_proto_enumTypes[12]
}

func (x CreatorRankingCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreatorRankingCategory.Descriptor instead.
func (CreatorRankingCategory) EnumDescriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{12}
}

type RecommendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	Size     int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page     int64  `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	Type string `protobuf:"bytes,5,opt,name=type,json=type,proto3" json:"type"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	LanguageCode string `protobuf:"bytes,6,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	CountryCode string `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	Project     string      `protobuf:"bytes,8,opt,name=project,json=project,proto3" json:"project"`
	ReleaseType ReleaseType `protobuf:"varint,9,opt,name=release_type,json=releaseType,proto3,enum=api.recommend.v1.ReleaseType" json:"release_type"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	PreferredLanguages string       `protobuf:"bytes,10,opt,name=preferred_languages,json=preferredLanguages,proto3" json:"preferred_languages"`
	FirstShowPage      int64        `protobuf:"varint,11,opt,name=first_show_page,json=firstShowPage,proto3" json:"first_show_page"`
	UserIdentify       UserIdentify `protobuf:"varint,12,opt,name=user_identify,json=userIdentify,proto3,enum=api.recommend.v1.UserIdentify" json:"user_identify"`
}

func (x *RecommendRequest) Reset() {
	*x = RecommendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendRequest) ProtoMessage() {}

func (x *RecommendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendRequest.ProtoReflect.Descriptor instead.
func (*RecommendRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RecommendRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *RecommendRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *RecommendRequest) GetReleaseType() ReleaseType {
	if x != nil {
		return x.ReleaseType
	}
	return ReleaseType_ReleaseTypeUnknown
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *RecommendRequest) GetPreferredLanguages() string {
	if x != nil {
		return x.PreferredLanguages
	}
	return ""
}

func (x *RecommendRequest) GetFirstShowPage() int64 {
	if x != nil {
		return x.FirstShowPage
	}
	return 0
}

func (x *RecommendRequest) GetUserIdentify() UserIdentify {
	if x != nil {
		return x.UserIdentify
	}
	return UserIdentify_UserIdentifyInvestor
}

type RecommendV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size          int64             `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
	Page          int64             `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Category      RecommendCategory `protobuf:"varint,3,opt,name=category,json=category,proto3,enum=api.recommend.v1.RecommendCategory" json:"category"`
	FirstShowPage int64             `protobuf:"varint,4,opt,name=first_show_page,json=firstShowPage,proto3" json:"first_show_page"`
	Position      RecommendPosition `protobuf:"varint,5,opt,name=position,json=position,proto3,enum=api.recommend.v1.RecommendPosition" json:"position"`
	UserIdentify  UserIdentify      `protobuf:"varint,6,opt,name=user_identify,json=userIdentify,proto3,enum=api.recommend.v1.UserIdentify" json:"user_identify"`
	PreLoad       int64             `protobuf:"varint,7,opt,name=pre_load,json=preLoad,proto3" json:"pre_load"`
}

func (x *RecommendV2Request) Reset() {
	*x = RecommendV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendV2Request) ProtoMessage() {}

func (x *RecommendV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendV2Request.ProtoReflect.Descriptor instead.
func (*RecommendV2Request) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{1}
}

func (x *RecommendV2Request) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *RecommendV2Request) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RecommendV2Request) GetCategory() RecommendCategory {
	if x != nil {
		return x.Category
	}
	return RecommendCategory_RecommendCategoryRecommend
}

func (x *RecommendV2Request) GetFirstShowPage() int64 {
	if x != nil {
		return x.FirstShowPage
	}
	return 0
}

func (x *RecommendV2Request) GetPosition() RecommendPosition {
	if x != nil {
		return x.Position
	}
	return RecommendPosition_RecommendPositionDiscoverPage
}

func (x *RecommendV2Request) GetUserIdentify() UserIdentify {
	if x != nil {
		return x.UserIdentify
	}
	return UserIdentify_UserIdentifyInvestor
}

func (x *RecommendV2Request) GetPreLoad() int64 {
	if x != nil {
		return x.PreLoad
	}
	return 0
}

type AdvertiseItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title          string `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	Slogan         string `protobuf:"bytes,2,opt,name=slogan,json=slogan,proto3" json:"slogan"`
	Image          string `protobuf:"bytes,3,opt,name=image,json=image,proto3" json:"image"`
	Url            string `protobuf:"bytes,4,opt,name=url,json=url,proto3" json:"url"`
	Code           string `protobuf:"bytes,5,opt,name=code,json=code,proto3" json:"code"`
	CountryCode    string `protobuf:"bytes,6,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	LanguageCode   string `protobuf:"bytes,7,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	AppJumpSubType string `protobuf:"bytes,8,opt,name=app_jump_sub_type,json=appJumpSubType,proto3" json:"app_jump_sub_type"`
	Width          int64  `protobuf:"varint,9,opt,name=width,json=width,proto3" json:"width"`
	Height         int64  `protobuf:"varint,10,opt,name=height,json=height,proto3" json:"height"`
	HitsInfo       string `protobuf:"bytes,11,opt,name=hits_info,json=hitsInfo,proto3" json:"hits_info"`
}

func (x *AdvertiseItem) Reset() {
	*x = AdvertiseItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AdvertiseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdvertiseItem) ProtoMessage() {}

func (x *AdvertiseItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdvertiseItem.ProtoReflect.Descriptor instead.
func (*AdvertiseItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{2}
}

func (x *AdvertiseItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AdvertiseItem) GetSlogan() string {
	if x != nil {
		return x.Slogan
	}
	return ""
}

func (x *AdvertiseItem) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *AdvertiseItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AdvertiseItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AdvertiseItem) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *AdvertiseItem) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *AdvertiseItem) GetAppJumpSubType() string {
	if x != nil {
		return x.AppJumpSubType
	}
	return ""
}

func (x *AdvertiseItem) GetWidth() int64 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *AdvertiseItem) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *AdvertiseItem) GetHitsInfo() string {
	if x != nil {
		return x.HitsInfo
	}
	return ""
}

type Advertise struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JumpType    AdvertiseJumpType `protobuf:"varint,1,opt,name=jump_type,json=jumpType,proto3,enum=api.recommend.v1.AdvertiseJumpType" json:"jump_type"`
	TagType     AdvertiseTagType  `protobuf:"varint,2,opt,name=tag_type,json=tagType,proto3,enum=api.recommend.v1.AdvertiseTagType" json:"tag_type"`
	Tag         string            `protobuf:"bytes,3,opt,name=tag,json=tag,proto3" json:"tag"`
	Title       string            `protobuf:"bytes,4,opt,name=title,json=title,proto3" json:"title"`
	Slogan      string            `protobuf:"bytes,5,opt,name=slogan,json=slogan,proto3" json:"slogan"`
	Items       []*AdvertiseItem  `protobuf:"bytes,6,rep,name=items,json=items,proto3" json:"items"`
	Order       int64             `protobuf:"varint,7,opt,name=order,json=order,proto3" json:"order"`
	PostType    AdvertisePostType `protobuf:"varint,8,opt,name=post_type,json=postType,proto3,enum=api.recommend.v1.AdvertisePostType" json:"post_type"`
	Id          string            `protobuf:"bytes,9,opt,name=id,json=id,proto3" json:"id"`
	AppJumpType string            `protobuf:"bytes,10,opt,name=app_jump_type,json=appJumpType,proto3" json:"app_jump_type"`
}

func (x *Advertise) Reset() {
	*x = Advertise{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Advertise) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Advertise) ProtoMessage() {}

func (x *Advertise) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Advertise.ProtoReflect.Descriptor instead.
func (*Advertise) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{3}
}

func (x *Advertise) GetJumpType() AdvertiseJumpType {
	if x != nil {
		return x.JumpType
	}
	return AdvertiseJumpType_AdvertiseJumpTypeUnknown
}

func (x *Advertise) GetTagType() AdvertiseTagType {
	if x != nil {
		return x.TagType
	}
	return AdvertiseTagType_AdvertiseTypeUnknown
}

func (x *Advertise) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *Advertise) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Advertise) GetSlogan() string {
	if x != nil {
		return x.Slogan
	}
	return ""
}

func (x *Advertise) GetItems() []*AdvertiseItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *Advertise) GetOrder() int64 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *Advertise) GetPostType() AdvertisePostType {
	if x != nil {
		return x.PostType
	}
	return AdvertisePostType_AdvertisePostTypeNone
}

func (x *Advertise) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Advertise) GetAppJumpType() string {
	if x != nil {
		return x.AppJumpType
	}
	return ""
}

type RecommendItemLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BackgroundColor string `protobuf:"bytes,1,opt,name=background_color,json=backgroundColor,proto3" json:"background_color"`
	Text            string `protobuf:"bytes,2,opt,name=text,json=text,proto3" json:"text"`
}

func (x *RecommendItemLabel) Reset() {
	*x = RecommendItemLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendItemLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendItemLabel) ProtoMessage() {}

func (x *RecommendItemLabel) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendItemLabel.ProtoReflect.Descriptor instead.
func (*RecommendItemLabel) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{4}
}

func (x *RecommendItemLabel) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *RecommendItemLabel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type RecommendItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            string                `protobuf:"bytes,1,opt,name=type,json=type,proto3" json:"type"`
	Id              string                `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`
	TraderCode      string                `protobuf:"bytes,3,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	UserId          string                `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	From            string                `protobuf:"bytes,5,opt,name=from,json=from,proto3" json:"from"`
	SubType         string                `protobuf:"bytes,6,opt,name=sub_type,json=subType,proto3" json:"sub_type"`
	ItemType        RecommendItemType     `protobuf:"varint,7,opt,name=item_type,json=itemType,proto3,enum=api.recommend.v1.RecommendItemType" json:"item_type"`
	Advertise       *Advertise            `protobuf:"bytes,8,opt,name=advertise,json=advertise,proto3" json:"advertise"`
	Labels          []*RecommendItemLabel `protobuf:"bytes,9,rep,name=labels,json=labels,proto3" json:"labels"`
	NotShowFeedback bool                  `protobuf:"varint,10,opt,name=not_show_feedback,json=notShowFeedback,proto3" json:"not_show_feedback"`
}

func (x *RecommendItem) Reset() {
	*x = RecommendItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendItem) ProtoMessage() {}

func (x *RecommendItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendItem.ProtoReflect.Descriptor instead.
func (*RecommendItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{5}
}

func (x *RecommendItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecommendItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecommendItem) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *RecommendItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RecommendItem) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *RecommendItem) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *RecommendItem) GetItemType() RecommendItemType {
	if x != nil {
		return x.ItemType
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *RecommendItem) GetAdvertise() *Advertise {
	if x != nil {
		return x.Advertise
	}
	return nil
}

func (x *RecommendItem) GetLabels() []*RecommendItemLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *RecommendItem) GetNotShowFeedback() bool {
	if x != nil {
		return x.NotShowFeedback
	}
	return false
}

type RecommendReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items         []*RecommendItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total         int64            `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
	FirstShowPage int64            `protobuf:"varint,3,opt,name=first_show_page,json=firstShowPage,proto3" json:"first_show_page"`
}

func (x *RecommendReply) Reset() {
	*x = RecommendReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendReply) ProtoMessage() {}

func (x *RecommendReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendReply.ProtoReflect.Descriptor instead.
func (*RecommendReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{6}
}

func (x *RecommendReply) GetItems() []*RecommendItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *RecommendReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RecommendReply) GetFirstShowPage() int64 {
	if x != nil {
		return x.FirstShowPage
	}
	return 0
}

type FindCommerceByCategoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	Size     int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page     int64  `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	LanguageCode string `protobuf:"bytes,5,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	CountryCode string `protobuf:"bytes,6,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	Project string `protobuf:"bytes,7,opt,name=project,json=project,proto3" json:"project"`
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	PreferredLanguages string `protobuf:"bytes,8,opt,name=preferred_languages,json=preferredLanguages,proto3" json:"preferred_languages"`
	CategoryId         string `protobuf:"bytes,9,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	FirstShowPage      int64  `protobuf:"varint,11,opt,name=first_show_page,json=firstShowPage,proto3" json:"first_show_page"`
}

func (x *FindCommerceByCategoryRequest) Reset() {
	*x = FindCommerceByCategoryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindCommerceByCategoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindCommerceByCategoryRequest) ProtoMessage() {}

func (x *FindCommerceByCategoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindCommerceByCategoryRequest.ProtoReflect.Descriptor instead.
func (*FindCommerceByCategoryRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{7}
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *FindCommerceByCategoryRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *FindCommerceByCategoryRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *FindCommerceByCategoryRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FindCommerceByCategoryRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *FindCommerceByCategoryRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *FindCommerceByCategoryRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *FindCommerceByCategoryRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *FindCommerceByCategoryRequest) GetPreferredLanguages() string {
	if x != nil {
		return x.PreferredLanguages
	}
	return ""
}

func (x *FindCommerceByCategoryRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *FindCommerceByCategoryRequest) GetFirstShowPage() int64 {
	if x != nil {
		return x.FirstShowPage
	}
	return 0
}

type CommerceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Type      RecommendItemType `protobuf:"varint,2,opt,name=type,json=type,proto3,enum=api.recommend.v1.RecommendItemType" json:"type"`
	Advertise *Advertise        `protobuf:"bytes,3,opt,name=advertise,json=advertise,proto3" json:"advertise"`
}

func (x *CommerceItem) Reset() {
	*x = CommerceItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommerceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommerceItem) ProtoMessage() {}

func (x *CommerceItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommerceItem.ProtoReflect.Descriptor instead.
func (*CommerceItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{8}
}

func (x *CommerceItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CommerceItem) GetType() RecommendItemType {
	if x != nil {
		return x.Type
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *CommerceItem) GetAdvertise() *Advertise {
	if x != nil {
		return x.Advertise
	}
	return nil
}

type FindCommerceByCategoryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items         []*CommerceItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total         int64           `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
	FirstShowPage int64           `protobuf:"varint,3,opt,name=first_show_page,json=firstShowPage,proto3" json:"first_show_page"`
}

func (x *FindCommerceByCategoryReply) Reset() {
	*x = FindCommerceByCategoryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindCommerceByCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindCommerceByCategoryReply) ProtoMessage() {}

func (x *FindCommerceByCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindCommerceByCategoryReply.ProtoReflect.Descriptor instead.
func (*FindCommerceByCategoryReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{9}
}

func (x *FindCommerceByCategoryReply) GetItems() []*CommerceItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FindCommerceByCategoryReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *FindCommerceByCategoryReply) GetFirstShowPage() int64 {
	if x != nil {
		return x.FirstShowPage
	}
	return 0
}

type Quote struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image             string  `protobuf:"bytes,1,opt,name=image,json=image,proto3" json:"image"`
	Symbol            string  `protobuf:"bytes,2,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	Rate              float32 `protobuf:"fixed32,3,opt,name=rate,json=rate,proto3" json:"rate"`
	MaxPrice          float32 `protobuf:"fixed32,4,opt,name=max_price,json=maxPrice,proto3" json:"max_price"`
	MaxPriceTimestamp int64   `protobuf:"varint,5,opt,name=max_price_timestamp,json=maxPriceTimestamp,proto3" json:"max_price_timestamp"`
	MinPrice          float32 `protobuf:"fixed32,6,opt,name=min_price,json=minPrice,proto3" json:"min_price"`
	MinPriceTimestamp int64   `protobuf:"varint,7,opt,name=min_price_timestamp,json=minPriceTimestamp,proto3" json:"min_price_timestamp"`
}

func (x *Quote) Reset() {
	*x = Quote{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Quote) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Quote) ProtoMessage() {}

func (x *Quote) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Quote.ProtoReflect.Descriptor instead.
func (*Quote) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{10}
}

func (x *Quote) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *Quote) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Quote) GetRate() float32 {
	if x != nil {
		return x.Rate
	}
	return 0
}

func (x *Quote) GetMaxPrice() float32 {
	if x != nil {
		return x.MaxPrice
	}
	return 0
}

func (x *Quote) GetMaxPriceTimestamp() int64 {
	if x != nil {
		return x.MaxPriceTimestamp
	}
	return 0
}

func (x *Quote) GetMinPrice() float32 {
	if x != nil {
		return x.MinPrice
	}
	return 0
}

func (x *Quote) GetMinPriceTimestamp() int64 {
	if x != nil {
		return x.MinPriceTimestamp
	}
	return 0
}

type YearlyReportRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
}

func (x *YearlyReportRequest) Reset() {
	*x = YearlyReportRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearlyReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearlyReportRequest) ProtoMessage() {}

func (x *YearlyReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearlyReportRequest.ProtoReflect.Descriptor instead.
func (*YearlyReportRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{11}
}

func (x *YearlyReportRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type YearlyReportGlobal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewTrader                       int64    `protobuf:"varint,3,opt,name=new_trader,json=newTrader,proto3" json:"new_trader"`
	NewServiceProvider              int64    `protobuf:"varint,4,opt,name=new_service_provider,json=newServiceProvider,proto3" json:"new_service_provider"`
	NewLicense                      int64    `protobuf:"varint,5,opt,name=new_license,json=newLicense,proto3" json:"new_license"`
	NewSurvey                       int64    `protobuf:"varint,6,opt,name=new_survey,json=newSurvey,proto3" json:"new_survey"`
	NewArticle                      int64    `protobuf:"varint,7,opt,name=new_article,json=newArticle,proto3" json:"new_article"`
	NewExposure                     int64    `protobuf:"varint,8,opt,name=new_exposure,json=newExposure,proto3" json:"new_exposure"`
	NewDisclosure                   int64    `protobuf:"varint,9,opt,name=new_disclosure,json=newDisclosure,proto3" json:"new_disclosure"`
	NewDisclosureRelTrader          int64    `protobuf:"varint,10,opt,name=new_disclosure_rel_trader,json=newDisclosureRelTrader,proto3" json:"new_disclosure_rel_trader"`
	MediatePerson                   int64    `protobuf:"varint,11,opt,name=mediate_person,json=mediatePerson,proto3" json:"mediate_person"`
	MediateAmount                   float32  `protobuf:"fixed32,12,opt,name=mediate_amount,json=mediateAmount,proto3" json:"mediate_amount"`
	QuoteCount                      int64    `protobuf:"varint,13,opt,name=quote_count,json=quoteCount,proto3" json:"quote_count"`
	UpQuoteCount                    int64    `protobuf:"varint,14,opt,name=up_quote_count,json=upQuoteCount,proto3" json:"up_quote_count"`
	FallQuoteCount                  int64    `protobuf:"varint,15,opt,name=fall_quote_count,json=fallQuoteCount,proto3" json:"fall_quote_count"`
	Quotes                          []*Quote `protobuf:"bytes,16,rep,name=quotes,json=quotes,proto3" json:"quotes"`
	InterestedSearchKeywordUserText string   `protobuf:"bytes,18,opt,name=interested_search_keyword_user_text,json=interestedSearchKeywordUserText,proto3" json:"interested_search_keyword_user_text"`
	Keywords                        []string `protobuf:"bytes,19,rep,name=keywords,json=keywords,proto3" json:"keywords"`
	ViewPostsUserCount              int64    `protobuf:"varint,20,opt,name=view_posts_user_count,json=viewPostsUserCount,proto3" json:"view_posts_user_count"`
	ActionUserCount                 int64    `protobuf:"varint,21,opt,name=action_user_count,json=actionUserCount,proto3" json:"action_user_count"`
	YearlyIncTop2                   []*Quote `protobuf:"bytes,22,rep,name=yearly_inc_top2,json=yearlyIncTop2,proto3" json:"yearly_inc_top2"`
	YearlyDescTop1                  *Quote   `protobuf:"bytes,23,opt,name=yearly_desc_top1,json=yearlyDescTop1,proto3" json:"yearly_desc_top1"`
	YearlyDurableIncTop1            *Quote   `protobuf:"bytes,24,opt,name=yearly_durable_inc_top1,json=yearlyDurableIncTop1,proto3" json:"yearly_durable_inc_top1"`
	CommunityUserText               string   `protobuf:"bytes,25,opt,name=community_user_text,json=communityUserText,proto3" json:"community_user_text"`
}

func (x *YearlyReportGlobal) Reset() {
	*x = YearlyReportGlobal{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearlyReportGlobal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearlyReportGlobal) ProtoMessage() {}

func (x *YearlyReportGlobal) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearlyReportGlobal.ProtoReflect.Descriptor instead.
func (*YearlyReportGlobal) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{12}
}

func (x *YearlyReportGlobal) GetNewTrader() int64 {
	if x != nil {
		return x.NewTrader
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewServiceProvider() int64 {
	if x != nil {
		return x.NewServiceProvider
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewLicense() int64 {
	if x != nil {
		return x.NewLicense
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewSurvey() int64 {
	if x != nil {
		return x.NewSurvey
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewArticle() int64 {
	if x != nil {
		return x.NewArticle
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewExposure() int64 {
	if x != nil {
		return x.NewExposure
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewDisclosure() int64 {
	if x != nil {
		return x.NewDisclosure
	}
	return 0
}

func (x *YearlyReportGlobal) GetNewDisclosureRelTrader() int64 {
	if x != nil {
		return x.NewDisclosureRelTrader
	}
	return 0
}

func (x *YearlyReportGlobal) GetMediatePerson() int64 {
	if x != nil {
		return x.MediatePerson
	}
	return 0
}

func (x *YearlyReportGlobal) GetMediateAmount() float32 {
	if x != nil {
		return x.MediateAmount
	}
	return 0
}

func (x *YearlyReportGlobal) GetQuoteCount() int64 {
	if x != nil {
		return x.QuoteCount
	}
	return 0
}

func (x *YearlyReportGlobal) GetUpQuoteCount() int64 {
	if x != nil {
		return x.UpQuoteCount
	}
	return 0
}

func (x *YearlyReportGlobal) GetFallQuoteCount() int64 {
	if x != nil {
		return x.FallQuoteCount
	}
	return 0
}

func (x *YearlyReportGlobal) GetQuotes() []*Quote {
	if x != nil {
		return x.Quotes
	}
	return nil
}

func (x *YearlyReportGlobal) GetInterestedSearchKeywordUserText() string {
	if x != nil {
		return x.InterestedSearchKeywordUserText
	}
	return ""
}

func (x *YearlyReportGlobal) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *YearlyReportGlobal) GetViewPostsUserCount() int64 {
	if x != nil {
		return x.ViewPostsUserCount
	}
	return 0
}

func (x *YearlyReportGlobal) GetActionUserCount() int64 {
	if x != nil {
		return x.ActionUserCount
	}
	return 0
}

func (x *YearlyReportGlobal) GetYearlyIncTop2() []*Quote {
	if x != nil {
		return x.YearlyIncTop2
	}
	return nil
}

func (x *YearlyReportGlobal) GetYearlyDescTop1() *Quote {
	if x != nil {
		return x.YearlyDescTop1
	}
	return nil
}

func (x *YearlyReportGlobal) GetYearlyDurableIncTop1() *Quote {
	if x != nil {
		return x.YearlyDurableIncTop1
	}
	return nil
}

func (x *YearlyReportGlobal) GetCommunityUserText() string {
	if x != nil {
		return x.CommunityUserText
	}
	return ""
}

type YearlyReportUserObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      string `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
	ViewCount int64  `protobuf:"varint,2,opt,name=view_count,json=viewCount,proto3" json:"view_count"`
}

func (x *YearlyReportUserObject) Reset() {
	*x = YearlyReportUserObject{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearlyReportUserObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearlyReportUserObject) ProtoMessage() {}

func (x *YearlyReportUserObject) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearlyReportUserObject.ProtoReflect.Descriptor instead.
func (*YearlyReportUserObject) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{13}
}

func (x *YearlyReportUserObject) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *YearlyReportUserObject) GetViewCount() int64 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

type YearlyReportUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegisterTimestamp         int64                     `protobuf:"varint,1,opt,name=register_timestamp,json=registerTimestamp,proto3" json:"register_timestamp"`
	RegisterDays              int64                     `protobuf:"varint,2,opt,name=register_days,json=registerDays,proto3" json:"register_days"`
	ViewTraderList            []*YearlyReportUserObject `protobuf:"bytes,3,rep,name=view_trader_list,json=viewTraderList,proto3" json:"view_trader_list"`
	ViewServiceProviderList   []*YearlyReportUserObject `protobuf:"bytes,4,rep,name=view_service_provider_list,json=viewServiceProviderList,proto3" json:"view_service_provider_list"`
	UseSearchCount            int64                     `protobuf:"varint,5,opt,name=use_search_count,json=useSearchCount,proto3" json:"use_search_count"`
	MostUsedSearchKeyword     string                    `protobuf:"bytes,6,opt,name=most_used_search_keyword,json=mostUsedSearchKeyword,proto3" json:"most_used_search_keyword"`
	InterestedKeywords        []string                  `protobuf:"bytes,7,rep,name=interested_keywords,json=interestedKeywords,proto3" json:"interested_keywords"`
	ViewArticleCount          int64                     `protobuf:"varint,8,opt,name=view_article_count,json=viewArticleCount,proto3" json:"view_article_count"`
	InterestedAreaCodes       []string                  `protobuf:"bytes,9,rep,name=interested_area_codes,json=interestedAreaCodes,proto3" json:"interested_area_codes"`
	PostsCount                int64                     `protobuf:"varint,10,opt,name=posts_count,json=postsCount,proto3" json:"posts_count"`
	ViewPostsCount            int64                     `protobuf:"varint,11,opt,name=view_posts_count,json=viewPostsCount,proto3" json:"view_posts_count"`
	ViewExposureCount         int64                     `protobuf:"varint,12,opt,name=view_exposure_count,json=viewExposureCount,proto3" json:"view_exposure_count"`
	MediateCount              int64                     `protobuf:"varint,13,opt,name=mediate_count,json=mediateCount,proto3" json:"mediate_count"`
	MediateAmount             float32                   `protobuf:"fixed32,14,opt,name=mediate_amount,json=mediateAmount,proto3" json:"mediate_amount"`
	InterestedQuote           *Quote                    `protobuf:"bytes,15,opt,name=interested_quote,json=interestedQuote,proto3" json:"interested_quote"`
	InterestedQuoteViewCount  int64                     `protobuf:"varint,16,opt,name=interested_quote_view_count,json=interestedQuoteViewCount,proto3" json:"interested_quote_view_count"`
	MockTraderCount           int64                     `protobuf:"varint,17,opt,name=mock_trader_count,json=mockTraderCount,proto3" json:"mock_trader_count"`
	MockTradeAmount           float32                   `protobuf:"fixed32,18,opt,name=mock_trade_amount,json=mockTradeAmount,proto3" json:"mock_trade_amount"`
	MockTradeRevenue          float32                   `protobuf:"fixed32,19,opt,name=mock_trade_revenue,json=mockTradeRevenue,proto3" json:"mock_trade_revenue"`
	MockTradeMostQuote        *Quote                    `protobuf:"bytes,20,opt,name=mock_trade_most_quote,json=mockTradeMostQuote,proto3" json:"mock_trade_most_quote"`
	FirstPostId               string                    `protobuf:"bytes,21,opt,name=first_post_id,json=firstPostId,proto3" json:"first_post_id"`
	FirstPostTimestamp        int64                     `protobuf:"varint,22,opt,name=first_post_timestamp,json=firstPostTimestamp,proto3" json:"first_post_timestamp"`
	FirstPostActionCount      int64                     `protobuf:"varint,23,opt,name=first_post_action_count,json=firstPostActionCount,proto3" json:"first_post_action_count"`
	FirstPostViewUserList     []*YearlyReportUserObject `protobuf:"bytes,24,rep,name=first_post_view_user_list,json=firstPostViewUserList,proto3" json:"first_post_view_user_list"`
	Tag                       YearlyReportUserTag       `protobuf:"varint,25,opt,name=tag,json=tag,proto3,enum=api.recommend.v1.YearlyReportUserTag" json:"tag"`
	ViewTraderCount           int64                     `protobuf:"varint,26,opt,name=view_trader_count,json=viewTraderCount,proto3" json:"view_trader_count"`
	ClickTradeCount           int64                     `protobuf:"varint,27,opt,name=click_trade_count,json=clickTradeCount,proto3" json:"click_trade_count"`
	ViewServiceProviderCount  int64                     `protobuf:"varint,28,opt,name=view_service_provider_count,json=viewServiceProviderCount,proto3" json:"view_service_provider_count"`
	ClickServiceProviderCount int64                     `protobuf:"varint,29,opt,name=click_service_provider_count,json=clickServiceProviderCount,proto3" json:"click_service_provider_count"`
}

func (x *YearlyReportUser) Reset() {
	*x = YearlyReportUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearlyReportUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearlyReportUser) ProtoMessage() {}

func (x *YearlyReportUser) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearlyReportUser.ProtoReflect.Descriptor instead.
func (*YearlyReportUser) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{14}
}

func (x *YearlyReportUser) GetRegisterTimestamp() int64 {
	if x != nil {
		return x.RegisterTimestamp
	}
	return 0
}

func (x *YearlyReportUser) GetRegisterDays() int64 {
	if x != nil {
		return x.RegisterDays
	}
	return 0
}

func (x *YearlyReportUser) GetViewTraderList() []*YearlyReportUserObject {
	if x != nil {
		return x.ViewTraderList
	}
	return nil
}

func (x *YearlyReportUser) GetViewServiceProviderList() []*YearlyReportUserObject {
	if x != nil {
		return x.ViewServiceProviderList
	}
	return nil
}

func (x *YearlyReportUser) GetUseSearchCount() int64 {
	if x != nil {
		return x.UseSearchCount
	}
	return 0
}

func (x *YearlyReportUser) GetMostUsedSearchKeyword() string {
	if x != nil {
		return x.MostUsedSearchKeyword
	}
	return ""
}

func (x *YearlyReportUser) GetInterestedKeywords() []string {
	if x != nil {
		return x.InterestedKeywords
	}
	return nil
}

func (x *YearlyReportUser) GetViewArticleCount() int64 {
	if x != nil {
		return x.ViewArticleCount
	}
	return 0
}

func (x *YearlyReportUser) GetInterestedAreaCodes() []string {
	if x != nil {
		return x.InterestedAreaCodes
	}
	return nil
}

func (x *YearlyReportUser) GetPostsCount() int64 {
	if x != nil {
		return x.PostsCount
	}
	return 0
}

func (x *YearlyReportUser) GetViewPostsCount() int64 {
	if x != nil {
		return x.ViewPostsCount
	}
	return 0
}

func (x *YearlyReportUser) GetViewExposureCount() int64 {
	if x != nil {
		return x.ViewExposureCount
	}
	return 0
}

func (x *YearlyReportUser) GetMediateCount() int64 {
	if x != nil {
		return x.MediateCount
	}
	return 0
}

func (x *YearlyReportUser) GetMediateAmount() float32 {
	if x != nil {
		return x.MediateAmount
	}
	return 0
}

func (x *YearlyReportUser) GetInterestedQuote() *Quote {
	if x != nil {
		return x.InterestedQuote
	}
	return nil
}

func (x *YearlyReportUser) GetInterestedQuoteViewCount() int64 {
	if x != nil {
		return x.InterestedQuoteViewCount
	}
	return 0
}

func (x *YearlyReportUser) GetMockTraderCount() int64 {
	if x != nil {
		return x.MockTraderCount
	}
	return 0
}

func (x *YearlyReportUser) GetMockTradeAmount() float32 {
	if x != nil {
		return x.MockTradeAmount
	}
	return 0
}

func (x *YearlyReportUser) GetMockTradeRevenue() float32 {
	if x != nil {
		return x.MockTradeRevenue
	}
	return 0
}

func (x *YearlyReportUser) GetMockTradeMostQuote() *Quote {
	if x != nil {
		return x.MockTradeMostQuote
	}
	return nil
}

func (x *YearlyReportUser) GetFirstPostId() string {
	if x != nil {
		return x.FirstPostId
	}
	return ""
}

func (x *YearlyReportUser) GetFirstPostTimestamp() int64 {
	if x != nil {
		return x.FirstPostTimestamp
	}
	return 0
}

func (x *YearlyReportUser) GetFirstPostActionCount() int64 {
	if x != nil {
		return x.FirstPostActionCount
	}
	return 0
}

func (x *YearlyReportUser) GetFirstPostViewUserList() []*YearlyReportUserObject {
	if x != nil {
		return x.FirstPostViewUserList
	}
	return nil
}

func (x *YearlyReportUser) GetTag() YearlyReportUserTag {
	if x != nil {
		return x.Tag
	}
	return YearlyReportUserTag_YearlyReportUserTagUnknown
}

func (x *YearlyReportUser) GetViewTraderCount() int64 {
	if x != nil {
		return x.ViewTraderCount
	}
	return 0
}

func (x *YearlyReportUser) GetClickTradeCount() int64 {
	if x != nil {
		return x.ClickTradeCount
	}
	return 0
}

func (x *YearlyReportUser) GetViewServiceProviderCount() int64 {
	if x != nil {
		return x.ViewServiceProviderCount
	}
	return 0
}

func (x *YearlyReportUser) GetClickServiceProviderCount() int64 {
	if x != nil {
		return x.ClickServiceProviderCount
	}
	return 0
}

type YearlyReportReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Global *YearlyReportGlobal `protobuf:"bytes,1,opt,name=global,json=global,proto3" json:"global"`
	User   *YearlyReportUser   `protobuf:"bytes,2,opt,name=user,json=user,proto3" json:"user"`
}

func (x *YearlyReportReply) Reset() {
	*x = YearlyReportReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *YearlyReportReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YearlyReportReply) ProtoMessage() {}

func (x *YearlyReportReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YearlyReportReply.ProtoReflect.Descriptor instead.
func (*YearlyReportReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{15}
}

func (x *YearlyReportReply) GetGlobal() *YearlyReportGlobal {
	if x != nil {
		return x.Global
	}
	return nil
}

func (x *YearlyReportReply) GetUser() *YearlyReportUser {
	if x != nil {
		return x.User
	}
	return nil
}

type FindHotAndNewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReleaseType    ReleaseType `protobuf:"varint,1,opt,name=release_type,json=releaseType,proto3,enum=api.recommend.v1.ReleaseType" json:"release_type"`
	CategoryId     string      `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id"`
	Page           int64       `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
	Size           int64       `protobuf:"varint,5,opt,name=size,json=size,proto3" json:"size"`
	StartTimestamp int64       `protobuf:"varint,10,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp"`
}

func (x *FindHotAndNewRequest) Reset() {
	*x = FindHotAndNewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindHotAndNewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindHotAndNewRequest) ProtoMessage() {}

func (x *FindHotAndNewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindHotAndNewRequest.ProtoReflect.Descriptor instead.
func (*FindHotAndNewRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{16}
}

func (x *FindHotAndNewRequest) GetReleaseType() ReleaseType {
	if x != nil {
		return x.ReleaseType
	}
	return ReleaseType_ReleaseTypeUnknown
}

func (x *FindHotAndNewRequest) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *FindHotAndNewRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FindHotAndNewRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FindHotAndNewRequest) GetStartTimestamp() int64 {
	if x != nil {
		return x.StartTimestamp
	}
	return 0
}

type HotAndNewItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *HotAndNewItem) Reset() {
	*x = HotAndNewItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotAndNewItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotAndNewItem) ProtoMessage() {}

func (x *HotAndNewItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotAndNewItem.ProtoReflect.Descriptor instead.
func (*HotAndNewItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{17}
}

func (x *HotAndNewItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type FindHotAndNewReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*HotAndNewItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total int64            `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *FindHotAndNewReply) Reset() {
	*x = FindHotAndNewReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindHotAndNewReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindHotAndNewReply) ProtoMessage() {}

func (x *FindHotAndNewReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindHotAndNewReply.ProtoReflect.Descriptor instead.
func (*FindHotAndNewReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{18}
}

func (x *FindHotAndNewReply) GetItems() []*HotAndNewItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FindHotAndNewReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type HotAndNewV2Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Type       RecommendItemType `protobuf:"varint,2,opt,name=type,json=type,proto3,enum=api.recommend.v1.RecommendItemType" json:"type"`
	TraderCode string            `protobuf:"bytes,3,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	UserId     string            `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	SubType    string            `protobuf:"bytes,5,opt,name=sub_type,json=subType,proto3" json:"sub_type"`
	CreateTime int64             `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	Advertise  *Advertise        `protobuf:"bytes,7,opt,name=advertise,json=advertise,proto3" json:"advertise"`
}

func (x *HotAndNewV2Item) Reset() {
	*x = HotAndNewV2Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotAndNewV2Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotAndNewV2Item) ProtoMessage() {}

func (x *HotAndNewV2Item) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotAndNewV2Item.ProtoReflect.Descriptor instead.
func (*HotAndNewV2Item) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{19}
}

func (x *HotAndNewV2Item) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *HotAndNewV2Item) GetType() RecommendItemType {
	if x != nil {
		return x.Type
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *HotAndNewV2Item) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *HotAndNewV2Item) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *HotAndNewV2Item) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *HotAndNewV2Item) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HotAndNewV2Item) GetAdvertise() *Advertise {
	if x != nil {
		return x.Advertise
	}
	return nil
}

type FindHotAndNewV2Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*HotAndNewV2Item `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	Total int64              `protobuf:"varint,2,opt,name=total,json=total,proto3" json:"total"`
}

func (x *FindHotAndNewV2Reply) Reset() {
	*x = FindHotAndNewV2Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindHotAndNewV2Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindHotAndNewV2Reply) ProtoMessage() {}

func (x *FindHotAndNewV2Reply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindHotAndNewV2Reply.ProtoReflect.Descriptor instead.
func (*FindHotAndNewV2Reply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{20}
}

func (x *FindHotAndNewV2Reply) GetItems() []*HotAndNewV2Item {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FindHotAndNewV2Reply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type FollowItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type OfficialNumberType `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.recommend.v1.OfficialNumberType" json:"type"`
	Ids  []string           `protobuf:"bytes,2,rep,name=ids,json=ids,proto3" json:"ids"`
}

func (x *FollowItem) Reset() {
	*x = FollowItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowItem) ProtoMessage() {}

func (x *FollowItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowItem.ProtoReflect.Descriptor instead.
func (*FollowItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{21}
}

func (x *FollowItem) GetType() OfficialNumberType {
	if x != nil {
		return x.Type
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

func (x *FollowItem) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type FindFollowPublishRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Follows []*FollowItem `protobuf:"bytes,1,rep,name=follows,json=follows,proto3" json:"follows"`
	Page    int64         `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`
	Size    int64         `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *FindFollowPublishRequest) Reset() {
	*x = FindFollowPublishRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindFollowPublishRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindFollowPublishRequest) ProtoMessage() {}

func (x *FindFollowPublishRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindFollowPublishRequest.ProtoReflect.Descriptor instead.
func (*FindFollowPublishRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{22}
}

func (x *FindFollowPublishRequest) GetFollows() []*FollowItem {
	if x != nil {
		return x.Follows
	}
	return nil
}

func (x *FindFollowPublishRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *FindFollowPublishRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type FollowPublishItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type         RecommendItemType `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.recommend.v1.RecommendItemType" json:"type"`
	Id           string            `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`
	TraderCode   string            `protobuf:"bytes,3,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	UserId       string            `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`
	SubType      string            `protobuf:"bytes,6,opt,name=sub_type,json=subType,proto3" json:"sub_type"`
	LanguageCode string            `protobuf:"bytes,7,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
}

func (x *FollowPublishItem) Reset() {
	*x = FollowPublishItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FollowPublishItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowPublishItem) ProtoMessage() {}

func (x *FollowPublishItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowPublishItem.ProtoReflect.Descriptor instead.
func (*FollowPublishItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{23}
}

func (x *FollowPublishItem) GetType() RecommendItemType {
	if x != nil {
		return x.Type
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *FollowPublishItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FollowPublishItem) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *FollowPublishItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *FollowPublishItem) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *FollowPublishItem) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

type FindFollowPublishReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*FollowPublishItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *FindFollowPublishReply) Reset() {
	*x = FindFollowPublishReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindFollowPublishReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindFollowPublishReply) ProtoMessage() {}

func (x *FindFollowPublishReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindFollowPublishReply.ProtoReflect.Descriptor instead.
func (*FindFollowPublishReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{24}
}

func (x *FindFollowPublishReply) GetItems() []*FollowPublishItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type TraderHomeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        string      `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`                                                           // code
	ReleaseType ReleaseType `protobuf:"varint,3,opt,name=release_type,json=releaseType,proto3,enum=api.recommend.v1.ReleaseType" json:"release_type"` // 商业或者动态
	Page        int64       `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`                                                          // 页数
	Size        int64       `protobuf:"varint,5,opt,name=size,json=size,proto3" json:"size"`                                                          // 每页数据大小
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	IsService          bool               `protobuf:"varint,6,opt,name=is_service,json=isService,proto3" json:"is_service"`
	OfficialNumberType OfficialNumberType `protobuf:"varint,7,opt,name=official_number_type,json=officialNumberType,proto3,enum=api.recommend.v1.OfficialNumberType" json:"official_number_type"`
}

func (x *TraderHomeRequest) Reset() {
	*x = TraderHomeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderHomeRequest) ProtoMessage() {}

func (x *TraderHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderHomeRequest.ProtoReflect.Descriptor instead.
func (*TraderHomeRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{25}
}

func (x *TraderHomeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *TraderHomeRequest) GetReleaseType() ReleaseType {
	if x != nil {
		return x.ReleaseType
	}
	return ReleaseType_ReleaseTypeUnknown
}

func (x *TraderHomeRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TraderHomeRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *TraderHomeRequest) GetIsService() bool {
	if x != nil {
		return x.IsService
	}
	return false
}

func (x *TraderHomeRequest) GetOfficialNumberType() OfficialNumberType {
	if x != nil {
		return x.OfficialNumberType
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

type TraderHomeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*FollowPublishItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *TraderHomeReply) Reset() {
	*x = TraderHomeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderHomeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderHomeReply) ProtoMessage() {}

func (x *TraderHomeReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderHomeReply.ProtoReflect.Descriptor instead.
func (*TraderHomeReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{26}
}

func (x *TraderHomeReply) GetItems() []*FollowPublishItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type TraderPostCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"` // code
	// Deprecated: Marked as deprecated in recommend/v1/models.proto.
	IsService          bool               `protobuf:"varint,6,opt,name=is_service,json=isService,proto3" json:"is_service"`
	OfficialNumberType OfficialNumberType `protobuf:"varint,7,opt,name=official_number_type,json=officialNumberType,proto3,enum=api.recommend.v1.OfficialNumberType" json:"official_number_type"`
}

func (x *TraderPostCountRequest) Reset() {
	*x = TraderPostCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderPostCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderPostCountRequest) ProtoMessage() {}

func (x *TraderPostCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderPostCountRequest.ProtoReflect.Descriptor instead.
func (*TraderPostCountRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{27}
}

func (x *TraderPostCountRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// Deprecated: Marked as deprecated in recommend/v1/models.proto.
func (x *TraderPostCountRequest) GetIsService() bool {
	if x != nil {
		return x.IsService
	}
	return false
}

func (x *TraderPostCountRequest) GetOfficialNumberType() OfficialNumberType {
	if x != nil {
		return x.OfficialNumberType
	}
	return OfficialNumberType_OfficialNumber_Unknown
}

type TraderPostCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommerceTotal int64 `protobuf:"varint,1,opt,name=commerce_total,json=commerceTotal,proto3" json:"commerce_total"` // 商业总数量
	DynamicTotal  int64 `protobuf:"varint,2,opt,name=dynamic_total,json=dynamicTotal,proto3" json:"dynamic_total"`    // 动态总数量
}

func (x *TraderPostCountReply) Reset() {
	*x = TraderPostCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderPostCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderPostCountReply) ProtoMessage() {}

func (x *TraderPostCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderPostCountReply.ProtoReflect.Descriptor instead.
func (*TraderPostCountReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{28}
}

func (x *TraderPostCountReply) GetCommerceTotal() int64 {
	if x != nil {
		return x.CommerceTotal
	}
	return 0
}

func (x *TraderPostCountReply) GetDynamicTotal() int64 {
	if x != nil {
		return x.DynamicTotal
	}
	return 0
}

type SearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"` // 搜索内容
	Page    int64  `protobuf:"varint,2,opt,name=page,json=page,proto3" json:"page"`         // 页数
	Size    int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`         // 每页数据大小
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{29}
}

func (x *SearchRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SearchRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type SearchItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       RecommendItemType `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.recommend.v1.RecommendItemType" json:"type"` // 搜索结果类型
	Id         string            `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`                                                // 对象ID
	TraderCode string            `protobuf:"bytes,3,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`                      // 交易商
	UserId     string            `protobuf:"bytes,4,opt,name=user_id,json=userId,proto3" json:"user_id"`                                  // 用户ID
	SubType    string            `protobuf:"bytes,6,opt,name=sub_type,json=subType,proto3" json:"sub_type"`                               // 子类型:article:(zx_jys:交易商新闻;zx_jyshuiping:交易商汇评;zx_jyshuodong:交易商活动;zx_baoguang:曝光新闻;zx_yaowen:行业新闻;ty_yaowen:天眼新闻;provider_news:服务商新闻);discover(commerce:商业;dynamic:动态)
	Title      string            `protobuf:"bytes,7,opt,name=title,json=title,proto3" json:"title"`                                       // 测试使用
}

func (x *SearchItem) Reset() {
	*x = SearchItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchItem) ProtoMessage() {}

func (x *SearchItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchItem.ProtoReflect.Descriptor instead.
func (*SearchItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{30}
}

func (x *SearchItem) GetType() RecommendItemType {
	if x != nil {
		return x.Type
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *SearchItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SearchItem) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *SearchItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SearchItem) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *SearchItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type SearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*SearchItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *SearchReply) Reset() {
	*x = SearchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchReply) ProtoMessage() {}

func (x *SearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchReply.ProtoReflect.Descriptor instead.
func (*SearchReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{31}
}

func (x *SearchReply) GetItems() []*SearchItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type FindSearchTitleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"` // 搜索内容
	Size    int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`         // 每页数据大小
}

func (x *FindSearchTitleRequest) Reset() {
	*x = FindSearchTitleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindSearchTitleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindSearchTitleRequest) ProtoMessage() {}

func (x *FindSearchTitleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindSearchTitleRequest.ProtoReflect.Descriptor instead.
func (*FindSearchTitleRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{32}
}

func (x *FindSearchTitleRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *FindSearchTitleRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type SearchTitleItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title     string `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`             // 标题
	Highlight string `protobuf:"bytes,2,opt,name=highlight,json=highlight,proto3" json:"highlight"` // 高亮标题
}

func (x *SearchTitleItem) Reset() {
	*x = SearchTitleItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTitleItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTitleItem) ProtoMessage() {}

func (x *SearchTitleItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTitleItem.ProtoReflect.Descriptor instead.
func (*SearchTitleItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{33}
}

func (x *SearchTitleItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SearchTitleItem) GetHighlight() string {
	if x != nil {
		return x.Highlight
	}
	return ""
}

type FindSearchTitleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*SearchTitleItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *FindSearchTitleReply) Reset() {
	*x = FindSearchTitleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindSearchTitleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindSearchTitleReply) ProtoMessage() {}

func (x *FindSearchTitleReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindSearchTitleReply.ProtoReflect.Descriptor instead.
func (*FindSearchTitleReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{34}
}

func (x *FindSearchTitleReply) GetItems() []*SearchTitleItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type InterestedUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FollowUsers []string `protobuf:"bytes,1,rep,name=follow_users,json=followUsers,proto3" json:"follow_users"`
	Size        int64    `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
}

func (x *InterestedUserRequest) Reset() {
	*x = InterestedUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterestedUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterestedUserRequest) ProtoMessage() {}

func (x *InterestedUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterestedUserRequest.ProtoReflect.Descriptor instead.
func (*InterestedUserRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{35}
}

func (x *InterestedUserRequest) GetFollowUsers() []string {
	if x != nil {
		return x.FollowUsers
	}
	return nil
}

func (x *InterestedUserRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type InterestedUserReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []string `protobuf:"bytes,1,rep,name=users,json=users,proto3" json:"users"`
}

func (x *InterestedUserReply) Reset() {
	*x = InterestedUserReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InterestedUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InterestedUserReply) ProtoMessage() {}

func (x *InterestedUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InterestedUserReply.ProtoReflect.Descriptor instead.
func (*InterestedUserReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{36}
}

func (x *InterestedUserReply) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type FindHotContentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *FindHotContentRequest) Reset() {
	*x = FindHotContentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindHotContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindHotContentRequest) ProtoMessage() {}

func (x *FindHotContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindHotContentRequest.ProtoReflect.Descriptor instead.
func (*FindHotContentRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{37}
}

type FindHotContentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*RecommendItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *FindHotContentReply) Reset() {
	*x = FindHotContentReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindHotContentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindHotContentReply) ProtoMessage() {}

func (x *FindHotContentReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindHotContentReply.ProtoReflect.Descriptor instead.
func (*FindHotContentReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{38}
}

func (x *FindHotContentReply) GetItems() []*RecommendItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type RecommendUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int64 `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
}

func (x *RecommendUserRequest) Reset() {
	*x = RecommendUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendUserRequest) ProtoMessage() {}

func (x *RecommendUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendUserRequest.ProtoReflect.Descriptor instead.
func (*RecommendUserRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{39}
}

func (x *RecommendUserRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type RecommendUserReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []string `protobuf:"bytes,1,rep,name=users,json=users,proto3" json:"users"`
}

func (x *RecommendUserReply) Reset() {
	*x = RecommendUserReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendUserReply) ProtoMessage() {}

func (x *RecommendUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendUserReply.ProtoReflect.Descriptor instead.
func (*RecommendUserReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{40}
}

func (x *RecommendUserReply) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type ActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Activity string `protobuf:"bytes,1,opt,name=activity,json=activity,proto3" json:"activity"`
	Rank     string `protobuf:"bytes,2,opt,name=rank,json=rank,proto3" json:"rank"`
}

func (x *ActivityRequest) Reset() {
	*x = ActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityRequest) ProtoMessage() {}

func (x *ActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityRequest.ProtoReflect.Descriptor instead.
func (*ActivityRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{41}
}

func (x *ActivityRequest) GetActivity() string {
	if x != nil {
		return x.Activity
	}
	return ""
}

func (x *ActivityRequest) GetRank() string {
	if x != nil {
		return x.Rank
	}
	return ""
}

type ActivityItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	Rank      int64  `protobuf:"varint,2,opt,name=rank,json=rank,proto3" json:"rank"`
	Score     int64  `protobuf:"varint,3,opt,name=score,json=score,proto3" json:"score"`
	HomeUrl   string `protobuf:"bytes,4,opt,name=home_url,json=homeUrl,proto3" json:"home_url"`
	WeekScore int64  `protobuf:"varint,5,opt,name=week_score,json=weekScore,proto3" json:"week_score"`
}

func (x *ActivityItem) Reset() {
	*x = ActivityItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityItem) ProtoMessage() {}

func (x *ActivityItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityItem.ProtoReflect.Descriptor instead.
func (*ActivityItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{42}
}

func (x *ActivityItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ActivityItem) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *ActivityItem) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *ActivityItem) GetHomeUrl() string {
	if x != nil {
		return x.HomeUrl
	}
	return ""
}

func (x *ActivityItem) GetWeekScore() int64 {
	if x != nil {
		return x.WeekScore
	}
	return 0
}

type ActivityReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ActivityItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *ActivityReply) Reset() {
	*x = ActivityReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityReply) ProtoMessage() {}

func (x *ActivityReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityReply.ProtoReflect.Descriptor instead.
func (*ActivityReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{43}
}

func (x *ActivityReply) GetItems() []*ActivityItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type RecommendFeedbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId     string                    `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id"`
	ItemType   RecommendItemType         `protobuf:"varint,2,opt,name=item_type,json=itemType,proto3,enum=api.recommend.v1.RecommendItemType" json:"item_type"`
	ItemUserId string                    `protobuf:"bytes,3,opt,name=item_user_id,json=itemUserId,proto3" json:"item_user_id"`
	Category   RecommendFeedbackCategory `protobuf:"varint,4,opt,name=category,json=category,proto3,enum=api.recommend.v1.RecommendFeedbackCategory" json:"category"`
}

func (x *RecommendFeedbackRequest) Reset() {
	*x = RecommendFeedbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendFeedbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendFeedbackRequest) ProtoMessage() {}

func (x *RecommendFeedbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendFeedbackRequest.ProtoReflect.Descriptor instead.
func (*RecommendFeedbackRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{44}
}

func (x *RecommendFeedbackRequest) GetItemId() string {
	if x != nil {
		return x.ItemId
	}
	return ""
}

func (x *RecommendFeedbackRequest) GetItemType() RecommendItemType {
	if x != nil {
		return x.ItemType
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *RecommendFeedbackRequest) GetItemUserId() string {
	if x != nil {
		return x.ItemUserId
	}
	return ""
}

func (x *RecommendFeedbackRequest) GetCategory() RecommendFeedbackCategory {
	if x != nil {
		return x.Category
	}
	return RecommendFeedbackCategory_RecommendFeedbackCategoryContent
}

type RecommendFeedbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RecommendFeedbackResponse) Reset() {
	*x = RecommendFeedbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendFeedbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendFeedbackResponse) ProtoMessage() {}

func (x *RecommendFeedbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendFeedbackResponse.ProtoReflect.Descriptor instead.
func (*RecommendFeedbackResponse) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{45}
}

type HotContentRankingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int64 `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
}

func (x *HotContentRankingRequest) Reset() {
	*x = HotContentRankingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotContentRankingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotContentRankingRequest) ProtoMessage() {}

func (x *HotContentRankingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotContentRankingRequest.ProtoReflect.Descriptor instead.
func (*HotContentRankingRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{46}
}

func (x *HotContentRankingRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type HotContentRankingItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Type   RecommendItemType `protobuf:"varint,2,opt,name=type,json=type,proto3,enum=api.recommend.v1.RecommendItemType" json:"type"`
	Score  int64             `protobuf:"varint,3,opt,name=score,json=score,proto3" json:"score"`
	Label  HotContentLabel   `protobuf:"varint,4,opt,name=label,json=label,proto3,enum=api.recommend.v1.HotContentLabel" json:"label"`
	UserId string            `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id"`
}

func (x *HotContentRankingItem) Reset() {
	*x = HotContentRankingItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotContentRankingItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotContentRankingItem) ProtoMessage() {}

func (x *HotContentRankingItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotContentRankingItem.ProtoReflect.Descriptor instead.
func (*HotContentRankingItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{47}
}

func (x *HotContentRankingItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *HotContentRankingItem) GetType() RecommendItemType {
	if x != nil {
		return x.Type
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *HotContentRankingItem) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *HotContentRankingItem) GetLabel() HotContentLabel {
	if x != nil {
		return x.Label
	}
	return HotContentLabel_HotContentLabelNone
}

func (x *HotContentRankingItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type HotContentRankingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*HotContentRankingItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *HotContentRankingResponse) Reset() {
	*x = HotContentRankingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HotContentRankingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HotContentRankingResponse) ProtoMessage() {}

func (x *HotContentRankingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HotContentRankingResponse.ProtoReflect.Descriptor instead.
func (*HotContentRankingResponse) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{48}
}

func (x *HotContentRankingResponse) GetItems() []*HotContentRankingItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type RankingScopeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RankingScopeRequest) Reset() {
	*x = RankingScopeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankingScopeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankingScopeRequest) ProtoMessage() {}

func (x *RankingScopeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankingScopeRequest.ProtoReflect.Descriptor instead.
func (*RankingScopeRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{49}
}

type RankingScopeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Week       []string `protobuf:"bytes,1,rep,name=week,json=week,proto3" json:"week"`                     // 周排行列表
	MonthStart string   `protobuf:"bytes,2,opt,name=month_start,json=monthStart,proto3" json:"month_start"` // 月排行开始时间
	MonthEnd   string   `protobuf:"bytes,3,opt,name=month_end,json=monthEnd,proto3" json:"month_end"`       // 月排行结束时间
	WeekStart  string   `protobuf:"bytes,4,opt,name=week_start,json=weekStart,proto3" json:"week_start"`
	WeekEnd    string   `protobuf:"bytes,5,opt,name=week_end,json=weekEnd,proto3" json:"week_end"`
}

func (x *RankingScopeResponse) Reset() {
	*x = RankingScopeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankingScopeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankingScopeResponse) ProtoMessage() {}

func (x *RankingScopeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankingScopeResponse.ProtoReflect.Descriptor instead.
func (*RankingScopeResponse) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{50}
}

func (x *RankingScopeResponse) GetWeek() []string {
	if x != nil {
		return x.Week
	}
	return nil
}

func (x *RankingScopeResponse) GetMonthStart() string {
	if x != nil {
		return x.MonthStart
	}
	return ""
}

func (x *RankingScopeResponse) GetMonthEnd() string {
	if x != nil {
		return x.MonthEnd
	}
	return ""
}

func (x *RankingScopeResponse) GetWeekStart() string {
	if x != nil {
		return x.WeekStart
	}
	return ""
}

func (x *RankingScopeResponse) GetWeekEnd() string {
	if x != nil {
		return x.WeekEnd
	}
	return ""
}

type CreatorRankingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category CreatorRankingCategory `protobuf:"varint,1,opt,name=category,json=category,proto3,enum=api.recommend.v1.CreatorRankingCategory" json:"category"`
	Size     int64                  `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Id       string                 `protobuf:"bytes,3,opt,name=id,json=id,proto3" json:"id"`
}

func (x *CreatorRankingRequest) Reset() {
	*x = CreatorRankingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatorRankingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatorRankingRequest) ProtoMessage() {}

func (x *CreatorRankingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatorRankingRequest.ProtoReflect.Descriptor instead.
func (*CreatorRankingRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{51}
}

func (x *CreatorRankingRequest) GetCategory() CreatorRankingCategory {
	if x != nil {
		return x.Category
	}
	return CreatorRankingCategory_CreatorRankingCategoryWeek
}

func (x *CreatorRankingRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CreatorRankingRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CreatorRankingItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"` // 用户ID
	Score  int64  `protobuf:"varint,2,opt,name=score,json=score,proto3" json:"score"`     // 分数
}

func (x *CreatorRankingItem) Reset() {
	*x = CreatorRankingItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatorRankingItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatorRankingItem) ProtoMessage() {}

func (x *CreatorRankingItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatorRankingItem.ProtoReflect.Descriptor instead.
func (*CreatorRankingItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{52}
}

func (x *CreatorRankingItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreatorRankingItem) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type CreatorRankingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*CreatorRankingItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
	UserRank  int64                 `protobuf:"varint,2,opt,name=user_rank,json=userRank,proto3" json:"user_rank"`    // 用户所在排名
	State     string                `protobuf:"bytes,3,opt,name=state,json=state,proto3" json:"state"`                // 第几期
	UserScore int64                 `protobuf:"varint,4,opt,name=user_score,json=userScore,proto3" json:"user_score"` // 用户分数
	Id        string                `protobuf:"bytes,5,opt,name=id,json=id,proto3" json:"id"`                         // 榜单ID
}

func (x *CreatorRankingResponse) Reset() {
	*x = CreatorRankingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatorRankingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatorRankingResponse) ProtoMessage() {}

func (x *CreatorRankingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatorRankingResponse.ProtoReflect.Descriptor instead.
func (*CreatorRankingResponse) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{53}
}

func (x *CreatorRankingResponse) GetItems() []*CreatorRankingItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *CreatorRankingResponse) GetUserRank() int64 {
	if x != nil {
		return x.UserRank
	}
	return 0
}

func (x *CreatorRankingResponse) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *CreatorRankingResponse) GetUserScore() int64 {
	if x != nil {
		return x.UserScore
	}
	return 0
}

func (x *CreatorRankingResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CreatorRankingNoticeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category CreatorRankingCategory `protobuf:"varint,1,opt,name=category,json=category,proto3,enum=api.recommend.v1.CreatorRankingCategory" json:"category"`
	Id       string                 `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`
}

func (x *CreatorRankingNoticeRequest) Reset() {
	*x = CreatorRankingNoticeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatorRankingNoticeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatorRankingNoticeRequest) ProtoMessage() {}

func (x *CreatorRankingNoticeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatorRankingNoticeRequest.ProtoReflect.Descriptor instead.
func (*CreatorRankingNoticeRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{54}
}

func (x *CreatorRankingNoticeRequest) GetCategory() CreatorRankingCategory {
	if x != nil {
		return x.Category
	}
	return CreatorRankingCategory_CreatorRankingCategoryWeek
}

func (x *CreatorRankingNoticeRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CreatorRankingNoticeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreatorRankingNoticeResponse) Reset() {
	*x = CreatorRankingNoticeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatorRankingNoticeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatorRankingNoticeResponse) ProtoMessage() {}

func (x *CreatorRankingNoticeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatorRankingNoticeResponse.ProtoReflect.Descriptor instead.
func (*CreatorRankingNoticeResponse) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{55}
}

// ------------------------- 下面接口内部测试使用 -----------------------------------------
type UserContentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds      []string    `protobuf:"bytes,1,rep,name=user_ids,json=userIds,proto3" json:"user_ids"`
	Size         int64       `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page         int64       `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
	Type         string      `protobuf:"bytes,5,opt,name=type,json=type,proto3" json:"type"`
	LanguageCode string      `protobuf:"bytes,6,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	CountryCode  string      `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	Project      string      `protobuf:"bytes,8,opt,name=project,json=project,proto3" json:"project"`
	ReleaseType  ReleaseType `protobuf:"varint,9,opt,name=release_type,json=releaseType,proto3,enum=api.recommend.v1.ReleaseType" json:"release_type"`
	Scale        string      `protobuf:"bytes,10,opt,name=scale,json=scale,proto3" json:"scale"`
	Offset       string      `protobuf:"bytes,11,opt,name=offset,json=offset,proto3" json:"offset"`
	Decay        float32     `protobuf:"fixed32,12,opt,name=decay,json=decay,proto3" json:"decay"`
}

func (x *UserContentRequest) Reset() {
	*x = UserContentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserContentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserContentRequest) ProtoMessage() {}

func (x *UserContentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserContentRequest.ProtoReflect.Descriptor instead.
func (*UserContentRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{56}
}

func (x *UserContentRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *UserContentRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UserContentRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *UserContentRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserContentRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *UserContentRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UserContentRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *UserContentRequest) GetReleaseType() ReleaseType {
	if x != nil {
		return x.ReleaseType
	}
	return ReleaseType_ReleaseTypeUnknown
}

func (x *UserContentRequest) GetScale() string {
	if x != nil {
		return x.Scale
	}
	return ""
}

func (x *UserContentRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *UserContentRequest) GetDecay() float32 {
	if x != nil {
		return x.Decay
	}
	return 0
}

type RecommendTestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId             string            `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId           string            `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	Size               int64             `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page               int64             `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
	LanguageCode       string            `protobuf:"bytes,6,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	CountryCode        string            `protobuf:"bytes,7,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	RealTime           bool              `protobuf:"varint,9,opt,name=real_time,json=realTime,proto3" json:"real_time"`
	Scale              string            `protobuf:"bytes,10,opt,name=scale,json=scale,proto3" json:"scale"`
	Offset             string            `protobuf:"bytes,11,opt,name=offset,json=offset,proto3" json:"offset"`
	Decay              float32           `protobuf:"fixed32,12,opt,name=decay,json=decay,proto3" json:"decay"`
	PreferredLanguages string            `protobuf:"bytes,14,opt,name=preferred_languages,json=preferredLanguages,proto3" json:"preferred_languages"`
	Category           RecommendCategory `protobuf:"varint,15,opt,name=category,json=category,proto3,enum=api.recommend.v1.RecommendCategory" json:"category"`
}

func (x *RecommendTestRequest) Reset() {
	*x = RecommendTestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendTestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendTestRequest) ProtoMessage() {}

func (x *RecommendTestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendTestRequest.ProtoReflect.Descriptor instead.
func (*RecommendTestRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{57}
}

func (x *RecommendTestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RecommendTestRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RecommendTestRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *RecommendTestRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RecommendTestRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *RecommendTestRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *RecommendTestRequest) GetRealTime() bool {
	if x != nil {
		return x.RealTime
	}
	return false
}

func (x *RecommendTestRequest) GetScale() string {
	if x != nil {
		return x.Scale
	}
	return ""
}

func (x *RecommendTestRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *RecommendTestRequest) GetDecay() float32 {
	if x != nil {
		return x.Decay
	}
	return 0
}

func (x *RecommendTestRequest) GetPreferredLanguages() string {
	if x != nil {
		return x.PreferredLanguages
	}
	return ""
}

func (x *RecommendTestRequest) GetCategory() RecommendCategory {
	if x != nil {
		return x.Category
	}
	return RecommendCategory_RecommendCategoryRecommend
}

type RecommendTestItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type         string    `protobuf:"bytes,1,opt,name=type,json=type,proto3" json:"type"`
	Id           string    `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`
	LanguageCode string    `protobuf:"bytes,5,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	Score        float32   `protobuf:"fixed32,6,opt,name=score,json=score,proto3" json:"score"`
	UserId       string    `protobuf:"bytes,9,opt,name=user_id,json=userId,proto3" json:"user_id"`
	CreateTime   string    `protobuf:"bytes,10,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	TraderCode   string    `protobuf:"bytes,11,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	RankParams   []float32 `protobuf:"fixed32,12,rep,packed,name=rank_params,json=rankParams,proto3" json:"rank_params"`
	From         string    `protobuf:"bytes,13,opt,name=from,json=from,proto3" json:"from"`
	RankShpValue []float32 `protobuf:"fixed32,14,rep,packed,name=rank_shp_value,json=rankShpValue,proto3" json:"rank_shp_value"`
	DurationDays int64     `protobuf:"varint,15,opt,name=duration_days,json=durationDays,proto3" json:"duration_days"`
}

func (x *RecommendTestItem) Reset() {
	*x = RecommendTestItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendTestItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendTestItem) ProtoMessage() {}

func (x *RecommendTestItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendTestItem.ProtoReflect.Descriptor instead.
func (*RecommendTestItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{58}
}

func (x *RecommendTestItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecommendTestItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RecommendTestItem) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *RecommendTestItem) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RecommendTestItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RecommendTestItem) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *RecommendTestItem) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *RecommendTestItem) GetRankParams() []float32 {
	if x != nil {
		return x.RankParams
	}
	return nil
}

func (x *RecommendTestItem) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *RecommendTestItem) GetRankShpValue() []float32 {
	if x != nil {
		return x.RankShpValue
	}
	return nil
}

func (x *RecommendTestItem) GetDurationDays() int64 {
	if x != nil {
		return x.DurationDays
	}
	return 0
}

type RecommendTestReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*RecommendTestItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *RecommendTestReply) Reset() {
	*x = RecommendTestReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecommendTestReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendTestReply) ProtoMessage() {}

func (x *RecommendTestReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendTestReply.ProtoReflect.Descriptor instead.
func (*RecommendTestReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{59}
}

func (x *RecommendTestReply) GetItems() []*RecommendTestItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type ContentSimilarityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string  `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	IdType       string  `protobuf:"bytes,2,opt,name=id_type,json=idType,proto3" json:"id_type"`
	TargetType   string  `protobuf:"bytes,3,opt,name=target_type,json=targetType,proto3" json:"target_type"`
	LanguageCode string  `protobuf:"bytes,4,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
	Size         int64   `protobuf:"varint,5,opt,name=size,json=size,proto3" json:"size"`
	Page         int64   `protobuf:"varint,6,opt,name=page,json=page,proto3" json:"page"`
	Project      string  `protobuf:"bytes,7,opt,name=project,json=project,proto3" json:"project"`
	Scale        string  `protobuf:"bytes,8,opt,name=scale,json=scale,proto3" json:"scale"`
	Offset       string  `protobuf:"bytes,9,opt,name=offset,json=offset,proto3" json:"offset"`
	Decay        float32 `protobuf:"fixed32,10,opt,name=decay,json=decay,proto3" json:"decay"`
	CountryCode  string  `protobuf:"bytes,11,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
}

func (x *ContentSimilarityRequest) Reset() {
	*x = ContentSimilarityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentSimilarityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentSimilarityRequest) ProtoMessage() {}

func (x *ContentSimilarityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentSimilarityRequest.ProtoReflect.Descriptor instead.
func (*ContentSimilarityRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{60}
}

func (x *ContentSimilarityRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContentSimilarityRequest) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

func (x *ContentSimilarityRequest) GetTargetType() string {
	if x != nil {
		return x.TargetType
	}
	return ""
}

func (x *ContentSimilarityRequest) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *ContentSimilarityRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ContentSimilarityRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ContentSimilarityRequest) GetProject() string {
	if x != nil {
		return x.Project
	}
	return ""
}

func (x *ContentSimilarityRequest) GetScale() string {
	if x != nil {
		return x.Scale
	}
	return ""
}

func (x *ContentSimilarityRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *ContentSimilarityRequest) GetDecay() float32 {
	if x != nil {
		return x.Decay
	}
	return 0
}

func (x *ContentSimilarityRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

type ContentSimilarityReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*RecommendTestItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *ContentSimilarityReply) Reset() {
	*x = ContentSimilarityReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentSimilarityReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentSimilarityReply) ProtoMessage() {}

func (x *ContentSimilarityReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentSimilarityReply.ProtoReflect.Descriptor instead.
func (*ContentSimilarityReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{61}
}

func (x *ContentSimilarityReply) GetItems() []*RecommendTestItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UserBehaviorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
}

func (x *UserBehaviorRequest) Reset() {
	*x = UserBehaviorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBehaviorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBehaviorRequest) ProtoMessage() {}

func (x *UserBehaviorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBehaviorRequest.ProtoReflect.Descriptor instead.
func (*UserBehaviorRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{62}
}

func (x *UserBehaviorRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserBehaviorRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type UserBehaviorItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId      string `protobuf:"bytes,1,opt,name=event_id,json=eventId,proto3" json:"event_id"`
	Event        string `protobuf:"bytes,2,opt,name=event,json=event,proto3" json:"event"`
	EventTime    string `protobuf:"bytes,3,opt,name=event_time,json=eventTime,proto3" json:"event_time"`
	ObjectId     string `protobuf:"bytes,4,opt,name=object_id,json=objectId,proto3" json:"object_id"`
	LanguageCode string `protobuf:"bytes,5,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
}

func (x *UserBehaviorItem) Reset() {
	*x = UserBehaviorItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBehaviorItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBehaviorItem) ProtoMessage() {}

func (x *UserBehaviorItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBehaviorItem.ProtoReflect.Descriptor instead.
func (*UserBehaviorItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{63}
}

func (x *UserBehaviorItem) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *UserBehaviorItem) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *UserBehaviorItem) GetEventTime() string {
	if x != nil {
		return x.EventTime
	}
	return ""
}

func (x *UserBehaviorItem) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

func (x *UserBehaviorItem) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

type UserBehaviorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*UserBehaviorItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *UserBehaviorReply) Reset() {
	*x = UserBehaviorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBehaviorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBehaviorReply) ProtoMessage() {}

func (x *UserBehaviorReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBehaviorReply.ProtoReflect.Descriptor instead.
func (*UserBehaviorReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{64}
}

func (x *UserBehaviorReply) GetItems() []*UserBehaviorItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UserOriginBehaviorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
	Size     int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
	Page     int64  `protobuf:"varint,4,opt,name=page,json=page,proto3" json:"page"`
}

func (x *UserOriginBehaviorRequest) Reset() {
	*x = UserOriginBehaviorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserOriginBehaviorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserOriginBehaviorRequest) ProtoMessage() {}

func (x *UserOriginBehaviorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserOriginBehaviorRequest.ProtoReflect.Descriptor instead.
func (*UserOriginBehaviorRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{65}
}

func (x *UserOriginBehaviorRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserOriginBehaviorRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UserOriginBehaviorRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UserOriginBehaviorRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type UserOriginBehaviorItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventName    string `protobuf:"bytes,2,opt,name=event_name,json=eventName,proto3" json:"event_name"`
	EventTime    string `protobuf:"bytes,3,opt,name=event_time,json=eventTime,proto3" json:"event_time"`
	ObjectId     string `protobuf:"bytes,4,opt,name=object_id,json=objectId,proto3" json:"object_id"`
	LanguageCode string `protobuf:"bytes,5,opt,name=language_code,json=languageCode,proto3" json:"language_code"`
}

func (x *UserOriginBehaviorItem) Reset() {
	*x = UserOriginBehaviorItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserOriginBehaviorItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserOriginBehaviorItem) ProtoMessage() {}

func (x *UserOriginBehaviorItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserOriginBehaviorItem.ProtoReflect.Descriptor instead.
func (*UserOriginBehaviorItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{66}
}

func (x *UserOriginBehaviorItem) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *UserOriginBehaviorItem) GetEventTime() string {
	if x != nil {
		return x.EventTime
	}
	return ""
}

func (x *UserOriginBehaviorItem) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

func (x *UserOriginBehaviorItem) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

type UserOriginBehaviorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*UserOriginBehaviorItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *UserOriginBehaviorReply) Reset() {
	*x = UserOriginBehaviorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserOriginBehaviorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserOriginBehaviorReply) ProtoMessage() {}

func (x *UserOriginBehaviorReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserOriginBehaviorReply.ProtoReflect.Descriptor instead.
func (*UserOriginBehaviorReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{67}
}

func (x *UserOriginBehaviorReply) GetItems() []*UserOriginBehaviorItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UserVectorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
}

func (x *UserVectorRequest) Reset() {
	*x = UserVectorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserVectorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserVectorRequest) ProtoMessage() {}

func (x *UserVectorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserVectorRequest.ProtoReflect.Descriptor instead.
func (*UserVectorRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{68}
}

func (x *UserVectorRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserVectorRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type UserVectorItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     string `protobuf:"bytes,1,opt,name=type,json=type,proto3" json:"type"`
	ObjectId string `protobuf:"bytes,2,opt,name=object_id,json=objectId,proto3" json:"object_id"`
}

func (x *UserVectorItem) Reset() {
	*x = UserVectorItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserVectorItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserVectorItem) ProtoMessage() {}

func (x *UserVectorItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserVectorItem.ProtoReflect.Descriptor instead.
func (*UserVectorItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{69}
}

func (x *UserVectorItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserVectorItem) GetObjectId() string {
	if x != nil {
		return x.ObjectId
	}
	return ""
}

type UserVectorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UpdatedAt string            `protobuf:"bytes,1,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	Items     []*UserVectorItem `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *UserVectorReply) Reset() {
	*x = UserVectorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserVectorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserVectorReply) ProtoMessage() {}

func (x *UserVectorReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserVectorReply.ProtoReflect.Descriptor instead.
func (*UserVectorReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{70}
}

func (x *UserVectorReply) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *UserVectorReply) GetItems() []*UserVectorItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type ResetUserBehaviorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId   string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	DeviceId string `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id"`
}

func (x *ResetUserBehaviorRequest) Reset() {
	*x = ResetUserBehaviorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetUserBehaviorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserBehaviorRequest) ProtoMessage() {}

func (x *ResetUserBehaviorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserBehaviorRequest.ProtoReflect.Descriptor instead.
func (*ResetUserBehaviorRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{71}
}

func (x *ResetUserBehaviorRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ResetUserBehaviorRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type ResetUserBehaviorReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ResetUserBehaviorReply) Reset() {
	*x = ResetUserBehaviorReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetUserBehaviorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetUserBehaviorReply) ProtoMessage() {}

func (x *ResetUserBehaviorReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetUserBehaviorReply.ProtoReflect.Descriptor instead.
func (*ResetUserBehaviorReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{72}
}

type SearchTitleAutoCompleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Keyword string `protobuf:"bytes,1,opt,name=keyword,json=keyword,proto3" json:"keyword"`
	Size    int64  `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
}

func (x *SearchTitleAutoCompleteRequest) Reset() {
	*x = SearchTitleAutoCompleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTitleAutoCompleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTitleAutoCompleteRequest) ProtoMessage() {}

func (x *SearchTitleAutoCompleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTitleAutoCompleteRequest.ProtoReflect.Descriptor instead.
func (*SearchTitleAutoCompleteRequest) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{73}
}

func (x *SearchTitleAutoCompleteRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchTitleAutoCompleteRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type SearchTitleAutoCompleteItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                string            `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Title             string            `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Highlight         string            `protobuf:"bytes,3,opt,name=highlight,json=highlight,proto3" json:"highlight"`
	ItemType          RecommendItemType `protobuf:"varint,4,opt,name=item_type,json=itemType,proto3,enum=api.recommend.v1.RecommendItemType" json:"item_type"`
	UserId            string            `protobuf:"bytes,5,opt,name=user_id,json=userId,proto3" json:"user_id"`
	Lang              string            `protobuf:"bytes,6,opt,name=lang,json=lang,proto3" json:"lang"`
	ArtCategoryId     string            `protobuf:"bytes,8,opt,name=art_category_id,json=artCategoryId,proto3" json:"art_category_id"`
	AffEnterpriseCode string            `protobuf:"bytes,9,opt,name=aff_enterprise_code,json=affEnterpriseCode,proto3" json:"aff_enterprise_code"`
}

func (x *SearchTitleAutoCompleteItem) Reset() {
	*x = SearchTitleAutoCompleteItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTitleAutoCompleteItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTitleAutoCompleteItem) ProtoMessage() {}

func (x *SearchTitleAutoCompleteItem) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTitleAutoCompleteItem.ProtoReflect.Descriptor instead.
func (*SearchTitleAutoCompleteItem) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{74}
}

func (x *SearchTitleAutoCompleteItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SearchTitleAutoCompleteItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SearchTitleAutoCompleteItem) GetHighlight() string {
	if x != nil {
		return x.Highlight
	}
	return ""
}

func (x *SearchTitleAutoCompleteItem) GetItemType() RecommendItemType {
	if x != nil {
		return x.ItemType
	}
	return RecommendItemType_RecommendItemTypeUnknown
}

func (x *SearchTitleAutoCompleteItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SearchTitleAutoCompleteItem) GetLang() string {
	if x != nil {
		return x.Lang
	}
	return ""
}

func (x *SearchTitleAutoCompleteItem) GetArtCategoryId() string {
	if x != nil {
		return x.ArtCategoryId
	}
	return ""
}

func (x *SearchTitleAutoCompleteItem) GetAffEnterpriseCode() string {
	if x != nil {
		return x.AffEnterpriseCode
	}
	return ""
}

type SearchTitleAutoCompleteReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*SearchTitleAutoCompleteItem `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *SearchTitleAutoCompleteReply) Reset() {
	*x = SearchTitleAutoCompleteReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_recommend_v1_models_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTitleAutoCompleteReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTitleAutoCompleteReply) ProtoMessage() {}

func (x *SearchTitleAutoCompleteReply) ProtoReflect() protoreflect.Message {
	mi := &file_recommend_v1_models_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTitleAutoCompleteReply.ProtoReflect.Descriptor instead.
func (*SearchTitleAutoCompleteReply) Descriptor() ([]byte, []int) {
	return file_recommend_v1_models_proto_rawDescGZIP(), []int{75}
}

func (x *SearchTitleAutoCompleteReply) GetItems() []*SearchTitleAutoCompleteItem {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_recommend_v1_models_proto protoreflect.FileDescriptor

var file_recommend_v1_models_proto_rawDesc = []byte{
	0x0a, 0x19, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70,
	0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x07,
	0x0a, 0x10, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x18, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0f, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0x18, 0x01,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1b, 0xe5,
	0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0,
	0x8f, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x32, 0x30, 0x3a, 0x02, 0x32, 0x30, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6,
	0x95, 0xb0, 0x3a, 0x01, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x88, 0x01, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x74, 0x92, 0x41, 0x6f, 0x2a,
	0x6d, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0xe9, 0xbb,
	0x98, 0xe8, 0xae, 0xa4, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0x3a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x3a, 0xe8, 0xb5, 0x84, 0xe8, 0xae, 0xaf, 0x3b,
	0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0x3b,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x3a, 0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0x3b,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86,
	0x3b, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x18, 0x01,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34,
	0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae,
	0xb6, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0x92, 0x41, 0x2b, 0x2a, 0x29, 0xe9, 0xa1, 0xb9, 0xe7,
	0x9b, 0xae, 0x3a, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0x3b, 0x77, 0x69, 0x6b, 0x69, 0x62, 0x69,
	0x74, 0x3b, 0x77, 0x69, 0x6b, 0x69, 0x74, 0x72, 0x61, 0x64, 0x65, 0x3b, 0x77, 0x69, 0x6b, 0x69,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x6a, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x28, 0x92, 0x41, 0x25, 0x2a, 0x23, 0xe5, 0x8f, 0x91, 0xe7,
	0x8e, 0xb0, 0xe9, 0xa1, 0xb5, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x31, 0x3a,
	0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b, 0x32, 0x3a, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x52,
	0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x13,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x35, 0x92, 0x41, 0x30, 0x2a, 0x2e,
	0xe5, 0x81, 0x8f, 0xe5, 0xa5, 0xbd, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x2c, 0xe5, 0xa4, 0x9a,
	0xe4, 0xb8, 0xaa, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe4, 0xb9, 0x8b, 0xe9, 0x97, 0xb4, 0xe7,
	0x94, 0xa8, 0xe9, 0x80, 0x97, 0xe5, 0x8f, 0xb7, 0xe5, 0x88, 0x86, 0xe9, 0x9a, 0x94, 0x18, 0x01,
	0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x73, 0x68,
	0x6f, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92,
	0x41, 0x1a, 0x2a, 0x18, 0xe9, 0xa6, 0x96, 0xe9, 0xa1, 0xb5, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba,
	0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x52, 0x0d, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x61, 0x67, 0x65, 0x12, 0x5c, 0x0a, 0x0d, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x79, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8,
	0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe8, 0xae, 0xa4, 0xe8, 0xaf, 0x81, 0x52, 0x0c, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x22, 0xbe, 0x04, 0x0a, 0x12, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14,
	0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d,
	0xae, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0x88, 0x86, 0xe9, 0xa1, 0xb5, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x73, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x42, 0x32, 0x92, 0x41, 0x2f, 0x2a, 0x2d, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef,
	0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xef, 0xbc, 0x9b, 0x31,
	0xef, 0xbc, 0x9a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a,
	0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x45, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18,
	0xe9, 0xa6, 0x96, 0xe9, 0xa1, 0xb5, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe6, 0x95, 0xb0, 0xe6,
	0x8d, 0xae, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x52, 0x0d, 0x66, 0x69, 0x72, 0x73, 0x74, 0x53,
	0x68, 0x6f, 0x77, 0x50, 0x61, 0x67, 0x65, 0x12, 0x6f, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x2e,
	0x92, 0x41, 0x2b, 0x2a, 0x29, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe4, 0xbd, 0x8d, 0xe7, 0xbd,
	0xae, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe9, 0xa6, 0x96, 0xe9, 0xa1, 0xb5, 0xef, 0xbc,
	0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0xe9, 0xa1, 0xb5, 0x52, 0x08,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0xba, 0xab, 0xe4,
	0xbb, 0xbd, 0xe8, 0xae, 0xa4, 0xe8, 0xaf, 0x81, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x12, 0x52, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x37, 0x92, 0x41, 0x34, 0x2a, 0x32, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9, 0xa2, 0x84, 0xe5, 0x8a, 0xa0, 0xe8, 0xbd, 0xbd, 0xe8, 0xaf,
	0xb7, 0xe6, 0xb1, 0x82, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x9d, 0x9e, 0xe9, 0xa2, 0x84, 0xe5,
	0x8a, 0xa0, 0xe8, 0xbd, 0xbd, 0x3b, 0x31, 0x3a, 0xe9, 0xa2, 0x84, 0xe5, 0x8a, 0xa0, 0xe8, 0xbd,
	0xbd, 0x52, 0x07, 0x70, 0x72, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x22, 0xed, 0x03, 0x0a, 0x0d, 0x41,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x26, 0x0a, 0x06, 0x73, 0x6c, 0x6f, 0x67, 0x61, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x89, 0xaf, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52,
	0x06, 0x73, 0x6c, 0x6f, 0x67, 0x61, 0x6e, 0x12, 0x21, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb7,
	0xb3, 0xe8, 0xbd, 0xac, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12,
	0x23, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92,
	0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a,
	0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65,
	0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3f,
	0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0x61, 0x70, 0x70, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52,
	0x0e, 0x61, 0x70, 0x70, 0x4a, 0x75, 0x6d, 0x70, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x24, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0xae, 0xbd, 0x52, 0x05,
	0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x9b, 0xbe, 0xe7,
	0x89, 0x87, 0xe9, 0xab, 0x98, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x2b, 0x0a,
	0x09, 0x68, 0x69, 0x74, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0x68, 0x69, 0x74, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x68, 0x69, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa2, 0x06, 0x0a, 0x09, 0x41,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x12, 0x8d, 0x02, 0x0a, 0x09, 0x6a, 0x75, 0x6d,
	0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x42, 0xca, 0x01, 0x92, 0x41, 0xc6, 0x01, 0x2a, 0xc3, 0x01, 0x30, 0x3a, 0xe9, 0x9d, 0x9e,
	0xe6, 0xb3, 0x95, 0xe5, 0x80, 0xbc, 0x3b, 0x31, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5,
	0x95, 0x86, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x3b, 0x32, 0x3a, 0xe5, 0xa4, 0x96, 0xe9, 0x83,
	0xa8, 0xe9, 0x93, 0xbe, 0xe6, 0x8e, 0xa5, 0x3b, 0x33, 0x3a, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb,
	0xe6, 0x96, 0x87, 0xe7, 0xab, 0xa0, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x3b, 0x34, 0x3a, 0xe5,
	0xae, 0x9e, 0xe5, 0x8b, 0x98, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x3b, 0x36, 0x3a, 0x56, 0x50,
	0x53, 0x3b, 0x31, 0x30, 0x3a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe8, 0xaf, 0xa6, 0xe6, 0x83,
	0x85, 0x3b, 0x31, 0x31, 0x3a, 0xe7, 0x9b, 0xb4, 0xe6, 0x92, 0xad, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x3b, 0x31, 0x33, 0x3a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b, 0x31, 0x34, 0x3a, 0xe4,
	0xb8, 0xaa, 0xe4, 0xba, 0xba, 0xe4, 0xb8, 0xbb, 0xe9, 0xa1, 0xb5, 0x3b, 0x31, 0x35, 0x3a, 0xe6,
	0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe9, 0xa1,
	0xb5, 0x3b, 0x31, 0x36, 0x3a, 0xe6, 0x8e, 0x92, 0xe8, 0xa1, 0x8c, 0xe6, 0xa6, 0x9c, 0x3b, 0x31,
	0x37, 0x3a, 0x48, 0x35, 0x3b, 0x35, 0x35, 0x3a, 0xe8, 0xaf, 0x9d, 0xe9, 0xa2, 0x98, 0x52, 0x08,
	0x6a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x73, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64,
	0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x42, 0x34,
	0x92, 0x41, 0x31, 0x2a, 0x2f, 0x31, 0x3a, 0xe4, 0xb8, 0x8d, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba,
	0x3b, 0x32, 0x3a, 0xe7, 0xbd, 0xae, 0xe9, 0xa1, 0xb6, 0x3b, 0x33, 0x3a, 0xe6, 0x8e, 0xa8, 0xe8,
	0x8d, 0x90, 0x3b, 0x34, 0x3a, 0xe7, 0x83, 0xad, 0xe9, 0x97, 0xa8, 0x3b, 0x35, 0x3a, 0xe6, 0xb5,
	0x8f, 0xe8, 0xa7, 0x88, 0x52, 0x07, 0x74, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x03, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x21, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x6c, 0x6f, 0x67, 0x61, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x73, 0x6c, 0x6f, 0x67, 0x61, 0x6e, 0x52, 0x06, 0x73, 0x6c,
	0x6f, 0x67, 0x61, 0x6e, 0x12, 0x4b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0xb9, 0xbf, 0xe5, 0x91,
	0x8a, 0xe9, 0xa1, 0xb9, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x27, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0xe4, 0xbd, 0x8d,
	0xe7, 0xbd, 0xae, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x5a, 0x0a, 0x09, 0x70, 0x6f,
	0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x18, 0x92, 0x41, 0x15, 0x2a, 0x13, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x2d,
	0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x49,
	0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x6a, 0x75, 0x6d,
	0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x2a, 0x0f, 0x61, 0x70, 0x70, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x7c, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x3f, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f,
	0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0xad, 0x97, 0xe4, 0xbd, 0x93, 0xe8, 0x83, 0x8c, 0xe6,
	0x99, 0xaf, 0xe8, 0x89, 0xb2, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xad, 0x97, 0xe4, 0xbd,
	0x93, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0x96, 0x09,
	0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0xf7, 0x01, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0xe2,
	0x01, 0x92, 0x41, 0xde, 0x01, 0x2a, 0xdb, 0x01, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe6, 0x89, 0x80, 0xe6, 0x9c,
	0x89, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x3a,
	0xe8, 0xb5, 0x84, 0xe8, 0xae, 0xaf, 0x28, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0xe7, 0x9c, 0x8b, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x29, 0x3b, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0x3b, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x3a, 0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0x3b, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x3b, 0x73, 0x75,
	0x72, 0x76, 0x65, 0x79, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x3b, 0x6d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x3b, 0x6e, 0x65, 0x77,
	0x73, 0x5f, 0x66, 0x6c, 0x61, 0x73, 0x68, 0x3a, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x3b, 0x72,
	0x65, 0x67, 0x64, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x3a, 0xe6, 0x8a, 0xab,
	0xe9, 0x9c, 0xb2, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x3a, 0xe8, 0xaf, 0x84, 0xe4,
	0xbb, 0xb7, 0x3b, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x3a, 0xe5, 0xb9, 0xbf,
	0xe5, 0x91, 0x8a, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xaf, 0xb9, 0xe8,
	0xb1, 0xa1, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92,
	0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64,
	0x65, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x31, 0x92, 0x41, 0x2e, 0x2a, 0x2c, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae,
	0xe6, 0x9d, 0xa5, 0xe6, 0xba, 0x90, 0xef, 0xbc, 0x8c, 0x64, 0x65, 0x62, 0x75, 0x67, 0xe4, 0xbd,
	0xbf, 0xe7, 0x94, 0xa8, 0xe4, 0xb8, 0x9a, 0xe5, 0x8a, 0xa1, 0xe4, 0xb8, 0x8d, 0xe7, 0x94, 0xa8,
	0xe5, 0x85, 0xb3, 0xe5, 0xbf, 0x83, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0xa3, 0x02, 0x0a,
	0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x87, 0x02, 0x92, 0x41, 0x83, 0x02, 0x2a, 0x80, 0x02, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x61,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x3a, 0x28, 0x7a, 0x78, 0x5f, 0x6a, 0x79, 0x73, 0x3a, 0xe4,
	0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x7a,
	0x78, 0x5f, 0x6a, 0x79, 0x73, 0x68, 0x75, 0x69, 0x70, 0x69, 0x6e, 0x67, 0x3a, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0xb1, 0x87, 0xe8, 0xaf, 0x84, 0x3b, 0x7a, 0x78, 0x5f,
	0x6a, 0x79, 0x73, 0x68, 0x75, 0x6f, 0x64, 0x6f, 0x6e, 0x67, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98,
	0x93, 0xe5, 0x95, 0x86, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0x3b, 0x7a, 0x78, 0x5f, 0x62, 0x61,
	0x6f, 0x67, 0x75, 0x61, 0x6e, 0x67, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0xe6, 0x96, 0xb0,
	0xe9, 0x97, 0xbb, 0x3b, 0x7a, 0x78, 0x5f, 0x79, 0x61, 0x6f, 0x77, 0x65, 0x6e, 0x3a, 0xe8, 0xa1,
	0x8c, 0xe4, 0xb8, 0x9a, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x74, 0x79, 0x5f, 0x79, 0x61,
	0x6f, 0x77, 0x65, 0x6e, 0x3a, 0xe5, 0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0xe6, 0x96, 0xb0, 0xe9, 0x97,
	0xbb, 0x3b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x65, 0x77, 0x73, 0x3a,
	0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x29,
	0x3b, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x28, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x72,
	0x63, 0x65, 0x3a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x3a, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x29, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x12, 0xc0, 0x01, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x7e, 0x92, 0x41, 0x7b,
	0x2a, 0x79, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc,
	0x9a, 0x31, 0x3a, 0xe6, 0x96, 0x87, 0xe7, 0xab, 0xa0, 0x3b, 0x32, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5,
	0x85, 0x89, 0x3b, 0x33, 0x3a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b, 0x34, 0x3a, 0xe4, 0xba,
	0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x3b, 0x35, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98,
	0x3b, 0x36, 0x3a, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x3b, 0x37, 0x3a, 0xe5, 0xbf, 0xab, 0xe8,
	0xae, 0xaf, 0x3b, 0x38, 0x3a, 0xe6, 0x8a, 0xab, 0xe9, 0x9c, 0xb2, 0x3b, 0x39, 0x3a, 0xe8, 0xaf,
	0x84, 0xe4, 0xbb, 0xb7, 0x3b, 0x31, 0x30, 0x3a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95,
	0x86, 0x3b, 0x31, 0x31, 0x3a, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x52, 0x08, 0x69, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb9, 0xbf, 0xe5,
	0x91, 0x8a, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x09, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x46,
	0x0a, 0x11, 0x6e, 0x6f, 0x74, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15,
	0xe4, 0xb8, 0x8d, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe5,
	0x8f, 0x8d, 0xe9, 0xa6, 0x88, 0x52, 0x0f, 0x6e, 0x6f, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x22, 0xa4, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x45, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x73, 0x68, 0x6f, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe9, 0xa6, 0x96, 0xe9, 0xa1, 0xb5, 0xe6, 0x98, 0xbe, 0xe7,
	0xa4, 0xba, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x52, 0x0d,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x61, 0x67, 0x65, 0x22, 0xf1, 0x04,
	0x0a, 0x1d, 0x46, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x42, 0x79,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x28, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0f, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18,
	0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0x18, 0x01, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1b, 0xe5, 0x88, 0x86, 0xe9,
	0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x2c, 0xe9,
	0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x32, 0x30, 0x3a, 0x02, 0x32, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x3a,
	0x01, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x34, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd,
	0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0x92, 0x41, 0x2b, 0x2a, 0x29, 0xe9, 0xa1,
	0xb9, 0xe7, 0x9b, 0xae, 0x3a, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0x3b, 0x77, 0x69, 0x6b, 0x69,
	0x62, 0x69, 0x74, 0x3b, 0x77, 0x69, 0x6b, 0x69, 0x74, 0x72, 0x61, 0x64, 0x65, 0x3b, 0x77, 0x69,
	0x6b, 0x69, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x66, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x35, 0x92, 0x41, 0x30, 0x2a, 0x2e, 0xe5, 0x81, 0x8f, 0xe5, 0xa5, 0xbd, 0xe8, 0xaf, 0xad, 0xe8,
	0xa8, 0x80, 0x2c, 0xe5, 0xa4, 0x9a, 0xe4, 0xb8, 0xaa, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe4,
	0xb9, 0x8b, 0xe9, 0x97, 0xb4, 0xe7, 0x94, 0xa8, 0xe9, 0x80, 0x97, 0xe5, 0x8f, 0xb7, 0xe5, 0x88,
	0x86, 0xe9, 0x9a, 0x94, 0x18, 0x01, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x49, 0x44, 0x52, 0x0a,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0f, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe9, 0xa6, 0x96, 0xe9, 0xa1, 0xb5,
	0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0x81, 0x8f, 0xe7,
	0xa7, 0xbb, 0x52, 0x0d, 0x66, 0x69, 0x72, 0x73, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x61, 0x67,
	0x65, 0x22, 0xb2, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x44, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4c, 0x0a, 0x09, 0x61, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x09, 0x61, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x1b, 0x46, 0x69, 0x6e, 0x64, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x42, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x45, 0x0a, 0x0f, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a,
	0x2a, 0x18, 0xe9, 0xa6, 0x96, 0xe9, 0xa1, 0xb5, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe6, 0x95,
	0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x52, 0x0d, 0x66, 0x69, 0x72, 0x73,
	0x74, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x61, 0x67, 0x65, 0x22, 0xf1, 0x02, 0x0a, 0x05, 0x51, 0x75,
	0x6f, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x93, 0x81,
	0xe7, 0xb1, 0xbb, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x72,
	0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe4, 0xb8, 0x8a, 0xe6, 0xb6, 0xa8, 0xe4, 0xb8, 0x8b, 0xe8, 0xb7, 0x8c, 0xe7, 0x8e, 0x87, 0x52,
	0x04, 0x72, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0x9c, 0x80, 0xe9, 0xab, 0x98, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x08, 0x6d, 0x61, 0x78,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x80, 0xe9, 0xab, 0x98, 0xe4,
	0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0xe7, 0x9a, 0x84, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x11,
	0x6d, 0x61, 0x78, 0x50, 0x72, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9c, 0x80, 0xe4, 0xbd,
	0x8e, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x08, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x12, 0x4a, 0x0a, 0x13, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a,
	0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9c, 0x80, 0xe4, 0xbd, 0x8e, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0,
	0xbc, 0xe7, 0x9a, 0x84, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x3d, 0x0a,
	0x13, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x83, 0x0d, 0x0a,
	0x12, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x12, 0x3c, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x96,
	0xb0, 0xe5, 0xa2, 0x9e, 0xe7, 0x9a, 0x84, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86,
	0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x09, 0x6e, 0x65, 0x77, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x4f, 0x0a, 0x14, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe7, 0x9a, 0x84, 0xe6,
	0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x12,
	0x6e, 0x65, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x96,
	0xb0, 0xe5, 0xa2, 0x9e, 0xe7, 0x9a, 0x84, 0xe7, 0x89, 0x8c, 0xe7, 0x85, 0xa7, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x12,
	0x39, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e,
	0xe7, 0x9a, 0x84, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52,
	0x09, 0x6e, 0x65, 0x77, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x3f, 0x0a, 0x0b, 0x6e, 0x65,
	0x77, 0x5f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x1e, 0x92, 0x41, 0x1b, 0x2a, 0x19, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x96, 0xb0, 0xe9,
	0x97, 0xbb, 0x2f, 0xe8, 0xb5, 0x84, 0xe8, 0xae, 0xaf, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52,
	0x0a, 0x6e, 0x65, 0x77, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x6e,
	0x65, 0x77, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x9b,
	0x9d, 0xe5, 0x85, 0x89, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x45,
	0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x3e, 0x0a, 0x0e, 0x6e, 0x65, 0x77, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe6, 0x8a, 0xab, 0xe9,
	0x9c, 0xb2, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x44, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x67, 0x0a, 0x19, 0x6e, 0x65, 0x77, 0x5f, 0x64,
	0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x5f, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x2a,
	0x27, 0xe6, 0x96, 0xb0, 0xe5, 0xa2, 0x9e, 0xe7, 0x9a, 0x84, 0xe6, 0x8a, 0xab, 0xe9, 0x9c, 0xb2,
	0xe6, 0xb6, 0x89, 0xe5, 0x8f, 0x8a, 0xe7, 0x9a, 0x84, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5,
	0x95, 0x86, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x16, 0x6e, 0x65, 0x77, 0x44, 0x69, 0x73,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe4, 0xba, 0xba, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x0e, 0x6d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe8,
	0xa7, 0xa3, 0xe5, 0x86, 0xb3, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0d, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0b, 0x71, 0x75,
	0x6f, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xa4, 0x96, 0xe6, 0xb1, 0x87, 0xe8, 0xa1, 0x8c, 0xe6,
	0x83, 0x85, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0a, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x75, 0x70, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41,
	0x1a, 0x2a, 0x18, 0xe4, 0xb8, 0x8a, 0xe6, 0xb6, 0xa8, 0xe5, 0xa4, 0x96, 0xe6, 0xb1, 0x87, 0xe8,
	0xa1, 0x8c, 0xe6, 0x83, 0x85, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0c, 0x75, 0x70, 0x51,
	0x75, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x10, 0x66, 0x61, 0x6c,
	0x6c, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe4, 0xb8, 0x8b, 0xe8, 0xb7, 0x8c,
	0xe5, 0xa4, 0x96, 0xe6, 0xb1, 0x87, 0xe8, 0xa1, 0x8c, 0xe6, 0x83, 0x85, 0xe6, 0x95, 0xb0, 0xe9,
	0x87, 0x8f, 0x52, 0x0e, 0x66, 0x61, 0x6c, 0x6c, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x48, 0x0a, 0x06, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe8, 0xa1, 0x8c, 0xe6, 0x83, 0x85, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x73, 0x12, 0x6b, 0x0a, 0x23,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18,
	0xe5, 0x85, 0xa8, 0xe5, 0x91, 0x98, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe8, 0xaf, 0x8d, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x87, 0x8f, 0x52, 0x1f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65, 0x78, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x6b, 0x65, 0x79,
	0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe8, 0xaf, 0x8d, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x52, 0x08, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x50, 0x0a, 0x15, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a,
	0x18, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0x9a, 0x84,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x95, 0xb0, 0x52, 0x12, 0x76, 0x69, 0x65, 0x77, 0x50,
	0x6f, 0x73, 0x74, 0x73, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f, 0x0a,
	0x11, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6,
	0x9c, 0x89, 0xe8, 0xbf, 0x87, 0xe4, 0xba, 0xa4, 0xe4, 0xba, 0x92, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8,
	0xba, 0xe7, 0x9a, 0x84, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5c,
	0x0a, 0x0f, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x5f, 0x74, 0x6f, 0x70,
	0x32, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x65,
	0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x93, 0x81,
	0xe7, 0xa7, 0x8d, 0xe5, 0xb9, 0xb4, 0xe6, 0xb6, 0xa8, 0x74, 0x6f, 0x70, 0x32, 0x52, 0x0d, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x54, 0x6f, 0x70, 0x32, 0x12, 0x5e, 0x0a, 0x10,
	0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x5f, 0x74, 0x6f, 0x70, 0x31,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x42,
	0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x93, 0x81, 0xe7,
	0xa7, 0x8d, 0xe5, 0xb9, 0xb4, 0xe8, 0xb7, 0x8c, 0x74, 0x6f, 0x70, 0x31, 0x52, 0x0e, 0x79, 0x65,
	0x61, 0x72, 0x6c, 0x79, 0x44, 0x65, 0x73, 0x63, 0x54, 0x6f, 0x70, 0x31, 0x12, 0x6b, 0x0a, 0x17,
	0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x69,
	0x6e, 0x63, 0x5f, 0x74, 0x6f, 0x70, 0x31, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x93, 0x81, 0xe7, 0xa7, 0x8d, 0xe8, 0xbf, 0x9e, 0xe6, 0xb6, 0xa8, 0x74,
	0x6f, 0x70, 0x31, 0x52, 0x14, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x44, 0x75, 0x72, 0x61, 0x62,
	0x6c, 0x65, 0x49, 0x6e, 0x63, 0x54, 0x6f, 0x70, 0x31, 0x12, 0x47, 0x0a, 0x13, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xa4, 0xbe,
	0xe5, 0x8c, 0xba, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x80, 0xbb, 0xe9, 0x87, 0x8f, 0x52,
	0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x65,
	0x78, 0x74, 0x22, 0x69, 0x0a, 0x16, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0x92, 0x41, 0x06, 0x2a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x76,
	0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe6, 0xac, 0xa1, 0xe6,
	0x95, 0xb0, 0x52, 0x09, 0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xff, 0x12,
	0x0a, 0x10, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x4d, 0x0a, 0x12, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1e,
	0x92, 0x41, 0x1b, 0x2a, 0x19, 0xe6, 0xb3, 0xa8, 0xe5, 0x86, 0x8c, 0xe6, 0x97, 0xa5, 0xe6, 0x9c,
	0x9f, 0xef, 0xbc, 0x9b, 0x32, 0x30, 0x32, 0x34, 0x2d, 0x31, 0x31, 0x2d, 0x32, 0x34, 0x52, 0x11,
	0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x36, 0x0a, 0x0d, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0xb3, 0xa8, 0xe5, 0x86, 0x8c, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x61, 0x79, 0x73, 0x12, 0x6e, 0x0a, 0x10, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x1a, 0x92,
	0x41, 0x17, 0x2a, 0x15, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93,
	0xe5, 0x95, 0x86, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0e, 0x76, 0x69, 0x65, 0x77, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x76, 0x69,
	0x65, 0x77, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6,
	0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x17, 0x76, 0x69, 0x65, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a,
	0x10, 0x75, 0x73, 0x65, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe4, 0xbd,
	0xbf, 0xe7, 0x94, 0xa8, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe5, 0x8a, 0x9f, 0xe8, 0x83, 0xbd,
	0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x18, 0x6d, 0x6f, 0x73, 0x74, 0x5f, 0x75,
	0x73, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5,
	0xb8, 0xb8, 0xe7, 0x94, 0xa8, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe8, 0xaf, 0x8d, 0x52, 0x15,
	0x6d, 0x6f, 0x73, 0x74, 0x55, 0x73, 0x65, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4b, 0x65,
	0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x45, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x65, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x90, 0x9c, 0xe7, 0xb4, 0xa2, 0xe8,
	0xaf, 0x8d, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x4b, 0x0a, 0x12,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe6,
	0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe8, 0xbf, 0x87, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe7, 0xab,
	0xa0, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x10, 0x76, 0x69, 0x65, 0x77, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x15, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1b, 0x92, 0x41, 0x18, 0x2a, 0x16, 0xe6,
	0x84, 0x9f, 0xe5, 0x85, 0xb4, 0xe8, 0xb6, 0xa3, 0xe7, 0x9a, 0x84, 0xe5, 0x8c, 0xba, 0xe5, 0x9f,
	0x9f, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x96, 0xe6, 0x95, 0xb0, 0xe9,
	0x87, 0x8f, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x41,
	0x0a, 0x10, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6,
	0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0x95, 0xb0, 0xe9, 0x87,
	0x8f, 0x52, 0x0e, 0x76, 0x69, 0x65, 0x77, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x4a, 0x0a, 0x13, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a,
	0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe7, 0x9a, 0x84, 0xe6, 0x9b,
	0x9d, 0xe5, 0x85, 0x89, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x11, 0x76, 0x69, 0x65, 0x77,
	0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a,
	0x0d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8f, 0x91, 0xe8, 0xb5,
	0xb7, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0c, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0e, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3,
	0xe6, 0x80, 0xbb, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0d, 0x6d, 0x65, 0x64, 0x69, 0x61,
	0x74, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x61, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x42, 0x1d, 0x92, 0x41, 0x1a,
	0x2a, 0x18, 0xe6, 0x84, 0x9f, 0xe5, 0x85, 0xb4, 0xe8, 0xb6, 0xa3, 0xe7, 0x9a, 0x84, 0xe5, 0xa4,
	0x96, 0xe6, 0xb1, 0x87, 0xe8, 0xa1, 0x8c, 0xe6, 0x83, 0x85, 0x52, 0x0f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x12, 0x68, 0x0a, 0x1b, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x71, 0x75, 0x6f, 0x74, 0x65, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x29, 0x92, 0x41, 0x26, 0x2a, 0x24, 0xe6, 0x84, 0x9f, 0xe5, 0x85, 0xb4, 0xe8, 0xb6, 0xa3,
	0xe7, 0x9a, 0x84, 0xe5, 0xa4, 0x96, 0xe6, 0xb1, 0x87, 0xe8, 0xa1, 0x8c, 0xe6, 0x83, 0x85, 0xe6,
	0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x18, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x65, 0x64, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x11, 0x6d, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x72,
	0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xa8, 0xa1, 0xe6, 0x8b, 0x9f, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x6d, 0x6f, 0x63, 0x6b, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x11, 0x6d, 0x6f,
	0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x02, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xa8, 0xa1, 0xe6,
	0x8b, 0x9f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0f,
	0x6d, 0x6f, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x64, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x4b, 0x0a, 0x12, 0x6d, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x72, 0x65,
	0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x02, 0x42, 0x1d, 0x92, 0x41, 0x1a,
	0x2a, 0x18, 0xe6, 0xa8, 0xa1, 0xe6, 0x8b, 0x9f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x85,
	0xa8, 0xe5, 0xb9, 0xb4, 0xe8, 0x90, 0xa5, 0xe6, 0x94, 0xb6, 0x52, 0x10, 0x6d, 0x6f, 0x63, 0x6b,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x6c, 0x0a, 0x15,
	0x6d, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x73, 0x74, 0x5f,
	0x71, 0x75, 0x6f, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x6f, 0x74, 0x65, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe6, 0xa8, 0xa1, 0xe6, 0x8b,
	0x9f, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe6, 0x9c, 0x80, 0xe5, 0xa4, 0x9a, 0xe7, 0x9a, 0x84,
	0xe5, 0x93, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x12, 0x6d, 0x6f, 0x63, 0x6b, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x4d, 0x6f, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x16, 0x92, 0x41, 0x13, 0x2a, 0x11, 0xe7, 0xac, 0xac, 0xe4, 0xb8, 0x80, 0xe7, 0xaf,
	0x87, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0x49, 0x44, 0x52, 0x0b, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x50, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x12, 0x52, 0x0a, 0x14, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f,
	0x70, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe7, 0xac, 0xac, 0xe4, 0xb8,
	0x80, 0xe7, 0xaf, 0x87, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x12, 0x66, 0x69, 0x72, 0x73, 0x74, 0x50, 0x6f, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x6c, 0x0a, 0x17, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x42, 0x35, 0x92, 0x41, 0x32,
	0x2a, 0x30, 0xe7, 0xac, 0xac, 0xe4, 0xb8, 0x80, 0xe7, 0xaf, 0x87, 0xe5, 0xb8, 0x96, 0xe5, 0xad,
	0x90, 0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab, 0xef, 0xbc, 0x8c, 0xe7, 0x82, 0xb9, 0xe8, 0xb5, 0x9e,
	0xef, 0xbc, 0x8c, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0xe7, 0x9a, 0x84, 0xe6, 0x80, 0xbb, 0xe6,
	0x95, 0xb0, 0x52, 0x14, 0x66, 0x69, 0x72, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x19, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x26, 0x92, 0x41, 0x23, 0x2a, 0x21, 0xe7, 0xac, 0xac,
	0xe4, 0xb8, 0x80, 0xe7, 0xaf, 0x87, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0x9f, 0xa5, 0xe7,
	0x9c, 0x8b, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x15,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x9c, 0x80, 0xe7, 0xbb, 0x88, 0xe7, 0xa7, 0xb0, 0xe5,
	0x8f, 0xb7, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x46, 0x0a, 0x11, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe4,
	0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0f,
	0x76, 0x69, 0x65, 0x77, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x46, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe7, 0x82, 0xb9, 0xe5, 0x87, 0xbb, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86,
	0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x59, 0x0a, 0x1b, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41,
	0x17, 0x2a, 0x15, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x95, 0x86, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x18, 0x76, 0x69, 0x65, 0x77, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x5b, 0x0a, 0x1c, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe7,
	0x82, 0xb9, 0xe5, 0x87, 0xbb, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x95,
	0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x19, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xaf, 0x01, 0x0a, 0x11, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x06, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x85, 0xa8, 0xe5, 0xb1, 0x80, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x06,
	0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x12, 0x49, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x22, 0xb8, 0x03, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64,
	0x4e, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x6e, 0x0a, 0x0c, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x2c, 0x92, 0x41, 0x29, 0x2a, 0x20, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x31,
	0xef, 0xbc, 0x9a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a,
	0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0xd2, 0x01, 0x04, 0x74, 0x72, 0x75, 0x65, 0x52, 0x0b, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x54, 0x0a, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x33, 0x92, 0x41, 0x30, 0x2a, 0x27, 0xe7, 0x83, 0xad, 0xe9, 0x97, 0xa8, 0xe5, 0x92, 0x8c, 0xe6,
	0x9c, 0x80, 0xe6, 0x96, 0xb0, 0xef, 0xbc, 0x9a, 0x30, 0x30, 0x32, 0x3a, 0xe7, 0x83, 0xad, 0xe9,
	0x97, 0xa8, 0x3b, 0x30, 0x30, 0x33, 0x3a, 0xe6, 0x9c, 0x80, 0xe6, 0x96, 0xb0, 0xd2, 0x01, 0x04,
	0x74, 0x72, 0x75, 0x65, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0xa1, 0xb5, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6,
	0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x8b,
	0x01, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x62, 0x92, 0x41, 0x5f, 0x2a, 0x5d, 0xe5,
	0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0xef, 0xbc,
	0x9a, 0xe5, 0x9c, 0xa8, 0xe5, 0x88, 0xb7, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe5, 0x9b, 0xba,
	0xe5, 0xae, 0x9a, 0xe5, 0xa5, 0xbd, 0xe4, 0xb8, 0x80, 0xe4, 0xb8, 0xaa, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0xe6, 0x88, 0xb3, 0xef, 0xbc, 0x8c, 0xe5, 0x9c, 0xa8, 0xe5, 0x8a, 0xa0, 0xe8, 0xbd,
	0xbd, 0xe6, 0x9b, 0xb4, 0xe5, 0xa4, 0x9a, 0xe6, 0x97, 0xb6, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8,
	0xe8, 0xbf, 0x99, 0xe4, 0xb8, 0xaa, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0e, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x1f, 0x0a, 0x0d,
	0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x61, 0x0a,
	0x12, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xe8, 0x04, 0x0a, 0x0f, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x56, 0x32,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x76, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x3d, 0x92, 0x41, 0x3a, 0x2a, 0x38, 0xe6, 0x9c,
	0x80, 0xe6, 0x96, 0xb0, 0xe5, 0x92, 0x8c, 0xe7, 0x83, 0xad, 0xe9, 0x97, 0xa8, 0xe7, 0x9a, 0x84,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x8c, 0x76, 0x32, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f,
	0xa3, 0xe6, 0x89, 0x8d, 0xe4, 0xbc, 0x9a, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe8, 0xaf, 0xa5,
	0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0xa3, 0x02, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x87, 0x02, 0x92, 0x41, 0x83, 0x02,
	0x2a, 0x80, 0x02, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe5,
	0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x3a, 0x28, 0x7a, 0x78, 0x5f, 0x6a, 0x79, 0x73, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5,
	0x95, 0x86, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x7a, 0x78, 0x5f, 0x6a, 0x79, 0x73, 0x68,
	0x75, 0x69, 0x70, 0x69, 0x6e, 0x67, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86,
	0xe6, 0xb1, 0x87, 0xe8, 0xaf, 0x84, 0x3b, 0x7a, 0x78, 0x5f, 0x6a, 0x79, 0x73, 0x68, 0x75, 0x6f,
	0x64, 0x6f, 0x6e, 0x67, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0xb4,
	0xbb, 0xe5, 0x8a, 0xa8, 0x3b, 0x7a, 0x78, 0x5f, 0x62, 0x61, 0x6f, 0x67, 0x75, 0x61, 0x6e, 0x67,
	0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x7a, 0x78,
	0x5f, 0x79, 0x61, 0x6f, 0x77, 0x65, 0x6e, 0x3a, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0x9a, 0xe6, 0x96,
	0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x74, 0x79, 0x5f, 0x79, 0x61, 0x6f, 0x77, 0x65, 0x6e, 0x3a, 0xe5,
	0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x65, 0x77, 0x73, 0x3a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1,
	0xe5, 0x95, 0x86, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x29, 0x3b, 0x64, 0x69, 0x73, 0x63, 0x6f,
	0x76, 0x65, 0x72, 0x28, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x3a, 0xe5, 0x95, 0x86,
	0xe4, 0xb8, 0x9a, 0x3b, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x3a, 0xe5, 0x8a, 0xa8, 0xe6,
	0x80, 0x81, 0x29, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a,
	0x09, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae,
	0x52, 0x09, 0x61, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x22, 0x65, 0x0a, 0x14, 0x46,
	0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x56, 0x32, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x74, 0x41, 0x6e, 0x64, 0x4e, 0x65, 0x77, 0x56,
	0x32, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x22, 0xa0, 0x02, 0x0a, 0x0a, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0xe6, 0x01, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0xab, 0x01, 0x92, 0x41, 0xa7, 0x01, 0x2a, 0xa4, 0x01,
	0xe8, 0xb4, 0xa6, 0xe5, 0x8f, 0xb7, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x31,
	0xef, 0xbc, 0x9a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0x8f, 0xb7, 0xef,
	0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0xe8, 0xb0, 0x83, 0xe8,
	0xa7, 0xa3, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0xe6,
	0x96, 0xb0, 0xe9, 0x97, 0xbb, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0x77, 0x69, 0x6b, 0x69,
	0x66, 0x78, 0x2d, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x3b, 0x35, 0xef, 0xbc, 0x9a, 0x77, 0x69,
	0x6b, 0x69, 0x66, 0x78, 0x2d, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0xef, 0xbc, 0x9b, 0x36, 0xef,
	0xbc, 0x9a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe5, 0x8f, 0xb7, 0xef, 0xbc,
	0x9b, 0x37, 0xef, 0xbc, 0x9a, 0xe7, 0x9b, 0x91, 0xe7, 0xae, 0xa1, 0xe6, 0x9c, 0xba, 0xe6, 0x9e,
	0x84, 0xe5, 0x8f, 0xb7, 0xef, 0xbc, 0x9b, 0x38, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe5, 0x8f, 0xb7, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x03, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xaf,
	0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0xe6, 0x88, 0x96, 0xe8, 0x80, 0x85, 0x63, 0x6f, 0x64, 0x65,
	0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x7a, 0x0a, 0x18, 0x46, 0x69, 0x6e, 0x64, 0x46, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x36, 0x0a, 0x07, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x07, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x22, 0xbf, 0x05, 0x0a, 0x11, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0xc8, 0x01, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x8e, 0x01, 0x92, 0x41,
	0x8a, 0x01, 0x2a, 0x87, 0x01, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0xef, 0xbc, 0x9a, 0x31, 0x3a, 0xe6, 0x96, 0x87, 0xe7, 0xab, 0xa0, 0x28, 0xe6, 0x9c, 0x89,
	0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x29, 0x3b, 0x32, 0x3a, 0xe6, 0x9b, 0x9d,
	0xe5, 0x85, 0x89, 0x3b, 0x33, 0x3a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b, 0x34, 0x3a, 0xe4,
	0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x3b, 0x35, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b,
	0x98, 0x3b, 0x36, 0x3a, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x3b, 0x37, 0x3a, 0xe5, 0xbf, 0xab,
	0xe8, 0xae, 0xaf, 0x3b, 0x38, 0x3a, 0xe6, 0x8a, 0xab, 0xe9, 0x9c, 0xb2, 0x3b, 0x39, 0x3a, 0xe8,
	0xaf, 0x84, 0xe4, 0xbb, 0xb7, 0x3b, 0x31, 0x30, 0x3a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5,
	0x95, 0x86, 0x3b, 0x31, 0x31, 0x3a, 0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0xa3,
	0x02, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x87, 0x02, 0x92, 0x41, 0x83, 0x02, 0x2a, 0x80, 0x02, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d,
	0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0x3a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x3a, 0x28, 0x7a, 0x78, 0x5f, 0x6a, 0x79, 0x73,
	0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb,
	0x3b, 0x7a, 0x78, 0x5f, 0x6a, 0x79, 0x73, 0x68, 0x75, 0x69, 0x70, 0x69, 0x6e, 0x67, 0x3a, 0xe4,
	0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0xb1, 0x87, 0xe8, 0xaf, 0x84, 0x3b, 0x7a,
	0x78, 0x5f, 0x6a, 0x79, 0x73, 0x68, 0x75, 0x6f, 0x64, 0x6f, 0x6e, 0x67, 0x3a, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a, 0xa8, 0x3b, 0x7a, 0x78, 0x5f,
	0x62, 0x61, 0x6f, 0x67, 0x75, 0x61, 0x6e, 0x67, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0xe6,
	0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x7a, 0x78, 0x5f, 0x79, 0x61, 0x6f, 0x77, 0x65, 0x6e, 0x3a,
	0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0x9a, 0xe6, 0x96, 0xb0, 0xe9, 0x97, 0xbb, 0x3b, 0x74, 0x79, 0x5f,
	0x79, 0x61, 0x6f, 0x77, 0x65, 0x6e, 0x3a, 0xe5, 0xa4, 0xa9, 0xe7, 0x9c, 0xbc, 0xe6, 0x96, 0xb0,
	0xe9, 0x97, 0xbb, 0x3b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x65, 0x77,
	0x73, 0x3a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xe6, 0x96, 0xb0, 0xe9, 0x97,
	0xbb, 0x29, 0x3b, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x28, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x72, 0x63, 0x65, 0x3a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b, 0x64, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x3a, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x29, 0x52, 0x07, 0x73, 0x75, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x18, 0x92, 0x41, 0x15,
	0x2a, 0x13, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0xef, 0xbc, 0x8c, 0xe5,
	0xb0, 0x8f, 0xe5, 0x86, 0x99, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x22, 0x66, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4c, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x74, 0x65,
	0x6d, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe5, 0x88,
	0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xce, 0x02, 0x0a, 0x11,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x40, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x4d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x2e, 0x92, 0x41, 0x29, 0x2a, 0x27, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe6, 0x98, 0xaf, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xef, 0xbc, 0x88, 0xe8,
	0xaf, 0xa5, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0xe5, 0xba, 0x9f, 0xe5, 0xbc, 0x83, 0xef, 0xbc,
	0x89, 0x18, 0x01, 0x52, 0x09, 0x69, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6c,
	0x0a, 0x14, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0xae, 0x98, 0xe6, 0x96, 0xb9, 0xe5,
	0x8f, 0xb7, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x4c, 0x0a, 0x0f,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x39, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x16, 0x54,
	0x72, 0x61, 0x64, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x2e, 0x92,
	0x41, 0x29, 0x2a, 0x27, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x98, 0xaf, 0xe6, 0x9c, 0x8d,
	0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0xef, 0xbc, 0x88, 0xe8, 0xaf, 0xa5, 0xe5, 0xad, 0x97, 0xe6,
	0xae, 0xb5, 0xe5, 0xba, 0x9f, 0xe5, 0xbc, 0x83, 0xef, 0xbc, 0x89, 0x18, 0x01, 0x52, 0x09, 0x69,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6c, 0x0a, 0x14, 0x6f, 0x66, 0x66, 0x69,
	0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69,
	0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x2a, 0x0f, 0xe5, 0xae, 0x98, 0xe6, 0x96, 0xb9, 0xe5, 0x8f, 0xb7, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x12, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x62, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x50, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63,
	0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x79,
	0x6e, 0x61, 0x6d, 0x69, 0x63, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x51, 0x0a, 0x0d, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xc0, 0x01,
	0x0a, 0x0a, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x37, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x22, 0x41, 0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x32, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x46, 0x0a, 0x16, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x45, 0x0a, 0x0f, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x22, 0x4f, 0x0a, 0x14, 0x46, 0x69, 0x6e, 0x64, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x4e, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x22, 0x17, 0x0a, 0x15, 0x46, 0x69, 0x6e, 0x64, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4c, 0x0a, 0x13, 0x46, 0x69, 0x6e,
	0x64, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x35, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x2a, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0x2a, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x75, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22,
	0x67, 0x0a, 0x0f, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0xb4, 0xbb, 0xe5, 0x8a,
	0xa8, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x52, 0x08, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x12, 0x25, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8e, 0x92, 0xe8, 0xa1, 0x8c, 0xe5, 0x88, 0x86, 0xe7,
	0xb1, 0xbb, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x22, 0xe6, 0x01, 0x0a, 0x0c, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x8e, 0x92, 0xe5,
	0x90, 0x8d, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe4, 0xba,
	0xba, 0xe6, 0xb0, 0x94, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2f,
	0x0a, 0x08, 0x68, 0x6f, 0x6d, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xb8, 0xbb,
	0xe9, 0xa1, 0xb5, 0x55, 0x52, 0x4c, 0x52, 0x07, 0x68, 0x6f, 0x6d, 0x65, 0x55, 0x72, 0x6c, 0x12,
	0x30, 0x0a, 0x0a, 0x77, 0x65, 0x65, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x91, 0xa8, 0xe4, 0xba, 0xba,
	0xe6, 0xb0, 0x94, 0xe5, 0x80, 0xbc, 0x52, 0x09, 0x77, 0x65, 0x65, 0x6b, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x22, 0x45, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xab, 0x03, 0x0a, 0x18, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0x49, 0x44, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0xd0, 0x01,
	0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x8d, 0x01, 0x92, 0x41, 0x89, 0x01, 0x2a, 0x86, 0x01,
	0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x30, 0x3a, 0xe6,
	0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3b, 0x31, 0x3a, 0xe6, 0x96,
	0x87, 0xe7, 0xab, 0xa0, 0x3b, 0x32, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0x3a, 0x33, 0x3a,
	0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0x3b, 0x34, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5,
	0x95, 0x86, 0x3b, 0x35, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x3b, 0x36, 0x3a, 0xe8, 0xb0,
	0x83, 0xe8, 0xa7, 0xa3, 0x3b, 0x37, 0x3a, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x3b, 0x38, 0x3a,
	0xe6, 0x8a, 0xab, 0xe9, 0x9c, 0xb2, 0x3b, 0x39, 0x3a, 0xe8, 0xaf, 0x84, 0xe4, 0xbb, 0xb7, 0x3b,
	0x31, 0x30, 0x3a, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0x3a, 0x31, 0x31, 0x3a,
	0xe5, 0xb9, 0xbf, 0xe5, 0x91, 0x8a, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x35, 0x0a, 0x0c, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x0a, 0x69, 0x74, 0x65,
	0x6d, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xb8, 0x8d,
	0xe5, 0x96, 0x9c, 0xe6, 0xac, 0xa2, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x1b, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2e, 0x0a, 0x18, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0xa5, 0x02, 0x0a, 0x15, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x4a, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xb1, 0xbb, 0xe5,
	0x9e, 0x8b, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8,
	0x96, 0xe5, 0xad, 0x90, 0xe5, 0x88, 0x86, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x4a, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90,
	0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x2c, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x13,
	0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x19, 0x48,
	0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x6f, 0x74, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x52, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa2,
	0x01, 0x0a, 0x14, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x77, 0x65, 0x65, 0x6b, 0x12, 0x1f, 0x0a, 0x0b, 0x6d,
	0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x45, 0x6e, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x65,
	0x6b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77,
	0x65, 0x65, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x65, 0x65, 0x6b,
	0x5f, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x65, 0x6b,
	0x45, 0x6e, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52,
	0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x44, 0x0a,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x43, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xb6, 0x01, 0x0a,
	0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x52, 0x61, 0x6e, 0x6b,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x73, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1e, 0x0a, 0x1c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x74, 0x69,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb1, 0x05, 0x0a, 0x12, 0x55,
	0x73, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1b,
	0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5,
	0xb0, 0x8f, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x32, 0x30, 0x3a, 0x02, 0x32, 0x30, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5,
	0xe6, 0x95, 0xb0, 0x3a, 0x01, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x86, 0x01, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x72, 0x92, 0x41, 0x6f,
	0x2a, 0x6d, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0xe9,
	0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe6, 0x89, 0x80, 0xe6, 0x9c, 0x89, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x3a, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x3a, 0xe8, 0xb5, 0x84, 0xe8, 0xae, 0xaf,
	0x3b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89,
	0x3b, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x3a, 0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0,
	0x3b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95,
	0x86, 0x3b, 0x73, 0x75, 0x72, 0x76, 0x65, 0x79, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41,
	0x0c, 0x2a, 0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f,
	0x64, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x48, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2e, 0x92, 0x41, 0x2b, 0x2a, 0x29, 0xe9, 0xa1, 0xb9, 0xe7, 0x9b, 0xae, 0x3a, 0x77, 0x69,
	0x6b, 0x69, 0x66, 0x78, 0x3b, 0x77, 0x69, 0x6b, 0x69, 0x62, 0x69, 0x74, 0x3b, 0x77, 0x69, 0x6b,
	0x69, 0x74, 0x72, 0x61, 0x64, 0x65, 0x3b, 0x77, 0x69, 0x6b, 0x69, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x6a, 0x0a, 0x0c, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x28,
	0x92, 0x41, 0x25, 0x2a, 0x23, 0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0xe9, 0xa1, 0xb5, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x31, 0x3a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0x3b,
	0x32, 0x3a, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65,
	0x52, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x05,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0x92, 0x41, 0x07,
	0x2a, 0x05, 0x64, 0x65, 0x63, 0x61, 0x79, 0x52, 0x05, 0x64, 0x65, 0x63, 0x61, 0x79, 0x22, 0xbe,
	0x05, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49,
	0x44, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1b,
	0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5,
	0xb0, 0x8f, 0x2c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x32, 0x30, 0x3a, 0x02, 0x32, 0x30, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5,
	0xe6, 0x95, 0xb0, 0x3a, 0x01, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x0d,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0x63, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5,
	0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3d, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaa, 0xe5, 0x9f, 0xba, 0xe4, 0xba, 0x8e, 0xe5,
	0xae, 0x9e, 0xe6, 0x97, 0xb6, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0xba, 0x52, 0x08, 0x72, 0x65, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65,
	0x52, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x6f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x05,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0x92, 0x41, 0x07,
	0x2a, 0x05, 0x64, 0x65, 0x63, 0x61, 0x79, 0x52, 0x05, 0x64, 0x65, 0x63, 0x61, 0x79, 0x12, 0x64,
	0x0a, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0x92, 0x41, 0x30,
	0x2a, 0x2e, 0xe5, 0x81, 0x8f, 0xe5, 0xa5, 0xbd, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x2c, 0xe5,
	0xa4, 0x9a, 0xe4, 0xb8, 0xaa, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe4, 0xb9, 0x8b, 0xe9, 0x97,
	0xb4, 0xe7, 0x94, 0xa8, 0xe9, 0x80, 0x97, 0xe5, 0x8f, 0xb7, 0xe5, 0x88, 0x86, 0xe9, 0x9a, 0x94,
	0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x4c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x73, 0x12, 0x79, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x38, 0x92, 0x41, 0x35,
	0x2a, 0x33, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc,
	0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0x85, 0xa8, 0xe6, 0x8e, 0xa8, 0xef, 0xbc, 0x9b, 0x31, 0xef,
	0xbc, 0x9a, 0xe5, 0x95, 0x86, 0xe4, 0xb8, 0x9a, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5,
	0x8a, 0xa8, 0xe6, 0x80, 0x81, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22,
	0xa3, 0x05, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x86, 0x01, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x72, 0x92, 0x41, 0x6f, 0x2a, 0x6d, 0xe6, 0x8e, 0xa8, 0xe8, 0x8d,
	0x90, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe6, 0x89,
	0x80, 0xe6, 0x9c, 0x89, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x3a, 0xe8, 0xb5, 0x84, 0xe8, 0xae, 0xaf, 0x3b, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0x3b, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x3a, 0xe5, 0x8f, 0x91, 0xe7, 0x8e, 0xb0, 0x3b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x3b, 0x73, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1d,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x34, 0x0a,
	0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8,
	0x80, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x02, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x88, 0x86, 0xe6, 0x95, 0xb0, 0x52,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x89,
	0x80, 0xe5, 0xb1, 0x9e, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88,
	0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe6, 0x89, 0x80, 0xe5, 0xb1, 0x9e, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95,
	0x86, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a,
	0x0b, 0x72, 0x61, 0x6e, 0x6b, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0xe5,
	0x8f, 0x82, 0xe6, 0x95, 0xb0, 0x52, 0x0a, 0x72, 0x61, 0x6e, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x64, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x50, 0x92, 0x41, 0x4d, 0x2a, 0x4b, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe6, 0x9d, 0xa5, 0xe6,
	0xba, 0x90, 0xef, 0xbc, 0x9a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x3a, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0xe7, 0x9b, 0xb8, 0xe4, 0xbc, 0xbc, 0xe6, 0x80, 0xa7, 0x3b, 0x68, 0x6f, 0x74,
	0x3a, 0xe7, 0x83, 0xad, 0xe5, 0xba, 0xa6, 0x3b, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x68, 0x69, 0x70, 0x3a, 0xe5, 0xa5, 0xbd, 0xe5, 0x8f, 0x8b, 0xe5, 0x85, 0xb3, 0xe7, 0xb3,
	0xbb, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x37, 0x0a, 0x0e, 0x72, 0x61, 0x6e, 0x6b, 0x5f,
	0x73, 0x68, 0x70, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x02, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0xe5, 0x87, 0xba, 0xe5,
	0x8f, 0x82, 0x52, 0x0c, 0x72, 0x61, 0x6e, 0x6b, 0x53, 0x68, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x79,
	0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x79, 0x73, 0x22, 0x4f, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x97, 0x04, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c, 0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x2a, 0x0a, 0x07, 0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x06, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32,
	0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x34, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a,
	0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x0c, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x24, 0x92, 0x41, 0x21, 0x2a, 0x1b, 0xe5, 0x88, 0x86,
	0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x2c,
	0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x32, 0x30, 0x3a, 0x02, 0x32, 0x30, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0,
	0x3a, 0x01, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x07, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2e, 0x92, 0x41, 0x2b, 0x2a,
	0x29, 0xe9, 0xa1, 0xb9, 0xe7, 0x9b, 0xae, 0x3a, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0x3b, 0x77,
	0x69, 0x6b, 0x69, 0x62, 0x69, 0x74, 0x3b, 0x77, 0x69, 0x6b, 0x69, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x3b, 0x77, 0x69, 0x6b, 0x69, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x05,
	0x73, 0x63, 0x61, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0x6f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x05, 0x64, 0x65,
	0x63, 0x61, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05,
	0x64, 0x65, 0x63, 0x61, 0x79, 0x52, 0x05, 0x64, 0x65, 0x63, 0x61, 0x79, 0x12, 0x32, 0x0a, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0f, 0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63,
	0x6f, 0x64, 0x65, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x53, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x6d, 0x69, 0x6c,
	0x61, 0x72, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x4b, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x22, 0xf5, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76,
	0x69, 0x6f, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x28, 0x0a, 0x08, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0x49, 0x44, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x09,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0x52, 0x08,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x0c, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x66, 0x0a, 0x11, 0x55, 0x73,
	0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x51, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8,
	0xa1, 0x8c, 0xe4, 0xb8, 0xba, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x79, 0x0a, 0x19, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xda, 0x01,
	0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x65, 0x68, 0x61,
	0x76, 0x69, 0x6f, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x30, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x09,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0x52, 0x08,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x0c, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x59, 0x0a, 0x17, 0x55, 0x73,
	0x65, 0x72, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x49, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x22, 0x63, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x09, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x49, 0x44, 0x52, 0x08, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3c, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92,
	0x41, 0x1a, 0x2a, 0x18, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe4, 0xb8, 0x80, 0xe6, 0xac, 0xa1,
	0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4f, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x56, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x90, 0x91, 0xe9, 0x87, 0x8f, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x50, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x52, 0x65,
	0x73, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x77, 0x0a, 0x1e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x07, 0x6b, 0x65, 0x79, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x90,
	0x9c, 0xe7, 0xb4, 0xa2, 0xe5, 0x85, 0xb3, 0xe9, 0x94, 0xae, 0xe5, 0xad, 0x97, 0x52, 0x07, 0x6b,
	0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x96, 0x04,
	0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x41, 0x75, 0x74,
	0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02,
	0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9,
	0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x68, 0x69, 0x67,
	0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe9, 0xab, 0x98, 0xe4, 0xba, 0xae, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52,
	0x09, 0x68, 0x69, 0x67, 0x68, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x12, 0xc0, 0x01, 0x0a, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x7e, 0x92, 0x41, 0x7b, 0x2a, 0x79, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90,
	0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xef, 0xbc, 0x9a, 0x31, 0x3a, 0xe6, 0x96, 0x87, 0xe7, 0xab,
	0xa0, 0x3b, 0x32, 0x3a, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0x3b, 0x33, 0x3a, 0xe5, 0x95, 0x86,
	0xe4, 0xb8, 0x9a, 0x3b, 0x34, 0x3a, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x3b,
	0x35, 0x3a, 0xe5, 0xae, 0x9e, 0xe5, 0x8b, 0x98, 0x3b, 0x36, 0x3a, 0xe8, 0xb0, 0x83, 0xe8, 0xa7,
	0xa3, 0x3b, 0x37, 0x3a, 0xe5, 0xbf, 0xab, 0xe8, 0xae, 0xaf, 0x3b, 0x38, 0x3a, 0xe6, 0x8a, 0xab,
	0xe9, 0x9c, 0xb2, 0x3b, 0x39, 0x3a, 0xe8, 0xaf, 0x84, 0xe4, 0xbb, 0xb7, 0x3b, 0x31, 0x30, 0x3a,
	0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95, 0x86, 0x3b, 0x31, 0x31, 0x3a, 0xe5, 0xb9, 0xbf,
	0xe5, 0x91, 0x8a, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0x52, 0x04, 0x6c, 0x61, 0x6e, 0x67, 0x12, 0x3b, 0x0a, 0x0f, 0x61, 0x72, 0x74, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe6, 0x96, 0x87, 0xe7, 0xab, 0xa0, 0xe5, 0x88, 0x86, 0xe7,
	0xb1, 0xbb, 0x49, 0x44, 0x52, 0x0d, 0x61, 0x72, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x13, 0x61, 0x66, 0x66, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9c, 0xba, 0xe6, 0x9e, 0x84, 0xe4, 0xbb, 0xa3,
	0xe7, 0xa0, 0x81, 0x52, 0x11, 0x61, 0x66, 0x66, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x63, 0x0a, 0x1c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x2a, 0x40, 0x0a, 0x0b, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x10, 0x01,
	0x12, 0x0b, 0x0a, 0x07, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x10, 0x02, 0x2a, 0x8f, 0x01,
	0x0a, 0x11, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69, 0x63, 0x10, 0x02,
	0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x10, 0x03, 0x2a,
	0x55, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65,
	0x72, 0x50, 0x61, 0x67, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x6f, 0x6d, 0x65,
	0x50, 0x61, 0x67, 0x65, 0x10, 0x01, 0x2a, 0x76, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79,
	0x4b, 0x4f, 0x4c, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x79, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x10, 0x02, 0x12, 0x1f, 0x0a,
	0x1b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x79, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x10, 0x03, 0x2a, 0xfe,
	0x02, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x10, 0x02, 0x12,
	0x1d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x10, 0x03, 0x12, 0x1b,
	0x0a, 0x17, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x52,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x46, 0x6c, 0x61, 0x73, 0x68,
	0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x09, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x10, 0x0a, 0x12,
	0x1e, 0x0a, 0x1a, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x10, 0x0b, 0x2a,
	0xcf, 0x03, 0x0a, 0x11, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x4a, 0x75, 0x6d,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x75, 0x74, 0x65, 0x72, 0x4c,
	0x69, 0x6e, 0x6b, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x65, 0x77, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x53, 0x75, 0x72, 0x76, 0x65,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x64, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x56, 0x50,
	0x53, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x10, 0x0a, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x69,
	0x61, 0x6c, 0x10, 0x0d, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73,
	0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61,
	0x6c, 0x48, 0x6f, 0x6d, 0x65, 0x70, 0x61, 0x67, 0x65, 0x10, 0x0e, 0x12, 0x24, 0x0a, 0x20, 0x41,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x10,
	0x0f, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x4a, 0x75,
	0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x61, 0x6e, 0x6b, 0x10, 0x10, 0x12, 0x17, 0x0a, 0x13,
	0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x35, 0x10, 0x11, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69,
	0x73, 0x65, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x10,
	0x37, 0x2a, 0xbd, 0x02, 0x0a, 0x11, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50,
	0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50,
	0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x10, 0x02, 0x12,
	0x1d, 0x0a, 0x19, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x10, 0x03, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x41,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x64, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x46, 0x6c, 0x61, 0x73, 0x68,
	0x10, 0x07, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x50,
	0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65,
	0x50, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x10,
	0x09, 0x2a, 0xa2, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54,
	0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74,
	0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x64, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x10, 0x02, 0x12, 0x1c, 0x0a,
	0x18, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x41,
	0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x6f, 0x74, 0x10,
	0x04, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x41, 0x64, 0x10, 0x05, 0x2a, 0xea, 0x02, 0x0a, 0x13, 0x59, 0x65, 0x61, 0x72, 0x6c,
	0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x67, 0x12, 0x1e,
	0x0a, 0x1a, 0x59, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x61, 0x67, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x61, 0x66, 0x65, 0x74, 0x79, 0x45, 0x78, 0x65, 0x72, 0x63, 0x69, 0x73, 0x65,
	0x72, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x6f, 0x72, 0x65, 0x69, 0x67, 0x6e, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x63, 0x79, 0x63, 0x6c, 0x6f, 0x70, 0x65, 0x64,
	0x69, 0x61, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x55, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x75, 0x70,
	0x65, 0x72, 0x68, 0x75, 0x6d, 0x61, 0x6e, 0x52, 0x69, 0x67, 0x68, 0x74, 0x73, 0x44, 0x65, 0x66,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x65, 0x77, 0x73, 0x53,
	0x61, 0x76, 0x76, 0x79, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x72, 0x75, 0x74, 0x68, 0x4b,
	0x65, 0x65, 0x70, 0x65, 0x72, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x57, 0x6f, 0x72, 0x64, 0x53, 0x75, 0x72, 0x66, 0x65, 0x72, 0x10, 0x07, 0x12, 0x14, 0x0a,
	0x10, 0x47, 0x6f, 0x6f, 0x64, 0x45, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x6c, 0x6f, 0x6e,
	0x73, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x6e, 0x63, 0x65, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x73, 0x10, 0x09, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d,
	0x62, 0x61, 0x73, 0x73, 0x61, 0x64, 0x6f, 0x72, 0x10, 0x0a, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x6f,
	0x72, 0x6f, 0x73, 0x4f, 0x66, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d,
	0x57, 0x61, 0x72, 0x72, 0x65, 0x6e, 0x42, 0x75, 0x66, 0x66, 0x65, 0x74, 0x74, 0x10, 0x0c, 0x12,
	0x14, 0x0a, 0x10, 0x53, 0x68, 0x65, 0x6c, 0x74, 0x65, 0x72, 0x65, 0x64, 0x46, 0x6c, 0x69, 0x70,
	0x70, 0x65, 0x72, 0x10, 0x0d, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x6f, 0x73, 0x74, 0x46, 0x61, 0x6e,
	0x73, 0x10, 0x0e, 0x2a, 0xab, 0x02, 0x0a, 0x12, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x65, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x4e,
	0x65, 0x77, 0x73, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x58, 0x45,
	0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x69, 0x6b, 0x69,
	0x46, 0x58, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x10, 0x06, 0x12,
	0x0d, 0x0a, 0x09, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x10, 0x07, 0x12, 0x08,
	0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x57, 0x69, 0x6b, 0x69,
	0x46, 0x58, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x10, 0x09, 0x12, 0x0a, 0x0a, 0x06,
	0x4c, 0x65, 0x6d, 0x6f, 0x6e, 0x58, 0x10, 0x0a, 0x12, 0x08, 0x0a, 0x04, 0x45, 0x78, 0x70, 0x6f,
	0x10, 0x0b, 0x12, 0x0a, 0x0a, 0x06, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x10, 0x0c, 0x12, 0x13,
	0x0a, 0x0f, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x45, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x45, 0x6c, 0x69,
	0x74, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x62, 0x10, 0x0e, 0x12, 0x16, 0x0a, 0x12, 0x57, 0x69, 0x6b,
	0x69, 0x46, 0x78, 0x53, 0x6b, 0x79, 0x6c, 0x69, 0x6e, 0x65, 0x47, 0x75, 0x69, 0x64, 0x65, 0x10,
	0x0f, 0x2a, 0x66, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x24,
	0x0a, 0x20, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x10, 0x01, 0x2a, 0x75, 0x0a, 0x0f, 0x48, 0x6f, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x17, 0x0a, 0x13,
	0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x65, 0x77, 0x10, 0x01, 0x12, 0x16, 0x0a,
	0x12, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x48, 0x6f, 0x74, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x48, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x10, 0x03,
	0x2a, 0x59, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x57, 0x65, 0x65, 0x6b, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x10, 0x01, 0x42, 0x15, 0x5a, 0x13, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_recommend_v1_models_proto_rawDescOnce sync.Once
	file_recommend_v1_models_proto_rawDescData = file_recommend_v1_models_proto_rawDesc
)

func file_recommend_v1_models_proto_rawDescGZIP() []byte {
	file_recommend_v1_models_proto_rawDescOnce.Do(func() {
		file_recommend_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_recommend_v1_models_proto_rawDescData)
	})
	return file_recommend_v1_models_proto_rawDescData
}

var file_recommend_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 13)
var file_recommend_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 76)
var file_recommend_v1_models_proto_goTypes = []interface{}{
	(ReleaseType)(0),                       // 0: api.recommend.v1.ReleaseType
	(RecommendCategory)(0),                 // 1: api.recommend.v1.RecommendCategory
	(RecommendPosition)(0),                 // 2: api.recommend.v1.RecommendPosition
	(UserIdentify)(0),                      // 3: api.recommend.v1.UserIdentify
	(RecommendItemType)(0),                 // 4: api.recommend.v1.RecommendItemType
	(AdvertiseJumpType)(0),                 // 5: api.recommend.v1.AdvertiseJumpType
	(AdvertisePostType)(0),                 // 6: api.recommend.v1.AdvertisePostType
	(AdvertiseTagType)(0),                  // 7: api.recommend.v1.AdvertiseTagType
	(YearlyReportUserTag)(0),               // 8: api.recommend.v1.YearlyReportUserTag
	(OfficialNumberType)(0),                // 9: api.recommend.v1.OfficialNumberType
	(RecommendFeedbackCategory)(0),         // 10: api.recommend.v1.RecommendFeedbackCategory
	(HotContentLabel)(0),                   // 11: api.recommend.v1.HotContentLabel
	(CreatorRankingCategory)(0),            // 12: api.recommend.v1.CreatorRankingCategory
	(*RecommendRequest)(nil),               // 13: api.recommend.v1.RecommendRequest
	(*RecommendV2Request)(nil),             // 14: api.recommend.v1.RecommendV2Request
	(*AdvertiseItem)(nil),                  // 15: api.recommend.v1.AdvertiseItem
	(*Advertise)(nil),                      // 16: api.recommend.v1.Advertise
	(*RecommendItemLabel)(nil),             // 17: api.recommend.v1.RecommendItemLabel
	(*RecommendItem)(nil),                  // 18: api.recommend.v1.RecommendItem
	(*RecommendReply)(nil),                 // 19: api.recommend.v1.RecommendReply
	(*FindCommerceByCategoryRequest)(nil),  // 20: api.recommend.v1.FindCommerceByCategoryRequest
	(*CommerceItem)(nil),                   // 21: api.recommend.v1.CommerceItem
	(*FindCommerceByCategoryReply)(nil),    // 22: api.recommend.v1.FindCommerceByCategoryReply
	(*Quote)(nil),                          // 23: api.recommend.v1.Quote
	(*YearlyReportRequest)(nil),            // 24: api.recommend.v1.YearlyReportRequest
	(*YearlyReportGlobal)(nil),             // 25: api.recommend.v1.YearlyReportGlobal
	(*YearlyReportUserObject)(nil),         // 26: api.recommend.v1.YearlyReportUserObject
	(*YearlyReportUser)(nil),               // 27: api.recommend.v1.YearlyReportUser
	(*YearlyReportReply)(nil),              // 28: api.recommend.v1.YearlyReportReply
	(*FindHotAndNewRequest)(nil),           // 29: api.recommend.v1.FindHotAndNewRequest
	(*HotAndNewItem)(nil),                  // 30: api.recommend.v1.HotAndNewItem
	(*FindHotAndNewReply)(nil),             // 31: api.recommend.v1.FindHotAndNewReply
	(*HotAndNewV2Item)(nil),                // 32: api.recommend.v1.HotAndNewV2Item
	(*FindHotAndNewV2Reply)(nil),           // 33: api.recommend.v1.FindHotAndNewV2Reply
	(*FollowItem)(nil),                     // 34: api.recommend.v1.FollowItem
	(*FindFollowPublishRequest)(nil),       // 35: api.recommend.v1.FindFollowPublishRequest
	(*FollowPublishItem)(nil),              // 36: api.recommend.v1.FollowPublishItem
	(*FindFollowPublishReply)(nil),         // 37: api.recommend.v1.FindFollowPublishReply
	(*TraderHomeRequest)(nil),              // 38: api.recommend.v1.TraderHomeRequest
	(*TraderHomeReply)(nil),                // 39: api.recommend.v1.TraderHomeReply
	(*TraderPostCountRequest)(nil),         // 40: api.recommend.v1.TraderPostCountRequest
	(*TraderPostCountReply)(nil),           // 41: api.recommend.v1.TraderPostCountReply
	(*SearchRequest)(nil),                  // 42: api.recommend.v1.SearchRequest
	(*SearchItem)(nil),                     // 43: api.recommend.v1.SearchItem
	(*SearchReply)(nil),                    // 44: api.recommend.v1.SearchReply
	(*FindSearchTitleRequest)(nil),         // 45: api.recommend.v1.FindSearchTitleRequest
	(*SearchTitleItem)(nil),                // 46: api.recommend.v1.SearchTitleItem
	(*FindSearchTitleReply)(nil),           // 47: api.recommend.v1.FindSearchTitleReply
	(*InterestedUserRequest)(nil),          // 48: api.recommend.v1.InterestedUserRequest
	(*InterestedUserReply)(nil),            // 49: api.recommend.v1.InterestedUserReply
	(*FindHotContentRequest)(nil),          // 50: api.recommend.v1.FindHotContentRequest
	(*FindHotContentReply)(nil),            // 51: api.recommend.v1.FindHotContentReply
	(*RecommendUserRequest)(nil),           // 52: api.recommend.v1.RecommendUserRequest
	(*RecommendUserReply)(nil),             // 53: api.recommend.v1.RecommendUserReply
	(*ActivityRequest)(nil),                // 54: api.recommend.v1.ActivityRequest
	(*ActivityItem)(nil),                   // 55: api.recommend.v1.ActivityItem
	(*ActivityReply)(nil),                  // 56: api.recommend.v1.ActivityReply
	(*RecommendFeedbackRequest)(nil),       // 57: api.recommend.v1.RecommendFeedbackRequest
	(*RecommendFeedbackResponse)(nil),      // 58: api.recommend.v1.RecommendFeedbackResponse
	(*HotContentRankingRequest)(nil),       // 59: api.recommend.v1.HotContentRankingRequest
	(*HotContentRankingItem)(nil),          // 60: api.recommend.v1.HotContentRankingItem
	(*HotContentRankingResponse)(nil),      // 61: api.recommend.v1.HotContentRankingResponse
	(*RankingScopeRequest)(nil),            // 62: api.recommend.v1.RankingScopeRequest
	(*RankingScopeResponse)(nil),           // 63: api.recommend.v1.RankingScopeResponse
	(*CreatorRankingRequest)(nil),          // 64: api.recommend.v1.CreatorRankingRequest
	(*CreatorRankingItem)(nil),             // 65: api.recommend.v1.CreatorRankingItem
	(*CreatorRankingResponse)(nil),         // 66: api.recommend.v1.CreatorRankingResponse
	(*CreatorRankingNoticeRequest)(nil),    // 67: api.recommend.v1.CreatorRankingNoticeRequest
	(*CreatorRankingNoticeResponse)(nil),   // 68: api.recommend.v1.CreatorRankingNoticeResponse
	(*UserContentRequest)(nil),             // 69: api.recommend.v1.UserContentRequest
	(*RecommendTestRequest)(nil),           // 70: api.recommend.v1.RecommendTestRequest
	(*RecommendTestItem)(nil),              // 71: api.recommend.v1.RecommendTestItem
	(*RecommendTestReply)(nil),             // 72: api.recommend.v1.RecommendTestReply
	(*ContentSimilarityRequest)(nil),       // 73: api.recommend.v1.ContentSimilarityRequest
	(*ContentSimilarityReply)(nil),         // 74: api.recommend.v1.ContentSimilarityReply
	(*UserBehaviorRequest)(nil),            // 75: api.recommend.v1.UserBehaviorRequest
	(*UserBehaviorItem)(nil),               // 76: api.recommend.v1.UserBehaviorItem
	(*UserBehaviorReply)(nil),              // 77: api.recommend.v1.UserBehaviorReply
	(*UserOriginBehaviorRequest)(nil),      // 78: api.recommend.v1.UserOriginBehaviorRequest
	(*UserOriginBehaviorItem)(nil),         // 79: api.recommend.v1.UserOriginBehaviorItem
	(*UserOriginBehaviorReply)(nil),        // 80: api.recommend.v1.UserOriginBehaviorReply
	(*UserVectorRequest)(nil),              // 81: api.recommend.v1.UserVectorRequest
	(*UserVectorItem)(nil),                 // 82: api.recommend.v1.UserVectorItem
	(*UserVectorReply)(nil),                // 83: api.recommend.v1.UserVectorReply
	(*ResetUserBehaviorRequest)(nil),       // 84: api.recommend.v1.ResetUserBehaviorRequest
	(*ResetUserBehaviorReply)(nil),         // 85: api.recommend.v1.ResetUserBehaviorReply
	(*SearchTitleAutoCompleteRequest)(nil), // 86: api.recommend.v1.SearchTitleAutoCompleteRequest
	(*SearchTitleAutoCompleteItem)(nil),    // 87: api.recommend.v1.SearchTitleAutoCompleteItem
	(*SearchTitleAutoCompleteReply)(nil),   // 88: api.recommend.v1.SearchTitleAutoCompleteReply
}
var file_recommend_v1_models_proto_depIdxs = []int32{
	0,  // 0: api.recommend.v1.RecommendRequest.release_type:type_name -> api.recommend.v1.ReleaseType
	3,  // 1: api.recommend.v1.RecommendRequest.user_identify:type_name -> api.recommend.v1.UserIdentify
	1,  // 2: api.recommend.v1.RecommendV2Request.category:type_name -> api.recommend.v1.RecommendCategory
	2,  // 3: api.recommend.v1.RecommendV2Request.position:type_name -> api.recommend.v1.RecommendPosition
	3,  // 4: api.recommend.v1.RecommendV2Request.user_identify:type_name -> api.recommend.v1.UserIdentify
	5,  // 5: api.recommend.v1.Advertise.jump_type:type_name -> api.recommend.v1.AdvertiseJumpType
	7,  // 6: api.recommend.v1.Advertise.tag_type:type_name -> api.recommend.v1.AdvertiseTagType
	15, // 7: api.recommend.v1.Advertise.items:type_name -> api.recommend.v1.AdvertiseItem
	6,  // 8: api.recommend.v1.Advertise.post_type:type_name -> api.recommend.v1.AdvertisePostType
	4,  // 9: api.recommend.v1.RecommendItem.item_type:type_name -> api.recommend.v1.RecommendItemType
	16, // 10: api.recommend.v1.RecommendItem.advertise:type_name -> api.recommend.v1.Advertise
	17, // 11: api.recommend.v1.RecommendItem.labels:type_name -> api.recommend.v1.RecommendItemLabel
	18, // 12: api.recommend.v1.RecommendReply.items:type_name -> api.recommend.v1.RecommendItem
	4,  // 13: api.recommend.v1.CommerceItem.type:type_name -> api.recommend.v1.RecommendItemType
	16, // 14: api.recommend.v1.CommerceItem.advertise:type_name -> api.recommend.v1.Advertise
	21, // 15: api.recommend.v1.FindCommerceByCategoryReply.items:type_name -> api.recommend.v1.CommerceItem
	23, // 16: api.recommend.v1.YearlyReportGlobal.quotes:type_name -> api.recommend.v1.Quote
	23, // 17: api.recommend.v1.YearlyReportGlobal.yearly_inc_top2:type_name -> api.recommend.v1.Quote
	23, // 18: api.recommend.v1.YearlyReportGlobal.yearly_desc_top1:type_name -> api.recommend.v1.Quote
	23, // 19: api.recommend.v1.YearlyReportGlobal.yearly_durable_inc_top1:type_name -> api.recommend.v1.Quote
	26, // 20: api.recommend.v1.YearlyReportUser.view_trader_list:type_name -> api.recommend.v1.YearlyReportUserObject
	26, // 21: api.recommend.v1.YearlyReportUser.view_service_provider_list:type_name -> api.recommend.v1.YearlyReportUserObject
	23, // 22: api.recommend.v1.YearlyReportUser.interested_quote:type_name -> api.recommend.v1.Quote
	23, // 23: api.recommend.v1.YearlyReportUser.mock_trade_most_quote:type_name -> api.recommend.v1.Quote
	26, // 24: api.recommend.v1.YearlyReportUser.first_post_view_user_list:type_name -> api.recommend.v1.YearlyReportUserObject
	8,  // 25: api.recommend.v1.YearlyReportUser.tag:type_name -> api.recommend.v1.YearlyReportUserTag
	25, // 26: api.recommend.v1.YearlyReportReply.global:type_name -> api.recommend.v1.YearlyReportGlobal
	27, // 27: api.recommend.v1.YearlyReportReply.user:type_name -> api.recommend.v1.YearlyReportUser
	0,  // 28: api.recommend.v1.FindHotAndNewRequest.release_type:type_name -> api.recommend.v1.ReleaseType
	30, // 29: api.recommend.v1.FindHotAndNewReply.items:type_name -> api.recommend.v1.HotAndNewItem
	4,  // 30: api.recommend.v1.HotAndNewV2Item.type:type_name -> api.recommend.v1.RecommendItemType
	16, // 31: api.recommend.v1.HotAndNewV2Item.advertise:type_name -> api.recommend.v1.Advertise
	32, // 32: api.recommend.v1.FindHotAndNewV2Reply.items:type_name -> api.recommend.v1.HotAndNewV2Item
	9,  // 33: api.recommend.v1.FollowItem.type:type_name -> api.recommend.v1.OfficialNumberType
	34, // 34: api.recommend.v1.FindFollowPublishRequest.follows:type_name -> api.recommend.v1.FollowItem
	4,  // 35: api.recommend.v1.FollowPublishItem.type:type_name -> api.recommend.v1.RecommendItemType
	36, // 36: api.recommend.v1.FindFollowPublishReply.items:type_name -> api.recommend.v1.FollowPublishItem
	0,  // 37: api.recommend.v1.TraderHomeRequest.release_type:type_name -> api.recommend.v1.ReleaseType
	9,  // 38: api.recommend.v1.TraderHomeRequest.official_number_type:type_name -> api.recommend.v1.OfficialNumberType
	36, // 39: api.recommend.v1.TraderHomeReply.items:type_name -> api.recommend.v1.FollowPublishItem
	9,  // 40: api.recommend.v1.TraderPostCountRequest.official_number_type:type_name -> api.recommend.v1.OfficialNumberType
	4,  // 41: api.recommend.v1.SearchItem.type:type_name -> api.recommend.v1.RecommendItemType
	43, // 42: api.recommend.v1.SearchReply.items:type_name -> api.recommend.v1.SearchItem
	46, // 43: api.recommend.v1.FindSearchTitleReply.items:type_name -> api.recommend.v1.SearchTitleItem
	18, // 44: api.recommend.v1.FindHotContentReply.items:type_name -> api.recommend.v1.RecommendItem
	55, // 45: api.recommend.v1.ActivityReply.items:type_name -> api.recommend.v1.ActivityItem
	4,  // 46: api.recommend.v1.RecommendFeedbackRequest.item_type:type_name -> api.recommend.v1.RecommendItemType
	10, // 47: api.recommend.v1.RecommendFeedbackRequest.category:type_name -> api.recommend.v1.RecommendFeedbackCategory
	4,  // 48: api.recommend.v1.HotContentRankingItem.type:type_name -> api.recommend.v1.RecommendItemType
	11, // 49: api.recommend.v1.HotContentRankingItem.label:type_name -> api.recommend.v1.HotContentLabel
	60, // 50: api.recommend.v1.HotContentRankingResponse.items:type_name -> api.recommend.v1.HotContentRankingItem
	12, // 51: api.recommend.v1.CreatorRankingRequest.category:type_name -> api.recommend.v1.CreatorRankingCategory
	65, // 52: api.recommend.v1.CreatorRankingResponse.items:type_name -> api.recommend.v1.CreatorRankingItem
	12, // 53: api.recommend.v1.CreatorRankingNoticeRequest.category:type_name -> api.recommend.v1.CreatorRankingCategory
	0,  // 54: api.recommend.v1.UserContentRequest.release_type:type_name -> api.recommend.v1.ReleaseType
	1,  // 55: api.recommend.v1.RecommendTestRequest.category:type_name -> api.recommend.v1.RecommendCategory
	71, // 56: api.recommend.v1.RecommendTestReply.items:type_name -> api.recommend.v1.RecommendTestItem
	71, // 57: api.recommend.v1.ContentSimilarityReply.items:type_name -> api.recommend.v1.RecommendTestItem
	76, // 58: api.recommend.v1.UserBehaviorReply.items:type_name -> api.recommend.v1.UserBehaviorItem
	79, // 59: api.recommend.v1.UserOriginBehaviorReply.items:type_name -> api.recommend.v1.UserOriginBehaviorItem
	82, // 60: api.recommend.v1.UserVectorReply.items:type_name -> api.recommend.v1.UserVectorItem
	4,  // 61: api.recommend.v1.SearchTitleAutoCompleteItem.item_type:type_name -> api.recommend.v1.RecommendItemType
	87, // 62: api.recommend.v1.SearchTitleAutoCompleteReply.items:type_name -> api.recommend.v1.SearchTitleAutoCompleteItem
	63, // [63:63] is the sub-list for method output_type
	63, // [63:63] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_recommend_v1_models_proto_init() }
func file_recommend_v1_models_proto_init() {
	if File_recommend_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_recommend_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AdvertiseItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Advertise); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendItemLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindCommerceByCategoryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommerceItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindCommerceByCategoryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Quote); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearlyReportRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearlyReportGlobal); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearlyReportUserObject); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearlyReportUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*YearlyReportReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindHotAndNewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotAndNewItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindHotAndNewReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotAndNewV2Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindHotAndNewV2Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindFollowPublishRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FollowPublishItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindFollowPublishReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderHomeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderHomeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderPostCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderPostCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindSearchTitleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTitleItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindSearchTitleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterestedUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InterestedUserReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindHotContentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindHotContentReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendUserReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendFeedbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendFeedbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotContentRankingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotContentRankingItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HotContentRankingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankingScopeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankingScopeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatorRankingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatorRankingItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatorRankingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatorRankingNoticeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatorRankingNoticeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserContentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendTestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendTestItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecommendTestReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentSimilarityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentSimilarityReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBehaviorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBehaviorItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBehaviorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserOriginBehaviorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserOriginBehaviorItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserOriginBehaviorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserVectorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserVectorItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserVectorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetUserBehaviorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetUserBehaviorReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTitleAutoCompleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTitleAutoCompleteItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_recommend_v1_models_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTitleAutoCompleteReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_recommend_v1_models_proto_rawDesc,
			NumEnums:      13,
			NumMessages:   76,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_recommend_v1_models_proto_goTypes,
		DependencyIndexes: file_recommend_v1_models_proto_depIdxs,
		EnumInfos:         file_recommend_v1_models_proto_enumTypes,
		MessageInfos:      file_recommend_v1_models_proto_msgTypes,
	}.Build()
	File_recommend_v1_models_proto = out.File
	file_recommend_v1_models_proto_rawDesc = nil
	file_recommend_v1_models_proto_goTypes = nil
	file_recommend_v1_models_proto_depIdxs = nil
}
