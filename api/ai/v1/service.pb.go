// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: ai/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TranslateStatus int32

const (
	TranslateStatus_TranslateStatusWaiting    TranslateStatus = 0 // 等待翻译
	TranslateStatus_TranslateStatusProcessing TranslateStatus = 1 // 翻译中
	TranslateStatus_TranslateStatusSuccess    TranslateStatus = 2 // 翻译成功
	TranslateStatus_TranslateStatusFailed     TranslateStatus = 3 // 翻译失败
)

// Enum value maps for TranslateStatus.
var (
	TranslateStatus_name = map[int32]string{
		0: "TranslateStatusWaiting",
		1: "TranslateStatusProcessing",
		2: "TranslateStatusSuccess",
		3: "TranslateStatusFailed",
	}
	TranslateStatus_value = map[string]int32{
		"TranslateStatusWaiting":    0,
		"TranslateStatusProcessing": 1,
		"TranslateStatusSuccess":    2,
		"TranslateStatusFailed":     3,
	}
)

func (x TranslateStatus) Enum() *TranslateStatus {
	p := new(TranslateStatus)
	*p = x
	return p
}

func (x TranslateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TranslateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_v1_service_proto_enumTypes[0].Descriptor()
}

func (TranslateStatus) Type() protoreflect.EnumType {
	return &file_ai_v1_service_proto_enumTypes[0]
}

func (x TranslateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TranslateStatus.Descriptor instead.
func (TranslateStatus) EnumDescriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{0}
}

type TranslateCallbackStatus int32

const (
	TranslateCallbackStatus_TranslateCallbackStatusWaiting TranslateCallbackStatus = 0 // 等待回调
	TranslateCallbackStatus_TranslateCallbackStatusSuccess TranslateCallbackStatus = 1 // 回调成功
	TranslateCallbackStatus_TranslateCallbackStatusFailed  TranslateCallbackStatus = 2 // 回调失败
)

// Enum value maps for TranslateCallbackStatus.
var (
	TranslateCallbackStatus_name = map[int32]string{
		0: "TranslateCallbackStatusWaiting",
		1: "TranslateCallbackStatusSuccess",
		2: "TranslateCallbackStatusFailed",
	}
	TranslateCallbackStatus_value = map[string]int32{
		"TranslateCallbackStatusWaiting": 0,
		"TranslateCallbackStatusSuccess": 1,
		"TranslateCallbackStatusFailed":  2,
	}
)

func (x TranslateCallbackStatus) Enum() *TranslateCallbackStatus {
	p := new(TranslateCallbackStatus)
	*p = x
	return p
}

func (x TranslateCallbackStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TranslateCallbackStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_v1_service_proto_enumTypes[1].Descriptor()
}

func (TranslateCallbackStatus) Type() protoreflect.EnumType {
	return &file_ai_v1_service_proto_enumTypes[1]
}

func (x TranslateCallbackStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TranslateCallbackStatus.Descriptor instead.
func (TranslateCallbackStatus) EnumDescriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{1}
}

type ActionMode int32

const (
	ActionMode_ActionModeComment   ActionMode = 0 // 评价
	ActionMode_ActionModeLike      ActionMode = 1 // 点赞帖子
	ActionMode_ActionModeCollect   ActionMode = 2 // 收藏
	ActionMode_ActionModeAttention ActionMode = 3 // 关注
	ActionMode_ActionModeView      ActionMode = 4 // 浏览帖子
)

// Enum value maps for ActionMode.
var (
	ActionMode_name = map[int32]string{
		0: "ActionModeComment",
		1: "ActionModeLike",
		2: "ActionModeCollect",
		3: "ActionModeAttention",
		4: "ActionModeView",
	}
	ActionMode_value = map[string]int32{
		"ActionModeComment":   0,
		"ActionModeLike":      1,
		"ActionModeCollect":   2,
		"ActionModeAttention": 3,
		"ActionModeView":      4,
	}
)

func (x ActionMode) Enum() *ActionMode {
	p := new(ActionMode)
	*p = x
	return p
}

func (x ActionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_ai_v1_service_proto_enumTypes[2].Descriptor()
}

func (ActionMode) Type() protoreflect.EnumType {
	return &file_ai_v1_service_proto_enumTypes[2]
}

func (x ActionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionMode.Descriptor instead.
func (ActionMode) EnumDescriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{2}
}

type GetAsyncTranslateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *GetAsyncTranslateRequest) Reset() {
	*x = GetAsyncTranslateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAsyncTranslateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAsyncTranslateRequest) ProtoMessage() {}

func (x *GetAsyncTranslateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAsyncTranslateRequest.ProtoReflect.Descriptor instead.
func (*GetAsyncTranslateRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetAsyncTranslateRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type CallbackRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Request    string `protobuf:"bytes,1,opt,name=request,json=request,proto3" json:"request"`
	Response   string `protobuf:"bytes,2,opt,name=response,json=response,proto3" json:"response"`
	StatusCode int32  `protobuf:"varint,3,opt,name=status_code,json=statusCode,proto3" json:"status_code"`
	Time       string `protobuf:"bytes,4,opt,name=time,json=time,proto3" json:"time"`
	Message    string `protobuf:"bytes,5,opt,name=message,json=message,proto3" json:"message"`
}

func (x *CallbackRecord) Reset() {
	*x = CallbackRecord{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackRecord) ProtoMessage() {}

func (x *CallbackRecord) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackRecord.ProtoReflect.Descriptor instead.
func (*CallbackRecord) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *CallbackRecord) GetRequest() string {
	if x != nil {
		return x.Request
	}
	return ""
}

func (x *CallbackRecord) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *CallbackRecord) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *CallbackRecord) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *CallbackRecord) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type AsyncTranslateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId          string                  `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Text            []string                `protobuf:"bytes,2,rep,name=text,json=text,proto3" json:"text"`
	Status          TranslateStatus         `protobuf:"varint,3,opt,name=status,json=status,proto3,enum=api.wiki_ai.v1.TranslateStatus" json:"status"`
	Reason          string                  `protobuf:"bytes,4,opt,name=reason,json=reason,proto3" json:"reason"`
	CreatedAt       int64                   `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Retry           int32                   `protobuf:"varint,6,opt,name=retry,json=retry,proto3" json:"retry"`
	OperationId     string                  `protobuf:"bytes,7,opt,name=operation_id,json=operationId,proto3" json:"operation_id"`
	Attach          string                  `protobuf:"bytes,8,opt,name=attach,json=attach,proto3" json:"attach"`
	OriginText      []string                `protobuf:"bytes,9,rep,name=origin_text,json=originText,proto3" json:"origin_text"`
	CallbackUrl     string                  `protobuf:"bytes,10,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url"`
	Headers         []*Header               `protobuf:"bytes,11,rep,name=headers,json=headers,proto3" json:"headers"`
	CallbackStatus  TranslateCallbackStatus `protobuf:"varint,12,opt,name=callback_status,json=callbackStatus,proto3,enum=api.wiki_ai.v1.TranslateCallbackStatus" json:"callback_status"`
	CallbackRetry   int32                   `protobuf:"varint,13,opt,name=callback_retry,json=callbackRetry,proto3" json:"callback_retry"`
	CallbackRecords []*CallbackRecord       `protobuf:"bytes,14,rep,name=callback_records,json=callbackRecords,proto3" json:"callback_records"`
	OriginTextLang  []string                `protobuf:"bytes,15,rep,name=origin_text_lang,json=originTextLang,proto3" json:"origin_text_lang"`
	Codes           []string                `protobuf:"bytes,16,rep,name=codes,json=codes,proto3" json:"codes"`
	Names           []string                `protobuf:"bytes,17,rep,name=names,json=names,proto3" json:"names"`
	Prompt          string                  `protobuf:"bytes,18,opt,name=prompt,json=prompt,proto3" json:"prompt"`
}

func (x *AsyncTranslateInfo) Reset() {
	*x = AsyncTranslateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AsyncTranslateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncTranslateInfo) ProtoMessage() {}

func (x *AsyncTranslateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncTranslateInfo.ProtoReflect.Descriptor instead.
func (*AsyncTranslateInfo) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *AsyncTranslateInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *AsyncTranslateInfo) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *AsyncTranslateInfo) GetStatus() TranslateStatus {
	if x != nil {
		return x.Status
	}
	return TranslateStatus_TranslateStatusWaiting
}

func (x *AsyncTranslateInfo) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AsyncTranslateInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AsyncTranslateInfo) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *AsyncTranslateInfo) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *AsyncTranslateInfo) GetAttach() string {
	if x != nil {
		return x.Attach
	}
	return ""
}

func (x *AsyncTranslateInfo) GetOriginText() []string {
	if x != nil {
		return x.OriginText
	}
	return nil
}

func (x *AsyncTranslateInfo) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *AsyncTranslateInfo) GetHeaders() []*Header {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *AsyncTranslateInfo) GetCallbackStatus() TranslateCallbackStatus {
	if x != nil {
		return x.CallbackStatus
	}
	return TranslateCallbackStatus_TranslateCallbackStatusWaiting
}

func (x *AsyncTranslateInfo) GetCallbackRetry() int32 {
	if x != nil {
		return x.CallbackRetry
	}
	return 0
}

func (x *AsyncTranslateInfo) GetCallbackRecords() []*CallbackRecord {
	if x != nil {
		return x.CallbackRecords
	}
	return nil
}

func (x *AsyncTranslateInfo) GetOriginTextLang() []string {
	if x != nil {
		return x.OriginTextLang
	}
	return nil
}

func (x *AsyncTranslateInfo) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *AsyncTranslateInfo) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *AsyncTranslateInfo) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key   string `protobuf:"bytes,1,opt,name=key,json=key,proto3" json:"key"`
	Value string `protobuf:"bytes,2,opt,name=value,json=value,proto3" json:"value"`
}

func (x *Header) Reset() {
	*x = Header{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *Header) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Header) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type AsyncTranslateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text        []string  `protobuf:"bytes,1,rep,name=text,json=text,proto3" json:"text"`
	To          string    `protobuf:"bytes,2,opt,name=to,json=to,proto3" json:"to"`
	CallbackUrl string    `protobuf:"bytes,3,opt,name=callback_url,json=callbackUrl,proto3" json:"callback_url"`
	Headers     []*Header `protobuf:"bytes,4,rep,name=headers,json=headers,proto3" json:"headers"`
	OperationId string    `protobuf:"bytes,5,opt,name=operation_id,json=operationId,proto3" json:"operation_id"`
	Attach      string    `protobuf:"bytes,6,opt,name=attach,json=attach,proto3" json:"attach"`
	Codes       []string  `protobuf:"bytes,7,rep,name=codes,json=codes,proto3" json:"codes"`
	Names       []string  `protobuf:"bytes,8,rep,name=names,json=names,proto3" json:"names"`
	Prompt      string    `protobuf:"bytes,9,opt,name=prompt,json=prompt,proto3" json:"prompt"`
	Method      string    `protobuf:"bytes,10,opt,name=Method,json=Method,proto3" json:"Method"`
}

func (x *AsyncTranslateRequest) Reset() {
	*x = AsyncTranslateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AsyncTranslateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncTranslateRequest) ProtoMessage() {}

func (x *AsyncTranslateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncTranslateRequest.ProtoReflect.Descriptor instead.
func (*AsyncTranslateRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *AsyncTranslateRequest) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *AsyncTranslateRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *AsyncTranslateRequest) GetCallbackUrl() string {
	if x != nil {
		return x.CallbackUrl
	}
	return ""
}

func (x *AsyncTranslateRequest) GetHeaders() []*Header {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *AsyncTranslateRequest) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *AsyncTranslateRequest) GetAttach() string {
	if x != nil {
		return x.Attach
	}
	return ""
}

func (x *AsyncTranslateRequest) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *AsyncTranslateRequest) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *AsyncTranslateRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *AsyncTranslateRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

type AsyncTranslateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
}

func (x *AsyncTranslateReply) Reset() {
	*x = AsyncTranslateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AsyncTranslateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsyncTranslateReply) ProtoMessage() {}

func (x *AsyncTranslateReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsyncTranslateReply.ProtoReflect.Descriptor instead.
func (*AsyncTranslateReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *AsyncTranslateReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type TranslateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text        []string `protobuf:"bytes,1,rep,name=text,json=text,proto3" json:"text"`
	To          string   `protobuf:"bytes,2,opt,name=to,json=to,proto3" json:"to"`
	Codes       []string `protobuf:"bytes,3,rep,name=codes,json=codes,proto3" json:"codes"`
	Names       []string `protobuf:"bytes,4,rep,name=names,json=names,proto3" json:"names"`
	Prompt      string   `protobuf:"bytes,5,opt,name=prompt,json=prompt,proto3" json:"prompt"`
	Method      string   `protobuf:"bytes,6,opt,name=Method,json=Method,proto3" json:"Method"`
	OperationId string   `protobuf:"bytes,7,opt,name=operation_id,json=operationId,proto3" json:"operation_id"`
}

func (x *TranslateRequest) Reset() {
	*x = TranslateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslateRequest) ProtoMessage() {}

func (x *TranslateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslateRequest.ProtoReflect.Descriptor instead.
func (*TranslateRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *TranslateRequest) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TranslateRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *TranslateRequest) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *TranslateRequest) GetNames() []string {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *TranslateRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *TranslateRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *TranslateRequest) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

type TranslateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text           []string `protobuf:"bytes,1,rep,name=text,json=text,proto3" json:"text"`
	OriginText     []string `protobuf:"bytes,2,rep,name=origin_text,json=originText,proto3" json:"origin_text"`
	OriginTextLang []string `protobuf:"bytes,3,rep,name=origin_text_lang,json=originTextLang,proto3" json:"origin_text_lang"`
}

func (x *TranslateReply) Reset() {
	*x = TranslateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslateReply) ProtoMessage() {}

func (x *TranslateReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslateReply.ProtoReflect.Descriptor instead.
func (*TranslateReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{7}
}

func (x *TranslateReply) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TranslateReply) GetOriginText() []string {
	if x != nil {
		return x.OriginText
	}
	return nil
}

func (x *TranslateReply) GetOriginTextLang() []string {
	if x != nil {
		return x.OriginTextLang
	}
	return nil
}

type TranslateCustomRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text         []string `protobuf:"bytes,1,rep,name=text,json=text,proto3" json:"text"`
	To           string   `protobuf:"bytes,2,opt,name=to,json=to,proto3" json:"to"`
	ModelName    string   `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name"`
	SystemPrompt string   `protobuf:"bytes,4,opt,name=system_prompt,json=systemPrompt,proto3" json:"system_prompt"`
	MaxTokens    int32    `protobuf:"varint,5,opt,name=max_tokens,json=maxTokens,proto3" json:"max_tokens"`
}

func (x *TranslateCustomRequest) Reset() {
	*x = TranslateCustomRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslateCustomRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslateCustomRequest) ProtoMessage() {}

func (x *TranslateCustomRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslateCustomRequest.ProtoReflect.Descriptor instead.
func (*TranslateCustomRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *TranslateCustomRequest) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *TranslateCustomRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *TranslateCustomRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *TranslateCustomRequest) GetSystemPrompt() string {
	if x != nil {
		return x.SystemPrompt
	}
	return ""
}

func (x *TranslateCustomRequest) GetMaxTokens() int32 {
	if x != nil {
		return x.MaxTokens
	}
	return 0
}

type TranslateCustomReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text []string `protobuf:"bytes,1,rep,name=text,json=text,proto3" json:"text"`
}

func (x *TranslateCustomReply) Reset() {
	*x = TranslateCustomReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TranslateCustomReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TranslateCustomReply) ProtoMessage() {}

func (x *TranslateCustomReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TranslateCustomReply.ProtoReflect.Descriptor instead.
func (*TranslateCustomReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *TranslateCustomReply) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

type TraderItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TraderCode       string  `protobuf:"bytes,1,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`                      // 交易商code
	Company          string  `protobuf:"bytes,2,opt,name=company,json=company,proto3" json:"company"`                                 // 公司名称
	Address          string  `protobuf:"bytes,3,opt,name=address,json=address,proto3" json:"address"`                                 // 公司地址
	FoundDate        string  `protobuf:"bytes,4,opt,name=found_date,json=foundDate,proto3" json:"found_date"`                         // 成立时间
	CountryName      string  `protobuf:"bytes,5,opt,name=country_name,json=countryName,proto3" json:"country_name"`                   // 成立国家名称
	ShortName        string  `protobuf:"bytes,6,opt,name=short_name,json=shortName,proto3" json:"short_name"`                         // 简称
	Websites         string  `protobuf:"bytes,7,opt,name=websites,json=websites,proto3" json:"websites"`                              // 网址
	AreaCode         string  `protobuf:"bytes,8,opt,name=area_code,json=areaCode,proto3" json:"area_code"`                            // 区域code
	AreaName         string  `protobuf:"bytes,9,opt,name=area_name,json=areaName,proto3" json:"area_name"`                            // 国家所在区域
	Score            float32 `protobuf:"fixed32,15,opt,name=score,json=score,proto3" json:"score"`                                    // 分数
	Label            string  `protobuf:"bytes,16,opt,name=label,json=label,proto3" json:"label"`                                      // 标签
	LicenseName      string  `protobuf:"bytes,17,opt,name=license_name,json=licenseName,proto3" json:"license_name"`                  // 牌照名称
	RegulatoryScore  float32 `protobuf:"fixed32,18,opt,name=regulatory_score,json=regulatoryScore,proto3" json:"regulatory_score"`    // 监管指数
	BusinessScore    float32 `protobuf:"fixed32,19,opt,name=business_score,json=businessScore,proto3" json:"business_score"`          // 业务指数
	RiskScore        float32 `protobuf:"fixed32,20,opt,name=risk_score,json=riskScore,proto3" json:"risk_score"`                      // 分控指数
	SoftwareScore    float32 `protobuf:"fixed32,21,opt,name=software_score,json=softwareScore,proto3" json:"software_score"`          // 软件指数
	LicenseScore     float32 `protobuf:"fixed32,22,opt,name=license_score,json=licenseScore,proto3" json:"license_score"`             // 牌照指数
	Mt4StatusName    string  `protobuf:"bytes,23,opt,name=mt4_status_name,json=mt4StatusName,proto3" json:"mt4_status_name"`          // mt4状态名称
	Mt5StatusName    string  `protobuf:"bytes,24,opt,name=mt5_status_name,json=mt5StatusName,proto3" json:"mt5_status_name"`          // mt5状态名称
	InfluenceClass   string  `protobuf:"bytes,25,opt,name=influence_class,json=influenceClass,proto3" json:"influence_class"`         // 影响力等级
	TradeEnvironment string  `protobuf:"bytes,26,opt,name=trade_environment,json=tradeEnvironment,proto3" json:"trade_environment"`   // 交易环境
	WikifxWebsiteUrl string  `protobuf:"bytes,27,opt,name=wikifx_website_url,json=wikifxWebsiteUrl,proto3" json:"wikifx_website_url"` // wikifx官网地址
	IsEpc            bool    `protobuf:"varint,28,opt,name=is_epc,json=isEpc,proto3" json:"is_epc"`                                   // 是否购买保障服务'
	NotEpcScore      float32 `protobuf:"fixed32,29,opt,name=not_epc_score,json=notEpcScore,proto3" json:"not_epc_score"`              // 没有购买保障时的分数
	Level1EpcScore   float32 `protobuf:"fixed32,30,opt,name=level1_epc_score,json=level1EpcScore,proto3" json:"level1_epc_score"`     // 一级保障分数
	Level2EpcScore   float32 `protobuf:"fixed32,31,opt,name=level2_epc_score,json=level2EpcScore,proto3" json:"level2_epc_score"`     // 二级保障分数
	Level3EpcScore   float32 `protobuf:"fixed32,32,opt,name=level3_epc_score,json=level3EpcScore,proto3" json:"level3_epc_score"`     // 三级保障分数
	Level4EpcScore   float32 `protobuf:"fixed32,33,opt,name=level4_epc_score,json=level4EpcScore,proto3" json:"level4_epc_score"`     // 四级保障分数
	Level5EpcScore   float32 `protobuf:"fixed32,34,opt,name=level5_epc_score,json=level5EpcScore,proto3" json:"level5_epc_score"`     // 五级保障分数
	Description      string  `protobuf:"bytes,35,opt,name=description,json=description,proto3" json:"description"`                    // 交易商描述
}

func (x *TraderItem) Reset() {
	*x = TraderItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraderItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraderItem) ProtoMessage() {}

func (x *TraderItem) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TraderItem.ProtoReflect.Descriptor instead.
func (*TraderItem) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *TraderItem) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *TraderItem) GetCompany() string {
	if x != nil {
		return x.Company
	}
	return ""
}

func (x *TraderItem) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TraderItem) GetFoundDate() string {
	if x != nil {
		return x.FoundDate
	}
	return ""
}

func (x *TraderItem) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *TraderItem) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *TraderItem) GetWebsites() string {
	if x != nil {
		return x.Websites
	}
	return ""
}

func (x *TraderItem) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *TraderItem) GetAreaName() string {
	if x != nil {
		return x.AreaName
	}
	return ""
}

func (x *TraderItem) GetScore() float32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *TraderItem) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *TraderItem) GetLicenseName() string {
	if x != nil {
		return x.LicenseName
	}
	return ""
}

func (x *TraderItem) GetRegulatoryScore() float32 {
	if x != nil {
		return x.RegulatoryScore
	}
	return 0
}

func (x *TraderItem) GetBusinessScore() float32 {
	if x != nil {
		return x.BusinessScore
	}
	return 0
}

func (x *TraderItem) GetRiskScore() float32 {
	if x != nil {
		return x.RiskScore
	}
	return 0
}

func (x *TraderItem) GetSoftwareScore() float32 {
	if x != nil {
		return x.SoftwareScore
	}
	return 0
}

func (x *TraderItem) GetLicenseScore() float32 {
	if x != nil {
		return x.LicenseScore
	}
	return 0
}

func (x *TraderItem) GetMt4StatusName() string {
	if x != nil {
		return x.Mt4StatusName
	}
	return ""
}

func (x *TraderItem) GetMt5StatusName() string {
	if x != nil {
		return x.Mt5StatusName
	}
	return ""
}

func (x *TraderItem) GetInfluenceClass() string {
	if x != nil {
		return x.InfluenceClass
	}
	return ""
}

func (x *TraderItem) GetTradeEnvironment() string {
	if x != nil {
		return x.TradeEnvironment
	}
	return ""
}

func (x *TraderItem) GetWikifxWebsiteUrl() string {
	if x != nil {
		return x.WikifxWebsiteUrl
	}
	return ""
}

func (x *TraderItem) GetIsEpc() bool {
	if x != nil {
		return x.IsEpc
	}
	return false
}

func (x *TraderItem) GetNotEpcScore() float32 {
	if x != nil {
		return x.NotEpcScore
	}
	return 0
}

func (x *TraderItem) GetLevel1EpcScore() float32 {
	if x != nil {
		return x.Level1EpcScore
	}
	return 0
}

func (x *TraderItem) GetLevel2EpcScore() float32 {
	if x != nil {
		return x.Level2EpcScore
	}
	return 0
}

func (x *TraderItem) GetLevel3EpcScore() float32 {
	if x != nil {
		return x.Level3EpcScore
	}
	return 0
}

func (x *TraderItem) GetLevel4EpcScore() float32 {
	if x != nil {
		return x.Level4EpcScore
	}
	return 0
}

func (x *TraderItem) GetLevel5EpcScore() float32 {
	if x != nil {
		return x.Level5EpcScore
	}
	return 0
}

func (x *TraderItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type SearchTradersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content     string   `protobuf:"bytes,1,opt,name=content,json=content,proto3" json:"content"`
	CountryCode string   `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	Asc         int64    `protobuf:"varint,3,opt,name=asc,json=asc,proto3" json:"asc"`
	Size        int64    `protobuf:"varint,4,opt,name=size,json=size,proto3" json:"size"`
	Fields      []string `protobuf:"bytes,5,rep,name=fields,json=fields,proto3" json:"fields"`
	Language    string   `protobuf:"bytes,6,opt,name=language,json=language,proto3" json:"language"`
	Page        int64    `protobuf:"varint,7,opt,name=page,json=page,proto3" json:"page"`
}

func (x *SearchTradersRequest) Reset() {
	*x = SearchTradersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTradersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTradersRequest) ProtoMessage() {}

func (x *SearchTradersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTradersRequest.ProtoReflect.Descriptor instead.
func (*SearchTradersRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *SearchTradersRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SearchTradersRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *SearchTradersRequest) GetAsc() int64 {
	if x != nil {
		return x.Asc
	}
	return 0
}

func (x *SearchTradersRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *SearchTradersRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *SearchTradersRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *SearchTradersRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type SearchTradersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text string `protobuf:"bytes,1,opt,name=text,json=text,proto3" json:"text"`
}

func (x *SearchTradersReply) Reset() {
	*x = SearchTradersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchTradersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTradersReply) ProtoMessage() {}

func (x *SearchTradersReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTradersReply.ProtoReflect.Descriptor instead.
func (*SearchTradersReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *SearchTradersReply) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type TradeRecommendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinScore       int64    `protobuf:"varint,1,opt,name=min_score,json=minScore,proto3" json:"min_score"`
	MaxScore       int64    `protobuf:"varint,2,opt,name=max_score,json=maxScore,proto3" json:"max_score"`
	CountryCode    string   `protobuf:"bytes,3,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	LicenseName    []string `protobuf:"bytes,4,rep,name=license_name,json=licenseName,proto3" json:"license_name"`
	InfluenceClass []string `protobuf:"bytes,5,rep,name=influence_class,json=influenceClass,proto3" json:"influence_class"`
	Asc            int64    `protobuf:"varint,6,opt,name=asc,json=asc,proto3" json:"asc"`
	Size           int64    `protobuf:"varint,7,opt,name=size,json=size,proto3" json:"size"`
	Fields         []string `protobuf:"bytes,8,rep,name=fields,json=fields,proto3" json:"fields"`
	Language       string   `protobuf:"bytes,9,opt,name=language,json=language,proto3" json:"language"`
	Page           int64    `protobuf:"varint,10,opt,name=page,json=page,proto3" json:"page"`
}

func (x *TradeRecommendRequest) Reset() {
	*x = TradeRecommendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TradeRecommendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TradeRecommendRequest) ProtoMessage() {}

func (x *TradeRecommendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TradeRecommendRequest.ProtoReflect.Descriptor instead.
func (*TradeRecommendRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{13}
}

func (x *TradeRecommendRequest) GetMinScore() int64 {
	if x != nil {
		return x.MinScore
	}
	return 0
}

func (x *TradeRecommendRequest) GetMaxScore() int64 {
	if x != nil {
		return x.MaxScore
	}
	return 0
}

func (x *TradeRecommendRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *TradeRecommendRequest) GetLicenseName() []string {
	if x != nil {
		return x.LicenseName
	}
	return nil
}

func (x *TradeRecommendRequest) GetInfluenceClass() []string {
	if x != nil {
		return x.InfluenceClass
	}
	return nil
}

func (x *TradeRecommendRequest) GetAsc() int64 {
	if x != nil {
		return x.Asc
	}
	return 0
}

func (x *TradeRecommendRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TradeRecommendRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *TradeRecommendRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *TradeRecommendRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

type TradeRecommendReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text string `protobuf:"bytes,1,opt,name=text,json=text,proto3" json:"text"`
}

func (x *TradeRecommendReply) Reset() {
	*x = TradeRecommendReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TradeRecommendReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TradeRecommendReply) ProtoMessage() {}

func (x *TradeRecommendReply) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TradeRecommendReply.ProtoReflect.Descriptor instead.
func (*TradeRecommendReply) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *TradeRecommendReply) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type RobotActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action   ActionMode `protobuf:"varint,1,opt,name=action,json=action,proto3,enum=api.wiki_ai.v1.ActionMode" json:"action"` // 行为
	PostsIds []string   `protobuf:"bytes,2,rep,name=posts_ids,json=postsIds,proto3" json:"posts_ids"`                         // 帖子ID
	UserIds  []string   `protobuf:"bytes,3,rep,name=user_ids,json=userIds,proto3" json:"user_ids"`                            // 当action为3时需要传递要关注的人的ID
	Min      int64      `protobuf:"varint,4,opt,name=min,json=min,proto3" json:"min"`                                         // 最小次数
	Max      int64      `protobuf:"varint,5,opt,name=max,json=max,proto3" json:"max"`                                         // 最大次数
}

func (x *RobotActionRequest) Reset() {
	*x = RobotActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotActionRequest) ProtoMessage() {}

func (x *RobotActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotActionRequest.ProtoReflect.Descriptor instead.
func (*RobotActionRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *RobotActionRequest) GetAction() ActionMode {
	if x != nil {
		return x.Action
	}
	return ActionMode_ActionModeComment
}

func (x *RobotActionRequest) GetPostsIds() []string {
	if x != nil {
		return x.PostsIds
	}
	return nil
}

func (x *RobotActionRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

func (x *RobotActionRequest) GetMin() int64 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *RobotActionRequest) GetMax() int64 {
	if x != nil {
		return x.Max
	}
	return 0
}

type RobotActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"` // 任务ID
}

func (x *RobotActionResponse) Reset() {
	*x = RobotActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotActionResponse) ProtoMessage() {}

func (x *RobotActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotActionResponse.ProtoReflect.Descriptor instead.
func (*RobotActionResponse) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *RobotActionResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ======================================= 内容审核 ===========================================
type ImageCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizType string   `protobuf:"bytes,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type"`
	Scenes  []string `protobuf:"bytes,2,rep,name=scenes,json=scenes,proto3" json:"scenes"`
	Urls    []string `protobuf:"bytes,3,rep,name=urls,json=urls,proto3" json:"urls"`
}

func (x *ImageCheck) Reset() {
	*x = ImageCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCheck) ProtoMessage() {}

func (x *ImageCheck) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCheck.ProtoReflect.Descriptor instead.
func (*ImageCheck) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{17}
}

func (x *ImageCheck) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *ImageCheck) GetScenes() []string {
	if x != nil {
		return x.Scenes
	}
	return nil
}

func (x *ImageCheck) GetUrls() []string {
	if x != nil {
		return x.Urls
	}
	return nil
}

type TextCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BizType string   `protobuf:"bytes,1,opt,name=biz_type,json=bizType,proto3" json:"biz_type"`
	Text    []string `protobuf:"bytes,2,rep,name=text,json=text,proto3" json:"text"`
}

func (x *TextCheck) Reset() {
	*x = TextCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextCheck) ProtoMessage() {}

func (x *TextCheck) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextCheck.ProtoReflect.Descriptor instead.
func (*TextCheck) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *TextCheck) GetBizType() string {
	if x != nil {
		return x.BizType
	}
	return ""
}

func (x *TextCheck) GetText() []string {
	if x != nil {
		return x.Text
	}
	return nil
}

type ContentCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image *ImageCheck `protobuf:"bytes,1,opt,name=image,json=image,proto3" json:"image"`
	Text  *TextCheck  `protobuf:"bytes,2,opt,name=text,json=text,proto3" json:"text"`
}

func (x *ContentCheckRequest) Reset() {
	*x = ContentCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentCheckRequest) ProtoMessage() {}

func (x *ContentCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentCheckRequest.ProtoReflect.Descriptor instead.
func (*ContentCheckRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *ContentCheckRequest) GetImage() *ImageCheck {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *ContentCheckRequest) GetText() *TextCheck {
	if x != nil {
		return x.Text
	}
	return nil
}

type ContentCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pass            bool     `protobuf:"varint,1,opt,name=pass,json=pass,proto3" json:"pass"`
	Reason          string   `protobuf:"bytes,2,opt,name=reason,json=reason,proto3" json:"reason"`
	FilteredContent []string `protobuf:"bytes,3,rep,name=filtered_content,json=filteredContent,proto3" json:"filtered_content"`
	DataIds         []string `protobuf:"bytes,4,rep,name=data_ids,json=dataIds,proto3" json:"data_ids"`
}

func (x *ContentCheckResponse) Reset() {
	*x = ContentCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContentCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentCheckResponse) ProtoMessage() {}

func (x *ContentCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentCheckResponse.ProtoReflect.Descriptor instead.
func (*ContentCheckResponse) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{20}
}

func (x *ContentCheckResponse) GetPass() bool {
	if x != nil {
		return x.Pass
	}
	return false
}

func (x *ContentCheckResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ContentCheckResponse) GetFilteredContent() []string {
	if x != nil {
		return x.FilteredContent
	}
	return nil
}

func (x *ContentCheckResponse) GetDataIds() []string {
	if x != nil {
		return x.DataIds
	}
	return nil
}

type FindAsyncTranslateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperationId string `protobuf:"bytes,1,opt,name=operation_id,json=operationId,proto3" json:"operation_id"`
	To          string `protobuf:"bytes,2,opt,name=to,json=to,proto3" json:"to"`
	Size        int64  `protobuf:"varint,3,opt,name=size,json=size,proto3" json:"size"`
}

func (x *FindAsyncTranslateRequest) Reset() {
	*x = FindAsyncTranslateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindAsyncTranslateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindAsyncTranslateRequest) ProtoMessage() {}

func (x *FindAsyncTranslateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindAsyncTranslateRequest.ProtoReflect.Descriptor instead.
func (*FindAsyncTranslateRequest) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *FindAsyncTranslateRequest) GetOperationId() string {
	if x != nil {
		return x.OperationId
	}
	return ""
}

func (x *FindAsyncTranslateRequest) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *FindAsyncTranslateRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type FindAsyncTranslateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*AsyncTranslateInfo `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *FindAsyncTranslateResponse) Reset() {
	*x = FindAsyncTranslateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ai_v1_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FindAsyncTranslateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindAsyncTranslateResponse) ProtoMessage() {}

func (x *FindAsyncTranslateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ai_v1_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindAsyncTranslateResponse.ProtoReflect.Descriptor instead.
func (*FindAsyncTranslateResponse) Descriptor() ([]byte, []int) {
	return file_ai_v1_service_proto_rawDescGZIP(), []int{22}
}

func (x *FindAsyncTranslateResponse) GetItems() []*AsyncTranslateInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_ai_v1_service_proto protoreflect.FileDescriptor

var file_ai_v1_service_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f,
	0x61, 0x69, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x42, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41,
	0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5,
	0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0xf7, 0x01, 0x0a,
	0x0e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12,
	0x28, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xaf, 0xb7, 0xe6, 0xb1, 0x82, 0xe4, 0xbd, 0x93,
	0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x08, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe5, 0x93, 0x8d, 0xe5, 0xba, 0x94, 0xe4, 0xbd,
	0x93, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0xe7, 0xa0, 0x81, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe6, 0x97, 0xb6,
	0xe9, 0x97, 0xb4, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc9, 0x09, 0x0a, 0x12, 0x41, 0x73, 0x79, 0x6e, 0x63,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91,
	0xe5, 0x90, 0x8e, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85, 0xe5,
	0xae, 0xb9, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x54, 0x92, 0x41, 0x51, 0x2a,
	0x4f, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe7, 0xad,
	0x89, 0xe5, 0xbe, 0x85, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc,
	0x9a, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe4, 0xb8, 0xad, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc,
	0x9a, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0xef, 0xbc, 0x9b,
	0x33, 0xef, 0xbc, 0x9a, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0xa4, 0xb1, 0xe8, 0xb4, 0xa5,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0xa4, 0xb1, 0xe8, 0xb4, 0xa5, 0xe5, 0x8e, 0x9f, 0xe5, 0x9b, 0xa0, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88,
	0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf,
	0x91, 0xe9, 0x87, 0x8d, 0xe8, 0xaf, 0x95, 0xe6, 0xac, 0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x12, 0x33, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a,
	0x0b, 0xe4, 0xb8, 0x9a, 0xe5, 0x8a, 0xa1, 0xe6, 0x96, 0xb9, 0x49, 0x44, 0x52, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe9, 0x99, 0x84, 0xe5, 0x8a, 0xa0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x06, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x12, 0x38, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85, 0xe5,
	0xae, 0xb9, 0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x5c,
	0x0a, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0x92, 0x41, 0x36, 0x2a, 0x34, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0,
	0x83, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xef, 0xbc, 0x9b, 0xe6, 0xb3, 0xa8, 0xe6, 0x84, 0x8f,
	0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe5, 0xbf, 0x85, 0xe9, 0xa1, 0xbb, 0xe4, 0xbd, 0xbf, 0xe7,
	0x94, 0xa8, 0x70, 0x6f, 0x73, 0x74, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x52,
	0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x46, 0x0a, 0x07,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x9b, 0x9e, 0xe8,
	0xb0, 0x83, 0xe8, 0xaf, 0xb7, 0xe6, 0xb1, 0x82, 0xe5, 0xa4, 0xb4, 0x52, 0x07, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x9c, 0x01, 0x0a, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x4a, 0x92, 0x41, 0x47, 0x2a, 0x45, 0xe5, 0x9b,
	0x9e, 0xe8, 0xb0, 0x83, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc,
	0x9a, 0xe7, 0xad, 0x89, 0xe5, 0xbe, 0x85, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xef, 0xbc, 0x9b,
	0x31, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f,
	0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe5, 0xa4, 0xb1,
	0xe8, 0xb4, 0xa5, 0x52, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3e, 0x0a, 0x0e, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe9, 0x87, 0x8d, 0xe8, 0xaf, 0x95, 0xe6, 0xac,
	0xa1, 0xe6, 0x95, 0xb0, 0x52, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x12, 0x5c, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95,
	0x52, 0x0f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x12, 0x41, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe8, 0xaf,
	0xad, 0xe8, 0xa8, 0x80, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x4c, 0x61, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x10, 0x20,
	0x03, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93,
	0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2a,
	0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xb8, 0x8d, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x06, 0x70, 0x72,
	0x6f, 0x6d, 0x70, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0xe8, 0xaf, 0x8d, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x22, 0x30, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0xfd, 0x05, 0x0a, 0x15, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41,
	0x1a, 0x2a, 0x18, 0xe8, 0xa6, 0x81, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe7, 0x9a, 0x84, 0xe6,
	0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x04, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x25, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0x92,
	0x41, 0x12, 0x2a, 0x0c, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0x3a, 0x02, 0x65, 0x6e, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x82, 0x02, 0x0a, 0x0c, 0x63, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0xde, 0x01, 0x92, 0x41, 0xda, 0x01, 0x2a, 0x0c, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0xe5, 0x9c,
	0xb0, 0xe5, 0x9d, 0x80, 0x32, 0xc9, 0x01, 0xe6, 0xb3, 0xa8, 0xe6, 0x84, 0x8f, 0xe5, 0x9b, 0x9e,
	0xe8, 0xb0, 0x83, 0xe5, 0xbf, 0x85, 0xe9, 0xa1, 0xbb, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0x70,
	0x6f, 0x73, 0x74, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0xef, 0xbc, 0x9b, 0xe5,
	0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0x62, 0x6f, 0x64, 0x79, 0x3a, 0x7b, 0x22, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x22, 0x3a, 0x22, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x22, 0x2c,
	0x22, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x22, 0x3a, 0x5b, 0x22,
	0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0x22, 0x5d, 0x2c, 0x22,
	0x74, 0x65, 0x78, 0x74, 0x22, 0x3a, 0x5b, 0x22, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe4, 0xb9,
	0x8b, 0xe5, 0x90, 0x8e, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0x22, 0x5d, 0x2c,
	0x22, 0x74, 0x6f, 0x22, 0x3a, 0x22, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0xad, 0xe8,
	0xa8, 0x80, 0x22, 0x2c, 0x22, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x22, 0x3a, 0x22, 0xe9, 0x99,
	0x84, 0xe4, 0xbb, 0xb6, 0x22, 0x2c, 0x22, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x22, 0x3a, 0x22, 0xe4, 0xb8, 0x9a, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x22, 0x7d,
	0x52, 0x0b, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x46, 0x0a,
	0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x9b, 0x9e,
	0xe8, 0xb0, 0x83, 0xe8, 0xaf, 0xb7, 0xe6, 0xb1, 0x82, 0xe5, 0xa4, 0xb4, 0x52, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x33, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10, 0x92, 0x41, 0x0d,
	0x2a, 0x0b, 0xe4, 0xb8, 0x9a, 0xe5, 0x8a, 0xa1, 0xe6, 0x96, 0xb9, 0x49, 0x44, 0x52, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x06, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe9, 0x99, 0x84, 0xe5, 0x8a, 0xa0, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x06, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98,
	0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x12,
	0x2a, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14,
	0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xb8, 0x8d, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0x90,
	0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x70,
	0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a,
	0x2a, 0x18, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe9, 0xa2, 0x9d, 0xe5, 0xa4,
	0x96, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0xe8, 0xaf, 0xad, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x12, 0x50, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x38, 0x92, 0x41, 0x35, 0x2a, 0x33, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe6,
	0x96, 0xb9, 0xe6, 0xb3, 0x95, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x4c, 0x4c,
	0x4d, 0xef, 0xbc, 0x8c, 0xe5, 0x8f, 0xaf, 0xe9, 0x80, 0x89, 0xe5, 0x80, 0xbc, 0xef, 0xbc, 0x9a,
	0x4c, 0x4c, 0x4d, 0xe3, 0x80, 0x81, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x06, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x22, 0x3d, 0x0a, 0x13, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x22, 0xfd, 0x02, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0xa6, 0x81,
	0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5,
	0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x25, 0x0a, 0x02, 0x74,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x0c, 0xe7, 0x9b,
	0xae, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x3a, 0x02, 0x65, 0x6e, 0x52, 0x02,
	0x74, 0x6f, 0x12, 0x28, 0x0a, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x12, 0x92, 0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95,
	0x86, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x05,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe4, 0xb8, 0x8d, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x70, 0x72, 0x6f, 0x6d,
	0x70, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8,
	0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe9, 0xa2, 0x9d, 0xe5, 0xa4, 0x96, 0xe6, 0x8f,
	0x90, 0xe7, 0xa4, 0xba, 0xe8, 0xaf, 0xad, 0x52, 0x06, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12,
	0x50, 0x0a, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x38, 0x92, 0x41, 0x35, 0x2a, 0x33, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe6, 0x96, 0xb9, 0xe6,
	0xb3, 0x95, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x4c, 0x4c, 0x4d, 0xef, 0xbc,
	0x8c, 0xe5, 0x8f, 0xaf, 0xe9, 0x80, 0x89, 0xe5, 0x80, 0xbc, 0xef, 0xbc, 0x9a, 0x4c, 0x4c, 0x4d,
	0xe3, 0x80, 0x81, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x06, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x30, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe6, 0x93,
	0x8d, 0xe4, 0xbd, 0x9c, 0x49, 0x44, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0xc0, 0x01, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf,
	0x91, 0xe5, 0x90, 0x8e, 0xe7, 0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x0b, 0x6f, 0x72, 0x69,
	0x67, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe6, 0x96, 0x87, 0xe6, 0x9c,
	0xac, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x41, 0x0a, 0x10, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac,
	0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x0e, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x22, 0xbe, 0x02, 0x0a, 0x16, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0xa6, 0x81, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe7,
	0x9a, 0x84, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x12, 0x25, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x0c, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0xad,
	0xe8, 0xa8, 0x80, 0x3a, 0x02, 0x65, 0x6e, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x4e, 0x0a, 0x0a, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2f, 0x92, 0x41, 0x2c, 0x2a, 0x2a, 0xe6, 0xa8, 0xa1, 0xe5, 0x9e, 0x8b, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0xef, 0xbc, 0x9b, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xef, 0xbc, 0x9a, 0x64, 0x65,
	0x65, 0x70, 0x73, 0x65, 0x65, 0x6b, 0x2d, 0x76, 0x33, 0x2d, 0x32, 0x35, 0x30, 0x33, 0x32, 0x34,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0d, 0x73,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4,
	0xb9, 0x89, 0xe7, 0xb3, 0xbb, 0xe7, 0xbb, 0x9f, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0xe8, 0xaf,
	0xad, 0x52, 0x0c, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12,
	0x36, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x9c, 0x80, 0xe5, 0xa4, 0xa7,
	0xe9, 0x99, 0x90, 0xe5, 0x88, 0xb6, 0xe9, 0x95, 0xbf, 0xe5, 0xba, 0xa6, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x49, 0x0a, 0x14, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x31, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1d, 0x92,
	0x41, 0x1a, 0x2a, 0x18, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0x90, 0x8e, 0xe7, 0x9a, 0x84,
	0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x22, 0xa7, 0x08, 0x0a, 0x0a, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x6f, 0x75, 0x6e,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68,
	0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x77, 0x65, 0x62, 0x73, 0x69,
	0x74, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x77, 0x65, 0x62, 0x73, 0x69,
	0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10,
	0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x72, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x53,
	0x63, 0x6f, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x6c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x74, 0x34,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x74, 0x34, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x74, 0x35, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x74, 0x35, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x66,
	0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x69,
	0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74,
	0x72, 0x61, 0x64, 0x65, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0x5f, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74,
	0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x77, 0x69, 0x6b,
	0x69, 0x66, 0x78, 0x57, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x73, 0x5f, 0x65, 0x70, 0x63, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69,
	0x73, 0x45, 0x70, 0x63, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x6f, 0x74, 0x5f, 0x65, 0x70, 0x63, 0x5f,
	0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x6e, 0x6f, 0x74,
	0x45, 0x70, 0x63, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x31, 0x5f, 0x65, 0x70, 0x63, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x0e, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x31, 0x45, 0x70, 0x63, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x5f, 0x65, 0x70, 0x63,
	0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x32, 0x45, 0x70, 0x63, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x5f, 0x65, 0x70, 0x63, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x33, 0x45, 0x70,
	0x63, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34,
	0x5f, 0x65, 0x70, 0x63, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x0e, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x34, 0x45, 0x70, 0x63, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x28, 0x0a, 0x10, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x35, 0x5f, 0x65, 0x70, 0x63, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x35, 0x45, 0x70, 0x63, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe3, 0x04, 0x0a,
	0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2c, 0x92, 0x41, 0x29, 0x2a, 0x27, 0xe4, 0xba, 0xa4,
	0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0xe3, 0x80, 0x81, 0xe7,
	0xbd, 0x91, 0xe5, 0x9d, 0x80, 0xe3, 0x80, 0x81, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0xe5, 0x90,
	0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x46, 0x0a,
	0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6,
	0xe4, 0xba, 0x8c, 0xe5, 0xad, 0x97, 0xe7, 0xa0, 0x81, 0xe6, 0x88, 0x96, 0xe8, 0x80, 0x85, 0xe4,
	0xb8, 0x89, 0xe5, 0xad, 0x97, 0xe7, 0xa0, 0x81, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x51, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x3f, 0x92, 0x41, 0x3c, 0x2a, 0x33, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5,
	0x8d, 0x87, 0xe5, 0xba, 0x8f, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0xef, 0xbc, 0x8c, 0xe9, 0xbb,
	0x98, 0xe8, 0xae, 0xa4, 0xe6, 0x8c, 0x89, 0xe7, 0x85, 0xa7, 0xe5, 0x88, 0x86, 0xe6, 0x95, 0xb0,
	0xe5, 0x80, 0x92, 0xe5, 0xba, 0x8f, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0x3a, 0x05, 0x66, 0x61,
	0x6c, 0x73, 0x65, 0x52, 0x03, 0x61, 0x73, 0x63, 0x12, 0x3b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x27, 0x92, 0x41, 0x24, 0x2a, 0x1f, 0xe8, 0xbf, 0x94,
	0xe5, 0x9b, 0x9e, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x87, 0x8f, 0xe5, 0xa4, 0xa7, 0xe5,
	0xb0, 0x8f, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0x35, 0x3a, 0x01, 0x35, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0xc4, 0x01, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0xab, 0x01, 0x92, 0x41, 0xa7, 0x01, 0x2a, 0xa4, 0x01,
	0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe7, 0x9a, 0x84, 0xe4,
	0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x3a, 0xef,
	0xbc, 0x88, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0xe7, 0xae,
	0x80, 0xe4, 0xbb, 0x8b, 0xef, 0xbc, 0x9b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x3a, 0xe6,
	0x96, 0x87, 0xe7, 0xab, 0xa0, 0xef, 0xbc, 0x9b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x3a,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xef, 0xbc, 0x9b, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x3a, 0xe7, 0xae, 0x80, 0xe7, 0xa7, 0xb0, 0xef, 0xbc, 0x9b, 0x77, 0x65, 0x62,
	0x73, 0x69, 0x74, 0x65, 0x73, 0x3a, 0xe7, 0xbd, 0x91, 0xe5, 0x9d, 0x80, 0xef, 0xbc, 0x9b, 0x6c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0xe7, 0x89, 0x8c, 0xe7,
	0x85, 0xa7, 0xef, 0xbc, 0x9b, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x3a, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0xef, 0xbc, 0x89, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x40, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24,
	0x92, 0x41, 0x21, 0x2a, 0x18, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x89, 0x80, 0xe4, 0xbd,
	0xbf, 0xe7, 0x94, 0xa8, 0xe7, 0x9a, 0x84, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x3a, 0x05, 0x7a,
	0x68, 0x2d, 0x63, 0x6e, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x22,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x3a, 0x01, 0x31, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0x28, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0xce, 0x05, 0x0a,
	0x15, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x9c, 0x80, 0xe5, 0xb0, 0x8f, 0xe5, 0x88, 0x86, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x6d, 0x69,
	0x6e, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x9c, 0x80, 0xe5, 0xa4, 0xa7, 0xe5, 0x88, 0x86, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x6d, 0x61,
	0x78, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe6, 0x89, 0x80, 0xe5, 0xb1, 0x9e, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x0c,
	0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0x8c, 0xe7, 0x85, 0xa7, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0b, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe5, 0xbd, 0xb1, 0xe5, 0x93, 0x8d, 0xe5, 0x8a, 0x9b, 0xe7, 0xad, 0x89, 0xe7, 0xba,
	0xa7, 0x52, 0x0e, 0x69, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x12, 0x51, 0x0a, 0x03, 0x61, 0x73, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x42, 0x3f,
	0x92, 0x41, 0x3c, 0x2a, 0x33, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8d, 0x87, 0xe5, 0xba,
	0x8f, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0xef, 0xbc, 0x8c, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4,
	0xe6, 0x8c, 0x89, 0xe7, 0x85, 0xa7, 0xe5, 0x88, 0x86, 0xe6, 0x95, 0xb0, 0xe5, 0x80, 0x92, 0xe5,
	0xba, 0x8f, 0xe6, 0x8e, 0x92, 0xe5, 0xba, 0x8f, 0x3a, 0x05, 0x66, 0x61, 0x6c, 0x73, 0x65, 0x52,
	0x03, 0x61, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x15, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe6,
	0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe9, 0x87, 0x8f, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x3a, 0x01,
	0x35, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0xc4, 0x01, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0xab, 0x01, 0x92, 0x41, 0xa7, 0x01, 0x2a,
	0xa4, 0x01, 0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe7, 0x9a,
	0x84, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5,
	0x3a, 0xef, 0xbc, 0x88, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a,
	0xe7, 0xae, 0x80, 0xe4, 0xbb, 0x8b, 0xef, 0xbc, 0x9b, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x3a, 0xe6, 0x96, 0x87, 0xe7, 0xab, 0xa0, 0xef, 0xbc, 0x9b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x3a, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xef, 0xbc, 0x9b, 0x73, 0x68, 0x6f, 0x72, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0xe7, 0xae, 0x80, 0xe7, 0xa7, 0xb0, 0xef, 0xbc, 0x9b, 0x77,
	0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x73, 0x3a, 0xe7, 0xbd, 0x91, 0xe5, 0x9d, 0x80, 0xef, 0xbc,
	0x9b, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3a, 0xe7, 0x89,
	0x8c, 0xe7, 0x85, 0xa7, 0xef, 0xbc, 0x9b, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x3a, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0xef, 0xbc, 0x89, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x39,
	0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x89, 0x80,
	0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe7, 0x9a, 0x84, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x06, 0xe9, 0xa1,
	0xb5, 0xe7, 0xa0, 0x81, 0x3a, 0x01, 0x31, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x29, 0x0a,
	0x13, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0xa4, 0x01, 0x0a, 0x12, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x32, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x69, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x61, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x61, 0x78, 0x22,
	0x25, 0x0a, 0x13, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xde, 0x02, 0x0a, 0x0a, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xb8,
	0x9a, 0xe5, 0x8a, 0xa1, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0x52, 0x07, 0x62, 0x69, 0x7a, 0x54,
	0x79, 0x70, 0x65, 0x12, 0xfa, 0x01, 0x0a, 0x06, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x42, 0xe1, 0x01, 0x92, 0x41, 0xdd, 0x01, 0x2a, 0xda, 0x01, 0xe6, 0x8c,
	0x87, 0xe5, 0xae, 0x9a, 0xe6, 0xa3, 0x80, 0xe6, 0xb5, 0x8b, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf,
	0x3a, 0x70, 0x6f, 0x72, 0x6e, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x99,
	0xba, 0xe8, 0x83, 0xbd, 0xe9, 0x89, 0xb4, 0xe9, 0xbb, 0x84, 0x3b, 0x74, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x69, 0x73, 0x6d, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe6, 0x9a, 0xb4,
	0xe6, 0x81, 0x90, 0xe6, 0xb6, 0x89, 0xe6, 0x94, 0xbf, 0x3b, 0x61, 0x64, 0xef, 0xbc, 0x9a, 0xe5,
	0x9b, 0xbe, 0xe6, 0x96, 0x87, 0xe8, 0xbf, 0x9d, 0xe8, 0xa7, 0x84, 0x3b, 0x71, 0x72, 0x63, 0x6f,
	0x64, 0x65, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe4, 0xba, 0x8c, 0xe7, 0xbb,
	0xb4, 0xe7, 0xa0, 0x81, 0x3b, 0x6c, 0x69, 0x76, 0x65, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7,
	0x89, 0x87, 0xe4, 0xb8, 0x8d, 0xe8, 0x89, 0xaf, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0x3b, 0x6c,
	0x6f, 0x67, 0x6f, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x6c, 0x6f, 0x67, 0x6f,
	0x3b, 0xe6, 0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe6, 0x8c, 0x87, 0xe5, 0xae, 0x9a, 0xe5, 0xa4, 0x9a,
	0xe4, 0xb8, 0xaa, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0x3b, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4,
	0xef, 0xbc, 0x9a, 0x70, 0x6f, 0x72, 0x6e, 0xef, 0xbc, 0x8c, 0x74, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x69, 0x73, 0x6d, 0xef, 0xbc, 0x8c, 0x61, 0x64, 0x52, 0x06, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x73,
	0x12, 0x25, 0x0a, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d,
	0x80, 0x52, 0x04, 0x75, 0x72, 0x6c, 0x73, 0x22, 0x60, 0x0a, 0x09, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x12, 0x2c, 0x0a, 0x08, 0x62, 0x69, 0x7a, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xb8, 0x9a,
	0xe5, 0x8a, 0xa1, 0xe5, 0x9c, 0xba, 0xe6, 0x99, 0xaf, 0x52, 0x07, 0x62, 0x69, 0x7a, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0x90, 0x01, 0x0a, 0x13, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3d, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x3a, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x22, 0xef, 0x01, 0x0a,
	0x14, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x61, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe9, 0x80, 0x9a, 0xe8, 0xbf, 0x87, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0x52, 0x04, 0x70, 0x61,
	0x73, 0x73, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe4, 0xb8, 0x8d, 0xe9, 0x80, 0x9a, 0xe8,
	0xbf, 0x87, 0xe5, 0x8e, 0x9f, 0xe5, 0x9b, 0xa0, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x4c, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x21, 0x92, 0x41, 0x1e, 0x2a,
	0x1c, 0xe5, 0xaf, 0xb9, 0xe5, 0x8e, 0x9f, 0xe5, 0xa7, 0x8b, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac,
	0xe8, 0xbf, 0x9b, 0xe8, 0xa1, 0x8c, 0x2a, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0x52, 0x0f, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2e,
	0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x13, 0x92, 0x41, 0x10, 0x2a, 0x0e, 0xe6, 0xa3, 0x80, 0xe6, 0xb5, 0x8b, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0x49, 0x44, 0x52, 0x07, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x73, 0x22, 0x9a,
	0x01, 0x0a, 0x19, 0x46, 0x69, 0x6e, 0x64, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x0c,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x49,
	0x44, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x02, 0x74,
	0x6f, 0x12, 0x28, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x9a, 0x84, 0xe6,
	0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x56, 0x0a, 0x1a, 0x46,
	0x69, 0x6e, 0x64, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x2a, 0x83, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x61, 0x69, 0x74, 0x69, 0x6e,
	0x67, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x02, 0x12, 0x19,
	0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x03, 0x2a, 0x84, 0x01, 0x0a, 0x17, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x57, 0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x21, 0x0a,
	0x1d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02,
	0x2a, 0x7b, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x15,
	0x0a, 0x11, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x4c, 0x69, 0x6b, 0x65, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x10, 0x02,
	0x12, 0x17, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x41, 0x74,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x56, 0x69, 0x65, 0x77, 0x10, 0x04, 0x32, 0xcb, 0x0f,
	0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x79, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x7a, 0x12, 0xa0, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x73, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f,
	0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x45,
	0x92, 0x41, 0x21, 0x0a, 0x02, 0x61, 0x69, 0x12, 0x1b, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5,
	0x95, 0x86, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0xe6, 0x9f,
	0xa5, 0xe8, 0xaf, 0xa2, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f,
	0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xa2, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65,
	0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x43, 0x92, 0x41, 0x21, 0x0a, 0x02, 0x61, 0x69, 0x12, 0x1b,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x2f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0xcd, 0x01, 0x0a, 0x0b, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x75, 0x92, 0x41, 0x5e, 0x0a, 0x02, 0x61, 0x69, 0x12, 0x58, 0xe6, 0x9c,
	0xba, 0xe5, 0x99, 0xa8, 0xe4, 0xba, 0xba, 0xe8, 0xa1, 0x8c, 0xe4, 0xb8, 0xba, 0x3a, 0x30, 0x3a,
	0xe8, 0xaf, 0x84, 0xe4, 0xbb, 0xb7, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe7, 0x82, 0xb9,
	0xe8, 0xb5, 0x9e, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe6, 0x94, 0xb6, 0xe8, 0x97, 0x8f,
	0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0x85, 0xb3, 0xe6, 0xb3, 0xa8, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88,
	0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22,
	0x09, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0xab, 0x01, 0x0a, 0x0c, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x50, 0x92, 0x41, 0x34, 0x0a, 0x02, 0x61, 0x69, 0x12,
	0x12, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0xe8, 0xbf, 0x9d, 0xe8, 0xa7, 0x84, 0xe5, 0x88, 0x9d,
	0xe5, 0xae, 0xa1, 0x72, 0x1a, 0x0a, 0x18, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72,
	0x65, 0x65, 0x6e, 0x2f, 0x73, 0x63, 0x61, 0x6e, 0x12, 0xa5, 0x01, 0x0a, 0x09, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b,
	0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x56, 0x92, 0x41, 0x3b, 0x0a, 0x06, 0xe7,
	0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x12, 0x15, 0xe5, 0xa4, 0xa7, 0xe6, 0xa8, 0xa1, 0xe5, 0x9e, 0x8b,
	0xe6, 0x89, 0xb9, 0xe9, 0x87, 0x8f, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x72, 0x1a, 0x0a, 0x18,
	0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01,
	0x2a, 0x22, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0xc5, 0x01, 0x0a, 0x0e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x79,
	0x4c, 0x4c, 0x4d, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69,
	0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x71, 0x92, 0x41, 0x52, 0x0a, 0x06, 0xe7, 0xbf, 0xbb, 0xe8,
	0xaf, 0x91, 0x12, 0x2c, 0xe5, 0xa4, 0xa7, 0xe6, 0xa8, 0xa1, 0xe5, 0x9e, 0x8b, 0xe6, 0x89, 0xb9,
	0xe9, 0x87, 0x8f, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x28, 0xe8, 0xbf, 0x99, 0xe9, 0x87, 0x8c,
	0xe7, 0x9b, 0xb4, 0xe8, 0xb5, 0xb0, 0xe5, 0xa4, 0xa7, 0xe6, 0xa8, 0xa1, 0xe5, 0x9e, 0x8b, 0x29,
	0x72, 0x1a, 0x0a, 0x18, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x6c, 0x61, 0x74, 0x65, 0x2f, 0x6c, 0x6c, 0x6d, 0x12, 0xeb, 0x01, 0x0a, 0x0f, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f,
	0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x89, 0x01, 0x92, 0x41, 0x67,
	0x0a, 0x06, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x12, 0x41, 0xe5, 0xa4, 0xa7, 0xe6, 0xa8, 0xa1,
	0xe5, 0x9e, 0x8b, 0xe6, 0x89, 0xb9, 0xe9, 0x87, 0x8f, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x28,
	0xe7, 0xae, 0x80, 0xe5, 0x8d, 0x95, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xef, 0xbc, 0x8c, 0xe6,
	0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe9, 0xa2, 0x9d, 0xe5, 0xa4, 0x96, 0xe6, 0x8f, 0x90, 0xe7, 0xa4,
	0xba, 0xe8, 0xaf, 0x8d, 0xe9, 0x99, 0x90, 0xe5, 0x88, 0xb6, 0x29, 0x72, 0x1a, 0x0a, 0x18, 0x0a,
	0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a,
	0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x2f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x12, 0xba, 0x01, 0x0a, 0x0e, 0x41, 0x73, 0x79, 0x6e, 0x63,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x79, 0x6e, 0x63,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5c, 0x92, 0x41, 0x3b, 0x0a, 0x06, 0xe7, 0xbf, 0xbb, 0xe8,
	0xaf, 0x91, 0x12, 0x15, 0xe5, 0xa4, 0xa7, 0xe6, 0xa8, 0xa1, 0xe5, 0x9e, 0x8b, 0xe7, 0xbf, 0xbb,
	0xe8, 0xaf, 0x91, 0xe5, 0xbc, 0x82, 0xe6, 0xad, 0xa5, 0x72, 0x1a, 0x0a, 0x18, 0x0a, 0x0d, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x05, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x61, 0x73,
	0x79, 0x6e, 0x63, 0x12, 0xc2, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x73, 0x79, 0x6e, 0x63,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73,
	0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61,
	0x69, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5f, 0x92, 0x41, 0x41, 0x0a, 0x06, 0xe7, 0xbf,
	0xbb, 0xe8, 0xaf, 0x91, 0x12, 0x1b, 0xe5, 0xa4, 0xa7, 0xe6, 0xa8, 0xa1, 0xe5, 0x9e, 0x8b, 0xe5,
	0xbc, 0x82, 0xe6, 0xad, 0xa5, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf,
	0xa2, 0x72, 0x1a, 0x0a, 0x18, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61,
	0x74, 0x65, 0x2f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x12, 0xd0, 0x01, 0x0a, 0x12, 0x46, 0x69, 0x6e,
	0x64, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31,
	0x2e, 0x46, 0x69, 0x6e, 0x64, 0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x61, 0x69, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6e, 0x64,
	0x41, 0x73, 0x79, 0x6e, 0x63, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x63, 0x92, 0x41, 0x40, 0x0a, 0x06, 0xe7, 0xbf, 0xbb,
	0xe8, 0xaf, 0x91, 0x12, 0x1a, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe4, 0xb8, 0x9a, 0xe5, 0x8a,
	0xa1, 0x49, 0x44, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0x72,
	0x1a, 0x0a, 0x18, 0x0a, 0x0d, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x05, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65,
	0x2f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x2f, 0x66, 0x69, 0x6e, 0x64, 0x42, 0x0e, 0x5a, 0x0c, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x69, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_ai_v1_service_proto_rawDescOnce sync.Once
	file_ai_v1_service_proto_rawDescData = file_ai_v1_service_proto_rawDesc
)

func file_ai_v1_service_proto_rawDescGZIP() []byte {
	file_ai_v1_service_proto_rawDescOnce.Do(func() {
		file_ai_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_ai_v1_service_proto_rawDescData)
	})
	return file_ai_v1_service_proto_rawDescData
}

var file_ai_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_ai_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_ai_v1_service_proto_goTypes = []interface{}{
	(TranslateStatus)(0),               // 0: api.wiki_ai.v1.TranslateStatus
	(TranslateCallbackStatus)(0),       // 1: api.wiki_ai.v1.TranslateCallbackStatus
	(ActionMode)(0),                    // 2: api.wiki_ai.v1.ActionMode
	(*GetAsyncTranslateRequest)(nil),   // 3: api.wiki_ai.v1.GetAsyncTranslateRequest
	(*CallbackRecord)(nil),             // 4: api.wiki_ai.v1.CallbackRecord
	(*AsyncTranslateInfo)(nil),         // 5: api.wiki_ai.v1.AsyncTranslateInfo
	(*Header)(nil),                     // 6: api.wiki_ai.v1.Header
	(*AsyncTranslateRequest)(nil),      // 7: api.wiki_ai.v1.AsyncTranslateRequest
	(*AsyncTranslateReply)(nil),        // 8: api.wiki_ai.v1.AsyncTranslateReply
	(*TranslateRequest)(nil),           // 9: api.wiki_ai.v1.TranslateRequest
	(*TranslateReply)(nil),             // 10: api.wiki_ai.v1.TranslateReply
	(*TranslateCustomRequest)(nil),     // 11: api.wiki_ai.v1.TranslateCustomRequest
	(*TranslateCustomReply)(nil),       // 12: api.wiki_ai.v1.TranslateCustomReply
	(*TraderItem)(nil),                 // 13: api.wiki_ai.v1.TraderItem
	(*SearchTradersRequest)(nil),       // 14: api.wiki_ai.v1.SearchTradersRequest
	(*SearchTradersReply)(nil),         // 15: api.wiki_ai.v1.SearchTradersReply
	(*TradeRecommendRequest)(nil),      // 16: api.wiki_ai.v1.TradeRecommendRequest
	(*TradeRecommendReply)(nil),        // 17: api.wiki_ai.v1.TradeRecommendReply
	(*RobotActionRequest)(nil),         // 18: api.wiki_ai.v1.RobotActionRequest
	(*RobotActionResponse)(nil),        // 19: api.wiki_ai.v1.RobotActionResponse
	(*ImageCheck)(nil),                 // 20: api.wiki_ai.v1.ImageCheck
	(*TextCheck)(nil),                  // 21: api.wiki_ai.v1.TextCheck
	(*ContentCheckRequest)(nil),        // 22: api.wiki_ai.v1.ContentCheckRequest
	(*ContentCheckResponse)(nil),       // 23: api.wiki_ai.v1.ContentCheckResponse
	(*FindAsyncTranslateRequest)(nil),  // 24: api.wiki_ai.v1.FindAsyncTranslateRequest
	(*FindAsyncTranslateResponse)(nil), // 25: api.wiki_ai.v1.FindAsyncTranslateResponse
	(*common.EmptyRequest)(nil),        // 26: common.EmptyRequest
	(*common.HealthyReply)(nil),        // 27: common.HealthyReply
}
var file_ai_v1_service_proto_depIdxs = []int32{
	0,  // 0: api.wiki_ai.v1.AsyncTranslateInfo.status:type_name -> api.wiki_ai.v1.TranslateStatus
	6,  // 1: api.wiki_ai.v1.AsyncTranslateInfo.headers:type_name -> api.wiki_ai.v1.Header
	1,  // 2: api.wiki_ai.v1.AsyncTranslateInfo.callback_status:type_name -> api.wiki_ai.v1.TranslateCallbackStatus
	4,  // 3: api.wiki_ai.v1.AsyncTranslateInfo.callback_records:type_name -> api.wiki_ai.v1.CallbackRecord
	6,  // 4: api.wiki_ai.v1.AsyncTranslateRequest.headers:type_name -> api.wiki_ai.v1.Header
	2,  // 5: api.wiki_ai.v1.RobotActionRequest.action:type_name -> api.wiki_ai.v1.ActionMode
	20, // 6: api.wiki_ai.v1.ContentCheckRequest.image:type_name -> api.wiki_ai.v1.ImageCheck
	21, // 7: api.wiki_ai.v1.ContentCheckRequest.text:type_name -> api.wiki_ai.v1.TextCheck
	5,  // 8: api.wiki_ai.v1.FindAsyncTranslateResponse.items:type_name -> api.wiki_ai.v1.AsyncTranslateInfo
	26, // 9: api.wiki_ai.v1.Service.Healthy:input_type -> common.EmptyRequest
	14, // 10: api.wiki_ai.v1.Service.SearchTraders:input_type -> api.wiki_ai.v1.SearchTradersRequest
	16, // 11: api.wiki_ai.v1.Service.TraderRecommend:input_type -> api.wiki_ai.v1.TradeRecommendRequest
	18, // 12: api.wiki_ai.v1.Service.RobotAction:input_type -> api.wiki_ai.v1.RobotActionRequest
	22, // 13: api.wiki_ai.v1.Service.ContentCheck:input_type -> api.wiki_ai.v1.ContentCheckRequest
	9,  // 14: api.wiki_ai.v1.Service.Translate:input_type -> api.wiki_ai.v1.TranslateRequest
	9,  // 15: api.wiki_ai.v1.Service.TranslateByLLM:input_type -> api.wiki_ai.v1.TranslateRequest
	11, // 16: api.wiki_ai.v1.Service.TranslateCustom:input_type -> api.wiki_ai.v1.TranslateCustomRequest
	7,  // 17: api.wiki_ai.v1.Service.AsyncTranslate:input_type -> api.wiki_ai.v1.AsyncTranslateRequest
	3,  // 18: api.wiki_ai.v1.Service.GetAsyncTranslate:input_type -> api.wiki_ai.v1.GetAsyncTranslateRequest
	24, // 19: api.wiki_ai.v1.Service.FindAsyncTranslate:input_type -> api.wiki_ai.v1.FindAsyncTranslateRequest
	27, // 20: api.wiki_ai.v1.Service.Healthy:output_type -> common.HealthyReply
	15, // 21: api.wiki_ai.v1.Service.SearchTraders:output_type -> api.wiki_ai.v1.SearchTradersReply
	17, // 22: api.wiki_ai.v1.Service.TraderRecommend:output_type -> api.wiki_ai.v1.TradeRecommendReply
	19, // 23: api.wiki_ai.v1.Service.RobotAction:output_type -> api.wiki_ai.v1.RobotActionResponse
	23, // 24: api.wiki_ai.v1.Service.ContentCheck:output_type -> api.wiki_ai.v1.ContentCheckResponse
	10, // 25: api.wiki_ai.v1.Service.Translate:output_type -> api.wiki_ai.v1.TranslateReply
	10, // 26: api.wiki_ai.v1.Service.TranslateByLLM:output_type -> api.wiki_ai.v1.TranslateReply
	12, // 27: api.wiki_ai.v1.Service.TranslateCustom:output_type -> api.wiki_ai.v1.TranslateCustomReply
	8,  // 28: api.wiki_ai.v1.Service.AsyncTranslate:output_type -> api.wiki_ai.v1.AsyncTranslateReply
	5,  // 29: api.wiki_ai.v1.Service.GetAsyncTranslate:output_type -> api.wiki_ai.v1.AsyncTranslateInfo
	25, // 30: api.wiki_ai.v1.Service.FindAsyncTranslate:output_type -> api.wiki_ai.v1.FindAsyncTranslateResponse
	20, // [20:31] is the sub-list for method output_type
	9,  // [9:20] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_ai_v1_service_proto_init() }
func file_ai_v1_service_proto_init() {
	if File_ai_v1_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ai_v1_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAsyncTranslateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackRecord); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AsyncTranslateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AsyncTranslateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AsyncTranslateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslateCustomRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TranslateCustomReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraderItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTradersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchTradersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TradeRecommendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TradeRecommendReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TextCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContentCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindAsyncTranslateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ai_v1_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FindAsyncTranslateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ai_v1_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_ai_v1_service_proto_goTypes,
		DependencyIndexes: file_ai_v1_service_proto_depIdxs,
		EnumInfos:         file_ai_v1_service_proto_enumTypes,
		MessageInfos:      file_ai_v1_service_proto_msgTypes,
	}.Build()
	File_ai_v1_service_proto = out.File
	file_ai_v1_service_proto_rawDesc = nil
	file_ai_v1_service_proto_goTypes = nil
	file_ai_v1_service_proto_depIdxs = nil
}
