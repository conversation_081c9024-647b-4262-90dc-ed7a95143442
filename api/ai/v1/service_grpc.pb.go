// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: ai/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName            = "/api.wiki_ai.v1.Service/Healthy"
	Service_SearchTraders_FullMethodName      = "/api.wiki_ai.v1.Service/SearchTraders"
	Service_TraderRecommend_FullMethodName    = "/api.wiki_ai.v1.Service/TraderRecommend"
	Service_RobotAction_FullMethodName        = "/api.wiki_ai.v1.Service/RobotAction"
	Service_ContentCheck_FullMethodName       = "/api.wiki_ai.v1.Service/ContentCheck"
	Service_Translate_FullMethodName          = "/api.wiki_ai.v1.Service/Translate"
	Service_TranslateByLLM_FullMethodName     = "/api.wiki_ai.v1.Service/TranslateByLLM"
	Service_TranslateCustom_FullMethodName    = "/api.wiki_ai.v1.Service/TranslateCustom"
	Service_AsyncTranslate_FullMethodName     = "/api.wiki_ai.v1.Service/AsyncTranslate"
	Service_GetAsyncTranslate_FullMethodName  = "/api.wiki_ai.v1.Service/GetAsyncTranslate"
	Service_FindAsyncTranslate_FullMethodName = "/api.wiki_ai.v1.Service/FindAsyncTranslate"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// 多个交易商信息查询
	SearchTraders(ctx context.Context, in *SearchTradersRequest, opts ...grpc.CallOption) (*SearchTradersReply, error)
	// 交易商推荐
	TraderRecommend(ctx context.Context, in *TradeRecommendRequest, opts ...grpc.CallOption) (*TradeRecommendReply, error)
	// 机器人水军接口调用
	RobotAction(ctx context.Context, in *RobotActionRequest, opts ...grpc.CallOption) (*RobotActionResponse, error)
	// ======================================= 内容审核 ===========================================
	ContentCheck(ctx context.Context, in *ContentCheckRequest, opts ...grpc.CallOption) (*ContentCheckResponse, error)
	// 大模型批量翻译（同步）
	Translate(ctx context.Context, in *TranslateRequest, opts ...grpc.CallOption) (*TranslateReply, error)
	// 大模型批量翻译(这里直走大模型)（同步）
	TranslateByLLM(ctx context.Context, in *TranslateRequest, opts ...grpc.CallOption) (*TranslateReply, error)
	// 大模型批量翻译（同步）
	TranslateCustom(ctx context.Context, in *TranslateCustomRequest, opts ...grpc.CallOption) (*TranslateCustomReply, error)
	// 大模型翻译（异步）
	AsyncTranslate(ctx context.Context, in *AsyncTranslateRequest, opts ...grpc.CallOption) (*AsyncTranslateReply, error)
	// 大模型异步翻译查询
	GetAsyncTranslate(ctx context.Context, in *GetAsyncTranslateRequest, opts ...grpc.CallOption) (*AsyncTranslateInfo, error)
	// 根据业务ID查询翻译
	FindAsyncTranslate(ctx context.Context, in *FindAsyncTranslateRequest, opts ...grpc.CallOption) (*FindAsyncTranslateResponse, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SearchTraders(ctx context.Context, in *SearchTradersRequest, opts ...grpc.CallOption) (*SearchTradersReply, error) {
	out := new(SearchTradersReply)
	err := c.cc.Invoke(ctx, Service_SearchTraders_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TraderRecommend(ctx context.Context, in *TradeRecommendRequest, opts ...grpc.CallOption) (*TradeRecommendReply, error) {
	out := new(TradeRecommendReply)
	err := c.cc.Invoke(ctx, Service_TraderRecommend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) RobotAction(ctx context.Context, in *RobotActionRequest, opts ...grpc.CallOption) (*RobotActionResponse, error) {
	out := new(RobotActionResponse)
	err := c.cc.Invoke(ctx, Service_RobotAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ContentCheck(ctx context.Context, in *ContentCheckRequest, opts ...grpc.CallOption) (*ContentCheckResponse, error) {
	out := new(ContentCheckResponse)
	err := c.cc.Invoke(ctx, Service_ContentCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) Translate(ctx context.Context, in *TranslateRequest, opts ...grpc.CallOption) (*TranslateReply, error) {
	out := new(TranslateReply)
	err := c.cc.Invoke(ctx, Service_Translate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TranslateByLLM(ctx context.Context, in *TranslateRequest, opts ...grpc.CallOption) (*TranslateReply, error) {
	out := new(TranslateReply)
	err := c.cc.Invoke(ctx, Service_TranslateByLLM_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) TranslateCustom(ctx context.Context, in *TranslateCustomRequest, opts ...grpc.CallOption) (*TranslateCustomReply, error) {
	out := new(TranslateCustomReply)
	err := c.cc.Invoke(ctx, Service_TranslateCustom_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) AsyncTranslate(ctx context.Context, in *AsyncTranslateRequest, opts ...grpc.CallOption) (*AsyncTranslateReply, error) {
	out := new(AsyncTranslateReply)
	err := c.cc.Invoke(ctx, Service_AsyncTranslate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetAsyncTranslate(ctx context.Context, in *GetAsyncTranslateRequest, opts ...grpc.CallOption) (*AsyncTranslateInfo, error) {
	out := new(AsyncTranslateInfo)
	err := c.cc.Invoke(ctx, Service_GetAsyncTranslate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FindAsyncTranslate(ctx context.Context, in *FindAsyncTranslateRequest, opts ...grpc.CallOption) (*FindAsyncTranslateResponse, error) {
	out := new(FindAsyncTranslateResponse)
	err := c.cc.Invoke(ctx, Service_FindAsyncTranslate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// 多个交易商信息查询
	SearchTraders(context.Context, *SearchTradersRequest) (*SearchTradersReply, error)
	// 交易商推荐
	TraderRecommend(context.Context, *TradeRecommendRequest) (*TradeRecommendReply, error)
	// 机器人水军接口调用
	RobotAction(context.Context, *RobotActionRequest) (*RobotActionResponse, error)
	// ======================================= 内容审核 ===========================================
	ContentCheck(context.Context, *ContentCheckRequest) (*ContentCheckResponse, error)
	// 大模型批量翻译（同步）
	Translate(context.Context, *TranslateRequest) (*TranslateReply, error)
	// 大模型批量翻译(这里直走大模型)（同步）
	TranslateByLLM(context.Context, *TranslateRequest) (*TranslateReply, error)
	// 大模型批量翻译（同步）
	TranslateCustom(context.Context, *TranslateCustomRequest) (*TranslateCustomReply, error)
	// 大模型翻译（异步）
	AsyncTranslate(context.Context, *AsyncTranslateRequest) (*AsyncTranslateReply, error)
	// 大模型异步翻译查询
	GetAsyncTranslate(context.Context, *GetAsyncTranslateRequest) (*AsyncTranslateInfo, error)
	// 根据业务ID查询翻译
	FindAsyncTranslate(context.Context, *FindAsyncTranslateRequest) (*FindAsyncTranslateResponse, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) SearchTraders(context.Context, *SearchTradersRequest) (*SearchTradersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchTraders not implemented")
}
func (UnimplementedServiceServer) TraderRecommend(context.Context, *TradeRecommendRequest) (*TradeRecommendReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TraderRecommend not implemented")
}
func (UnimplementedServiceServer) RobotAction(context.Context, *RobotActionRequest) (*RobotActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RobotAction not implemented")
}
func (UnimplementedServiceServer) ContentCheck(context.Context, *ContentCheckRequest) (*ContentCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContentCheck not implemented")
}
func (UnimplementedServiceServer) Translate(context.Context, *TranslateRequest) (*TranslateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Translate not implemented")
}
func (UnimplementedServiceServer) TranslateByLLM(context.Context, *TranslateRequest) (*TranslateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TranslateByLLM not implemented")
}
func (UnimplementedServiceServer) TranslateCustom(context.Context, *TranslateCustomRequest) (*TranslateCustomReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TranslateCustom not implemented")
}
func (UnimplementedServiceServer) AsyncTranslate(context.Context, *AsyncTranslateRequest) (*AsyncTranslateReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AsyncTranslate not implemented")
}
func (UnimplementedServiceServer) GetAsyncTranslate(context.Context, *GetAsyncTranslateRequest) (*AsyncTranslateInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAsyncTranslate not implemented")
}
func (UnimplementedServiceServer) FindAsyncTranslate(context.Context, *FindAsyncTranslateRequest) (*FindAsyncTranslateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindAsyncTranslate not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SearchTraders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchTradersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SearchTraders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SearchTraders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SearchTraders(ctx, req.(*SearchTradersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TraderRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TradeRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TraderRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TraderRecommend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TraderRecommend(ctx, req.(*TradeRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_RobotAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RobotActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).RobotAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_RobotAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).RobotAction(ctx, req.(*RobotActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ContentCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ContentCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ContentCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ContentCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ContentCheck(ctx, req.(*ContentCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_Translate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TranslateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Translate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Translate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Translate(ctx, req.(*TranslateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TranslateByLLM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TranslateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TranslateByLLM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TranslateByLLM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TranslateByLLM(ctx, req.(*TranslateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_TranslateCustom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TranslateCustomRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).TranslateCustom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_TranslateCustom_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).TranslateCustom(ctx, req.(*TranslateCustomRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_AsyncTranslate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AsyncTranslateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).AsyncTranslate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_AsyncTranslate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).AsyncTranslate(ctx, req.(*AsyncTranslateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetAsyncTranslate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAsyncTranslateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetAsyncTranslate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetAsyncTranslate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetAsyncTranslate(ctx, req.(*GetAsyncTranslateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FindAsyncTranslate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindAsyncTranslateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FindAsyncTranslate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FindAsyncTranslate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FindAsyncTranslate(ctx, req.(*FindAsyncTranslateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wiki_ai.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "SearchTraders",
			Handler:    _Service_SearchTraders_Handler,
		},
		{
			MethodName: "TraderRecommend",
			Handler:    _Service_TraderRecommend_Handler,
		},
		{
			MethodName: "RobotAction",
			Handler:    _Service_RobotAction_Handler,
		},
		{
			MethodName: "ContentCheck",
			Handler:    _Service_ContentCheck_Handler,
		},
		{
			MethodName: "Translate",
			Handler:    _Service_Translate_Handler,
		},
		{
			MethodName: "TranslateByLLM",
			Handler:    _Service_TranslateByLLM_Handler,
		},
		{
			MethodName: "TranslateCustom",
			Handler:    _Service_TranslateCustom_Handler,
		},
		{
			MethodName: "AsyncTranslate",
			Handler:    _Service_AsyncTranslate_Handler,
		},
		{
			MethodName: "GetAsyncTranslate",
			Handler:    _Service_GetAsyncTranslate_Handler,
		},
		{
			MethodName: "FindAsyncTranslate",
			Handler:    _Service_FindAsyncTranslate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ai/v1/service.proto",
}
