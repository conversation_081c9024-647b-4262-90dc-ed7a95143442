// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: forum/v1/model.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	MyService_GetMediateList_FullMethodName   = "/api.forum.v1.MyService/GetMediateList"
	MyService_GetMediateDetail_FullMethodName = "/api.forum.v1.MyService/GetMediateDetail"
	MyService_GetLatest_FullMethodName        = "/api.forum.v1.MyService/GetLatest"
	MyService_GetMyMediateList_FullMethodName = "/api.forum.v1.MyService/GetMyMediateList"
	MyService_GetMediates_FullMethodName      = "/api.forum.v1.MyService/GetMediates"
)

// MyServiceClient is the client API for MyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MyServiceClient interface {
	GetMediateList(ctx context.Context, in *GetMediateInfoRequest, opts ...grpc.CallOption) (*GetMediateInfoReply, error)
	GetMediateDetail(ctx context.Context, in *GetMediateDetailRequest, opts ...grpc.CallOption) (*GetMediateDetailReply, error)
	GetLatest(ctx context.Context, in *Request, opts ...grpc.CallOption) (*GetMediateListReply, error)
	GetMyMediateList(ctx context.Context, in *GetMyMediateListRequest, opts ...grpc.CallOption) (*GetMediateListReply, error)
	GetMediates(ctx context.Context, in *GetMyMediateListRequest, opts ...grpc.CallOption) (*GetMediateListReply, error)
}

type myServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMyServiceClient(cc grpc.ClientConnInterface) MyServiceClient {
	return &myServiceClient{cc}
}

func (c *myServiceClient) GetMediateList(ctx context.Context, in *GetMediateInfoRequest, opts ...grpc.CallOption) (*GetMediateInfoReply, error) {
	out := new(GetMediateInfoReply)
	err := c.cc.Invoke(ctx, MyService_GetMediateList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *myServiceClient) GetMediateDetail(ctx context.Context, in *GetMediateDetailRequest, opts ...grpc.CallOption) (*GetMediateDetailReply, error) {
	out := new(GetMediateDetailReply)
	err := c.cc.Invoke(ctx, MyService_GetMediateDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *myServiceClient) GetLatest(ctx context.Context, in *Request, opts ...grpc.CallOption) (*GetMediateListReply, error) {
	out := new(GetMediateListReply)
	err := c.cc.Invoke(ctx, MyService_GetLatest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *myServiceClient) GetMyMediateList(ctx context.Context, in *GetMyMediateListRequest, opts ...grpc.CallOption) (*GetMediateListReply, error) {
	out := new(GetMediateListReply)
	err := c.cc.Invoke(ctx, MyService_GetMyMediateList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *myServiceClient) GetMediates(ctx context.Context, in *GetMyMediateListRequest, opts ...grpc.CallOption) (*GetMediateListReply, error) {
	out := new(GetMediateListReply)
	err := c.cc.Invoke(ctx, MyService_GetMediates_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MyServiceServer is the server API for MyService service.
// All implementations must embed UnimplementedMyServiceServer
// for forward compatibility
type MyServiceServer interface {
	GetMediateList(context.Context, *GetMediateInfoRequest) (*GetMediateInfoReply, error)
	GetMediateDetail(context.Context, *GetMediateDetailRequest) (*GetMediateDetailReply, error)
	GetLatest(context.Context, *Request) (*GetMediateListReply, error)
	GetMyMediateList(context.Context, *GetMyMediateListRequest) (*GetMediateListReply, error)
	GetMediates(context.Context, *GetMyMediateListRequest) (*GetMediateListReply, error)
	mustEmbedUnimplementedMyServiceServer()
}

// UnimplementedMyServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMyServiceServer struct {
}

func (UnimplementedMyServiceServer) GetMediateList(context.Context, *GetMediateInfoRequest) (*GetMediateInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMediateList not implemented")
}
func (UnimplementedMyServiceServer) GetMediateDetail(context.Context, *GetMediateDetailRequest) (*GetMediateDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMediateDetail not implemented")
}
func (UnimplementedMyServiceServer) GetLatest(context.Context, *Request) (*GetMediateListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatest not implemented")
}
func (UnimplementedMyServiceServer) GetMyMediateList(context.Context, *GetMyMediateListRequest) (*GetMediateListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyMediateList not implemented")
}
func (UnimplementedMyServiceServer) GetMediates(context.Context, *GetMyMediateListRequest) (*GetMediateListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMediates not implemented")
}
func (UnimplementedMyServiceServer) mustEmbedUnimplementedMyServiceServer() {}

// UnsafeMyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MyServiceServer will
// result in compilation errors.
type UnsafeMyServiceServer interface {
	mustEmbedUnimplementedMyServiceServer()
}

func RegisterMyServiceServer(s grpc.ServiceRegistrar, srv MyServiceServer) {
	s.RegisterService(&MyService_ServiceDesc, srv)
}

func _MyService_GetMediateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMediateInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MyServiceServer).GetMediateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MyService_GetMediateList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MyServiceServer).GetMediateList(ctx, req.(*GetMediateInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MyService_GetMediateDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMediateDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MyServiceServer).GetMediateDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MyService_GetMediateDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MyServiceServer).GetMediateDetail(ctx, req.(*GetMediateDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MyService_GetLatest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MyServiceServer).GetLatest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MyService_GetLatest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MyServiceServer).GetLatest(ctx, req.(*Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _MyService_GetMyMediateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyMediateListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MyServiceServer).GetMyMediateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MyService_GetMyMediateList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MyServiceServer).GetMyMediateList(ctx, req.(*GetMyMediateListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MyService_GetMediates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyMediateListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MyServiceServer).GetMediates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MyService_GetMediates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MyServiceServer).GetMediates(ctx, req.(*GetMyMediateListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MyService_ServiceDesc is the grpc.ServiceDesc for MyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.forum.v1.MyService",
	HandlerType: (*MyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMediateList",
			Handler:    _MyService_GetMediateList_Handler,
		},
		{
			MethodName: "GetMediateDetail",
			Handler:    _MyService_GetMediateDetail_Handler,
		},
		{
			MethodName: "GetLatest",
			Handler:    _MyService_GetLatest_Handler,
		},
		{
			MethodName: "GetMyMediateList",
			Handler:    _MyService_GetMyMediateList_Handler,
		},
		{
			MethodName: "GetMediates",
			Handler:    _MyService_GetMediates_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "forum/v1/model.proto",
}
