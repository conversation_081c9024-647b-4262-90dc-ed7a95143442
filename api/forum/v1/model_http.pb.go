// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: forum/v1/model.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationMyServiceGetLatest = "/api.forum.v1.MyService/GetLatest"
const OperationMyServiceGetMediateDetail = "/api.forum.v1.MyService/GetMediateDetail"
const OperationMyServiceGetMediateList = "/api.forum.v1.MyService/GetMediateList"
const OperationMyServiceGetMediates = "/api.forum.v1.MyService/GetMediates"
const OperationMyServiceGetMyMediateList = "/api.forum.v1.MyService/GetMyMediateList"

type MyServiceHTTPServer interface {
	GetLatest(context.Context, *Request) (*GetMediateListReply, error)
	GetMediateDetail(context.Context, *GetMediateDetailRequest) (*GetMediateDetailReply, error)
	GetMediateList(context.Context, *GetMediateInfoRequest) (*GetMediateInfoReply, error)
	GetMediates(context.Context, *GetMyMediateListRequest) (*GetMediateListReply, error)
	GetMyMediateList(context.Context, *GetMyMediateListRequest) (*GetMediateListReply, error)
}

func RegisterMyServiceHTTPServer(s *http.Server, srv MyServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/app/forum/getmediatelist", _MyService_GetMediateList0_HTTP_Handler(srv))
	r.POST("/v1/app/forum/getmediatedetail", _MyService_GetMediateDetail0_HTTP_Handler(srv))
	r.GET("/v1/app/forum/getlatest", _MyService_GetLatest0_HTTP_Handler(srv))
	r.POST("/v1/app/forum/getmymediatelist", _MyService_GetMyMediateList0_HTTP_Handler(srv))
	r.POST("/v1/app/forum/GetMediates", _MyService_GetMediates0_HTTP_Handler(srv))
}

func _MyService_GetMediateList0_HTTP_Handler(srv MyServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMediateInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMyServiceGetMediateList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMediateList(ctx, req.(*GetMediateInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMediateInfoReply)
		return ctx.Result(200, reply)
	}
}

func _MyService_GetMediateDetail0_HTTP_Handler(srv MyServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMediateDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMyServiceGetMediateDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMediateDetail(ctx, req.(*GetMediateDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMediateDetailReply)
		return ctx.Result(200, reply)
	}
}

func _MyService_GetLatest0_HTTP_Handler(srv MyServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Request
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMyServiceGetLatest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLatest(ctx, req.(*Request))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMediateListReply)
		return ctx.Result(200, reply)
	}
}

func _MyService_GetMyMediateList0_HTTP_Handler(srv MyServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMyMediateListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMyServiceGetMyMediateList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMyMediateList(ctx, req.(*GetMyMediateListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMediateListReply)
		return ctx.Result(200, reply)
	}
}

func _MyService_GetMediates0_HTTP_Handler(srv MyServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMyMediateListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationMyServiceGetMediates)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMediates(ctx, req.(*GetMyMediateListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMediateListReply)
		return ctx.Result(200, reply)
	}
}

type MyServiceHTTPClient interface {
	GetLatest(ctx context.Context, req *Request, opts ...http.CallOption) (rsp *GetMediateListReply, err error)
	GetMediateDetail(ctx context.Context, req *GetMediateDetailRequest, opts ...http.CallOption) (rsp *GetMediateDetailReply, err error)
	GetMediateList(ctx context.Context, req *GetMediateInfoRequest, opts ...http.CallOption) (rsp *GetMediateInfoReply, err error)
	GetMediates(ctx context.Context, req *GetMyMediateListRequest, opts ...http.CallOption) (rsp *GetMediateListReply, err error)
	GetMyMediateList(ctx context.Context, req *GetMyMediateListRequest, opts ...http.CallOption) (rsp *GetMediateListReply, err error)
}

type MyServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewMyServiceHTTPClient(client *http.Client) MyServiceHTTPClient {
	return &MyServiceHTTPClientImpl{client}
}

func (c *MyServiceHTTPClientImpl) GetLatest(ctx context.Context, in *Request, opts ...http.CallOption) (*GetMediateListReply, error) {
	var out GetMediateListReply
	pattern := "/v1/app/forum/getlatest"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationMyServiceGetLatest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MyServiceHTTPClientImpl) GetMediateDetail(ctx context.Context, in *GetMediateDetailRequest, opts ...http.CallOption) (*GetMediateDetailReply, error) {
	var out GetMediateDetailReply
	pattern := "/v1/app/forum/getmediatedetail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMyServiceGetMediateDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MyServiceHTTPClientImpl) GetMediateList(ctx context.Context, in *GetMediateInfoRequest, opts ...http.CallOption) (*GetMediateInfoReply, error) {
	var out GetMediateInfoReply
	pattern := "/v1/app/forum/getmediatelist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMyServiceGetMediateList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MyServiceHTTPClientImpl) GetMediates(ctx context.Context, in *GetMyMediateListRequest, opts ...http.CallOption) (*GetMediateListReply, error) {
	var out GetMediateListReply
	pattern := "/v1/app/forum/GetMediates"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMyServiceGetMediates))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *MyServiceHTTPClientImpl) GetMyMediateList(ctx context.Context, in *GetMyMediateListRequest, opts ...http.CallOption) (*GetMediateListReply, error) {
	var out GetMediateListReply
	pattern := "/v1/app/forum/getmymediatelist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationMyServiceGetMyMediateList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
