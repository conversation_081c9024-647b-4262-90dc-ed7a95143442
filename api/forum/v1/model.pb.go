// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: forum/v1/model.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuditStatus int32

const (
	AuditStatus_Topic_All     AuditStatus = 0   //全部
	AuditStatus_Topic_Pending AuditStatus = 100 //未审核
	AuditStatus_Topic_Success AuditStatus = 200 //审核通过
	AuditStatus_Topic_Fail    AuditStatus = 401 //审核未通过
	AuditStatus_Topic_Private AuditStatus = 407 //隐藏
	AuditStatus_Topic_Hidden  AuditStatus = 408 //后台隐藏
)

// Enum value maps for AuditStatus.
var (
	AuditStatus_name = map[int32]string{
		0:   "Topic_All",
		100: "Topic_Pending",
		200: "Topic_Success",
		401: "Topic_Fail",
		407: "Topic_Private",
		408: "Topic_Hidden",
	}
	AuditStatus_value = map[string]int32{
		"Topic_All":     0,
		"Topic_Pending": 100,
		"Topic_Success": 200,
		"Topic_Fail":    401,
		"Topic_Private": 407,
		"Topic_Hidden":  408,
	}
)

func (x AuditStatus) Enum() *AuditStatus {
	p := new(AuditStatus)
	*p = x
	return p
}

func (x AuditStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_forum_v1_model_proto_enumTypes[0].Descriptor()
}

func (AuditStatus) Type() protoreflect.EnumType {
	return &file_forum_v1_model_proto_enumTypes[0]
}

func (x AuditStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditStatus.Descriptor instead.
func (AuditStatus) EnumDescriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{0}
}

type MediateStatus int32

const (
	MediateStatus_Mediate_All      MediateStatus = 0   //全部
	MediateStatus_Mediate_Pending  MediateStatus = 100 //待审核
	MediateStatus_Mediate_Handling MediateStatus = 110 //处理中
	MediateStatus_Mediate_Reply    MediateStatus = 112 //处理中
	MediateStatus_Mediate_Finish   MediateStatus = 200 //已解决
	MediateStatus_Mediate_Abandon  MediateStatus = 300 //已放弃
	MediateStatus_Mediate_Fail     MediateStatus = 401 //未通过
)

// Enum value maps for MediateStatus.
var (
	MediateStatus_name = map[int32]string{
		0:   "Mediate_All",
		100: "Mediate_Pending",
		110: "Mediate_Handling",
		112: "Mediate_Reply",
		200: "Mediate_Finish",
		300: "Mediate_Abandon",
		401: "Mediate_Fail",
	}
	MediateStatus_value = map[string]int32{
		"Mediate_All":      0,
		"Mediate_Pending":  100,
		"Mediate_Handling": 110,
		"Mediate_Reply":    112,
		"Mediate_Finish":   200,
		"Mediate_Abandon":  300,
		"Mediate_Fail":     401,
	}
)

func (x MediateStatus) Enum() *MediateStatus {
	p := new(MediateStatus)
	*p = x
	return p
}

func (x MediateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MediateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_forum_v1_model_proto_enumTypes[1].Descriptor()
}

func (MediateStatus) Type() protoreflect.EnumType {
	return &file_forum_v1_model_proto_enumTypes[1]
}

func (x MediateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MediateStatus.Descriptor instead.
func (MediateStatus) EnumDescriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{1}
}

type Owner int32

const (
	Owner_Reply_User   Owner = 0 //用户追问
	Owner_Reply_Wikifx Owner = 1 //官方回复
	Owner_Reply_Trader Owner = 2 //交易商回复
)

// Enum value maps for Owner.
var (
	Owner_name = map[int32]string{
		0: "Reply_User",
		1: "Reply_Wikifx",
		2: "Reply_Trader",
	}
	Owner_value = map[string]int32{
		"Reply_User":   0,
		"Reply_Wikifx": 1,
		"Reply_Trader": 2,
	}
)

func (x Owner) Enum() *Owner {
	p := new(Owner)
	*p = x
	return p
}

func (x Owner) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Owner) Descriptor() protoreflect.EnumDescriptor {
	return file_forum_v1_model_proto_enumTypes[2].Descriptor()
}

func (Owner) Type() protoreflect.EnumType {
	return &file_forum_v1_model_proto_enumTypes[2]
}

func (x Owner) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Owner.Descriptor instead.
func (Owner) EnumDescriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{2}
}

type Platform int32

const (
	Platform_P_Fxeye   Platform = 0 //国内版
	Platform_P_WikiFx  Platform = 1 //国际版
	Platform_P_Wikibit Platform = 2 //wikibit
)

// Enum value maps for Platform.
var (
	Platform_name = map[int32]string{
		0: "P_Fxeye",
		1: "P_WikiFx",
		2: "P_Wikibit",
	}
	Platform_value = map[string]int32{
		"P_Fxeye":   0,
		"P_WikiFx":  1,
		"P_Wikibit": 2,
	}
)

func (x Platform) Enum() *Platform {
	p := new(Platform)
	*p = x
	return p
}

func (x Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_forum_v1_model_proto_enumTypes[3].Descriptor()
}

func (Platform) Type() protoreflect.EnumType {
	return &file_forum_v1_model_proto_enumTypes[3]
}

func (x Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Platform.Descriptor instead.
func (Platform) EnumDescriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{3}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{0}
}

type GetMyMediateListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuditStatus MediateStatus `protobuf:"varint,35,opt,name=auditStatus,json=auditStatus,proto3,enum=api.forum.v1.MediateStatus" json:"auditStatus"`
	CountryCode string        `protobuf:"bytes,2,opt,name=countryCode,json=countryCode,proto3" json:"countryCode"`
	Uid         string        `protobuf:"bytes,3,opt,name=uid,json=uid,proto3" json:"uid"`
	Platform    Platform      `protobuf:"varint,4,opt,name=platform,json=platform,proto3,enum=api.forum.v1.Platform" json:"platform"`
	PageIndex   int32         `protobuf:"varint,5,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize    int32         `protobuf:"varint,6,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
	IsApp       bool          `protobuf:"varint,7,opt,name=isApp,json=isApp,proto3" json:"isApp"`
}

func (x *GetMyMediateListRequest) Reset() {
	*x = GetMyMediateListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMyMediateListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyMediateListRequest) ProtoMessage() {}

func (x *GetMyMediateListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyMediateListRequest.ProtoReflect.Descriptor instead.
func (*GetMyMediateListRequest) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{1}
}

func (x *GetMyMediateListRequest) GetAuditStatus() MediateStatus {
	if x != nil {
		return x.AuditStatus
	}
	return MediateStatus_Mediate_All
}

func (x *GetMyMediateListRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *GetMyMediateListRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetMyMediateListRequest) GetPlatform() Platform {
	if x != nil {
		return x.Platform
	}
	return Platform_P_Fxeye
}

func (x *GetMyMediateListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetMyMediateListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetMyMediateListRequest) GetIsApp() bool {
	if x != nil {
		return x.IsApp
	}
	return false
}

type GetMediateInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Codes     []string `protobuf:"bytes,1,rep,name=codes,json=codes,proto3" json:"codes"`
	PageIndex int32    `protobuf:"varint,2,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32    `protobuf:"varint,3,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetMediateInfoRequest) Reset() {
	*x = GetMediateInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediateInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediateInfoRequest) ProtoMessage() {}

func (x *GetMediateInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediateInfoRequest.ProtoReflect.Descriptor instead.
func (*GetMediateInfoRequest) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{2}
}

func (x *GetMediateInfoRequest) GetCodes() []string {
	if x != nil {
		return x.Codes
	}
	return nil
}

func (x *GetMediateInfoRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetMediateInfoRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetMediateInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32          `protobuf:"varint,1,opt,name=total,json=total,proto3" json:"total"`
	Items []*MediateInfo `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetMediateInfoReply) Reset() {
	*x = GetMediateInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediateInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediateInfoReply) ProtoMessage() {}

func (x *GetMediateInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediateInfoReply.ProtoReflect.Descriptor instead.
func (*GetMediateInfoReply) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{3}
}

func (x *GetMediateInfoReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetMediateInfoReply) GetItems() []*MediateInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type MediateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code              string        `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
	LanguageCode      string        `protobuf:"bytes,2,opt,name=languageCode,json=languageCode,proto3" json:"languageCode"`
	OriginalTitle     string        `protobuf:"bytes,3,opt,name=originalTitle,json=originalTitle,proto3" json:"originalTitle"`
	OriginalContent   string        `protobuf:"bytes,4,opt,name=originalContent,json=originalContent,proto3" json:"originalContent"`
	Title             string        `protobuf:"bytes,5,opt,name=title,json=title,proto3" json:"title"`
	Content           string        `protobuf:"bytes,6,opt,name=content,json=content,proto3" json:"content"`
	Images            []*Image      `protobuf:"bytes,7,rep,name=images,json=images,proto3" json:"images"`
	ClosedTimestamp   int64         `protobuf:"varint,8,opt,name=closedTimestamp,json=closedTimestamp,proto3" json:"closedTimestamp"`
	Amount            string        `protobuf:"bytes,9,opt,name=amount,json=amount,proto3" json:"amount"`
	ObjectLabel       string        `protobuf:"bytes,10,opt,name=objectLabel,json=objectLabel,proto3" json:"objectLabel"`
	AmountLabel       string        `protobuf:"bytes,11,opt,name=amountLabel,json=amountLabel,proto3" json:"amountLabel"`
	CategoryLabel     string        `protobuf:"bytes,12,opt,name=categoryLabel,json=categoryLabel,proto3" json:"categoryLabel"`
	RequirementLabel  string        `protobuf:"bytes,13,opt,name=requirementLabel,json=requirementLabel,proto3" json:"requirementLabel"`
	Requirement       string        `protobuf:"bytes,14,opt,name=requirement,json=requirement,proto3" json:"requirement"`
	Category          string        `protobuf:"bytes,15,opt,name=category,json=category,proto3" json:"category"`
	Symbol            string        `protobuf:"bytes,17,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	TraderCode        string        `protobuf:"bytes,18,opt,name=traderCode,json=traderCode,proto3" json:"traderCode"`
	UserId            string        `protobuf:"bytes,19,opt,name=userId,json=userId,proto3" json:"userId"`
	CreatedAt         int64         `protobuf:"varint,20,opt,name=createdAt,json=createdAt,proto3" json:"createdAt"`
	Annoation         string        `protobuf:"bytes,21,opt,name=annoation,json=annoation,proto3" json:"annoation"`
	Color             string        `protobuf:"bytes,22,opt,name=color,json=color,proto3" json:"color"`
	NewColor          string        `protobuf:"bytes,23,opt,name=newColor,json=newColor,proto3" json:"newColor"`
	Stamp             string        `protobuf:"bytes,24,opt,name=stamp,json=stamp,proto3" json:"stamp"`
	AppendAuditStatus MediateStatus `protobuf:"varint,25,opt,name=appendAuditStatus,json=appendAuditStatus,proto3,enum=api.forum.v1.MediateStatus" json:"appendAuditStatus"`
	Id                string        `protobuf:"bytes,26,opt,name=id,json=id,proto3" json:"id"`
	CurrencyCode      string        `protobuf:"bytes,27,opt,name=currencyCode,json=currencyCode,proto3" json:"currencyCode"`
	Elapsed           string        `protobuf:"bytes,28,opt,name=elapsed,json=elapsed,proto3" json:"elapsed"`
	CloseLabel        string        `protobuf:"bytes,29,opt,name=closeLabel,json=closeLabel,proto3" json:"closeLabel"`
	CreatedAtMili     int64         `protobuf:"varint,30,opt,name=createdAtMili,json=createdAtMili,proto3" json:"createdAtMili"`
}

func (x *MediateInfo) Reset() {
	*x = MediateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediateInfo) ProtoMessage() {}

func (x *MediateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediateInfo.ProtoReflect.Descriptor instead.
func (*MediateInfo) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{4}
}

func (x *MediateInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *MediateInfo) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *MediateInfo) GetOriginalTitle() string {
	if x != nil {
		return x.OriginalTitle
	}
	return ""
}

func (x *MediateInfo) GetOriginalContent() string {
	if x != nil {
		return x.OriginalContent
	}
	return ""
}

func (x *MediateInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MediateInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MediateInfo) GetImages() []*Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *MediateInfo) GetClosedTimestamp() int64 {
	if x != nil {
		return x.ClosedTimestamp
	}
	return 0
}

func (x *MediateInfo) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *MediateInfo) GetObjectLabel() string {
	if x != nil {
		return x.ObjectLabel
	}
	return ""
}

func (x *MediateInfo) GetAmountLabel() string {
	if x != nil {
		return x.AmountLabel
	}
	return ""
}

func (x *MediateInfo) GetCategoryLabel() string {
	if x != nil {
		return x.CategoryLabel
	}
	return ""
}

func (x *MediateInfo) GetRequirementLabel() string {
	if x != nil {
		return x.RequirementLabel
	}
	return ""
}

func (x *MediateInfo) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

func (x *MediateInfo) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *MediateInfo) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *MediateInfo) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *MediateInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MediateInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MediateInfo) GetAnnoation() string {
	if x != nil {
		return x.Annoation
	}
	return ""
}

func (x *MediateInfo) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *MediateInfo) GetNewColor() string {
	if x != nil {
		return x.NewColor
	}
	return ""
}

func (x *MediateInfo) GetStamp() string {
	if x != nil {
		return x.Stamp
	}
	return ""
}

func (x *MediateInfo) GetAppendAuditStatus() MediateStatus {
	if x != nil {
		return x.AppendAuditStatus
	}
	return MediateStatus_Mediate_All
}

func (x *MediateInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MediateInfo) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *MediateInfo) GetElapsed() string {
	if x != nil {
		return x.Elapsed
	}
	return ""
}

func (x *MediateInfo) GetCloseLabel() string {
	if x != nil {
		return x.CloseLabel
	}
	return ""
}

func (x *MediateInfo) GetCreatedAtMili() int64 {
	if x != nil {
		return x.CreatedAtMili
	}
	return 0
}

type GetMediateListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int32          `protobuf:"varint,1,opt,name=total,json=total,proto3" json:"total"`
	Items []*MediateList `protobuf:"bytes,2,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetMediateListReply) Reset() {
	*x = GetMediateListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediateListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediateListReply) ProtoMessage() {}

func (x *GetMediateListReply) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediateListReply.ProtoReflect.Descriptor instead.
func (*GetMediateListReply) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{5}
}

func (x *GetMediateListReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetMediateListReply) GetItems() []*MediateList {
	if x != nil {
		return x.Items
	}
	return nil
}

type MediateList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code                string        `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
	Categories          []*Category   `protobuf:"bytes,2,rep,name=categories,json=categories,proto3" json:"categories"`
	Language            string        `protobuf:"bytes,3,opt,name=language,json=language,proto3" json:"language"`
	Translanguage       string        `protobuf:"bytes,4,opt,name=translanguage,json=translanguage,proto3" json:"translanguage"`
	IsChinese           bool          `protobuf:"varint,5,opt,name=isChinese,json=isChinese,proto3" json:"isChinese"`
	OriginalTitle       string        `protobuf:"bytes,6,opt,name=originalTitle,json=originalTitle,proto3" json:"originalTitle"`
	OriginalContent     string        `protobuf:"bytes,7,opt,name=originalContent,json=originalContent,proto3" json:"originalContent"`
	Title               string        `protobuf:"bytes,8,opt,name=title,json=title,proto3" json:"title"`
	Content             string        `protobuf:"bytes,9,opt,name=content,json=content,proto3" json:"content"`
	CountryCode         string        `protobuf:"bytes,10,opt,name=countryCode,json=countryCode,proto3" json:"countryCode"`
	CountryName         string        `protobuf:"bytes,11,opt,name=countryName,json=countryName,proto3" json:"countryName"`
	Flag                string        `protobuf:"bytes,12,opt,name=flag,json=flag,proto3" json:"flag"`
	Time                string        `protobuf:"bytes,13,opt,name=time,json=time,proto3" json:"time"`
	Closetime           string        `protobuf:"bytes,14,opt,name=closetime,json=closetime,proto3" json:"closetime"`
	Ip                  string        `protobuf:"bytes,15,opt,name=ip,json=ip,proto3" json:"ip"`
	Images              []*Image      `protobuf:"bytes,16,rep,name=images,json=images,proto3" json:"images"`
	TopicImage          string        `protobuf:"bytes,17,opt,name=topicImage,json=topicImage,proto3" json:"topicImage"`
	ForwardCount        int32         `protobuf:"varint,18,opt,name=forwardCount,json=forwardCount,proto3" json:"forwardCount"`
	CommentCount        int32         `protobuf:"varint,19,opt,name=commentCount,json=commentCount,proto3" json:"commentCount"`
	ApplaudCount        int32         `protobuf:"varint,20,opt,name=applaudCount,json=applaudCount,proto3" json:"applaudCount"`
	ViewCount           int32         `protobuf:"varint,21,opt,name=viewCount,json=viewCount,proto3" json:"viewCount"`
	ForwardCountVisible bool          `protobuf:"varint,22,opt,name=forwardCountVisible,json=forwardCountVisible,proto3" json:"forwardCountVisible"`
	CommentCountVisible bool          `protobuf:"varint,23,opt,name=commentCountVisible,json=commentCountVisible,proto3" json:"commentCountVisible"`
	ApplaudCountVisible bool          `protobuf:"varint,24,opt,name=applaudCountVisible,json=applaudCountVisible,proto3" json:"applaudCountVisible"`
	ViewCountVisible    bool          `protobuf:"varint,25,opt,name=viewCountVisible,json=viewCountVisible,proto3" json:"viewCountVisible"`
	HasImage            bool          `protobuf:"varint,26,opt,name=hasImage,json=hasImage,proto3" json:"hasImage"`
	IsResolved          bool          `protobuf:"varint,27,opt,name=isResolved,json=isResolved,proto3" json:"isResolved"`
	IsResolvedByUser    bool          `protobuf:"varint,28,opt,name=isResolvedByUser,json=isResolvedByUser,proto3" json:"isResolvedByUser"`
	IsApplaud           bool          `protobuf:"varint,29,opt,name=isApplaud,json=isApplaud,proto3" json:"isApplaud"`
	Status              AuditStatus   `protobuf:"varint,30,opt,name=status,json=status,proto3,enum=api.forum.v1.AuditStatus" json:"status"`
	IsCommentClosed     bool          `protobuf:"varint,31,opt,name=IsCommentClosed,json=IsCommentClosed,proto3" json:"IsCommentClosed"`
	Feedback            string        `protobuf:"bytes,32,opt,name=feedback,json=feedback,proto3" json:"feedback"`
	Share               string        `protobuf:"bytes,33,opt,name=share,json=share,proto3" json:"share"`
	HasNewAppend        bool          `protobuf:"varint,34,opt,name=hasNewAppend,json=hasNewAppend,proto3" json:"hasNewAppend"`
	AppendAuditStatus   MediateStatus `protobuf:"varint,35,opt,name=appendAuditStatus,json=appendAuditStatus,proto3,enum=api.forum.v1.MediateStatus" json:"appendAuditStatus"`
	WikiTimestamp       int64         `protobuf:"varint,36,opt,name=wikiTimestamp,json=wikiTimestamp,proto3" json:"wikiTimestamp"`
	WikiTimestampms     int64         `protobuf:"varint,37,opt,name=wikiTimestampms,json=wikiTimestampms,proto3" json:"wikiTimestampms"`
	TopicViewCount      int32         `protobuf:"varint,38,opt,name=topicViewCount,json=topicViewCount,proto3" json:"topicViewCount"`
	IsBoutique          bool          `protobuf:"varint,39,opt,name=isBoutique,json=isBoutique,proto3" json:"isBoutique"`
	CountDownText       string        `protobuf:"bytes,40,opt,name=countDownText,json=countDownText,proto3" json:"countDownText"`
	ExpireTime          string        `protobuf:"bytes,41,opt,name=ExpireTime,json=expireTime,proto3" json:"ExpireTime"`
	Amount              string        `protobuf:"bytes,42,opt,name=amount,json=amount,proto3" json:"amount"`
	ObjectLabel         string        `protobuf:"bytes,43,opt,name=objectLabel,json=objectLabel,proto3" json:"objectLabel"`
	AmountLabel         string        `protobuf:"bytes,44,opt,name=amountLabel,json=amountLabel,proto3" json:"amountLabel"`
	CategoryLabel       string        `protobuf:"bytes,45,opt,name=categoryLabel,json=categoryLabel,proto3" json:"categoryLabel"`
	RequirementLabel    string        `protobuf:"bytes,46,opt,name=requirementLabel,json=requirementLabel,proto3" json:"requirementLabel"`
	Requirement         string        `protobuf:"bytes,47,opt,name=requirement,json=requirement,proto3" json:"requirement"`
	Annoation           string        `protobuf:"bytes,48,opt,name=annoation,json=annoation,proto3" json:"annoation"`
	NewColor            string        `protobuf:"bytes,49,opt,name=newColor,json=newColor,proto3" json:"newColor"`
	Elapsed             string        `protobuf:"bytes,50,opt,name=elapsed,json=elapsed,proto3" json:"elapsed"`
	CloseLabel          string        `protobuf:"bytes,51,opt,name=closeLabel,json=closeLabel,proto3" json:"closeLabel"`
	AppendStatus        MediateStatus `protobuf:"varint,52,opt,name=appendStatus,json=appendStatus,proto3,enum=api.forum.v1.MediateStatus" json:"appendStatus"`
	Stamp               string        `protobuf:"bytes,53,opt,name=stamp,json=stamp,proto3" json:"stamp"`
	CurrencySymbol      string        `protobuf:"bytes,54,opt,name=currency_symbol,json=currency_symbol,proto3" json:"currency_symbol"`
	CurrencyCode        string        `protobuf:"bytes,55,opt,name=currency_code,json=currency_code,proto3" json:"currency_code"`
	AggInfo             *AggInfo      `protobuf:"bytes,56,opt,name=aggInfo,json=aggInfo,proto3" json:"aggInfo"`
	TraderCode          string        `protobuf:"bytes,57,opt,name=traderCode,json=traderCode,proto3" json:"traderCode"`
	UserId              string        `protobuf:"bytes,58,opt,name=userId,json=userId,proto3" json:"userId"`
	Id                  string        `protobuf:"bytes,59,opt,name=id,json=id,proto3" json:"id"`
}

func (x *MediateList) Reset() {
	*x = MediateList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediateList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediateList) ProtoMessage() {}

func (x *MediateList) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediateList.ProtoReflect.Descriptor instead.
func (*MediateList) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{6}
}

func (x *MediateList) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *MediateList) GetCategories() []*Category {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *MediateList) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *MediateList) GetTranslanguage() string {
	if x != nil {
		return x.Translanguage
	}
	return ""
}

func (x *MediateList) GetIsChinese() bool {
	if x != nil {
		return x.IsChinese
	}
	return false
}

func (x *MediateList) GetOriginalTitle() string {
	if x != nil {
		return x.OriginalTitle
	}
	return ""
}

func (x *MediateList) GetOriginalContent() string {
	if x != nil {
		return x.OriginalContent
	}
	return ""
}

func (x *MediateList) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MediateList) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MediateList) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *MediateList) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *MediateList) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *MediateList) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *MediateList) GetClosetime() string {
	if x != nil {
		return x.Closetime
	}
	return ""
}

func (x *MediateList) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *MediateList) GetImages() []*Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *MediateList) GetTopicImage() string {
	if x != nil {
		return x.TopicImage
	}
	return ""
}

func (x *MediateList) GetForwardCount() int32 {
	if x != nil {
		return x.ForwardCount
	}
	return 0
}

func (x *MediateList) GetCommentCount() int32 {
	if x != nil {
		return x.CommentCount
	}
	return 0
}

func (x *MediateList) GetApplaudCount() int32 {
	if x != nil {
		return x.ApplaudCount
	}
	return 0
}

func (x *MediateList) GetViewCount() int32 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *MediateList) GetForwardCountVisible() bool {
	if x != nil {
		return x.ForwardCountVisible
	}
	return false
}

func (x *MediateList) GetCommentCountVisible() bool {
	if x != nil {
		return x.CommentCountVisible
	}
	return false
}

func (x *MediateList) GetApplaudCountVisible() bool {
	if x != nil {
		return x.ApplaudCountVisible
	}
	return false
}

func (x *MediateList) GetViewCountVisible() bool {
	if x != nil {
		return x.ViewCountVisible
	}
	return false
}

func (x *MediateList) GetHasImage() bool {
	if x != nil {
		return x.HasImage
	}
	return false
}

func (x *MediateList) GetIsResolved() bool {
	if x != nil {
		return x.IsResolved
	}
	return false
}

func (x *MediateList) GetIsResolvedByUser() bool {
	if x != nil {
		return x.IsResolvedByUser
	}
	return false
}

func (x *MediateList) GetIsApplaud() bool {
	if x != nil {
		return x.IsApplaud
	}
	return false
}

func (x *MediateList) GetStatus() AuditStatus {
	if x != nil {
		return x.Status
	}
	return AuditStatus_Topic_All
}

func (x *MediateList) GetIsCommentClosed() bool {
	if x != nil {
		return x.IsCommentClosed
	}
	return false
}

func (x *MediateList) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

func (x *MediateList) GetShare() string {
	if x != nil {
		return x.Share
	}
	return ""
}

func (x *MediateList) GetHasNewAppend() bool {
	if x != nil {
		return x.HasNewAppend
	}
	return false
}

func (x *MediateList) GetAppendAuditStatus() MediateStatus {
	if x != nil {
		return x.AppendAuditStatus
	}
	return MediateStatus_Mediate_All
}

func (x *MediateList) GetWikiTimestamp() int64 {
	if x != nil {
		return x.WikiTimestamp
	}
	return 0
}

func (x *MediateList) GetWikiTimestampms() int64 {
	if x != nil {
		return x.WikiTimestampms
	}
	return 0
}

func (x *MediateList) GetTopicViewCount() int32 {
	if x != nil {
		return x.TopicViewCount
	}
	return 0
}

func (x *MediateList) GetIsBoutique() bool {
	if x != nil {
		return x.IsBoutique
	}
	return false
}

func (x *MediateList) GetCountDownText() string {
	if x != nil {
		return x.CountDownText
	}
	return ""
}

func (x *MediateList) GetExpireTime() string {
	if x != nil {
		return x.ExpireTime
	}
	return ""
}

func (x *MediateList) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *MediateList) GetObjectLabel() string {
	if x != nil {
		return x.ObjectLabel
	}
	return ""
}

func (x *MediateList) GetAmountLabel() string {
	if x != nil {
		return x.AmountLabel
	}
	return ""
}

func (x *MediateList) GetCategoryLabel() string {
	if x != nil {
		return x.CategoryLabel
	}
	return ""
}

func (x *MediateList) GetRequirementLabel() string {
	if x != nil {
		return x.RequirementLabel
	}
	return ""
}

func (x *MediateList) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

func (x *MediateList) GetAnnoation() string {
	if x != nil {
		return x.Annoation
	}
	return ""
}

func (x *MediateList) GetNewColor() string {
	if x != nil {
		return x.NewColor
	}
	return ""
}

func (x *MediateList) GetElapsed() string {
	if x != nil {
		return x.Elapsed
	}
	return ""
}

func (x *MediateList) GetCloseLabel() string {
	if x != nil {
		return x.CloseLabel
	}
	return ""
}

func (x *MediateList) GetAppendStatus() MediateStatus {
	if x != nil {
		return x.AppendStatus
	}
	return MediateStatus_Mediate_All
}

func (x *MediateList) GetStamp() string {
	if x != nil {
		return x.Stamp
	}
	return ""
}

func (x *MediateList) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *MediateList) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *MediateList) GetAggInfo() *AggInfo {
	if x != nil {
		return x.AggInfo
	}
	return nil
}

func (x *MediateList) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *MediateList) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *MediateList) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetMediateDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        string   `protobuf:"bytes,1,opt,name=code,json=name,proto3" json:"code"`
	CountryCode string   `protobuf:"bytes,2,opt,name=countryCode,json=countryCode,proto3" json:"countryCode"`
	Uid         string   `protobuf:"bytes,3,opt,name=uid,json=uid,proto3" json:"uid"`
	Platform    Platform `protobuf:"varint,4,opt,name=platform,json=platform,proto3,enum=api.forum.v1.Platform" json:"platform"`
	PageIndex   int32    `protobuf:"varint,5,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize    int32    `protobuf:"varint,6,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
	IsApp       bool     `protobuf:"varint,7,opt,name=isApp,json=isApp,proto3" json:"isApp"`
}

func (x *GetMediateDetailRequest) Reset() {
	*x = GetMediateDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediateDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediateDetailRequest) ProtoMessage() {}

func (x *GetMediateDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediateDetailRequest.ProtoReflect.Descriptor instead.
func (*GetMediateDetailRequest) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{7}
}

func (x *GetMediateDetailRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetMediateDetailRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *GetMediateDetailRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *GetMediateDetailRequest) GetPlatform() Platform {
	if x != nil {
		return x.Platform
	}
	return Platform_P_Fxeye
}

func (x *GetMediateDetailRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetMediateDetailRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetMediateDetailRequest) GetIsApp() bool {
	if x != nil {
		return x.IsApp
	}
	return false
}

type GetMediateDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code               string          `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
	AppendStatus       MediateStatus   `protobuf:"varint,2,opt,name=append_status,json=append_status,proto3,enum=api.forum.v1.MediateStatus" json:"append_status"`
	CountryName        string          `protobuf:"bytes,3,opt,name=country_name,json=country_name,proto3" json:"country_name"`
	Flag               string          `protobuf:"bytes,4,opt,name=flag,json=flag,proto3" json:"flag"`
	Feedback           string          `protobuf:"bytes,5,opt,name=feedback,json=feedback,proto3" json:"feedback"`
	OriginTitle        string          `protobuf:"bytes,6,opt,name=originTitle,json=originTitle,proto3" json:"originTitle"`
	Title              string          `protobuf:"bytes,7,opt,name=title,json=title,proto3" json:"title"`
	ObjectLabel        string          `protobuf:"bytes,8,opt,name=object_label,json=object_label,proto3" json:"object_label"`
	CategoryLabel      string          `protobuf:"bytes,9,opt,name=category_label,json=category_label,proto3" json:"category_label"`
	RequirementLabel   string          `protobuf:"bytes,10,opt,name=requirement_label,json=requirement_label,proto3" json:"requirement_label"`
	AmountLabel        string          `protobuf:"bytes,11,opt,name=amount_label,json=amount_label,proto3" json:"amount_label"`
	CloseLabel         string          `protobuf:"bytes,12,opt,name=close_label,json=close_label,proto3" json:"close_label"`
	AccountLabel       string          `protobuf:"bytes,13,opt,name=account_label,json=account_label,proto3" json:"account_label"`
	NameLabel          string          `protobuf:"bytes,14,opt,name=name_label,json=name_label,proto3" json:"name_label"`
	PhoneLabel         string          `protobuf:"bytes,15,opt,name=phone_label,json=phone_label,proto3" json:"phone_label"`
	EmailLabel         string          `protobuf:"bytes,16,opt,name=email_label,json=email_label,proto3" json:"email_label"`
	Category           string          `protobuf:"bytes,17,opt,name=category,json=category,proto3" json:"category"`
	Requirement        string          `protobuf:"bytes,18,opt,name=requirement,json=requirement,proto3" json:"requirement"`
	Amount             string          `protobuf:"bytes,19,opt,name=amount,json=amount,proto3" json:"amount"`
	Symbol             string          `protobuf:"bytes,20,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	Threecode          string          `protobuf:"bytes,21,opt,name=threecode,json=threecode,proto3" json:"threecode"`
	Account            string          `protobuf:"bytes,22,opt,name=account,json=account,proto3" json:"account"`
	Name               string          `protobuf:"bytes,23,opt,name=name,json=name,proto3" json:"name"`
	Phone              string          `protobuf:"bytes,24,opt,name=phone,json=phone,proto3" json:"phone"`
	Email              string          `protobuf:"bytes,25,opt,name=email,json=email,proto3" json:"email"`
	Stamp              string          `protobuf:"bytes,26,opt,name=stamp,json=stamp,proto3" json:"stamp"`
	Share              string          `protobuf:"bytes,27,opt,name=share,json=share,proto3" json:"share"`
	Appends            []*MediateReply `protobuf:"bytes,28,rep,name=appends,json=appends,proto3" json:"appends"`
	Total              int32           `protobuf:"varint,29,opt,name=total,json=total,proto3" json:"total"`
	Banner             *MediateReply   `protobuf:"bytes,30,opt,name=banner,json=banner,proto3" json:"banner"`
	Statement          string          `protobuf:"bytes,31,opt,name=statement,json=statement,proto3" json:"statement"`
	Time               string          `protobuf:"bytes,32,opt,name=time,json=time,proto3" json:"time"`
	WikiTimestamp      string          `protobuf:"bytes,33,opt,name=wiki_timestamp,json=wiki_timestamp,proto3" json:"wiki_timestamp"`
	IsAppendVisible    string          `protobuf:"bytes,34,opt,name=is_append_visible,json=is_append_visible,proto3" json:"is_append_visible"`
	IsExtrainfoVisible string          `protobuf:"bytes,35,opt,name=is_extrainfo_visible,json=is_extrainfo_visible,proto3" json:"is_extrainfo_visible"`
	Elapsed            string          `protobuf:"bytes,36,opt,name=elapsed,json=elapsed,proto3" json:"elapsed"`
	Annoation          string          `protobuf:"bytes,37,opt,name=annoation,json=annoation,proto3" json:"annoation"`
	Color              string          `protobuf:"bytes,38,opt,name=color,json=color,proto3" json:"color"`
	NewColor           string          `protobuf:"bytes,39,opt,name=newColor,json=newColor,proto3" json:"newColor"`
	CountDownText      string          `protobuf:"bytes,40,opt,name=countDownText,json=countDownText,proto3" json:"countDownText"`
	TraderCode         string          `protobuf:"bytes,41,opt,name=traderCode,json=traderCode,proto3" json:"traderCode"`
	UserId             string          `protobuf:"bytes,42,opt,name=userId,json=userId,proto3" json:"userId"`
	Id                 string          `protobuf:"bytes,43,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetMediateDetailReply) Reset() {
	*x = GetMediateDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMediateDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMediateDetailReply) ProtoMessage() {}

func (x *GetMediateDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMediateDetailReply.ProtoReflect.Descriptor instead.
func (*GetMediateDetailReply) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{8}
}

func (x *GetMediateDetailReply) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *GetMediateDetailReply) GetAppendStatus() MediateStatus {
	if x != nil {
		return x.AppendStatus
	}
	return MediateStatus_Mediate_All
}

func (x *GetMediateDetailReply) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *GetMediateDetailReply) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *GetMediateDetailReply) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

func (x *GetMediateDetailReply) GetOriginTitle() string {
	if x != nil {
		return x.OriginTitle
	}
	return ""
}

func (x *GetMediateDetailReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetMediateDetailReply) GetObjectLabel() string {
	if x != nil {
		return x.ObjectLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetCategoryLabel() string {
	if x != nil {
		return x.CategoryLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetRequirementLabel() string {
	if x != nil {
		return x.RequirementLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetAmountLabel() string {
	if x != nil {
		return x.AmountLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetCloseLabel() string {
	if x != nil {
		return x.CloseLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetAccountLabel() string {
	if x != nil {
		return x.AccountLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetNameLabel() string {
	if x != nil {
		return x.NameLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetPhoneLabel() string {
	if x != nil {
		return x.PhoneLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetEmailLabel() string {
	if x != nil {
		return x.EmailLabel
	}
	return ""
}

func (x *GetMediateDetailReply) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *GetMediateDetailReply) GetRequirement() string {
	if x != nil {
		return x.Requirement
	}
	return ""
}

func (x *GetMediateDetailReply) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GetMediateDetailReply) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GetMediateDetailReply) GetThreecode() string {
	if x != nil {
		return x.Threecode
	}
	return ""
}

func (x *GetMediateDetailReply) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *GetMediateDetailReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetMediateDetailReply) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *GetMediateDetailReply) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetMediateDetailReply) GetStamp() string {
	if x != nil {
		return x.Stamp
	}
	return ""
}

func (x *GetMediateDetailReply) GetShare() string {
	if x != nil {
		return x.Share
	}
	return ""
}

func (x *GetMediateDetailReply) GetAppends() []*MediateReply {
	if x != nil {
		return x.Appends
	}
	return nil
}

func (x *GetMediateDetailReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetMediateDetailReply) GetBanner() *MediateReply {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *GetMediateDetailReply) GetStatement() string {
	if x != nil {
		return x.Statement
	}
	return ""
}

func (x *GetMediateDetailReply) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *GetMediateDetailReply) GetWikiTimestamp() string {
	if x != nil {
		return x.WikiTimestamp
	}
	return ""
}

func (x *GetMediateDetailReply) GetIsAppendVisible() string {
	if x != nil {
		return x.IsAppendVisible
	}
	return ""
}

func (x *GetMediateDetailReply) GetIsExtrainfoVisible() string {
	if x != nil {
		return x.IsExtrainfoVisible
	}
	return ""
}

func (x *GetMediateDetailReply) GetElapsed() string {
	if x != nil {
		return x.Elapsed
	}
	return ""
}

func (x *GetMediateDetailReply) GetAnnoation() string {
	if x != nil {
		return x.Annoation
	}
	return ""
}

func (x *GetMediateDetailReply) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *GetMediateDetailReply) GetNewColor() string {
	if x != nil {
		return x.NewColor
	}
	return ""
}

func (x *GetMediateDetailReply) GetCountDownText() string {
	if x != nil {
		return x.CountDownText
	}
	return ""
}

func (x *GetMediateDetailReply) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *GetMediateDetailReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetMediateDetailReply) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type Category struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cid         string `protobuf:"bytes,1,opt,name=cid,json=cid,proto3" json:"cid"`
	Name        string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Icon        string `protobuf:"bytes,3,opt,name=icon,json=icon,proto3" json:"icon"`
	Iconwikibit string `protobuf:"bytes,4,opt,name=iconwikibit,json=iconwikibit,proto3" json:"iconwikibit"`
	Color       string `protobuf:"bytes,5,opt,name=color,json=color,proto3" json:"color"`
	NewColor    string `protobuf:"bytes,6,opt,name=newColor,json=newColor,proto3" json:"newColor"`
}

func (x *Category) Reset() {
	*x = Category{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Category) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Category) ProtoMessage() {}

func (x *Category) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Category.ProtoReflect.Descriptor instead.
func (*Category) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{9}
}

func (x *Category) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *Category) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Category) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *Category) GetIconwikibit() string {
	if x != nil {
		return x.Iconwikibit
	}
	return ""
}

func (x *Category) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *Category) GetNewColor() string {
	if x != nil {
		return x.NewColor
	}
	return ""
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Abbr   string `protobuf:"bytes,1,opt,name=abbr,json=abbr,proto3" json:"abbr"`
	Detail string `protobuf:"bytes,2,opt,name=detail,json=detail,proto3" json:"detail"`
	Width  string `protobuf:"bytes,3,opt,name=width,json=width,proto3" json:"width"`
	Height string `protobuf:"bytes,4,opt,name=height,json=height,proto3" json:"height"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{10}
}

func (x *Image) GetAbbr() string {
	if x != nil {
		return x.Abbr
	}
	return ""
}

func (x *Image) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *Image) GetWidth() string {
	if x != nil {
		return x.Width
	}
	return ""
}

func (x *Image) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

type AggInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code        string `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
	UserId      string `protobuf:"bytes,2,opt,name=userId,json=userId,proto3" json:"userId"`
	CountryCode string `protobuf:"bytes,3,opt,name=countryCode,json=countryCode,proto3" json:"countryCode"`
}

func (x *AggInfo) Reset() {
	*x = AggInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AggInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AggInfo) ProtoMessage() {}

func (x *AggInfo) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AggInfo.ProtoReflect.Descriptor instead.
func (*AggInfo) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{11}
}

func (x *AggInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *AggInfo) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AggInfo) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

type MediateReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            Owner    `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.forum.v1.Owner" json:"type"`
	Name            string   `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Avatar          string   `protobuf:"bytes,3,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Color           string   `protobuf:"bytes,4,opt,name=color,json=color,proto3" json:"color"`
	Tip             string   `protobuf:"bytes,5,opt,name=tip,json=tip,proto3" json:"tip"`
	Origin          string   `protobuf:"bytes,6,opt,name=origin,json=origin,proto3" json:"origin"`
	Translate       string   `protobuf:"bytes,7,opt,name=translate,json=translate,proto3" json:"translate"`
	Images          []*Image `protobuf:"bytes,8,rep,name=images,json=images,proto3" json:"images"`
	Createtimestamp int64    `protobuf:"varint,9,opt,name=createtimestamp,json=createtimestamp,proto3" json:"createtimestamp"`
	WikiTimestamp   int64    `protobuf:"varint,10,opt,name=wiki_timestamp,json=wiki_timestamp,proto3" json:"wiki_timestamp"`
	Flag            string   `protobuf:"bytes,11,opt,name=flag,json=flag,proto3" json:"flag"`
	CountryName     string   `protobuf:"bytes,12,opt,name=country_name,json=country_name,proto3" json:"country_name"`
	IsVisible       bool     `protobuf:"varint,13,opt,name=is_visible,json=is_visible,proto3" json:"is_visible"`
}

func (x *MediateReply) Reset() {
	*x = MediateReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_forum_v1_model_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MediateReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediateReply) ProtoMessage() {}

func (x *MediateReply) ProtoReflect() protoreflect.Message {
	mi := &file_forum_v1_model_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediateReply.ProtoReflect.Descriptor instead.
func (*MediateReply) Descriptor() ([]byte, []int) {
	return file_forum_v1_model_proto_rawDescGZIP(), []int{12}
}

func (x *MediateReply) GetType() Owner {
	if x != nil {
		return x.Type
	}
	return Owner_Reply_User
}

func (x *MediateReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MediateReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *MediateReply) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *MediateReply) GetTip() string {
	if x != nil {
		return x.Tip
	}
	return ""
}

func (x *MediateReply) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *MediateReply) GetTranslate() string {
	if x != nil {
		return x.Translate
	}
	return ""
}

func (x *MediateReply) GetImages() []*Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *MediateReply) GetCreatetimestamp() int64 {
	if x != nil {
		return x.Createtimestamp
	}
	return 0
}

func (x *MediateReply) GetWikiTimestamp() int64 {
	if x != nil {
		return x.WikiTimestamp
	}
	return 0
}

func (x *MediateReply) GetFlag() string {
	if x != nil {
		return x.Flag
	}
	return ""
}

func (x *MediateReply) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *MediateReply) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

var File_forum_v1_model_proto protoreflect.FileDescriptor

var file_forum_v1_model_proto_rawDesc = []byte{
	0x0a, 0x14, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75,
	0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xa1, 0x03,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x0b, 0x61, 0x75, 0x64,
	0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0xe7, 0x8a,
	0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0b, 0x61, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbd,
	0xe5, 0xae, 0xb6, 0xe7, 0xbc, 0x96, 0xe7, 0xa0, 0x81, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x51, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f,
	0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42,
	0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0xe4, 0xbc, 0xa0, 0x31,
	0xef, 0xbc, 0x8c, 0x77, 0x69, 0x6b, 0x69, 0x62, 0x69, 0x74, 0xe4, 0xbc, 0xa0, 0x32, 0x52, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x2f, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe9, 0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x73, 0x41, 0x70, 0x70, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0x61, 0x70, 0x70, 0xe8, 0xb0, 0x83, 0xe7, 0x94, 0xa8, 0x52, 0x05, 0x69, 0x73, 0x41, 0x70,
	0x70, 0x22, 0x9a, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x63,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe7, 0xbc, 0x96, 0xe7, 0xa0, 0x81, 0x52, 0x05, 0x63,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1,
	0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x2d, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4,
	0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x7c,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x80, 0xbb, 0xe6, 0x95,
	0xb0, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x42, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f,
	0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe6,
	0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x96, 0x0c, 0x0a,
	0x0b, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe7, 0xbc, 0x96, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x0c, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x6f, 0x72,
	0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0xa0,
	0x87, 0xe9, 0xa2, 0x98, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52,
	0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x2d, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xbf, 0xbb, 0xe8,
	0xaf, 0x91, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x31, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xbf, 0xbb,
	0xe8, 0xaf, 0x91, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x38, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b,
	0xbe, 0xe7, 0x89, 0x87, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3e, 0x0a, 0x0f,
	0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x85, 0xb3, 0xe9,
	0x97, 0xad, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x52, 0x0f, 0x63, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x29, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x39, 0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb,
	0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0x52, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x3d, 0x0a,
	0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d,
	0x83, 0xe9, 0x97, 0xae, 0xe9, 0xa2, 0x98, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0d, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x43, 0x0a, 0x10,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4,
	0xe6, 0x9d, 0x83, 0xe8, 0xa6, 0x81, 0xe6, 0xb1, 0x82, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52,
	0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x33, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4,
	0xe6, 0x9d, 0x83, 0xe8, 0xa6, 0x81, 0xe6, 0xb1, 0x82, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7,
	0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x97, 0xae, 0xe9, 0xa2, 0x98, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4, 0xa7, 0xe5,
	0xb8, 0x81, 0xe7, 0xac, 0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x12, 0x31, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb0, 0x83, 0xe8, 0xa7,
	0xa3, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x69, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0xe6, 0x88, 0xb3, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x29,
	0x0a, 0x09, 0x61, 0x6e, 0x6e, 0x6f, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0x52, 0x09,
	0x61, 0x6e, 0x6e, 0x6f, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8,
	0xa7, 0x92, 0xe6, 0xa0, 0x87, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe9, 0xa2, 0x9c, 0xe8, 0x89,
	0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x36, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe6, 0x96, 0xb0, 0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8d, 0xb0, 0xe7, 0xab, 0xa0, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x52, 0x05, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x62, 0x0a, 0x11, 0x61, 0x70, 0x70,
	0x65, 0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe5, 0xae,
	0xa1, 0xe6, 0xa0, 0xb8, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x11, 0x61, 0x70, 0x70, 0x65,
	0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08,
	0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x0c,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xb4, 0xa7, 0xe5, 0xb8, 0x81, 0x52,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a,
	0x07, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x07, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x40, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x4d, 0x69, 0x6c, 0x69, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a,
	0x15, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3,
	0xe6, 0xaf, 0xab, 0xe7, 0xa7, 0x92, 0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x4d, 0x69, 0x6c, 0x69, 0x22, 0x7c, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x21, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08,
	0x2a, 0x06, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x42, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0xef, 0x1a, 0x0a, 0x0b, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe7, 0xbc,
	0x96, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0x9d,
	0xe5, 0x85, 0x89, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8,
	0x96, 0xe5, 0xad, 0x90, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x12, 0x37, 0x0a, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x6e,
	0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x0d,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a,
	0x09, 0x69, 0x73, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe4, 0xb8, 0xad,
	0xe5, 0x9b, 0xbd, 0x52, 0x09, 0x69, 0x73, 0x43, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x65, 0x12, 0x37,
	0x0a, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5,
	0xad, 0x90, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x0d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69,
	0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe5, 0x86, 0x85,
	0xe5, 0xae, 0xb9, 0x52, 0x0f, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90,
	0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x31, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb8, 0x96, 0xe5, 0xad,
	0x90, 0xe7, 0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a,
	0x2a, 0x18, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe5, 0x9b,
	0xbd, 0xe5, 0xae, 0xb6, 0xe7, 0xbc, 0x96, 0xe7, 0xa0, 0x81, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe5,
	0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe5, 0x9b, 0xbd,
	0xe5, 0xae, 0xb6, 0xe5, 0x9b, 0xbd, 0xe6, 0x97, 0x97, 0x52, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x12,
	0x25, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x85, 0xb3, 0xe9, 0x97, 0xad, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x63, 0x6c,
	0x6f, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x19, 0x92, 0x41, 0x16, 0x2a, 0x14, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x96, 0x49, 0x50, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x38, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b,
	0xbe, 0xe7, 0x89, 0x87, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x0a,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9b, 0x9d, 0xe5, 0x85, 0x89, 0xe7, 0xac, 0xac,
	0xe4, 0xb8, 0x80, 0xe5, 0xbc, 0xa0, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x0a, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x0c, 0x66, 0x6f, 0x72, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xbd, 0xac, 0xe5, 0x8f, 0x91, 0xe6, 0x95, 0xb0, 0x52, 0x0c,
	0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x0c,
	0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba, 0xe6,
	0x95, 0xb0, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x35, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x82, 0xb9,
	0xe8, 0xb5, 0x9e, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x61,
	0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x09, 0x76, 0x69, 0x65, 0x77, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x09, 0x76,
	0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x77,
	0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe8, 0xbd, 0xac, 0xe5,
	0x8f, 0x91, 0xe6, 0x95, 0xb0, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf, 0xe8, 0xa7,
	0x81, 0x52, 0x13, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0xaf, 0x84, 0xe8, 0xae, 0xba,
	0xe6, 0x95, 0xb0, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf, 0xe4, 0xbb, 0xa5, 0xe5,
	0xb0, 0xb1, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x61,
	0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe7, 0x82, 0xb9, 0xe8, 0xb5,
	0x9e, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf,
	0xe8, 0xa7, 0x81, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x46, 0x0a, 0x10, 0x76, 0x69, 0x65, 0x77,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe6,
	0x95, 0xb0, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf, 0xe8, 0xa7, 0x81, 0x52, 0x10,
	0x76, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x12, 0x30, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6,
	0x9c, 0x89, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x08, 0x68, 0x61, 0x73, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf,
	0xe5, 0x90, 0xa6, 0xe8, 0xa7, 0xa3, 0xe5, 0x86, 0xb3, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x12, 0x4f, 0x0a, 0x10, 0x69, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x6c,
	0x76, 0x65, 0x64, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe7, 0x94, 0xb1, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x82, 0xb9, 0xe5, 0x87, 0xbb, 0xe5, 0xb7, 0xb2, 0xe8, 0xa7,
	0xa3, 0xe5, 0x86, 0xb3, 0x52, 0x10, 0x69, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64,
	0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x09, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c,
	0x61, 0x75, 0x64, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe7, 0x82, 0xb9, 0xe8, 0xb5, 0x9e, 0x52, 0x09, 0x69, 0x73,
	0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x12, 0x44, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f,
	0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0xa1, 0xe6, 0x89, 0xb9, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a,
	0x0f, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xaf, 0x84,
	0xe8, 0xae, 0xba, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x85, 0xb3, 0xe9, 0x97, 0xad, 0x52,
	0x0f, 0x49, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x12, 0x27, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x8f, 0x8d, 0xe9, 0xa6, 0x88, 0x52,
	0x08, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5,
	0x88, 0x86, 0xe4, 0xba, 0xab, 0xe9, 0xa1, 0xb5, 0x52, 0x05, 0x73, 0x68, 0x61, 0x72, 0x65, 0x12,
	0x3e, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe6, 0x9c, 0x80, 0xe6, 0x96, 0xb0, 0xe5, 0x9b, 0x9e, 0xe5, 0xa4,
	0x8d, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x4e, 0x65, 0x77, 0x41, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x12,
	0x62, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf,
	0xbd, 0xe9, 0x97, 0xae, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0x52, 0x11, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x0d, 0x77, 0x69, 0x6b, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a,
	0x0f, 0xe5, 0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3,
	0x52, 0x0d, 0x77, 0x69, 0x6b, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x50, 0x0a, 0x0f, 0x77, 0x69, 0x6b, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x6d, 0x73, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x42, 0x26, 0x92, 0x41, 0x23, 0x2a, 0x21, 0xe5,
	0x8f, 0x91, 0xe5, 0xb8, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0xef, 0xbc,
	0x88, 0xe7, 0xb2, 0xbe, 0xe7, 0xa1, 0xae, 0xe5, 0x88, 0xb0, 0xe6, 0xaf, 0xab, 0xe7, 0xa7, 0x92,
	0x52, 0x0f, 0x77, 0x69, 0x6b, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x6d,
	0x73, 0x12, 0x3c, 0x0a, 0x0e, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe5, 0xb8, 0x96, 0xe5, 0xad, 0x90, 0xe6, 0xb5, 0x8f, 0xe8, 0xa7, 0x88, 0xe6, 0x95, 0xb0, 0x52,
	0x0e, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x31, 0x0a, 0x0a, 0x69, 0x73, 0x42, 0x6f, 0x75, 0x74, 0x69, 0x71, 0x75, 0x65, 0x18, 0x27, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6,
	0xe7, 0xb2, 0xbe, 0xe5, 0x93, 0x81, 0x52, 0x0a, 0x69, 0x73, 0x42, 0x6f, 0x75, 0x74, 0x69, 0x71,
	0x75, 0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe5, 0x80, 0x92, 0xe8, 0xae, 0xa1, 0xe6, 0x97, 0xb6, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0x52,
	0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x31,
	0x0a, 0x0a, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0x87, 0xe6, 0x9c, 0x9f, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x29, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x87,
	0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0b,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x2b, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe5, 0xaf,
	0xb9, 0xe8, 0xb1, 0xa1, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x39, 0x0a, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x3d, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x97, 0xae, 0xe9, 0xa2, 0x98, 0xe6, 0xa0, 0x87, 0xe7,
	0xad, 0xbe, 0x52, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x43, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe8, 0xa6, 0x81, 0xe6, 0xb1, 0x82, 0xe6, 0xa0,
	0x87, 0xe7, 0xad, 0xbe, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x33, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe8, 0xa6, 0x81, 0xe6, 0xb1, 0x82, 0x52, 0x0b,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x09, 0x61,
	0x6e, 0x6e, 0x6f, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0x52, 0x09, 0x61, 0x6e, 0x6e,
	0x6f, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8,
	0xa7, 0x92, 0xe6, 0xa0, 0x87, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe9, 0xa2, 0x9c, 0xe8, 0x89,
	0xb2, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2b, 0x0a, 0x07, 0x65,
	0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52,
	0x07, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73,
	0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x52, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f,
	0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d,
	0x83, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x35,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8d, 0xb0, 0xe7, 0xab,
	0xa0, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x05, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x3b,
	0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4,
	0xa7, 0xe5, 0xb8, 0x81, 0xe7, 0xac, 0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x31, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x37, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xb4, 0xa7, 0xe5, 0xb8, 0x81, 0x52,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x7c,
	0x0a, 0x07, 0x61, 0x67, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x38, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x67, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x4b, 0x92, 0x41, 0x48, 0x2a, 0x46, 0xe8, 0x81, 0x9a,
	0xe5, 0x90, 0x88, 0xe5, 0xb1, 0x82, 0xe7, 0xba, 0xa6, 0xe5, 0xae, 0x9a, 0xe6, 0xa8, 0xa1, 0xe5,
	0x9e, 0x8b, 0x20, 0xe7, 0x94, 0xb1, 0xe4, 0xb8, 0x8b, 0xe6, 0xb8, 0xb8, 0xe6, 0x8e, 0xa5, 0xe5,
	0x8f, 0xa3, 0xe5, 0xa1, 0xab, 0xe5, 0x86, 0x99, 0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe8, 0xa1,
	0xa5, 0xe5, 0x87, 0xba, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe7, 0x9a, 0x84, 0xe4, 0xb8, 0xbb,
	0xe9, 0x94, 0xae, 0x52, 0x07, 0x61, 0x67, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0a,
	0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe5, 0xaf, 0xb9,
	0xe8, 0xb1, 0xa1, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x3b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x69,
	0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf0, 0x02, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x25, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe7, 0xbc, 0x96, 0xe7,
	0xa0, 0x81, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe7, 0xbc, 0x96, 0xe7, 0xa0, 0x81,
	0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x51, 0x0a, 0x08,
	0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0x77, 0x69, 0x6b,
	0x69, 0x66, 0x78, 0xe4, 0xbc, 0xa0, 0x31, 0xef, 0xbc, 0x8c, 0x77, 0x69, 0x6b, 0x69, 0x62, 0x69,
	0x74, 0xe4, 0xbc, 0xa0, 0x32, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12,
	0x2f, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe9,
	0xa1, 0xb5, 0xe7, 0xa0, 0x81, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x2a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe6,
	0x95, 0xb0, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2a, 0x0a, 0x05,
	0x69, 0x73, 0x41, 0x70, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0x61, 0x70, 0x70, 0xe8, 0xb0, 0x83, 0xe7, 0x94,
	0xa8, 0x52, 0x05, 0x69, 0x73, 0x41, 0x70, 0x70, 0x22, 0xe2, 0x11, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0xbc, 0x96, 0xe5, 0x8f, 0xb7, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x54, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb,
	0xb4, 0xe6, 0x9d, 0x83, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x65,
	0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbd, 0xe6, 0x97, 0x97, 0x52, 0x04, 0x66, 0x6c, 0x61,
	0x67, 0x12, 0x2d, 0x0a, 0x08, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0xa1, 0xe6, 0xa0, 0xb8,
	0xe5, 0x8f, 0x8d, 0xe9, 0xa6, 0x88, 0x52, 0x08, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x12, 0x30, 0x0a, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x8e, 0x9f, 0xe6,
	0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14,
	0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0xe6, 0xa0,
	0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0c, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x3f, 0x0a, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x97, 0xae, 0xe9, 0xa2, 0x98, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0x52, 0x0e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x45, 0x0a, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe8, 0xa6, 0x81, 0xe6, 0xb1,
	0x82, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x11, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x3b, 0x0a, 0x0c, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x87, 0x91,
	0xe9, 0xa2, 0x9d, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0c, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x39, 0x0a, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x3d, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe8, 0xb4, 0xa6, 0xe5, 0x8f, 0xb7, 0xe6, 0xa0, 0x87, 0xe7,
	0xad, 0xbe, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x37, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xbc, 0x80, 0xe6,
	0x88, 0xb7, 0xe5, 0xa7, 0x93, 0xe5, 0x90, 0x8d, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0a,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x3c, 0x0a, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0xbc, 0x80, 0xe6, 0x88, 0xb7, 0xe6, 0x89, 0x8b, 0xe6,
	0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x39, 0x0a, 0x0b, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe5, 0xbc, 0x80, 0xe6, 0x88, 0xb7, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1,
	0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x12, 0x2d, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0xb4, 0xe6,
	0x9d, 0x83, 0xe9, 0x97, 0xae, 0xe9, 0xa2, 0x98, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x33, 0x0a, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb,
	0xb4, 0xe6, 0x9d, 0x83, 0xe8, 0xa6, 0x81, 0xe6, 0xb1, 0x82, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb,
	0xb4, 0xe6, 0x9d, 0x83, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4, 0xa7, 0xe5, 0xb8, 0x81, 0xe7,
	0xac, 0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x33, 0x0a,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x15, 0x92, 0x41, 0x12, 0x2a, 0x10, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe4, 0xb8, 0x89,
	0xe4, 0xbd, 0x8d, 0x63, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x65, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93,
	0xe8, 0xb4, 0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe6, 0x88, 0xb7, 0xe5, 0xa7, 0x93, 0xe5, 0x90, 0x8d,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0xbc, 0x80, 0xe6,
	0x88, 0xb7, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe6, 0x88, 0xb7, 0xe9, 0x82,
	0xae, 0xe7, 0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0x8d, 0xb0, 0xe7, 0xab, 0xa0, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x24, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x88, 0x86, 0xe4, 0xba, 0xab,
	0xe9, 0xa1, 0xb5, 0x52, 0x05, 0x73, 0x68, 0x61, 0x72, 0x65, 0x12, 0x41, 0x0a, 0x07, 0x61, 0x70,
	0x70, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b,
	0x9e, 0xe5, 0xa4, 0x8d, 0x52, 0x07, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x27, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0x9e, 0xe5, 0xa4, 0x8d, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x57, 0x0a, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72,
	0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe5, 0xb7, 0xb2, 0xe8, 0xa7, 0xa3, 0xe5,
	0x86, 0xb3, 0xe5, 0x92, 0x8c, 0xe6, 0x94, 0xbe, 0xe5, 0xbc, 0x83, 0xe8, 0xa7, 0xa3, 0xe5, 0x86,
	0xb3, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12,
	0x29, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0x94, 0xb3, 0xe6, 0x98, 0x8e, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe5, 0x88, 0x9b, 0xe5, 0xbb, 0xba, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x52,
	0x0e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x45, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x6c, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe5, 0x9b, 0x9e, 0xe5, 0xa4, 0x8d, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf,
	0xe8, 0xa7, 0x81, 0x52, 0x11, 0x69, 0x73, 0x5f, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x5f, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x23,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe8, 0xa1, 0xa5, 0xe5, 0x85,
	0x85, 0xe8, 0xb5, 0x84, 0xe6, 0x96, 0x99, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf,
	0xe8, 0xa7, 0x81, 0x52, 0x14, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x66,
	0x6f, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x2b, 0x0a, 0x07, 0x65, 0x6c, 0x61,
	0x70, 0x73, 0x65, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0xbb, 0xb4, 0xe6, 0x9d, 0x83, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x07, 0x65,
	0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x09, 0x61, 0x6e, 0x6e, 0x6f, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0x52, 0x09, 0x61, 0x6e, 0x6e, 0x6f, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xa7, 0x92, 0xe6, 0xa0, 0x87, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x36, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x27, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x96, 0xb0, 0xe8, 0xa7, 0x92, 0xe6,
	0xa0, 0x87, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x08,
	0x6e, 0x65, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x0d, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x6f, 0x77, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x80, 0x92, 0xe8, 0xae, 0xa1, 0xe6, 0x97, 0xb6, 0xe6,
	0x96, 0x87, 0xe6, 0x9c, 0xac, 0x52, 0x0d, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe5, 0xaf, 0xb9, 0xe8, 0xb1, 0xa1, 0x52, 0x0a, 0x74, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x22, 0x84, 0x02,
	0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x23, 0x0a, 0x03, 0x63, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88,
	0x86, 0xe7, 0xb1, 0xbb, 0xe7, 0xbc, 0x96, 0xe5, 0x8f, 0xb7, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12,
	0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe7, 0xb1,
	0xbb, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x30, 0x0a,
	0x0b, 0x69, 0x63, 0x6f, 0x6e, 0x77, 0x69, 0x6b, 0x69, 0x62, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0x62, 0x69, 0x74, 0xe5, 0x9b, 0xbe, 0xe6,
	0xa0, 0x87, 0x52, 0x0b, 0x69, 0x63, 0x6f, 0x6e, 0x77, 0x69, 0x6b, 0x69, 0x62, 0x69, 0x74, 0x12,
	0x27, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xe9, 0xa2, 0x9c, 0xe8, 0x89,
	0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe6, 0x96, 0xb0, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xa4, 0x01, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x22,
	0x0a, 0x04, 0x61, 0x62, 0x62, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe7, 0xbc, 0xa9, 0xe7, 0x95, 0xa5, 0xe5, 0x9b, 0xbe, 0x52, 0x04, 0x61, 0x62,
	0x62, 0x72, 0x12, 0x23, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xaf, 0xa6, 0xe7, 0xbb, 0x86, 0x52,
	0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe,
	0xe7, 0x89, 0x87, 0xe5, 0xae, 0xbd, 0xe5, 0xba, 0xa6, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe9, 0xab, 0x98,
	0xe5, 0xba, 0xa6, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0xc9, 0x01, 0x0a, 0x07,
	0x41, 0x67, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0x92, 0x41, 0x27, 0x2a, 0x25, 0xe4, 0xba, 0xa4, 0xe6,
	0x98, 0x93, 0xe5, 0x95, 0x86, 0xe3, 0x80, 0x81, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x95,
	0x86, 0xe3, 0x80, 0x81, 0xe5, 0x8f, 0x97, 0xe8, 0xaf, 0x84, 0xe6, 0x96, 0xb9, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x57,
	0x0a, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x35, 0x92, 0x41, 0x32, 0x2a, 0x30, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6,
	0x33, 0xe4, 0xbd, 0x8d, 0x63, 0x6f, 0x64, 0x65, 0x20, 0xe7, 0x94, 0xa8, 0xe4, 0xba, 0x8e, 0xe8,
	0xa1, 0xa5, 0xe5, 0x85, 0xa8, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0xe5, 0x92, 0x8c, 0xe5, 0x9b, 0xbd, 0xe6, 0x97, 0x97, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xf5, 0x05, 0x0a, 0x0c, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x78, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72,
	0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x4f, 0x92, 0x41, 0x4c,
	0x2a, 0x4a, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe5, 0x9b, 0x9e, 0xe5, 0xa4, 0x8d, 0xe6, 0x89,
	0x80, 0xe5, 0xb1, 0x9e, 0x2c, 0x30, 0x2d, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0xbf, 0xbd,
	0xe9, 0x97, 0xae, 0xef, 0xbc, 0x8c, 0x31, 0x2d, 0x77, 0x69, 0x6b, 0x69, 0x66, 0x78, 0xe5, 0xae,
	0x98, 0xe6, 0x96, 0xb9, 0xe5, 0x9b, 0x9e, 0xe5, 0xa4, 0x8d, 0x2c, 0x32, 0x2d, 0xe5, 0x85, 0xb3,
	0xe8, 0x81, 0x94, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe5, 0xa4, 0xb4, 0xe5,
	0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x2d, 0x0a, 0x05, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe9, 0xa2, 0x9c, 0xe8,
	0x89, 0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x29, 0x0a, 0x03, 0x74, 0x69, 0x70,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0xbd,
	0xe9, 0x97, 0xae, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52,
	0x03, 0x74, 0x69, 0x70, 0x12, 0x29, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0xbd, 0xe9, 0x97,
	0xae, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12,
	0x35, 0x0a, 0x09, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe7,
	0xbf, 0xbb, 0xe8, 0xaf, 0x91, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x09, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72,
	0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x06,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x0f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x1c, 0x92, 0x41, 0x19, 0x2a, 0x17, 0xe8, 0xbf, 0xbd, 0xe9, 0x97, 0xae, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x28, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x29, 0x52, 0x0f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x44,
	0x0a, 0x0e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1c, 0x92, 0x41, 0x19, 0x2a, 0x17, 0xe8, 0xbf, 0xbd,
	0xe9, 0x97, 0xae, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x28, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4,
	0xe6, 0x88, 0xb3, 0x29, 0x52, 0x0e, 0x77, 0x69, 0x6b, 0x69, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x1f, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0x9b, 0xbd, 0xe6, 0x97, 0x97, 0x52,
	0x04, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x35, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0c,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x0a,
	0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x98, 0xbe,
	0xe7, 0xa4, 0xba, 0x52, 0x0a, 0x69, 0x73, 0x5f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x2a,
	0x7b, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0d,
	0x0a, 0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x41, 0x6c, 0x6c, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x64,
	0x12, 0x12, 0x0a, 0x0d, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x10, 0xc8, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x46, 0x61,
	0x69, 0x6c, 0x10, 0x91, 0x03, 0x12, 0x12, 0x0a, 0x0d, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x5f, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x10, 0x97, 0x03, 0x12, 0x11, 0x0a, 0x0c, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x5f, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x10, 0x98, 0x03, 0x2a, 0x9c, 0x01, 0x0a,
	0x0d, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f,
	0x0a, 0x0b, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x41, 0x6c, 0x6c, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x50, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x10, 0x64, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x10, 0x6e, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x10, 0x70, 0x12, 0x13, 0x0a,
	0x0e, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x10,
	0xc8, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x5f, 0x41, 0x62,
	0x61, 0x6e, 0x64, 0x6f, 0x6e, 0x10, 0xac, 0x02, 0x12, 0x11, 0x0a, 0x0c, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x65, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x10, 0x91, 0x03, 0x2a, 0x3b, 0x0a, 0x05, 0x4f,
	0x77, 0x6e, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x55, 0x73,
	0x65, 0x72, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x57, 0x69,
	0x6b, 0x69, 0x66, 0x78, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x10, 0x02, 0x2a, 0x34, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x5f, 0x46, 0x78, 0x65, 0x79, 0x65, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x5f, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x10, 0x01, 0x12,
	0x0d, 0x0a, 0x09, 0x50, 0x5f, 0x57, 0x69, 0x6b, 0x69, 0x62, 0x69, 0x74, 0x10, 0x02, 0x32, 0xb2,
	0x06, 0x0a, 0x09, 0x4d, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa0, 0x01, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8, 0xb0,
	0x83, 0xe8, 0xa7, 0xa3, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0xb0, 0x83, 0xe8,
	0xa7, 0xa3, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01,
	0x2a, 0x22, 0x1c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x66, 0x6f, 0x72, 0x75, 0x6d,
	0x2f, 0x67, 0x65, 0x74, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12,
	0xa8, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x48, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x12, 0x12, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe8, 0xaf, 0xa6, 0xe6, 0x83,
	0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2f, 0x67, 0x65, 0x74, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x8e, 0x01, 0x0a, 0x09, 0x47,
	0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x12, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66,
	0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x47, 0x92, 0x41, 0x25, 0x0a, 0x06, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x12,
	0x1b, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x9c, 0x80, 0xe6, 0x96, 0xb0, 0xe7, 0x9a, 0x84,
	0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x66, 0x6f, 0x72, 0x75,
	0x6d, 0x2f, 0x67, 0x65, 0x74, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x12, 0xa6, 0x01, 0x0a, 0x10,
	0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f,
	0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x1c, 0x0a,
	0x06, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8,
	0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x66, 0x6f,
	0x72, 0x75, 0x6d, 0x2f, 0x67, 0x65, 0x74, 0x6d, 0x79, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x9c, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69,
	0x61, 0x74, 0x65, 0x73, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x79, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65,
	0x64, 0x69, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x43,
	0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0x12, 0x12, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe8, 0xb0, 0x83, 0xe8, 0xa7, 0xa3, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x66, 0x6f, 0x72, 0x75, 0x6d, 0x2f, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x64, 0x69, 0x61,
	0x74, 0x65, 0x73, 0x42, 0x11, 0x5a, 0x0f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x6f, 0x72, 0x75, 0x6d,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_forum_v1_model_proto_rawDescOnce sync.Once
	file_forum_v1_model_proto_rawDescData = file_forum_v1_model_proto_rawDesc
)

func file_forum_v1_model_proto_rawDescGZIP() []byte {
	file_forum_v1_model_proto_rawDescOnce.Do(func() {
		file_forum_v1_model_proto_rawDescData = protoimpl.X.CompressGZIP(file_forum_v1_model_proto_rawDescData)
	})
	return file_forum_v1_model_proto_rawDescData
}

var file_forum_v1_model_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_forum_v1_model_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_forum_v1_model_proto_goTypes = []interface{}{
	(AuditStatus)(0),                // 0: api.forum.v1.AuditStatus
	(MediateStatus)(0),              // 1: api.forum.v1.MediateStatus
	(Owner)(0),                      // 2: api.forum.v1.Owner
	(Platform)(0),                   // 3: api.forum.v1.Platform
	(*Request)(nil),                 // 4: api.forum.v1.Request
	(*GetMyMediateListRequest)(nil), // 5: api.forum.v1.GetMyMediateListRequest
	(*GetMediateInfoRequest)(nil),   // 6: api.forum.v1.GetMediateInfoRequest
	(*GetMediateInfoReply)(nil),     // 7: api.forum.v1.GetMediateInfoReply
	(*MediateInfo)(nil),             // 8: api.forum.v1.MediateInfo
	(*GetMediateListReply)(nil),     // 9: api.forum.v1.GetMediateListReply
	(*MediateList)(nil),             // 10: api.forum.v1.MediateList
	(*GetMediateDetailRequest)(nil), // 11: api.forum.v1.GetMediateDetailRequest
	(*GetMediateDetailReply)(nil),   // 12: api.forum.v1.GetMediateDetailReply
	(*Category)(nil),                // 13: api.forum.v1.Category
	(*Image)(nil),                   // 14: api.forum.v1.Image
	(*AggInfo)(nil),                 // 15: api.forum.v1.AggInfo
	(*MediateReply)(nil),            // 16: api.forum.v1.MediateReply
}
var file_forum_v1_model_proto_depIdxs = []int32{
	1,  // 0: api.forum.v1.GetMyMediateListRequest.auditStatus:type_name -> api.forum.v1.MediateStatus
	3,  // 1: api.forum.v1.GetMyMediateListRequest.platform:type_name -> api.forum.v1.Platform
	8,  // 2: api.forum.v1.GetMediateInfoReply.items:type_name -> api.forum.v1.MediateInfo
	14, // 3: api.forum.v1.MediateInfo.images:type_name -> api.forum.v1.Image
	1,  // 4: api.forum.v1.MediateInfo.appendAuditStatus:type_name -> api.forum.v1.MediateStatus
	10, // 5: api.forum.v1.GetMediateListReply.items:type_name -> api.forum.v1.MediateList
	13, // 6: api.forum.v1.MediateList.categories:type_name -> api.forum.v1.Category
	14, // 7: api.forum.v1.MediateList.images:type_name -> api.forum.v1.Image
	0,  // 8: api.forum.v1.MediateList.status:type_name -> api.forum.v1.AuditStatus
	1,  // 9: api.forum.v1.MediateList.appendAuditStatus:type_name -> api.forum.v1.MediateStatus
	1,  // 10: api.forum.v1.MediateList.appendStatus:type_name -> api.forum.v1.MediateStatus
	15, // 11: api.forum.v1.MediateList.aggInfo:type_name -> api.forum.v1.AggInfo
	3,  // 12: api.forum.v1.GetMediateDetailRequest.platform:type_name -> api.forum.v1.Platform
	1,  // 13: api.forum.v1.GetMediateDetailReply.append_status:type_name -> api.forum.v1.MediateStatus
	16, // 14: api.forum.v1.GetMediateDetailReply.appends:type_name -> api.forum.v1.MediateReply
	16, // 15: api.forum.v1.GetMediateDetailReply.banner:type_name -> api.forum.v1.MediateReply
	2,  // 16: api.forum.v1.MediateReply.type:type_name -> api.forum.v1.Owner
	14, // 17: api.forum.v1.MediateReply.images:type_name -> api.forum.v1.Image
	6,  // 18: api.forum.v1.MyService.GetMediateList:input_type -> api.forum.v1.GetMediateInfoRequest
	11, // 19: api.forum.v1.MyService.GetMediateDetail:input_type -> api.forum.v1.GetMediateDetailRequest
	4,  // 20: api.forum.v1.MyService.GetLatest:input_type -> api.forum.v1.Request
	5,  // 21: api.forum.v1.MyService.GetMyMediateList:input_type -> api.forum.v1.GetMyMediateListRequest
	5,  // 22: api.forum.v1.MyService.GetMediates:input_type -> api.forum.v1.GetMyMediateListRequest
	7,  // 23: api.forum.v1.MyService.GetMediateList:output_type -> api.forum.v1.GetMediateInfoReply
	12, // 24: api.forum.v1.MyService.GetMediateDetail:output_type -> api.forum.v1.GetMediateDetailReply
	9,  // 25: api.forum.v1.MyService.GetLatest:output_type -> api.forum.v1.GetMediateListReply
	9,  // 26: api.forum.v1.MyService.GetMyMediateList:output_type -> api.forum.v1.GetMediateListReply
	9,  // 27: api.forum.v1.MyService.GetMediates:output_type -> api.forum.v1.GetMediateListReply
	23, // [23:28] is the sub-list for method output_type
	18, // [18:23] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_forum_v1_model_proto_init() }
func file_forum_v1_model_proto_init() {
	if File_forum_v1_model_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_forum_v1_model_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMyMediateListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediateInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediateInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediateListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediateList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediateDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMediateDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Category); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AggInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_forum_v1_model_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MediateReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_forum_v1_model_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_forum_v1_model_proto_goTypes,
		DependencyIndexes: file_forum_v1_model_proto_depIdxs,
		EnumInfos:         file_forum_v1_model_proto_enumTypes,
		MessageInfos:      file_forum_v1_model_proto_msgTypes,
	}.Build()
	File_forum_v1_model_proto = out.File
	file_forum_v1_model_proto_rawDesc = nil
	file_forum_v1_model_proto_goTypes = nil
	file_forum_v1_model_proto_depIdxs = nil
}
