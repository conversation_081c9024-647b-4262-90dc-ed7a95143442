// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.0
// - protoc             v4.25.3
// source: user_profile/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServiceAddLabel = "/api.user_profile.v1.Service/AddLabel"
const OperationServiceAddRecall = "/api.user_profile.v1.Service/AddRecall"
const OperationServiceCountByLabel = "/api.user_profile.v1.Service/CountByLabel"
const OperationServiceCustomFilter = "/api.user_profile.v1.Service/CustomFilter"
const OperationServiceDeleteLabel = "/api.user_profile.v1.Service/DeleteLabel"
const OperationServiceDeleteRecall = "/api.user_profile.v1.Service/DeleteRecall"
const OperationServiceEditorList = "/api.user_profile.v1.Service/EditorList"
const OperationServiceFilterByLabel = "/api.user_profile.v1.Service/FilterByLabel"
const OperationServiceFilterLabels = "/api.user_profile.v1.Service/FilterLabels"
const OperationServiceGetByUserId = "/api.user_profile.v1.Service/GetByUserId"
const OperationServiceGetLabel = "/api.user_profile.v1.Service/GetLabel"
const OperationServiceGetRecallCoverUsers = "/api.user_profile.v1.Service/GetRecallCoverUsers"
const OperationServiceGetRecallExecRecords = "/api.user_profile.v1.Service/GetRecallExecRecords"
const OperationServiceGetRecallTouch = "/api.user_profile.v1.Service/GetRecallTouch"
const OperationServiceGetRecalls = "/api.user_profile.v1.Service/GetRecalls"
const OperationServiceGetUserProfile = "/api.user_profile.v1.Service/GetUserProfile"
const OperationServiceHealthy = "/api.user_profile.v1.Service/Healthy"
const OperationServiceLabelCountry = "/api.user_profile.v1.Service/LabelCountry"
const OperationServiceLabelLanguage = "/api.user_profile.v1.Service/LabelLanguage"
const OperationServiceListLabel = "/api.user_profile.v1.Service/ListLabel"
const OperationServicePushIdReport = "/api.user_profile.v1.Service/PushIdReport"
const OperationServiceSetRecall = "/api.user_profile.v1.Service/SetRecall"
const OperationServiceUpdateLabel = "/api.user_profile.v1.Service/UpdateLabel"
const OperationServiceUpdateLabelStatus = "/api.user_profile.v1.Service/UpdateLabelStatus"
const OperationServiceUserProfileField = "/api.user_profile.v1.Service/UserProfileField"

type ServiceHTTPServer interface {
	// AddLabel =============== 自定义标签 =========================
	// 新增自定义标签
	AddLabel(context.Context, *Label) (*AddLabelReply, error)
	// AddRecall =============== 召回任务 =========================
	// 新增召回
	AddRecall(context.Context, *Recall) (*AddRecallReply, error)
	// CountByLabel 根据标签获取用户数量
	CountByLabel(context.Context, *CountByLabelRequest) (*CountByLabelReply, error)
	// CustomFilter 按照标签筛选
	CustomFilter(context.Context, *CustomFilterRequest) (*CustomFilterReply, error)
	// DeleteLabel 删除自定义标签
	DeleteLabel(context.Context, *DeleteLabelRequest) (*DeleteLabelReply, error)
	// DeleteRecall 删除召回任务
	DeleteRecall(context.Context, *DeleteRecallRequest) (*DeleteRecallReply, error)
	// EditorList 编辑人列表
	EditorList(context.Context, *EditorListRequest) (*EditorListReply, error)
	// FilterByLabel 根据标签筛选用户
	FilterByLabel(context.Context, *FilterByLabelRequest) (*FilterByLabelReply, error)
	// FilterLabels 获取筛选标签
	FilterLabels(context.Context, *FilterLabelsRequest) (*FilterLabelsReply, error)
	// GetByUserId 根据用户ID查看用户画像
	GetByUserId(context.Context, *GetByUserIdRequest) (*UserProfile, error)
	// GetLabel 获取自定义标签详情
	GetLabel(context.Context, *GetLabelRequest) (*Label, error)
	// GetRecallCoverUsers 召回任务覆盖用户列表
	GetRecallCoverUsers(context.Context, *GetRecallCoverUsersRequest) (*GetRecallCoverUsersReply, error)
	// GetRecallExecRecords  // 获取召回详情
	//  rpc GetRecall(GetRecallRequest) returns (Recall) {
	//    option (google.api.http) = {get: "/v1/recall"};
	//    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取召回详情",tags: ["召回"]};
	//  }
	// 获取召回任务执行记录
	GetRecallExecRecords(context.Context, *GetRecallExecRecordsRequest) (*GetRecallExecRecordsReply, error)
	// GetRecallTouch 召回任务触达率
	GetRecallTouch(context.Context, *GetRecallTouchRequest) (*GetRecallTouchReply, error)
	// GetRecalls 获取召回列表
	GetRecalls(context.Context, *GetRecallsRequest) (*GetRecallsReply, error)
	// GetUserProfile 根据用户ID或者设备ID查看用户画像
	GetUserProfile(context.Context, *GetUserprofileRequest) (*UserProfile, error)
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// LabelCountry 标签国家
	LabelCountry(context.Context, *LabelCountryRequest) (*LabelCountryReply, error)
	// LabelLanguage 标签语言
	LabelLanguage(context.Context, *LabelLanguageRequest) (*LabelLanguageReply, error)
	// ListLabel 获取自定义标签列表
	ListLabel(context.Context, *ListLabelRequest) (*ListLabelReply, error)
	// PushIdReport 推送ID上报
	PushIdReport(context.Context, *PushIdReportRequest) (*PushIdReportReply, error)
	// SetRecall 设置召回任务状态
	SetRecall(context.Context, *SetRecallRequest) (*SetRecallReply, error)
	// UpdateLabel 更新自定义标签
	UpdateLabel(context.Context, *Label) (*UpdateLabelReply, error)
	// UpdateLabelStatus 更新自定义标签状态
	UpdateLabelStatus(context.Context, *UpdateLabelStatusRequest) (*UpdateLabelStatusReply, error)
	// UserProfileField =============== 用户画像 =========================
	// 用户画像支持的字段列表
	UserProfileField(context.Context, *UserProfileFieldRequest) (*UserProfileFieldReply, error)
}

func RegisterServiceHTTPServer(s *http.Server, srv ServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/healthz", _Service_Healthy8_HTTP_Handler(srv))
	r.POST("/v1/push_report", _Service_PushIdReport0_HTTP_Handler(srv))
	r.GET("/v1/push_labels", _Service_FilterLabels0_HTTP_Handler(srv))
	r.POST("/v1/push_filter", _Service_CustomFilter0_HTTP_Handler(srv))
	r.GET("/v1/profile/fields", _Service_UserProfileField0_HTTP_Handler(srv))
	r.GET("/v1/user/{user_id}/profile", _Service_GetByUserId0_HTTP_Handler(srv))
	r.GET("/v1/profile", _Service_GetUserProfile0_HTTP_Handler(srv))
	r.POST("/v1/label", _Service_AddLabel0_HTTP_Handler(srv))
	r.GET("/v1/label/{id}", _Service_GetLabel0_HTTP_Handler(srv))
	r.PUT("/v1/label/{id}", _Service_UpdateLabel0_HTTP_Handler(srv))
	r.PUT("/v1/label/{id}/status", _Service_UpdateLabelStatus0_HTTP_Handler(srv))
	r.DELETE("/v1/label/{id}", _Service_DeleteLabel0_HTTP_Handler(srv))
	r.GET("/v1/label", _Service_ListLabel0_HTTP_Handler(srv))
	r.GET("/v1/label/{id}/count", _Service_CountByLabel0_HTTP_Handler(srv))
	r.GET("/v1/label/{id}/filter", _Service_FilterByLabel0_HTTP_Handler(srv))
	r.GET("/v1/label/editor/list", _Service_EditorList0_HTTP_Handler(srv))
	r.GET("/v1/label/{id}/countries", _Service_LabelCountry0_HTTP_Handler(srv))
	r.GET("/v1/label/{id}/languages", _Service_LabelLanguage0_HTTP_Handler(srv))
	r.POST("/v1/recall", _Service_AddRecall0_HTTP_Handler(srv))
	r.GET("/v1/recalls", _Service_GetRecalls0_HTTP_Handler(srv))
	r.GET("/v1/recall/exec_records", _Service_GetRecallExecRecords0_HTTP_Handler(srv))
	r.DELETE("/v1/recall", _Service_DeleteRecall0_HTTP_Handler(srv))
	r.POST("/v1/recall/set_status", _Service_SetRecall0_HTTP_Handler(srv))
	r.GET("/v1/recall/touch", _Service_GetRecallTouch0_HTTP_Handler(srv))
	r.GET("/v1/recall/cover_users", _Service_GetRecallCoverUsers0_HTTP_Handler(srv))
}

func _Service_Healthy8_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceHealthy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Healthy(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.HealthyReply)
		return ctx.Result(200, reply)
	}
}

func _Service_PushIdReport0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PushIdReportRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServicePushIdReport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PushIdReport(ctx, req.(*PushIdReportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PushIdReportReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FilterLabels0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FilterLabelsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFilterLabels)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FilterLabels(ctx, req.(*FilterLabelsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FilterLabelsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CustomFilter0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CustomFilterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCustomFilter)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CustomFilter(ctx, req.(*CustomFilterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CustomFilterReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UserProfileField0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserProfileFieldRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUserProfileField)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserProfileField(ctx, req.(*UserProfileFieldRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserProfileFieldReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetByUserId0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetByUserIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetByUserId)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetByUserId(ctx, req.(*GetByUserIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserProfile)
		return ctx.Result(200, reply)
	}
}

func _Service_GetUserProfile0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserprofileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetUserProfile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserProfile(ctx, req.(*GetUserprofileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserProfile)
		return ctx.Result(200, reply)
	}
}

func _Service_AddLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Label
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceAddLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddLabel(ctx, req.(*Label))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddLabelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLabelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLabel(ctx, req.(*GetLabelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Label)
		return ctx.Result(200, reply)
	}
}

func _Service_UpdateLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Label
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUpdateLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLabel(ctx, req.(*Label))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateLabelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_UpdateLabelStatus0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateLabelStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceUpdateLabelStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateLabelStatus(ctx, req.(*UpdateLabelStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateLabelStatusReply)
		return ctx.Result(200, reply)
	}
}

func _Service_DeleteLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteLabelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceDeleteLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteLabel(ctx, req.(*DeleteLabelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteLabelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_ListLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLabelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceListLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLabel(ctx, req.(*ListLabelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLabelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_CountByLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CountByLabelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceCountByLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CountByLabel(ctx, req.(*CountByLabelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CountByLabelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_FilterByLabel0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FilterByLabelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceFilterByLabel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FilterByLabel(ctx, req.(*FilterByLabelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FilterByLabelReply)
		return ctx.Result(200, reply)
	}
}

func _Service_EditorList0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EditorListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceEditorList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.EditorList(ctx, req.(*EditorListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EditorListReply)
		return ctx.Result(200, reply)
	}
}

func _Service_LabelCountry0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LabelCountryRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceLabelCountry)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LabelCountry(ctx, req.(*LabelCountryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LabelCountryReply)
		return ctx.Result(200, reply)
	}
}

func _Service_LabelLanguage0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in LabelLanguageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceLabelLanguage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LabelLanguage(ctx, req.(*LabelLanguageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*LabelLanguageReply)
		return ctx.Result(200, reply)
	}
}

func _Service_AddRecall0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Recall
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceAddRecall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddRecall(ctx, req.(*Recall))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddRecallReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRecalls0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRecallsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRecalls)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRecalls(ctx, req.(*GetRecallsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRecallsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRecallExecRecords0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRecallExecRecordsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRecallExecRecords)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRecallExecRecords(ctx, req.(*GetRecallExecRecordsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRecallExecRecordsReply)
		return ctx.Result(200, reply)
	}
}

func _Service_DeleteRecall0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteRecallRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceDeleteRecall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteRecall(ctx, req.(*DeleteRecallRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteRecallReply)
		return ctx.Result(200, reply)
	}
}

func _Service_SetRecall0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetRecallRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceSetRecall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetRecall(ctx, req.(*SetRecallRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SetRecallReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRecallTouch0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRecallTouchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRecallTouch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRecallTouch(ctx, req.(*GetRecallTouchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRecallTouchReply)
		return ctx.Result(200, reply)
	}
}

func _Service_GetRecallCoverUsers0_HTTP_Handler(srv ServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRecallCoverUsersRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServiceGetRecallCoverUsers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRecallCoverUsers(ctx, req.(*GetRecallCoverUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRecallCoverUsersReply)
		return ctx.Result(200, reply)
	}
}

type ServiceHTTPClient interface {
	AddLabel(ctx context.Context, req *Label, opts ...http.CallOption) (rsp *AddLabelReply, err error)
	AddRecall(ctx context.Context, req *Recall, opts ...http.CallOption) (rsp *AddRecallReply, err error)
	CountByLabel(ctx context.Context, req *CountByLabelRequest, opts ...http.CallOption) (rsp *CountByLabelReply, err error)
	CustomFilter(ctx context.Context, req *CustomFilterRequest, opts ...http.CallOption) (rsp *CustomFilterReply, err error)
	DeleteLabel(ctx context.Context, req *DeleteLabelRequest, opts ...http.CallOption) (rsp *DeleteLabelReply, err error)
	DeleteRecall(ctx context.Context, req *DeleteRecallRequest, opts ...http.CallOption) (rsp *DeleteRecallReply, err error)
	EditorList(ctx context.Context, req *EditorListRequest, opts ...http.CallOption) (rsp *EditorListReply, err error)
	FilterByLabel(ctx context.Context, req *FilterByLabelRequest, opts ...http.CallOption) (rsp *FilterByLabelReply, err error)
	FilterLabels(ctx context.Context, req *FilterLabelsRequest, opts ...http.CallOption) (rsp *FilterLabelsReply, err error)
	GetByUserId(ctx context.Context, req *GetByUserIdRequest, opts ...http.CallOption) (rsp *UserProfile, err error)
	GetLabel(ctx context.Context, req *GetLabelRequest, opts ...http.CallOption) (rsp *Label, err error)
	GetRecallCoverUsers(ctx context.Context, req *GetRecallCoverUsersRequest, opts ...http.CallOption) (rsp *GetRecallCoverUsersReply, err error)
	GetRecallExecRecords(ctx context.Context, req *GetRecallExecRecordsRequest, opts ...http.CallOption) (rsp *GetRecallExecRecordsReply, err error)
	GetRecallTouch(ctx context.Context, req *GetRecallTouchRequest, opts ...http.CallOption) (rsp *GetRecallTouchReply, err error)
	GetRecalls(ctx context.Context, req *GetRecallsRequest, opts ...http.CallOption) (rsp *GetRecallsReply, err error)
	GetUserProfile(ctx context.Context, req *GetUserprofileRequest, opts ...http.CallOption) (rsp *UserProfile, err error)
	Healthy(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *common.HealthyReply, err error)
	LabelCountry(ctx context.Context, req *LabelCountryRequest, opts ...http.CallOption) (rsp *LabelCountryReply, err error)
	LabelLanguage(ctx context.Context, req *LabelLanguageRequest, opts ...http.CallOption) (rsp *LabelLanguageReply, err error)
	ListLabel(ctx context.Context, req *ListLabelRequest, opts ...http.CallOption) (rsp *ListLabelReply, err error)
	PushIdReport(ctx context.Context, req *PushIdReportRequest, opts ...http.CallOption) (rsp *PushIdReportReply, err error)
	SetRecall(ctx context.Context, req *SetRecallRequest, opts ...http.CallOption) (rsp *SetRecallReply, err error)
	UpdateLabel(ctx context.Context, req *Label, opts ...http.CallOption) (rsp *UpdateLabelReply, err error)
	UpdateLabelStatus(ctx context.Context, req *UpdateLabelStatusRequest, opts ...http.CallOption) (rsp *UpdateLabelStatusReply, err error)
	UserProfileField(ctx context.Context, req *UserProfileFieldRequest, opts ...http.CallOption) (rsp *UserProfileFieldReply, err error)
}

type ServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewServiceHTTPClient(client *http.Client) ServiceHTTPClient {
	return &ServiceHTTPClientImpl{client}
}

func (c *ServiceHTTPClientImpl) AddLabel(ctx context.Context, in *Label, opts ...http.CallOption) (*AddLabelReply, error) {
	var out AddLabelReply
	pattern := "/v1/label"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceAddLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) AddRecall(ctx context.Context, in *Recall, opts ...http.CallOption) (*AddRecallReply, error) {
	var out AddRecallReply
	pattern := "/v1/recall"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceAddRecall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) CountByLabel(ctx context.Context, in *CountByLabelRequest, opts ...http.CallOption) (*CountByLabelReply, error) {
	var out CountByLabelReply
	pattern := "/v1/label/{id}/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceCountByLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) CustomFilter(ctx context.Context, in *CustomFilterRequest, opts ...http.CallOption) (*CustomFilterReply, error) {
	var out CustomFilterReply
	pattern := "/v1/push_filter"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceCustomFilter))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) DeleteLabel(ctx context.Context, in *DeleteLabelRequest, opts ...http.CallOption) (*DeleteLabelReply, error) {
	var out DeleteLabelReply
	pattern := "/v1/label/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceDeleteLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) DeleteRecall(ctx context.Context, in *DeleteRecallRequest, opts ...http.CallOption) (*DeleteRecallReply, error) {
	var out DeleteRecallReply
	pattern := "/v1/recall"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceDeleteRecall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) EditorList(ctx context.Context, in *EditorListRequest, opts ...http.CallOption) (*EditorListReply, error) {
	var out EditorListReply
	pattern := "/v1/label/editor/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceEditorList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FilterByLabel(ctx context.Context, in *FilterByLabelRequest, opts ...http.CallOption) (*FilterByLabelReply, error) {
	var out FilterByLabelReply
	pattern := "/v1/label/{id}/filter"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFilterByLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) FilterLabels(ctx context.Context, in *FilterLabelsRequest, opts ...http.CallOption) (*FilterLabelsReply, error) {
	var out FilterLabelsReply
	pattern := "/v1/push_labels"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceFilterLabels))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetByUserId(ctx context.Context, in *GetByUserIdRequest, opts ...http.CallOption) (*UserProfile, error) {
	var out UserProfile
	pattern := "/v1/user/{user_id}/profile"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetByUserId))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetLabel(ctx context.Context, in *GetLabelRequest, opts ...http.CallOption) (*Label, error) {
	var out Label
	pattern := "/v1/label/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetRecallCoverUsers(ctx context.Context, in *GetRecallCoverUsersRequest, opts ...http.CallOption) (*GetRecallCoverUsersReply, error) {
	var out GetRecallCoverUsersReply
	pattern := "/v1/recall/cover_users"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRecallCoverUsers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetRecallExecRecords(ctx context.Context, in *GetRecallExecRecordsRequest, opts ...http.CallOption) (*GetRecallExecRecordsReply, error) {
	var out GetRecallExecRecordsReply
	pattern := "/v1/recall/exec_records"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRecallExecRecords))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetRecallTouch(ctx context.Context, in *GetRecallTouchRequest, opts ...http.CallOption) (*GetRecallTouchReply, error) {
	var out GetRecallTouchReply
	pattern := "/v1/recall/touch"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRecallTouch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetRecalls(ctx context.Context, in *GetRecallsRequest, opts ...http.CallOption) (*GetRecallsReply, error) {
	var out GetRecallsReply
	pattern := "/v1/recalls"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetRecalls))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) GetUserProfile(ctx context.Context, in *GetUserprofileRequest, opts ...http.CallOption) (*UserProfile, error) {
	var out UserProfile
	pattern := "/v1/profile"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceGetUserProfile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*common.HealthyReply, error) {
	var out common.HealthyReply
	pattern := "/healthz"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceHealthy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) LabelCountry(ctx context.Context, in *LabelCountryRequest, opts ...http.CallOption) (*LabelCountryReply, error) {
	var out LabelCountryReply
	pattern := "/v1/label/{id}/countries"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceLabelCountry))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) LabelLanguage(ctx context.Context, in *LabelLanguageRequest, opts ...http.CallOption) (*LabelLanguageReply, error) {
	var out LabelLanguageReply
	pattern := "/v1/label/{id}/languages"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceLabelLanguage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) ListLabel(ctx context.Context, in *ListLabelRequest, opts ...http.CallOption) (*ListLabelReply, error) {
	var out ListLabelReply
	pattern := "/v1/label"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceListLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) PushIdReport(ctx context.Context, in *PushIdReportRequest, opts ...http.CallOption) (*PushIdReportReply, error) {
	var out PushIdReportReply
	pattern := "/v1/push_report"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServicePushIdReport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) SetRecall(ctx context.Context, in *SetRecallRequest, opts ...http.CallOption) (*SetRecallReply, error) {
	var out SetRecallReply
	pattern := "/v1/recall/set_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceSetRecall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UpdateLabel(ctx context.Context, in *Label, opts ...http.CallOption) (*UpdateLabelReply, error) {
	var out UpdateLabelReply
	pattern := "/v1/label/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUpdateLabel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UpdateLabelStatus(ctx context.Context, in *UpdateLabelStatusRequest, opts ...http.CallOption) (*UpdateLabelStatusReply, error) {
	var out UpdateLabelStatusReply
	pattern := "/v1/label/{id}/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServiceUpdateLabelStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *ServiceHTTPClientImpl) UserProfileField(ctx context.Context, in *UserProfileFieldRequest, opts ...http.CallOption) (*UserProfileFieldReply, error) {
	var out UserProfileFieldReply
	pattern := "/v1/profile/fields"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServiceUserProfileField))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
