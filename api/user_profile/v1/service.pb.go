// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: user_profile/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_user_profile_v1_service_proto protoreflect.FileDescriptor

var file_user_profile_v1_service_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65,
	0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xd0, 0x20, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x60, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x29, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5,
	0xae, 0x89, 0xe5, 0x85, 0xa8, 0x12, 0x0c, 0xe5, 0x81, 0xa5, 0xe5, 0xba, 0xb7, 0xe6, 0xa3, 0x80,
	0xe6, 0x9f, 0xa5, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x7a, 0x12, 0x9d, 0x01, 0x0a, 0x0c, 0x50, 0x75, 0x73, 0x68, 0x49, 0x64, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x49,
	0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3b, 0x92, 0x41, 0x1e, 0x0a, 0x06, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0x12, 0x14, 0xe4, 0xb8, 0x8a, 0xe6, 0x8a, 0xa5, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x49, 0x44, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a,
	0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0xaa, 0x01, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x2e, 0x0a, 0x0f, 0xe9, 0xa2, 0x84,
	0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x1b, 0xe8, 0x8e,
	0xb7, 0xe5, 0x8f, 0x96, 0xe9, 0xa2, 0x84, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12,
	0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0xad, 0x01, 0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x4b, 0x92, 0x41, 0x2e, 0x0a, 0x0f, 0xe9, 0xa2, 0x84, 0xe5, 0xae, 0x9a,
	0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x1b, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d,
	0xae, 0xe9, 0xa2, 0x84, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0xe7, 0xad, 0x9b, 0xe9, 0x80, 0x89, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22,
	0x0f, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0xb6, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x48, 0x92, 0x41, 0x2b, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xbb, 0xe5,
	0x83, 0x8f, 0x12, 0x1b, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xbb, 0xe5, 0x83, 0x8f,
	0xe6, 0x94, 0xaf, 0xe6, 0x8c, 0x81, 0xe7, 0x9a, 0x84, 0xe5, 0xad, 0x97, 0xe6, 0xae, 0xb5, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x14, 0x12, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0xaf, 0x01, 0x0a, 0x0b, 0x47, 0x65,
	0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x22, 0x55, 0x92, 0x41, 0x30, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe7, 0x94, 0xbb, 0xe5, 0x83, 0x8f, 0x12, 0x20, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xbb, 0xe5, 0x83, 0x8f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12,
	0x1a, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x7b, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x7d, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0xb4, 0x01, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x54, 0x92, 0x41,
	0x3e, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xbb, 0xe5, 0x83, 0x8f, 0x12,
	0x2e, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0xe6,
	0x88, 0x96, 0xe8, 0x80, 0x85, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0x49, 0x44, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0x94, 0xbb, 0xe5, 0x83, 0x8f, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x1a, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x64, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x3f, 0x92, 0x41, 0x28, 0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x15, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe8, 0x87, 0xaa,
	0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22, 0x09, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x95, 0x01, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x22,
	0x47, 0x92, 0x41, 0x2e, 0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x1b, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0x87, 0xaa,
	0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe8, 0xaf, 0xa6, 0xe6,
	0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x96, 0x01, 0x0a, 0x0b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x44, 0x92, 0x41, 0x28,
	0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0x12, 0x15, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4,
	0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01,
	0x2a, 0x1a, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x12, 0xc2, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x51, 0x92, 0x41, 0x2e, 0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a,
	0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x1b, 0xe6, 0x9b, 0xb4, 0xe6, 0x96,
	0xb0, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x1a,
	0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0xa0, 0x01, 0x0a, 0x0b, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x41, 0x92, 0x41, 0x28, 0x0a, 0x0f, 0xe8, 0x87, 0xaa,
	0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x15, 0xe5, 0x88,
	0xa0, 0xe9, 0x99, 0xa4, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87,
	0xe7, 0xad, 0xbe, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x2a, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x12, 0x9b, 0x01, 0x0a, 0x09, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x42, 0x92, 0x41, 0x2e, 0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a,
	0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x1b, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f, 0x76,
	0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0xb5, 0x01, 0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x53, 0x92, 0x41, 0x34, 0x0a,
	0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe,
	0x12, 0x21, 0xe6, 0xa0, 0xb9, 0xe6, 0x8d, 0xae, 0xe9, 0xa2, 0x84, 0xe5, 0xae, 0x9a, 0xe4, 0xb9,
	0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0xb9, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x54, 0x92, 0x41, 0x34, 0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5,
	0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x21, 0xe6, 0xa0, 0xb9,
	0xe6, 0x8d, 0xae, 0xe9, 0xa2, 0x84, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7,
	0xad, 0xbe, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0xa4, 0x01, 0x0a, 0x0a,
	0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x48, 0x92, 0x41, 0x28, 0x0a, 0x0f, 0xe8,
	0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x15,
	0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0xbc, 0x96, 0xe8, 0xbe, 0x91, 0xe4, 0xba, 0xba, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2f, 0x65, 0x64, 0x69, 0x74, 0x6f, 0x72, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0xb0, 0x01, 0x0a, 0x0c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41, 0x2b, 0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5,
	0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x12, 0x18, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31,
	0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64, 0x7d, 0x2f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0xb3, 0x01, 0x0a, 0x0d, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41, 0x2b,
	0x0a, 0x0f, 0xe8, 0x87, 0xaa, 0xe5, 0xae, 0x9a, 0xe4, 0xb9, 0x89, 0xe6, 0xa0, 0x87, 0xe7, 0xad,
	0xbe, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0xe8,
	0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1a, 0x12, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x2f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x73, 0x12, 0x83, 0x01, 0x0a, 0x09,
	0x41, 0x64, 0x64, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64,
	0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x92, 0x41, 0x1c,
	0x0a, 0x06, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0,
	0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x6c,
	0x6c, 0x12, 0x8e, 0x01, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x73,
	0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x32,
	0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x12, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x6c,
	0x6c, 0x73, 0x12, 0xc4, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c,
	0x45, 0x78, 0x65, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x78, 0x65, 0x63, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x78, 0x65,
	0x63, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4a, 0x92,
	0x41, 0x28, 0x0a, 0x06, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x1e, 0xe8, 0x8e, 0xb7, 0xe5,
	0x8f, 0x96, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x89,
	0xa7, 0xe8, 0xa1, 0x8c, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19,
	0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x2f, 0x65, 0x78, 0x65,
	0x63, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x93, 0x01, 0x0a, 0x0c, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x92, 0x41,
	0x1c, 0x0a, 0x06, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99,
	0xa4, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0c, 0x2a, 0x0a, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12,
	0x9e, 0x01, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x12, 0x25, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x63, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x45, 0x92, 0x41, 0x22, 0x0a, 0x06,
	0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x18, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe5, 0x8f,
	0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x72,
	0x65, 0x63, 0x61, 0x6c, 0x6c, 0x2f, 0x73, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0xa2, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54, 0x6f,
	0x75, 0x63, 0x68, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x54, 0x6f, 0x75, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x54,
	0x6f, 0x75, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3a, 0x92, 0x41, 0x1f, 0x0a, 0x06,
	0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x15, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xa7, 0xa6, 0xe8, 0xbe, 0xbe, 0xe7, 0x8e, 0x87, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x2f,
	0x74, 0x6f, 0x75, 0x63, 0x68, 0x12, 0xc0, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x76,
	0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43, 0x6f,
	0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x49, 0x92,
	0x41, 0x28, 0x0a, 0x06, 0xe5, 0x8f, 0xac, 0xe5, 0x9b, 0x9e, 0x12, 0x1e, 0xe5, 0x8f, 0xac, 0xe5,
	0x9b, 0x9e, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe8, 0xa6, 0x86, 0xe7, 0x9b, 0x96, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18,
	0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x2f, 0x63, 0x6f, 0x76,
	0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x42, 0x18, 0x5a, 0x16, 0x61, 0x70, 0x69, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_user_profile_v1_service_proto_goTypes = []interface{}{
	(*common.EmptyRequest)(nil),         // 0: common.EmptyRequest
	(*PushIdReportRequest)(nil),         // 1: api.user_profile.v1.PushIdReportRequest
	(*FilterLabelsRequest)(nil),         // 2: api.user_profile.v1.FilterLabelsRequest
	(*CustomFilterRequest)(nil),         // 3: api.user_profile.v1.CustomFilterRequest
	(*UserProfileFieldRequest)(nil),     // 4: api.user_profile.v1.UserProfileFieldRequest
	(*GetByUserIdRequest)(nil),          // 5: api.user_profile.v1.GetByUserIdRequest
	(*GetUserprofileRequest)(nil),       // 6: api.user_profile.v1.GetUserprofileRequest
	(*Label)(nil),                       // 7: api.user_profile.v1.Label
	(*GetLabelRequest)(nil),             // 8: api.user_profile.v1.GetLabelRequest
	(*UpdateLabelStatusRequest)(nil),    // 9: api.user_profile.v1.UpdateLabelStatusRequest
	(*DeleteLabelRequest)(nil),          // 10: api.user_profile.v1.DeleteLabelRequest
	(*ListLabelRequest)(nil),            // 11: api.user_profile.v1.ListLabelRequest
	(*CountByLabelRequest)(nil),         // 12: api.user_profile.v1.CountByLabelRequest
	(*FilterByLabelRequest)(nil),        // 13: api.user_profile.v1.FilterByLabelRequest
	(*EditorListRequest)(nil),           // 14: api.user_profile.v1.EditorListRequest
	(*LabelCountryRequest)(nil),         // 15: api.user_profile.v1.LabelCountryRequest
	(*LabelLanguageRequest)(nil),        // 16: api.user_profile.v1.LabelLanguageRequest
	(*Recall)(nil),                      // 17: api.user_profile.v1.Recall
	(*GetRecallsRequest)(nil),           // 18: api.user_profile.v1.GetRecallsRequest
	(*GetRecallExecRecordsRequest)(nil), // 19: api.user_profile.v1.GetRecallExecRecordsRequest
	(*DeleteRecallRequest)(nil),         // 20: api.user_profile.v1.DeleteRecallRequest
	(*SetRecallRequest)(nil),            // 21: api.user_profile.v1.SetRecallRequest
	(*GetRecallTouchRequest)(nil),       // 22: api.user_profile.v1.GetRecallTouchRequest
	(*GetRecallCoverUsersRequest)(nil),  // 23: api.user_profile.v1.GetRecallCoverUsersRequest
	(*common.HealthyReply)(nil),         // 24: common.HealthyReply
	(*PushIdReportReply)(nil),           // 25: api.user_profile.v1.PushIdReportReply
	(*FilterLabelsReply)(nil),           // 26: api.user_profile.v1.FilterLabelsReply
	(*CustomFilterReply)(nil),           // 27: api.user_profile.v1.CustomFilterReply
	(*UserProfileFieldReply)(nil),       // 28: api.user_profile.v1.UserProfileFieldReply
	(*UserProfile)(nil),                 // 29: api.user_profile.v1.UserProfile
	(*AddLabelReply)(nil),               // 30: api.user_profile.v1.AddLabelReply
	(*UpdateLabelReply)(nil),            // 31: api.user_profile.v1.UpdateLabelReply
	(*UpdateLabelStatusReply)(nil),      // 32: api.user_profile.v1.UpdateLabelStatusReply
	(*DeleteLabelReply)(nil),            // 33: api.user_profile.v1.DeleteLabelReply
	(*ListLabelReply)(nil),              // 34: api.user_profile.v1.ListLabelReply
	(*CountByLabelReply)(nil),           // 35: api.user_profile.v1.CountByLabelReply
	(*FilterByLabelReply)(nil),          // 36: api.user_profile.v1.FilterByLabelReply
	(*EditorListReply)(nil),             // 37: api.user_profile.v1.EditorListReply
	(*LabelCountryReply)(nil),           // 38: api.user_profile.v1.LabelCountryReply
	(*LabelLanguageReply)(nil),          // 39: api.user_profile.v1.LabelLanguageReply
	(*AddRecallReply)(nil),              // 40: api.user_profile.v1.AddRecallReply
	(*GetRecallsReply)(nil),             // 41: api.user_profile.v1.GetRecallsReply
	(*GetRecallExecRecordsReply)(nil),   // 42: api.user_profile.v1.GetRecallExecRecordsReply
	(*DeleteRecallReply)(nil),           // 43: api.user_profile.v1.DeleteRecallReply
	(*SetRecallReply)(nil),              // 44: api.user_profile.v1.SetRecallReply
	(*GetRecallTouchReply)(nil),         // 45: api.user_profile.v1.GetRecallTouchReply
	(*GetRecallCoverUsersReply)(nil),    // 46: api.user_profile.v1.GetRecallCoverUsersReply
}
var file_user_profile_v1_service_proto_depIdxs = []int32{
	0,  // 0: api.user_profile.v1.Service.Healthy:input_type -> common.EmptyRequest
	1,  // 1: api.user_profile.v1.Service.PushIdReport:input_type -> api.user_profile.v1.PushIdReportRequest
	2,  // 2: api.user_profile.v1.Service.FilterLabels:input_type -> api.user_profile.v1.FilterLabelsRequest
	3,  // 3: api.user_profile.v1.Service.CustomFilter:input_type -> api.user_profile.v1.CustomFilterRequest
	4,  // 4: api.user_profile.v1.Service.UserProfileField:input_type -> api.user_profile.v1.UserProfileFieldRequest
	5,  // 5: api.user_profile.v1.Service.GetByUserId:input_type -> api.user_profile.v1.GetByUserIdRequest
	6,  // 6: api.user_profile.v1.Service.GetUserProfile:input_type -> api.user_profile.v1.GetUserprofileRequest
	7,  // 7: api.user_profile.v1.Service.AddLabel:input_type -> api.user_profile.v1.Label
	8,  // 8: api.user_profile.v1.Service.GetLabel:input_type -> api.user_profile.v1.GetLabelRequest
	7,  // 9: api.user_profile.v1.Service.UpdateLabel:input_type -> api.user_profile.v1.Label
	9,  // 10: api.user_profile.v1.Service.UpdateLabelStatus:input_type -> api.user_profile.v1.UpdateLabelStatusRequest
	10, // 11: api.user_profile.v1.Service.DeleteLabel:input_type -> api.user_profile.v1.DeleteLabelRequest
	11, // 12: api.user_profile.v1.Service.ListLabel:input_type -> api.user_profile.v1.ListLabelRequest
	12, // 13: api.user_profile.v1.Service.CountByLabel:input_type -> api.user_profile.v1.CountByLabelRequest
	13, // 14: api.user_profile.v1.Service.FilterByLabel:input_type -> api.user_profile.v1.FilterByLabelRequest
	14, // 15: api.user_profile.v1.Service.EditorList:input_type -> api.user_profile.v1.EditorListRequest
	15, // 16: api.user_profile.v1.Service.LabelCountry:input_type -> api.user_profile.v1.LabelCountryRequest
	16, // 17: api.user_profile.v1.Service.LabelLanguage:input_type -> api.user_profile.v1.LabelLanguageRequest
	17, // 18: api.user_profile.v1.Service.AddRecall:input_type -> api.user_profile.v1.Recall
	18, // 19: api.user_profile.v1.Service.GetRecalls:input_type -> api.user_profile.v1.GetRecallsRequest
	19, // 20: api.user_profile.v1.Service.GetRecallExecRecords:input_type -> api.user_profile.v1.GetRecallExecRecordsRequest
	20, // 21: api.user_profile.v1.Service.DeleteRecall:input_type -> api.user_profile.v1.DeleteRecallRequest
	21, // 22: api.user_profile.v1.Service.SetRecall:input_type -> api.user_profile.v1.SetRecallRequest
	22, // 23: api.user_profile.v1.Service.GetRecallTouch:input_type -> api.user_profile.v1.GetRecallTouchRequest
	23, // 24: api.user_profile.v1.Service.GetRecallCoverUsers:input_type -> api.user_profile.v1.GetRecallCoverUsersRequest
	24, // 25: api.user_profile.v1.Service.Healthy:output_type -> common.HealthyReply
	25, // 26: api.user_profile.v1.Service.PushIdReport:output_type -> api.user_profile.v1.PushIdReportReply
	26, // 27: api.user_profile.v1.Service.FilterLabels:output_type -> api.user_profile.v1.FilterLabelsReply
	27, // 28: api.user_profile.v1.Service.CustomFilter:output_type -> api.user_profile.v1.CustomFilterReply
	28, // 29: api.user_profile.v1.Service.UserProfileField:output_type -> api.user_profile.v1.UserProfileFieldReply
	29, // 30: api.user_profile.v1.Service.GetByUserId:output_type -> api.user_profile.v1.UserProfile
	29, // 31: api.user_profile.v1.Service.GetUserProfile:output_type -> api.user_profile.v1.UserProfile
	30, // 32: api.user_profile.v1.Service.AddLabel:output_type -> api.user_profile.v1.AddLabelReply
	7,  // 33: api.user_profile.v1.Service.GetLabel:output_type -> api.user_profile.v1.Label
	31, // 34: api.user_profile.v1.Service.UpdateLabel:output_type -> api.user_profile.v1.UpdateLabelReply
	32, // 35: api.user_profile.v1.Service.UpdateLabelStatus:output_type -> api.user_profile.v1.UpdateLabelStatusReply
	33, // 36: api.user_profile.v1.Service.DeleteLabel:output_type -> api.user_profile.v1.DeleteLabelReply
	34, // 37: api.user_profile.v1.Service.ListLabel:output_type -> api.user_profile.v1.ListLabelReply
	35, // 38: api.user_profile.v1.Service.CountByLabel:output_type -> api.user_profile.v1.CountByLabelReply
	36, // 39: api.user_profile.v1.Service.FilterByLabel:output_type -> api.user_profile.v1.FilterByLabelReply
	37, // 40: api.user_profile.v1.Service.EditorList:output_type -> api.user_profile.v1.EditorListReply
	38, // 41: api.user_profile.v1.Service.LabelCountry:output_type -> api.user_profile.v1.LabelCountryReply
	39, // 42: api.user_profile.v1.Service.LabelLanguage:output_type -> api.user_profile.v1.LabelLanguageReply
	40, // 43: api.user_profile.v1.Service.AddRecall:output_type -> api.user_profile.v1.AddRecallReply
	41, // 44: api.user_profile.v1.Service.GetRecalls:output_type -> api.user_profile.v1.GetRecallsReply
	42, // 45: api.user_profile.v1.Service.GetRecallExecRecords:output_type -> api.user_profile.v1.GetRecallExecRecordsReply
	43, // 46: api.user_profile.v1.Service.DeleteRecall:output_type -> api.user_profile.v1.DeleteRecallReply
	44, // 47: api.user_profile.v1.Service.SetRecall:output_type -> api.user_profile.v1.SetRecallReply
	45, // 48: api.user_profile.v1.Service.GetRecallTouch:output_type -> api.user_profile.v1.GetRecallTouchReply
	46, // 49: api.user_profile.v1.Service.GetRecallCoverUsers:output_type -> api.user_profile.v1.GetRecallCoverUsersReply
	25, // [25:50] is the sub-list for method output_type
	0,  // [0:25] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_user_profile_v1_service_proto_init() }
func file_user_profile_v1_service_proto_init() {
	if File_user_profile_v1_service_proto != nil {
		return
	}
	file_user_profile_v1_models_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_profile_v1_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_profile_v1_service_proto_goTypes,
		DependencyIndexes: file_user_profile_v1_service_proto_depIdxs,
	}.Build()
	File_user_profile_v1_service_proto = out.File
	file_user_profile_v1_service_proto_rawDesc = nil
	file_user_profile_v1_service_proto_goTypes = nil
	file_user_profile_v1_service_proto_depIdxs = nil
}
