// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: user_profile/v1/service.proto

package v1

import (
	common "api-expo/api/common"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Service_Healthy_FullMethodName              = "/api.user_profile.v1.Service/Healthy"
	Service_PushIdReport_FullMethodName         = "/api.user_profile.v1.Service/PushIdReport"
	Service_FilterLabels_FullMethodName         = "/api.user_profile.v1.Service/FilterLabels"
	Service_CustomFilter_FullMethodName         = "/api.user_profile.v1.Service/CustomFilter"
	Service_UserProfileField_FullMethodName     = "/api.user_profile.v1.Service/UserProfileField"
	Service_GetByUserId_FullMethodName          = "/api.user_profile.v1.Service/GetByUserId"
	Service_GetUserProfile_FullMethodName       = "/api.user_profile.v1.Service/GetUserProfile"
	Service_AddLabel_FullMethodName             = "/api.user_profile.v1.Service/AddLabel"
	Service_GetLabel_FullMethodName             = "/api.user_profile.v1.Service/GetLabel"
	Service_UpdateLabel_FullMethodName          = "/api.user_profile.v1.Service/UpdateLabel"
	Service_UpdateLabelStatus_FullMethodName    = "/api.user_profile.v1.Service/UpdateLabelStatus"
	Service_DeleteLabel_FullMethodName          = "/api.user_profile.v1.Service/DeleteLabel"
	Service_ListLabel_FullMethodName            = "/api.user_profile.v1.Service/ListLabel"
	Service_CountByLabel_FullMethodName         = "/api.user_profile.v1.Service/CountByLabel"
	Service_FilterByLabel_FullMethodName        = "/api.user_profile.v1.Service/FilterByLabel"
	Service_EditorList_FullMethodName           = "/api.user_profile.v1.Service/EditorList"
	Service_LabelCountry_FullMethodName         = "/api.user_profile.v1.Service/LabelCountry"
	Service_LabelLanguage_FullMethodName        = "/api.user_profile.v1.Service/LabelLanguage"
	Service_AddRecall_FullMethodName            = "/api.user_profile.v1.Service/AddRecall"
	Service_GetRecalls_FullMethodName           = "/api.user_profile.v1.Service/GetRecalls"
	Service_GetRecallExecRecords_FullMethodName = "/api.user_profile.v1.Service/GetRecallExecRecords"
	Service_DeleteRecall_FullMethodName         = "/api.user_profile.v1.Service/DeleteRecall"
	Service_SetRecall_FullMethodName            = "/api.user_profile.v1.Service/SetRecall"
	Service_GetRecallTouch_FullMethodName       = "/api.user_profile.v1.Service/GetRecallTouch"
	Service_GetRecallCoverUsers_FullMethodName  = "/api.user_profile.v1.Service/GetRecallCoverUsers"
)

// ServiceClient is the client API for Service service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServiceClient interface {
	Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error)
	// 推送ID上报
	PushIdReport(ctx context.Context, in *PushIdReportRequest, opts ...grpc.CallOption) (*PushIdReportReply, error)
	// 获取筛选标签
	FilterLabels(ctx context.Context, in *FilterLabelsRequest, opts ...grpc.CallOption) (*FilterLabelsReply, error)
	// 按照标签筛选
	CustomFilter(ctx context.Context, in *CustomFilterRequest, opts ...grpc.CallOption) (*CustomFilterReply, error)
	// =============== 用户画像 =========================
	// 用户画像支持的字段列表
	UserProfileField(ctx context.Context, in *UserProfileFieldRequest, opts ...grpc.CallOption) (*UserProfileFieldReply, error)
	// 根据用户ID查看用户画像
	GetByUserId(ctx context.Context, in *GetByUserIdRequest, opts ...grpc.CallOption) (*UserProfile, error)
	// 根据用户ID或者设备ID查看用户画像
	GetUserProfile(ctx context.Context, in *GetUserprofileRequest, opts ...grpc.CallOption) (*UserProfile, error)
	// =============== 自定义标签 =========================
	// 新增自定义标签
	AddLabel(ctx context.Context, in *Label, opts ...grpc.CallOption) (*AddLabelReply, error)
	// 获取自定义标签详情
	GetLabel(ctx context.Context, in *GetLabelRequest, opts ...grpc.CallOption) (*Label, error)
	// 更新自定义标签
	UpdateLabel(ctx context.Context, in *Label, opts ...grpc.CallOption) (*UpdateLabelReply, error)
	// 更新自定义标签状态
	UpdateLabelStatus(ctx context.Context, in *UpdateLabelStatusRequest, opts ...grpc.CallOption) (*UpdateLabelStatusReply, error)
	// 删除自定义标签
	DeleteLabel(ctx context.Context, in *DeleteLabelRequest, opts ...grpc.CallOption) (*DeleteLabelReply, error)
	// 获取自定义标签列表
	ListLabel(ctx context.Context, in *ListLabelRequest, opts ...grpc.CallOption) (*ListLabelReply, error)
	// 根据标签获取用户数量
	CountByLabel(ctx context.Context, in *CountByLabelRequest, opts ...grpc.CallOption) (*CountByLabelReply, error)
	// 根据标签筛选用户
	FilterByLabel(ctx context.Context, in *FilterByLabelRequest, opts ...grpc.CallOption) (*FilterByLabelReply, error)
	// 编辑人列表
	EditorList(ctx context.Context, in *EditorListRequest, opts ...grpc.CallOption) (*EditorListReply, error)
	// 标签国家
	LabelCountry(ctx context.Context, in *LabelCountryRequest, opts ...grpc.CallOption) (*LabelCountryReply, error)
	// 标签语言
	LabelLanguage(ctx context.Context, in *LabelLanguageRequest, opts ...grpc.CallOption) (*LabelLanguageReply, error)
	// =============== 召回任务 =========================
	// 新增召回
	AddRecall(ctx context.Context, in *Recall, opts ...grpc.CallOption) (*AddRecallReply, error)
	// 获取召回列表
	GetRecalls(ctx context.Context, in *GetRecallsRequest, opts ...grpc.CallOption) (*GetRecallsReply, error)
	//  // 获取召回详情
	//  rpc GetRecall(GetRecallRequest) returns (Recall) {
	//    option (google.api.http) = {get: "/v1/recall"};
	//    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取召回详情",tags: ["召回"]};
	//  }
	// 获取召回任务执行记录
	GetRecallExecRecords(ctx context.Context, in *GetRecallExecRecordsRequest, opts ...grpc.CallOption) (*GetRecallExecRecordsReply, error)
	// 删除召回任务
	DeleteRecall(ctx context.Context, in *DeleteRecallRequest, opts ...grpc.CallOption) (*DeleteRecallReply, error)
	// 设置召回任务状态
	SetRecall(ctx context.Context, in *SetRecallRequest, opts ...grpc.CallOption) (*SetRecallReply, error)
	// 召回任务触达率
	GetRecallTouch(ctx context.Context, in *GetRecallTouchRequest, opts ...grpc.CallOption) (*GetRecallTouchReply, error)
	// 召回任务覆盖用户列表
	GetRecallCoverUsers(ctx context.Context, in *GetRecallCoverUsersRequest, opts ...grpc.CallOption) (*GetRecallCoverUsersReply, error)
}

type serviceClient struct {
	cc grpc.ClientConnInterface
}

func NewServiceClient(cc grpc.ClientConnInterface) ServiceClient {
	return &serviceClient{cc}
}

func (c *serviceClient) Healthy(ctx context.Context, in *common.EmptyRequest, opts ...grpc.CallOption) (*common.HealthyReply, error) {
	out := new(common.HealthyReply)
	err := c.cc.Invoke(ctx, Service_Healthy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) PushIdReport(ctx context.Context, in *PushIdReportRequest, opts ...grpc.CallOption) (*PushIdReportReply, error) {
	out := new(PushIdReportReply)
	err := c.cc.Invoke(ctx, Service_PushIdReport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FilterLabels(ctx context.Context, in *FilterLabelsRequest, opts ...grpc.CallOption) (*FilterLabelsReply, error) {
	out := new(FilterLabelsReply)
	err := c.cc.Invoke(ctx, Service_FilterLabels_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CustomFilter(ctx context.Context, in *CustomFilterRequest, opts ...grpc.CallOption) (*CustomFilterReply, error) {
	out := new(CustomFilterReply)
	err := c.cc.Invoke(ctx, Service_CustomFilter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UserProfileField(ctx context.Context, in *UserProfileFieldRequest, opts ...grpc.CallOption) (*UserProfileFieldReply, error) {
	out := new(UserProfileFieldReply)
	err := c.cc.Invoke(ctx, Service_UserProfileField_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetByUserId(ctx context.Context, in *GetByUserIdRequest, opts ...grpc.CallOption) (*UserProfile, error) {
	out := new(UserProfile)
	err := c.cc.Invoke(ctx, Service_GetByUserId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetUserProfile(ctx context.Context, in *GetUserprofileRequest, opts ...grpc.CallOption) (*UserProfile, error) {
	out := new(UserProfile)
	err := c.cc.Invoke(ctx, Service_GetUserProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) AddLabel(ctx context.Context, in *Label, opts ...grpc.CallOption) (*AddLabelReply, error) {
	out := new(AddLabelReply)
	err := c.cc.Invoke(ctx, Service_AddLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetLabel(ctx context.Context, in *GetLabelRequest, opts ...grpc.CallOption) (*Label, error) {
	out := new(Label)
	err := c.cc.Invoke(ctx, Service_GetLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UpdateLabel(ctx context.Context, in *Label, opts ...grpc.CallOption) (*UpdateLabelReply, error) {
	out := new(UpdateLabelReply)
	err := c.cc.Invoke(ctx, Service_UpdateLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) UpdateLabelStatus(ctx context.Context, in *UpdateLabelStatusRequest, opts ...grpc.CallOption) (*UpdateLabelStatusReply, error) {
	out := new(UpdateLabelStatusReply)
	err := c.cc.Invoke(ctx, Service_UpdateLabelStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) DeleteLabel(ctx context.Context, in *DeleteLabelRequest, opts ...grpc.CallOption) (*DeleteLabelReply, error) {
	out := new(DeleteLabelReply)
	err := c.cc.Invoke(ctx, Service_DeleteLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) ListLabel(ctx context.Context, in *ListLabelRequest, opts ...grpc.CallOption) (*ListLabelReply, error) {
	out := new(ListLabelReply)
	err := c.cc.Invoke(ctx, Service_ListLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) CountByLabel(ctx context.Context, in *CountByLabelRequest, opts ...grpc.CallOption) (*CountByLabelReply, error) {
	out := new(CountByLabelReply)
	err := c.cc.Invoke(ctx, Service_CountByLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) FilterByLabel(ctx context.Context, in *FilterByLabelRequest, opts ...grpc.CallOption) (*FilterByLabelReply, error) {
	out := new(FilterByLabelReply)
	err := c.cc.Invoke(ctx, Service_FilterByLabel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) EditorList(ctx context.Context, in *EditorListRequest, opts ...grpc.CallOption) (*EditorListReply, error) {
	out := new(EditorListReply)
	err := c.cc.Invoke(ctx, Service_EditorList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) LabelCountry(ctx context.Context, in *LabelCountryRequest, opts ...grpc.CallOption) (*LabelCountryReply, error) {
	out := new(LabelCountryReply)
	err := c.cc.Invoke(ctx, Service_LabelCountry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) LabelLanguage(ctx context.Context, in *LabelLanguageRequest, opts ...grpc.CallOption) (*LabelLanguageReply, error) {
	out := new(LabelLanguageReply)
	err := c.cc.Invoke(ctx, Service_LabelLanguage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) AddRecall(ctx context.Context, in *Recall, opts ...grpc.CallOption) (*AddRecallReply, error) {
	out := new(AddRecallReply)
	err := c.cc.Invoke(ctx, Service_AddRecall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRecalls(ctx context.Context, in *GetRecallsRequest, opts ...grpc.CallOption) (*GetRecallsReply, error) {
	out := new(GetRecallsReply)
	err := c.cc.Invoke(ctx, Service_GetRecalls_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRecallExecRecords(ctx context.Context, in *GetRecallExecRecordsRequest, opts ...grpc.CallOption) (*GetRecallExecRecordsReply, error) {
	out := new(GetRecallExecRecordsReply)
	err := c.cc.Invoke(ctx, Service_GetRecallExecRecords_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) DeleteRecall(ctx context.Context, in *DeleteRecallRequest, opts ...grpc.CallOption) (*DeleteRecallReply, error) {
	out := new(DeleteRecallReply)
	err := c.cc.Invoke(ctx, Service_DeleteRecall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) SetRecall(ctx context.Context, in *SetRecallRequest, opts ...grpc.CallOption) (*SetRecallReply, error) {
	out := new(SetRecallReply)
	err := c.cc.Invoke(ctx, Service_SetRecall_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRecallTouch(ctx context.Context, in *GetRecallTouchRequest, opts ...grpc.CallOption) (*GetRecallTouchReply, error) {
	out := new(GetRecallTouchReply)
	err := c.cc.Invoke(ctx, Service_GetRecallTouch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serviceClient) GetRecallCoverUsers(ctx context.Context, in *GetRecallCoverUsersRequest, opts ...grpc.CallOption) (*GetRecallCoverUsersReply, error) {
	out := new(GetRecallCoverUsersReply)
	err := c.cc.Invoke(ctx, Service_GetRecallCoverUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServiceServer is the server API for Service service.
// All implementations must embed UnimplementedServiceServer
// for forward compatibility
type ServiceServer interface {
	Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error)
	// 推送ID上报
	PushIdReport(context.Context, *PushIdReportRequest) (*PushIdReportReply, error)
	// 获取筛选标签
	FilterLabels(context.Context, *FilterLabelsRequest) (*FilterLabelsReply, error)
	// 按照标签筛选
	CustomFilter(context.Context, *CustomFilterRequest) (*CustomFilterReply, error)
	// =============== 用户画像 =========================
	// 用户画像支持的字段列表
	UserProfileField(context.Context, *UserProfileFieldRequest) (*UserProfileFieldReply, error)
	// 根据用户ID查看用户画像
	GetByUserId(context.Context, *GetByUserIdRequest) (*UserProfile, error)
	// 根据用户ID或者设备ID查看用户画像
	GetUserProfile(context.Context, *GetUserprofileRequest) (*UserProfile, error)
	// =============== 自定义标签 =========================
	// 新增自定义标签
	AddLabel(context.Context, *Label) (*AddLabelReply, error)
	// 获取自定义标签详情
	GetLabel(context.Context, *GetLabelRequest) (*Label, error)
	// 更新自定义标签
	UpdateLabel(context.Context, *Label) (*UpdateLabelReply, error)
	// 更新自定义标签状态
	UpdateLabelStatus(context.Context, *UpdateLabelStatusRequest) (*UpdateLabelStatusReply, error)
	// 删除自定义标签
	DeleteLabel(context.Context, *DeleteLabelRequest) (*DeleteLabelReply, error)
	// 获取自定义标签列表
	ListLabel(context.Context, *ListLabelRequest) (*ListLabelReply, error)
	// 根据标签获取用户数量
	CountByLabel(context.Context, *CountByLabelRequest) (*CountByLabelReply, error)
	// 根据标签筛选用户
	FilterByLabel(context.Context, *FilterByLabelRequest) (*FilterByLabelReply, error)
	// 编辑人列表
	EditorList(context.Context, *EditorListRequest) (*EditorListReply, error)
	// 标签国家
	LabelCountry(context.Context, *LabelCountryRequest) (*LabelCountryReply, error)
	// 标签语言
	LabelLanguage(context.Context, *LabelLanguageRequest) (*LabelLanguageReply, error)
	// =============== 召回任务 =========================
	// 新增召回
	AddRecall(context.Context, *Recall) (*AddRecallReply, error)
	// 获取召回列表
	GetRecalls(context.Context, *GetRecallsRequest) (*GetRecallsReply, error)
	//  // 获取召回详情
	//  rpc GetRecall(GetRecallRequest) returns (Recall) {
	//    option (google.api.http) = {get: "/v1/recall"};
	//    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取召回详情",tags: ["召回"]};
	//  }
	// 获取召回任务执行记录
	GetRecallExecRecords(context.Context, *GetRecallExecRecordsRequest) (*GetRecallExecRecordsReply, error)
	// 删除召回任务
	DeleteRecall(context.Context, *DeleteRecallRequest) (*DeleteRecallReply, error)
	// 设置召回任务状态
	SetRecall(context.Context, *SetRecallRequest) (*SetRecallReply, error)
	// 召回任务触达率
	GetRecallTouch(context.Context, *GetRecallTouchRequest) (*GetRecallTouchReply, error)
	// 召回任务覆盖用户列表
	GetRecallCoverUsers(context.Context, *GetRecallCoverUsersRequest) (*GetRecallCoverUsersReply, error)
	mustEmbedUnimplementedServiceServer()
}

// UnimplementedServiceServer must be embedded to have forward compatible implementations.
type UnimplementedServiceServer struct {
}

func (UnimplementedServiceServer) Healthy(context.Context, *common.EmptyRequest) (*common.HealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Healthy not implemented")
}
func (UnimplementedServiceServer) PushIdReport(context.Context, *PushIdReportRequest) (*PushIdReportReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushIdReport not implemented")
}
func (UnimplementedServiceServer) FilterLabels(context.Context, *FilterLabelsRequest) (*FilterLabelsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterLabels not implemented")
}
func (UnimplementedServiceServer) CustomFilter(context.Context, *CustomFilterRequest) (*CustomFilterReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CustomFilter not implemented")
}
func (UnimplementedServiceServer) UserProfileField(context.Context, *UserProfileFieldRequest) (*UserProfileFieldReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserProfileField not implemented")
}
func (UnimplementedServiceServer) GetByUserId(context.Context, *GetByUserIdRequest) (*UserProfile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetByUserId not implemented")
}
func (UnimplementedServiceServer) GetUserProfile(context.Context, *GetUserprofileRequest) (*UserProfile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserProfile not implemented")
}
func (UnimplementedServiceServer) AddLabel(context.Context, *Label) (*AddLabelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLabel not implemented")
}
func (UnimplementedServiceServer) GetLabel(context.Context, *GetLabelRequest) (*Label, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLabel not implemented")
}
func (UnimplementedServiceServer) UpdateLabel(context.Context, *Label) (*UpdateLabelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLabel not implemented")
}
func (UnimplementedServiceServer) UpdateLabelStatus(context.Context, *UpdateLabelStatusRequest) (*UpdateLabelStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLabelStatus not implemented")
}
func (UnimplementedServiceServer) DeleteLabel(context.Context, *DeleteLabelRequest) (*DeleteLabelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLabel not implemented")
}
func (UnimplementedServiceServer) ListLabel(context.Context, *ListLabelRequest) (*ListLabelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLabel not implemented")
}
func (UnimplementedServiceServer) CountByLabel(context.Context, *CountByLabelRequest) (*CountByLabelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountByLabel not implemented")
}
func (UnimplementedServiceServer) FilterByLabel(context.Context, *FilterByLabelRequest) (*FilterByLabelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterByLabel not implemented")
}
func (UnimplementedServiceServer) EditorList(context.Context, *EditorListRequest) (*EditorListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditorList not implemented")
}
func (UnimplementedServiceServer) LabelCountry(context.Context, *LabelCountryRequest) (*LabelCountryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelCountry not implemented")
}
func (UnimplementedServiceServer) LabelLanguage(context.Context, *LabelLanguageRequest) (*LabelLanguageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelLanguage not implemented")
}
func (UnimplementedServiceServer) AddRecall(context.Context, *Recall) (*AddRecallReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddRecall not implemented")
}
func (UnimplementedServiceServer) GetRecalls(context.Context, *GetRecallsRequest) (*GetRecallsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecalls not implemented")
}
func (UnimplementedServiceServer) GetRecallExecRecords(context.Context, *GetRecallExecRecordsRequest) (*GetRecallExecRecordsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecallExecRecords not implemented")
}
func (UnimplementedServiceServer) DeleteRecall(context.Context, *DeleteRecallRequest) (*DeleteRecallReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRecall not implemented")
}
func (UnimplementedServiceServer) SetRecall(context.Context, *SetRecallRequest) (*SetRecallReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRecall not implemented")
}
func (UnimplementedServiceServer) GetRecallTouch(context.Context, *GetRecallTouchRequest) (*GetRecallTouchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecallTouch not implemented")
}
func (UnimplementedServiceServer) GetRecallCoverUsers(context.Context, *GetRecallCoverUsersRequest) (*GetRecallCoverUsersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecallCoverUsers not implemented")
}
func (UnimplementedServiceServer) mustEmbedUnimplementedServiceServer() {}

// UnsafeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServiceServer will
// result in compilation errors.
type UnsafeServiceServer interface {
	mustEmbedUnimplementedServiceServer()
}

func RegisterServiceServer(s grpc.ServiceRegistrar, srv ServiceServer) {
	s.RegisterService(&Service_ServiceDesc, srv)
}

func _Service_Healthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(common.EmptyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).Healthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_Healthy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).Healthy(ctx, req.(*common.EmptyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_PushIdReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushIdReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).PushIdReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_PushIdReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).PushIdReport(ctx, req.(*PushIdReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FilterLabels_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterLabelsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FilterLabels(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FilterLabels_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FilterLabels(ctx, req.(*FilterLabelsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CustomFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CustomFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CustomFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CustomFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CustomFilter(ctx, req.(*CustomFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UserProfileField_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserProfileFieldRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UserProfileField(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UserProfileField_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UserProfileField(ctx, req.(*UserProfileFieldRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetByUserIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetByUserId(ctx, req.(*GetByUserIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserprofileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetUserProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetUserProfile(ctx, req.(*GetUserprofileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_AddLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Label)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).AddLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_AddLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).AddLabel(ctx, req.(*Label))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetLabel(ctx, req.(*GetLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UpdateLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Label)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UpdateLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UpdateLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UpdateLabel(ctx, req.(*Label))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_UpdateLabelStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLabelStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).UpdateLabelStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_UpdateLabelStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).UpdateLabelStatus(ctx, req.(*UpdateLabelStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_DeleteLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).DeleteLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_DeleteLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).DeleteLabel(ctx, req.(*DeleteLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_ListLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).ListLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_ListLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).ListLabel(ctx, req.(*ListLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_CountByLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountByLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).CountByLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_CountByLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).CountByLabel(ctx, req.(*CountByLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_FilterByLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterByLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).FilterByLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_FilterByLabel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).FilterByLabel(ctx, req.(*FilterByLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_EditorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditorListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).EditorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_EditorList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).EditorList(ctx, req.(*EditorListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_LabelCountry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelCountryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).LabelCountry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_LabelCountry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).LabelCountry(ctx, req.(*LabelCountryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_LabelLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelLanguageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).LabelLanguage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_LabelLanguage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).LabelLanguage(ctx, req.(*LabelLanguageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_AddRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Recall)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).AddRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_AddRecall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).AddRecall(ctx, req.(*Recall))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRecalls_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRecalls(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRecalls_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRecalls(ctx, req.(*GetRecallsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRecallExecRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallExecRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRecallExecRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRecallExecRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRecallExecRecords(ctx, req.(*GetRecallExecRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_DeleteRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRecallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).DeleteRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_DeleteRecall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).DeleteRecall(ctx, req.(*DeleteRecallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_SetRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRecallRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).SetRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_SetRecall_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).SetRecall(ctx, req.(*SetRecallRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRecallTouch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallTouchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRecallTouch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRecallTouch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRecallTouch(ctx, req.(*GetRecallTouchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Service_GetRecallCoverUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecallCoverUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServiceServer).GetRecallCoverUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Service_GetRecallCoverUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServiceServer).GetRecallCoverUsers(ctx, req.(*GetRecallCoverUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Service_ServiceDesc is the grpc.ServiceDesc for Service service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Service_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.user_profile.v1.Service",
	HandlerType: (*ServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Healthy",
			Handler:    _Service_Healthy_Handler,
		},
		{
			MethodName: "PushIdReport",
			Handler:    _Service_PushIdReport_Handler,
		},
		{
			MethodName: "FilterLabels",
			Handler:    _Service_FilterLabels_Handler,
		},
		{
			MethodName: "CustomFilter",
			Handler:    _Service_CustomFilter_Handler,
		},
		{
			MethodName: "UserProfileField",
			Handler:    _Service_UserProfileField_Handler,
		},
		{
			MethodName: "GetByUserId",
			Handler:    _Service_GetByUserId_Handler,
		},
		{
			MethodName: "GetUserProfile",
			Handler:    _Service_GetUserProfile_Handler,
		},
		{
			MethodName: "AddLabel",
			Handler:    _Service_AddLabel_Handler,
		},
		{
			MethodName: "GetLabel",
			Handler:    _Service_GetLabel_Handler,
		},
		{
			MethodName: "UpdateLabel",
			Handler:    _Service_UpdateLabel_Handler,
		},
		{
			MethodName: "UpdateLabelStatus",
			Handler:    _Service_UpdateLabelStatus_Handler,
		},
		{
			MethodName: "DeleteLabel",
			Handler:    _Service_DeleteLabel_Handler,
		},
		{
			MethodName: "ListLabel",
			Handler:    _Service_ListLabel_Handler,
		},
		{
			MethodName: "CountByLabel",
			Handler:    _Service_CountByLabel_Handler,
		},
		{
			MethodName: "FilterByLabel",
			Handler:    _Service_FilterByLabel_Handler,
		},
		{
			MethodName: "EditorList",
			Handler:    _Service_EditorList_Handler,
		},
		{
			MethodName: "LabelCountry",
			Handler:    _Service_LabelCountry_Handler,
		},
		{
			MethodName: "LabelLanguage",
			Handler:    _Service_LabelLanguage_Handler,
		},
		{
			MethodName: "AddRecall",
			Handler:    _Service_AddRecall_Handler,
		},
		{
			MethodName: "GetRecalls",
			Handler:    _Service_GetRecalls_Handler,
		},
		{
			MethodName: "GetRecallExecRecords",
			Handler:    _Service_GetRecallExecRecords_Handler,
		},
		{
			MethodName: "DeleteRecall",
			Handler:    _Service_DeleteRecall_Handler,
		},
		{
			MethodName: "SetRecall",
			Handler:    _Service_SetRecall_Handler,
		},
		{
			MethodName: "GetRecallTouch",
			Handler:    _Service_GetRecallTouch_Handler,
		},
		{
			MethodName: "GetRecallCoverUsers",
			Handler:    _Service_GetRecallCoverUsers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user_profile/v1/service.proto",
}
