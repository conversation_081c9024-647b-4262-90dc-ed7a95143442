package v1

import (
	"encoding/json"

	"github.com/airunny/wiki-go-tools/middleware"
)

func (r *ExpressPushCallbackReply) Body() []byte {
	response := map[string]interface{}{
		"result":      r.Result,
		"return_code": r.ReturnCode,
		"message":     r.Message,
	}
	data, _ := json.Marshal(response)
	return data
}

func (r *ExpressPushCallbackReply) ContentType() string {
	return "application/json; charset=utf-8"
}

var _ middleware.CustomReply = (*ExpressPushCallbackReply)(nil)
