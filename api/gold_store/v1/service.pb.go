// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: gold_store/v1/service.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	common "gold_store/api/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ==================================== 金币商城 =======================================
type QuicAccessJumpType int32

const (
	QuicAccessJumpType_QuicAccessJumpTypeGiftCard          QuicAccessJumpType = 0 // 礼品卡
	QuicAccessJumpType_QuicAccessJumpTypeEarnPoint         QuicAccessJumpType = 1 // 赚积分
	QuicAccessJumpType_QuicAccessJumpTypePointsStore       QuicAccessJumpType = 2 // 积分商城
	QuicAccessJumpType_QuicAccessJumpTypeAddressManagement QuicAccessJumpType = 3 // 地址管理
)

// Enum value maps for QuicAccessJumpType.
var (
	QuicAccessJumpType_name = map[int32]string{
		0: "QuicAccessJumpTypeGiftCard",
		1: "QuicAccessJumpTypeEarnPoint",
		2: "QuicAccessJumpTypePointsStore",
		3: "QuicAccessJumpTypeAddressManagement",
	}
	QuicAccessJumpType_value = map[string]int32{
		"QuicAccessJumpTypeGiftCard":          0,
		"QuicAccessJumpTypeEarnPoint":         1,
		"QuicAccessJumpTypePointsStore":       2,
		"QuicAccessJumpTypeAddressManagement": 3,
	}
)

func (x QuicAccessJumpType) Enum() *QuicAccessJumpType {
	p := new(QuicAccessJumpType)
	*p = x
	return p
}

func (x QuicAccessJumpType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuicAccessJumpType) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_service_proto_enumTypes[0].Descriptor()
}

func (QuicAccessJumpType) Type() protoreflect.EnumType {
	return &file_gold_store_v1_service_proto_enumTypes[0]
}

func (x QuicAccessJumpType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuicAccessJumpType.Descriptor instead.
func (QuicAccessJumpType) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{0}
}

// ==================================== 商品 =======================================
type GoodsLabelType int32

const (
	GoodsLabelType_GoodsLabelImage GoodsLabelType = 0 // 图片
	GoodsLabelType_GoodsLabelText  GoodsLabelType = 1 // 文本
)

// Enum value maps for GoodsLabelType.
var (
	GoodsLabelType_name = map[int32]string{
		0: "GoodsLabelImage",
		1: "GoodsLabelText",
	}
	GoodsLabelType_value = map[string]int32{
		"GoodsLabelImage": 0,
		"GoodsLabelText":  1,
	}
)

func (x GoodsLabelType) Enum() *GoodsLabelType {
	p := new(GoodsLabelType)
	*p = x
	return p
}

func (x GoodsLabelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoodsLabelType) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_service_proto_enumTypes[1].Descriptor()
}

func (GoodsLabelType) Type() protoreflect.EnumType {
	return &file_gold_store_v1_service_proto_enumTypes[1]
}

func (x GoodsLabelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GoodsLabelType.Descriptor instead.
func (GoodsLabelType) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{1}
}

type OrderPaymentMethodIcon int32

const (
	OrderPaymentMethodIcon_OrderPaymentMethodIcon_UNKNOWN OrderPaymentMethodIcon = 0 // 未知
	OrderPaymentMethodIcon_OrderPaymentMethodIcon_GOLD    OrderPaymentMethodIcon = 1 // 金币
	OrderPaymentMethodIcon_OrderPaymentMethodIcon_POINTS  OrderPaymentMethodIcon = 2 // 积分
)

// Enum value maps for OrderPaymentMethodIcon.
var (
	OrderPaymentMethodIcon_name = map[int32]string{
		0: "OrderPaymentMethodIcon_UNKNOWN",
		1: "OrderPaymentMethodIcon_GOLD",
		2: "OrderPaymentMethodIcon_POINTS",
	}
	OrderPaymentMethodIcon_value = map[string]int32{
		"OrderPaymentMethodIcon_UNKNOWN": 0,
		"OrderPaymentMethodIcon_GOLD":    1,
		"OrderPaymentMethodIcon_POINTS":  2,
	}
)

func (x OrderPaymentMethodIcon) Enum() *OrderPaymentMethodIcon {
	p := new(OrderPaymentMethodIcon)
	*p = x
	return p
}

func (x OrderPaymentMethodIcon) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderPaymentMethodIcon) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_service_proto_enumTypes[2].Descriptor()
}

func (OrderPaymentMethodIcon) Type() protoreflect.EnumType {
	return &file_gold_store_v1_service_proto_enumTypes[2]
}

func (x OrderPaymentMethodIcon) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderPaymentMethodIcon.Descriptor instead.
func (OrderPaymentMethodIcon) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{2}
}

// ==================================== 礼品卡 ====================================
type GiftCardStatus int32

const (
	GiftCardStatus_GiftCardStatusUnknown  GiftCardStatus = 0 // 未知状态
	GiftCardStatus_GiftCardStatusEnable   GiftCardStatus = 1 // 可用
	GiftCardStatus_GiftCardStatusNotReady GiftCardStatus = 2 // 未到开始时间
	GiftCardStatus_GiftCardStatusExpired  GiftCardStatus = 3 // 已过期
	GiftCardStatus_GiftCardStatusUsed     GiftCardStatus = 4 //  已使用
)

// Enum value maps for GiftCardStatus.
var (
	GiftCardStatus_name = map[int32]string{
		0: "GiftCardStatusUnknown",
		1: "GiftCardStatusEnable",
		2: "GiftCardStatusNotReady",
		3: "GiftCardStatusExpired",
		4: "GiftCardStatusUsed",
	}
	GiftCardStatus_value = map[string]int32{
		"GiftCardStatusUnknown":  0,
		"GiftCardStatusEnable":   1,
		"GiftCardStatusNotReady": 2,
		"GiftCardStatusExpired":  3,
		"GiftCardStatusUsed":     4,
	}
)

func (x GiftCardStatus) Enum() *GiftCardStatus {
	p := new(GiftCardStatus)
	*p = x
	return p
}

func (x GiftCardStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GiftCardStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_service_proto_enumTypes[3].Descriptor()
}

func (GiftCardStatus) Type() protoreflect.EnumType {
	return &file_gold_store_v1_service_proto_enumTypes[3]
}

func (x GiftCardStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GiftCardStatus.Descriptor instead.
func (GiftCardStatus) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{3}
}

type UserGiftCardTabType int32

const (
	UserGiftCardTabType_UserGiftCardTabTypeALL     UserGiftCardTabType = 0 // 全部
	UserGiftCardTabType_UserGiftCardTabTypeENABLE  UserGiftCardTabType = 1 // 可用
	UserGiftCardTabType_UserGiftCardTabTypeDISABLE UserGiftCardTabType = 2 // 不可用
)

// Enum value maps for UserGiftCardTabType.
var (
	UserGiftCardTabType_name = map[int32]string{
		0: "UserGiftCardTabTypeALL",
		1: "UserGiftCardTabTypeENABLE",
		2: "UserGiftCardTabTypeDISABLE",
	}
	UserGiftCardTabType_value = map[string]int32{
		"UserGiftCardTabTypeALL":     0,
		"UserGiftCardTabTypeENABLE":  1,
		"UserGiftCardTabTypeDISABLE": 2,
	}
)

func (x UserGiftCardTabType) Enum() *UserGiftCardTabType {
	p := new(UserGiftCardTabType)
	*p = x
	return p
}

func (x UserGiftCardTabType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserGiftCardTabType) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_service_proto_enumTypes[4].Descriptor()
}

func (UserGiftCardTabType) Type() protoreflect.EnumType {
	return &file_gold_store_v1_service_proto_enumTypes[4]
}

func (x UserGiftCardTabType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserGiftCardTabType.Descriptor instead.
func (UserGiftCardTabType) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{4}
}

// 物流状态枚举
type LogisticStatus int32

const (
	LogisticStatus_LOGISTIC_STATUS_UNKNOWN      LogisticStatus = 0 // 未知状态
	LogisticStatus_LOGISTIC_STATUS_ACCEPTED     LogisticStatus = 1 // 已收寄
	LogisticStatus_LOGISTIC_STATUS_IN_TRANSIT   LogisticStatus = 2 // 运输中
	LogisticStatus_LOGISTIC_STATUS_OUT_DELIVERY LogisticStatus = 3 // 派送中
	LogisticStatus_LOGISTIC_STATUS_DELIVERED    LogisticStatus = 4 // 已签收
	LogisticStatus_LOGISTIC_STATUS_EXCEPTION    LogisticStatus = 5 // 异常
	LogisticStatus_LOGISTIC_STATUS_RETURNED     LogisticStatus = 6 // 退回
)

// Enum value maps for LogisticStatus.
var (
	LogisticStatus_name = map[int32]string{
		0: "LOGISTIC_STATUS_UNKNOWN",
		1: "LOGISTIC_STATUS_ACCEPTED",
		2: "LOGISTIC_STATUS_IN_TRANSIT",
		3: "LOGISTIC_STATUS_OUT_DELIVERY",
		4: "LOGISTIC_STATUS_DELIVERED",
		5: "LOGISTIC_STATUS_EXCEPTION",
		6: "LOGISTIC_STATUS_RETURNED",
	}
	LogisticStatus_value = map[string]int32{
		"LOGISTIC_STATUS_UNKNOWN":      0,
		"LOGISTIC_STATUS_ACCEPTED":     1,
		"LOGISTIC_STATUS_IN_TRANSIT":   2,
		"LOGISTIC_STATUS_OUT_DELIVERY": 3,
		"LOGISTIC_STATUS_DELIVERED":    4,
		"LOGISTIC_STATUS_EXCEPTION":    5,
		"LOGISTIC_STATUS_RETURNED":     6,
	}
)

func (x LogisticStatus) Enum() *LogisticStatus {
	p := new(LogisticStatus)
	*p = x
	return p
}

func (x LogisticStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogisticStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_gold_store_v1_service_proto_enumTypes[5].Descriptor()
}

func (LogisticStatus) Type() protoreflect.EnumType {
	return &file_gold_store_v1_service_proto_enumTypes[5]
}

func (x LogisticStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogisticStatus.Descriptor instead.
func (LogisticStatus) EnumDescriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{5}
}

type QuickAccessItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string             `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Icon     string             `protobuf:"bytes,2,opt,name=icon,json=icon,proto3" json:"icon"`
	JumpType QuicAccessJumpType `protobuf:"varint,3,opt,name=jump_type,json=jumpType,proto3,enum=api.gold_store.v1.QuicAccessJumpType" json:"jump_type"`
}

func (x *QuickAccessItem) Reset() {
	*x = QuickAccessItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuickAccessItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickAccessItem) ProtoMessage() {}

func (x *QuickAccessItem) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickAccessItem.ProtoReflect.Descriptor instead.
func (*QuickAccessItem) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{0}
}

func (x *QuickAccessItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QuickAccessItem) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *QuickAccessItem) GetJumpType() QuicAccessJumpType {
	if x != nil {
		return x.JumpType
	}
	return QuicAccessJumpType_QuicAccessJumpTypeGiftCard
}

type GoldStoreQuickAccessReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*QuickAccessItem `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GoldStoreQuickAccessReply) Reset() {
	*x = GoldStoreQuickAccessReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoldStoreQuickAccessReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoldStoreQuickAccessReply) ProtoMessage() {}

func (x *GoldStoreQuickAccessReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoldStoreQuickAccessReply.ProtoReflect.Descriptor instead.
func (*GoldStoreQuickAccessReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{1}
}

func (x *GoldStoreQuickAccessReply) GetItems() []*QuickAccessItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type MyGoldJumpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timezone string `protobuf:"bytes,1,opt,name=timezone,json=timezone,proto3" json:"timezone"`
}

func (x *MyGoldJumpRequest) Reset() {
	*x = MyGoldJumpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyGoldJumpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyGoldJumpRequest) ProtoMessage() {}

func (x *MyGoldJumpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyGoldJumpRequest.ProtoReflect.Descriptor instead.
func (*MyGoldJumpRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{2}
}

func (x *MyGoldJumpRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type MyGoldJumpReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JumpNew bool  `protobuf:"varint,1,opt,name=jump_new,json=jumpNew,proto3" json:"jump_new"`
	Sign    bool  `protobuf:"varint,2,opt,name=sign,json=sign,proto3" json:"sign"`
	Points  int32 `protobuf:"varint,3,opt,name=points,json=points,proto3" json:"points"`
}

func (x *MyGoldJumpReply) Reset() {
	*x = MyGoldJumpReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MyGoldJumpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MyGoldJumpReply) ProtoMessage() {}

func (x *MyGoldJumpReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MyGoldJumpReply.ProtoReflect.Descriptor instead.
func (*MyGoldJumpReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{3}
}

func (x *MyGoldJumpReply) GetJumpNew() bool {
	if x != nil {
		return x.JumpNew
	}
	return false
}

func (x *MyGoldJumpReply) GetSign() bool {
	if x != nil {
		return x.Sign
	}
	return false
}

func (x *MyGoldJumpReply) GetPoints() int32 {
	if x != nil {
		return x.Points
	}
	return 0
}

type GoodsLabel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  GoodsLabelType `protobuf:"varint,1,opt,name=type,json=type,proto3,enum=api.gold_store.v1.GoodsLabelType" json:"type"`
	Image string         `protobuf:"bytes,2,opt,name=image,json=image,proto3" json:"image"`
	Text  string         `protobuf:"bytes,3,opt,name=text,json=text,proto3" json:"text"`
	Color string         `protobuf:"bytes,4,opt,name=color,json=color,proto3" json:"color"`
	Id    string         `protobuf:"bytes,5,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GoodsLabel) Reset() {
	*x = GoodsLabel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsLabel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsLabel) ProtoMessage() {}

func (x *GoodsLabel) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsLabel.ProtoReflect.Descriptor instead.
func (*GoodsLabel) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{4}
}

func (x *GoodsLabel) GetType() GoodsLabelType {
	if x != nil {
		return x.Type
	}
	return GoodsLabelType_GoodsLabelImage
}

func (x *GoodsLabel) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *GoodsLabel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *GoodsLabel) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *GoodsLabel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GoodsListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId   string        `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	Image     *Image        `protobuf:"bytes,2,opt,name=image,json=image,proto3" json:"image"`
	Name      string        `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Title     string        `protobuf:"bytes,4,opt,name=title,json=title,proto3" json:"title"`
	Price     float32       `protobuf:"fixed32,5,opt,name=price,json=price,proto3" json:"price"`
	PriceShow string        `protobuf:"bytes,6,opt,name=price_show,json=priceShow,proto3" json:"price_show"`
	Labels    []*GoodsLabel `protobuf:"bytes,7,rep,name=labels,json=labels,proto3" json:"labels"`
	Status    GoodsStatus   `protobuf:"varint,8,opt,name=status,json=status,proto3,enum=api.gold_store.v1.GoodsStatus" json:"status"`
	Category  GoodsCategory `protobuf:"varint,9,opt,name=category,json=category,proto3,enum=api.gold_store.v1.GoodsCategory" json:"category"`
}

func (x *GoodsListItem) Reset() {
	*x = GoodsListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListItem) ProtoMessage() {}

func (x *GoodsListItem) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListItem.ProtoReflect.Descriptor instead.
func (*GoodsListItem) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{5}
}

func (x *GoodsListItem) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *GoodsListItem) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GoodsListItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GoodsListItem) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GoodsListItem) GetPriceShow() string {
	if x != nil {
		return x.PriceShow
	}
	return ""
}

func (x *GoodsListItem) GetLabels() []*GoodsLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *GoodsListItem) GetStatus() GoodsStatus {
	if x != nil {
		return x.Status
	}
	return GoodsStatus_GoodsStatusOff
}

func (x *GoodsListItem) GetCategory() GoodsCategory {
	if x != nil {
		return x.Category
	}
	return GoodsCategory_GOODS_CATEGORY_PHYSICAL
}

type GoodsDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId          string        `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	Name             string        `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Title            string        `protobuf:"bytes,3,opt,name=title,json=title,proto3" json:"title"`
	Description      string        `protobuf:"bytes,4,opt,name=description,json=description,proto3" json:"description"`
	Price            float32       `protobuf:"fixed32,5,opt,name=price,json=price,proto3" json:"price"`
	Sales            int32         `protobuf:"varint,6,opt,name=sales,json=sales,proto3" json:"sales"`
	FreeShipping     bool          `protobuf:"varint,7,opt,name=free_shipping,json=freeShipping,proto3" json:"free_shipping"`
	SelectedSkuId    string        `protobuf:"bytes,8,opt,name=selected_sku_id,json=selectedSkuId,proto3" json:"selected_sku_id"`
	SelectedSpecDesc string        `protobuf:"bytes,9,opt,name=selected_spec_desc,json=selectedSpecDesc,proto3" json:"selected_spec_desc"`
	Category         GoodsCategory `protobuf:"varint,10,opt,name=category,json=category,proto3,enum=api.gold_store.v1.GoodsCategory" json:"category"`
	Image            *Image        `protobuf:"bytes,11,opt,name=image,json=image,proto3" json:"image"`
	Disable          bool          `protobuf:"varint,12,opt,name=disable,json=disable,proto3" json:"disable"`
	Status           GoodsStatus   `protobuf:"varint,13,opt,name=status,json=status,proto3,enum=api.gold_store.v1.GoodsStatus" json:"status"`
	BestSellers      []*GoodsLabel `protobuf:"bytes,14,rep,name=best_sellers,json=bestSellers,proto3" json:"best_sellers"`
	Labels           []*GoodsLabel `protobuf:"bytes,15,rep,name=labels,json=labels,proto3" json:"labels"`
	Carousels        []*Image      `protobuf:"bytes,16,rep,name=carousels,json=carousels,proto3" json:"carousels"`
	Details          []*Image      `protobuf:"bytes,17,rep,name=details,json=details,proto3" json:"details"`
	Specs            []*GoodsSpec  `protobuf:"bytes,18,rep,name=specs,json=specs,proto3" json:"specs"`
	Skus             []*GoodsSku   `protobuf:"bytes,19,rep,name=skus,json=skus,proto3" json:"skus"`
	BuyLimit         int32         `protobuf:"varint,20,opt,name=buy_limit,json=buyLimit,proto3" json:"buy_limit"`
}

func (x *GoodsDetail) Reset() {
	*x = GoodsDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsDetail) ProtoMessage() {}

func (x *GoodsDetail) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsDetail.ProtoReflect.Descriptor instead.
func (*GoodsDetail) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{6}
}

func (x *GoodsDetail) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *GoodsDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GoodsDetail) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GoodsDetail) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GoodsDetail) GetSales() int32 {
	if x != nil {
		return x.Sales
	}
	return 0
}

func (x *GoodsDetail) GetFreeShipping() bool {
	if x != nil {
		return x.FreeShipping
	}
	return false
}

func (x *GoodsDetail) GetSelectedSkuId() string {
	if x != nil {
		return x.SelectedSkuId
	}
	return ""
}

func (x *GoodsDetail) GetSelectedSpecDesc() string {
	if x != nil {
		return x.SelectedSpecDesc
	}
	return ""
}

func (x *GoodsDetail) GetCategory() GoodsCategory {
	if x != nil {
		return x.Category
	}
	return GoodsCategory_GOODS_CATEGORY_PHYSICAL
}

func (x *GoodsDetail) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GoodsDetail) GetDisable() bool {
	if x != nil {
		return x.Disable
	}
	return false
}

func (x *GoodsDetail) GetStatus() GoodsStatus {
	if x != nil {
		return x.Status
	}
	return GoodsStatus_GoodsStatusOff
}

func (x *GoodsDetail) GetBestSellers() []*GoodsLabel {
	if x != nil {
		return x.BestSellers
	}
	return nil
}

func (x *GoodsDetail) GetLabels() []*GoodsLabel {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *GoodsDetail) GetCarousels() []*Image {
	if x != nil {
		return x.Carousels
	}
	return nil
}

func (x *GoodsDetail) GetDetails() []*Image {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *GoodsDetail) GetSpecs() []*GoodsSpec {
	if x != nil {
		return x.Specs
	}
	return nil
}

func (x *GoodsDetail) GetSkus() []*GoodsSku {
	if x != nil {
		return x.Skus
	}
	return nil
}

func (x *GoodsDetail) GetBuyLimit() int32 {
	if x != nil {
		return x.BuyLimit
	}
	return 0
}

type GoodsTabRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GoodsTabRequest) Reset() {
	*x = GoodsTabRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsTabRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsTabRequest) ProtoMessage() {}

func (x *GoodsTabRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsTabRequest.ProtoReflect.Descriptor instead.
func (*GoodsTabRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{7}
}

type GoodsTab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name     string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Selected bool   `protobuf:"varint,3,opt,name=selected,json=selected,proto3" json:"selected"`
}

func (x *GoodsTab) Reset() {
	*x = GoodsTab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsTab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsTab) ProtoMessage() {}

func (x *GoodsTab) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsTab.ProtoReflect.Descriptor instead.
func (*GoodsTab) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{8}
}

func (x *GoodsTab) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GoodsTab) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GoodsTab) GetSelected() bool {
	if x != nil {
		return x.Selected
	}
	return false
}

type GoodsTabReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tabs  []*GoodsTab      `protobuf:"bytes,1,rep,name=tabs,json=tabs,proto3" json:"tabs"`
	Goods []*GoodsListItem `protobuf:"bytes,4,rep,name=goods,json=goods,proto3" json:"goods"`
}

func (x *GoodsTabReply) Reset() {
	*x = GoodsTabReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsTabReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsTabReply) ProtoMessage() {}

func (x *GoodsTabReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsTabReply.ProtoReflect.Descriptor instead.
func (*GoodsTabReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{9}
}

func (x *GoodsTabReply) GetTabs() []*GoodsTab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *GoodsTabReply) GetGoods() []*GoodsListItem {
	if x != nil {
		return x.Goods
	}
	return nil
}

type GoodsListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TabId string `protobuf:"bytes,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id"`
	Size  int32  `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Page  int32  `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`
}

func (x *GoodsListRequest) Reset() {
	*x = GoodsListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListRequest) ProtoMessage() {}

func (x *GoodsListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListRequest.ProtoReflect.Descriptor instead.
func (*GoodsListRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{10}
}

func (x *GoodsListRequest) GetTabId() string {
	if x != nil {
		return x.TabId
	}
	return ""
}

func (x *GoodsListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GoodsListRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GoodsListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Goods []*GoodsListItem `protobuf:"bytes,4,rep,name=goods,json=goods,proto3" json:"goods"`
}

func (x *GoodsListReply) Reset() {
	*x = GoodsListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsListReply) ProtoMessage() {}

func (x *GoodsListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsListReply.ProtoReflect.Descriptor instead.
func (*GoodsListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{11}
}

func (x *GoodsListReply) GetGoods() []*GoodsListItem {
	if x != nil {
		return x.Goods
	}
	return nil
}

type GoodsDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId string `protobuf:"bytes,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
}

func (x *GoodsDetailRequest) Reset() {
	*x = GoodsDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsDetailRequest) ProtoMessage() {}

func (x *GoodsDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsDetailRequest.ProtoReflect.Descriptor instead.
func (*GoodsDetailRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{12}
}

func (x *GoodsDetailRequest) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

type BestGoodsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BestGoodsRequest) Reset() {
	*x = BestGoodsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BestGoodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BestGoodsRequest) ProtoMessage() {}

func (x *BestGoodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BestGoodsRequest.ProtoReflect.Descriptor instead.
func (*BestGoodsRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{13}
}

type BestGoodsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Goods []*GoodsListItem `protobuf:"bytes,1,rep,name=goods,json=goods,proto3" json:"goods"`
}

func (x *BestGoodsReply) Reset() {
	*x = BestGoodsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BestGoodsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BestGoodsReply) ProtoMessage() {}

func (x *BestGoodsReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BestGoodsReply.ProtoReflect.Descriptor instead.
func (*BestGoodsReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{14}
}

func (x *BestGoodsReply) GetGoods() []*GoodsListItem {
	if x != nil {
		return x.Goods
	}
	return nil
}

// ============================================== 地址=================================================
type CountryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,json=code,proto3" json:"code"`
	Name string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
}

func (x *CountryInfo) Reset() {
	*x = CountryInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CountryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountryInfo) ProtoMessage() {}

func (x *CountryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountryInfo.ProtoReflect.Descriptor instead.
func (*CountryInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{15}
}

func (x *CountryInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CountryInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetCountryListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*CountryInfo `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetCountryListReply) Reset() {
	*x = GetCountryListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCountryListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCountryListReply) ProtoMessage() {}

func (x *GetCountryListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCountryListReply.ProtoReflect.Descriptor instead.
func (*GetCountryListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetCountryListReply) GetItems() []*CountryInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetAddressListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*Address `protobuf:"bytes,1,rep,name=items,json=items,proto3" json:"items"`
}

func (x *GetAddressListReply) Reset() {
	*x = GetAddressListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressListReply) ProtoMessage() {}

func (x *GetAddressListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressListReply.ProtoReflect.Descriptor instead.
func (*GetAddressListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetAddressListReply) GetItems() []*Address {
	if x != nil {
		return x.Items
	}
	return nil
}

type AddAddressReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	AddressShow string `protobuf:"bytes,2,opt,name=address_show,json=addressShow,proto3" json:"address_show"`
}

func (x *AddAddressReply) Reset() {
	*x = AddAddressReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAddressReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAddressReply) ProtoMessage() {}

func (x *AddAddressReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAddressReply.ProtoReflect.Descriptor instead.
func (*AddAddressReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{18}
}

func (x *AddAddressReply) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddAddressReply) GetAddressShow() string {
	if x != nil {
		return x.AddressShow
	}
	return ""
}

type SetAddressDefaultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *SetAddressDefaultRequest) Reset() {
	*x = SetAddressDefaultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetAddressDefaultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAddressDefaultRequest) ProtoMessage() {}

func (x *SetAddressDefaultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAddressDefaultRequest.ProtoReflect.Descriptor instead.
func (*SetAddressDefaultRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{19}
}

func (x *SetAddressDefaultRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteAddressRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *DeleteAddressRequest) Reset() {
	*x = DeleteAddressRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressRequest) ProtoMessage() {}

func (x *DeleteAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressRequest.ProtoReflect.Descriptor instead.
func (*DeleteAddressRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteAddressRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAddressDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
}

func (x *GetAddressDetailRequest) Reset() {
	*x = GetAddressDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressDetailRequest) ProtoMessage() {}

func (x *GetAddressDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressDetailRequest.ProtoReflect.Descriptor instead.
func (*GetAddressDetailRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{21}
}

func (x *GetAddressDetailRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAddressCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int32 `protobuf:"varint,1,opt,name=count,json=count,proto3" json:"count"`
}

func (x *GetAddressCountReply) Reset() {
	*x = GetAddressCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAddressCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAddressCountReply) ProtoMessage() {}

func (x *GetAddressCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAddressCountReply.ProtoReflect.Descriptor instead.
func (*GetAddressCountReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{22}
}

func (x *GetAddressCountReply) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// ==================================== 订单 ====================================
type OrderTotalAmountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method          PaymentMethod   `protobuf:"varint,1,opt,name=method,json=method,proto3,enum=api.gold_store.v1.PaymentMethod" json:"method"`
	OperationTicket string          `protobuf:"bytes,2,opt,name=operation_ticket,json=operationTicket,proto3" json:"operation_ticket"`
	GoodsId         string          `protobuf:"bytes,3,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	AddressId       int32           `protobuf:"varint,4,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	Quantity        int32           `protobuf:"varint,5,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	Specs           []*SpecSelected `protobuf:"bytes,7,rep,name=specs,json=specs,proto3" json:"specs"`
}

func (x *OrderTotalAmountRequest) Reset() {
	*x = OrderTotalAmountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTotalAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTotalAmountRequest) ProtoMessage() {}

func (x *OrderTotalAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTotalAmountRequest.ProtoReflect.Descriptor instead.
func (*OrderTotalAmountRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{23}
}

func (x *OrderTotalAmountRequest) GetMethod() PaymentMethod {
	if x != nil {
		return x.Method
	}
	return PaymentMethod_GOLD
}

func (x *OrderTotalAmountRequest) GetOperationTicket() string {
	if x != nil {
		return x.OperationTicket
	}
	return ""
}

func (x *OrderTotalAmountRequest) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *OrderTotalAmountRequest) GetAddressId() int32 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

func (x *OrderTotalAmountRequest) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderTotalAmountRequest) GetSpecs() []*SpecSelected {
	if x != nil {
		return x.Specs
	}
	return nil
}

type OrderTotalAmountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalAmount float32  `protobuf:"fixed32,1,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	ShippingFee float32  `protobuf:"fixed32,2,opt,name=shipping_fee,json=shippingFee,proto3" json:"shipping_fee"`
	Address     *Address `protobuf:"bytes,3,opt,name=address,json=address,proto3" json:"address"`
	Code        string   `protobuf:"bytes,4,opt,name=code,json=code,proto3" json:"code"`
}

func (x *OrderTotalAmountReply) Reset() {
	*x = OrderTotalAmountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTotalAmountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTotalAmountReply) ProtoMessage() {}

func (x *OrderTotalAmountReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTotalAmountReply.ProtoReflect.Descriptor instead.
func (*OrderTotalAmountReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{24}
}

func (x *OrderTotalAmountReply) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *OrderTotalAmountReply) GetShippingFee() float32 {
	if x != nil {
		return x.ShippingFee
	}
	return 0
}

func (x *OrderTotalAmountReply) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *OrderTotalAmountReply) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type SpecSelected struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpecId  string `protobuf:"bytes,1,opt,name=spec_id,json=specId,proto3" json:"spec_id"`
	ValueId string `protobuf:"bytes,2,opt,name=value_id,json=valueId,proto3" json:"value_id"`
}

func (x *SpecSelected) Reset() {
	*x = SpecSelected{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpecSelected) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecSelected) ProtoMessage() {}

func (x *SpecSelected) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecSelected.ProtoReflect.Descriptor instead.
func (*SpecSelected) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{25}
}

func (x *SpecSelected) GetSpecId() string {
	if x != nil {
		return x.SpecId
	}
	return ""
}

func (x *SpecSelected) GetValueId() string {
	if x != nil {
		return x.ValueId
	}
	return ""
}

type CreateOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method          PaymentMethod   `protobuf:"varint,1,opt,name=method,json=method,proto3,enum=api.gold_store.v1.PaymentMethod" json:"method"`
	OperationTicket string          `protobuf:"bytes,2,opt,name=operation_ticket,json=operationTicket,proto3" json:"operation_ticket"`
	GoodsId         string          `protobuf:"bytes,3,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	AddressId       int32           `protobuf:"varint,4,opt,name=address_id,json=addressId,proto3" json:"address_id"`
	Quantity        int32           `protobuf:"varint,5,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	TotalAmount     float32         `protobuf:"fixed32,6,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	Specs           []*SpecSelected `protobuf:"bytes,7,rep,name=specs,json=specs,proto3" json:"specs"`
}

func (x *CreateOrderRequest) Reset() {
	*x = CreateOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderRequest) ProtoMessage() {}

func (x *CreateOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateOrderRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{26}
}

func (x *CreateOrderRequest) GetMethod() PaymentMethod {
	if x != nil {
		return x.Method
	}
	return PaymentMethod_GOLD
}

func (x *CreateOrderRequest) GetOperationTicket() string {
	if x != nil {
		return x.OperationTicket
	}
	return ""
}

func (x *CreateOrderRequest) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *CreateOrderRequest) GetAddressId() int32 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

func (x *CreateOrderRequest) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CreateOrderRequest) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *CreateOrderRequest) GetSpecs() []*SpecSelected {
	if x != nil {
		return x.Specs
	}
	return nil
}

type PreCheckReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalAmount float32      `protobuf:"fixed32,1,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	ShippingFee float32      `protobuf:"fixed32,2,opt,name=shipping_fee,json=shippingFee,proto3" json:"shipping_fee"`
	Address     *Address     `protobuf:"bytes,4,opt,name=address,json=address,proto3" json:"address"`
	GoodsDetail *GoodsDetail `protobuf:"bytes,5,opt,name=goods_detail,json=goodsDetail,proto3" json:"goods_detail"`
}

func (x *PreCheckReply) Reset() {
	*x = PreCheckReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreCheckReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreCheckReply) ProtoMessage() {}

func (x *PreCheckReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreCheckReply.ProtoReflect.Descriptor instead.
func (*PreCheckReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{27}
}

func (x *PreCheckReply) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *PreCheckReply) GetShippingFee() float32 {
	if x != nil {
		return x.ShippingFee
	}
	return 0
}

func (x *PreCheckReply) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *PreCheckReply) GetGoodsDetail() *GoodsDetail {
	if x != nil {
		return x.GoodsDetail
	}
	return nil
}

type CreateOrderReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
}

func (x *CreateOrderReply) Reset() {
	*x = CreateOrderReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrderReply) ProtoMessage() {}

func (x *CreateOrderReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrderReply.ProtoReflect.Descriptor instead.
func (*CreateOrderReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{28}
}

func (x *CreateOrderReply) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

type OrderFilterRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OrderFilterRequest) Reset() {
	*x = OrderFilterRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderFilterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderFilterRequest) ProtoMessage() {}

func (x *OrderFilterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderFilterRequest.ProtoReflect.Descriptor instead.
func (*OrderFilterRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{29}
}

type OrderFilterValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
}

func (x *OrderFilterValue) Reset() {
	*x = OrderFilterValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderFilterValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderFilterValue) ProtoMessage() {}

func (x *OrderFilterValue) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderFilterValue.ProtoReflect.Descriptor instead.
func (*OrderFilterValue) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{30}
}

func (x *OrderFilterValue) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrderFilterValue) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type OrderFilterGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     string              `protobuf:"bytes,1,opt,name=id,json=id,proto3" json:"id"`
	Name   string              `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Values []*OrderFilterValue `protobuf:"bytes,3,rep,name=values,json=values,proto3" json:"values"`
}

func (x *OrderFilterGroup) Reset() {
	*x = OrderFilterGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderFilterGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderFilterGroup) ProtoMessage() {}

func (x *OrderFilterGroup) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderFilterGroup.ProtoReflect.Descriptor instead.
func (*OrderFilterGroup) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{31}
}

func (x *OrderFilterGroup) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrderFilterGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *OrderFilterGroup) GetValues() []*OrderFilterValue {
	if x != nil {
		return x.Values
	}
	return nil
}

type OrderFilterReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Groups []*OrderFilterGroup `protobuf:"bytes,1,rep,name=groups,json=groups,proto3" json:"groups"`
}

func (x *OrderFilterReply) Reset() {
	*x = OrderFilterReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderFilterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderFilterReply) ProtoMessage() {}

func (x *OrderFilterReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderFilterReply.ProtoReflect.Descriptor instead.
func (*OrderFilterReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{32}
}

func (x *OrderFilterReply) GetGroups() []*OrderFilterGroup {
	if x != nil {
		return x.Groups
	}
	return nil
}

type OrderCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Delivery int32 `protobuf:"varint,1,opt,name=delivery,json=delivery,proto3" json:"delivery"`
	Complete int32 `protobuf:"varint,2,opt,name=complete,json=complete,proto3" json:"complete"`
}

func (x *OrderCountReply) Reset() {
	*x = OrderCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderCountReply) ProtoMessage() {}

func (x *OrderCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderCountReply.ProtoReflect.Descriptor instead.
func (*OrderCountReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{33}
}

func (x *OrderCountReply) GetDelivery() int32 {
	if x != nil {
		return x.Delivery
	}
	return 0
}

func (x *OrderCountReply) GetComplete() int32 {
	if x != nil {
		return x.Complete
	}
	return 0
}

type OrderBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo           string                 `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	GoodsId           string                 `protobuf:"bytes,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	SkuId             string                 `protobuf:"bytes,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	GoodsName         string                 `protobuf:"bytes,4,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	Image             *Image                 `protobuf:"bytes,5,opt,name=image,json=image,proto3" json:"image"`
	Source            OrderSource            `protobuf:"varint,6,opt,name=source,json=source,proto3,enum=api.gold_store.v1.OrderSource" json:"source"`
	SourceShow        string                 `protobuf:"bytes,7,opt,name=source_show,json=sourceShow,proto3" json:"source_show"`
	PaymentMethod     PaymentMethod          `protobuf:"varint,8,opt,name=payment_method,json=paymentMethod,proto3,enum=api.gold_store.v1.PaymentMethod" json:"payment_method"`
	TotalAmountShow   string                 `protobuf:"bytes,9,opt,name=total_amount_show,json=totalAmountShow,proto3" json:"total_amount_show"`
	SpecDesc          string                 `protobuf:"bytes,16,opt,name=spec_desc,json=specDesc,proto3" json:"spec_desc"`
	Price             float32                `protobuf:"fixed32,17,opt,name=price,json=price,proto3" json:"price"`
	PriceShow         string                 `protobuf:"bytes,18,opt,name=price_show,json=priceShow,proto3" json:"price_show"`
	Quantity          int32                  `protobuf:"varint,19,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	TotalAmount       float32                `protobuf:"fixed32,20,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	Status            OrderStatus            `protobuf:"varint,21,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	StatusShow        string                 `protobuf:"bytes,22,opt,name=status_show,json=statusShow,proto3" json:"status_show"`
	PaymentMethodIcon OrderPaymentMethodIcon `protobuf:"varint,23,opt,name=payment_method_icon,json=paymentMethodIcon,proto3,enum=api.gold_store.v1.OrderPaymentMethodIcon" json:"payment_method_icon"`
}

func (x *OrderBase) Reset() {
	*x = OrderBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderBase) ProtoMessage() {}

func (x *OrderBase) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderBase.ProtoReflect.Descriptor instead.
func (*OrderBase) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{34}
}

func (x *OrderBase) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *OrderBase) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *OrderBase) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *OrderBase) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *OrderBase) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *OrderBase) GetSource() OrderSource {
	if x != nil {
		return x.Source
	}
	return OrderSource_STORE
}

func (x *OrderBase) GetSourceShow() string {
	if x != nil {
		return x.SourceShow
	}
	return ""
}

func (x *OrderBase) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_GOLD
}

func (x *OrderBase) GetTotalAmountShow() string {
	if x != nil {
		return x.TotalAmountShow
	}
	return ""
}

func (x *OrderBase) GetSpecDesc() string {
	if x != nil {
		return x.SpecDesc
	}
	return ""
}

func (x *OrderBase) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *OrderBase) GetPriceShow() string {
	if x != nil {
		return x.PriceShow
	}
	return ""
}

func (x *OrderBase) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderBase) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *OrderBase) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *OrderBase) GetStatusShow() string {
	if x != nil {
		return x.StatusShow
	}
	return ""
}

func (x *OrderBase) GetPaymentMethodIcon() OrderPaymentMethodIcon {
	if x != nil {
		return x.PaymentMethodIcon
	}
	return OrderPaymentMethodIcon_OrderPaymentMethodIcon_UNKNOWN
}

type OrderTab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status OrderStatus `protobuf:"varint,1,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	Name   string      `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"` //  bool selected = 3[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "是否选中"}];
}

func (x *OrderTab) Reset() {
	*x = OrderTab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTab) ProtoMessage() {}

func (x *OrderTab) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTab.ProtoReflect.Descriptor instead.
func (*OrderTab) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{35}
}

func (x *OrderTab) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *OrderTab) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetOrderTabRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size int32 `protobuf:"varint,1,opt,name=size,json=size,proto3" json:"size"`
}

func (x *GetOrderTabRequest) Reset() {
	*x = GetOrderTabRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderTabRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderTabRequest) ProtoMessage() {}

func (x *GetOrderTabRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderTabRequest.ProtoReflect.Descriptor instead.
func (*GetOrderTabRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetOrderTabRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

type OrderTabReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tabs []*OrderTab `protobuf:"bytes,1,rep,name=tabs,json=tabs,proto3" json:"tabs"` //  repeated OrderBase orders = 2[(grpc.gateway.protoc_gen_openapiv2.options.openapiv2_field) = {title: "订单列表"}];
}

func (x *OrderTabReply) Reset() {
	*x = OrderTabReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderTabReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderTabReply) ProtoMessage() {}

func (x *OrderTabReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderTabReply.ProtoReflect.Descriptor instead.
func (*OrderTabReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{37}
}

func (x *OrderTabReply) GetTabs() []*OrderTab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

type OrderFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId   string   `protobuf:"bytes,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	ValuesIds []string `protobuf:"bytes,2,rep,name=values_ids,json=valuesIds,proto3" json:"values_ids"`
}

func (x *OrderFilter) Reset() {
	*x = OrderFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderFilter) ProtoMessage() {}

func (x *OrderFilter) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderFilter.ProtoReflect.Descriptor instead.
func (*OrderFilter) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{38}
}

func (x *OrderFilter) GetGroupId() string {
	if x != nil {
		return x.GroupId
	}
	return ""
}

func (x *OrderFilter) GetValuesIds() []string {
	if x != nil {
		return x.ValuesIds
	}
	return nil
}

type OrderListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  OrderStatus         `protobuf:"varint,1,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	Size    int32               `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Offset  string              `protobuf:"bytes,3,opt,name=offset,json=offset,proto3" json:"offset"`
	Filters []*OrderFilterGroup `protobuf:"bytes,4,rep,name=filters,json=filters,proto3" json:"filters"`
}

func (x *OrderListRequest) Reset() {
	*x = OrderListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderListRequest) ProtoMessage() {}

func (x *OrderListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderListRequest.ProtoReflect.Descriptor instead.
func (*OrderListRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{39}
}

func (x *OrderListRequest) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *OrderListRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *OrderListRequest) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

func (x *OrderListRequest) GetFilters() []*OrderFilterGroup {
	if x != nil {
		return x.Filters
	}
	return nil
}

type OrderListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Orders []*OrderBase `protobuf:"bytes,1,rep,name=orders,json=orders,proto3" json:"orders"`
	Offset string       `protobuf:"bytes,2,opt,name=offset,json=offset,proto3" json:"offset"`
}

func (x *OrderListReply) Reset() {
	*x = OrderListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderListReply) ProtoMessage() {}

func (x *OrderListReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderListReply.ProtoReflect.Descriptor instead.
func (*OrderListReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{40}
}

func (x *OrderListReply) GetOrders() []*OrderBase {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *OrderListReply) GetOffset() string {
	if x != nil {
		return x.Offset
	}
	return ""
}

type OrderDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
}

func (x *OrderDetailRequest) Reset() {
	*x = OrderDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetailRequest) ProtoMessage() {}

func (x *OrderDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetailRequest.ProtoReflect.Descriptor instead.
func (*OrderDetailRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{41}
}

func (x *OrderDetailRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

type VPSExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EffectiveTime  string `protobuf:"bytes,1,opt,name=effective_time,json=effectiveTime,proto3" json:"effective_time"`
	ExpirationTime string `protobuf:"bytes,2,opt,name=expiration_time,json=expirationTime,proto3" json:"expiration_time"`
	OpenState      string `protobuf:"bytes,3,opt,name=open_state,json=openState,proto3" json:"open_state"`
	ServerLanguage string `protobuf:"bytes,4,opt,name=server_language,json=serverLanguage,proto3" json:"server_language"`
	ServerCity     string `protobuf:"bytes,5,opt,name=server_city,json=serverCity,proto3" json:"server_city"`
	ServerAccount  string `protobuf:"bytes,6,opt,name=server_account,json=serverAccount,proto3" json:"server_account"`
}

func (x *VPSExtra) Reset() {
	*x = VPSExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPSExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPSExtra) ProtoMessage() {}

func (x *VPSExtra) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPSExtra.ProtoReflect.Descriptor instead.
func (*VPSExtra) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{42}
}

func (x *VPSExtra) GetEffectiveTime() string {
	if x != nil {
		return x.EffectiveTime
	}
	return ""
}

func (x *VPSExtra) GetExpirationTime() string {
	if x != nil {
		return x.ExpirationTime
	}
	return ""
}

func (x *VPSExtra) GetOpenState() string {
	if x != nil {
		return x.OpenState
	}
	return ""
}

func (x *VPSExtra) GetServerLanguage() string {
	if x != nil {
		return x.ServerLanguage
	}
	return ""
}

func (x *VPSExtra) GetServerCity() string {
	if x != nil {
		return x.ServerCity
	}
	return ""
}

func (x *VPSExtra) GetServerAccount() string {
	if x != nil {
		return x.ServerAccount
	}
	return ""
}

type ReportExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SendEmail     string `protobuf:"bytes,1,opt,name=send_email,json=sendEmail,proto3" json:"send_email"`
	Language      string `protobuf:"bytes,2,opt,name=language,json=language,proto3" json:"language"`
	SendState     string `protobuf:"bytes,3,opt,name=send_state,json=sendState,proto3" json:"send_state"`
	GenerateState string `protobuf:"bytes,4,opt,name=generate_state,json=generateState,proto3" json:"generate_state"`
	GoDetails     bool   `protobuf:"varint,5,opt,name=go_details,json=goDetails,proto3" json:"go_details"`
}

func (x *ReportExtra) Reset() {
	*x = ReportExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportExtra) ProtoMessage() {}

func (x *ReportExtra) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportExtra.ProtoReflect.Descriptor instead.
func (*ReportExtra) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{43}
}

func (x *ReportExtra) GetSendEmail() string {
	if x != nil {
		return x.SendEmail
	}
	return ""
}

func (x *ReportExtra) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ReportExtra) GetSendState() string {
	if x != nil {
		return x.SendState
	}
	return ""
}

func (x *ReportExtra) GetGenerateState() string {
	if x != nil {
		return x.GenerateState
	}
	return ""
}

func (x *ReportExtra) GetGoDetails() bool {
	if x != nil {
		return x.GoDetails
	}
	return false
}

type ExhibitionUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName    string `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name"`
	Email       string `protobuf:"bytes,2,opt,name=email,json=email,proto3" json:"email"`
	PhoneNumber string `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number"`
}

func (x *ExhibitionUser) Reset() {
	*x = ExhibitionUser{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitionUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitionUser) ProtoMessage() {}

func (x *ExhibitionUser) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitionUser.ProtoReflect.Descriptor instead.
func (*ExhibitionUser) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{44}
}

func (x *ExhibitionUser) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ExhibitionUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ExhibitionUser) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type ExhibitionExtra struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title       string            `protobuf:"bytes,1,opt,name=title,json=title,proto3" json:"title"`
	Description string            `protobuf:"bytes,2,opt,name=description,json=description,proto3" json:"description"`
	TicketType  string            `protobuf:"bytes,3,opt,name=ticket_type,json=ticketType,proto3" json:"ticket_type"`
	UseExchange bool              `protobuf:"varint,4,opt,name=use_exchange,json=useExchange,proto3" json:"use_exchange"`
	Users       []*ExhibitionUser `protobuf:"bytes,5,rep,name=users,json=users,proto3" json:"users"`
}

func (x *ExhibitionExtra) Reset() {
	*x = ExhibitionExtra{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExhibitionExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExhibitionExtra) ProtoMessage() {}

func (x *ExhibitionExtra) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExhibitionExtra.ProtoReflect.Descriptor instead.
func (*ExhibitionExtra) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{45}
}

func (x *ExhibitionExtra) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ExhibitionExtra) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ExhibitionExtra) GetTicketType() string {
	if x != nil {
		return x.TicketType
	}
	return ""
}

func (x *ExhibitionExtra) GetUseExchange() bool {
	if x != nil {
		return x.UseExchange
	}
	return false
}

func (x *ExhibitionExtra) GetUsers() []*ExhibitionUser {
	if x != nil {
		return x.Users
	}
	return nil
}

type OrderDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo           string                 `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
	GoodsId           string                 `protobuf:"bytes,2,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	SkuId             string                 `protobuf:"bytes,3,opt,name=sku_id,json=skuId,proto3" json:"sku_id"`
	GoodsName         string                 `protobuf:"bytes,4,opt,name=goods_name,json=goodsName,proto3" json:"goods_name"`
	Image             *Image                 `protobuf:"bytes,5,opt,name=image,json=image,proto3" json:"image"`
	Category          GoodsCategory          `protobuf:"varint,6,opt,name=category,json=category,proto3,enum=api.gold_store.v1.GoodsCategory" json:"category"`
	Email             string                 `protobuf:"bytes,7,opt,name=email,json=email,proto3" json:"email"`
	Symbol            string                 `protobuf:"bytes,8,opt,name=symbol,json=symbol,proto3" json:"symbol"`
	Source            OrderSource            `protobuf:"varint,15,opt,name=source,json=source,proto3,enum=api.gold_store.v1.OrderSource" json:"source"`
	SourceShow        string                 `protobuf:"bytes,16,opt,name=source_show,json=sourceShow,proto3" json:"source_show"`
	SpecDesc          string                 `protobuf:"bytes,17,opt,name=spec_desc,json=specDesc,proto3" json:"spec_desc"`
	Price             float32                `protobuf:"fixed32,18,opt,name=price,json=price,proto3" json:"price"`
	PriceShow         string                 `protobuf:"bytes,19,opt,name=price_show,json=priceShow,proto3" json:"price_show"`
	Quantity          int32                  `protobuf:"varint,20,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	ShippingFee       float32                `protobuf:"fixed32,21,opt,name=shipping_fee,json=shippingFee,proto3" json:"shipping_fee"`
	TotalAmount       float32                `protobuf:"fixed32,22,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount"`
	TotalAmountShow   string                 `protobuf:"bytes,23,opt,name=total_amount_show,json=totalAmountShow,proto3" json:"total_amount_show"`
	TotalPay          float32                `protobuf:"fixed32,24,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	TotalPayShow      string                 `protobuf:"bytes,25,opt,name=total_pay_show,json=totalPayShow,proto3" json:"total_pay_show"`
	Status            OrderStatus            `protobuf:"varint,26,opt,name=status,json=status,proto3,enum=api.gold_store.v1.OrderStatus" json:"status"`
	StatusShow        string                 `protobuf:"bytes,27,opt,name=status_show,json=statusShow,proto3" json:"status_show"`
	PaymentMethod     PaymentMethod          `protobuf:"varint,28,opt,name=payment_method,json=paymentMethod,proto3,enum=api.gold_store.v1.PaymentMethod" json:"payment_method"`
	OrderTime         int64                  `protobuf:"varint,29,opt,name=order_time,json=orderTime,proto3" json:"order_time"`
	PayTime           int64                  `protobuf:"varint,30,opt,name=pay_time,json=payTime,proto3" json:"pay_time"`
	DeliveryMethod    string                 `protobuf:"bytes,31,opt,name=delivery_method,json=deliveryMethod,proto3" json:"delivery_method"`
	Address           *Address               `protobuf:"bytes,32,opt,name=address,json=address,proto3" json:"address"`
	CurrentDelivery   *LogisticStep          `protobuf:"bytes,33,opt,name=current_delivery,json=currentDelivery,proto3" json:"current_delivery"`
	VpsExtra          *VPSExtra              `protobuf:"bytes,34,opt,name=vps_extra,json=vpsExtra,proto3" json:"vps_extra"`
	ReportExtra       *ReportExtra           `protobuf:"bytes,35,opt,name=report_extra,json=reportExtra,proto3" json:"report_extra"`
	ExhibitionExtra   *ExhibitionExtra       `protobuf:"bytes,36,opt,name=exhibition_extra,json=exhibitionExtra,proto3" json:"exhibition_extra"`
	SelectedSpecs     []*SpecSelected        `protobuf:"bytes,37,rep,name=selected_specs,json=selectedSpecs,proto3" json:"selected_specs"`
	PaymentMethodIcon OrderPaymentMethodIcon `protobuf:"varint,38,opt,name=payment_method_icon,json=paymentMethodIcon,proto3,enum=api.gold_store.v1.OrderPaymentMethodIcon" json:"payment_method_icon"`
}

func (x *OrderDetail) Reset() {
	*x = OrderDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDetail) ProtoMessage() {}

func (x *OrderDetail) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDetail.ProtoReflect.Descriptor instead.
func (*OrderDetail) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{46}
}

func (x *OrderDetail) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

func (x *OrderDetail) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *OrderDetail) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *OrderDetail) GetGoodsName() string {
	if x != nil {
		return x.GoodsName
	}
	return ""
}

func (x *OrderDetail) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *OrderDetail) GetCategory() GoodsCategory {
	if x != nil {
		return x.Category
	}
	return GoodsCategory_GOODS_CATEGORY_PHYSICAL
}

func (x *OrderDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *OrderDetail) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *OrderDetail) GetSource() OrderSource {
	if x != nil {
		return x.Source
	}
	return OrderSource_STORE
}

func (x *OrderDetail) GetSourceShow() string {
	if x != nil {
		return x.SourceShow
	}
	return ""
}

func (x *OrderDetail) GetSpecDesc() string {
	if x != nil {
		return x.SpecDesc
	}
	return ""
}

func (x *OrderDetail) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *OrderDetail) GetPriceShow() string {
	if x != nil {
		return x.PriceShow
	}
	return ""
}

func (x *OrderDetail) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *OrderDetail) GetShippingFee() float32 {
	if x != nil {
		return x.ShippingFee
	}
	return 0
}

func (x *OrderDetail) GetTotalAmount() float32 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *OrderDetail) GetTotalAmountShow() string {
	if x != nil {
		return x.TotalAmountShow
	}
	return ""
}

func (x *OrderDetail) GetTotalPay() float32 {
	if x != nil {
		return x.TotalPay
	}
	return 0
}

func (x *OrderDetail) GetTotalPayShow() string {
	if x != nil {
		return x.TotalPayShow
	}
	return ""
}

func (x *OrderDetail) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN
}

func (x *OrderDetail) GetStatusShow() string {
	if x != nil {
		return x.StatusShow
	}
	return ""
}

func (x *OrderDetail) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_GOLD
}

func (x *OrderDetail) GetOrderTime() int64 {
	if x != nil {
		return x.OrderTime
	}
	return 0
}

func (x *OrderDetail) GetPayTime() int64 {
	if x != nil {
		return x.PayTime
	}
	return 0
}

func (x *OrderDetail) GetDeliveryMethod() string {
	if x != nil {
		return x.DeliveryMethod
	}
	return ""
}

func (x *OrderDetail) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *OrderDetail) GetCurrentDelivery() *LogisticStep {
	if x != nil {
		return x.CurrentDelivery
	}
	return nil
}

func (x *OrderDetail) GetVpsExtra() *VPSExtra {
	if x != nil {
		return x.VpsExtra
	}
	return nil
}

func (x *OrderDetail) GetReportExtra() *ReportExtra {
	if x != nil {
		return x.ReportExtra
	}
	return nil
}

func (x *OrderDetail) GetExhibitionExtra() *ExhibitionExtra {
	if x != nil {
		return x.ExhibitionExtra
	}
	return nil
}

func (x *OrderDetail) GetSelectedSpecs() []*SpecSelected {
	if x != nil {
		return x.SelectedSpecs
	}
	return nil
}

func (x *OrderDetail) GetPaymentMethodIcon() OrderPaymentMethodIcon {
	if x != nil {
		return x.PaymentMethodIcon
	}
	return OrderPaymentMethodIcon_OrderPaymentMethodIcon_UNKNOWN
}

type OrderLogisticsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderNo string `protobuf:"bytes,1,opt,name=order_no,json=orderNo,proto3" json:"order_no"`
}

func (x *OrderLogisticsRequest) Reset() {
	*x = OrderLogisticsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderLogisticsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderLogisticsRequest) ProtoMessage() {}

func (x *OrderLogisticsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderLogisticsRequest.ProtoReflect.Descriptor instead.
func (*OrderLogisticsRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{47}
}

func (x *OrderLogisticsRequest) GetOrderNo() string {
	if x != nil {
		return x.OrderNo
	}
	return ""
}

type GetOrderLogisticsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CarrierName string          `protobuf:"bytes,1,opt,name=carrier_name,json=carrierName,proto3" json:"carrier_name"`
	CarrierIcon string          `protobuf:"bytes,2,opt,name=carrier_icon,json=carrierIcon,proto3" json:"carrier_icon"`
	TrackingNo  string          `protobuf:"bytes,3,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	Address     *Address        `protobuf:"bytes,4,opt,name=address,json=address,proto3" json:"address"`
	Steps       []*LogisticStep `protobuf:"bytes,5,rep,name=steps,json=steps,proto3" json:"steps"`
}

func (x *GetOrderLogisticsReply) Reset() {
	*x = GetOrderLogisticsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrderLogisticsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrderLogisticsReply) ProtoMessage() {}

func (x *GetOrderLogisticsReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrderLogisticsReply.ProtoReflect.Descriptor instead.
func (*GetOrderLogisticsReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetOrderLogisticsReply) GetCarrierName() string {
	if x != nil {
		return x.CarrierName
	}
	return ""
}

func (x *GetOrderLogisticsReply) GetCarrierIcon() string {
	if x != nil {
		return x.CarrierIcon
	}
	return ""
}

func (x *GetOrderLogisticsReply) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *GetOrderLogisticsReply) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *GetOrderLogisticsReply) GetSteps() []*LogisticStep {
	if x != nil {
		return x.Steps
	}
	return nil
}

type GetUserOrderCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
}

func (x *GetUserOrderCountRequest) Reset() {
	*x = GetUserOrderCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserOrderCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserOrderCountRequest) ProtoMessage() {}

func (x *GetUserOrderCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserOrderCountRequest.ProtoReflect.Descriptor instead.
func (*GetUserOrderCountRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{49}
}

func (x *GetUserOrderCountRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserOrderCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total      int32 `protobuf:"varint,1,opt,name=total,json=total,proto3" json:"total"`
	StoreTotal int32 `protobuf:"varint,2,opt,name=store_total,json=storeTotal,proto3" json:"store_total"`
}

func (x *GetUserOrderCountReply) Reset() {
	*x = GetUserOrderCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserOrderCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserOrderCountReply) ProtoMessage() {}

func (x *GetUserOrderCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserOrderCountReply.ProtoReflect.Descriptor instead.
func (*GetUserOrderCountReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetUserOrderCountReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetUserOrderCountReply) GetStoreTotal() int32 {
	if x != nil {
		return x.StoreTotal
	}
	return 0
}

type ReportOrderPushRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string  `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	OrderId        string  `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id"`
	TraderCode     string  `protobuf:"bytes,3,opt,name=trader_code,json=traderCode,proto3" json:"trader_code"`
	ReportType     int32   `protobuf:"varint,4,opt,name=report_type,json=reportType,proto3" json:"report_type"`
	OrderTime      string  `protobuf:"bytes,5,opt,name=order_time,json=orderTime,proto3" json:"order_time"`
	Price          float32 `protobuf:"fixed32,6,opt,name=price,json=price,proto3" json:"price"`
	SymbolType     int32   `protobuf:"varint,7,opt,name=symbol_type,json=symbolType,proto3" json:"symbol_type"`
	ReportLanguage int32   `protobuf:"varint,8,opt,name=report_language,json=reportLanguage,proto3" json:"report_language"`
	PayType        int32   `protobuf:"varint,9,opt,name=pay_type,json=payType,proto3" json:"pay_type"`
}

func (x *ReportOrderPushRequest) Reset() {
	*x = ReportOrderPushRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportOrderPushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportOrderPushRequest) ProtoMessage() {}

func (x *ReportOrderPushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportOrderPushRequest.ProtoReflect.Descriptor instead.
func (*ReportOrderPushRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{51}
}

func (x *ReportOrderPushRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ReportOrderPushRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ReportOrderPushRequest) GetTraderCode() string {
	if x != nil {
		return x.TraderCode
	}
	return ""
}

func (x *ReportOrderPushRequest) GetReportType() int32 {
	if x != nil {
		return x.ReportType
	}
	return 0
}

func (x *ReportOrderPushRequest) GetOrderTime() string {
	if x != nil {
		return x.OrderTime
	}
	return ""
}

func (x *ReportOrderPushRequest) GetPrice() float32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ReportOrderPushRequest) GetSymbolType() int32 {
	if x != nil {
		return x.SymbolType
	}
	return 0
}

func (x *ReportOrderPushRequest) GetReportLanguage() int32 {
	if x != nil {
		return x.ReportLanguage
	}
	return 0
}

func (x *ReportOrderPushRequest) GetPayType() int32 {
	if x != nil {
		return x.PayType
	}
	return 0
}

type ReportOrderPushReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReportOrderPushReply) Reset() {
	*x = ReportOrderPushReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportOrderPushReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportOrderPushReply) ProtoMessage() {}

func (x *ReportOrderPushReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportOrderPushReply.ProtoReflect.Descriptor instead.
func (*ReportOrderPushReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{52}
}

type GiftCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64          `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Name        string         `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Image       *Image         `protobuf:"bytes,3,opt,name=image,json=image,proto3" json:"image"`
	StartTime   int64          `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime     int64          `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	Status      GiftCardStatus `protobuf:"varint,6,opt,name=status,json=status,proto3,enum=api.gold_store.v1.GiftCardStatus" json:"status"`
	StatusStamp string         `protobuf:"bytes,7,opt,name=status_stamp,json=statusStamp,proto3" json:"status_stamp"`
	GoodsId     string         `protobuf:"bytes,8,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
}

func (x *GiftCard) Reset() {
	*x = GiftCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCard) ProtoMessage() {}

func (x *GiftCard) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCard.ProtoReflect.Descriptor instead.
func (*GiftCard) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{53}
}

func (x *GiftCard) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GiftCard) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GiftCard) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GiftCard) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GiftCard) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GiftCard) GetStatus() GiftCardStatus {
	if x != nil {
		return x.Status
	}
	return GiftCardStatus_GiftCardStatusUnknown
}

func (x *GiftCard) GetStatusStamp() string {
	if x != nil {
		return x.StatusStamp
	}
	return ""
}

func (x *GiftCard) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

type GetUserGiftCardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status UserGiftCardTabType `protobuf:"varint,1,opt,name=status,json=status,proto3,enum=api.gold_store.v1.UserGiftCardTabType" json:"status"`
	Size   int32               `protobuf:"varint,2,opt,name=size,json=size,proto3" json:"size"`
	Page   int32               `protobuf:"varint,3,opt,name=page,json=page,proto3" json:"page"`
}

func (x *GetUserGiftCardRequest) Reset() {
	*x = GetUserGiftCardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGiftCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGiftCardRequest) ProtoMessage() {}

func (x *GetUserGiftCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGiftCardRequest.ProtoReflect.Descriptor instead.
func (*GetUserGiftCardRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{54}
}

func (x *GetUserGiftCardRequest) GetStatus() UserGiftCardTabType {
	if x != nil {
		return x.Status
	}
	return UserGiftCardTabType_UserGiftCardTabTypeALL
}

func (x *GetUserGiftCardRequest) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetUserGiftCardRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

type GetUserGiftCardReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tabs      []*GiftCardTab `protobuf:"bytes,1,rep,name=tabs,json=tabs,proto3" json:"tabs"`
	GiftCards []*GiftCard    `protobuf:"bytes,2,rep,name=gift_cards,json=giftCards,proto3" json:"gift_cards"`
}

func (x *GetUserGiftCardReply) Reset() {
	*x = GetUserGiftCardReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGiftCardReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGiftCardReply) ProtoMessage() {}

func (x *GetUserGiftCardReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGiftCardReply.ProtoReflect.Descriptor instead.
func (*GetUserGiftCardReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{55}
}

func (x *GetUserGiftCardReply) GetTabs() []*GiftCardTab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *GetUserGiftCardReply) GetGiftCards() []*GiftCard {
	if x != nil {
		return x.GiftCards
	}
	return nil
}

type GiftCardTab struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string              `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Value UserGiftCardTabType `protobuf:"varint,2,opt,name=value,json=value,proto3,enum=api.gold_store.v1.UserGiftCardTabType" json:"value"`
	Count int32               `protobuf:"varint,3,opt,name=count,json=count,proto3" json:"count"`
}

func (x *GiftCardTab) Reset() {
	*x = GiftCardTab{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardTab) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardTab) ProtoMessage() {}

func (x *GiftCardTab) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardTab.ProtoReflect.Descriptor instead.
func (*GiftCardTab) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{56}
}

func (x *GiftCardTab) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GiftCardTab) GetValue() UserGiftCardTabType {
	if x != nil {
		return x.Value
	}
	return UserGiftCardTabType_UserGiftCardTabTypeALL
}

func (x *GiftCardTab) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GiftCardRemindRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GiftCardRemindRequest) Reset() {
	*x = GiftCardRemindRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardRemindRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardRemindRequest) ProtoMessage() {}

func (x *GiftCardRemindRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardRemindRequest.ProtoReflect.Descriptor instead.
func (*GiftCardRemindRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{57}
}

type GiftCardRemindReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int64  `protobuf:"varint,1,opt,name=id,json=id,proto3" json:"id"`
	Name       string `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Image      *Image `protobuf:"bytes,3,opt,name=image,json=image,proto3" json:"image"`
	StartTime  int64  `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	EndTime    int64  `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	GoodsId    string `protobuf:"bytes,6,opt,name=goods_id,json=goodsId,proto3" json:"goods_id"`
	IsHaveGift bool   `protobuf:"varint,7,opt,name=is_have_gift,json=isHaveGift,proto3" json:"is_have_gift"`
}

func (x *GiftCardRemindReply) Reset() {
	*x = GiftCardRemindReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GiftCardRemindReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GiftCardRemindReply) ProtoMessage() {}

func (x *GiftCardRemindReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GiftCardRemindReply.ProtoReflect.Descriptor instead.
func (*GiftCardRemindReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{58}
}

func (x *GiftCardRemindReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GiftCardRemindReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GiftCardRemindReply) GetImage() *Image {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *GiftCardRemindReply) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GiftCardRemindReply) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *GiftCardRemindReply) GetGoodsId() string {
	if x != nil {
		return x.GoodsId
	}
	return ""
}

func (x *GiftCardRemindReply) GetIsHaveGift() bool {
	if x != nil {
		return x.IsHaveGift
	}
	return false
}

type QueryExpressInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingNo string `protobuf:"bytes,1,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	Phone      string `protobuf:"bytes,2,opt,name=phone,json=phone,proto3" json:"phone"`
}

func (x *QueryExpressInfoRequest) Reset() {
	*x = QueryExpressInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryExpressInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryExpressInfoRequest) ProtoMessage() {}

func (x *QueryExpressInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryExpressInfoRequest.ProtoReflect.Descriptor instead.
func (*QueryExpressInfoRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{59}
}

func (x *QueryExpressInfoRequest) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *QueryExpressInfoRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type QueryExpressInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackingNo  string             `protobuf:"bytes,1,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	CarrierCode string             `protobuf:"bytes,2,opt,name=carrier_code,json=carrierCode,proto3" json:"carrier_code"`
	CarrierName string             `protobuf:"bytes,3,opt,name=carrier_name,json=carrierName,proto3" json:"carrier_name"`
	Status      LogisticStepStatus `protobuf:"varint,4,opt,name=status,json=status,proto3,enum=api.gold_store.v1.LogisticStepStatus" json:"status"`
	IsCompleted bool               `protobuf:"varint,5,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed"`
	LastUpdate  string             `protobuf:"bytes,6,opt,name=last_update,json=lastUpdate,proto3" json:"last_update"`
	Traces      []*ExpressTrace    `protobuf:"bytes,7,rep,name=traces,json=traces,proto3" json:"traces"`
}

func (x *QueryExpressInfoReply) Reset() {
	*x = QueryExpressInfoReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryExpressInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryExpressInfoReply) ProtoMessage() {}

func (x *QueryExpressInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryExpressInfoReply.ProtoReflect.Descriptor instead.
func (*QueryExpressInfoReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{60}
}

func (x *QueryExpressInfoReply) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *QueryExpressInfoReply) GetCarrierCode() string {
	if x != nil {
		return x.CarrierCode
	}
	return ""
}

func (x *QueryExpressInfoReply) GetCarrierName() string {
	if x != nil {
		return x.CarrierName
	}
	return ""
}

func (x *QueryExpressInfoReply) GetStatus() LogisticStepStatus {
	if x != nil {
		return x.Status
	}
	return LogisticStepStatus_LogisticStepStatusORDERED
}

func (x *QueryExpressInfoReply) GetIsCompleted() bool {
	if x != nil {
		return x.IsCompleted
	}
	return false
}

func (x *QueryExpressInfoReply) GetLastUpdate() string {
	if x != nil {
		return x.LastUpdate
	}
	return ""
}

func (x *QueryExpressInfoReply) GetTraces() []*ExpressTrace {
	if x != nil {
		return x.Traces
	}
	return nil
}

type ExpressTrace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time     string             `protobuf:"bytes,1,opt,name=time,json=time,proto3" json:"time"`
	Context  string             `protobuf:"bytes,2,opt,name=context,json=context,proto3" json:"context"`
	Location string             `protobuf:"bytes,3,opt,name=location,json=location,proto3" json:"location"`
	Status   LogisticStepStatus `protobuf:"varint,4,opt,name=status,json=status,proto3,enum=api.gold_store.v1.LogisticStepStatus" json:"status"`
}

func (x *ExpressTrace) Reset() {
	*x = ExpressTrace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpressTrace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpressTrace) ProtoMessage() {}

func (x *ExpressTrace) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpressTrace.ProtoReflect.Descriptor instead.
func (*ExpressTrace) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{61}
}

func (x *ExpressTrace) GetTime() string {
	if x != nil {
		return x.Time
	}
	return ""
}

func (x *ExpressTrace) GetContext() string {
	if x != nil {
		return x.Context
	}
	return ""
}

func (x *ExpressTrace) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ExpressTrace) GetStatus() LogisticStepStatus {
	if x != nil {
		return x.Status
	}
	return LogisticStepStatus_LogisticStepStatusORDERED
}

type ExpressPushCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sign  string `protobuf:"bytes,1,opt,name=sign,json=sign,proto3" json:"sign"`
	Param string `protobuf:"bytes,2,opt,name=param,json=param,proto3" json:"param"`
}

func (x *ExpressPushCallbackRequest) Reset() {
	*x = ExpressPushCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpressPushCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpressPushCallbackRequest) ProtoMessage() {}

func (x *ExpressPushCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpressPushCallbackRequest.ProtoReflect.Descriptor instead.
func (*ExpressPushCallbackRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{62}
}

func (x *ExpressPushCallbackRequest) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *ExpressPushCallbackRequest) GetParam() string {
	if x != nil {
		return x.Param
	}
	return ""
}

type ExpressPushCallbackReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     bool   `protobuf:"varint,1,opt,name=result,json=result,proto3" json:"result"`
	ReturnCode string `protobuf:"bytes,2,opt,name=return_code,json=returnCode,proto3" json:"return_code"`
	Message    string `protobuf:"bytes,3,opt,name=message,json=message,proto3" json:"message"`
}

func (x *ExpressPushCallbackReply) Reset() {
	*x = ExpressPushCallbackReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpressPushCallbackReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpressPushCallbackReply) ProtoMessage() {}

func (x *ExpressPushCallbackReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpressPushCallbackReply.ProtoReflect.Descriptor instead.
func (*ExpressPushCallbackReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{63}
}

func (x *ExpressPushCallbackReply) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *ExpressPushCallbackReply) GetReturnCode() string {
	if x != nil {
		return x.ReturnCode
	}
	return ""
}

func (x *ExpressPushCallbackReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type QueryUserTaskFinishedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId       string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	TaskEnumCode string `protobuf:"bytes,2,opt,name=task_enum_code,json=taskEnumCode,proto3" json:"task_enum_code"`
}

func (x *QueryUserTaskFinishedRequest) Reset() {
	*x = QueryUserTaskFinishedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryUserTaskFinishedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUserTaskFinishedRequest) ProtoMessage() {}

func (x *QueryUserTaskFinishedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUserTaskFinishedRequest.ProtoReflect.Descriptor instead.
func (*QueryUserTaskFinishedRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{64}
}

func (x *QueryUserTaskFinishedRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *QueryUserTaskFinishedRequest) GetTaskEnumCode() string {
	if x != nil {
		return x.TaskEnumCode
	}
	return ""
}

type QueryUserTaskFinishedReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsFinished bool `protobuf:"varint,1,opt,name=is_finished,json=isFinished,proto3" json:"is_finished"`
}

func (x *QueryUserTaskFinishedReply) Reset() {
	*x = QueryUserTaskFinishedReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryUserTaskFinishedReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUserTaskFinishedReply) ProtoMessage() {}

func (x *QueryUserTaskFinishedReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUserTaskFinishedReply.ProtoReflect.Descriptor instead.
func (*QueryUserTaskFinishedReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{65}
}

func (x *QueryUserTaskFinishedReply) GetIsFinished() bool {
	if x != nil {
		return x.IsFinished
	}
	return false
}

// 查询物流轨迹请求
type QueryTrackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language    string   `protobuf:"bytes,1,opt,name=language,json=language,proto3" json:"language"`
	WaybillNos  []string `protobuf:"bytes,2,rep,name=waybill_nos,json=waybillNos,proto3" json:"waybill_nos"`
	TrackingNos []string `protobuf:"bytes,3,rep,name=tracking_nos,json=trackingNos,proto3" json:"tracking_nos"`
}

func (x *QueryTrackRequest) Reset() {
	*x = QueryTrackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTrackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTrackRequest) ProtoMessage() {}

func (x *QueryTrackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTrackRequest.ProtoReflect.Descriptor instead.
func (*QueryTrackRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{66}
}

func (x *QueryTrackRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *QueryTrackRequest) GetWaybillNos() []string {
	if x != nil {
		return x.WaybillNos
	}
	return nil
}

func (x *QueryTrackRequest) GetTrackingNos() []string {
	if x != nil {
		return x.TrackingNos
	}
	return nil
}

// 查询物流轨迹响应
type QueryTrackReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool         `protobuf:"varint,1,opt,name=success,json=success,proto3" json:"success"`
	Message string       `protobuf:"bytes,2,opt,name=message,json=message,proto3" json:"message"`
	Tracks  []*TrackInfo `protobuf:"bytes,3,rep,name=tracks,json=tracks,proto3" json:"tracks"`
}

func (x *QueryTrackReply) Reset() {
	*x = QueryTrackReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryTrackReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTrackReply) ProtoMessage() {}

func (x *QueryTrackReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTrackReply.ProtoReflect.Descriptor instead.
func (*QueryTrackReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{67}
}

func (x *QueryTrackReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QueryTrackReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *QueryTrackReply) GetTracks() []*TrackInfo {
	if x != nil {
		return x.Tracks
	}
	return nil
}

// 轨迹信息
type TrackInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WaybillNo  string         `protobuf:"bytes,1,opt,name=waybill_no,json=waybillNo,proto3" json:"waybill_no"`
	TrackingNo string         `protobuf:"bytes,2,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	Status     string         `protobuf:"bytes,3,opt,name=status,json=status,proto3" json:"status"`
	StatusDesc string         `protobuf:"bytes,4,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc"`
	Tracks     []*TrackDetail `protobuf:"bytes,5,rep,name=tracks,json=tracks,proto3" json:"tracks"`
}

func (x *TrackInfo) Reset() {
	*x = TrackInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackInfo) ProtoMessage() {}

func (x *TrackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackInfo.ProtoReflect.Descriptor instead.
func (*TrackInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{68}
}

func (x *TrackInfo) GetWaybillNo() string {
	if x != nil {
		return x.WaybillNo
	}
	return ""
}

func (x *TrackInfo) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *TrackInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TrackInfo) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

func (x *TrackInfo) GetTracks() []*TrackDetail {
	if x != nil {
		return x.Tracks
	}
	return nil
}

// 轨迹详情
type TrackDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TrackTime   string `protobuf:"bytes,1,opt,name=track_time,json=trackTime,proto3" json:"track_time"`
	TrackStatus string `protobuf:"bytes,2,opt,name=track_status,json=trackStatus,proto3" json:"track_status"`
	TrackDesc   string `protobuf:"bytes,3,opt,name=track_desc,json=trackDesc,proto3" json:"track_desc"`
	Location    string `protobuf:"bytes,4,opt,name=location,json=location,proto3" json:"location"`
	Operator    string `protobuf:"bytes,5,opt,name=operator,json=operator,proto3" json:"operator"`
	OpOrgCode   string `protobuf:"bytes,6,opt,name=op_org_code,json=opOrgCode,proto3" json:"op_org_code"`
	OpOrgName   string `protobuf:"bytes,7,opt,name=op_org_name,json=opOrgName,proto3" json:"op_org_name"`
	OpCode      string `protobuf:"bytes,8,opt,name=op_code,json=opCode,proto3" json:"op_code"`
	OpName      string `protobuf:"bytes,9,opt,name=op_name,json=opName,proto3" json:"op_name"`
}

func (x *TrackDetail) Reset() {
	*x = TrackDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TrackDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackDetail) ProtoMessage() {}

func (x *TrackDetail) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackDetail.ProtoReflect.Descriptor instead.
func (*TrackDetail) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{69}
}

func (x *TrackDetail) GetTrackTime() string {
	if x != nil {
		return x.TrackTime
	}
	return ""
}

func (x *TrackDetail) GetTrackStatus() string {
	if x != nil {
		return x.TrackStatus
	}
	return ""
}

func (x *TrackDetail) GetTrackDesc() string {
	if x != nil {
		return x.TrackDesc
	}
	return ""
}

func (x *TrackDetail) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *TrackDetail) GetOperator() string {
	if x != nil {
		return x.Operator
	}
	return ""
}

func (x *TrackDetail) GetOpOrgCode() string {
	if x != nil {
		return x.OpOrgCode
	}
	return ""
}

func (x *TrackDetail) GetOpOrgName() string {
	if x != nil {
		return x.OpOrgName
	}
	return ""
}

func (x *TrackDetail) GetOpCode() string {
	if x != nil {
		return x.OpCode
	}
	return ""
}

func (x *TrackDetail) GetOpName() string {
	if x != nil {
		return x.OpName
	}
	return ""
}

// 查询运单请求
type QueryWaybillRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language    string   `protobuf:"bytes,1,opt,name=language,json=language,proto3" json:"language"`
	WaybillNos  []string `protobuf:"bytes,2,rep,name=waybill_nos,json=waybillNos,proto3" json:"waybill_nos"`
	TrackingNos []string `protobuf:"bytes,3,rep,name=tracking_nos,json=trackingNos,proto3" json:"tracking_nos"`
}

func (x *QueryWaybillRequest) Reset() {
	*x = QueryWaybillRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryWaybillRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryWaybillRequest) ProtoMessage() {}

func (x *QueryWaybillRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryWaybillRequest.ProtoReflect.Descriptor instead.
func (*QueryWaybillRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{70}
}

func (x *QueryWaybillRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *QueryWaybillRequest) GetWaybillNos() []string {
	if x != nil {
		return x.WaybillNos
	}
	return nil
}

func (x *QueryWaybillRequest) GetTrackingNos() []string {
	if x != nil {
		return x.TrackingNos
	}
	return nil
}

// 查询运单响应
type QueryWaybillReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success  bool           `protobuf:"varint,1,opt,name=success,json=success,proto3" json:"success"`
	Message  string         `protobuf:"bytes,2,opt,name=message,json=message,proto3" json:"message"`
	Waybills []*WaybillInfo `protobuf:"bytes,3,rep,name=waybills,json=waybills,proto3" json:"waybills"`
}

func (x *QueryWaybillReply) Reset() {
	*x = QueryWaybillReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryWaybillReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryWaybillReply) ProtoMessage() {}

func (x *QueryWaybillReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryWaybillReply.ProtoReflect.Descriptor instead.
func (*QueryWaybillReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{71}
}

func (x *QueryWaybillReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QueryWaybillReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *QueryWaybillReply) GetWaybills() []*WaybillInfo {
	if x != nil {
		return x.Waybills
	}
	return nil
}

// 运单信息
type WaybillInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WaybillNo  string `protobuf:"bytes,1,opt,name=waybill_no,json=waybillNo,proto3" json:"waybill_no"`
	TrackingNo string `protobuf:"bytes,2,opt,name=tracking_no,json=trackingNo,proto3" json:"tracking_no"`
	Status     string `protobuf:"bytes,3,opt,name=status,json=status,proto3" json:"status"`
	StatusDesc string `protobuf:"bytes,4,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc"`
	UpdateTime string `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
}

func (x *WaybillInfo) Reset() {
	*x = WaybillInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WaybillInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaybillInfo) ProtoMessage() {}

func (x *WaybillInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaybillInfo.ProtoReflect.Descriptor instead.
func (*WaybillInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{72}
}

func (x *WaybillInfo) GetWaybillNo() string {
	if x != nil {
		return x.WaybillNo
	}
	return ""
}

func (x *WaybillInfo) GetTrackingNo() string {
	if x != nil {
		return x.TrackingNo
	}
	return ""
}

func (x *WaybillInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *WaybillInfo) GetStatusDesc() string {
	if x != nil {
		return x.StatusDesc
	}
	return ""
}

func (x *WaybillInfo) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

// 查询费用请求
type QueryFeeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language        string       `protobuf:"bytes,1,opt,name=language,json=language,proto3" json:"language"`
	ServiceType     string       `protobuf:"bytes,2,opt,name=service_type,json=serviceType,proto3" json:"service_type"`
	SenderCountry   string       `protobuf:"bytes,3,opt,name=sender_country,json=senderCountry,proto3" json:"sender_country"`
	ReceiverCountry string       `protobuf:"bytes,4,opt,name=receiver_country,json=receiverCountry,proto3" json:"receiver_country"`
	Weight          int32        `protobuf:"varint,5,opt,name=weight,json=weight,proto3" json:"weight"`
	Cargo           []*CargoInfo `protobuf:"bytes,6,rep,name=cargo,json=cargo,proto3" json:"cargo"`
}

func (x *QueryFeeRequest) Reset() {
	*x = QueryFeeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryFeeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryFeeRequest) ProtoMessage() {}

func (x *QueryFeeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryFeeRequest.ProtoReflect.Descriptor instead.
func (*QueryFeeRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{73}
}

func (x *QueryFeeRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *QueryFeeRequest) GetServiceType() string {
	if x != nil {
		return x.ServiceType
	}
	return ""
}

func (x *QueryFeeRequest) GetSenderCountry() string {
	if x != nil {
		return x.SenderCountry
	}
	return ""
}

func (x *QueryFeeRequest) GetReceiverCountry() string {
	if x != nil {
		return x.ReceiverCountry
	}
	return ""
}

func (x *QueryFeeRequest) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *QueryFeeRequest) GetCargo() []*CargoInfo {
	if x != nil {
		return x.Cargo
	}
	return nil
}

// 货物信息
type CargoInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Quantity    int32  `protobuf:"varint,2,opt,name=quantity,json=quantity,proto3" json:"quantity"`
	Weight      int32  `protobuf:"varint,3,opt,name=weight,json=weight,proto3" json:"weight"`
	Value       int32  `protobuf:"varint,4,opt,name=value,json=value,proto3" json:"value"`
	Currency    string `protobuf:"bytes,5,opt,name=currency,json=currency,proto3" json:"currency"`
	Origin      string `protobuf:"bytes,6,opt,name=origin,json=origin,proto3" json:"origin"`
	Description string `protobuf:"bytes,7,opt,name=description,json=description,proto3" json:"description"`
}

func (x *CargoInfo) Reset() {
	*x = CargoInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CargoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CargoInfo) ProtoMessage() {}

func (x *CargoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CargoInfo.ProtoReflect.Descriptor instead.
func (*CargoInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{74}
}

func (x *CargoInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CargoInfo) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CargoInfo) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *CargoInfo) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *CargoInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CargoInfo) GetOrigin() string {
	if x != nil {
		return x.Origin
	}
	return ""
}

func (x *CargoInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 查询费用响应
type QueryFeeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool     `protobuf:"varint,1,opt,name=success,json=success,proto3" json:"success"`
	Message string   `protobuf:"bytes,2,opt,name=message,json=message,proto3" json:"message"`
	Fee     *FeeInfo `protobuf:"bytes,3,opt,name=fee,json=fee,proto3" json:"fee"`
}

func (x *QueryFeeReply) Reset() {
	*x = QueryFeeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryFeeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryFeeReply) ProtoMessage() {}

func (x *QueryFeeReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryFeeReply.ProtoReflect.Descriptor instead.
func (*QueryFeeReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{75}
}

func (x *QueryFeeReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *QueryFeeReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *QueryFeeReply) GetFee() *FeeInfo {
	if x != nil {
		return x.Fee
	}
	return nil
}

// 费用信息
type FeeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceType   string `protobuf:"bytes,1,opt,name=service_type,json=serviceType,proto3" json:"service_type"`
	ServiceDesc   string `protobuf:"bytes,2,opt,name=service_desc,json=serviceDesc,proto3" json:"service_desc"`
	TotalFee      int32  `protobuf:"varint,3,opt,name=total_fee,json=totalFee,proto3" json:"total_fee"`
	Currency      string `protobuf:"bytes,4,opt,name=currency,json=currency,proto3" json:"currency"`
	EstimatedDays int32  `protobuf:"varint,5,opt,name=estimated_days,json=estimatedDays,proto3" json:"estimated_days"`
}

func (x *FeeInfo) Reset() {
	*x = FeeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeeInfo) ProtoMessage() {}

func (x *FeeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeeInfo.ProtoReflect.Descriptor instead.
func (*FeeInfo) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{76}
}

func (x *FeeInfo) GetServiceType() string {
	if x != nil {
		return x.ServiceType
	}
	return ""
}

func (x *FeeInfo) GetServiceDesc() string {
	if x != nil {
		return x.ServiceDesc
	}
	return ""
}

func (x *FeeInfo) GetTotalFee() int32 {
	if x != nil {
		return x.TotalFee
	}
	return 0
}

func (x *FeeInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *FeeInfo) GetEstimatedDays() int32 {
	if x != nil {
		return x.EstimatedDays
	}
	return 0
}

// 推送通知请求
type PushNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Method           string `protobuf:"bytes,1,opt,name=method,json=method,proto3" json:"method"`
	SenderNo         string `protobuf:"bytes,2,opt,name=sender_no,json=senderNo,proto3" json:"sender_no"`
	MsgType          string `protobuf:"bytes,3,opt,name=msg_type,json=msgType,proto3" json:"msg_type"`
	TimeStamp        string `protobuf:"bytes,4,opt,name=time_stamp,json=timeStamp,proto3" json:"time_stamp"`
	Version          string `protobuf:"bytes,5,opt,name=version,json=version,proto3" json:"version"`
	LogitcsInterface string `protobuf:"bytes,6,opt,name=logitcs_interface,json=logitcsInterface,proto3" json:"logitcs_interface"`
	SerialNo         string `protobuf:"bytes,7,opt,name=serial_no,json=serialNo,proto3" json:"serial_no"`
}

func (x *PushNotificationRequest) Reset() {
	*x = PushNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushNotificationRequest) ProtoMessage() {}

func (x *PushNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushNotificationRequest.ProtoReflect.Descriptor instead.
func (*PushNotificationRequest) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{77}
}

func (x *PushNotificationRequest) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *PushNotificationRequest) GetSenderNo() string {
	if x != nil {
		return x.SenderNo
	}
	return ""
}

func (x *PushNotificationRequest) GetMsgType() string {
	if x != nil {
		return x.MsgType
	}
	return ""
}

func (x *PushNotificationRequest) GetTimeStamp() string {
	if x != nil {
		return x.TimeStamp
	}
	return ""
}

func (x *PushNotificationRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PushNotificationRequest) GetLogitcsInterface() string {
	if x != nil {
		return x.LogitcsInterface
	}
	return ""
}

func (x *PushNotificationRequest) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

// 推送通知响应
type PushNotificationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SerialNo    string `protobuf:"bytes,1,opt,name=serial_no,json=serialNo,proto3" json:"serial_no"`
	Code        string `protobuf:"bytes,2,opt,name=code,json=code,proto3" json:"code"`
	CodeMessage string `protobuf:"bytes,3,opt,name=code_message,json=codeMessage,proto3" json:"code_message"`
	SenderNo    string `protobuf:"bytes,4,opt,name=sender_no,json=senderNo,proto3" json:"sender_no"`
}

func (x *PushNotificationReply) Reset() {
	*x = PushNotificationReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_gold_store_v1_service_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushNotificationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushNotificationReply) ProtoMessage() {}

func (x *PushNotificationReply) ProtoReflect() protoreflect.Message {
	mi := &file_gold_store_v1_service_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushNotificationReply.ProtoReflect.Descriptor instead.
func (*PushNotificationReply) Descriptor() ([]byte, []int) {
	return file_gold_store_v1_service_proto_rawDescGZIP(), []int{78}
}

func (x *PushNotificationReply) GetSerialNo() string {
	if x != nil {
		return x.SerialNo
	}
	return ""
}

func (x *PushNotificationReply) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *PushNotificationReply) GetCodeMessage() string {
	if x != nil {
		return x.CodeMessage
	}
	return ""
}

func (x *PushNotificationReply) GetSenderNo() string {
	if x != nil {
		return x.SenderNo
	}
	return ""
}

var File_gold_store_v1_service_proto protoreflect.FileDescriptor

var file_gold_store_v1_service_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f,
	0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x69, 0x67, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x85, 0x02, 0x0a, 0x0f, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xbf, 0xab, 0xe6,
	0x8d, 0xb7, 0xe5, 0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xbf, 0xab, 0xe6, 0x8d, 0xb7, 0xe5,
	0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x12, 0x97, 0x01, 0x0a, 0x09, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x69, 0x63, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x53, 0x92, 0x41,
	0x50, 0x2a, 0x4e, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a,
	0x30, 0x3a, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xef, 0xbc, 0x9b, 0x31, 0xef,
	0xbc, 0x9a, 0xe8, 0xb5, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xef, 0xbc, 0x9b, 0x32, 0xef,
	0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xe5, 0x85, 0x85, 0xe5, 0x80, 0xbc, 0xef, 0xbc,
	0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe7, 0xae, 0xa1, 0xe7, 0x90,
	0x86, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x22, 0x55, 0x0a, 0x19, 0x47,
	0x6f, 0x6c, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x69, 0x63,
	0x6b, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0x3c, 0x0a, 0x11, 0x4d, 0x79, 0x47, 0x6f, 0x6c, 0x64, 0x4a, 0x75, 0x6d, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe6, 0x97, 0xb6, 0xe5, 0x8c, 0xba, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x7a, 0x6f, 0x6e, 0x65,
	0x22, 0x9d, 0x01, 0x0a, 0x0f, 0x4d, 0x79, 0x47, 0x6f, 0x6c, 0x64, 0x4a, 0x75, 0x6d, 0x70, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x35, 0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x6e, 0x65, 0x77,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x98, 0xaf,
	0xe5, 0x90, 0xa6, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe6, 0x96, 0xb0, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x52, 0x07, 0x6a, 0x75, 0x6d, 0x70, 0x4e, 0x65, 0x77, 0x12, 0x28, 0x0a, 0x04, 0x73,
	0x69, 0x67, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe8, 0xbf, 0x87, 0x52,
	0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x29, 0x0a, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe7, 0xa7, 0xaf, 0xe5, 0x88, 0x86, 0x52, 0x06, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x22, 0xfd, 0x01, 0x0a, 0x0a, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12,
	0x5d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x26, 0x92, 0x41, 0x23, 0x2a, 0x21, 0x74, 0x61, 0x67, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xef, 0xbc, 0x9b, 0x31, 0xef,
	0xbc, 0x9a, 0xe6, 0x96, 0x87, 0xe6, 0x9c, 0xac, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x27,
	0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x96, 0x87, 0xe6,
	0x9c, 0xac, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x96,
	0x87, 0xe6, 0x9c, 0xac, 0xe9, 0xa2, 0x9c, 0xe8, 0x89, 0xb2, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64,
	0x22, 0xa3, 0x04, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x27, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0,
	0xbc, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52,
	0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x48, 0x0a, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x06, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x5f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x27, 0x92, 0x41, 0x24, 0x2a, 0x22, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0x3a, 0xe4, 0xb8, 0x8b, 0xe6, 0x9e, 0xb6,
	0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe6, 0x9e, 0xb6, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4f, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x52, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xe8, 0x0a, 0x0a, 0x0b, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64,
	0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x2a,
	0x0a, 0x05, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe9, 0x94, 0x80, 0xef, 0xbf, 0xbd,
	0xef, 0xbf, 0xbd, 0x52, 0x05, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x66, 0x72,
	0x65, 0x65, 0x5f, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x85,
	0x8d, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0c, 0x66, 0x72, 0x65, 0x65, 0x53, 0x68, 0x69,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x3f, 0x0a, 0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb7, 0xb2, 0xe9, 0x80, 0x89, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0x73, 0x6b, 0x75, 0x20, 0x69, 0x64, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x53, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x12, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1d, 0x92, 0x41, 0x1a, 0x2a, 0x18, 0xe5, 0xb7, 0xb2, 0xe9, 0x80, 0x89, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf,
	0xb0, 0x52, 0x10, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x70, 0x65, 0x63, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x76, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x38, 0x92, 0x41, 0x35, 0x2a, 0x33, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc,
	0x9a, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xef, 0xbc, 0x9b,
	0x31, 0xef, 0xbc, 0x9a, 0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0x56, 0x50,
	0x53, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x65,
	0x0a, 0x07, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x4b, 0x92, 0x41, 0x48, 0x2a, 0x46, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe4, 0xb8, 0x8d, 0xe5, 0x8f, 0xaf, 0xe7, 0x94, 0xa8, 0xef, 0xbc, 0x8c, 0xe5, 0xa6,
	0x82, 0xe6, 0x9e, 0x9c, 0xe4, 0xb8, 0xba, 0x74, 0x72, 0x75, 0x65, 0xe8, 0xa1, 0xa8, 0xe7, 0xa4,
	0xba, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe6, 0x98, 0xaf, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81,
	0xe4, 0xb8, 0x8d, 0xe5, 0x8f, 0xaf, 0xe8, 0xb4, 0xad, 0xe4, 0xb9, 0xb0, 0x52, 0x07, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x61, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x29, 0x92, 0x41, 0x26, 0x2a, 0x24, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8b,
	0xe6, 0x9e, 0xb6, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0x8a, 0xe6, 0x9e, 0xb6,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0c, 0x62, 0x65, 0x73, 0x74,
	0x5f, 0x73, 0x65, 0x6c, 0x6c, 0x65, 0x72, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe7, 0x95, 0x85, 0xe9, 0x94, 0x80,
	0xe6, 0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x0b, 0x62, 0x65, 0x73, 0x74, 0x53, 0x65, 0x6c, 0x6c,
	0x65, 0x72, 0x73, 0x12, 0x48, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0f, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6,
	0xa0, 0x87, 0xe7, 0xad, 0xbe, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x4c, 0x0a,
	0x09, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a,
	0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad, 0xe5, 0x9b, 0xbe,
	0x52, 0x09, 0x63, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x73, 0x12, 0x48, 0x0a, 0x07, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0xe5, 0x9b, 0xbe, 0x52, 0x07, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x45, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x18, 0x12,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x53, 0x70,
	0x65, 0x63, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8,
	0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x12, 0x3f, 0x0a, 0x04,
	0x73, 0x6b, 0x75, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x6f, 0x6f, 0x64, 0x73, 0x53, 0x6b, 0x75, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0x73, 0x6b, 0x75, 0x52, 0x04, 0x73, 0x6b, 0x75, 0x73, 0x12, 0x42, 0x0a,
	0x09, 0x62, 0x75, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x20, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe9, 0x99, 0x90,
	0xe8, 0xb4, 0xad, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe4, 0xb8,
	0x8d, 0xe9, 0x99, 0x90, 0xe8, 0xb4, 0xad, 0x52, 0x08, 0x62, 0x75, 0x79, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0x11, 0x0a, 0x0f, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x79, 0x0a, 0x08, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62,
	0x12, 0x1a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0x92, 0x41,
	0x07, 0x2a, 0x05, 0x74, 0x61, 0x62, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0x74, 0x61, 0x62, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2d, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe9,
	0x80, 0x89, 0xe4, 0xb8, 0xad, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22,
	0x9b, 0x01, 0x0a, 0x0d, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x3f, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x74, 0x61, 0x62, 0x52, 0x04, 0x74, 0x61,
	0x62, 0x73, 0x12, 0x49, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x22, 0x83, 0x01,
	0x0a, 0x10, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x06, 0x74, 0x61, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05, 0x74, 0x61, 0x62, 0x49, 0x44, 0x52, 0x05,
	0x74, 0x61, 0x62, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5,
	0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x22, 0x5b, 0x0a, 0x0e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x22, 0x3e, 0x0a, 0x12, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64,
	0x22, 0x12, 0x0a, 0x10, 0x42, 0x65, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x5b, 0x0a, 0x0e, 0x42, 0x65, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x49, 0x0a, 0x05, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x22, 0x59, 0x0a, 0x0b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x23, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0f,
	0x92, 0x41, 0x0c, 0x2a, 0x0a, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x47, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0xe5,
	0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x5a, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x0f, 0x41, 0x64, 0x64,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x52, 0x0a, 0x0c, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2f, 0x92, 0x41, 0x2c, 0x2a, 0x2a, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0xe5, 0x90,
	0x8d, 0xe7, 0xa7, 0xb0, 0xef, 0xbc, 0x8c, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe7, 0x9a, 0x84,
	0xe6, 0x97, 0xb6, 0xe5, 0x80, 0x99, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe4, 0xbd, 0xbf, 0xe7,
	0x94, 0xa8, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x22,
	0x39, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x9c,
	0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x35, 0x0a, 0x14, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x38, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x22, 0x3f, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe6,
	0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xcb, 0x04, 0x0a,
	0x17, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0xbd, 0x01, 0x0a, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x82, 0x01, 0x92, 0x41,
	0x7f, 0x2a, 0x7d, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a,
	0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc,
	0x9a, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc,
	0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x9b,
	0x33, 0xef, 0xbc, 0x9a, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe5, 0xae, 0x9d, 0xef, 0xbc, 0x9b,
	0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0xae, 0xe4, 0xbf, 0xa1, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc,
	0x9a, 0xe8, 0x8b, 0xb9, 0xe6, 0x9e, 0x9c, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0xef, 0xbc, 0x9b,
	0x36, 0xef, 0xbc, 0x9a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad,
	0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x66, 0x0a, 0x10, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x3b, 0x92, 0x41, 0x38, 0x2a, 0x36, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6,
	0x98, 0xaf, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xe5, 0x92, 0x8c, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x8c, 0xe4, 0xbc, 0xa0,
	0xe9, 0x80, 0x92, 0xe7, 0xa5, 0xa8, 0xe6, 0x8d, 0xae, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52,
	0x0f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49,
	0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x0a, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3d,
	0x92, 0x41, 0x3a, 0x2a, 0x38, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0x98, 0xaf, 0xe5, 0xae,
	0x9e, 0xe7, 0x89, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81,
	0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x80, 0x89, 0xe4,
	0xb8, 0xad, 0xe7, 0x9a, 0x84, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0x52, 0x09, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x71,
	0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x51, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65, 0x63, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe9, 0x80,
	0x89, 0xe4, 0xb8, 0xad, 0xe7, 0x9a, 0x84, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84,
	0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x22, 0xea, 0x01, 0x0a, 0x15, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x80, 0xbb, 0xe4, 0xbb, 0xb7, 0x52, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x0c, 0x73, 0x68,
	0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0b, 0x73,
	0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94,
	0xb6, 0xe8, 0xb4, 0xa7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe9, 0x94, 0x99, 0xe8, 0xaf, 0xaf, 0xe7, 0xa0,
	0x81, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x63, 0x0a, 0x0c, 0x53, 0x70, 0x65, 0x63, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x07, 0x73, 0x70, 0x65, 0x63, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8,
	0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x49, 0x44, 0x52, 0x06, 0x73, 0x70, 0x65, 0x63, 0x49, 0x64, 0x12,
	0x2b, 0x0a, 0x08, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe5, 0x80,
	0xbc, 0x49, 0x44, 0x52, 0x07, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x64, 0x22, 0xff, 0x04, 0x0a,
	0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0xbd, 0x01, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42, 0x82, 0x01, 0x92, 0x41, 0x7f, 0x2a, 0x7d, 0xe6, 0x94,
	0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9,
	0x87, 0x91, 0xe5, 0xb8, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe7, 0xa4, 0xbc, 0xe5,
	0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe6,
	0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe5, 0xae, 0x9d, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5,
	0xbe, 0xae, 0xe4, 0xbf, 0xa1, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe8, 0x8b, 0xb9, 0xe6,
	0x9e, 0x9c, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0xef, 0xbc, 0x9b, 0x36, 0xef, 0xbc, 0x9a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0x52, 0x06, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x12, 0x66, 0x0a, 0x10, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3b, 0x92,
	0x41, 0x38, 0x2a, 0x36, 0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0x98, 0xaf, 0xe7, 0xa4, 0xbc,
	0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xe5, 0x92, 0x8c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4,
	0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x8c, 0xe4, 0xbc, 0xa0, 0xe9, 0x80, 0x92, 0xe7, 0xa5,
	0xa8, 0xe6, 0x8d, 0xae, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x28, 0x0a, 0x08, 0x67,
	0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f,
	0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x0a, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3d, 0x92, 0x41, 0x3a, 0x2a, 0x38,
	0xe5, 0xa6, 0x82, 0xe6, 0x9e, 0x9c, 0xe6, 0x98, 0xaf, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0xe9, 0x9c, 0x80, 0xe8, 0xa6, 0x81, 0xe4, 0xbc, 0xa0, 0xe9, 0x80,
	0x92, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe9, 0x80, 0x89, 0xe4, 0xb8, 0xad, 0xe7, 0x9a, 0x84,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x49, 0x44, 0x52, 0x09, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x37, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe9,
	0xa2, 0x84, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0x52, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x51, 0x0a, 0x05, 0x73,
	0x70, 0x65, 0x63, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x70, 0x65, 0x63, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe9, 0x80, 0x89, 0xe4, 0xb8, 0xad, 0xe7, 0x9a, 0x84, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x52, 0x05, 0x73, 0x70, 0x65, 0x63, 0x73, 0x22, 0x94,
	0x02, 0x0a, 0x0d, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x34, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xae, 0xa2,
	0xe5, 0x8d, 0x95, 0xe6, 0x80, 0xbb, 0xe4, 0xbb, 0xb7, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9, 0x52, 0x0b, 0x73, 0x68, 0x69, 0x70, 0x70,
	0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x54, 0x0a, 0x0c, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x52, 0x0b, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x3c, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x28, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x49, 0x44, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x4e, 0x6f, 0x22, 0x14, 0x0a, 0x12, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x52, 0x0a, 0x10, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0x92, 0x41, 0x04, 0x2a, 0x02,
	0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe7, 0xa4,
	0xba, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9f, 0x01,
	0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0x92, 0x41, 0x04, 0x2a, 0x02, 0x49, 0x44, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xbf,
	0x87, 0xe6, 0xbb, 0xa4, 0xe5, 0x80, 0xbc, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22,
	0x68, 0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x54, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8,
	0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x9d, 0xa5, 0xe6, 0xba, 0x90, 0xe5, 0x88, 0x86, 0xe7, 0xb1,
	0xbb, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x0f, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x36, 0x0a,
	0x08, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xe8,
	0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x36, 0x0a, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe5, 0xb7,
	0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x22, 0xc9, 0x0a,
	0x0a, 0x09, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x49, 0x44, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95,
	0x86, 0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x44, 0x52, 0x05, 0x73, 0x6b, 0x75,
	0x49, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x5b, 0x92, 0x41, 0x58, 0x2a, 0x56, 0xe8,
	0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x9d, 0xa5, 0xe6, 0xba, 0x90, 0xef, 0xbc, 0x9a, 0x30, 0xef,
	0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xe5, 0x95, 0x86, 0xe5, 0x9f, 0x8e, 0xef, 0xbc,
	0x9b, 0x31, 0xef, 0xbc, 0x9a, 0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0x56,
	0x50, 0x53, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xef,
	0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xef, 0xbc, 0x9b, 0x35,
	0xef, 0xbc, 0x9a, 0x45, 0x41, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x38, 0x0a,
	0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6,
	0x9d, 0xa5, 0xe6, 0xba, 0x90, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x0a, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x12, 0xcc, 0x01, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x42, 0x82, 0x01, 0x92, 0x41, 0x7f, 0x2a, 0x7d, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98,
	0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8,
	0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d,
	0xa1, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb,
	0xbb, 0xe5, 0x8a, 0xa1, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe6, 0x94, 0xaf, 0xe4, 0xbb,
	0x98, 0xe5, 0xae, 0x9d, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0xae, 0xe4, 0xbf,
	0xa1, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe8, 0x8b, 0xb9, 0xe6, 0x9e, 0x9c, 0xe5, 0x86,
	0x85, 0xe8, 0xb4, 0xad, 0xef, 0xbc, 0x9b, 0x36, 0xef, 0xbc, 0x9a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0xe5, 0x86, 0x85, 0xe8, 0xb4, 0xad, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x43, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe9, 0x87,
	0x91, 0xe9, 0xa2, 0x9d, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x48, 0x0a, 0x09, 0x73,
	0x70, 0x65, 0x63, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b,
	0x92, 0x41, 0x28, 0x2a, 0x26, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0,
	0xbc, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x28, 0xe5, 0x89, 0x8d, 0xe7, 0xab, 0xaf, 0xe5, 0xb1,
	0x95, 0xe7, 0xa4, 0xba, 0xe5, 0xb0, 0xb1, 0xe8, 0xa1, 0x8c, 0x29, 0x52, 0x08, 0x73, 0x70, 0x65,
	0x63, 0x44, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe5, 0x8d, 0x95, 0xe4, 0xbb, 0xb7, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x36,
	0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5,
	0x8d, 0x95, 0xe4, 0xbb, 0xb7, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x09, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x2d, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x08, 0x71, 0x75, 0x61,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x37, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x02, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x80, 0xbb, 0xe9, 0x87, 0x91, 0xe9, 0xa2,
	0x9d, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xaa,
	0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x72, 0x92, 0x41, 0x6f, 0x2a, 0x6d, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xaf,
	0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe6, 0x94, 0xaf,
	0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f, 0x96,
	0xe6, 0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xb6,
	0xe8, 0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c,
	0xe6, 0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x8a, 0xb6,
	0xe6, 0x80, 0x81, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x72, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f,
	0xe5, 0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0xc4, 0x01, 0x0a, 0x08, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x62, 0x12, 0x93, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x5b, 0x92, 0x41, 0x58, 0x2a, 0x56, 0x74, 0x61,
	0x62, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0x85,
	0xa8, 0xe9, 0x83, 0xa8, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94,
	0xaf, 0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94,
	0xb6, 0xe8, 0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f,
	0x96, 0xe6, 0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0xae,
	0x8c, 0xe6, 0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0x74, 0x61, 0x62, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x38, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x95, 0xb0, 0xe9, 0x87,
	0x8f, 0xe9, 0x87, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x50, 0x0a, 0x0d, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x62, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3f, 0x0a, 0x04, 0x74,
	0x61, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x62, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xae, 0xa2,
	0xe5, 0x8d, 0x95, 0x74, 0x61, 0x62, 0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x22, 0x62, 0x0a, 0x0b,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x08, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92,
	0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x49, 0x44, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05,
	0xe5, 0x80, 0xbc, 0x49, 0x44, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x49, 0x64, 0x73,
	0x22, 0xe2, 0x02, 0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x93, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x5b, 0x92, 0x41, 0x58, 0x2a, 0x56, 0x74, 0x61, 0x62,
	0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0x85, 0xa8,
	0xe9, 0x83, 0xa8, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xaf,
	0xe4, 0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xb6,
	0xe8, 0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f, 0x96,
	0xe6, 0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c,
	0xe6, 0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15,
	0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x2c, 0xe9, 0xbb, 0x98,
	0xe8, 0xae, 0xa4, 0x31, 0x30, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0x92, 0x41, 0x1b,
	0x2a, 0x19, 0xe5, 0x81, 0x8f, 0xe7, 0xa7, 0xbb, 0x3b, 0xe9, 0xbb, 0x98, 0xe8, 0xae, 0xa4, 0xe7,
	0xa9, 0xba, 0xe5, 0xad, 0x97, 0xe7, 0xac, 0xa6, 0xe4, 0xb8, 0xb2, 0x52, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x12, 0x50, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe8, 0xbf, 0x87, 0xe6, 0xbb, 0xa4, 0xe6, 0x9d, 0xa1, 0xe4, 0xbb, 0xb6, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x84, 0x01, 0x0a, 0x0e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x47, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xae, 0xa2,
	0xe5, 0x8d, 0x95, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x73, 0x12, 0x29, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0x86, 0xe9, 0xa1, 0xb5, 0xe5, 0x81,
	0x8f, 0xe7, 0xa7, 0xbb, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x3e, 0x0a, 0x12,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xa2, 0xe5, 0x8d,
	0x95, 0x49, 0x44, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x22, 0xe5, 0x02, 0x0a,
	0x08, 0x56, 0x50, 0x53, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x38, 0x0a, 0x0e, 0x65, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0x9f, 0xe6, 0x95, 0x88, 0xe6, 0x97,
	0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0d, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x88, 0xb0, 0xe6, 0x9c, 0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52,
	0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x0a, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe9, 0x80, 0x9a,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a,
	0x0f, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x35, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x9c, 0x8d, 0xe5,
	0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0xe5, 0x9f, 0x8e, 0xe5, 0xb8, 0x82, 0x52, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x43, 0x69, 0x74, 0x79, 0x12, 0x3b, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe5, 0x99, 0xa8, 0xe8,
	0xb4, 0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8f, 0x02, 0x0a, 0x0b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5,
	0x8f, 0x91, 0xe9, 0x80, 0x81, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52, 0x09, 0x73, 0x65, 0x6e,
	0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8,
	0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x30, 0x0a, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x8f, 0x91, 0xe9, 0x80, 0x81,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x38, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe7, 0x94, 0x9f, 0xe6, 0x88, 0x90, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0d, 0x67, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x67,
	0x6f, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0x8f, 0xaf, 0xe6,
	0x9f, 0xa5, 0xe7, 0x9c, 0x8b, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x52, 0x09, 0x67, 0x6f, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x0e, 0x45, 0x78, 0x68, 0x69, 0x62,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x09, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41,
	0x08, 0x2a, 0x06, 0xe5, 0xa7, 0x93, 0xe5, 0x90, 0x8d, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x31, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41,
	0x0b, 0x2a, 0x09, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xae, 0x02, 0x0a, 0x0f, 0x45, 0x78,
	0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x27, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0xb1, 0x95, 0xe4, 0xbc, 0x9a, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x0b, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0x97, 0xa8, 0xe7, 0xa5, 0xa8, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0x52, 0x0a, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3d, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe6, 0x98, 0xaf, 0xe5,
	0x90, 0xa6, 0xe4, 0xbd, 0xbf, 0xe7, 0x94, 0xa8, 0xe5, 0x85, 0x91, 0xe6, 0x8d, 0xa2, 0xe7, 0xa0,
	0x81, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x4a,
	0x0a, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbf, 0xa1,
	0xe6, 0x81, 0xaf, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x22, 0xbb, 0x12, 0x0a, 0x0b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x28, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41,
	0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x49, 0x44, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x4e, 0x6f, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0x49, 0x44, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x21,
	0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0x92, 0x41, 0x07, 0x2a, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x44, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49,
	0x64, 0x12, 0x30, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52,
	0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x76, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x38, 0x92, 0x41, 0x35, 0x2a,
	0x33, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0xef, 0xbc, 0x9a,
	0x30, 0xef, 0xbc, 0x9a, 0xe5, 0xae, 0x9e, 0xe7, 0x89, 0xa9, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81,
	0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc,
	0x9a, 0x56, 0x50, 0x53, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe9, 0x82, 0xae, 0xe7, 0xae, 0xb1, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x29, 0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0xe7, 0xac,
	0xa6, 0xe5, 0x8f, 0xb7, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x7b, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x43, 0x92, 0x41,
	0x40, 0x2a, 0x3e, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x9d, 0xa5, 0xe6, 0xba, 0x90, 0x3a,
	0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xe5, 0x95, 0x86, 0xe5, 0x9f, 0x8e,
	0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xef, 0xbc, 0x9b,
	0x32, 0xef, 0xbc, 0x9a, 0x56, 0x49, 0x50, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0x56, 0x50,
	0x53, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x9d, 0xa5, 0xe6, 0xba,
	0x90, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x68, 0x6f, 0x77, 0x12, 0x48, 0x0a, 0x09, 0x73, 0x70, 0x65, 0x63, 0x5f, 0x64, 0x65, 0x73, 0x63,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0x92, 0x41, 0x28, 0x2a, 0x26, 0xe5, 0x95, 0x86,
	0xe5, 0x93, 0x81, 0xe8, 0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x28,
	0xe5, 0x89, 0x8d, 0xe7, 0xab, 0xaf, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe5, 0xb0, 0xb1, 0xe8,
	0xa1, 0x8c, 0x29, 0x52, 0x08, 0x73, 0x70, 0x65, 0x63, 0x44, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x02, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0x95, 0xe4, 0xbb, 0xb7, 0x52,
	0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x73, 0x68, 0x6f, 0x77, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0x95, 0xe4, 0xbb, 0xb7, 0xe5, 0xb1, 0x95,
	0xe7, 0xa4, 0xba, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63, 0x65, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x2d,
	0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a,
	0x0c, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xbf, 0x90, 0xe8, 0xb4, 0xb9,
	0x52, 0x0b, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x12, 0x37, 0x0a,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95,
	0xe6, 0x80, 0xbb, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe9, 0x87,
	0x91, 0xe9, 0xa2, 0x9d, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x31, 0x0a, 0x09, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x02, 0x42, 0x14,
	0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x80, 0xbb, 0xe9, 0x87,
	0x91, 0xe9, 0xa2, 0x9d, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x12, 0x3d,
	0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x79, 0x5f, 0x73, 0x68, 0x6f, 0x77,
	0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x94, 0xaf,
	0xe4, 0xbb, 0x98, 0xe9, 0x87, 0x91, 0xe9, 0xa2, 0x9d, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0x52,
	0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x53, 0x68, 0x6f, 0x77, 0x12, 0xaa, 0x01,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x72,
	0x92, 0x41, 0x6f, 0x2a, 0x6d, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x8a, 0xb6, 0xe6, 0x80,
	0x81, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe6, 0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0xef, 0xbc, 0x9b, 0x31, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xaf, 0xe4,
	0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe6, 0x94, 0xaf, 0xe4,
	0xbb, 0x98, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0x8f, 0x96, 0xe6,
	0xb6, 0x88, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xbe, 0x85, 0xe6, 0x94, 0xb6, 0xe8,
	0xb4, 0xa7, 0xef, 0xbc, 0x9b, 0x35, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6,
	0x88, 0x90, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x8a, 0xb6, 0xe6,
	0x80, 0x81, 0xe6, 0x98, 0xbe, 0xe7, 0xa4, 0xba, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x53, 0x68, 0x6f, 0x77, 0x12, 0x95, 0x01, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x42,
	0x4c, 0x92, 0x41, 0x49, 0x2a, 0x47, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5,
	0xbc, 0x8f, 0x3a, 0x30, 0xef, 0xbc, 0x9a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xef, 0xbc, 0x9b,
	0x31, 0xef, 0xbc, 0x9a, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xef, 0xbc, 0x9b,
	0x32, 0xef, 0xbc, 0x9a, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe7, 0x8e, 0xb0, 0xe9, 0x87, 0x91, 0x52, 0x0d, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x3b, 0x0a, 0x0a,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x1c, 0x92, 0x41, 0x19, 0x2a, 0x17, 0xe4, 0xb8, 0x8b, 0xe5, 0x8d, 0x95, 0xe6, 0x97, 0xb6,
	0xe9, 0x97, 0xb4, 0x28, 0xe5, 0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x29, 0x52, 0x09,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x08, 0x70, 0x61, 0x79,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x1c, 0x92, 0x41, 0x19,
	0x2a, 0x17, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x28, 0xe5,
	0x8d, 0x95, 0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0x29, 0x52, 0x07, 0x70, 0x61, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe9, 0x85, 0x8d, 0xe9, 0x80, 0x81, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0x52, 0x0e,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x47,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x63, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74,
	0x65, 0x70, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe9,
	0x85, 0x8d, 0xe9, 0x80, 0x81, 0xe6, 0xad, 0xa5, 0xe9, 0xaa, 0xa4, 0x52, 0x0f, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x12, 0x4e, 0x0a, 0x09,
	0x76, 0x70, 0x73, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x53, 0x45, 0x78, 0x74, 0x72, 0x61, 0x42, 0x14, 0x92, 0x41,
	0x11, 0x2a, 0x0f, 0x56, 0x50, 0x53, 0xe9, 0xa2, 0x9d, 0xe5, 0xa4, 0x96, 0xe4, 0xbf, 0xa1, 0xe6,
	0x81, 0xaf, 0x52, 0x08, 0x76, 0x70, 0x73, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x5a, 0x0a, 0x0c,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xb8, 0x8a, 0xe6, 0x8a, 0xa5, 0xe9,
	0xa2, 0x9d, 0xe5, 0xa4, 0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x0b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x45, 0x78, 0x74, 0x72, 0x61, 0x12, 0x66, 0x0a, 0x10, 0x65, 0x78, 0x68, 0x69,
	0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x24, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5, 0xb1, 0x95,
	0xe4, 0xbc, 0x9a, 0xe9, 0xa2, 0x9d, 0xe5, 0xa4, 0x96, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52,
	0x0f, 0x65, 0x78, 0x68, 0x69, 0x62, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x12, 0x62, 0x0a, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x73, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70, 0x65,
	0x63, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15,
	0xe9, 0x80, 0x89, 0xe4, 0xb8, 0xad, 0xe7, 0x9a, 0x84, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe8,
	0xa7, 0x84, 0xe6, 0xa0, 0xbc, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53,
	0x70, 0x65, 0x63, 0x73, 0x12, 0x72, 0x0a, 0x13, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe6, 0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe6, 0x96, 0xb9, 0xe5, 0xbc, 0x8f, 0xe5,
	0x9b, 0xbe, 0xe6, 0xa0, 0x87, 0x52, 0x11, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x22, 0x42, 0x0a, 0x15, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x29, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95,
	0xe5, 0x8f, 0xb7, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x22, 0xd7, 0x02, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8,
	0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0xe5, 0x9b, 0xbe, 0xe6,
	0xa0, 0x87, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x12,
	0x32, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0xa9, 0xe6, 0xb5,
	0x81, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x4e, 0x6f, 0x12, 0x47, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x94, 0xb6, 0xe8, 0xb4, 0xa7, 0xe5, 0x9c, 0xb0,
	0xe5, 0x9d, 0x80, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x48, 0x0a, 0x05,
	0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0xe8, 0x8a, 0x82, 0xe7, 0x82, 0xb9, 0x52,
	0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x22, 0x33, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7e, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x80, 0xbb, 0xe8, 0xae,
	0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x95, 0xb0, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3b,
	0x0a, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81,
	0xe5, 0x95, 0x86, 0xe5, 0x9f, 0x8e, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x95, 0xb0, 0x52,
	0x0a, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xca, 0x04, 0x0a, 0x16,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x73, 0x68, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x69, 0x64, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0x92,
	0x41, 0x0f, 0x2a, 0x0d, 0xe4, 0xba, 0xa4, 0xe6, 0x98, 0x93, 0xe5, 0x95, 0x86, 0x63, 0x6f, 0x64,
	0x65, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x54, 0x0a,
	0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x33, 0x92, 0x41, 0x30, 0x2a, 0x2e, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x31, 0x3a, 0xe4, 0xbf, 0xa1, 0xe7, 0x94, 0xa8, 0xe6, 0x8a,
	0xa5, 0xe5, 0x91, 0x8a, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe7, 0xa0, 0x94, 0xe7, 0xa9,
	0xb6, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe4, 0xb8,
	0x8b, 0xe5, 0x8d, 0x95, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x88, 0xe5, 0x8d, 0x95,
	0xe4, 0xbd, 0x8d, 0xe7, 0xa7, 0x92, 0xef, 0xbc, 0x89, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc,
	0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x23, 0x92, 0x41,
	0x20, 0x2a, 0x1e, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a,
	0x31, 0x3a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0x3b, 0x32, 0x3a, 0xe7, 0xbe, 0x8e, 0xe5, 0x85,
	0x83, 0x52, 0x0a, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a,
	0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x3e, 0x92, 0x41, 0x3b, 0x2a, 0x39, 0xe6, 0x8a, 0xa5,
	0xe5, 0x91, 0x8a, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x3a, 0x31, 0x3a, 0xe4, 0xb8, 0xad, 0xe6,
	0x96, 0x87, 0xe7, 0xae, 0x80, 0xe4, 0xbd, 0x93, 0xef, 0xbc, 0x9b, 0x32, 0x3a, 0xe8, 0x8b, 0xb1,
	0xe8, 0xaf, 0xad, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe4, 0xb8, 0xad, 0xe6, 0x96, 0x87,
	0xe7, 0xb9, 0x81, 0xe4, 0xbd, 0x93, 0x52, 0x0e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x40, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x25, 0x92, 0x41, 0x22, 0x2a, 0x20, 0xe6,
	0x94, 0xaf, 0xe4, 0xbb, 0x98, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x3a, 0x30, 0x3a, 0xe5, 0x85,
	0xb6, 0xe4, 0xbb, 0x96, 0xef, 0xbc, 0x9b, 0x31, 0x3a, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x54, 0x79, 0x70, 0x65, 0x22, 0x16, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x90, 0x04, 0x0a, 0x08, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x12, 0x20, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b,
	0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x28, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92,
	0x41, 0x11, 0x2a, 0x0f, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5,
	0x8d, 0xa1, 0xe5, 0x9b, 0xbe, 0xe7, 0x89, 0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f,
	0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0xa8, 0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x6d, 0x92, 0x41, 0x6a, 0x2a, 0x68, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81,
	0xe5, 0x8d, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a,
	0xe6, 0x9c, 0xaa, 0xe7, 0x9f, 0xa5, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xef, 0xbc, 0x9b, 0x31,
	0xef, 0xbc, 0x9a, 0xe6, 0x9c, 0xaa, 0xe5, 0x88, 0xb0, 0xe5, 0x8f, 0xaf, 0xe7, 0x94, 0xa8, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc, 0x9a, 0xe5, 0x8f, 0xaf, 0xe7,
	0x94, 0xa8, 0xef, 0xbc, 0x9b, 0x33, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe8, 0xbf, 0x87, 0xe6,
	0x9c, 0x9f, 0xef, 0xbc, 0x9b, 0x34, 0xef, 0xbc, 0x9a, 0xe5, 0xb7, 0xb2, 0xe4, 0xbd, 0xbf, 0xe7,
	0x94, 0xa8, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe4, 0xb8, 0x8d, 0xe5, 0x8f, 0xaf, 0xe7, 0x94, 0xa8,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe5, 0x8d, 0xb0, 0xe7, 0xab, 0xa0, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x49, 0x64, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x49, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x81,
	0x01, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x41, 0x92, 0x41, 0x3e, 0x2a, 0x3c, 0xe7, 0xa4,
	0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x74, 0x61, 0x62, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b,
	0xef, 0xbc, 0x9a, 0x30, 0xef, 0xbc, 0x9a, 0xe5, 0x85, 0xa8, 0xe9, 0x83, 0xa8, 0xef, 0xbc, 0x9b,
	0x31, 0xef, 0xbc, 0x9a, 0xe5, 0x8f, 0xaf, 0xe7, 0x94, 0xa8, 0xef, 0xbc, 0x9b, 0x32, 0xef, 0xbc,
	0x9a, 0xe4, 0xb8, 0x8d, 0xe5, 0x8f, 0xaf, 0xe7, 0x94, 0xa8, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0xaf, 0x8f, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0,
	0xe6, 0x8d, 0xae, 0xe5, 0xa4, 0xa7, 0xe5, 0xb0, 0x8f, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1f, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92,
	0x41, 0x08, 0x2a, 0x06, 0xe9, 0xa1, 0xb5, 0xe6, 0x95, 0xb0, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0xb5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4b, 0x0a, 0x04, 0x74, 0x61, 0x62,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x66, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xa4,
	0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x74, 0x61, 0x62, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8,
	0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x12, 0x50, 0x0a, 0x0a, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0xa4,
	0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x09, 0x67,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x0b, 0x47, 0x69, 0x66,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x12, 0x2b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xa4, 0xbc,
	0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x74, 0x61, 0x62, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x42, 0x1d, 0x92, 0x41,
	0x1a, 0x2a, 0x18, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x74, 0x61, 0x62, 0xe7,
	0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe7, 0x9a, 0x84, 0xe5, 0x80, 0xbc, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x95, 0xb0, 0xe9, 0x87, 0x8f, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x17, 0x0a, 0x15, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xe9,
	0x02, 0x0a, 0x13, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x20, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5,
	0x8d, 0xa1, 0x69, 0x64, 0x52, 0x02, 0x69, 0x64, 0x12, 0x28, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe7, 0xa4, 0xbc,
	0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x44, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x42, 0x14, 0x92, 0x41, 0x11,
	0x2a, 0x0f, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0xe5, 0x9b, 0xbe, 0xe7, 0x89,
	0x87, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe5, 0xbc, 0x80, 0xe5, 0xa7, 0x8b, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41,
	0x0e, 0x2a, 0x0c, 0xe7, 0xbb, 0x93, 0xe6, 0x9d, 0x9f, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe4, 0xba, 0xa7, 0xe5, 0x93, 0x81, 0x49, 0x64, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x49, 0x64, 0x12, 0x36, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x68, 0x61, 0x76, 0x65, 0x5f, 0x67, 0x69,
	0x66, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x9c, 0x89, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0x52, 0x0a,
	0x69, 0x73, 0x48, 0x61, 0x76, 0x65, 0x47, 0x69, 0x66, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x17, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x0a,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x12, 0x3c, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0x41, 0x23, 0x2a, 0x21,
	0xe6, 0x94, 0xb6, 0xe4, 0xbb, 0xb6, 0xe4, 0xba, 0xba, 0xe6, 0x88, 0x96, 0xe5, 0xaf, 0x84, 0xe4,
	0xbb, 0xb6, 0xe4, 0xba, 0xba, 0xe6, 0x89, 0x8b, 0xe6, 0x9c, 0xba, 0xe5, 0x8f, 0xb7, 0xef, 0xbc,
	0x89, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x22, 0xd4, 0x03, 0x0a, 0x15, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x32, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xbf,
	0xab, 0xe9, 0x80, 0x92, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41,
	0x14, 0x2a, 0x12, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0xe7,
	0xbc, 0x96, 0xe7, 0xa0, 0x81, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe5,
	0xbf, 0xab, 0xe9, 0x80, 0x92, 0xe5, 0x85, 0xac, 0xe5, 0x8f, 0xb8, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x50,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x65, 0x70, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0xa9, 0xe6,
	0xb5, 0x81, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x37, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6, 0x98, 0xaf,
	0xe5, 0x90, 0xa6, 0xe5, 0xb7, 0xb2, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x52, 0x0b, 0x69, 0x73,
	0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x0b, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17,
	0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x9c, 0x80, 0xe5, 0x90, 0x8e, 0xe6, 0x9b, 0xb4, 0xe6, 0x96,
	0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0a, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x4a, 0x0a, 0x06, 0x74, 0x72, 0x61, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x54,
	0x72, 0x61, 0x63, 0x65, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x89, 0xa9, 0xe6, 0xb5,
	0x81, 0xe8, 0xbd, 0xa8, 0xe8, 0xbf, 0xb9, 0x52, 0x06, 0x74, 0x72, 0x61, 0x63, 0x65, 0x73, 0x22,
	0xcb, 0x01, 0x0a, 0x0c, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x54, 0x72, 0x61, 0x63, 0x65,
	0x12, 0x1f, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a,
	0x06, 0xe5, 0x9c, 0xb0, 0xe7, 0x82, 0xb9, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74,
	0x65, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7,
	0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x66, 0x0a,
	0x1a, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x50, 0x75, 0x73, 0x68, 0x43, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x04, 0x73,
	0x69, 0x67, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe7, 0xad, 0xbe, 0xe5, 0x90, 0x8d, 0x52, 0x04, 0x73, 0x69, 0x67, 0x6e, 0x12, 0x27, 0x0a, 0x05,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe6, 0x95, 0xb0, 0xe6, 0x8d, 0xae, 0x52, 0x05,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x22, 0xa6, 0x01, 0x0a, 0x18, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x50, 0x75, 0x73, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xa4, 0x84, 0xe7, 0x90, 0x86, 0xe7,
	0xbb, 0x93, 0xe6, 0x9e, 0x9c, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x32, 0x0a,
	0x0b, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe4,
	0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbf, 0x94, 0xe5, 0x9b, 0x9e, 0xe6,
	0xb6, 0x88, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x7f,
	0x0a, 0x1c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x9e, 0x9a, 0xe4, 0xb8,
	0xbe, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x75, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x22,
	0x56, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b,
	0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x38, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x52, 0x0a, 0x69, 0x73, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x22, 0xb2, 0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a,
	0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe4, 0xbb, 0xa3, 0xe7,
	0xa0, 0x81, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a, 0x0b,
	0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xbf, 0x90, 0xe5, 0x8d, 0x95, 0xe5, 0x8f,
	0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0a, 0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c,
	0x4e, 0x6f, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x6e, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f,
	0xe8, 0xb7, 0x9f, 0xe8, 0xb8, 0xaa, 0xe5, 0x8f, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52,
	0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x73, 0x22, 0xba, 0x01, 0x0a,
	0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x2b, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x88,
	0x90, 0xe5, 0x8a, 0x9f, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x93, 0x8d, 0xe5, 0xba, 0x94, 0xe6, 0xb6, 0x88, 0xe6, 0x81,
	0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x06, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe8,
	0xbd, 0xa8, 0xe8, 0xbf, 0xb9, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xe5, 0x88, 0x97, 0xe8, 0xa1,
	0xa8, 0x52, 0x06, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x73, 0x22, 0x8f, 0x02, 0x0a, 0x09, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x0a, 0x77, 0x61, 0x79, 0x62, 0x69,
	0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe8, 0xbf, 0x90, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x09, 0x77, 0x61, 0x79,
	0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12, 0x2f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b,
	0x2a, 0x09, 0xe8, 0xb7, 0x9f, 0xe8, 0xb8, 0xaa, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x74, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0x8a,
	0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x0b,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe6, 0x8f,
	0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63,
	0x12, 0x49, 0x0a, 0x06, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0xa6, 0xe7, 0xbb, 0x86, 0xe8, 0xbd, 0xa8,
	0xe8, 0xbf, 0xb9, 0x52, 0x06, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x73, 0x22, 0xc6, 0x03, 0x0a, 0x0b,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x30, 0x0a, 0x0a, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbd, 0xa8, 0xe8, 0xbf, 0xb9, 0xe6, 0x97, 0xb6, 0xe9,
	0x97, 0xb4, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a,
	0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbd, 0xa8, 0xe8, 0xbf, 0xb9,
	0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x30, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xbd,
	0xa8, 0xe8, 0xbf, 0xb9, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63,
	0x6b, 0x44, 0x65, 0x73, 0x63, 0x12, 0x27, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe4, 0xbd,
	0x8d, 0xe7, 0xbd, 0xae, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a,
	0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe5, 0x91, 0x98,
	0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x37, 0x0a, 0x0b, 0x6f, 0x70,
	0x5f, 0x6f, 0x72, 0x67, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe6, 0x9c, 0xba, 0xe6,
	0x9e, 0x84, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x09, 0x6f, 0x70, 0x4f, 0x72, 0x67, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x6f, 0x70, 0x5f, 0x6f, 0x72, 0x67, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe6,
	0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe6, 0x9c, 0xba, 0xe6, 0x9e, 0x84, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x09, 0x6f, 0x70, 0x4f, 0x72, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x07,
	0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81,
	0x52, 0x06, 0x6f, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x6f, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe6, 0x93, 0x8d, 0xe4, 0xbd, 0x9c, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x06, 0x6f, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb4, 0x01, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x61,
	0x79, 0x62, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0,
	0x81, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x77,
	0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xbf, 0x90, 0xe5, 0x8d, 0x95, 0xe5, 0x8f, 0xb7,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0a, 0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x4e,
	0x6f, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8,
	0xb7, 0x9f, 0xe8, 0xb8, 0xaa, 0xe5, 0x8f, 0xb7, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x0b,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f, 0x73, 0x22, 0xc2, 0x01, 0x0a, 0x11,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x2b, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6,
	0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2b,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x93, 0x8d, 0xe5, 0xba, 0x94, 0xe6, 0xb6, 0x88, 0xe6,
	0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x53, 0x0a, 0x08, 0x77,
	0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x57, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x17, 0x92,
	0x41, 0x14, 0x2a, 0x12, 0xe8, 0xbf, 0x90, 0xe5, 0x8d, 0x95, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x08, 0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x73,
	0x22, 0xfa, 0x01, 0x0a, 0x0b, 0x57, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x2d, 0x0a, 0x0a, 0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xbf, 0x90, 0xe5, 0x8d,
	0x95, 0xe5, 0x8f, 0xb7, 0x52, 0x09, 0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x4e, 0x6f, 0x12,
	0x2f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0xb7, 0x9f, 0xe8, 0xb8,
	0xaa, 0xe5, 0x8f, 0xb7, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x4e, 0x6f,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0a, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x12, 0x32, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9b, 0xb4, 0xe6, 0x96, 0xb0, 0xe6, 0x97, 0xb6, 0xe9, 0x97,
	0xb4, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xdf, 0x02,
	0x0a, 0x0f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2d, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x34, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9c, 0x8d,
	0xe5, 0x8a, 0xa1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0e, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xaf, 0x84, 0xe4, 0xbb, 0xb6, 0xe5, 0x9b, 0xbd, 0xe5, 0xae,
	0xb6, 0x52, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x3c, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x94, 0xb6, 0xe4, 0xbb, 0xb6, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x52, 0x0f, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x28,
	0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x10,
	0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe9, 0x87, 0x8d, 0xe9, 0x87, 0x8f, 0x28, 0xe5, 0x85, 0x8b, 0x29,
	0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x45, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x67,
	0x6f, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x67,
	0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4, 0xa7, 0xe7,
	0x89, 0xa9, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x05, 0x63, 0x61, 0x72, 0x67, 0x6f, 0x22,
	0xb3, 0x02, 0x0a, 0x09, 0x43, 0x61, 0x72, 0x67, 0x6f, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe8, 0xb4, 0xa7, 0xe7, 0x89, 0xa9, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x95, 0xb0,
	0xe9, 0x87, 0x8f, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x28, 0x0a,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x10, 0x92,
	0x41, 0x0d, 0x2a, 0x0b, 0xe9, 0x87, 0x8d, 0xe9, 0x87, 0x8f, 0x28, 0xe5, 0x85, 0x8b, 0x29, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x10, 0x92, 0x41, 0x0d, 0x2a, 0x0b, 0xe4, 0xbb, 0xb7,
	0xe5, 0x80, 0xbc, 0x28, 0xe5, 0x88, 0x86, 0x29, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12,
	0x27, 0x0a, 0x08, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x26, 0x0a, 0x06, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5,
	0x8e, 0x9f, 0xe4, 0xba, 0xa7, 0xe5, 0x9c, 0xb0, 0x52, 0x06, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb4, 0xa7, 0xe7,
	0x89, 0xa9, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x0d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46,
	0x65, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0x52, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x93, 0x8d, 0xe5,
	0xba, 0x94, 0xe6, 0xb6, 0x88, 0xe6, 0x81, 0xaf, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x3f, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe8, 0xb4, 0xb9, 0xe7, 0x94, 0xa8, 0xe4, 0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0x52, 0x03, 0x66,
	0x65, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x07, 0x46, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a,
	0xa1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x9c, 0x8d, 0xe5, 0x8a, 0xa1, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0b, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x30, 0x0a, 0x09, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x13, 0x92,
	0x41, 0x10, 0x2a, 0x0e, 0xe6, 0x80, 0xbb, 0xe8, 0xb4, 0xb9, 0xe7, 0x94, 0xa8, 0x28, 0xe5, 0x88,
	0x86, 0x29, 0x52, 0x08, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x46, 0x65, 0x65, 0x12, 0x27, 0x0a, 0x08,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b,
	0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xb8, 0x81, 0xe7, 0xa7, 0x8d, 0x52, 0x08, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x38, 0x0a, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x11, 0x92,
	0x41, 0x0e, 0x2a, 0x0c, 0xe9, 0xa2, 0x84, 0xe8, 0xae, 0xa1, 0xe5, 0xa4, 0xa9, 0xe6, 0x95, 0xb0,
	0x52, 0x0d, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x79, 0x73, 0x22,
	0xeb, 0x02, 0x0a, 0x17, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe6, 0x8e, 0xa5, 0xe5, 0x8f, 0xa3, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x06,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe5, 0xae, 0xa2, 0xe6, 0x88, 0xb7, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x08, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x12, 0x2c, 0x0a, 0x08, 0x6d, 0x73, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6,
	0xb6, 0x88, 0xe6, 0x81, 0xaf, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x07, 0x6d, 0x73, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe6,
	0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe6, 0x88, 0xb3, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe7, 0x89, 0x88, 0xe6, 0x9c,
	0xac, 0xe5, 0x8f, 0xb7, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a,
	0x11, 0x6c, 0x6f, 0x67, 0x69, 0x74, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe6,
	0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe6, 0xb6, 0x88, 0xe6, 0x81, 0xaf, 0xe4, 0xbd, 0x93, 0x52, 0x10,
	0x6c, 0x6f, 0x67, 0x69, 0x74, 0x63, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x12, 0x2b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe5, 0xba, 0x8f, 0xe5, 0x88, 0x97,
	0xe5, 0x8f, 0xb7, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x6f, 0x22, 0xd1, 0x01,
	0x0a, 0x15, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x65, 0x72, 0x69, 0x61,
	0x6c, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe5, 0xba, 0x8f, 0xe5, 0x88, 0x97, 0xe5, 0x8f, 0xb7, 0x52, 0x08, 0x73, 0x65, 0x72, 0x69,
	0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x25, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x93, 0x8d, 0xe5, 0xba, 0x94, 0xe4,
	0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x34, 0x0a, 0x0c, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0x93, 0x8d, 0xe5, 0xba, 0x94, 0xe6, 0xb6,
	0x88, 0xe6, 0x81, 0xaf, 0x52, 0x0b, 0x63, 0x6f, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2e, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe5, 0xae, 0xa2, 0xe6, 0x88,
	0xb7, 0xe4, 0xbb, 0xa3, 0xe7, 0xa0, 0x81, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x4e,
	0x6f, 0x2a, 0xa1, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x69, 0x63, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x51, 0x75, 0x69, 0x63,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x51, 0x75, 0x69, 0x63,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x45, 0x61,
	0x72, 0x6e, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x51, 0x75, 0x69,
	0x63, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x10, 0x02, 0x12, 0x27, 0x0a, 0x23,
	0x51, 0x75, 0x69, 0x63, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4a, 0x75, 0x6d, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x10, 0x03, 0x2a, 0x39, 0x0a, 0x0e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x10, 0x01,
	0x2a, 0x80, 0x01, 0x0a, 0x16, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x1e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x49, 0x63, 0x6f, 0x6e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x1f, 0x0a, 0x1b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x10, 0x01,
	0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x53, 0x10, 0x02, 0x2a, 0x94, 0x01, 0x0a, 0x0e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x47,
	0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x6f, 0x74,
	0x52, 0x65, 0x61, 0x64, 0x79, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x69, 0x66, 0x74, 0x43,
	0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64,
	0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x55, 0x73, 0x65, 0x64, 0x10, 0x04, 0x2a, 0x70, 0x0a, 0x13, 0x55, 0x73,
	0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x61, 0x62, 0x54, 0x79, 0x70, 0x65, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1d, 0x0a,
	0x19, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a,
	0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0xe9, 0x01, 0x0a,
	0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1b, 0x0a, 0x17, 0x4c, 0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18,
	0x4c, 0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f,
	0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4c, 0x4f,
	0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x55,
	0x54, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19,
	0x4c, 0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x4c,
	0x4f, 0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f,
	0x47, 0x49, 0x53, 0x54, 0x49, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x45, 0x44, 0x10, 0x06, 0x32, 0xb7, 0x36, 0x0a, 0x07, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x12, 0x9e, 0x01,
	0x0a, 0x14, 0x47, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x69, 0x63, 0x6b,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x6f, 0x6c, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x42, 0x92, 0x41, 0x1c, 0x0a,
	0x0c, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xe5, 0x95, 0x86, 0xe5, 0x9f, 0x8e, 0x12, 0x0c, 0xe5,
	0xbf, 0xab, 0xe6, 0x8d, 0xb7, 0xe5, 0x85, 0xa5, 0xe5, 0x8f, 0xa3, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2f, 0x71, 0x75, 0x69, 0x63, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x9e,
	0x01, 0x0a, 0x0a, 0x4d, 0x79, 0x47, 0x6f, 0x6c, 0x64, 0x4a, 0x75, 0x6d, 0x70, 0x12, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4d, 0x79, 0x47, 0x6f, 0x6c, 0x64, 0x4a, 0x75, 0x6d, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x79, 0x47, 0x6f, 0x6c, 0x64, 0x4a, 0x75,
	0x6d, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x46, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe9, 0x87,
	0x91, 0xe5, 0xb8, 0x81, 0xe5, 0x95, 0x86, 0xe5, 0x9f, 0x8e, 0x12, 0x12, 0xe6, 0x88, 0x91, 0xe7,
	0x9a, 0x84, 0xe9, 0x87, 0x91, 0xe5, 0xb8, 0x81, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x79, 0x5f,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2f, 0x6a, 0x75, 0x6d, 0x70, 0x12,
	0x83, 0x01, 0x0a, 0x08, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x54, 0x61, 0x62, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x31, 0x92, 0x41, 0x19, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x12,
	0x0f, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x74, 0x61, 0x62,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x2f, 0x74, 0x61, 0x62, 0x12, 0x8a, 0x01, 0x0a, 0x09, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x35, 0x92, 0x41, 0x1c,
	0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96,
	0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x2f, 0x6c, 0x69,
	0x73, 0x74, 0x12, 0x8a, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x31, 0x92, 0x41,
	0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5, 0x93, 0x81, 0x12, 0x0c, 0xe5, 0x95, 0x86, 0xe5, 0x93,
	0x81, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f,
	0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x84, 0x01, 0x0a, 0x09, 0x42, 0x65, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x65, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x65, 0x73, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x92, 0x41, 0x16, 0x0a, 0x06, 0xe5, 0x95, 0x86, 0xe5,
	0x93, 0x81, 0x12, 0x0c, 0xe7, 0xb2, 0xbe, 0xe9, 0x80, 0x89, 0xe5, 0xa5, 0xbd, 0xe7, 0x89, 0xa9,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x12, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x64,
	0x73, 0x2f, 0x62, 0x65, 0x73, 0x74, 0x12, 0xbf, 0x01, 0x0a, 0x10, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x55, 0x92, 0x41, 0x37, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x12,
	0xe4, 0xb8, 0x8b, 0xe5, 0x8d, 0x95, 0xe4, 0xbb, 0xb7, 0xe6, 0xa0, 0xbc, 0xe8, 0xae, 0xa1, 0xe7,
	0xae, 0x97, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49,
	0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xa9, 0x01, 0x0a, 0x08, 0x50, 0x72, 0x65,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x54,
	0x92, 0x41, 0x34, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0f, 0xe4, 0xb8, 0x8b,
	0xe5, 0x8d, 0x95, 0xe9, 0xa2, 0x84, 0xe6, 0xa3, 0x80, 0xe6, 0x9f, 0xa5, 0x72, 0x19, 0x0a, 0x17,
	0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a,
	0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x65, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x12, 0xa3, 0x01, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x48, 0x92, 0x41, 0x31, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0c, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe4, 0xb8, 0x8b, 0xe5, 0x8d, 0x95, 0x72, 0x19, 0x0a, 0x17, 0x0a,
	0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0e, 0x3a, 0x01, 0x2a, 0x22,
	0x09, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x93, 0x01, 0x0a, 0x0b, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x38, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8, 0xae, 0xa2,
	0xe5, 0x8d, 0x95, 0x12, 0x12, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0xad, 0x9b, 0xe9, 0x80,
	0x89, 0xe5, 0x88, 0x86, 0xe7, 0xb1, 0xbb, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x12, 0x11, 0x2f,
	0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x12, 0x99, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x92, 0x41,
	0x34, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0f, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe6, 0x95, 0xb0, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09,
	0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x89, 0x01, 0x0a,
	0x0b, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x62, 0x12, 0x25, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61, 0x62,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x92, 0x41, 0x19, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5,
	0x8d, 0x95, 0x12, 0x0f, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95,
	0x74, 0x61, 0x62, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x74, 0x61, 0x62, 0x12, 0xab, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x53, 0x92, 0x41, 0x37, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x12,
	0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe5, 0x88, 0x97, 0xe8,
	0xa1, 0xa8, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49,
	0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xab, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x22, 0x52, 0x92, 0x41, 0x37, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x12, 0xe8,
	0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0xe8, 0xaf, 0xa6, 0xe6, 0x83,
	0x85, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64,
	0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x12, 0xb9, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x4f, 0x92, 0x41, 0x31, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0c, 0xe8, 0xae,
	0xa2, 0xe5, 0x8d, 0x95, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09,
	0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6c, 0x6f, 0x67, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73,
	0x12, 0xbd, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x50,
	0x92, 0x41, 0x31, 0x0a, 0x06, 0xe8, 0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x0c, 0xe8, 0xae, 0xa2,
	0xe5, 0x8d, 0x95, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58,
	0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0xa6, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x75, 0x73, 0x68, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50,
	0x75, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3f, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe8,
	0xae, 0xa2, 0xe5, 0x8d, 0x95, 0x12, 0x12, 0xe6, 0x8a, 0xa5, 0xe5, 0x91, 0x8a, 0xe8, 0xae, 0xa2,
	0xe5, 0x8d, 0x95, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a,
	0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x12, 0xba, 0x01, 0x0a, 0x10, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x50, 0x92, 0x41, 0x31, 0x0a, 0x06, 0xe7, 0x89, 0xa9, 0xe6, 0xb5,
	0x81, 0x12, 0x0c, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0x72,
	0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16,
	0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x2f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0xb4, 0x01, 0x0a, 0x13, 0x45, 0x78, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x50, 0x75, 0x73, 0x68, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x50, 0x75, 0x73, 0x68, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x50, 0x75, 0x73, 0x68, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x41, 0x92, 0x41, 0x1f, 0x0a,
	0x06, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81, 0x12, 0x15, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92, 0x31,
	0x30, 0x30, 0xe6, 0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe5, 0x9b, 0x9e, 0xe8, 0xb0, 0x83, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x19, 0x3a, 0x01, 0x2a, 0x22, 0x14, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x2f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0xd8, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c,
	0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x62, 0x92, 0x41, 0x3d, 0x0a, 0x06, 0xe7, 0xad, 0xbe, 0xe5, 0x88,
	0xb0, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0, 0xe4,
	0xbf, 0xa1, 0xe6, 0x81, 0xaf, 0xe8, 0x81, 0x9a, 0xe5, 0x90, 0x88, 0x72, 0x19, 0x0a, 0x17, 0x0a,
	0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x22,
	0x17, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x2d, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x96, 0x01, 0x0a, 0x06, 0x53, 0x69, 0x67,
	0x6e, 0x49, 0x6e, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4a, 0x92, 0x41, 0x31, 0x0a, 0x06, 0xe7, 0xad, 0xbe, 0xe5,
	0x88, 0xb0, 0x12, 0x0c, 0xe6, 0x89, 0xa7, 0xe8, 0xa1, 0x8c, 0xe7, 0xad, 0xbe, 0xe5, 0x88, 0xb0,
	0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12,
	0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x69, 0x67, 0x6e, 0x2f, 0x69,
	0x6e, 0x12, 0x92, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x37, 0x92,
	0x41, 0x1c, 0x0a, 0x06, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5,
	0x8f, 0x96, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xa6, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x39, 0x92, 0x41, 0x1c, 0x0a, 0x06, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x12, 0x12, 0xe9, 0xa2, 0x86, 0xe5, 0x8f, 0x96, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5,
	0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12,
	0xa9, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x3c, 0x92,
	0x41, 0x1c, 0x0a, 0x06, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x12, 0x12, 0xe6, 0x9f, 0xa5, 0xe8,
	0xaf, 0xa2, 0xe5, 0xa5, 0x96, 0xe5, 0x8a, 0xb1, 0xe8, 0xae, 0xb0, 0xe5, 0xbd, 0x95, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x2f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x98, 0x01, 0x0a, 0x14,
	0x53, 0x65, 0x6e, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x12, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x38, 0x92, 0x41,
	0x1c, 0x0a, 0x06, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x12, 0x12, 0xe5, 0x8f, 0x91, 0xe9, 0x80,
	0x81, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe4, 0xba, 0x8b, 0xe4, 0xbb, 0xb6, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x61, 0x73, 0x6b,
	0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12, 0xd5, 0x01, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54, 0x61,
	0x73, 0x6b, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x61, 0x73, 0x6b, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x5c, 0x92, 0x41, 0x3d, 0x0a, 0x06, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x12, 0x18, 0xe6,
	0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe6, 0x98, 0xaf, 0xe5, 0x90,
	0xa6, 0xe5, 0xae, 0x8c, 0xe6, 0x88, 0x90, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55,
	0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44,
	0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x16, 0x3a, 0x01, 0x2a, 0x22, 0x11, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x12, 0x8d,
	0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x3d, 0x92, 0x41, 0x22, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x12, 0x12, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6,
	0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76,
	0x31, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xb3,
	0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x63, 0x92, 0x41, 0x43, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5,
	0x9d, 0x80, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x72, 0x19, 0x0a, 0x17,
	0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f,
	0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0xa9, 0x01, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a,
	0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x5b, 0x92, 0x41, 0x3d, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x12, 0x12, 0xe6, 0xb7, 0xbb, 0xe5, 0x8a, 0xa0, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09,
	0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x3a, 0x01, 0x2a, 0x22, 0x10,
	0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0xa3, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x62, 0x92, 0x41, 0x3d, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x12, 0x12, 0xe4, 0xbf, 0xae, 0xe6, 0x94, 0xb9, 0xe7, 0x94, 0xa8,
	0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58,
	0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x3a, 0x01, 0x2a, 0x1a, 0x17, 0x2f,
	0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xc1, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f,
	0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x22, 0x65, 0x92, 0x41, 0x43, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x12, 0x18, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe8, 0xaf, 0xa6, 0xe6, 0x83, 0x85,
	0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12,
	0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xc2, 0x01, 0x0a, 0x11, 0x53,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x6c, 0x92, 0x41, 0x46, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c,
	0xb0, 0xe5, 0x9d, 0x80, 0x12, 0x1b, 0xe8, 0xae, 0xbe, 0xe7, 0xbd, 0xae, 0xe7, 0x94, 0xa8, 0xe6,
	0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0xe4, 0xb8, 0xba, 0xe9, 0xbb, 0x98, 0xe8, 0xae,
	0xa4, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64,
	0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x1d, 0x3a, 0x01, 0x2a, 0x1a, 0x18, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12,
	0xad, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5f,
	0x92, 0x41, 0x3d, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d,
	0x80, 0x12, 0x12, 0xe5, 0x88, 0xa0, 0xe9, 0x99, 0xa4, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65,
	0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x2a, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0xb0, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x5e, 0x92, 0x41, 0x3d, 0x0a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x12, 0x12, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0,
	0xe5, 0x9d, 0x80, 0xe6, 0x80, 0xbb, 0xe6, 0x95, 0xb0, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58,
	0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0xa9, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x64, 0x92, 0x41, 0x43, 0x0a, 0x0c, 0xe7,
	0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x12, 0x18, 0xe8, 0x8e, 0xb7,
	0xe5, 0x8f, 0x96, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe8, 0xb4, 0xad, 0xe7, 0x89, 0xa9, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x72, 0x19, 0x0a, 0x17, 0x0a, 0x09, 0x58, 0x2d, 0x55, 0x73, 0x65,
	0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x18, 0x01,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0xab,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x44, 0x92, 0x41, 0x22, 0x0a, 0x09, 0xe7, 0xa4, 0xbc,
	0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x12, 0x15, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe7, 0x94,
	0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d, 0xa1, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x19, 0x12, 0x17, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x67, 0x69,
	0x66, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xaa, 0x01, 0x0a,
	0x0e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x12,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69, 0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6d, 0x69,
	0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x69,
	0x66, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x46, 0x92, 0x41, 0x22, 0x0a, 0x09, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5, 0x8d,
	0xa1, 0x12, 0x15, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe7, 0xa4, 0xbc, 0xe5, 0x93, 0x81, 0xe5,
	0x8d, 0xa1, 0xe6, 0x8f, 0x90, 0xe7, 0xa4, 0xba, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x12, 0x19,
	0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x67, 0x69, 0x66, 0x74, 0x5f, 0x63, 0x61,
	0x72, 0x64, 0x2f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x12, 0x8f, 0x01, 0x0a, 0x0a, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67,
	0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x37, 0x92, 0x41, 0x1f, 0x0a, 0x09, 0x45, 0x4d, 0x53, 0xe5, 0xbf, 0xab, 0xe9,
	0x80, 0x92, 0x12, 0x12, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe7, 0x89, 0xa9, 0xe6, 0xb5, 0x81,
	0xe8, 0xbd, 0xa8, 0xe8, 0xbf, 0xb9, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6d, 0x73, 0x2f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x12, 0x91, 0x01, 0x0a, 0x0c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x12, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x57, 0x61,
	0x79, 0x62, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x33, 0x92, 0x41, 0x19, 0x0a,
	0x09, 0x45, 0x4d, 0x53, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92, 0x12, 0x0c, 0xe6, 0x9f, 0xa5, 0xe8,
	0xaf, 0xa2, 0xe8, 0xbf, 0x90, 0xe5, 0x8d, 0x95, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x12, 0x0f,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x73, 0x2f, 0x77, 0x61, 0x79, 0x62, 0x69, 0x6c, 0x6c, 0x12,
	0x84, 0x01, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x65, 0x65, 0x12, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x65, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x32, 0x92, 0x41, 0x19, 0x0a, 0x09, 0x45, 0x4d, 0x53, 0xe5, 0xbf, 0xab, 0xe9,
	0x80, 0x92, 0x12, 0x0c, 0xe6, 0x9f, 0xa5, 0xe8, 0xaf, 0xa2, 0xe8, 0xb4, 0xb9, 0xe7, 0x94, 0xa8,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x6d, 0x73, 0x2f, 0x66, 0x65, 0x65, 0x12, 0xa9, 0x01, 0x0a, 0x16, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x39, 0x92, 0x41, 0x1f, 0x0a, 0x09, 0x45, 0x4d,
	0x53, 0xe5, 0xbf, 0xab, 0xe9, 0x80, 0x92, 0x12, 0x12, 0xe6, 0x8e, 0xa5, 0xe6, 0x94, 0xb6, 0xe6,
	0x8e, 0xa8, 0xe9, 0x80, 0x81, 0xe9, 0x80, 0x9a, 0xe7, 0x9f, 0xa5, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x11, 0x3a, 0x01, 0x2a, 0x22, 0x0c, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6d, 0x73, 0x2f, 0x70, 0x75,
	0x73, 0x68, 0x42, 0x16, 0x5a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x67, 0x6f, 0x6c, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_gold_store_v1_service_proto_rawDescOnce sync.Once
	file_gold_store_v1_service_proto_rawDescData = file_gold_store_v1_service_proto_rawDesc
)

func file_gold_store_v1_service_proto_rawDescGZIP() []byte {
	file_gold_store_v1_service_proto_rawDescOnce.Do(func() {
		file_gold_store_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_gold_store_v1_service_proto_rawDescData)
	})
	return file_gold_store_v1_service_proto_rawDescData
}

var file_gold_store_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_gold_store_v1_service_proto_msgTypes = make([]protoimpl.MessageInfo, 79)
var file_gold_store_v1_service_proto_goTypes = []interface{}{
	(QuicAccessJumpType)(0),              // 0: api.gold_store.v1.QuicAccessJumpType
	(GoodsLabelType)(0),                  // 1: api.gold_store.v1.GoodsLabelType
	(OrderPaymentMethodIcon)(0),          // 2: api.gold_store.v1.OrderPaymentMethodIcon
	(GiftCardStatus)(0),                  // 3: api.gold_store.v1.GiftCardStatus
	(UserGiftCardTabType)(0),             // 4: api.gold_store.v1.UserGiftCardTabType
	(LogisticStatus)(0),                  // 5: api.gold_store.v1.LogisticStatus
	(*QuickAccessItem)(nil),              // 6: api.gold_store.v1.QuickAccessItem
	(*GoldStoreQuickAccessReply)(nil),    // 7: api.gold_store.v1.GoldStoreQuickAccessReply
	(*MyGoldJumpRequest)(nil),            // 8: api.gold_store.v1.MyGoldJumpRequest
	(*MyGoldJumpReply)(nil),              // 9: api.gold_store.v1.MyGoldJumpReply
	(*GoodsLabel)(nil),                   // 10: api.gold_store.v1.GoodsLabel
	(*GoodsListItem)(nil),                // 11: api.gold_store.v1.GoodsListItem
	(*GoodsDetail)(nil),                  // 12: api.gold_store.v1.GoodsDetail
	(*GoodsTabRequest)(nil),              // 13: api.gold_store.v1.GoodsTabRequest
	(*GoodsTab)(nil),                     // 14: api.gold_store.v1.GoodsTab
	(*GoodsTabReply)(nil),                // 15: api.gold_store.v1.GoodsTabReply
	(*GoodsListRequest)(nil),             // 16: api.gold_store.v1.GoodsListRequest
	(*GoodsListReply)(nil),               // 17: api.gold_store.v1.GoodsListReply
	(*GoodsDetailRequest)(nil),           // 18: api.gold_store.v1.GoodsDetailRequest
	(*BestGoodsRequest)(nil),             // 19: api.gold_store.v1.BestGoodsRequest
	(*BestGoodsReply)(nil),               // 20: api.gold_store.v1.BestGoodsReply
	(*CountryInfo)(nil),                  // 21: api.gold_store.v1.CountryInfo
	(*GetCountryListReply)(nil),          // 22: api.gold_store.v1.GetCountryListReply
	(*GetAddressListReply)(nil),          // 23: api.gold_store.v1.GetAddressListReply
	(*AddAddressReply)(nil),              // 24: api.gold_store.v1.AddAddressReply
	(*SetAddressDefaultRequest)(nil),     // 25: api.gold_store.v1.SetAddressDefaultRequest
	(*DeleteAddressRequest)(nil),         // 26: api.gold_store.v1.DeleteAddressRequest
	(*GetAddressDetailRequest)(nil),      // 27: api.gold_store.v1.GetAddressDetailRequest
	(*GetAddressCountReply)(nil),         // 28: api.gold_store.v1.GetAddressCountReply
	(*OrderTotalAmountRequest)(nil),      // 29: api.gold_store.v1.OrderTotalAmountRequest
	(*OrderTotalAmountReply)(nil),        // 30: api.gold_store.v1.OrderTotalAmountReply
	(*SpecSelected)(nil),                 // 31: api.gold_store.v1.SpecSelected
	(*CreateOrderRequest)(nil),           // 32: api.gold_store.v1.CreateOrderRequest
	(*PreCheckReply)(nil),                // 33: api.gold_store.v1.PreCheckReply
	(*CreateOrderReply)(nil),             // 34: api.gold_store.v1.CreateOrderReply
	(*OrderFilterRequest)(nil),           // 35: api.gold_store.v1.OrderFilterRequest
	(*OrderFilterValue)(nil),             // 36: api.gold_store.v1.OrderFilterValue
	(*OrderFilterGroup)(nil),             // 37: api.gold_store.v1.OrderFilterGroup
	(*OrderFilterReply)(nil),             // 38: api.gold_store.v1.OrderFilterReply
	(*OrderCountReply)(nil),              // 39: api.gold_store.v1.OrderCountReply
	(*OrderBase)(nil),                    // 40: api.gold_store.v1.OrderBase
	(*OrderTab)(nil),                     // 41: api.gold_store.v1.OrderTab
	(*GetOrderTabRequest)(nil),           // 42: api.gold_store.v1.GetOrderTabRequest
	(*OrderTabReply)(nil),                // 43: api.gold_store.v1.OrderTabReply
	(*OrderFilter)(nil),                  // 44: api.gold_store.v1.OrderFilter
	(*OrderListRequest)(nil),             // 45: api.gold_store.v1.OrderListRequest
	(*OrderListReply)(nil),               // 46: api.gold_store.v1.OrderListReply
	(*OrderDetailRequest)(nil),           // 47: api.gold_store.v1.OrderDetailRequest
	(*VPSExtra)(nil),                     // 48: api.gold_store.v1.VPSExtra
	(*ReportExtra)(nil),                  // 49: api.gold_store.v1.ReportExtra
	(*ExhibitionUser)(nil),               // 50: api.gold_store.v1.ExhibitionUser
	(*ExhibitionExtra)(nil),              // 51: api.gold_store.v1.ExhibitionExtra
	(*OrderDetail)(nil),                  // 52: api.gold_store.v1.OrderDetail
	(*OrderLogisticsRequest)(nil),        // 53: api.gold_store.v1.OrderLogisticsRequest
	(*GetOrderLogisticsReply)(nil),       // 54: api.gold_store.v1.GetOrderLogisticsReply
	(*GetUserOrderCountRequest)(nil),     // 55: api.gold_store.v1.GetUserOrderCountRequest
	(*GetUserOrderCountReply)(nil),       // 56: api.gold_store.v1.GetUserOrderCountReply
	(*ReportOrderPushRequest)(nil),       // 57: api.gold_store.v1.ReportOrderPushRequest
	(*ReportOrderPushReply)(nil),         // 58: api.gold_store.v1.ReportOrderPushReply
	(*GiftCard)(nil),                     // 59: api.gold_store.v1.GiftCard
	(*GetUserGiftCardRequest)(nil),       // 60: api.gold_store.v1.GetUserGiftCardRequest
	(*GetUserGiftCardReply)(nil),         // 61: api.gold_store.v1.GetUserGiftCardReply
	(*GiftCardTab)(nil),                  // 62: api.gold_store.v1.GiftCardTab
	(*GiftCardRemindRequest)(nil),        // 63: api.gold_store.v1.GiftCardRemindRequest
	(*GiftCardRemindReply)(nil),          // 64: api.gold_store.v1.GiftCardRemindReply
	(*QueryExpressInfoRequest)(nil),      // 65: api.gold_store.v1.QueryExpressInfoRequest
	(*QueryExpressInfoReply)(nil),        // 66: api.gold_store.v1.QueryExpressInfoReply
	(*ExpressTrace)(nil),                 // 67: api.gold_store.v1.ExpressTrace
	(*ExpressPushCallbackRequest)(nil),   // 68: api.gold_store.v1.ExpressPushCallbackRequest
	(*ExpressPushCallbackReply)(nil),     // 69: api.gold_store.v1.ExpressPushCallbackReply
	(*QueryUserTaskFinishedRequest)(nil), // 70: api.gold_store.v1.QueryUserTaskFinishedRequest
	(*QueryUserTaskFinishedReply)(nil),   // 71: api.gold_store.v1.QueryUserTaskFinishedReply
	(*QueryTrackRequest)(nil),            // 72: api.gold_store.v1.QueryTrackRequest
	(*QueryTrackReply)(nil),              // 73: api.gold_store.v1.QueryTrackReply
	(*TrackInfo)(nil),                    // 74: api.gold_store.v1.TrackInfo
	(*TrackDetail)(nil),                  // 75: api.gold_store.v1.TrackDetail
	(*QueryWaybillRequest)(nil),          // 76: api.gold_store.v1.QueryWaybillRequest
	(*QueryWaybillReply)(nil),            // 77: api.gold_store.v1.QueryWaybillReply
	(*WaybillInfo)(nil),                  // 78: api.gold_store.v1.WaybillInfo
	(*QueryFeeRequest)(nil),              // 79: api.gold_store.v1.QueryFeeRequest
	(*CargoInfo)(nil),                    // 80: api.gold_store.v1.CargoInfo
	(*QueryFeeReply)(nil),                // 81: api.gold_store.v1.QueryFeeReply
	(*FeeInfo)(nil),                      // 82: api.gold_store.v1.FeeInfo
	(*PushNotificationRequest)(nil),      // 83: api.gold_store.v1.PushNotificationRequest
	(*PushNotificationReply)(nil),        // 84: api.gold_store.v1.PushNotificationReply
	(*Image)(nil),                        // 85: api.gold_store.v1.Image
	(GoodsStatus)(0),                     // 86: api.gold_store.v1.GoodsStatus
	(GoodsCategory)(0),                   // 87: api.gold_store.v1.GoodsCategory
	(*GoodsSpec)(nil),                    // 88: api.gold_store.v1.GoodsSpec
	(*GoodsSku)(nil),                     // 89: api.gold_store.v1.GoodsSku
	(*Address)(nil),                      // 90: api.gold_store.v1.Address
	(PaymentMethod)(0),                   // 91: api.gold_store.v1.PaymentMethod
	(OrderSource)(0),                     // 92: api.gold_store.v1.OrderSource
	(OrderStatus)(0),                     // 93: api.gold_store.v1.OrderStatus
	(*LogisticStep)(nil),                 // 94: api.gold_store.v1.LogisticStep
	(LogisticStepStatus)(0),              // 95: api.gold_store.v1.LogisticStepStatus
	(*common.EmptyRequest)(nil),          // 96: common.EmptyRequest
	(*GetSignAggregateInfoRequest)(nil),  // 97: api.gold_store.v1.GetSignAggregateInfoRequest
	(*SignInRequest)(nil),                // 98: api.gold_store.v1.SignInRequest
	(*GetTaskListRequest)(nil),           // 99: api.gold_store.v1.GetTaskListRequest
	(*ReceiveTaskRewardRequest)(nil),     // 100: api.gold_store.v1.ReceiveTaskRewardRequest
	(*ListRewardRecordsRequest)(nil),     // 101: api.gold_store.v1.ListRewardRecordsRequest
	(*TaskEventRequest)(nil),             // 102: api.gold_store.v1.TaskEventRequest
	(*common.HealthyReply)(nil),          // 103: common.HealthyReply
	(*GetSignAggregateInfoReply)(nil),    // 104: api.gold_store.v1.GetSignAggregateInfoReply
	(*SignInReply)(nil),                  // 105: api.gold_store.v1.SignInReply
	(*GetTaskListReply)(nil),             // 106: api.gold_store.v1.GetTaskListReply
	(*ReceiveTaskRewardReply)(nil),       // 107: api.gold_store.v1.ReceiveTaskRewardReply
	(*ListRewardRecordsReply)(nil),       // 108: api.gold_store.v1.ListRewardRecordsReply
	(*TaskEventReply)(nil),               // 109: api.gold_store.v1.TaskEventReply
	(*common.EmptyReply)(nil),            // 110: common.EmptyReply
}
var file_gold_store_v1_service_proto_depIdxs = []int32{
	0,   // 0: api.gold_store.v1.QuickAccessItem.jump_type:type_name -> api.gold_store.v1.QuicAccessJumpType
	6,   // 1: api.gold_store.v1.GoldStoreQuickAccessReply.items:type_name -> api.gold_store.v1.QuickAccessItem
	1,   // 2: api.gold_store.v1.GoodsLabel.type:type_name -> api.gold_store.v1.GoodsLabelType
	85,  // 3: api.gold_store.v1.GoodsListItem.image:type_name -> api.gold_store.v1.Image
	10,  // 4: api.gold_store.v1.GoodsListItem.labels:type_name -> api.gold_store.v1.GoodsLabel
	86,  // 5: api.gold_store.v1.GoodsListItem.status:type_name -> api.gold_store.v1.GoodsStatus
	87,  // 6: api.gold_store.v1.GoodsListItem.category:type_name -> api.gold_store.v1.GoodsCategory
	87,  // 7: api.gold_store.v1.GoodsDetail.category:type_name -> api.gold_store.v1.GoodsCategory
	85,  // 8: api.gold_store.v1.GoodsDetail.image:type_name -> api.gold_store.v1.Image
	86,  // 9: api.gold_store.v1.GoodsDetail.status:type_name -> api.gold_store.v1.GoodsStatus
	10,  // 10: api.gold_store.v1.GoodsDetail.best_sellers:type_name -> api.gold_store.v1.GoodsLabel
	10,  // 11: api.gold_store.v1.GoodsDetail.labels:type_name -> api.gold_store.v1.GoodsLabel
	85,  // 12: api.gold_store.v1.GoodsDetail.carousels:type_name -> api.gold_store.v1.Image
	85,  // 13: api.gold_store.v1.GoodsDetail.details:type_name -> api.gold_store.v1.Image
	88,  // 14: api.gold_store.v1.GoodsDetail.specs:type_name -> api.gold_store.v1.GoodsSpec
	89,  // 15: api.gold_store.v1.GoodsDetail.skus:type_name -> api.gold_store.v1.GoodsSku
	14,  // 16: api.gold_store.v1.GoodsTabReply.tabs:type_name -> api.gold_store.v1.GoodsTab
	11,  // 17: api.gold_store.v1.GoodsTabReply.goods:type_name -> api.gold_store.v1.GoodsListItem
	11,  // 18: api.gold_store.v1.GoodsListReply.goods:type_name -> api.gold_store.v1.GoodsListItem
	11,  // 19: api.gold_store.v1.BestGoodsReply.goods:type_name -> api.gold_store.v1.GoodsListItem
	21,  // 20: api.gold_store.v1.GetCountryListReply.items:type_name -> api.gold_store.v1.CountryInfo
	90,  // 21: api.gold_store.v1.GetAddressListReply.items:type_name -> api.gold_store.v1.Address
	91,  // 22: api.gold_store.v1.OrderTotalAmountRequest.method:type_name -> api.gold_store.v1.PaymentMethod
	31,  // 23: api.gold_store.v1.OrderTotalAmountRequest.specs:type_name -> api.gold_store.v1.SpecSelected
	90,  // 24: api.gold_store.v1.OrderTotalAmountReply.address:type_name -> api.gold_store.v1.Address
	91,  // 25: api.gold_store.v1.CreateOrderRequest.method:type_name -> api.gold_store.v1.PaymentMethod
	31,  // 26: api.gold_store.v1.CreateOrderRequest.specs:type_name -> api.gold_store.v1.SpecSelected
	90,  // 27: api.gold_store.v1.PreCheckReply.address:type_name -> api.gold_store.v1.Address
	12,  // 28: api.gold_store.v1.PreCheckReply.goods_detail:type_name -> api.gold_store.v1.GoodsDetail
	36,  // 29: api.gold_store.v1.OrderFilterGroup.values:type_name -> api.gold_store.v1.OrderFilterValue
	37,  // 30: api.gold_store.v1.OrderFilterReply.groups:type_name -> api.gold_store.v1.OrderFilterGroup
	85,  // 31: api.gold_store.v1.OrderBase.image:type_name -> api.gold_store.v1.Image
	92,  // 32: api.gold_store.v1.OrderBase.source:type_name -> api.gold_store.v1.OrderSource
	91,  // 33: api.gold_store.v1.OrderBase.payment_method:type_name -> api.gold_store.v1.PaymentMethod
	93,  // 34: api.gold_store.v1.OrderBase.status:type_name -> api.gold_store.v1.OrderStatus
	2,   // 35: api.gold_store.v1.OrderBase.payment_method_icon:type_name -> api.gold_store.v1.OrderPaymentMethodIcon
	93,  // 36: api.gold_store.v1.OrderTab.status:type_name -> api.gold_store.v1.OrderStatus
	41,  // 37: api.gold_store.v1.OrderTabReply.tabs:type_name -> api.gold_store.v1.OrderTab
	93,  // 38: api.gold_store.v1.OrderListRequest.status:type_name -> api.gold_store.v1.OrderStatus
	37,  // 39: api.gold_store.v1.OrderListRequest.filters:type_name -> api.gold_store.v1.OrderFilterGroup
	40,  // 40: api.gold_store.v1.OrderListReply.orders:type_name -> api.gold_store.v1.OrderBase
	50,  // 41: api.gold_store.v1.ExhibitionExtra.users:type_name -> api.gold_store.v1.ExhibitionUser
	85,  // 42: api.gold_store.v1.OrderDetail.image:type_name -> api.gold_store.v1.Image
	87,  // 43: api.gold_store.v1.OrderDetail.category:type_name -> api.gold_store.v1.GoodsCategory
	92,  // 44: api.gold_store.v1.OrderDetail.source:type_name -> api.gold_store.v1.OrderSource
	93,  // 45: api.gold_store.v1.OrderDetail.status:type_name -> api.gold_store.v1.OrderStatus
	91,  // 46: api.gold_store.v1.OrderDetail.payment_method:type_name -> api.gold_store.v1.PaymentMethod
	90,  // 47: api.gold_store.v1.OrderDetail.address:type_name -> api.gold_store.v1.Address
	94,  // 48: api.gold_store.v1.OrderDetail.current_delivery:type_name -> api.gold_store.v1.LogisticStep
	48,  // 49: api.gold_store.v1.OrderDetail.vps_extra:type_name -> api.gold_store.v1.VPSExtra
	49,  // 50: api.gold_store.v1.OrderDetail.report_extra:type_name -> api.gold_store.v1.ReportExtra
	51,  // 51: api.gold_store.v1.OrderDetail.exhibition_extra:type_name -> api.gold_store.v1.ExhibitionExtra
	31,  // 52: api.gold_store.v1.OrderDetail.selected_specs:type_name -> api.gold_store.v1.SpecSelected
	2,   // 53: api.gold_store.v1.OrderDetail.payment_method_icon:type_name -> api.gold_store.v1.OrderPaymentMethodIcon
	90,  // 54: api.gold_store.v1.GetOrderLogisticsReply.address:type_name -> api.gold_store.v1.Address
	94,  // 55: api.gold_store.v1.GetOrderLogisticsReply.steps:type_name -> api.gold_store.v1.LogisticStep
	85,  // 56: api.gold_store.v1.GiftCard.image:type_name -> api.gold_store.v1.Image
	3,   // 57: api.gold_store.v1.GiftCard.status:type_name -> api.gold_store.v1.GiftCardStatus
	4,   // 58: api.gold_store.v1.GetUserGiftCardRequest.status:type_name -> api.gold_store.v1.UserGiftCardTabType
	62,  // 59: api.gold_store.v1.GetUserGiftCardReply.tabs:type_name -> api.gold_store.v1.GiftCardTab
	59,  // 60: api.gold_store.v1.GetUserGiftCardReply.gift_cards:type_name -> api.gold_store.v1.GiftCard
	4,   // 61: api.gold_store.v1.GiftCardTab.value:type_name -> api.gold_store.v1.UserGiftCardTabType
	85,  // 62: api.gold_store.v1.GiftCardRemindReply.image:type_name -> api.gold_store.v1.Image
	95,  // 63: api.gold_store.v1.QueryExpressInfoReply.status:type_name -> api.gold_store.v1.LogisticStepStatus
	67,  // 64: api.gold_store.v1.QueryExpressInfoReply.traces:type_name -> api.gold_store.v1.ExpressTrace
	95,  // 65: api.gold_store.v1.ExpressTrace.status:type_name -> api.gold_store.v1.LogisticStepStatus
	74,  // 66: api.gold_store.v1.QueryTrackReply.tracks:type_name -> api.gold_store.v1.TrackInfo
	75,  // 67: api.gold_store.v1.TrackInfo.tracks:type_name -> api.gold_store.v1.TrackDetail
	78,  // 68: api.gold_store.v1.QueryWaybillReply.waybills:type_name -> api.gold_store.v1.WaybillInfo
	80,  // 69: api.gold_store.v1.QueryFeeRequest.cargo:type_name -> api.gold_store.v1.CargoInfo
	82,  // 70: api.gold_store.v1.QueryFeeReply.fee:type_name -> api.gold_store.v1.FeeInfo
	96,  // 71: api.gold_store.v1.Service.Healthy:input_type -> common.EmptyRequest
	96,  // 72: api.gold_store.v1.Service.GoldStoreQuickAccess:input_type -> common.EmptyRequest
	8,   // 73: api.gold_store.v1.Service.MyGoldJump:input_type -> api.gold_store.v1.MyGoldJumpRequest
	13,  // 74: api.gold_store.v1.Service.GoodsTab:input_type -> api.gold_store.v1.GoodsTabRequest
	16,  // 75: api.gold_store.v1.Service.GoodsList:input_type -> api.gold_store.v1.GoodsListRequest
	18,  // 76: api.gold_store.v1.Service.GetGoodsDetail:input_type -> api.gold_store.v1.GoodsDetailRequest
	19,  // 77: api.gold_store.v1.Service.BestGoods:input_type -> api.gold_store.v1.BestGoodsRequest
	29,  // 78: api.gold_store.v1.Service.OrderTotalAmount:input_type -> api.gold_store.v1.OrderTotalAmountRequest
	32,  // 79: api.gold_store.v1.Service.PreCheck:input_type -> api.gold_store.v1.CreateOrderRequest
	32,  // 80: api.gold_store.v1.Service.CreateOrder:input_type -> api.gold_store.v1.CreateOrderRequest
	35,  // 81: api.gold_store.v1.Service.OrderFilter:input_type -> api.gold_store.v1.OrderFilterRequest
	96,  // 82: api.gold_store.v1.Service.GetOrderCount:input_type -> common.EmptyRequest
	42,  // 83: api.gold_store.v1.Service.GetOrderTab:input_type -> api.gold_store.v1.GetOrderTabRequest
	45,  // 84: api.gold_store.v1.Service.GetOrderList:input_type -> api.gold_store.v1.OrderListRequest
	47,  // 85: api.gold_store.v1.Service.GetOrderDetail:input_type -> api.gold_store.v1.OrderDetailRequest
	53,  // 86: api.gold_store.v1.Service.GetOrderLogistics:input_type -> api.gold_store.v1.OrderLogisticsRequest
	55,  // 87: api.gold_store.v1.Service.GetUserOrderCount:input_type -> api.gold_store.v1.GetUserOrderCountRequest
	57,  // 88: api.gold_store.v1.Service.ReportOrderPush:input_type -> api.gold_store.v1.ReportOrderPushRequest
	65,  // 89: api.gold_store.v1.Service.QueryExpressInfo:input_type -> api.gold_store.v1.QueryExpressInfoRequest
	68,  // 90: api.gold_store.v1.Service.ExpressPushCallback:input_type -> api.gold_store.v1.ExpressPushCallbackRequest
	97,  // 91: api.gold_store.v1.Service.GetSignAggregateInfo:input_type -> api.gold_store.v1.GetSignAggregateInfoRequest
	98,  // 92: api.gold_store.v1.Service.SignIn:input_type -> api.gold_store.v1.SignInRequest
	99,  // 93: api.gold_store.v1.Service.GetTaskList:input_type -> api.gold_store.v1.GetTaskListRequest
	100, // 94: api.gold_store.v1.Service.ReceiveTaskReward:input_type -> api.gold_store.v1.ReceiveTaskRewardRequest
	101, // 95: api.gold_store.v1.Service.ListRewardRecords:input_type -> api.gold_store.v1.ListRewardRecordsRequest
	102, // 96: api.gold_store.v1.Service.SendTaskEventToQueue:input_type -> api.gold_store.v1.TaskEventRequest
	70,  // 97: api.gold_store.v1.Service.QueryUserTaskFinished:input_type -> api.gold_store.v1.QueryUserTaskFinishedRequest
	96,  // 98: api.gold_store.v1.Service.GetCountryList:input_type -> common.EmptyRequest
	96,  // 99: api.gold_store.v1.Service.GetAddressList:input_type -> common.EmptyRequest
	90,  // 100: api.gold_store.v1.Service.AddAddress:input_type -> api.gold_store.v1.Address
	90,  // 101: api.gold_store.v1.Service.UpdateAddress:input_type -> api.gold_store.v1.Address
	27,  // 102: api.gold_store.v1.Service.GetAddressDetail:input_type -> api.gold_store.v1.GetAddressDetailRequest
	25,  // 103: api.gold_store.v1.Service.SetAddressDefault:input_type -> api.gold_store.v1.SetAddressDefaultRequest
	26,  // 104: api.gold_store.v1.Service.DeleteAddress:input_type -> api.gold_store.v1.DeleteAddressRequest
	96,  // 105: api.gold_store.v1.Service.GetAddressCount:input_type -> common.EmptyRequest
	96,  // 106: api.gold_store.v1.Service.GetOrderAddress:input_type -> common.EmptyRequest
	60,  // 107: api.gold_store.v1.Service.GetUserGiftCard:input_type -> api.gold_store.v1.GetUserGiftCardRequest
	63,  // 108: api.gold_store.v1.Service.GiftCardRemind:input_type -> api.gold_store.v1.GiftCardRemindRequest
	72,  // 109: api.gold_store.v1.Service.QueryTrack:input_type -> api.gold_store.v1.QueryTrackRequest
	76,  // 110: api.gold_store.v1.Service.QueryWaybill:input_type -> api.gold_store.v1.QueryWaybillRequest
	79,  // 111: api.gold_store.v1.Service.QueryFee:input_type -> api.gold_store.v1.QueryFeeRequest
	83,  // 112: api.gold_store.v1.Service.HandlePushNotification:input_type -> api.gold_store.v1.PushNotificationRequest
	103, // 113: api.gold_store.v1.Service.Healthy:output_type -> common.HealthyReply
	7,   // 114: api.gold_store.v1.Service.GoldStoreQuickAccess:output_type -> api.gold_store.v1.GoldStoreQuickAccessReply
	9,   // 115: api.gold_store.v1.Service.MyGoldJump:output_type -> api.gold_store.v1.MyGoldJumpReply
	15,  // 116: api.gold_store.v1.Service.GoodsTab:output_type -> api.gold_store.v1.GoodsTabReply
	17,  // 117: api.gold_store.v1.Service.GoodsList:output_type -> api.gold_store.v1.GoodsListReply
	12,  // 118: api.gold_store.v1.Service.GetGoodsDetail:output_type -> api.gold_store.v1.GoodsDetail
	20,  // 119: api.gold_store.v1.Service.BestGoods:output_type -> api.gold_store.v1.BestGoodsReply
	30,  // 120: api.gold_store.v1.Service.OrderTotalAmount:output_type -> api.gold_store.v1.OrderTotalAmountReply
	33,  // 121: api.gold_store.v1.Service.PreCheck:output_type -> api.gold_store.v1.PreCheckReply
	34,  // 122: api.gold_store.v1.Service.CreateOrder:output_type -> api.gold_store.v1.CreateOrderReply
	38,  // 123: api.gold_store.v1.Service.OrderFilter:output_type -> api.gold_store.v1.OrderFilterReply
	39,  // 124: api.gold_store.v1.Service.GetOrderCount:output_type -> api.gold_store.v1.OrderCountReply
	43,  // 125: api.gold_store.v1.Service.GetOrderTab:output_type -> api.gold_store.v1.OrderTabReply
	46,  // 126: api.gold_store.v1.Service.GetOrderList:output_type -> api.gold_store.v1.OrderListReply
	52,  // 127: api.gold_store.v1.Service.GetOrderDetail:output_type -> api.gold_store.v1.OrderDetail
	54,  // 128: api.gold_store.v1.Service.GetOrderLogistics:output_type -> api.gold_store.v1.GetOrderLogisticsReply
	56,  // 129: api.gold_store.v1.Service.GetUserOrderCount:output_type -> api.gold_store.v1.GetUserOrderCountReply
	58,  // 130: api.gold_store.v1.Service.ReportOrderPush:output_type -> api.gold_store.v1.ReportOrderPushReply
	66,  // 131: api.gold_store.v1.Service.QueryExpressInfo:output_type -> api.gold_store.v1.QueryExpressInfoReply
	69,  // 132: api.gold_store.v1.Service.ExpressPushCallback:output_type -> api.gold_store.v1.ExpressPushCallbackReply
	104, // 133: api.gold_store.v1.Service.GetSignAggregateInfo:output_type -> api.gold_store.v1.GetSignAggregateInfoReply
	105, // 134: api.gold_store.v1.Service.SignIn:output_type -> api.gold_store.v1.SignInReply
	106, // 135: api.gold_store.v1.Service.GetTaskList:output_type -> api.gold_store.v1.GetTaskListReply
	107, // 136: api.gold_store.v1.Service.ReceiveTaskReward:output_type -> api.gold_store.v1.ReceiveTaskRewardReply
	108, // 137: api.gold_store.v1.Service.ListRewardRecords:output_type -> api.gold_store.v1.ListRewardRecordsReply
	109, // 138: api.gold_store.v1.Service.SendTaskEventToQueue:output_type -> api.gold_store.v1.TaskEventReply
	71,  // 139: api.gold_store.v1.Service.QueryUserTaskFinished:output_type -> api.gold_store.v1.QueryUserTaskFinishedReply
	22,  // 140: api.gold_store.v1.Service.GetCountryList:output_type -> api.gold_store.v1.GetCountryListReply
	23,  // 141: api.gold_store.v1.Service.GetAddressList:output_type -> api.gold_store.v1.GetAddressListReply
	24,  // 142: api.gold_store.v1.Service.AddAddress:output_type -> api.gold_store.v1.AddAddressReply
	110, // 143: api.gold_store.v1.Service.UpdateAddress:output_type -> common.EmptyReply
	90,  // 144: api.gold_store.v1.Service.GetAddressDetail:output_type -> api.gold_store.v1.Address
	110, // 145: api.gold_store.v1.Service.SetAddressDefault:output_type -> common.EmptyReply
	110, // 146: api.gold_store.v1.Service.DeleteAddress:output_type -> common.EmptyReply
	28,  // 147: api.gold_store.v1.Service.GetAddressCount:output_type -> api.gold_store.v1.GetAddressCountReply
	90,  // 148: api.gold_store.v1.Service.GetOrderAddress:output_type -> api.gold_store.v1.Address
	61,  // 149: api.gold_store.v1.Service.GetUserGiftCard:output_type -> api.gold_store.v1.GetUserGiftCardReply
	64,  // 150: api.gold_store.v1.Service.GiftCardRemind:output_type -> api.gold_store.v1.GiftCardRemindReply
	73,  // 151: api.gold_store.v1.Service.QueryTrack:output_type -> api.gold_store.v1.QueryTrackReply
	77,  // 152: api.gold_store.v1.Service.QueryWaybill:output_type -> api.gold_store.v1.QueryWaybillReply
	81,  // 153: api.gold_store.v1.Service.QueryFee:output_type -> api.gold_store.v1.QueryFeeReply
	84,  // 154: api.gold_store.v1.Service.HandlePushNotification:output_type -> api.gold_store.v1.PushNotificationReply
	113, // [113:155] is the sub-list for method output_type
	71,  // [71:113] is the sub-list for method input_type
	71,  // [71:71] is the sub-list for extension type_name
	71,  // [71:71] is the sub-list for extension extendee
	0,   // [0:71] is the sub-list for field type_name
}

func init() { file_gold_store_v1_service_proto_init() }
func file_gold_store_v1_service_proto_init() {
	if File_gold_store_v1_service_proto != nil {
		return
	}
	file_gold_store_v1_models_proto_init()
	file_gold_store_v1_sign_proto_init()
	file_gold_store_v1_task_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_gold_store_v1_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuickAccessItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoldStoreQuickAccessReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyGoldJumpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MyGoldJumpReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsLabel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsTabRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsTab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsTabReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BestGoodsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BestGoodsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CountryInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCountryListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAddressReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetAddressDefaultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAddressRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAddressCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTotalAmountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTotalAmountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpecSelected); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreCheckReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOrderReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderFilterRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderFilterValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderFilterGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderFilterReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderTabRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderTabReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPSExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitionUser); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExhibitionExtra); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderLogisticsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrderLogisticsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserOrderCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserOrderCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportOrderPushRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportOrderPushReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGiftCardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGiftCardReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardTab); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardRemindRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GiftCardRemindReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryExpressInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryExpressInfoReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpressTrace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpressPushCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpressPushCallbackReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryUserTaskFinishedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryUserTaskFinishedReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryTrackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryTrackReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TrackDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryWaybillRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryWaybillReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WaybillInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryFeeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CargoInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryFeeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_gold_store_v1_service_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushNotificationReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_gold_store_v1_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   79,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gold_store_v1_service_proto_goTypes,
		DependencyIndexes: file_gold_store_v1_service_proto_depIdxs,
		EnumInfos:         file_gold_store_v1_service_proto_enumTypes,
		MessageInfos:      file_gold_store_v1_service_proto_msgTypes,
	}.Build()
	File_gold_store_v1_service_proto = out.File
	file_gold_store_v1_service_proto_rawDesc = nil
	file_gold_store_v1_service_proto_goTypes = nil
	file_gold_store_v1_service_proto_depIdxs = nil
}
