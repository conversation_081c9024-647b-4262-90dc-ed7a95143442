// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: gold_store/v1/sign.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSignServiceCreateSignConfig = "/api.gold_store.v1.SignService/CreateSignConfig"
const OperationSignServiceDeleteSignConfig = "/api.gold_store.v1.SignService/DeleteSignConfig"
const OperationSignServiceGetSignAggregateInfo = "/api.gold_store.v1.SignService/GetSignAggregateInfo"
const OperationSignServiceListSignConfig = "/api.gold_store.v1.SignService/ListSignConfig"
const OperationSignServiceSignIn = "/api.gold_store.v1.SignService/SignIn"
const OperationSignServiceUpdateSignConfig = "/api.gold_store.v1.SignService/UpdateSignConfig"

type SignServiceHTTPServer interface {
	// CreateSignConfig 后台：新增签到配置
	CreateSignConfig(context.Context, *CreateSignConfigRequest) (*CreateSignConfigReply, error)
	// DeleteSignConfig 后台：删除签到配置
	DeleteSignConfig(context.Context, *DeleteSignConfigRequest) (*DeleteSignConfigReply, error)
	// GetSignAggregateInfo 获取签到信息聚合
	GetSignAggregateInfo(context.Context, *GetSignAggregateInfoRequest) (*GetSignAggregateInfoReply, error)
	// ListSignConfig 后台：获取签到配置列表
	ListSignConfig(context.Context, *ListSignConfigRequest) (*ListSignConfigReply, error)
	// SignIn 执行签到
	SignIn(context.Context, *SignInRequest) (*SignInReply, error)
	// UpdateSignConfig 后台：修改签到配置
	UpdateSignConfig(context.Context, *UpdateSignConfigRequest) (*UpdateSignConfigReply, error)
}

func RegisterSignServiceHTTPServer(s *http.Server, srv SignServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/sign/aggregate-info", _SignService_GetSignAggregateInfo0_HTTP_Handler(srv))
	r.POST("/v1/sign/in", _SignService_SignIn0_HTTP_Handler(srv))
	r.GET("/v1/api/sign-configs", _SignService_ListSignConfig0_HTTP_Handler(srv))
	r.POST("/v1/api/sign-configs", _SignService_CreateSignConfig0_HTTP_Handler(srv))
	r.POST("/v1/api/sign-configs", _SignService_UpdateSignConfig0_HTTP_Handler(srv))
	r.POST("/v1/api/sign-configs", _SignService_DeleteSignConfig0_HTTP_Handler(srv))
}

func _SignService_GetSignAggregateInfo0_HTTP_Handler(srv SignServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSignAggregateInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSignServiceGetSignAggregateInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSignAggregateInfo(ctx, req.(*GetSignAggregateInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSignAggregateInfoReply)
		return ctx.Result(200, reply)
	}
}

func _SignService_SignIn0_HTTP_Handler(srv SignServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SignInRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSignServiceSignIn)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SignIn(ctx, req.(*SignInRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SignInReply)
		return ctx.Result(200, reply)
	}
}

func _SignService_ListSignConfig0_HTTP_Handler(srv SignServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSignConfigRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSignServiceListSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSignConfig(ctx, req.(*ListSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _SignService_CreateSignConfig0_HTTP_Handler(srv SignServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateSignConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSignServiceCreateSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateSignConfig(ctx, req.(*CreateSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _SignService_UpdateSignConfig0_HTTP_Handler(srv SignServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateSignConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSignServiceUpdateSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSignConfig(ctx, req.(*UpdateSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateSignConfigReply)
		return ctx.Result(200, reply)
	}
}

func _SignService_DeleteSignConfig0_HTTP_Handler(srv SignServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSignConfigRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSignServiceDeleteSignConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSignConfig(ctx, req.(*DeleteSignConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteSignConfigReply)
		return ctx.Result(200, reply)
	}
}

type SignServiceHTTPClient interface {
	CreateSignConfig(ctx context.Context, req *CreateSignConfigRequest, opts ...http.CallOption) (rsp *CreateSignConfigReply, err error)
	DeleteSignConfig(ctx context.Context, req *DeleteSignConfigRequest, opts ...http.CallOption) (rsp *DeleteSignConfigReply, err error)
	GetSignAggregateInfo(ctx context.Context, req *GetSignAggregateInfoRequest, opts ...http.CallOption) (rsp *GetSignAggregateInfoReply, err error)
	ListSignConfig(ctx context.Context, req *ListSignConfigRequest, opts ...http.CallOption) (rsp *ListSignConfigReply, err error)
	SignIn(ctx context.Context, req *SignInRequest, opts ...http.CallOption) (rsp *SignInReply, err error)
	UpdateSignConfig(ctx context.Context, req *UpdateSignConfigRequest, opts ...http.CallOption) (rsp *UpdateSignConfigReply, err error)
}

type SignServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewSignServiceHTTPClient(client *http.Client) SignServiceHTTPClient {
	return &SignServiceHTTPClientImpl{client}
}

func (c *SignServiceHTTPClientImpl) CreateSignConfig(ctx context.Context, in *CreateSignConfigRequest, opts ...http.CallOption) (*CreateSignConfigReply, error) {
	var out CreateSignConfigReply
	pattern := "/v1/api/sign-configs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSignServiceCreateSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SignServiceHTTPClientImpl) DeleteSignConfig(ctx context.Context, in *DeleteSignConfigRequest, opts ...http.CallOption) (*DeleteSignConfigReply, error) {
	var out DeleteSignConfigReply
	pattern := "/v1/api/sign-configs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSignServiceDeleteSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SignServiceHTTPClientImpl) GetSignAggregateInfo(ctx context.Context, in *GetSignAggregateInfoRequest, opts ...http.CallOption) (*GetSignAggregateInfoReply, error) {
	var out GetSignAggregateInfoReply
	pattern := "/v1/sign/aggregate-info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSignServiceGetSignAggregateInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SignServiceHTTPClientImpl) ListSignConfig(ctx context.Context, in *ListSignConfigRequest, opts ...http.CallOption) (*ListSignConfigReply, error) {
	var out ListSignConfigReply
	pattern := "/v1/api/sign-configs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSignServiceListSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SignServiceHTTPClientImpl) SignIn(ctx context.Context, in *SignInRequest, opts ...http.CallOption) (*SignInReply, error) {
	var out SignInReply
	pattern := "/v1/sign/in"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSignServiceSignIn))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SignServiceHTTPClientImpl) UpdateSignConfig(ctx context.Context, in *UpdateSignConfigRequest, opts ...http.CallOption) (*UpdateSignConfigReply, error) {
	var out UpdateSignConfigReply
	pattern := "/v1/api/sign-configs"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSignServiceUpdateSignConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
