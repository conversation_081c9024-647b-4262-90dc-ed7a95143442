// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: gold_store/v1/sign.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SignService_GetSignAggregateInfo_FullMethodName = "/api.gold_store.v1.SignService/GetSignAggregateInfo"
	SignService_SignIn_FullMethodName               = "/api.gold_store.v1.SignService/SignIn"
	SignService_ListSignConfig_FullMethodName       = "/api.gold_store.v1.SignService/ListSignConfig"
	SignService_CreateSignConfig_FullMethodName     = "/api.gold_store.v1.SignService/CreateSignConfig"
	SignService_UpdateSignConfig_FullMethodName     = "/api.gold_store.v1.SignService/UpdateSignConfig"
	SignService_DeleteSignConfig_FullMethodName     = "/api.gold_store.v1.SignService/DeleteSignConfig"
)

// SignServiceClient is the client API for SignService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SignServiceClient interface {
	// 获取签到信息聚合
	GetSignAggregateInfo(ctx context.Context, in *GetSignAggregateInfoRequest, opts ...grpc.CallOption) (*GetSignAggregateInfoReply, error)
	// 执行签到
	SignIn(ctx context.Context, in *SignInRequest, opts ...grpc.CallOption) (*SignInReply, error)
	// 后台：获取签到配置列表
	ListSignConfig(ctx context.Context, in *ListSignConfigRequest, opts ...grpc.CallOption) (*ListSignConfigReply, error)
	// 后台：新增签到配置
	CreateSignConfig(ctx context.Context, in *CreateSignConfigRequest, opts ...grpc.CallOption) (*CreateSignConfigReply, error)
	// 后台：修改签到配置
	UpdateSignConfig(ctx context.Context, in *UpdateSignConfigRequest, opts ...grpc.CallOption) (*UpdateSignConfigReply, error)
	// 后台：删除签到配置
	DeleteSignConfig(ctx context.Context, in *DeleteSignConfigRequest, opts ...grpc.CallOption) (*DeleteSignConfigReply, error)
}

type signServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSignServiceClient(cc grpc.ClientConnInterface) SignServiceClient {
	return &signServiceClient{cc}
}

func (c *signServiceClient) GetSignAggregateInfo(ctx context.Context, in *GetSignAggregateInfoRequest, opts ...grpc.CallOption) (*GetSignAggregateInfoReply, error) {
	out := new(GetSignAggregateInfoReply)
	err := c.cc.Invoke(ctx, SignService_GetSignAggregateInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signServiceClient) SignIn(ctx context.Context, in *SignInRequest, opts ...grpc.CallOption) (*SignInReply, error) {
	out := new(SignInReply)
	err := c.cc.Invoke(ctx, SignService_SignIn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signServiceClient) ListSignConfig(ctx context.Context, in *ListSignConfigRequest, opts ...grpc.CallOption) (*ListSignConfigReply, error) {
	out := new(ListSignConfigReply)
	err := c.cc.Invoke(ctx, SignService_ListSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signServiceClient) CreateSignConfig(ctx context.Context, in *CreateSignConfigRequest, opts ...grpc.CallOption) (*CreateSignConfigReply, error) {
	out := new(CreateSignConfigReply)
	err := c.cc.Invoke(ctx, SignService_CreateSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signServiceClient) UpdateSignConfig(ctx context.Context, in *UpdateSignConfigRequest, opts ...grpc.CallOption) (*UpdateSignConfigReply, error) {
	out := new(UpdateSignConfigReply)
	err := c.cc.Invoke(ctx, SignService_UpdateSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *signServiceClient) DeleteSignConfig(ctx context.Context, in *DeleteSignConfigRequest, opts ...grpc.CallOption) (*DeleteSignConfigReply, error) {
	out := new(DeleteSignConfigReply)
	err := c.cc.Invoke(ctx, SignService_DeleteSignConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SignServiceServer is the server API for SignService service.
// All implementations must embed UnimplementedSignServiceServer
// for forward compatibility
type SignServiceServer interface {
	// 获取签到信息聚合
	GetSignAggregateInfo(context.Context, *GetSignAggregateInfoRequest) (*GetSignAggregateInfoReply, error)
	// 执行签到
	SignIn(context.Context, *SignInRequest) (*SignInReply, error)
	// 后台：获取签到配置列表
	ListSignConfig(context.Context, *ListSignConfigRequest) (*ListSignConfigReply, error)
	// 后台：新增签到配置
	CreateSignConfig(context.Context, *CreateSignConfigRequest) (*CreateSignConfigReply, error)
	// 后台：修改签到配置
	UpdateSignConfig(context.Context, *UpdateSignConfigRequest) (*UpdateSignConfigReply, error)
	// 后台：删除签到配置
	DeleteSignConfig(context.Context, *DeleteSignConfigRequest) (*DeleteSignConfigReply, error)
	mustEmbedUnimplementedSignServiceServer()
}

// UnimplementedSignServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSignServiceServer struct {
}

func (UnimplementedSignServiceServer) GetSignAggregateInfo(context.Context, *GetSignAggregateInfoRequest) (*GetSignAggregateInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSignAggregateInfo not implemented")
}
func (UnimplementedSignServiceServer) SignIn(context.Context, *SignInRequest) (*SignInReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignIn not implemented")
}
func (UnimplementedSignServiceServer) ListSignConfig(context.Context, *ListSignConfigRequest) (*ListSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSignConfig not implemented")
}
func (UnimplementedSignServiceServer) CreateSignConfig(context.Context, *CreateSignConfigRequest) (*CreateSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSignConfig not implemented")
}
func (UnimplementedSignServiceServer) UpdateSignConfig(context.Context, *UpdateSignConfigRequest) (*UpdateSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSignConfig not implemented")
}
func (UnimplementedSignServiceServer) DeleteSignConfig(context.Context, *DeleteSignConfigRequest) (*DeleteSignConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSignConfig not implemented")
}
func (UnimplementedSignServiceServer) mustEmbedUnimplementedSignServiceServer() {}

// UnsafeSignServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SignServiceServer will
// result in compilation errors.
type UnsafeSignServiceServer interface {
	mustEmbedUnimplementedSignServiceServer()
}

func RegisterSignServiceServer(s grpc.ServiceRegistrar, srv SignServiceServer) {
	s.RegisterService(&SignService_ServiceDesc, srv)
}

func _SignService_GetSignAggregateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSignAggregateInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignServiceServer).GetSignAggregateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignService_GetSignAggregateInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignServiceServer).GetSignAggregateInfo(ctx, req.(*GetSignAggregateInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignService_SignIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignServiceServer).SignIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignService_SignIn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignServiceServer).SignIn(ctx, req.(*SignInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignService_ListSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignServiceServer).ListSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignService_ListSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignServiceServer).ListSignConfig(ctx, req.(*ListSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignService_CreateSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignServiceServer).CreateSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignService_CreateSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignServiceServer).CreateSignConfig(ctx, req.(*CreateSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignService_UpdateSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignServiceServer).UpdateSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignService_UpdateSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignServiceServer).UpdateSignConfig(ctx, req.(*UpdateSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SignService_DeleteSignConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSignConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SignServiceServer).DeleteSignConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SignService_DeleteSignConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SignServiceServer).DeleteSignConfig(ctx, req.(*DeleteSignConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SignService_ServiceDesc is the grpc.ServiceDesc for SignService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SignService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.gold_store.v1.SignService",
	HandlerType: (*SignServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSignAggregateInfo",
			Handler:    _SignService_GetSignAggregateInfo_Handler,
		},
		{
			MethodName: "SignIn",
			Handler:    _SignService_SignIn_Handler,
		},
		{
			MethodName: "ListSignConfig",
			Handler:    _SignService_ListSignConfig_Handler,
		},
		{
			MethodName: "CreateSignConfig",
			Handler:    _SignService_CreateSignConfig_Handler,
		},
		{
			MethodName: "UpdateSignConfig",
			Handler:    _SignService_UpdateSignConfig_Handler,
		},
		{
			MethodName: "DeleteSignConfig",
			Handler:    _SignService_DeleteSignConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gold_store/v1/sign.proto",
}
