// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: community/v1/wikicommunity.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetPostsReplyStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataUserId []string `protobuf:"bytes,1,rep,name=dataUserId,json=dataUserId,proto3" json:"dataUserId"`
}

func (x *GetPostsReplyStatusRequest) Reset() {
	*x = GetPostsReplyStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsReplyStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsReplyStatusRequest) ProtoMessage() {}

func (x *GetPostsReplyStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsReplyStatusRequest.ProtoReflect.Descriptor instead.
func (*GetPostsReplyStatusRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{0}
}

func (x *GetPostsReplyStatusRequest) GetDataUserId() []string {
	if x != nil {
		return x.DataUserId
	}
	return nil
}

type GetPostsReplyStatusReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetPostsReplyStatusReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetPostsReplyStatusReply) Reset() {
	*x = GetPostsReplyStatusReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsReplyStatusReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsReplyStatusReply) ProtoMessage() {}

func (x *GetPostsReplyStatusReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsReplyStatusReply.ProtoReflect.Descriptor instead.
func (*GetPostsReplyStatusReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{1}
}

func (x *GetPostsReplyStatusReply) GetList() []*GetPostsReplyStatusReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetPostsReplyStatusReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataUserId string `protobuf:"bytes,1,opt,name=dataUserId,json=dataUserId,proto3" json:"dataUserId"`
	Status     int32  `protobuf:"varint,2,opt,name=status,json=status,proto3" json:"status"` // 0 none   1Write 2 Read 3readWrite
}

func (x *GetPostsReplyStatusReplyItem) Reset() {
	*x = GetPostsReplyStatusReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsReplyStatusReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsReplyStatusReplyItem) ProtoMessage() {}

func (x *GetPostsReplyStatusReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsReplyStatusReplyItem.ProtoReflect.Descriptor instead.
func (*GetPostsReplyStatusReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{2}
}

func (x *GetPostsReplyStatusReplyItem) GetDataUserId() string {
	if x != nil {
		return x.DataUserId
	}
	return ""
}

func (x *GetPostsReplyStatusReplyItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

type GetUserPostsApplaudRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsIds []string `protobuf:"bytes,1,rep,name=postsIds,json=postsIds,proto3" json:"postsIds"`
}

func (x *GetUserPostsApplaudRequest) Reset() {
	*x = GetUserPostsApplaudRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPostsApplaudRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPostsApplaudRequest) ProtoMessage() {}

func (x *GetUserPostsApplaudRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPostsApplaudRequest.ProtoReflect.Descriptor instead.
func (*GetUserPostsApplaudRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserPostsApplaudRequest) GetPostsIds() []string {
	if x != nil {
		return x.PostsIds
	}
	return nil
}

type GetUserPostsApplaudReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserPostsApplaudReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserPostsApplaudReply) Reset() {
	*x = GetUserPostsApplaudReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPostsApplaudReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPostsApplaudReply) ProtoMessage() {}

func (x *GetUserPostsApplaudReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPostsApplaudReply.ProtoReflect.Descriptor instead.
func (*GetUserPostsApplaudReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{4}
}

func (x *GetUserPostsApplaudReply) GetList() []*GetUserPostsApplaudReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetUserPostsApplaudReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsId string `protobuf:"bytes,1,opt,name=postsId,json=postsId,proto3" json:"postsId"`
	Number  string `protobuf:"bytes,2,opt,name=Number,json=Number,proto3" json:"Number"`
}

func (x *GetUserPostsApplaudReplyItem) Reset() {
	*x = GetUserPostsApplaudReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserPostsApplaudReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPostsApplaudReplyItem) ProtoMessage() {}

func (x *GetUserPostsApplaudReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPostsApplaudReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserPostsApplaudReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserPostsApplaudReplyItem) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

func (x *GetUserPostsApplaudReplyItem) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

type GetUserViewHistoryPostsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserLoginId string `protobuf:"bytes,1,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	ReleaseType int32  `protobuf:"varint,2,opt,name=releaseType,json=releaseType,proto3" json:"releaseType"` //1 商业 2 动态
	PageIndex   int32  `protobuf:"varint,3,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize    int32  `protobuf:"varint,4,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserViewHistoryPostsRequest) Reset() {
	*x = GetUserViewHistoryPostsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserViewHistoryPostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserViewHistoryPostsRequest) ProtoMessage() {}

func (x *GetUserViewHistoryPostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserViewHistoryPostsRequest.ProtoReflect.Descriptor instead.
func (*GetUserViewHistoryPostsRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserViewHistoryPostsRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetUserViewHistoryPostsRequest) GetReleaseType() int32 {
	if x != nil {
		return x.ReleaseType
	}
	return 0
}

func (x *GetUserViewHistoryPostsRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserViewHistoryPostsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserViewHistoryPostsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserViewHistoryPostsItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserViewHistoryPostsReply) Reset() {
	*x = GetUserViewHistoryPostsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserViewHistoryPostsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserViewHistoryPostsReply) ProtoMessage() {}

func (x *GetUserViewHistoryPostsReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserViewHistoryPostsReply.ProtoReflect.Descriptor instead.
func (*GetUserViewHistoryPostsReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserViewHistoryPostsReply) GetList() []*GetUserViewHistoryPostsItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetUserViewHistoryPostsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataType    int32  `protobuf:"varint,1,opt,name=dataType,json=dataType,proto3" json:"dataType"` //    1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
	PostsId     string `protobuf:"bytes,2,opt,name=postsId,json=postsId,proto3" json:"postsId"`
	CollectTime int64  `protobuf:"varint,3,opt,name=collectTime,json=collectTime,proto3" json:"collectTime"` //收藏时间
}

func (x *GetUserViewHistoryPostsItem) Reset() {
	*x = GetUserViewHistoryPostsItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserViewHistoryPostsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserViewHistoryPostsItem) ProtoMessage() {}

func (x *GetUserViewHistoryPostsItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserViewHistoryPostsItem.ProtoReflect.Descriptor instead.
func (*GetUserViewHistoryPostsItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserViewHistoryPostsItem) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *GetUserViewHistoryPostsItem) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

func (x *GetUserViewHistoryPostsItem) GetCollectTime() int64 {
	if x != nil {
		return x.CollectTime
	}
	return 0
}

type GetPostsNumberRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`                //查看用户的userid
	UserLoginId string `protobuf:"bytes,2,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"` //用户登录Id 可以为空
}

func (x *GetPostsNumberRequest) Reset() {
	*x = GetPostsNumberRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsNumberRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsNumberRequest) ProtoMessage() {}

func (x *GetPostsNumberRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsNumberRequest.ProtoReflect.Descriptor instead.
func (*GetPostsNumberRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{9}
}

func (x *GetPostsNumberRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetPostsNumberRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

type GetPostsNumberReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BusinessNumber string `protobuf:"bytes,1,opt,name=businessNumber,json=businessNumber,proto3" json:"businessNumber"` //商业数量
	DailyNumber    string `protobuf:"bytes,2,opt,name=dailyNumber,json=dailyNumber,proto3" json:"dailyNumber"`          //动态数量
}

func (x *GetPostsNumberReply) Reset() {
	*x = GetPostsNumberReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsNumberReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsNumberReply) ProtoMessage() {}

func (x *GetPostsNumberReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsNumberReply.ProtoReflect.Descriptor instead.
func (*GetPostsNumberReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{10}
}

func (x *GetPostsNumberReply) GetBusinessNumber() string {
	if x != nil {
		return x.BusinessNumber
	}
	return ""
}

func (x *GetPostsNumberReply) GetDailyNumber() string {
	if x != nil {
		return x.DailyNumber
	}
	return ""
}

// 通过帖子Id获取用户
type GetUserIdByPostsIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsId string `protobuf:"bytes,1,opt,name=postsId,json=postsId,proto3" json:"postsId"`
}

func (x *GetUserIdByPostsIdRequest) Reset() {
	*x = GetUserIdByPostsIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserIdByPostsIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserIdByPostsIdRequest) ProtoMessage() {}

func (x *GetUserIdByPostsIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserIdByPostsIdRequest.ProtoReflect.Descriptor instead.
func (*GetUserIdByPostsIdRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{11}
}

func (x *GetUserIdByPostsIdRequest) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

type GetUserIdByPostsIdReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserIdByPostsIdReply) Reset() {
	*x = GetUserIdByPostsIdReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserIdByPostsIdReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserIdByPostsIdReply) ProtoMessage() {}

func (x *GetUserIdByPostsIdReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserIdByPostsIdReply.ProtoReflect.Descriptor instead.
func (*GetUserIdByPostsIdReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{12}
}

func (x *GetUserIdByPostsIdReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetPersonHomePostsPageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex   int32  `protobuf:"varint,1,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize    int32  `protobuf:"varint,2,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
	ReleaseType int32  `protobuf:"varint,3,opt,name=releaseType,json=releaseType,proto3" json:"releaseType"`
	UserLoginId string `protobuf:"bytes,4,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	UserId      string `protobuf:"bytes,5,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetPersonHomePostsPageListRequest) Reset() {
	*x = GetPersonHomePostsPageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPersonHomePostsPageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersonHomePostsPageListRequest) ProtoMessage() {}

func (x *GetPersonHomePostsPageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersonHomePostsPageListRequest.ProtoReflect.Descriptor instead.
func (*GetPersonHomePostsPageListRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{13}
}

func (x *GetPersonHomePostsPageListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetPersonHomePostsPageListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetPersonHomePostsPageListRequest) GetReleaseType() int32 {
	if x != nil {
		return x.ReleaseType
	}
	return 0
}

func (x *GetPersonHomePostsPageListRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetPersonHomePostsPageListRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetPersonHomePostsPageListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetPostsReplyItem `protobuf:"bytes,1,rep,name=List,json=List,proto3" json:"List"`
}

func (x *GetPersonHomePostsPageListReply) Reset() {
	*x = GetPersonHomePostsPageListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPersonHomePostsPageListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersonHomePostsPageListReply) ProtoMessage() {}

func (x *GetPersonHomePostsPageListReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersonHomePostsPageListReply.ProtoReflect.Descriptor instead.
func (*GetPersonHomePostsPageListReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{14}
}

func (x *GetPersonHomePostsPageListReply) GetList() []*GetPostsReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

// 用户收藏帖子请求
type GetUserCollectPostsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserLoginId string `protobuf:"bytes,1,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	ReleaseType int32  `protobuf:"varint,2,opt,name=releaseType,json=releaseType,proto3" json:"releaseType"` //1 商业 2 动态
	PageIndex   int32  `protobuf:"varint,3,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize    int32  `protobuf:"varint,4,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserCollectPostsRequest) Reset() {
	*x = GetUserCollectPostsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCollectPostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCollectPostsRequest) ProtoMessage() {}

func (x *GetUserCollectPostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCollectPostsRequest.ProtoReflect.Descriptor instead.
func (*GetUserCollectPostsRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserCollectPostsRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetUserCollectPostsRequest) GetReleaseType() int32 {
	if x != nil {
		return x.ReleaseType
	}
	return 0
}

func (x *GetUserCollectPostsRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserCollectPostsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserCollectPostsReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataType     int32  `protobuf:"varint,1,opt,name=dataType,json=dataType,proto3" json:"dataType"` //    1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
	PostsId      string `protobuf:"bytes,2,opt,name=postsId,json=postsId,proto3" json:"postsId"`
	CollectTime  int64  `protobuf:"varint,3,opt,name=collectTime,json=collectTime,proto3" json:"collectTime"`   //收藏时间
	PublicUserId string `protobuf:"bytes,4,opt,name=publicUserId,json=publicUserId,proto3" json:"publicUserId"` //发布用户userid
}

func (x *GetUserCollectPostsReplyItem) Reset() {
	*x = GetUserCollectPostsReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCollectPostsReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCollectPostsReplyItem) ProtoMessage() {}

func (x *GetUserCollectPostsReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCollectPostsReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserCollectPostsReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{16}
}

func (x *GetUserCollectPostsReplyItem) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *GetUserCollectPostsReplyItem) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

func (x *GetUserCollectPostsReplyItem) GetCollectTime() int64 {
	if x != nil {
		return x.CollectTime
	}
	return 0
}

func (x *GetUserCollectPostsReplyItem) GetPublicUserId() string {
	if x != nil {
		return x.PublicUserId
	}
	return ""
}

// 用户收藏帖子返回
type GetUserCollectPostsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*GetUserCollectPostsReplyItem `protobuf:"bytes,1,rep,name=List,json=List,proto3" json:"List"` //返回结果
	Total int32                           `protobuf:"varint,2,opt,name=Total,json=Total,proto3" json:"Total"`
}

func (x *GetUserCollectPostsReply) Reset() {
	*x = GetUserCollectPostsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserCollectPostsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCollectPostsReply) ProtoMessage() {}

func (x *GetUserCollectPostsReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCollectPostsReply.ProtoReflect.Descriptor instead.
func (*GetUserCollectPostsReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserCollectPostsReply) GetList() []*GetUserCollectPostsReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetUserCollectPostsReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取商业或者评论数据
type GetPostsOrCommentGradesInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsIds        []string `protobuf:"bytes,1,rep,name=PostsIds,json=PostsIds,proto3" json:"PostsIds"`                      //商业Id
	CommentGradeIds []string `protobuf:"bytes,2,rep,name=CommentGradeIds,json=CommentGradeIds,proto3" json:"CommentGradeIds"` //评价Id
	UserLoginId     string   `protobuf:"bytes,3,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`             //登录用户Id
	IsDoTitle       bool     `protobuf:"varint,4,opt,name=IsDoTitle,json=IsDoTitle,proto3" json:"IsDoTitle"`                  //是否处理title
}

func (x *GetPostsOrCommentGradesInfoRequest) Reset() {
	*x = GetPostsOrCommentGradesInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsOrCommentGradesInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsOrCommentGradesInfoRequest) ProtoMessage() {}

func (x *GetPostsOrCommentGradesInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsOrCommentGradesInfoRequest.ProtoReflect.Descriptor instead.
func (*GetPostsOrCommentGradesInfoRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{18}
}

func (x *GetPostsOrCommentGradesInfoRequest) GetPostsIds() []string {
	if x != nil {
		return x.PostsIds
	}
	return nil
}

func (x *GetPostsOrCommentGradesInfoRequest) GetCommentGradeIds() []string {
	if x != nil {
		return x.CommentGradeIds
	}
	return nil
}

func (x *GetPostsOrCommentGradesInfoRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetPostsOrCommentGradesInfoRequest) GetIsDoTitle() bool {
	if x != nil {
		return x.IsDoTitle
	}
	return false
}

type GetSinglePostsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsId     string `protobuf:"bytes,1,opt,name=postsId,json=postsId,proto3" json:"postsId"`
	UserLoginId string `protobuf:"bytes,2,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	PostsUserId string `protobuf:"bytes,3,opt,name=postsUserId,json=postsUserId,proto3" json:"postsUserId"`
}

func (x *GetSinglePostsRequest) Reset() {
	*x = GetSinglePostsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSinglePostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSinglePostsRequest) ProtoMessage() {}

func (x *GetSinglePostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSinglePostsRequest.ProtoReflect.Descriptor instead.
func (*GetSinglePostsRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{19}
}

func (x *GetSinglePostsRequest) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

func (x *GetSinglePostsRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetSinglePostsRequest) GetPostsUserId() string {
	if x != nil {
		return x.PostsUserId
	}
	return ""
}

type GetPostsApplaudRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserLoginId string   `protobuf:"bytes,1,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"` //登录用户Id
	PostsIds    []string `protobuf:"bytes,2,rep,name=postsIds,json=postsIds,proto3" json:"postsIds"`          //帖子集合
}

func (x *GetPostsApplaudRequest) Reset() {
	*x = GetPostsApplaudRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsApplaudRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsApplaudRequest) ProtoMessage() {}

func (x *GetPostsApplaudRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsApplaudRequest.ProtoReflect.Descriptor instead.
func (*GetPostsApplaudRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{20}
}

func (x *GetPostsApplaudRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetPostsApplaudRequest) GetPostsIds() []string {
	if x != nil {
		return x.PostsIds
	}
	return nil
}

type GetPostsApplaudReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsId []string `protobuf:"bytes,1,rep,name=postsId,json=postsId,proto3" json:"postsId"`
}

func (x *GetPostsApplaudReply) Reset() {
	*x = GetPostsApplaudReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsApplaudReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsApplaudReply) ProtoMessage() {}

func (x *GetPostsApplaudReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsApplaudReply.ProtoReflect.Descriptor instead.
func (*GetPostsApplaudReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{21}
}

func (x *GetPostsApplaudReply) GetPostsId() []string {
	if x != nil {
		return x.PostsId
	}
	return nil
}

type GetPostsApplaudAndCollectReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Applauds       []string                               `protobuf:"bytes,1,rep,name=Applauds,json=Applauds,proto3" json:"Applauds"`
	Collects       []string                               `protobuf:"bytes,2,rep,name=Collects,json=Collects,proto3" json:"Collects"`
	ApplaudsCounts []*GetPostsApplaudAndCollectReplyCount `protobuf:"bytes,3,rep,name=ApplaudsCounts,json=ApplaudsCounts,proto3" json:"ApplaudsCounts"`
}

func (x *GetPostsApplaudAndCollectReply) Reset() {
	*x = GetPostsApplaudAndCollectReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsApplaudAndCollectReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsApplaudAndCollectReply) ProtoMessage() {}

func (x *GetPostsApplaudAndCollectReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsApplaudAndCollectReply.ProtoReflect.Descriptor instead.
func (*GetPostsApplaudAndCollectReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{22}
}

func (x *GetPostsApplaudAndCollectReply) GetApplauds() []string {
	if x != nil {
		return x.Applauds
	}
	return nil
}

func (x *GetPostsApplaudAndCollectReply) GetCollects() []string {
	if x != nil {
		return x.Collects
	}
	return nil
}

func (x *GetPostsApplaudAndCollectReply) GetApplaudsCounts() []*GetPostsApplaudAndCollectReplyCount {
	if x != nil {
		return x.ApplaudsCounts
	}
	return nil
}

type GetPostsApplaudAndCollectReplyCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsId             string `protobuf:"bytes,1,opt,name=PostsId,json=PostsId,proto3" json:"PostsId"`
	ApplaudNumber       int32  `protobuf:"varint,2,opt,name=ApplaudNumber,json=ApplaudNumber,proto3" json:"ApplaudNumber"`                    //点赞数量
	CommentNumber       int32  `protobuf:"varint,3,opt,name=CommentNumber,json=CommentNumber,proto3" json:"CommentNumber"`                    //评论数量
	ShowApplaudNumber   string `protobuf:"bytes,4,opt,name=ShowApplaudNumber,json=ShowApplaudNumber,proto3" json:"ShowApplaudNumber"`         //展示点赞数量
	ShowCommentNumber   string `protobuf:"bytes,5,opt,name=ShowCommentNumber,json=ShowCommentNumber,proto3" json:"ShowCommentNumber"`         //展示评论数量
	CollectNumber       int32  `protobuf:"varint,6,opt,name=CollectNumber,json=CollectNumber,proto3" json:"CollectNumber"`                    //收藏数量
	ShowCollectNumber   string `protobuf:"bytes,7,opt,name=ShowCollectNumber,json=ShowCollectNumber,proto3" json:"ShowCollectNumber"`         //展示收藏数量
	IsShowApplaudNumber bool   `protobuf:"varint,8,opt,name=isShowApplaudNumber,json=isShowApplaudNumber,proto3" json:"isShowApplaudNumber"`  //是否展示点赞
	IsShowCommentNumber bool   `protobuf:"varint,9,opt,name=isShowCommentNumber,json=isShowCommentNumber,proto3" json:"isShowCommentNumber"`  //是否展示评论
	IsShowCollectNumber bool   `protobuf:"varint,10,opt,name=isShowCollectNumber,json=isShowCollectNumber,proto3" json:"isShowCollectNumber"` //是否展示点赞
	IsShowPlayTimes     bool   `protobuf:"varint,11,opt,name=isShowPlayTimes,json=isShowPlayTimes,proto3" json:"isShowPlayTimes"`             //是否展示视频播放数量
	PlayTimes           int32  `protobuf:"varint,12,opt,name=playTimes,json=playTimes,proto3" json:"playTimes"`
	ShowPlayTimes       string `protobuf:"bytes,13,opt,name=showPlayTimes,json=showPlayTimes,proto3" json:"showPlayTimes"`
}

func (x *GetPostsApplaudAndCollectReplyCount) Reset() {
	*x = GetPostsApplaudAndCollectReplyCount{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsApplaudAndCollectReplyCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsApplaudAndCollectReplyCount) ProtoMessage() {}

func (x *GetPostsApplaudAndCollectReplyCount) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsApplaudAndCollectReplyCount.ProtoReflect.Descriptor instead.
func (*GetPostsApplaudAndCollectReplyCount) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{23}
}

func (x *GetPostsApplaudAndCollectReplyCount) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

func (x *GetPostsApplaudAndCollectReplyCount) GetApplaudNumber() int32 {
	if x != nil {
		return x.ApplaudNumber
	}
	return 0
}

func (x *GetPostsApplaudAndCollectReplyCount) GetCommentNumber() int32 {
	if x != nil {
		return x.CommentNumber
	}
	return 0
}

func (x *GetPostsApplaudAndCollectReplyCount) GetShowApplaudNumber() string {
	if x != nil {
		return x.ShowApplaudNumber
	}
	return ""
}

func (x *GetPostsApplaudAndCollectReplyCount) GetShowCommentNumber() string {
	if x != nil {
		return x.ShowCommentNumber
	}
	return ""
}

func (x *GetPostsApplaudAndCollectReplyCount) GetCollectNumber() int32 {
	if x != nil {
		return x.CollectNumber
	}
	return 0
}

func (x *GetPostsApplaudAndCollectReplyCount) GetShowCollectNumber() string {
	if x != nil {
		return x.ShowCollectNumber
	}
	return ""
}

func (x *GetPostsApplaudAndCollectReplyCount) GetIsShowApplaudNumber() bool {
	if x != nil {
		return x.IsShowApplaudNumber
	}
	return false
}

func (x *GetPostsApplaudAndCollectReplyCount) GetIsShowCommentNumber() bool {
	if x != nil {
		return x.IsShowCommentNumber
	}
	return false
}

func (x *GetPostsApplaudAndCollectReplyCount) GetIsShowCollectNumber() bool {
	if x != nil {
		return x.IsShowCollectNumber
	}
	return false
}

func (x *GetPostsApplaudAndCollectReplyCount) GetIsShowPlayTimes() bool {
	if x != nil {
		return x.IsShowPlayTimes
	}
	return false
}

func (x *GetPostsApplaudAndCollectReplyCount) GetPlayTimes() int32 {
	if x != nil {
		return x.PlayTimes
	}
	return 0
}

func (x *GetPostsApplaudAndCollectReplyCount) GetShowPlayTimes() string {
	if x != nil {
		return x.ShowPlayTimes
	}
	return ""
}

type GetPostsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GetPostsReplyItem `protobuf:"bytes,1,rep,name=Items,json=Items,proto3" json:"Items"`
}

func (x *GetPostsReply) Reset() {
	*x = GetPostsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsReply) ProtoMessage() {}

func (x *GetPostsReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsReply.ProtoReflect.Descriptor instead.
func (*GetPostsReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{24}
}

func (x *GetPostsReply) GetItems() []*GetPostsReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetPostsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 登录用户id
	UserLoginId            string   `protobuf:"bytes,1,opt,name=userLoginId,json=userLoginId,proto3" json:"userLoginId"`
	PostsIds               []string `protobuf:"bytes,2,rep,name=postsIds,json=postsIds,proto3" json:"postsIds"`
	IsShowLoginUserNoAudit bool     `protobuf:"varint,3,opt,name=isShowLoginUserNoAudit,json=isShowLoginUserNoAudit,proto3" json:"isShowLoginUserNoAudit"` //是否显示未审核的用户数据
}

func (x *GetPostsRequest) Reset() {
	*x = GetPostsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsRequest) ProtoMessage() {}

func (x *GetPostsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsRequest.ProtoReflect.Descriptor instead.
func (*GetPostsRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{25}
}

func (x *GetPostsRequest) GetUserLoginId() string {
	if x != nil {
		return x.UserLoginId
	}
	return ""
}

func (x *GetPostsRequest) GetPostsIds() []string {
	if x != nil {
		return x.PostsIds
	}
	return nil
}

func (x *GetPostsRequest) GetIsShowLoginUserNoAudit() bool {
	if x != nil {
		return x.IsShowLoginUserNoAudit
	}
	return false
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List   string `protobuf:"bytes,1,opt,name=list,json=list,proto3" json:"list"`
	Detail string `protobuf:"bytes,2,opt,name=detail,json=detail,proto3" json:"detail"`
	Url    string `protobuf:"bytes,3,opt,name=url,json=url,proto3" json:"url"`
	Width  int32  `protobuf:"varint,4,opt,name=width,json=width,proto3" json:"width"`
	Height int32  `protobuf:"varint,5,opt,name=height,json=height,proto3" json:"height"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{26}
}

func (x *Image) GetList() string {
	if x != nil {
		return x.List
	}
	return ""
}

func (x *Image) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *Image) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Image) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *Image) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type PostsSign struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsShow  int32  `protobuf:"varint,1,opt,name=IsShow,json=IsShow,proto3" json:"IsShow"`
	BgColor string `protobuf:"bytes,2,opt,name=BgColor,json=BgColor,proto3" json:"BgColor"`
	Word    string `protobuf:"bytes,3,opt,name=Word,json=Word,proto3" json:"Word"`
	Icon    string `protobuf:"bytes,4,opt,name=Icon,json=Icon,proto3" json:"Icon"`
	Color   string `protobuf:"bytes,5,opt,name=Color,json=Color,proto3" json:"Color"`
}

func (x *PostsSign) Reset() {
	*x = PostsSign{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostsSign) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostsSign) ProtoMessage() {}

func (x *PostsSign) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostsSign.ProtoReflect.Descriptor instead.
func (*PostsSign) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{27}
}

func (x *PostsSign) GetIsShow() int32 {
	if x != nil {
		return x.IsShow
	}
	return 0
}

func (x *PostsSign) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *PostsSign) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

func (x *PostsSign) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *PostsSign) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type PostsTopicItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   int32  `protobuf:"varint,1,opt,name=type,json=type,proto3" json:"type"` //// 1 @ 2 话题
	Id     string `protobuf:"bytes,2,opt,name=id,json=id,proto3" json:"id"`
	Name   string `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Enable bool   `protobuf:"varint,4,opt,name=enable,json=enable,proto3" json:"enable"`
}

func (x *PostsTopicItems) Reset() {
	*x = PostsTopicItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostsTopicItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostsTopicItems) ProtoMessage() {}

func (x *PostsTopicItems) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostsTopicItems.ProtoReflect.Descriptor instead.
func (*PostsTopicItems) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{28}
}

func (x *PostsTopicItems) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *PostsTopicItems) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PostsTopicItems) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PostsTopicItems) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type GetPostsReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsId          string             `protobuf:"bytes,1,opt,name=postsId,json=postsId,proto3" json:"postsId"`
	ReleaseType      int32              `protobuf:"varint,2,opt,name=releaseType,json=releaseType,proto3" json:"releaseType"` //类型 1服务 2动态
	PublicTime       int64              `protobuf:"varint,3,opt,name=publicTime,json=publicTime,proto3" json:"publicTime"`    //展示时间
	Title            string             `protobuf:"bytes,4,opt,name=title,json=title,proto3" json:"title"`
	TitleNew         string             `protobuf:"bytes,5,opt,name=titleNew,json=titleNew,proto3" json:"titleNew"`                          //没有处理的标题
	Content          string             `protobuf:"bytes,6,opt,name=content,json=content,proto3" json:"content"`                             //
	ContentNew       string             `protobuf:"bytes,7,opt,name=contentNew,json=contentNew,proto3" json:"contentNew"`                    // 内容新 逻辑处理没有处理的内容
	RefusalReason    string             `protobuf:"bytes,8,opt,name=refusalReason,json=refusalReason,proto3" json:"refusalReason"`           //拒绝理由
	Images           []*Image           `protobuf:"bytes,9,rep,name=images,json=images,proto3" json:"images"`                                //
	PostsCode        string             `protobuf:"bytes,10,opt,name=postsCode,json=postsCode,proto3" json:"postsCode"`                      //动态code
	ApplaudCount     int32              `protobuf:"varint,11,opt,name=applaudCount,json=applaudCount,proto3" json:"applaudCount"`            //点赞数量
	ShowApplaudCount string             `protobuf:"bytes,12,opt,name=showApplaudCount,json=showApplaudCount,proto3" json:"showApplaudCount"` //展示点赞数量
	CollectCount     int32              `protobuf:"varint,13,opt,name=collectCount,json=collectCount,proto3" json:"collectCount"`            //收藏数量
	IsCollect        bool               `protobuf:"varint,14,opt,name=isCollect,json=isCollect,proto3" json:"isCollect"`                     //是否收藏
	ForwardCount     int32              `protobuf:"varint,15,opt,name=forwardCount,json=forwardCount,proto3" json:"forwardCount"`            //分享数量;
	ShowForwardCount string             `protobuf:"bytes,16,opt,name=showForwardCount,json=showForwardCount,proto3" json:"showForwardCount"` //显示分享数量
	ReplyCount       int32              `protobuf:"varint,17,opt,name=replyCount,json=replyCount,proto3" json:"replyCount"`                  //原始头像
	ShowReplyCount   string             `protobuf:"bytes,18,opt,name=showReplyCount,json=showReplyCount,proto3" json:"showReplyCount"`       //显示评论数量
	IsApplaud        bool               `protobuf:"varint,19,opt,name=isApplaud,json=isApplaud,proto3" json:"isApplaud"`                     //是否点赞
	Theme            string             `protobuf:"bytes,20,opt,name=theme,json=theme,proto3" json:"theme"`                                  // 服务类型
	ThemeCode        string             `protobuf:"bytes,21,opt,name=themeCode,json=themeCode,proto3" json:"themeCode"`                      //服务code
	ThemeColor       string             `protobuf:"bytes,22,opt,name=themeColor,json=themeColor,proto3" json:"themeColor"`                   //服务颜色
	ShareUrl         string             `protobuf:"bytes,23,opt,name=shareUrl,json=shareUrl,proto3" json:"shareUrl"`                         //分享url
	Sign             *PostsSign         `protobuf:"bytes,24,opt,name=Sign,json=Sign,proto3" json:"Sign"`
	UserId           string             `protobuf:"bytes,25,opt,name=userId,json=userId,proto3" json:"userId"`
	CountryName      string             `protobuf:"bytes,26,opt,name=CountryName,json=CountryName,proto3" json:"CountryName"`              //国家名称
	ViewCount        string             `protobuf:"bytes,27,opt,name=ViewCount,json=ViewCount,proto3" json:"ViewCount"`                    //查看数量
	IsShowViewCount  bool               `protobuf:"varint,28,opt,name=IsShowViewCount,json=IsShowViewCount,proto3" json:"IsShowViewCount"` //是否显示访问数量
	EnterpriseCode   string             `protobuf:"bytes,29,opt,name=enterpriseCode,json=enterpriseCode,proto3" json:"enterpriseCode"`     //管理企业code
	Grade            int32              `protobuf:"varint,30,opt,name=Grade,json=Grade,proto3" json:"Grade"`                               //评价等级 1好评 2中评 3 曝光
	DataType         int32              `protobuf:"varint,41,opt,name=dataType,json=dataType,proto3" json:"dataType"`                      //  1:article(文章) 2:exposure(曝光) 3:discover(发现) 4:trader(交易商) 5:survey(实勘) 6:mediate(调解) 7:flash(快讯) 8:disclosure(披露) 9:comment(评价)
	ContentLanguage  string             `protobuf:"bytes,42,opt,name=contentLanguage,json=contentLanguage,proto3" json:"contentLanguage"`
	PostsTopicItems  []*PostsTopicItems `protobuf:"bytes,43,rep,name=postsTopicItems,json=postsTopicItems,proto3" json:"postsTopicItems"`
	IsTop            bool               `protobuf:"varint,44,opt,name=isTop,json=isTop,proto3" json:"isTop"`               //是否置顶
	TopContent       string             `protobuf:"bytes,45,opt,name=topContent,json=topContent,proto3" json:"topContent"` //置顶文字
	TopColor         string             `protobuf:"bytes,46,opt,name=topColor,json=topColor,proto3" json:"topColor"`       //置顶颜色 置顶background: #FFFFFF;
	TopBgColor       string             `protobuf:"bytes,47,opt,name=topBgColor,json=topBgColor,proto3" json:"topBgColor"` //置顶背景颜色 background: #4E5969 80;
	VodFileId        string             `protobuf:"bytes,48,opt,name=vodFileId,json=vodFileId,proto3" json:"vodFileId"`    //视频地址
	HasVideo         bool               `protobuf:"varint,49,opt,name=hasVideo,json=hasVideo,proto3" json:"hasVideo"`      //是否有视频
}

func (x *GetPostsReplyItem) Reset() {
	*x = GetPostsReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPostsReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostsReplyItem) ProtoMessage() {}

func (x *GetPostsReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostsReplyItem.ProtoReflect.Descriptor instead.
func (*GetPostsReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{29}
}

func (x *GetPostsReplyItem) GetPostsId() string {
	if x != nil {
		return x.PostsId
	}
	return ""
}

func (x *GetPostsReplyItem) GetReleaseType() int32 {
	if x != nil {
		return x.ReleaseType
	}
	return 0
}

func (x *GetPostsReplyItem) GetPublicTime() int64 {
	if x != nil {
		return x.PublicTime
	}
	return 0
}

func (x *GetPostsReplyItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetPostsReplyItem) GetTitleNew() string {
	if x != nil {
		return x.TitleNew
	}
	return ""
}

func (x *GetPostsReplyItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *GetPostsReplyItem) GetContentNew() string {
	if x != nil {
		return x.ContentNew
	}
	return ""
}

func (x *GetPostsReplyItem) GetRefusalReason() string {
	if x != nil {
		return x.RefusalReason
	}
	return ""
}

func (x *GetPostsReplyItem) GetImages() []*Image {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *GetPostsReplyItem) GetPostsCode() string {
	if x != nil {
		return x.PostsCode
	}
	return ""
}

func (x *GetPostsReplyItem) GetApplaudCount() int32 {
	if x != nil {
		return x.ApplaudCount
	}
	return 0
}

func (x *GetPostsReplyItem) GetShowApplaudCount() string {
	if x != nil {
		return x.ShowApplaudCount
	}
	return ""
}

func (x *GetPostsReplyItem) GetCollectCount() int32 {
	if x != nil {
		return x.CollectCount
	}
	return 0
}

func (x *GetPostsReplyItem) GetIsCollect() bool {
	if x != nil {
		return x.IsCollect
	}
	return false
}

func (x *GetPostsReplyItem) GetForwardCount() int32 {
	if x != nil {
		return x.ForwardCount
	}
	return 0
}

func (x *GetPostsReplyItem) GetShowForwardCount() string {
	if x != nil {
		return x.ShowForwardCount
	}
	return ""
}

func (x *GetPostsReplyItem) GetReplyCount() int32 {
	if x != nil {
		return x.ReplyCount
	}
	return 0
}

func (x *GetPostsReplyItem) GetShowReplyCount() string {
	if x != nil {
		return x.ShowReplyCount
	}
	return ""
}

func (x *GetPostsReplyItem) GetIsApplaud() bool {
	if x != nil {
		return x.IsApplaud
	}
	return false
}

func (x *GetPostsReplyItem) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

func (x *GetPostsReplyItem) GetThemeCode() string {
	if x != nil {
		return x.ThemeCode
	}
	return ""
}

func (x *GetPostsReplyItem) GetThemeColor() string {
	if x != nil {
		return x.ThemeColor
	}
	return ""
}

func (x *GetPostsReplyItem) GetShareUrl() string {
	if x != nil {
		return x.ShareUrl
	}
	return ""
}

func (x *GetPostsReplyItem) GetSign() *PostsSign {
	if x != nil {
		return x.Sign
	}
	return nil
}

func (x *GetPostsReplyItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetPostsReplyItem) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *GetPostsReplyItem) GetViewCount() string {
	if x != nil {
		return x.ViewCount
	}
	return ""
}

func (x *GetPostsReplyItem) GetIsShowViewCount() bool {
	if x != nil {
		return x.IsShowViewCount
	}
	return false
}

func (x *GetPostsReplyItem) GetEnterpriseCode() string {
	if x != nil {
		return x.EnterpriseCode
	}
	return ""
}

func (x *GetPostsReplyItem) GetGrade() int32 {
	if x != nil {
		return x.Grade
	}
	return 0
}

func (x *GetPostsReplyItem) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *GetPostsReplyItem) GetContentLanguage() string {
	if x != nil {
		return x.ContentLanguage
	}
	return ""
}

func (x *GetPostsReplyItem) GetPostsTopicItems() []*PostsTopicItems {
	if x != nil {
		return x.PostsTopicItems
	}
	return nil
}

func (x *GetPostsReplyItem) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

func (x *GetPostsReplyItem) GetTopContent() string {
	if x != nil {
		return x.TopContent
	}
	return ""
}

func (x *GetPostsReplyItem) GetTopColor() string {
	if x != nil {
		return x.TopColor
	}
	return ""
}

func (x *GetPostsReplyItem) GetTopBgColor() string {
	if x != nil {
		return x.TopBgColor
	}
	return ""
}

func (x *GetPostsReplyItem) GetVodFileId() string {
	if x != nil {
		return x.VodFileId
	}
	return ""
}

func (x *GetPostsReplyItem) GetHasVideo() bool {
	if x != nil {
		return x.HasVideo
	}
	return false
}

type GetTopicRecommendRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetTopicRecommendRequest) Reset() {
	*x = GetTopicRecommendRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopicRecommendRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopicRecommendRequest) ProtoMessage() {}

func (x *GetTopicRecommendRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopicRecommendRequest.ProtoReflect.Descriptor instead.
func (*GetTopicRecommendRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{30}
}

type GetTopicRecommendReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RecommendCol []*TopicRecommend `protobuf:"bytes,1,rep,name=recommendCol,json=recommendCol,proto3" json:"recommendCol"`
}

func (x *GetTopicRecommendReply) Reset() {
	*x = GetTopicRecommendReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopicRecommendReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopicRecommendReply) ProtoMessage() {}

func (x *GetTopicRecommendReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopicRecommendReply.ProtoReflect.Descriptor instead.
func (*GetTopicRecommendReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{31}
}

func (x *GetTopicRecommendReply) GetRecommendCol() []*TopicRecommend {
	if x != nil {
		return x.RecommendCol
	}
	return nil
}

type TopicRecommend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prefix    string `protobuf:"bytes,1,opt,name=Prefix,json=Prefix,proto3" json:"Prefix"`
	TopicId   string `protobuf:"bytes,2,opt,name=TopicId,json=TopicId,proto3" json:"TopicId"`
	Content   string `protobuf:"bytes,3,opt,name=Content,json=Content,proto3" json:"Content"`
	ViewCount int32  `protobuf:"varint,4,opt,name=ViewCount,json=ViewCount,proto3" json:"ViewCount"`
}

func (x *TopicRecommend) Reset() {
	*x = TopicRecommend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TopicRecommend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicRecommend) ProtoMessage() {}

func (x *TopicRecommend) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicRecommend.ProtoReflect.Descriptor instead.
func (*TopicRecommend) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{32}
}

func (x *TopicRecommend) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *TopicRecommend) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

func (x *TopicRecommend) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *TopicRecommend) GetViewCount() int32 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

type GetTopicDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=UserId,json=UserId,proto3" json:"UserId"`
	TopicId   string `protobuf:"bytes,2,opt,name=TopicId,json=TopicId,proto3" json:"TopicId"`
	PageIndex int32  `protobuf:"varint,3,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32  `protobuf:"varint,4,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
	Type      int32  `protobuf:"varint,5,opt,name=type,json=type,proto3" json:"type"` // 1:热门,2:最新
}

func (x *GetTopicDetailRequest) Reset() {
	*x = GetTopicDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopicDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopicDetailRequest) ProtoMessage() {}

func (x *GetTopicDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopicDetailRequest.ProtoReflect.Descriptor instead.
func (*GetTopicDetailRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{33}
}

func (x *GetTopicDetailRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetTopicDetailRequest) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

func (x *GetTopicDetailRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetTopicDetailRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetTopicDetailRequest) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type GetTopicDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Prefix           string               `protobuf:"bytes,1,opt,name=Prefix,json=Prefix,proto3" json:"Prefix"`
	TopicId          string               `protobuf:"bytes,2,opt,name=TopicId,json=TopicId,proto3" json:"TopicId"`
	Content          string               `protobuf:"bytes,3,opt,name=Content,json=Content,proto3" json:"Content"`
	Introduction     string               `protobuf:"bytes,4,opt,name=Introduction,json=Introduction,proto3" json:"Introduction"`
	BackgroudImage   string               `protobuf:"bytes,5,opt,name=BackgroudImage,json=BackgroudImage,proto3" json:"BackgroudImage"`
	ViewCount        int32                `protobuf:"varint,6,opt,name=ViewCount,json=ViewCount,proto3" json:"ViewCount"`
	ParticipantCount int32                `protobuf:"varint,7,opt,name=ParticipantCount,json=ParticipantCount,proto3" json:"ParticipantCount"`
	PostsCount       int32                `protobuf:"varint,8,opt,name=PostsCount,json=PostsCount,proto3" json:"PostsCount"`
	PostsCol         []*GetPostsReplyItem `protobuf:"bytes,9,rep,name=postsCol,json=postsCol,proto3" json:"postsCol"`
	Collected        bool                 `protobuf:"varint,10,opt,name=Collected,json=Collected,proto3" json:"Collected"`
}

func (x *GetTopicDetailReply) Reset() {
	*x = GetTopicDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTopicDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTopicDetailReply) ProtoMessage() {}

func (x *GetTopicDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTopicDetailReply.ProtoReflect.Descriptor instead.
func (*GetTopicDetailReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{34}
}

func (x *GetTopicDetailReply) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *GetTopicDetailReply) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

func (x *GetTopicDetailReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *GetTopicDetailReply) GetIntroduction() string {
	if x != nil {
		return x.Introduction
	}
	return ""
}

func (x *GetTopicDetailReply) GetBackgroudImage() string {
	if x != nil {
		return x.BackgroudImage
	}
	return ""
}

func (x *GetTopicDetailReply) GetViewCount() int32 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *GetTopicDetailReply) GetParticipantCount() int32 {
	if x != nil {
		return x.ParticipantCount
	}
	return 0
}

func (x *GetTopicDetailReply) GetPostsCount() int32 {
	if x != nil {
		return x.PostsCount
	}
	return 0
}

func (x *GetTopicDetailReply) GetPostsCol() []*GetPostsReplyItem {
	if x != nil {
		return x.PostsCol
	}
	return nil
}

func (x *GetTopicDetailReply) GetCollected() bool {
	if x != nil {
		return x.Collected
	}
	return false
}

type GetUserTopicCollectListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string `protobuf:"bytes,1,opt,name=userId,json=userId,proto3" json:"userId"`
	PageIndex int32  `protobuf:"varint,2,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32  `protobuf:"varint,3,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *GetUserTopicCollectListRequest) Reset() {
	*x = GetUserTopicCollectListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserTopicCollectListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserTopicCollectListRequest) ProtoMessage() {}

func (x *GetUserTopicCollectListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserTopicCollectListRequest.ProtoReflect.Descriptor instead.
func (*GetUserTopicCollectListRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{35}
}

func (x *GetUserTopicCollectListRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserTopicCollectListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *GetUserTopicCollectListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserTopicCollectListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserTopicCollectListReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserTopicCollectListReply) Reset() {
	*x = GetUserTopicCollectListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserTopicCollectListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserTopicCollectListReply) ProtoMessage() {}

func (x *GetUserTopicCollectListReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserTopicCollectListReply.ProtoReflect.Descriptor instead.
func (*GetUserTopicCollectListReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{36}
}

func (x *GetUserTopicCollectListReply) GetList() []*GetUserTopicCollectListReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

type GetUserTopicCollectListReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicId         string `protobuf:"bytes,1,opt,name=topicId,json=topicId,proto3" json:"topicId"`                          //话题Id
	TopicName       string `protobuf:"bytes,2,opt,name=topicName,json=topicName,proto3" json:"topicName"`                    //话题内容
	ViewCount       string `protobuf:"bytes,3,opt,name=ViewCount,json=ViewCount,proto3" json:"ViewCount"`                    //浏览数
	UserCount       string `protobuf:"bytes,4,opt,name=UserCount,json=UserCount,proto3" json:"UserCount"`                    //参与人数
	IsShowViewCount bool   `protobuf:"varint,5,opt,name=IsShowViewCount,json=IsShowViewCount,proto3" json:"IsShowViewCount"` //是否展示浏览数
	IsShowUserCount bool   `protobuf:"varint,6,opt,name=IsShowUserCount,json=IsShowUserCount,proto3" json:"IsShowUserCount"` //是否展示参与数量
}

func (x *GetUserTopicCollectListReplyItem) Reset() {
	*x = GetUserTopicCollectListReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserTopicCollectListReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserTopicCollectListReplyItem) ProtoMessage() {}

func (x *GetUserTopicCollectListReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserTopicCollectListReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserTopicCollectListReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{37}
}

func (x *GetUserTopicCollectListReplyItem) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

func (x *GetUserTopicCollectListReplyItem) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

func (x *GetUserTopicCollectListReplyItem) GetViewCount() string {
	if x != nil {
		return x.ViewCount
	}
	return ""
}

func (x *GetUserTopicCollectListReplyItem) GetUserCount() string {
	if x != nil {
		return x.UserCount
	}
	return ""
}

func (x *GetUserTopicCollectListReplyItem) GetIsShowViewCount() bool {
	if x != nil {
		return x.IsShowViewCount
	}
	return false
}

func (x *GetUserTopicCollectListReplyItem) GetIsShowUserCount() bool {
	if x != nil {
		return x.IsShowUserCount
	}
	return false
}

type GetUserBusinessCountOrApplaudCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIds []string `protobuf:"bytes,1,rep,name=userIds,json=userIds,proto3" json:"userIds"` //用户Id
}

func (x *GetUserBusinessCountOrApplaudCountRequest) Reset() {
	*x = GetUserBusinessCountOrApplaudCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserBusinessCountOrApplaudCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBusinessCountOrApplaudCountRequest) ProtoMessage() {}

func (x *GetUserBusinessCountOrApplaudCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBusinessCountOrApplaudCountRequest.ProtoReflect.Descriptor instead.
func (*GetUserBusinessCountOrApplaudCountRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{38}
}

func (x *GetUserBusinessCountOrApplaudCountRequest) GetUserIds() []string {
	if x != nil {
		return x.UserIds
	}
	return nil
}

type GetUserBusinessCountOrApplaudCountReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PostsCount   int32  `protobuf:"varint,1,opt,name=postsCount,json=postsCount,proto3" json:"postsCount"`       //商业数量
	ApplaudCount int32  `protobuf:"varint,2,opt,name=applaudCount,json=applaudCount,proto3" json:"applaudCount"` //获赞数量
	UserId       string `protobuf:"bytes,3,opt,name=userId,json=userId,proto3" json:"userId"`
}

func (x *GetUserBusinessCountOrApplaudCountReplyItem) Reset() {
	*x = GetUserBusinessCountOrApplaudCountReplyItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserBusinessCountOrApplaudCountReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBusinessCountOrApplaudCountReplyItem) ProtoMessage() {}

func (x *GetUserBusinessCountOrApplaudCountReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBusinessCountOrApplaudCountReplyItem.ProtoReflect.Descriptor instead.
func (*GetUserBusinessCountOrApplaudCountReplyItem) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{39}
}

func (x *GetUserBusinessCountOrApplaudCountReplyItem) GetPostsCount() int32 {
	if x != nil {
		return x.PostsCount
	}
	return 0
}

func (x *GetUserBusinessCountOrApplaudCountReplyItem) GetApplaudCount() int32 {
	if x != nil {
		return x.ApplaudCount
	}
	return 0
}

func (x *GetUserBusinessCountOrApplaudCountReplyItem) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserBusinessCountOrApplaudCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*GetUserBusinessCountOrApplaudCountReplyItem `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *GetUserBusinessCountOrApplaudCountReply) Reset() {
	*x = GetUserBusinessCountOrApplaudCountReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserBusinessCountOrApplaudCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserBusinessCountOrApplaudCountReply) ProtoMessage() {}

func (x *GetUserBusinessCountOrApplaudCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserBusinessCountOrApplaudCountReply.ProtoReflect.Descriptor instead.
func (*GetUserBusinessCountOrApplaudCountReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{40}
}

func (x *GetUserBusinessCountOrApplaudCountReply) GetList() []*GetUserBusinessCountOrApplaudCountReplyItem {
	if x != nil {
		return x.List
	}
	return nil
}

// 活动列表
type ActivityListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32 `protobuf:"varint,2,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *ActivityListRequest) Reset() {
	*x = ActivityListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityListRequest) ProtoMessage() {}

func (x *ActivityListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityListRequest.ProtoReflect.Descriptor instead.
func (*ActivityListRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{41}
}

func (x *ActivityListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ActivityListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ActivityListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*ActivityListItemReply `protobuf:"bytes,1,rep,name=list,json=list,proto3" json:"list"`
}

func (x *ActivityListReply) Reset() {
	*x = ActivityListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityListReply) ProtoMessage() {}

func (x *ActivityListReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityListReply.ProtoReflect.Descriptor instead.
func (*ActivityListReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{42}
}

func (x *ActivityListReply) GetList() []*ActivityListItemReply {
	if x != nil {
		return x.List
	}
	return nil
}

type ActivityListItemReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId    string `protobuf:"bytes,1,opt,name=activityId,json=activityId,proto3" json:"activityId"` //活动Id
	Title         string `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Intro         string `protobuf:"bytes,3,opt,name=intro,json=intro,proto3" json:"intro"`                         //活动简介
	StartTime     string `protobuf:"bytes,4,opt,name=startTime,json=startTime,proto3" json:"startTime"`             //开始时间
	EndTime       string `protobuf:"bytes,5,opt,name=endTime,json=endTime,proto3" json:"endTime"`                   //结束时间
	Status        int32  `protobuf:"varint,6,opt,name=status,json=status,proto3" json:"status"`                     //活动状态 0 未开始 1进行中 2 已结束
	StatusContent string `protobuf:"bytes,7,opt,name=statusContent,json=statusContent,proto3" json:"statusContent"` //活动状态
	StatusBgColor string `protobuf:"bytes,8,opt,name=statusBgColor,json=statusBgColor,proto3" json:"statusBgColor"` //状态背景色
	StatusColor   string `protobuf:"bytes,9,opt,name=statusColor,json=statusColor,proto3" json:"statusColor"`       //状态字体颜色
	Image         string `protobuf:"bytes,10,opt,name=Image,json=Image,proto3" json:"Image"`                        //图片
}

func (x *ActivityListItemReply) Reset() {
	*x = ActivityListItemReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityListItemReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityListItemReply) ProtoMessage() {}

func (x *ActivityListItemReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityListItemReply.ProtoReflect.Descriptor instead.
func (*ActivityListItemReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{43}
}

func (x *ActivityListItemReply) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *ActivityListItemReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ActivityListItemReply) GetIntro() string {
	if x != nil {
		return x.Intro
	}
	return ""
}

func (x *ActivityListItemReply) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ActivityListItemReply) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ActivityListItemReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ActivityListItemReply) GetStatusContent() string {
	if x != nil {
		return x.StatusContent
	}
	return ""
}

func (x *ActivityListItemReply) GetStatusBgColor() string {
	if x != nil {
		return x.StatusBgColor
	}
	return ""
}

func (x *ActivityListItemReply) GetStatusColor() string {
	if x != nil {
		return x.StatusColor
	}
	return ""
}

func (x *ActivityListItemReply) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

// 活动详情
type ActivityDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId string `protobuf:"bytes,1,opt,name=activityId,json=activityId,proto3" json:"activityId"` //活动Id
	PageIndex  int32  `protobuf:"varint,2,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize   int32  `protobuf:"varint,3,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *ActivityDetailRequest) Reset() {
	*x = ActivityDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityDetailRequest) ProtoMessage() {}

func (x *ActivityDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityDetailRequest.ProtoReflect.Descriptor instead.
func (*ActivityDetailRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{44}
}

func (x *ActivityDetailRequest) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *ActivityDetailRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ActivityDetailRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ActivityDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId      string               `protobuf:"bytes,1,opt,name=activityId,json=activityId,proto3" json:"activityId"` //活动Id
	Title           string               `protobuf:"bytes,2,opt,name=title,json=title,proto3" json:"title"`
	Intro           string               `protobuf:"bytes,3,opt,name=intro,json=intro,proto3" json:"intro"`                          //活动简介
	StartTime       string               `protobuf:"bytes,4,opt,name=startTime,json=startTime,proto3" json:"startTime"`              //开始时间
	EndTime         string               `protobuf:"bytes,5,opt,name=endTime,json=endTime,proto3" json:"endTime"`                    //结束时间
	Status          int32                `protobuf:"varint,6,opt,name=status,json=status,proto3" json:"status"`                      //活动状态 0 未开始 1进行中 2 已结束
	StatusContent   string               `protobuf:"bytes,7,opt,name=statusContent,json=statusContent,proto3" json:"statusContent"`  //活动状态
	StatusBgColor   string               `protobuf:"bytes,8,opt,name=statusBgColor,json=statusBgColor,proto3" json:"statusBgColor"`  //状态背景色
	StatusColor     string               `protobuf:"bytes,9,opt,name=statusColor,json=statusColor,proto3" json:"statusColor"`        //状态字体颜色
	Content         string               `protobuf:"bytes,10,opt,name=content,json=content,proto3" json:"content"`                   //活动内容
	JoinCount       string               `protobuf:"bytes,11,opt,name=joinCount,json=joinCount,proto3" json:"joinCount"`             //参数人数
	JoinUserPhoto   []string             `protobuf:"bytes,12,rep,name=JoinUserPhoto,json=JoinUserPhoto,proto3" json:"JoinUserPhoto"` //参与人数头像
	Image           string               `protobuf:"bytes,13,opt,name=Image,json=Image,proto3" json:"Image"`                         //图片
	TopicId         string               `protobuf:"bytes,14,opt,name=TopicId,json=TopicId,proto3" json:"TopicId"`                   // 话题Id
	TopicName       string               `protobuf:"bytes,15,opt,name=TopicName,json=TopicName,proto3" json:"TopicName"`             //话题名称
	LinkType        int32                `protobuf:"varint,16,opt,name=LinkType,json=LinkType,proto3" json:"LinkType"`               //跳转类型  1发布帖子 2外链 3内链 4交易商详情页5直播6排行榜7实盘
	LinkContent     string               `protobuf:"bytes,17,opt,name=LinkContent,json=LinkContent,proto3" json:"LinkContent"`       //跳转内容,
	IsWonderful     bool                 `protobuf:"varint,18,opt,name=IsWonderful,json=IsWonderful,proto3" json:"IsWonderful"`      //是否是精彩活动
	LinkAddress     string               `protobuf:"bytes,19,opt,name=LinkAddress,json=LinkAddress,proto3" json:"LinkAddress"`       //跳转地址
	PostsCol        []*GetPostsReplyItem `protobuf:"bytes,20,rep,name=postsCol,json=postsCol,proto3" json:"postsCol"`
	AreaCode        string               `protobuf:"bytes,21,opt,name=AreaCode,json=AreaCode,proto3" json:"AreaCode"`                       //区域Code
	LanguageCode    string               `protobuf:"bytes,22,opt,name=LanguageCode,json=LanguageCode,proto3" json:"LanguageCode"`           //语言
	LinkAddressType int32                `protobuf:"varint,23,opt,name=LinkAddressType,json=LinkAddressType,proto3" json:"LinkAddressType"` //1内链 2外链
}

func (x *ActivityDetailReply) Reset() {
	*x = ActivityDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityDetailReply) ProtoMessage() {}

func (x *ActivityDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityDetailReply.ProtoReflect.Descriptor instead.
func (*ActivityDetailReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{45}
}

func (x *ActivityDetailReply) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *ActivityDetailReply) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ActivityDetailReply) GetIntro() string {
	if x != nil {
		return x.Intro
	}
	return ""
}

func (x *ActivityDetailReply) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ActivityDetailReply) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ActivityDetailReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ActivityDetailReply) GetStatusContent() string {
	if x != nil {
		return x.StatusContent
	}
	return ""
}

func (x *ActivityDetailReply) GetStatusBgColor() string {
	if x != nil {
		return x.StatusBgColor
	}
	return ""
}

func (x *ActivityDetailReply) GetStatusColor() string {
	if x != nil {
		return x.StatusColor
	}
	return ""
}

func (x *ActivityDetailReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ActivityDetailReply) GetJoinCount() string {
	if x != nil {
		return x.JoinCount
	}
	return ""
}

func (x *ActivityDetailReply) GetJoinUserPhoto() []string {
	if x != nil {
		return x.JoinUserPhoto
	}
	return nil
}

func (x *ActivityDetailReply) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *ActivityDetailReply) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

func (x *ActivityDetailReply) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

func (x *ActivityDetailReply) GetLinkType() int32 {
	if x != nil {
		return x.LinkType
	}
	return 0
}

func (x *ActivityDetailReply) GetLinkContent() string {
	if x != nil {
		return x.LinkContent
	}
	return ""
}

func (x *ActivityDetailReply) GetIsWonderful() bool {
	if x != nil {
		return x.IsWonderful
	}
	return false
}

func (x *ActivityDetailReply) GetLinkAddress() string {
	if x != nil {
		return x.LinkAddress
	}
	return ""
}

func (x *ActivityDetailReply) GetPostsCol() []*GetPostsReplyItem {
	if x != nil {
		return x.PostsCol
	}
	return nil
}

func (x *ActivityDetailReply) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *ActivityDetailReply) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

func (x *ActivityDetailReply) GetLinkAddressType() int32 {
	if x != nil {
		return x.LinkAddressType
	}
	return 0
}

type ActivityPostsPageListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32 `protobuf:"varint,1,opt,name=pageIndex,json=pageIndex,proto3" json:"pageIndex"`
	PageSize  int32 `protobuf:"varint,2,opt,name=pageSize,json=pageSize,proto3" json:"pageSize"`
}

func (x *ActivityPostsPageListRequest) Reset() {
	*x = ActivityPostsPageListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityPostsPageListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityPostsPageListRequest) ProtoMessage() {}

func (x *ActivityPostsPageListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityPostsPageListRequest.ProtoReflect.Descriptor instead.
func (*ActivityPostsPageListRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{46}
}

func (x *ActivityPostsPageListRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ActivityPostsPageListRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ActivityPostsPageListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GetPostsReplyItem `protobuf:"bytes,1,rep,name=Items,json=Items,proto3" json:"Items"`
}

func (x *ActivityPostsPageListReply) Reset() {
	*x = ActivityPostsPageListReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityPostsPageListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityPostsPageListReply) ProtoMessage() {}

func (x *ActivityPostsPageListReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityPostsPageListReply.ProtoReflect.Descriptor instead.
func (*ActivityPostsPageListReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{47}
}

func (x *ActivityPostsPageListReply) GetItems() []*GetPostsReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UserJoinActivityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId    string `protobuf:"bytes,1,opt,name=activityId,json=activityId,proto3" json:"activityId"`          //活动Id
	AvatarAddress string `protobuf:"bytes,2,opt,name=AvatarAddress,json=AvatarAddress,proto3" json:"AvatarAddress"` //参与用户的头像
}

func (x *UserJoinActivityRequest) Reset() {
	*x = UserJoinActivityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserJoinActivityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserJoinActivityRequest) ProtoMessage() {}

func (x *UserJoinActivityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserJoinActivityRequest.ProtoReflect.Descriptor instead.
func (*UserJoinActivityRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{48}
}

func (x *UserJoinActivityRequest) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *UserJoinActivityRequest) GetAvatarAddress() string {
	if x != nil {
		return x.AvatarAddress
	}
	return ""
}

type EmptyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyResponse) Reset() {
	*x = EmptyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyResponse) ProtoMessage() {}

func (x *EmptyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyResponse.ProtoReflect.Descriptor instead.
func (*EmptyResponse) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{49}
}

type UserTopicNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicId string `protobuf:"bytes,1,opt,name=topicId,json=topicId,proto3" json:"topicId"`
}

func (x *UserTopicNameRequest) Reset() {
	*x = UserTopicNameRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserTopicNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicNameRequest) ProtoMessage() {}

func (x *UserTopicNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicNameRequest.ProtoReflect.Descriptor instead.
func (*UserTopicNameRequest) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{50}
}

func (x *UserTopicNameRequest) GetTopicId() string {
	if x != nil {
		return x.TopicId
	}
	return ""
}

type UserTopicNameReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TopicName string `protobuf:"bytes,1,opt,name=topicName,json=topicName,proto3" json:"topicName"`
}

func (x *UserTopicNameReply) Reset() {
	*x = UserTopicNameReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_community_v1_wikicommunity_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserTopicNameReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserTopicNameReply) ProtoMessage() {}

func (x *UserTopicNameReply) ProtoReflect() protoreflect.Message {
	mi := &file_community_v1_wikicommunity_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserTopicNameReply.ProtoReflect.Descriptor instead.
func (*UserTopicNameReply) Descriptor() ([]byte, []int) {
	return file_community_v1_wikicommunity_proto_rawDescGZIP(), []int{51}
}

func (x *UserTopicNameReply) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

var File_community_v1_wikicommunity_proto protoreflect.FileDescriptor

var file_community_v1_wikicommunity_proto_rawDesc = []byte{
	0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x77,
	0x69, 0x6b, 0x69, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x10, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f,
	0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3c, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64,
	0x61, 0x74, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x61, 0x74, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x56, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x64,
	0x61, 0x74, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x61, 0x74, 0x61, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x38, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6f,
	0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73, 0x22, 0x5e, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x50, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22,
	0x9e, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0x61, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x41, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0x75, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69,
	0x65, 0x77, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x51, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x22, 0x5f, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x35,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x50, 0x6f, 0x73,
	0x74, 0x73, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x49, 0x64, 0x22, 0x31, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x42, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50,
	0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x9a, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x9a, 0x01,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x73,
	0x74, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x75,
	0x62, 0x6c, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x42, 0x0a, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x22, 0xaa, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4f, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x61, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x74, 0x73,
	0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x50, 0x6f, 0x73, 0x74, 0x73,
	0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72,
	0x61, 0x64, 0x65, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x64, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x49, 0x73, 0x44, 0x6f, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x44, 0x6f, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x75, 0x0a,
	0x15, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73,
	0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73, 0x22, 0x30, 0x0a, 0x14,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x22, 0xb7,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61,
	0x75, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x08, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x73, 0x12, 0x5d, 0x0a, 0x0e, 0x41, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0e, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75,
	0x64, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xbf, 0x04, 0x0a, 0x23, 0x47, 0x65, 0x74,
	0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x41, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x18, 0x0a, 0x07, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x41, 0x70,
	0x70, 0x6c, 0x61, 0x75, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0d, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x70,
	0x70, 0x6c, 0x61, 0x75, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x68, 0x6f, 0x77,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77,
	0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x6c, 0x61,
	0x75, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73, 0x53, 0x68,
	0x6f, 0x77, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x13, 0x69, 0x73,
	0x53, 0x68, 0x6f, 0x77, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x0f,
	0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x6c, 0x61,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x50, 0x6c, 0x61, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x68, 0x6f,
	0x77, 0x50, 0x6c, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0x4a, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x73,
	0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x73, 0x12, 0x36, 0x0a, 0x16, 0x69, 0x73, 0x53, 0x68,
	0x6f, 0x77, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x41, 0x75, 0x64,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x69, 0x73, 0x53, 0x68, 0x6f, 0x77,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x41, 0x75, 0x64, 0x69, 0x74,
	0x22, 0x73, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x7b, 0x0a, 0x09, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x53, 0x69,
	0x67, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x12, 0x18, 0x0a, 0x07, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x42, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x57, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x57, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x22, 0x61, 0x0a, 0x0f, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xb8, 0x0a, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73,
	0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4e, 0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x4e, 0x65, 0x77, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4e, 0x65,
	0x77, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x4e, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x73, 0x61, 0x6c, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x75,
	0x73, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c,
	0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x10,
	0x73, 0x68, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x6c,
	0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x6f,
	0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a,
	0x0a, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x68, 0x6f, 0x77, 0x46, 0x6f,
	0x72, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65,
	0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x72, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x68,
	0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x68, 0x65, 0x6d, 0x65,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x55, 0x72, 0x6c,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x68, 0x61, 0x72, 0x65, 0x55, 0x72, 0x6c,
	0x12, 0x2f, 0x0a, 0x04, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x53, 0x69, 0x67, 0x6e, 0x52, 0x04, 0x53, 0x69, 0x67,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x56,
	0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73, 0x53,
	0x68, 0x6f, 0x77, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x47,
	0x72, 0x61, 0x64, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x47, 0x72, 0x61, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x29, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x4b, 0x0a, 0x0f, 0x70, 0x6f, 0x73, 0x74, 0x73,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x52, 0x0f, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x54, 0x6f, 0x70, 0x18, 0x2c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x54, 0x6f, 0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f,
	0x70, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x74, 0x6f, 0x70, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f,
	0x70, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f,
	0x70, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x70, 0x42, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x6f, 0x70, 0x42,
	0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x6f, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x64, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x6f, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x18, 0x31, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x68, 0x61, 0x73, 0x56, 0x69, 0x64, 0x65, 0x6f,
	0x22, 0x1a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5e, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x44, 0x0a, 0x0c, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x0c,
	0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x22, 0x7a, 0x0a, 0x0e,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69,
	0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x56,
	0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x97, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x22, 0xf6, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x72,
	0x65, 0x66, 0x69, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x72, 0x65, 0x66,
	0x69, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x6e,
	0x74, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x64, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2a, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x50, 0x61, 0x72, 0x74,
	0x69, 0x63, 0x69, 0x70, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x50, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x08,
	0x70, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x6c, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x72, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0x66, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x46, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xea, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74,
	0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x49, 0x73, 0x53, 0x68, 0x6f,
	0x77, 0x56, 0x69, 0x65, 0x77, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x49, 0x73,
	0x53, 0x68, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x72, 0x41, 0x70,
	0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73, 0x22, 0x89, 0x01, 0x0a, 0x2b,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1e, 0x0a, 0x0a, 0x70,
	0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f,
	0x72, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x51, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4f, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x50, 0x0a, 0x11, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3b, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xb7, 0x02, 0x0a, 0x15, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x72,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x1c,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24,
	0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x22, 0x71, 0x0a, 0x15, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xf8, 0x05, 0x0a, 0x13, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1e, 0x0a,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6a, 0x6f, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6a, 0x6f, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x4a, 0x6f, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x50, 0x68, 0x6f, 0x74, 0x6f,
	0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x4a, 0x6f, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x69, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x4c, 0x69, 0x6e, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x4c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x49, 0x73, 0x57, 0x6f, 0x6e, 0x64, 0x65, 0x72, 0x66, 0x75, 0x6c,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x49, 0x73, 0x57, 0x6f, 0x6e, 0x64, 0x65, 0x72,
	0x66, 0x75, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3f, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x43, 0x6f,
	0x6c, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x08, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x43, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x65, 0x61, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x58, 0x0a, 0x1c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x73, 0x74,
	0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x57, 0x0a, 0x1a, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x5f, 0x0a, 0x17, 0x55, 0x73, 0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x22, 0x0f, 0x0a, 0x0d, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x6f, 0x70, 0x69, 0x63, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x54,
	0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0x9d, 0x1a, 0x0a, 0x0d,
	0x57, 0x69, 0x6b, 0x69, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x7d, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x3a, 0x01, 0x2a, 0x22, 0x1e, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f,
	0x67, 0x65, 0x74, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x91, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64,
	0x12, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c,
	0x61, 0x75, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2f, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64,
	0x12, 0x88, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6f,
	0x73, 0x74, 0x73, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65,
	0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65,
	0x6d, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x47, 0x65, 0x74,
	0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x12, 0xae, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4f, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x74, 0x47, 0x72, 0x61, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4f, 0x72, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x3a, 0x01, 0x2a, 0x22, 0x2d, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79,
	0x2f, 0x67, 0x65, 0x74, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x6f, 0x72, 0x63, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x67, 0x72, 0x61, 0x64, 0x65, 0x73, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0xaf, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64,
	0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73,
	0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01,
	0x2a, 0x22, 0x2b, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x61, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x61, 0x6e, 0x64, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x9e,
	0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x12, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x75,
	0x73, 0x65, 0x72, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x12,
	0x9a, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x50,
	0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x42, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x42, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x75, 0x73, 0x65,
	0x72, 0x69, 0x64, 0x62, 0x79, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x69, 0x64, 0x12, 0x8a, 0x01, 0x0a,
	0x0e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x73, 0x74, 0x73, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x70, 0x6f,
	0x73, 0x74, 0x73, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0xba, 0x01, 0x0a, 0x1a, 0x47, 0x65,
	0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x73,
	0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x6f,
	0x73, 0x74, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x12, 0x2c, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x68, 0x6f, 0x6d, 0x65, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x70, 0x61,
	0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xae, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73,
	0x74, 0x73, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69,
	0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56, 0x69, 0x65,
	0x77, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x56,
	0x69, 0x65, 0x77, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x12, 0x29, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f,
	0x67, 0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x12, 0xa1, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x73, 0x74, 0x73, 0x55, 0x73, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x12,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41,
	0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x41, 0x70, 0x70,
	0x6c, 0x61, 0x75, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x70, 0x6f, 0x73, 0x74, 0x73,
	0x75, 0x73, 0x65, 0x72, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x12, 0xa1, 0x01, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x30, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a, 0x22, 0x25, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x70,
	0x6f, 0x73, 0x74, 0x73, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x96, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x72,
	0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x8a, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x70, 0x69, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x27, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69, 0x63,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67, 0x65, 0x74, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xa9, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f,
	0x70, 0x69, 0x63, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x12, 0x24, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67,
	0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6c, 0x69, 0x73,
	0x74, 0x12, 0xdd, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x72, 0x41, 0x70, 0x70, 0x6c,
	0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x72, 0x41,
	0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x3f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x39, 0x3a, 0x01, 0x2a, 0x22, 0x34, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67,
	0x65, 0x74, 0x75, 0x73, 0x65, 0x72, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x6f, 0x72, 0x61, 0x70, 0x70, 0x6c, 0x61, 0x75, 0x64, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x8a, 0x01, 0x0a, 0x10, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x61,
	0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x70, 0x61, 0x67, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x8a,
	0x01, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0xa6, 0x01, 0x0a, 0x15,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50, 0x61, 0x67,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x50, 0x6f, 0x73, 0x74, 0x73, 0x50, 0x61, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x12, 0x27, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x70, 0x6f, 0x73, 0x74, 0x73, 0x70, 0x61, 0x67, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x8d, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65, 0x72, 0x4a, 0x6f, 0x69,
	0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75,
	0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a,
	0x22, 0x22, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e,
	0x69, 0x74, 0x79, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x6a, 0x6f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x12, 0x84, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x12, 0x1e, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x70, 0x70, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x67,
	0x65, 0x74, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x23, 0x5a, 0x21, 0x61,
	0x70, 0x69, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x74, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_community_v1_wikicommunity_proto_rawDescOnce sync.Once
	file_community_v1_wikicommunity_proto_rawDescData = file_community_v1_wikicommunity_proto_rawDesc
)

func file_community_v1_wikicommunity_proto_rawDescGZIP() []byte {
	file_community_v1_wikicommunity_proto_rawDescOnce.Do(func() {
		file_community_v1_wikicommunity_proto_rawDescData = protoimpl.X.CompressGZIP(file_community_v1_wikicommunity_proto_rawDescData)
	})
	return file_community_v1_wikicommunity_proto_rawDescData
}

var file_community_v1_wikicommunity_proto_msgTypes = make([]protoimpl.MessageInfo, 52)
var file_community_v1_wikicommunity_proto_goTypes = []interface{}{
	(*GetPostsReplyStatusRequest)(nil),                  // 0: api.community.v1.GetPostsReplyStatusRequest
	(*GetPostsReplyStatusReply)(nil),                    // 1: api.community.v1.GetPostsReplyStatusReply
	(*GetPostsReplyStatusReplyItem)(nil),                // 2: api.community.v1.GetPostsReplyStatusReplyItem
	(*GetUserPostsApplaudRequest)(nil),                  // 3: api.community.v1.GetUserPostsApplaudRequest
	(*GetUserPostsApplaudReply)(nil),                    // 4: api.community.v1.GetUserPostsApplaudReply
	(*GetUserPostsApplaudReplyItem)(nil),                // 5: api.community.v1.GetUserPostsApplaudReplyItem
	(*GetUserViewHistoryPostsRequest)(nil),              // 6: api.community.v1.GetUserViewHistoryPostsRequest
	(*GetUserViewHistoryPostsReply)(nil),                // 7: api.community.v1.GetUserViewHistoryPostsReply
	(*GetUserViewHistoryPostsItem)(nil),                 // 8: api.community.v1.GetUserViewHistoryPostsItem
	(*GetPostsNumberRequest)(nil),                       // 9: api.community.v1.GetPostsNumberRequest
	(*GetPostsNumberReply)(nil),                         // 10: api.community.v1.GetPostsNumberReply
	(*GetUserIdByPostsIdRequest)(nil),                   // 11: api.community.v1.GetUserIdByPostsIdRequest
	(*GetUserIdByPostsIdReply)(nil),                     // 12: api.community.v1.GetUserIdByPostsIdReply
	(*GetPersonHomePostsPageListRequest)(nil),           // 13: api.community.v1.GetPersonHomePostsPageListRequest
	(*GetPersonHomePostsPageListReply)(nil),             // 14: api.community.v1.GetPersonHomePostsPageListReply
	(*GetUserCollectPostsRequest)(nil),                  // 15: api.community.v1.GetUserCollectPostsRequest
	(*GetUserCollectPostsReplyItem)(nil),                // 16: api.community.v1.GetUserCollectPostsReplyItem
	(*GetUserCollectPostsReply)(nil),                    // 17: api.community.v1.GetUserCollectPostsReply
	(*GetPostsOrCommentGradesInfoRequest)(nil),          // 18: api.community.v1.GetPostsOrCommentGradesInfoRequest
	(*GetSinglePostsRequest)(nil),                       // 19: api.community.v1.GetSinglePostsRequest
	(*GetPostsApplaudRequest)(nil),                      // 20: api.community.v1.GetPostsApplaudRequest
	(*GetPostsApplaudReply)(nil),                        // 21: api.community.v1.GetPostsApplaudReply
	(*GetPostsApplaudAndCollectReply)(nil),              // 22: api.community.v1.GetPostsApplaudAndCollectReply
	(*GetPostsApplaudAndCollectReplyCount)(nil),         // 23: api.community.v1.GetPostsApplaudAndCollectReplyCount
	(*GetPostsReply)(nil),                               // 24: api.community.v1.GetPostsReply
	(*GetPostsRequest)(nil),                             // 25: api.community.v1.GetPostsRequest
	(*Image)(nil),                                       // 26: api.community.v1.Image
	(*PostsSign)(nil),                                   // 27: api.community.v1.PostsSign
	(*PostsTopicItems)(nil),                             // 28: api.community.v1.PostsTopicItems
	(*GetPostsReplyItem)(nil),                           // 29: api.community.v1.GetPostsReplyItem
	(*GetTopicRecommendRequest)(nil),                    // 30: api.community.v1.GetTopicRecommendRequest
	(*GetTopicRecommendReply)(nil),                      // 31: api.community.v1.GetTopicRecommendReply
	(*TopicRecommend)(nil),                              // 32: api.community.v1.TopicRecommend
	(*GetTopicDetailRequest)(nil),                       // 33: api.community.v1.GetTopicDetailRequest
	(*GetTopicDetailReply)(nil),                         // 34: api.community.v1.GetTopicDetailReply
	(*GetUserTopicCollectListRequest)(nil),              // 35: api.community.v1.GetUserTopicCollectListRequest
	(*GetUserTopicCollectListReply)(nil),                // 36: api.community.v1.GetUserTopicCollectListReply
	(*GetUserTopicCollectListReplyItem)(nil),            // 37: api.community.v1.GetUserTopicCollectListReplyItem
	(*GetUserBusinessCountOrApplaudCountRequest)(nil),   // 38: api.community.v1.GetUserBusinessCountOrApplaudCountRequest
	(*GetUserBusinessCountOrApplaudCountReplyItem)(nil), // 39: api.community.v1.GetUserBusinessCountOrApplaudCountReplyItem
	(*GetUserBusinessCountOrApplaudCountReply)(nil),     // 40: api.community.v1.GetUserBusinessCountOrApplaudCountReply
	(*ActivityListRequest)(nil),                         // 41: api.community.v1.ActivityListRequest
	(*ActivityListReply)(nil),                           // 42: api.community.v1.ActivityListReply
	(*ActivityListItemReply)(nil),                       // 43: api.community.v1.ActivityListItemReply
	(*ActivityDetailRequest)(nil),                       // 44: api.community.v1.ActivityDetailRequest
	(*ActivityDetailReply)(nil),                         // 45: api.community.v1.ActivityDetailReply
	(*ActivityPostsPageListRequest)(nil),                // 46: api.community.v1.ActivityPostsPageListRequest
	(*ActivityPostsPageListReply)(nil),                  // 47: api.community.v1.ActivityPostsPageListReply
	(*UserJoinActivityRequest)(nil),                     // 48: api.community.v1.UserJoinActivityRequest
	(*EmptyResponse)(nil),                               // 49: api.community.v1.EmptyResponse
	(*UserTopicNameRequest)(nil),                        // 50: api.community.v1.UserTopicNameRequest
	(*UserTopicNameReply)(nil),                          // 51: api.community.v1.UserTopicNameReply
}
var file_community_v1_wikicommunity_proto_depIdxs = []int32{
	2,  // 0: api.community.v1.GetPostsReplyStatusReply.list:type_name -> api.community.v1.GetPostsReplyStatusReplyItem
	5,  // 1: api.community.v1.GetUserPostsApplaudReply.list:type_name -> api.community.v1.GetUserPostsApplaudReplyItem
	8,  // 2: api.community.v1.GetUserViewHistoryPostsReply.list:type_name -> api.community.v1.GetUserViewHistoryPostsItem
	29, // 3: api.community.v1.GetPersonHomePostsPageListReply.List:type_name -> api.community.v1.GetPostsReplyItem
	16, // 4: api.community.v1.GetUserCollectPostsReply.List:type_name -> api.community.v1.GetUserCollectPostsReplyItem
	23, // 5: api.community.v1.GetPostsApplaudAndCollectReply.ApplaudsCounts:type_name -> api.community.v1.GetPostsApplaudAndCollectReplyCount
	29, // 6: api.community.v1.GetPostsReply.Items:type_name -> api.community.v1.GetPostsReplyItem
	26, // 7: api.community.v1.GetPostsReplyItem.images:type_name -> api.community.v1.Image
	27, // 8: api.community.v1.GetPostsReplyItem.Sign:type_name -> api.community.v1.PostsSign
	28, // 9: api.community.v1.GetPostsReplyItem.postsTopicItems:type_name -> api.community.v1.PostsTopicItems
	32, // 10: api.community.v1.GetTopicRecommendReply.recommendCol:type_name -> api.community.v1.TopicRecommend
	29, // 11: api.community.v1.GetTopicDetailReply.postsCol:type_name -> api.community.v1.GetPostsReplyItem
	37, // 12: api.community.v1.GetUserTopicCollectListReply.list:type_name -> api.community.v1.GetUserTopicCollectListReplyItem
	39, // 13: api.community.v1.GetUserBusinessCountOrApplaudCountReply.list:type_name -> api.community.v1.GetUserBusinessCountOrApplaudCountReplyItem
	43, // 14: api.community.v1.ActivityListReply.list:type_name -> api.community.v1.ActivityListItemReply
	29, // 15: api.community.v1.ActivityDetailReply.postsCol:type_name -> api.community.v1.GetPostsReplyItem
	29, // 16: api.community.v1.ActivityPostsPageListReply.Items:type_name -> api.community.v1.GetPostsReplyItem
	25, // 17: api.community.v1.WikiCommunity.GetPostsInfo:input_type -> api.community.v1.GetPostsRequest
	20, // 18: api.community.v1.WikiCommunity.GetPostsApplaud:input_type -> api.community.v1.GetPostsApplaudRequest
	19, // 19: api.community.v1.WikiCommunity.GetSinglePosts:input_type -> api.community.v1.GetSinglePostsRequest
	18, // 20: api.community.v1.WikiCommunity.GetPostsOrCommentGradesInfo:input_type -> api.community.v1.GetPostsOrCommentGradesInfoRequest
	20, // 21: api.community.v1.WikiCommunity.GetPostsApplaudAndCollect:input_type -> api.community.v1.GetPostsApplaudRequest
	15, // 22: api.community.v1.WikiCommunity.GetUserCollectPosts:input_type -> api.community.v1.GetUserCollectPostsRequest
	11, // 23: api.community.v1.WikiCommunity.GetUserIdByPostsId:input_type -> api.community.v1.GetUserIdByPostsIdRequest
	9,  // 24: api.community.v1.WikiCommunity.GetPostsNumber:input_type -> api.community.v1.GetPostsNumberRequest
	13, // 25: api.community.v1.WikiCommunity.GetPersonHomePostsPageList:input_type -> api.community.v1.GetPersonHomePostsPageListRequest
	6,  // 26: api.community.v1.WikiCommunity.GetUserViewHistoryPosts:input_type -> api.community.v1.GetUserViewHistoryPostsRequest
	3,  // 27: api.community.v1.WikiCommunity.GetPostsUserApplaud:input_type -> api.community.v1.GetUserPostsApplaudRequest
	0,  // 28: api.community.v1.WikiCommunity.GetPostsReplyStatus:input_type -> api.community.v1.GetPostsReplyStatusRequest
	30, // 29: api.community.v1.WikiCommunity.GetTopicRecommend:input_type -> api.community.v1.GetTopicRecommendRequest
	33, // 30: api.community.v1.WikiCommunity.GetTopicDetail:input_type -> api.community.v1.GetTopicDetailRequest
	35, // 31: api.community.v1.WikiCommunity.GetUserTopicCollectList:input_type -> api.community.v1.GetUserTopicCollectListRequest
	38, // 32: api.community.v1.WikiCommunity.GetUserBusinessCountOrApplaudCount:input_type -> api.community.v1.GetUserBusinessCountOrApplaudCountRequest
	41, // 33: api.community.v1.WikiCommunity.ActivityPageList:input_type -> api.community.v1.ActivityListRequest
	44, // 34: api.community.v1.WikiCommunity.ActivityDetail:input_type -> api.community.v1.ActivityDetailRequest
	46, // 35: api.community.v1.WikiCommunity.ActivityPostsPageList:input_type -> api.community.v1.ActivityPostsPageListRequest
	48, // 36: api.community.v1.WikiCommunity.UserJoinActivity:input_type -> api.community.v1.UserJoinActivityRequest
	50, // 37: api.community.v1.WikiCommunity.GetTopicName:input_type -> api.community.v1.UserTopicNameRequest
	24, // 38: api.community.v1.WikiCommunity.GetPostsInfo:output_type -> api.community.v1.GetPostsReply
	21, // 39: api.community.v1.WikiCommunity.GetPostsApplaud:output_type -> api.community.v1.GetPostsApplaudReply
	29, // 40: api.community.v1.WikiCommunity.GetSinglePosts:output_type -> api.community.v1.GetPostsReplyItem
	24, // 41: api.community.v1.WikiCommunity.GetPostsOrCommentGradesInfo:output_type -> api.community.v1.GetPostsReply
	22, // 42: api.community.v1.WikiCommunity.GetPostsApplaudAndCollect:output_type -> api.community.v1.GetPostsApplaudAndCollectReply
	17, // 43: api.community.v1.WikiCommunity.GetUserCollectPosts:output_type -> api.community.v1.GetUserCollectPostsReply
	12, // 44: api.community.v1.WikiCommunity.GetUserIdByPostsId:output_type -> api.community.v1.GetUserIdByPostsIdReply
	10, // 45: api.community.v1.WikiCommunity.GetPostsNumber:output_type -> api.community.v1.GetPostsNumberReply
	14, // 46: api.community.v1.WikiCommunity.GetPersonHomePostsPageList:output_type -> api.community.v1.GetPersonHomePostsPageListReply
	7,  // 47: api.community.v1.WikiCommunity.GetUserViewHistoryPosts:output_type -> api.community.v1.GetUserViewHistoryPostsReply
	4,  // 48: api.community.v1.WikiCommunity.GetPostsUserApplaud:output_type -> api.community.v1.GetUserPostsApplaudReply
	1,  // 49: api.community.v1.WikiCommunity.GetPostsReplyStatus:output_type -> api.community.v1.GetPostsReplyStatusReply
	31, // 50: api.community.v1.WikiCommunity.GetTopicRecommend:output_type -> api.community.v1.GetTopicRecommendReply
	34, // 51: api.community.v1.WikiCommunity.GetTopicDetail:output_type -> api.community.v1.GetTopicDetailReply
	36, // 52: api.community.v1.WikiCommunity.GetUserTopicCollectList:output_type -> api.community.v1.GetUserTopicCollectListReply
	40, // 53: api.community.v1.WikiCommunity.GetUserBusinessCountOrApplaudCount:output_type -> api.community.v1.GetUserBusinessCountOrApplaudCountReply
	42, // 54: api.community.v1.WikiCommunity.ActivityPageList:output_type -> api.community.v1.ActivityListReply
	45, // 55: api.community.v1.WikiCommunity.ActivityDetail:output_type -> api.community.v1.ActivityDetailReply
	47, // 56: api.community.v1.WikiCommunity.ActivityPostsPageList:output_type -> api.community.v1.ActivityPostsPageListReply
	49, // 57: api.community.v1.WikiCommunity.UserJoinActivity:output_type -> api.community.v1.EmptyResponse
	51, // 58: api.community.v1.WikiCommunity.GetTopicName:output_type -> api.community.v1.UserTopicNameReply
	38, // [38:59] is the sub-list for method output_type
	17, // [17:38] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_community_v1_wikicommunity_proto_init() }
func file_community_v1_wikicommunity_proto_init() {
	if File_community_v1_wikicommunity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_community_v1_wikicommunity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsReplyStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsReplyStatusReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsReplyStatusReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserPostsApplaudRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserPostsApplaudReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserPostsApplaudReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserViewHistoryPostsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserViewHistoryPostsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserViewHistoryPostsItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsNumberRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsNumberReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserIdByPostsIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserIdByPostsIdReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPersonHomePostsPageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPersonHomePostsPageListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCollectPostsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCollectPostsReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserCollectPostsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsOrCommentGradesInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSinglePostsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsApplaudRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsApplaudReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsApplaudAndCollectReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsApplaudAndCollectReplyCount); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostsSign); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostsTopicItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPostsReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopicRecommendRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopicRecommendReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TopicRecommend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopicDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTopicDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserTopicCollectListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserTopicCollectListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserTopicCollectListReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserBusinessCountOrApplaudCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserBusinessCountOrApplaudCountReplyItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserBusinessCountOrApplaudCountReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityListItemReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityPostsPageListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityPostsPageListReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserJoinActivityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserTopicNameRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_community_v1_wikicommunity_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserTopicNameReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_community_v1_wikicommunity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   52,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_community_v1_wikicommunity_proto_goTypes,
		DependencyIndexes: file_community_v1_wikicommunity_proto_depIdxs,
		MessageInfos:      file_community_v1_wikicommunity_proto_msgTypes,
	}.Build()
	File_community_v1_wikicommunity_proto = out.File
	file_community_v1_wikicommunity_proto_rawDesc = nil
	file_community_v1_wikicommunity_proto_goTypes = nil
	file_community_v1_wikicommunity_proto_depIdxs = nil
}
