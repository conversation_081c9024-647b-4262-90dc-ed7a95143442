// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: community/v1/wikicommunity.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	WikiCommunity_GetPostsInfo_FullMethodName                       = "/api.community.v1.WikiCommunity/GetPostsInfo"
	WikiCommunity_GetPostsApplaud_FullMethodName                    = "/api.community.v1.WikiCommunity/GetPostsApplaud"
	WikiCommunity_GetSinglePosts_FullMethodName                     = "/api.community.v1.WikiCommunity/GetSinglePosts"
	WikiCommunity_GetPostsOrCommentGradesInfo_FullMethodName        = "/api.community.v1.WikiCommunity/GetPostsOrCommentGradesInfo"
	WikiCommunity_GetPostsApplaudAndCollect_FullMethodName          = "/api.community.v1.WikiCommunity/GetPostsApplaudAndCollect"
	WikiCommunity_GetUserCollectPosts_FullMethodName                = "/api.community.v1.WikiCommunity/GetUserCollectPosts"
	WikiCommunity_GetUserIdByPostsId_FullMethodName                 = "/api.community.v1.WikiCommunity/GetUserIdByPostsId"
	WikiCommunity_GetPostsNumber_FullMethodName                     = "/api.community.v1.WikiCommunity/GetPostsNumber"
	WikiCommunity_GetPersonHomePostsPageList_FullMethodName         = "/api.community.v1.WikiCommunity/GetPersonHomePostsPageList"
	WikiCommunity_GetUserViewHistoryPosts_FullMethodName            = "/api.community.v1.WikiCommunity/GetUserViewHistoryPosts"
	WikiCommunity_GetPostsUserApplaud_FullMethodName                = "/api.community.v1.WikiCommunity/GetPostsUserApplaud"
	WikiCommunity_GetPostsReplyStatus_FullMethodName                = "/api.community.v1.WikiCommunity/GetPostsReplyStatus"
	WikiCommunity_GetTopicRecommend_FullMethodName                  = "/api.community.v1.WikiCommunity/GetTopicRecommend"
	WikiCommunity_GetTopicDetail_FullMethodName                     = "/api.community.v1.WikiCommunity/GetTopicDetail"
	WikiCommunity_GetUserTopicCollectList_FullMethodName            = "/api.community.v1.WikiCommunity/GetUserTopicCollectList"
	WikiCommunity_GetUserBusinessCountOrApplaudCount_FullMethodName = "/api.community.v1.WikiCommunity/GetUserBusinessCountOrApplaudCount"
	WikiCommunity_ActivityPageList_FullMethodName                   = "/api.community.v1.WikiCommunity/ActivityPageList"
	WikiCommunity_ActivityDetail_FullMethodName                     = "/api.community.v1.WikiCommunity/ActivityDetail"
	WikiCommunity_ActivityPostsPageList_FullMethodName              = "/api.community.v1.WikiCommunity/ActivityPostsPageList"
	WikiCommunity_UserJoinActivity_FullMethodName                   = "/api.community.v1.WikiCommunity/UserJoinActivity"
	WikiCommunity_GetTopicName_FullMethodName                       = "/api.community.v1.WikiCommunity/GetTopicName"
	WikiCommunity_GetSingleUserApplaudCount_FullMethodName          = "/api.community.v1.WikiCommunity/GetSingleUserApplaudCount"
)

// WikiCommunityClient is the client API for WikiCommunity service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WikiCommunityClient interface {
	//弃用
	GetPostsInfo(ctx context.Context, in *GetPostsRequest, opts ...grpc.CallOption) (*GetPostsReply, error)
	// 帖子收藏
	GetPostsApplaud(ctx context.Context, in *GetPostsApplaudRequest, opts ...grpc.CallOption) (*GetPostsApplaudReply, error)
	GetSinglePosts(ctx context.Context, in *GetSinglePostsRequest, opts ...grpc.CallOption) (*GetPostsReplyItem, error)
	//获取商业或者评论数据
	GetPostsOrCommentGradesInfo(ctx context.Context, in *GetPostsOrCommentGradesInfoRequest, opts ...grpc.CallOption) (*GetPostsReply, error)
	//获取帖子收藏和点赞
	GetPostsApplaudAndCollect(ctx context.Context, in *GetPostsApplaudRequest, opts ...grpc.CallOption) (*GetPostsApplaudAndCollectReply, error)
	GetUserCollectPosts(ctx context.Context, in *GetUserCollectPostsRequest, opts ...grpc.CallOption) (*GetUserCollectPostsReply, error)
	//根据帖子Id获取用户
	GetUserIdByPostsId(ctx context.Context, in *GetUserIdByPostsIdRequest, opts ...grpc.CallOption) (*GetUserIdByPostsIdReply, error)
	// 获取用户帖子数量
	GetPostsNumber(ctx context.Context, in *GetPostsNumberRequest, opts ...grpc.CallOption) (*GetPostsNumberReply, error)
	// 用户用户个人
	GetPersonHomePostsPageList(ctx context.Context, in *GetPersonHomePostsPageListRequest, opts ...grpc.CallOption) (*GetPersonHomePostsPageListReply, error)
	//用户浏览历史
	GetUserViewHistoryPosts(ctx context.Context, in *GetUserViewHistoryPostsRequest, opts ...grpc.CallOption) (*GetUserViewHistoryPostsReply, error)
	//获取帖子用户点赞数据
	GetPostsUserApplaud(ctx context.Context, in *GetUserPostsApplaudRequest, opts ...grpc.CallOption) (*GetUserPostsApplaudReply, error)
	GetPostsReplyStatus(ctx context.Context, in *GetPostsReplyStatusRequest, opts ...grpc.CallOption) (*GetPostsReplyStatusReply, error)
	GetTopicRecommend(ctx context.Context, in *GetTopicRecommendRequest, opts ...grpc.CallOption) (*GetTopicRecommendReply, error)
	GetTopicDetail(ctx context.Context, in *GetTopicDetailRequest, opts ...grpc.CallOption) (*GetTopicDetailReply, error)
	//GetUserTopicCollectList 用户收藏话题
	GetUserTopicCollectList(ctx context.Context, in *GetUserTopicCollectListRequest, opts ...grpc.CallOption) (*GetUserTopicCollectListReply, error)
	// 获取用户帖子数量和获赞数量
	GetUserBusinessCountOrApplaudCount(ctx context.Context, in *GetUserBusinessCountOrApplaudCountRequest, opts ...grpc.CallOption) (*GetUserBusinessCountOrApplaudCountReply, error)
	//活动列表
	ActivityPageList(ctx context.Context, in *ActivityListRequest, opts ...grpc.CallOption) (*ActivityListReply, error)
	//活动详情
	ActivityDetail(ctx context.Context, in *ActivityDetailRequest, opts ...grpc.CallOption) (*ActivityDetailReply, error)
	//活动广场
	ActivityPostsPageList(ctx context.Context, in *ActivityPostsPageListRequest, opts ...grpc.CallOption) (*ActivityPostsPageListReply, error)
	///参数活动
	UserJoinActivity(ctx context.Context, in *UserJoinActivityRequest, opts ...grpc.CallOption) (*EmptyResponse, error)
	///获取话题
	GetTopicName(ctx context.Context, in *UserTopicNameRequest, opts ...grpc.CallOption) (*UserTopicNameReply, error)
	// 获取单个用户获赞数量
	GetSingleUserApplaudCount(ctx context.Context, in *GetSingleUserApplaudCountRequest, opts ...grpc.CallOption) (*GetSingleUserApplaudCountReply, error)
}

type wikiCommunityClient struct {
	cc grpc.ClientConnInterface
}

func NewWikiCommunityClient(cc grpc.ClientConnInterface) WikiCommunityClient {
	return &wikiCommunityClient{cc}
}

func (c *wikiCommunityClient) GetPostsInfo(ctx context.Context, in *GetPostsRequest, opts ...grpc.CallOption) (*GetPostsReply, error) {
	out := new(GetPostsReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPostsApplaud(ctx context.Context, in *GetPostsApplaudRequest, opts ...grpc.CallOption) (*GetPostsApplaudReply, error) {
	out := new(GetPostsApplaudReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsApplaud_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetSinglePosts(ctx context.Context, in *GetSinglePostsRequest, opts ...grpc.CallOption) (*GetPostsReplyItem, error) {
	out := new(GetPostsReplyItem)
	err := c.cc.Invoke(ctx, WikiCommunity_GetSinglePosts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPostsOrCommentGradesInfo(ctx context.Context, in *GetPostsOrCommentGradesInfoRequest, opts ...grpc.CallOption) (*GetPostsReply, error) {
	out := new(GetPostsReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsOrCommentGradesInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPostsApplaudAndCollect(ctx context.Context, in *GetPostsApplaudRequest, opts ...grpc.CallOption) (*GetPostsApplaudAndCollectReply, error) {
	out := new(GetPostsApplaudAndCollectReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsApplaudAndCollect_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetUserCollectPosts(ctx context.Context, in *GetUserCollectPostsRequest, opts ...grpc.CallOption) (*GetUserCollectPostsReply, error) {
	out := new(GetUserCollectPostsReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetUserCollectPosts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetUserIdByPostsId(ctx context.Context, in *GetUserIdByPostsIdRequest, opts ...grpc.CallOption) (*GetUserIdByPostsIdReply, error) {
	out := new(GetUserIdByPostsIdReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetUserIdByPostsId_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPostsNumber(ctx context.Context, in *GetPostsNumberRequest, opts ...grpc.CallOption) (*GetPostsNumberReply, error) {
	out := new(GetPostsNumberReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPersonHomePostsPageList(ctx context.Context, in *GetPersonHomePostsPageListRequest, opts ...grpc.CallOption) (*GetPersonHomePostsPageListReply, error) {
	out := new(GetPersonHomePostsPageListReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPersonHomePostsPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetUserViewHistoryPosts(ctx context.Context, in *GetUserViewHistoryPostsRequest, opts ...grpc.CallOption) (*GetUserViewHistoryPostsReply, error) {
	out := new(GetUserViewHistoryPostsReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetUserViewHistoryPosts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPostsUserApplaud(ctx context.Context, in *GetUserPostsApplaudRequest, opts ...grpc.CallOption) (*GetUserPostsApplaudReply, error) {
	out := new(GetUserPostsApplaudReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsUserApplaud_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetPostsReplyStatus(ctx context.Context, in *GetPostsReplyStatusRequest, opts ...grpc.CallOption) (*GetPostsReplyStatusReply, error) {
	out := new(GetPostsReplyStatusReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetPostsReplyStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetTopicRecommend(ctx context.Context, in *GetTopicRecommendRequest, opts ...grpc.CallOption) (*GetTopicRecommendReply, error) {
	out := new(GetTopicRecommendReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetTopicRecommend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetTopicDetail(ctx context.Context, in *GetTopicDetailRequest, opts ...grpc.CallOption) (*GetTopicDetailReply, error) {
	out := new(GetTopicDetailReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetTopicDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetUserTopicCollectList(ctx context.Context, in *GetUserTopicCollectListRequest, opts ...grpc.CallOption) (*GetUserTopicCollectListReply, error) {
	out := new(GetUserTopicCollectListReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetUserTopicCollectList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetUserBusinessCountOrApplaudCount(ctx context.Context, in *GetUserBusinessCountOrApplaudCountRequest, opts ...grpc.CallOption) (*GetUserBusinessCountOrApplaudCountReply, error) {
	out := new(GetUserBusinessCountOrApplaudCountReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetUserBusinessCountOrApplaudCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) ActivityPageList(ctx context.Context, in *ActivityListRequest, opts ...grpc.CallOption) (*ActivityListReply, error) {
	out := new(ActivityListReply)
	err := c.cc.Invoke(ctx, WikiCommunity_ActivityPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) ActivityDetail(ctx context.Context, in *ActivityDetailRequest, opts ...grpc.CallOption) (*ActivityDetailReply, error) {
	out := new(ActivityDetailReply)
	err := c.cc.Invoke(ctx, WikiCommunity_ActivityDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) ActivityPostsPageList(ctx context.Context, in *ActivityPostsPageListRequest, opts ...grpc.CallOption) (*ActivityPostsPageListReply, error) {
	out := new(ActivityPostsPageListReply)
	err := c.cc.Invoke(ctx, WikiCommunity_ActivityPostsPageList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) UserJoinActivity(ctx context.Context, in *UserJoinActivityRequest, opts ...grpc.CallOption) (*EmptyResponse, error) {
	out := new(EmptyResponse)
	err := c.cc.Invoke(ctx, WikiCommunity_UserJoinActivity_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetTopicName(ctx context.Context, in *UserTopicNameRequest, opts ...grpc.CallOption) (*UserTopicNameReply, error) {
	out := new(UserTopicNameReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetTopicName_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wikiCommunityClient) GetSingleUserApplaudCount(ctx context.Context, in *GetSingleUserApplaudCountRequest, opts ...grpc.CallOption) (*GetSingleUserApplaudCountReply, error) {
	out := new(GetSingleUserApplaudCountReply)
	err := c.cc.Invoke(ctx, WikiCommunity_GetSingleUserApplaudCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WikiCommunityServer is the server API for WikiCommunity service.
// All implementations must embed UnimplementedWikiCommunityServer
// for forward compatibility
type WikiCommunityServer interface {
	//弃用
	GetPostsInfo(context.Context, *GetPostsRequest) (*GetPostsReply, error)
	// 帖子收藏
	GetPostsApplaud(context.Context, *GetPostsApplaudRequest) (*GetPostsApplaudReply, error)
	GetSinglePosts(context.Context, *GetSinglePostsRequest) (*GetPostsReplyItem, error)
	//获取商业或者评论数据
	GetPostsOrCommentGradesInfo(context.Context, *GetPostsOrCommentGradesInfoRequest) (*GetPostsReply, error)
	//获取帖子收藏和点赞
	GetPostsApplaudAndCollect(context.Context, *GetPostsApplaudRequest) (*GetPostsApplaudAndCollectReply, error)
	GetUserCollectPosts(context.Context, *GetUserCollectPostsRequest) (*GetUserCollectPostsReply, error)
	//根据帖子Id获取用户
	GetUserIdByPostsId(context.Context, *GetUserIdByPostsIdRequest) (*GetUserIdByPostsIdReply, error)
	// 获取用户帖子数量
	GetPostsNumber(context.Context, *GetPostsNumberRequest) (*GetPostsNumberReply, error)
	// 用户用户个人
	GetPersonHomePostsPageList(context.Context, *GetPersonHomePostsPageListRequest) (*GetPersonHomePostsPageListReply, error)
	//用户浏览历史
	GetUserViewHistoryPosts(context.Context, *GetUserViewHistoryPostsRequest) (*GetUserViewHistoryPostsReply, error)
	//获取帖子用户点赞数据
	GetPostsUserApplaud(context.Context, *GetUserPostsApplaudRequest) (*GetUserPostsApplaudReply, error)
	GetPostsReplyStatus(context.Context, *GetPostsReplyStatusRequest) (*GetPostsReplyStatusReply, error)
	GetTopicRecommend(context.Context, *GetTopicRecommendRequest) (*GetTopicRecommendReply, error)
	GetTopicDetail(context.Context, *GetTopicDetailRequest) (*GetTopicDetailReply, error)
	//GetUserTopicCollectList 用户收藏话题
	GetUserTopicCollectList(context.Context, *GetUserTopicCollectListRequest) (*GetUserTopicCollectListReply, error)
	// 获取用户帖子数量和获赞数量
	GetUserBusinessCountOrApplaudCount(context.Context, *GetUserBusinessCountOrApplaudCountRequest) (*GetUserBusinessCountOrApplaudCountReply, error)
	//活动列表
	ActivityPageList(context.Context, *ActivityListRequest) (*ActivityListReply, error)
	//活动详情
	ActivityDetail(context.Context, *ActivityDetailRequest) (*ActivityDetailReply, error)
	//活动广场
	ActivityPostsPageList(context.Context, *ActivityPostsPageListRequest) (*ActivityPostsPageListReply, error)
	///参数活动
	UserJoinActivity(context.Context, *UserJoinActivityRequest) (*EmptyResponse, error)
	///获取话题
	GetTopicName(context.Context, *UserTopicNameRequest) (*UserTopicNameReply, error)
	// 获取单个用户获赞数量
	GetSingleUserApplaudCount(context.Context, *GetSingleUserApplaudCountRequest) (*GetSingleUserApplaudCountReply, error)
	mustEmbedUnimplementedWikiCommunityServer()
}

// UnimplementedWikiCommunityServer must be embedded to have forward compatible implementations.
type UnimplementedWikiCommunityServer struct {
}

func (UnimplementedWikiCommunityServer) GetPostsInfo(context.Context, *GetPostsRequest) (*GetPostsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsInfo not implemented")
}
func (UnimplementedWikiCommunityServer) GetPostsApplaud(context.Context, *GetPostsApplaudRequest) (*GetPostsApplaudReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsApplaud not implemented")
}
func (UnimplementedWikiCommunityServer) GetSinglePosts(context.Context, *GetSinglePostsRequest) (*GetPostsReplyItem, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSinglePosts not implemented")
}
func (UnimplementedWikiCommunityServer) GetPostsOrCommentGradesInfo(context.Context, *GetPostsOrCommentGradesInfoRequest) (*GetPostsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsOrCommentGradesInfo not implemented")
}
func (UnimplementedWikiCommunityServer) GetPostsApplaudAndCollect(context.Context, *GetPostsApplaudRequest) (*GetPostsApplaudAndCollectReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsApplaudAndCollect not implemented")
}
func (UnimplementedWikiCommunityServer) GetUserCollectPosts(context.Context, *GetUserCollectPostsRequest) (*GetUserCollectPostsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCollectPosts not implemented")
}
func (UnimplementedWikiCommunityServer) GetUserIdByPostsId(context.Context, *GetUserIdByPostsIdRequest) (*GetUserIdByPostsIdReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserIdByPostsId not implemented")
}
func (UnimplementedWikiCommunityServer) GetPostsNumber(context.Context, *GetPostsNumberRequest) (*GetPostsNumberReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsNumber not implemented")
}
func (UnimplementedWikiCommunityServer) GetPersonHomePostsPageList(context.Context, *GetPersonHomePostsPageListRequest) (*GetPersonHomePostsPageListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPersonHomePostsPageList not implemented")
}
func (UnimplementedWikiCommunityServer) GetUserViewHistoryPosts(context.Context, *GetUserViewHistoryPostsRequest) (*GetUserViewHistoryPostsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserViewHistoryPosts not implemented")
}
func (UnimplementedWikiCommunityServer) GetPostsUserApplaud(context.Context, *GetUserPostsApplaudRequest) (*GetUserPostsApplaudReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsUserApplaud not implemented")
}
func (UnimplementedWikiCommunityServer) GetPostsReplyStatus(context.Context, *GetPostsReplyStatusRequest) (*GetPostsReplyStatusReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostsReplyStatus not implemented")
}
func (UnimplementedWikiCommunityServer) GetTopicRecommend(context.Context, *GetTopicRecommendRequest) (*GetTopicRecommendReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopicRecommend not implemented")
}
func (UnimplementedWikiCommunityServer) GetTopicDetail(context.Context, *GetTopicDetailRequest) (*GetTopicDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopicDetail not implemented")
}
func (UnimplementedWikiCommunityServer) GetUserTopicCollectList(context.Context, *GetUserTopicCollectListRequest) (*GetUserTopicCollectListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserTopicCollectList not implemented")
}
func (UnimplementedWikiCommunityServer) GetUserBusinessCountOrApplaudCount(context.Context, *GetUserBusinessCountOrApplaudCountRequest) (*GetUserBusinessCountOrApplaudCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserBusinessCountOrApplaudCount not implemented")
}
func (UnimplementedWikiCommunityServer) ActivityPageList(context.Context, *ActivityListRequest) (*ActivityListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityPageList not implemented")
}
func (UnimplementedWikiCommunityServer) ActivityDetail(context.Context, *ActivityDetailRequest) (*ActivityDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityDetail not implemented")
}
func (UnimplementedWikiCommunityServer) ActivityPostsPageList(context.Context, *ActivityPostsPageListRequest) (*ActivityPostsPageListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivityPostsPageList not implemented")
}
func (UnimplementedWikiCommunityServer) UserJoinActivity(context.Context, *UserJoinActivityRequest) (*EmptyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserJoinActivity not implemented")
}
func (UnimplementedWikiCommunityServer) GetTopicName(context.Context, *UserTopicNameRequest) (*UserTopicNameReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTopicName not implemented")
}
func (UnimplementedWikiCommunityServer) GetSingleUserApplaudCount(context.Context, *GetSingleUserApplaudCountRequest) (*GetSingleUserApplaudCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSingleUserApplaudCount not implemented")
}
func (UnimplementedWikiCommunityServer) mustEmbedUnimplementedWikiCommunityServer() {}

// UnsafeWikiCommunityServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WikiCommunityServer will
// result in compilation errors.
type UnsafeWikiCommunityServer interface {
	mustEmbedUnimplementedWikiCommunityServer()
}

func RegisterWikiCommunityServer(s grpc.ServiceRegistrar, srv WikiCommunityServer) {
	s.RegisterService(&WikiCommunity_ServiceDesc, srv)
}

func _WikiCommunity_GetPostsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsInfo(ctx, req.(*GetPostsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPostsApplaud_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsApplaudRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsApplaud(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsApplaud_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsApplaud(ctx, req.(*GetPostsApplaudRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetSinglePosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSinglePostsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetSinglePosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetSinglePosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetSinglePosts(ctx, req.(*GetSinglePostsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPostsOrCommentGradesInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsOrCommentGradesInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsOrCommentGradesInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsOrCommentGradesInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsOrCommentGradesInfo(ctx, req.(*GetPostsOrCommentGradesInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPostsApplaudAndCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsApplaudRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsApplaudAndCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsApplaudAndCollect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsApplaudAndCollect(ctx, req.(*GetPostsApplaudRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetUserCollectPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCollectPostsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetUserCollectPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetUserCollectPosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetUserCollectPosts(ctx, req.(*GetUserCollectPostsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetUserIdByPostsId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserIdByPostsIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetUserIdByPostsId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetUserIdByPostsId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetUserIdByPostsId(ctx, req.(*GetUserIdByPostsIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPostsNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsNumber(ctx, req.(*GetPostsNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPersonHomePostsPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonHomePostsPageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPersonHomePostsPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPersonHomePostsPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPersonHomePostsPageList(ctx, req.(*GetPersonHomePostsPageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetUserViewHistoryPosts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserViewHistoryPostsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetUserViewHistoryPosts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetUserViewHistoryPosts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetUserViewHistoryPosts(ctx, req.(*GetUserViewHistoryPostsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPostsUserApplaud_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserPostsApplaudRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsUserApplaud(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsUserApplaud_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsUserApplaud(ctx, req.(*GetUserPostsApplaudRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetPostsReplyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostsReplyStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetPostsReplyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetPostsReplyStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetPostsReplyStatus(ctx, req.(*GetPostsReplyStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetTopicRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicRecommendRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetTopicRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetTopicRecommend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetTopicRecommend(ctx, req.(*GetTopicRecommendRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetTopicDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetTopicDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetTopicDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetTopicDetail(ctx, req.(*GetTopicDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetUserTopicCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTopicCollectListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetUserTopicCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetUserTopicCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetUserTopicCollectList(ctx, req.(*GetUserTopicCollectListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetUserBusinessCountOrApplaudCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBusinessCountOrApplaudCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetUserBusinessCountOrApplaudCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetUserBusinessCountOrApplaudCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetUserBusinessCountOrApplaudCount(ctx, req.(*GetUserBusinessCountOrApplaudCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_ActivityPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).ActivityPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_ActivityPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).ActivityPageList(ctx, req.(*ActivityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_ActivityDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).ActivityDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_ActivityDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).ActivityDetail(ctx, req.(*ActivityDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_ActivityPostsPageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivityPostsPageListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).ActivityPostsPageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_ActivityPostsPageList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).ActivityPostsPageList(ctx, req.(*ActivityPostsPageListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_UserJoinActivity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserJoinActivityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).UserJoinActivity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_UserJoinActivity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).UserJoinActivity(ctx, req.(*UserJoinActivityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetTopicName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserTopicNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetTopicName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetTopicName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetTopicName(ctx, req.(*UserTopicNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WikiCommunity_GetSingleUserApplaudCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingleUserApplaudCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WikiCommunityServer).GetSingleUserApplaudCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WikiCommunity_GetSingleUserApplaudCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WikiCommunityServer).GetSingleUserApplaudCount(ctx, req.(*GetSingleUserApplaudCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WikiCommunity_ServiceDesc is the grpc.ServiceDesc for WikiCommunity service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WikiCommunity_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.community.v1.WikiCommunity",
	HandlerType: (*WikiCommunityServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPostsInfo",
			Handler:    _WikiCommunity_GetPostsInfo_Handler,
		},
		{
			MethodName: "GetPostsApplaud",
			Handler:    _WikiCommunity_GetPostsApplaud_Handler,
		},
		{
			MethodName: "GetSinglePosts",
			Handler:    _WikiCommunity_GetSinglePosts_Handler,
		},
		{
			MethodName: "GetPostsOrCommentGradesInfo",
			Handler:    _WikiCommunity_GetPostsOrCommentGradesInfo_Handler,
		},
		{
			MethodName: "GetPostsApplaudAndCollect",
			Handler:    _WikiCommunity_GetPostsApplaudAndCollect_Handler,
		},
		{
			MethodName: "GetUserCollectPosts",
			Handler:    _WikiCommunity_GetUserCollectPosts_Handler,
		},
		{
			MethodName: "GetUserIdByPostsId",
			Handler:    _WikiCommunity_GetUserIdByPostsId_Handler,
		},
		{
			MethodName: "GetPostsNumber",
			Handler:    _WikiCommunity_GetPostsNumber_Handler,
		},
		{
			MethodName: "GetPersonHomePostsPageList",
			Handler:    _WikiCommunity_GetPersonHomePostsPageList_Handler,
		},
		{
			MethodName: "GetUserViewHistoryPosts",
			Handler:    _WikiCommunity_GetUserViewHistoryPosts_Handler,
		},
		{
			MethodName: "GetPostsUserApplaud",
			Handler:    _WikiCommunity_GetPostsUserApplaud_Handler,
		},
		{
			MethodName: "GetPostsReplyStatus",
			Handler:    _WikiCommunity_GetPostsReplyStatus_Handler,
		},
		{
			MethodName: "GetTopicRecommend",
			Handler:    _WikiCommunity_GetTopicRecommend_Handler,
		},
		{
			MethodName: "GetTopicDetail",
			Handler:    _WikiCommunity_GetTopicDetail_Handler,
		},
		{
			MethodName: "GetUserTopicCollectList",
			Handler:    _WikiCommunity_GetUserTopicCollectList_Handler,
		},
		{
			MethodName: "GetUserBusinessCountOrApplaudCount",
			Handler:    _WikiCommunity_GetUserBusinessCountOrApplaudCount_Handler,
		},
		{
			MethodName: "ActivityPageList",
			Handler:    _WikiCommunity_ActivityPageList_Handler,
		},
		{
			MethodName: "ActivityDetail",
			Handler:    _WikiCommunity_ActivityDetail_Handler,
		},
		{
			MethodName: "ActivityPostsPageList",
			Handler:    _WikiCommunity_ActivityPostsPageList_Handler,
		},
		{
			MethodName: "UserJoinActivity",
			Handler:    _WikiCommunity_UserJoinActivity_Handler,
		},
		{
			MethodName: "GetTopicName",
			Handler:    _WikiCommunity_GetTopicName_Handler,
		},
		{
			MethodName: "GetSingleUserApplaudCount",
			Handler:    _WikiCommunity_GetSingleUserApplaudCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "community/v1/wikicommunity.proto",
}
