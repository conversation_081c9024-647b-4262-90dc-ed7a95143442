// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: community/v1/wikicommunity.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationWikiCommunityActivityDetail = "/api.community.v1.WikiCommunity/ActivityDetail"
const OperationWikiCommunityActivityPageList = "/api.community.v1.WikiCommunity/ActivityPageList"
const OperationWikiCommunityActivityPostsPageList = "/api.community.v1.WikiCommunity/ActivityPostsPageList"
const OperationWikiCommunityGetPersonHomePostsPageList = "/api.community.v1.WikiCommunity/GetPersonHomePostsPageList"
const OperationWikiCommunityGetPostsApplaud = "/api.community.v1.WikiCommunity/GetPostsApplaud"
const OperationWikiCommunityGetPostsApplaudAndCollect = "/api.community.v1.WikiCommunity/GetPostsApplaudAndCollect"
const OperationWikiCommunityGetPostsInfo = "/api.community.v1.WikiCommunity/GetPostsInfo"
const OperationWikiCommunityGetPostsNumber = "/api.community.v1.WikiCommunity/GetPostsNumber"
const OperationWikiCommunityGetPostsOrCommentGradesInfo = "/api.community.v1.WikiCommunity/GetPostsOrCommentGradesInfo"
const OperationWikiCommunityGetPostsReplyStatus = "/api.community.v1.WikiCommunity/GetPostsReplyStatus"
const OperationWikiCommunityGetPostsUserApplaud = "/api.community.v1.WikiCommunity/GetPostsUserApplaud"
const OperationWikiCommunityGetSinglePosts = "/api.community.v1.WikiCommunity/GetSinglePosts"
const OperationWikiCommunityGetTopicDetail = "/api.community.v1.WikiCommunity/GetTopicDetail"
const OperationWikiCommunityGetTopicName = "/api.community.v1.WikiCommunity/GetTopicName"
const OperationWikiCommunityGetTopicRecommend = "/api.community.v1.WikiCommunity/GetTopicRecommend"
const OperationWikiCommunityGetUserBusinessCountOrApplaudCount = "/api.community.v1.WikiCommunity/GetUserBusinessCountOrApplaudCount"
const OperationWikiCommunityGetUserCollectPosts = "/api.community.v1.WikiCommunity/GetUserCollectPosts"
const OperationWikiCommunityGetUserIdByPostsId = "/api.community.v1.WikiCommunity/GetUserIdByPostsId"
const OperationWikiCommunityGetUserTopicCollectList = "/api.community.v1.WikiCommunity/GetUserTopicCollectList"
const OperationWikiCommunityGetUserViewHistoryPosts = "/api.community.v1.WikiCommunity/GetUserViewHistoryPosts"
const OperationWikiCommunityUserJoinActivity = "/api.community.v1.WikiCommunity/UserJoinActivity"

type WikiCommunityHTTPServer interface {
	// ActivityDetail活动详情
	ActivityDetail(context.Context, *ActivityDetailRequest) (*ActivityDetailReply, error)
	// ActivityPageList活动列表
	ActivityPageList(context.Context, *ActivityListRequest) (*ActivityListReply, error)
	// ActivityPostsPageList活动广场
	ActivityPostsPageList(context.Context, *ActivityPostsPageListRequest) (*ActivityPostsPageListReply, error)
	// GetPersonHomePostsPageList 用户用户个人
	GetPersonHomePostsPageList(context.Context, *GetPersonHomePostsPageListRequest) (*GetPersonHomePostsPageListReply, error)
	// GetPostsApplaud 帖子收藏
	GetPostsApplaud(context.Context, *GetPostsApplaudRequest) (*GetPostsApplaudReply, error)
	// GetPostsApplaudAndCollect获取帖子收藏和点赞
	GetPostsApplaudAndCollect(context.Context, *GetPostsApplaudRequest) (*GetPostsApplaudAndCollectReply, error)
	// GetPostsInfo弃用
	GetPostsInfo(context.Context, *GetPostsRequest) (*GetPostsReply, error)
	// GetPostsNumber 获取用户帖子数量
	GetPostsNumber(context.Context, *GetPostsNumberRequest) (*GetPostsNumberReply, error)
	// GetPostsOrCommentGradesInfo获取商业或者评论数据
	GetPostsOrCommentGradesInfo(context.Context, *GetPostsOrCommentGradesInfoRequest) (*GetPostsReply, error)
	GetPostsReplyStatus(context.Context, *GetPostsReplyStatusRequest) (*GetPostsReplyStatusReply, error)
	// GetPostsUserApplaud获取帖子用户点赞数据
	GetPostsUserApplaud(context.Context, *GetUserPostsApplaudRequest) (*GetUserPostsApplaudReply, error)
	GetSinglePosts(context.Context, *GetSinglePostsRequest) (*GetPostsReplyItem, error)
	GetTopicDetail(context.Context, *GetTopicDetailRequest) (*GetTopicDetailReply, error)
	// GetTopicName/获取话题
	GetTopicName(context.Context, *UserTopicNameRequest) (*UserTopicNameReply, error)
	GetTopicRecommend(context.Context, *GetTopicRecommendRequest) (*GetTopicRecommendReply, error)
	// GetUserBusinessCountOrApplaudCount 获取用户帖子数量和获赞数量
	GetUserBusinessCountOrApplaudCount(context.Context, *GetUserBusinessCountOrApplaudCountRequest) (*GetUserBusinessCountOrApplaudCountReply, error)
	GetUserCollectPosts(context.Context, *GetUserCollectPostsRequest) (*GetUserCollectPostsReply, error)
	// GetUserIdByPostsId根据帖子Id获取用户
	GetUserIdByPostsId(context.Context, *GetUserIdByPostsIdRequest) (*GetUserIdByPostsIdReply, error)
	// GetUserTopicCollectListGetUserTopicCollectList 用户收藏话题
	GetUserTopicCollectList(context.Context, *GetUserTopicCollectListRequest) (*GetUserTopicCollectListReply, error)
	// GetUserViewHistoryPosts用户浏览历史
	GetUserViewHistoryPosts(context.Context, *GetUserViewHistoryPostsRequest) (*GetUserViewHistoryPostsReply, error)
	// UserJoinActivity/参数活动
	UserJoinActivity(context.Context, *UserJoinActivityRequest) (*EmptyResponse, error)
}

func RegisterWikiCommunityHTTPServer(s *http.Server, srv WikiCommunityHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/app/community/getpostsinfo", _WikiCommunity_GetPostsInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/community/GetPostsApplaud", _WikiCommunity_GetPostsApplaud0_HTTP_Handler(srv))
	r.GET("/v1/app/community/GetSinglePosts", _WikiCommunity_GetSinglePosts0_HTTP_Handler(srv))
	r.POST("/v1/app/community/getpostsorcommentgradesinfo", _WikiCommunity_GetPostsOrCommentGradesInfo0_HTTP_Handler(srv))
	r.POST("/v1/app/community/getpostsapplaudandcollect", _WikiCommunity_GetPostsApplaudAndCollect0_HTTP_Handler(srv))
	r.GET("/v1/app/community/getusercollectposts", _WikiCommunity_GetUserCollectPosts0_HTTP_Handler(srv))
	r.GET("/v1/app/community/getuseridbypostsid", _WikiCommunity_GetUserIdByPostsId0_HTTP_Handler(srv))
	r.GET("/v1/app/community/getpostsnumber", _WikiCommunity_GetPostsNumber0_HTTP_Handler(srv))
	r.GET("/v1/app/community/getpersonhomepostspagelist", _WikiCommunity_GetPersonHomePostsPageList0_HTTP_Handler(srv))
	r.GET("/v1/app/community/getuserviewhistoryposts", _WikiCommunity_GetUserViewHistoryPosts0_HTTP_Handler(srv))
	r.POST("/v1/app/community/getpostsuserapplaud", _WikiCommunity_GetPostsUserApplaud0_HTTP_Handler(srv))
	r.POST("/v1/app/community/getpostsreplystatus", _WikiCommunity_GetPostsReplyStatus0_HTTP_Handler(srv))
	r.GET("/v1/app/community/gettopicrecommend", _WikiCommunity_GetTopicRecommend1_HTTP_Handler(srv))
	r.GET("/v1/app/community/gettopicdetail", _WikiCommunity_GetTopicDetail1_HTTP_Handler(srv))
	r.GET("/v1/app/community/getusercollectlist", _WikiCommunity_GetUserTopicCollectList0_HTTP_Handler(srv))
	r.POST("/v1/app/community/getuserbusinesscountorapplaudcount", _WikiCommunity_GetUserBusinessCountOrApplaudCount0_HTTP_Handler(srv))
	r.GET("/v1/app/community/activitypagelist", _WikiCommunity_ActivityPageList1_HTTP_Handler(srv))
	r.GET("/v1/app/community/ActivityDetail", _WikiCommunity_ActivityDetail1_HTTP_Handler(srv))
	r.GET("/v1/app/community/activitypostspagelist", _WikiCommunity_ActivityPostsPageList1_HTTP_Handler(srv))
	r.POST("/v1/app/community/userjoinactivity", _WikiCommunity_UserJoinActivity1_HTTP_Handler(srv))
	r.GET("/v1/app/community/gettopicname", _WikiCommunity_GetTopicName0_HTTP_Handler(srv))
}

func _WikiCommunity_GetPostsInfo0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPostsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsInfo(ctx, req.(*GetPostsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPostsApplaud0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPostsApplaudRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsApplaud)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsApplaud(ctx, req.(*GetPostsApplaudRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsApplaudReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetSinglePosts0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSinglePostsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetSinglePosts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSinglePosts(ctx, req.(*GetSinglePostsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsReplyItem)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPostsOrCommentGradesInfo0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPostsOrCommentGradesInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsOrCommentGradesInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsOrCommentGradesInfo(ctx, req.(*GetPostsOrCommentGradesInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPostsApplaudAndCollect0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPostsApplaudRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsApplaudAndCollect)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsApplaudAndCollect(ctx, req.(*GetPostsApplaudRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsApplaudAndCollectReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetUserCollectPosts0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserCollectPostsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetUserCollectPosts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserCollectPosts(ctx, req.(*GetUserCollectPostsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserCollectPostsReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetUserIdByPostsId0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserIdByPostsIdRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetUserIdByPostsId)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserIdByPostsId(ctx, req.(*GetUserIdByPostsIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserIdByPostsIdReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPostsNumber0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPostsNumberRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsNumber)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsNumber(ctx, req.(*GetPostsNumberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsNumberReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPersonHomePostsPageList0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPersonHomePostsPageListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPersonHomePostsPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPersonHomePostsPageList(ctx, req.(*GetPersonHomePostsPageListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPersonHomePostsPageListReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetUserViewHistoryPosts0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserViewHistoryPostsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetUserViewHistoryPosts)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserViewHistoryPosts(ctx, req.(*GetUserViewHistoryPostsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserViewHistoryPostsReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPostsUserApplaud0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserPostsApplaudRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsUserApplaud)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsUserApplaud(ctx, req.(*GetUserPostsApplaudRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserPostsApplaudReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetPostsReplyStatus0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPostsReplyStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetPostsReplyStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPostsReplyStatus(ctx, req.(*GetPostsReplyStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPostsReplyStatusReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetTopicRecommend1_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTopicRecommendRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetTopicRecommend)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTopicRecommend(ctx, req.(*GetTopicRecommendRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTopicRecommendReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetTopicDetail1_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetTopicDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetTopicDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTopicDetail(ctx, req.(*GetTopicDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetTopicDetailReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetUserTopicCollectList0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserTopicCollectListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetUserTopicCollectList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserTopicCollectList(ctx, req.(*GetUserTopicCollectListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserTopicCollectListReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetUserBusinessCountOrApplaudCount0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserBusinessCountOrApplaudCountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetUserBusinessCountOrApplaudCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserBusinessCountOrApplaudCount(ctx, req.(*GetUserBusinessCountOrApplaudCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserBusinessCountOrApplaudCountReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_ActivityPageList1_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityActivityPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ActivityPageList(ctx, req.(*ActivityListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ActivityListReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_ActivityDetail1_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityActivityDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ActivityDetail(ctx, req.(*ActivityDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ActivityDetailReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_ActivityPostsPageList1_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ActivityPostsPageListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityActivityPostsPageList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ActivityPostsPageList(ctx, req.(*ActivityPostsPageListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ActivityPostsPageListReply)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_UserJoinActivity1_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserJoinActivityRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityUserJoinActivity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserJoinActivity(ctx, req.(*UserJoinActivityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EmptyResponse)
		return ctx.Result(200, reply)
	}
}

func _WikiCommunity_GetTopicName0_HTTP_Handler(srv WikiCommunityHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UserTopicNameRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationWikiCommunityGetTopicName)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetTopicName(ctx, req.(*UserTopicNameRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserTopicNameReply)
		return ctx.Result(200, reply)
	}
}

type WikiCommunityHTTPClient interface {
	ActivityDetail(ctx context.Context, req *ActivityDetailRequest, opts ...http.CallOption) (rsp *ActivityDetailReply, err error)
	ActivityPageList(ctx context.Context, req *ActivityListRequest, opts ...http.CallOption) (rsp *ActivityListReply, err error)
	ActivityPostsPageList(ctx context.Context, req *ActivityPostsPageListRequest, opts ...http.CallOption) (rsp *ActivityPostsPageListReply, err error)
	GetPersonHomePostsPageList(ctx context.Context, req *GetPersonHomePostsPageListRequest, opts ...http.CallOption) (rsp *GetPersonHomePostsPageListReply, err error)
	GetPostsApplaud(ctx context.Context, req *GetPostsApplaudRequest, opts ...http.CallOption) (rsp *GetPostsApplaudReply, err error)
	GetPostsApplaudAndCollect(ctx context.Context, req *GetPostsApplaudRequest, opts ...http.CallOption) (rsp *GetPostsApplaudAndCollectReply, err error)
	GetPostsInfo(ctx context.Context, req *GetPostsRequest, opts ...http.CallOption) (rsp *GetPostsReply, err error)
	GetPostsNumber(ctx context.Context, req *GetPostsNumberRequest, opts ...http.CallOption) (rsp *GetPostsNumberReply, err error)
	GetPostsOrCommentGradesInfo(ctx context.Context, req *GetPostsOrCommentGradesInfoRequest, opts ...http.CallOption) (rsp *GetPostsReply, err error)
	GetPostsReplyStatus(ctx context.Context, req *GetPostsReplyStatusRequest, opts ...http.CallOption) (rsp *GetPostsReplyStatusReply, err error)
	GetPostsUserApplaud(ctx context.Context, req *GetUserPostsApplaudRequest, opts ...http.CallOption) (rsp *GetUserPostsApplaudReply, err error)
	GetSinglePosts(ctx context.Context, req *GetSinglePostsRequest, opts ...http.CallOption) (rsp *GetPostsReplyItem, err error)
	GetTopicDetail(ctx context.Context, req *GetTopicDetailRequest, opts ...http.CallOption) (rsp *GetTopicDetailReply, err error)
	GetTopicName(ctx context.Context, req *UserTopicNameRequest, opts ...http.CallOption) (rsp *UserTopicNameReply, err error)
	GetTopicRecommend(ctx context.Context, req *GetTopicRecommendRequest, opts ...http.CallOption) (rsp *GetTopicRecommendReply, err error)
	GetUserBusinessCountOrApplaudCount(ctx context.Context, req *GetUserBusinessCountOrApplaudCountRequest, opts ...http.CallOption) (rsp *GetUserBusinessCountOrApplaudCountReply, err error)
	GetUserCollectPosts(ctx context.Context, req *GetUserCollectPostsRequest, opts ...http.CallOption) (rsp *GetUserCollectPostsReply, err error)
	GetUserIdByPostsId(ctx context.Context, req *GetUserIdByPostsIdRequest, opts ...http.CallOption) (rsp *GetUserIdByPostsIdReply, err error)
	GetUserTopicCollectList(ctx context.Context, req *GetUserTopicCollectListRequest, opts ...http.CallOption) (rsp *GetUserTopicCollectListReply, err error)
	GetUserViewHistoryPosts(ctx context.Context, req *GetUserViewHistoryPostsRequest, opts ...http.CallOption) (rsp *GetUserViewHistoryPostsReply, err error)
	UserJoinActivity(ctx context.Context, req *UserJoinActivityRequest, opts ...http.CallOption) (rsp *EmptyResponse, err error)
}

type WikiCommunityHTTPClientImpl struct {
	cc *http.Client
}

func NewWikiCommunityHTTPClient(client *http.Client) WikiCommunityHTTPClient {
	return &WikiCommunityHTTPClientImpl{client}
}

func (c *WikiCommunityHTTPClientImpl) ActivityDetail(ctx context.Context, in *ActivityDetailRequest, opts ...http.CallOption) (*ActivityDetailReply, error) {
	var out ActivityDetailReply
	pattern := "/v1/app/community/ActivityDetail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityActivityDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) ActivityPageList(ctx context.Context, in *ActivityListRequest, opts ...http.CallOption) (*ActivityListReply, error) {
	var out ActivityListReply
	pattern := "/v1/app/community/activitypagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityActivityPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) ActivityPostsPageList(ctx context.Context, in *ActivityPostsPageListRequest, opts ...http.CallOption) (*ActivityPostsPageListReply, error) {
	var out ActivityPostsPageListReply
	pattern := "/v1/app/community/activitypostspagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityActivityPostsPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPersonHomePostsPageList(ctx context.Context, in *GetPersonHomePostsPageListRequest, opts ...http.CallOption) (*GetPersonHomePostsPageListReply, error) {
	var out GetPersonHomePostsPageListReply
	pattern := "/v1/app/community/getpersonhomepostspagelist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPersonHomePostsPageList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsApplaud(ctx context.Context, in *GetPostsApplaudRequest, opts ...http.CallOption) (*GetPostsApplaudReply, error) {
	var out GetPostsApplaudReply
	pattern := "/v1/app/community/GetPostsApplaud"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsApplaud))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsApplaudAndCollect(ctx context.Context, in *GetPostsApplaudRequest, opts ...http.CallOption) (*GetPostsApplaudAndCollectReply, error) {
	var out GetPostsApplaudAndCollectReply
	pattern := "/v1/app/community/getpostsapplaudandcollect"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsApplaudAndCollect))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsInfo(ctx context.Context, in *GetPostsRequest, opts ...http.CallOption) (*GetPostsReply, error) {
	var out GetPostsReply
	pattern := "/v1/app/community/getpostsinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsNumber(ctx context.Context, in *GetPostsNumberRequest, opts ...http.CallOption) (*GetPostsNumberReply, error) {
	var out GetPostsNumberReply
	pattern := "/v1/app/community/getpostsnumber"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsNumber))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsOrCommentGradesInfo(ctx context.Context, in *GetPostsOrCommentGradesInfoRequest, opts ...http.CallOption) (*GetPostsReply, error) {
	var out GetPostsReply
	pattern := "/v1/app/community/getpostsorcommentgradesinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsOrCommentGradesInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsReplyStatus(ctx context.Context, in *GetPostsReplyStatusRequest, opts ...http.CallOption) (*GetPostsReplyStatusReply, error) {
	var out GetPostsReplyStatusReply
	pattern := "/v1/app/community/getpostsreplystatus"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsReplyStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetPostsUserApplaud(ctx context.Context, in *GetUserPostsApplaudRequest, opts ...http.CallOption) (*GetUserPostsApplaudReply, error) {
	var out GetUserPostsApplaudReply
	pattern := "/v1/app/community/getpostsuserapplaud"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetPostsUserApplaud))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetSinglePosts(ctx context.Context, in *GetSinglePostsRequest, opts ...http.CallOption) (*GetPostsReplyItem, error) {
	var out GetPostsReplyItem
	pattern := "/v1/app/community/GetSinglePosts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetSinglePosts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetTopicDetail(ctx context.Context, in *GetTopicDetailRequest, opts ...http.CallOption) (*GetTopicDetailReply, error) {
	var out GetTopicDetailReply
	pattern := "/v1/app/community/gettopicdetail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetTopicDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetTopicName(ctx context.Context, in *UserTopicNameRequest, opts ...http.CallOption) (*UserTopicNameReply, error) {
	var out UserTopicNameReply
	pattern := "/v1/app/community/gettopicname"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetTopicName))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetTopicRecommend(ctx context.Context, in *GetTopicRecommendRequest, opts ...http.CallOption) (*GetTopicRecommendReply, error) {
	var out GetTopicRecommendReply
	pattern := "/v1/app/community/gettopicrecommend"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetTopicRecommend))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetUserBusinessCountOrApplaudCount(ctx context.Context, in *GetUserBusinessCountOrApplaudCountRequest, opts ...http.CallOption) (*GetUserBusinessCountOrApplaudCountReply, error) {
	var out GetUserBusinessCountOrApplaudCountReply
	pattern := "/v1/app/community/getuserbusinesscountorapplaudcount"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityGetUserBusinessCountOrApplaudCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetUserCollectPosts(ctx context.Context, in *GetUserCollectPostsRequest, opts ...http.CallOption) (*GetUserCollectPostsReply, error) {
	var out GetUserCollectPostsReply
	pattern := "/v1/app/community/getusercollectposts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetUserCollectPosts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetUserIdByPostsId(ctx context.Context, in *GetUserIdByPostsIdRequest, opts ...http.CallOption) (*GetUserIdByPostsIdReply, error) {
	var out GetUserIdByPostsIdReply
	pattern := "/v1/app/community/getuseridbypostsid"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetUserIdByPostsId))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetUserTopicCollectList(ctx context.Context, in *GetUserTopicCollectListRequest, opts ...http.CallOption) (*GetUserTopicCollectListReply, error) {
	var out GetUserTopicCollectListReply
	pattern := "/v1/app/community/getusercollectlist"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetUserTopicCollectList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) GetUserViewHistoryPosts(ctx context.Context, in *GetUserViewHistoryPostsRequest, opts ...http.CallOption) (*GetUserViewHistoryPostsReply, error) {
	var out GetUserViewHistoryPostsReply
	pattern := "/v1/app/community/getuserviewhistoryposts"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationWikiCommunityGetUserViewHistoryPosts))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *WikiCommunityHTTPClientImpl) UserJoinActivity(ctx context.Context, in *UserJoinActivityRequest, opts ...http.CallOption) (*EmptyResponse, error) {
	var out EmptyResponse
	pattern := "/v1/app/community/userjoinactivity"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationWikiCommunityUserJoinActivity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
