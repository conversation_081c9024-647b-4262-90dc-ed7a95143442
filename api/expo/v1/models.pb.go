// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: expo/v1/models.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "gold_store/api/common"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 赞助等级
type SponsorLevel int32

const (
	SponsorLevel_SponsorLevel_NONE     SponsorLevel = 0 // 无赞助
	SponsorLevel_SponsorLevel_SILVER   SponsorLevel = 1 // 白银
	SponsorLevel_SponsorLevel_GOLD     SponsorLevel = 2 // 黄金
	SponsorLevel_SponsorLevel_PLATINUM SponsorLevel = 3 // 铂金
	SponsorLevel_SponsorLevel_DIAMOND  SponsorLevel = 4 // 钻石
	SponsorLevel_SponsorLevel_GOLOBAL  SponsorLevel = 5 // 全球
)

// Enum value maps for SponsorLevel.
var (
	SponsorLevel_name = map[int32]string{
		0: "SponsorLevel_NONE",
		1: "SponsorLevel_SILVER",
		2: "SponsorLevel_GOLD",
		3: "SponsorLevel_PLATINUM",
		4: "SponsorLevel_DIAMOND",
		5: "SponsorLevel_GOLOBAL",
	}
	SponsorLevel_value = map[string]int32{
		"SponsorLevel_NONE":     0,
		"SponsorLevel_SILVER":   1,
		"SponsorLevel_GOLD":     2,
		"SponsorLevel_PLATINUM": 3,
		"SponsorLevel_DIAMOND":  4,
		"SponsorLevel_GOLOBAL":  5,
	}
)

func (x SponsorLevel) Enum() *SponsorLevel {
	p := new(SponsorLevel)
	*p = x
	return p
}

func (x SponsorLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SponsorLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[0].Descriptor()
}

func (SponsorLevel) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[0]
}

func (x SponsorLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SponsorLevel.Descriptor instead.
func (SponsorLevel) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{0}
}

type Industry int32

const (
	Industry_INDUSTRY_UNKNOWN Industry = 0 // 未知
	Industry_INDUSTRY_STOCK   Industry = 1 // Stock
	Industry_INDUSTRY_FOREX   Industry = 2 // Forex
	Industry_INDUSTRY_CRYPTO  Industry = 3 // Crypto
	Industry_INDUSTRY_FINTECH Industry = 4 // Fintech
)

// Enum value maps for Industry.
var (
	Industry_name = map[int32]string{
		0: "INDUSTRY_UNKNOWN",
		1: "INDUSTRY_STOCK",
		2: "INDUSTRY_FOREX",
		3: "INDUSTRY_CRYPTO",
		4: "INDUSTRY_FINTECH",
	}
	Industry_value = map[string]int32{
		"INDUSTRY_UNKNOWN": 0,
		"INDUSTRY_STOCK":   1,
		"INDUSTRY_FOREX":   2,
		"INDUSTRY_CRYPTO":  3,
		"INDUSTRY_FINTECH": 4,
	}
)

func (x Industry) Enum() *Industry {
	p := new(Industry)
	*p = x
	return p
}

func (x Industry) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Industry) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[1].Descriptor()
}

func (Industry) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[1]
}

func (x Industry) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Industry.Descriptor instead.
func (Industry) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{1}
}

type Identity int32

const (
	Identity_IDENTITY_UNKNOWN          Identity = 0 // 未知
	Identity_IDENTITY_TRADER           Identity = 1 // 交易商
	Identity_IDENTITY_INVESTOR         Identity = 2 // 投资者
	Identity_IDENTITY_SERVICE_PROVIDER Identity = 3 // 服务商
	Identity_IDENTITY_KOL              Identity = 4 // KOL
)

// Enum value maps for Identity.
var (
	Identity_name = map[int32]string{
		0: "IDENTITY_UNKNOWN",
		1: "IDENTITY_TRADER",
		2: "IDENTITY_INVESTOR",
		3: "IDENTITY_SERVICE_PROVIDER",
		4: "IDENTITY_KOL",
	}
	Identity_value = map[string]int32{
		"IDENTITY_UNKNOWN":          0,
		"IDENTITY_TRADER":           1,
		"IDENTITY_INVESTOR":         2,
		"IDENTITY_SERVICE_PROVIDER": 3,
		"IDENTITY_KOL":              4,
	}
)

func (x Identity) Enum() *Identity {
	p := new(Identity)
	*p = x
	return p
}

func (x Identity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Identity) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[2].Descriptor()
}

func (Identity) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[2]
}

func (x Identity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Identity.Descriptor instead.
func (Identity) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{2}
}

type SubIdentity int32

const (
	SubIdentity_SUB_IDENTITY_UNKNOWN          SubIdentity = 0     // 未知
	SubIdentity_SUB_IDENTITY_FOREX            SubIdentity = 10001 // Forex Broker
	SubIdentity_SUB_IDENTITY_SERVICE_PROVIDER SubIdentity = 30001 // Service Provider（Press/Media/Cloud/Bank/Wealth management/CRM）
	SubIdentity_SUB_IDENTITY_FINTECH          SubIdentity = 30002 // Fintech（Payment/Al/Liquidity/Trading platform）
	SubIdentity_SUB_IDENTITY_CRYPTO           SubIdentity = 30003 // Crypto / Digital Assets
	SubIdentity_SUB_IDENTITY_SERVICE_IB       SubIdentity = 30004 // IB / Affiliate
	SubIdentity_SUB_IDENTITY_INVESTOR         SubIdentity = 30005 // Investor / VC
	SubIdentity_SUB_IDENTITY_TRADER           SubIdentity = 30006 // Trader
	SubIdentity_SUB_IDENTITY_OTHER            SubIdentity = 30007 // Other
	SubIdentity_SUB_IDENTITY_KOL              SubIdentity = 40001 // KOL
)

// Enum value maps for SubIdentity.
var (
	SubIdentity_name = map[int32]string{
		0:     "SUB_IDENTITY_UNKNOWN",
		10001: "SUB_IDENTITY_FOREX",
		30001: "SUB_IDENTITY_SERVICE_PROVIDER",
		30002: "SUB_IDENTITY_FINTECH",
		30003: "SUB_IDENTITY_CRYPTO",
		30004: "SUB_IDENTITY_SERVICE_IB",
		30005: "SUB_IDENTITY_INVESTOR",
		30006: "SUB_IDENTITY_TRADER",
		30007: "SUB_IDENTITY_OTHER",
		40001: "SUB_IDENTITY_KOL",
	}
	SubIdentity_value = map[string]int32{
		"SUB_IDENTITY_UNKNOWN":          0,
		"SUB_IDENTITY_FOREX":            10001,
		"SUB_IDENTITY_SERVICE_PROVIDER": 30001,
		"SUB_IDENTITY_FINTECH":          30002,
		"SUB_IDENTITY_CRYPTO":           30003,
		"SUB_IDENTITY_SERVICE_IB":       30004,
		"SUB_IDENTITY_INVESTOR":         30005,
		"SUB_IDENTITY_TRADER":           30006,
		"SUB_IDENTITY_OTHER":            30007,
		"SUB_IDENTITY_KOL":              40001,
	}
)

func (x SubIdentity) Enum() *SubIdentity {
	p := new(SubIdentity)
	*p = x
	return p
}

func (x SubIdentity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubIdentity) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[3].Descriptor()
}

func (SubIdentity) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[3]
}

func (x SubIdentity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubIdentity.Descriptor instead.
func (SubIdentity) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{3}
}

type ApplyAudit int32

const (
	ApplyAudit_APPLY_AUDIT_UNKNOWN ApplyAudit = 0 //未知
	ApplyAudit_APPLY_AUDIT_Wait    ApplyAudit = 1 //待审核
	ApplyAudit_APPLY_AUDIT_Pass    ApplyAudit = 2 //已通过
	ApplyAudit_APPLY_AUDIT_NoPass  ApplyAudit = 3 //未通过
)

// Enum value maps for ApplyAudit.
var (
	ApplyAudit_name = map[int32]string{
		0: "APPLY_AUDIT_UNKNOWN",
		1: "APPLY_AUDIT_Wait",
		2: "APPLY_AUDIT_Pass",
		3: "APPLY_AUDIT_NoPass",
	}
	ApplyAudit_value = map[string]int32{
		"APPLY_AUDIT_UNKNOWN": 0,
		"APPLY_AUDIT_Wait":    1,
		"APPLY_AUDIT_Pass":    2,
		"APPLY_AUDIT_NoPass":  3,
	}
)

func (x ApplyAudit) Enum() *ApplyAudit {
	p := new(ApplyAudit)
	*p = x
	return p
}

func (x ApplyAudit) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApplyAudit) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[4].Descriptor()
}

func (ApplyAudit) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[4]
}

func (x ApplyAudit) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApplyAudit.Descriptor instead.
func (ApplyAudit) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{4}
}

type ApplyRefuseReason int32

const (
	ApplyRefuseReason_UNKNOWN        ApplyRefuseReason = 0 //未知
	ApplyRefuseReason_PHONE_ERROR    ApplyRefuseReason = 1 //电话有误
	ApplyRefuseReason_EMAIL_ERROR    ApplyRefuseReason = 2 //邮箱有误
	ApplyRefuseReason_INDUSTRY_ERROR ApplyRefuseReason = 3 //行业有误
	ApplyRefuseReason_IDENTITY_ERROR ApplyRefuseReason = 4 //身份有误
	ApplyRefuseReason_COMPANY_ERROR  ApplyRefuseReason = 5 //公司有误
	ApplyRefuseReason_JOB_ERROR      ApplyRefuseReason = 6 //职位有误
)

// Enum value maps for ApplyRefuseReason.
var (
	ApplyRefuseReason_name = map[int32]string{
		0: "UNKNOWN",
		1: "PHONE_ERROR",
		2: "EMAIL_ERROR",
		3: "INDUSTRY_ERROR",
		4: "IDENTITY_ERROR",
		5: "COMPANY_ERROR",
		6: "JOB_ERROR",
	}
	ApplyRefuseReason_value = map[string]int32{
		"UNKNOWN":        0,
		"PHONE_ERROR":    1,
		"EMAIL_ERROR":    2,
		"INDUSTRY_ERROR": 3,
		"IDENTITY_ERROR": 4,
		"COMPANY_ERROR":  5,
		"JOB_ERROR":      6,
	}
)

func (x ApplyRefuseReason) Enum() *ApplyRefuseReason {
	p := new(ApplyRefuseReason)
	*p = x
	return p
}

func (x ApplyRefuseReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApplyRefuseReason) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[5].Descriptor()
}

func (ApplyRefuseReason) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[5]
}

func (x ApplyRefuseReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApplyRefuseReason.Descriptor instead.
func (ApplyRefuseReason) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{5}
}

type ExhibitorType int32

const (
	// 未知
	ExhibitorType_EXHIBITOR_TYPE_UNKNOWN ExhibitorType = 0
	// 交易商
	ExhibitorType_EXHIBITOR_TYPE_BROKER ExhibitorType = 1
	// 服务商
	ExhibitorType_EXHIBITOR_TYPE_SPONSOR ExhibitorType = 3
)

// Enum value maps for ExhibitorType.
var (
	ExhibitorType_name = map[int32]string{
		0: "EXHIBITOR_TYPE_UNKNOWN",
		1: "EXHIBITOR_TYPE_BROKER",
		3: "EXHIBITOR_TYPE_SPONSOR",
	}
	ExhibitorType_value = map[string]int32{
		"EXHIBITOR_TYPE_UNKNOWN": 0,
		"EXHIBITOR_TYPE_BROKER":  1,
		"EXHIBITOR_TYPE_SPONSOR": 3,
	}
)

func (x ExhibitorType) Enum() *ExhibitorType {
	p := new(ExhibitorType)
	*p = x
	return p
}

func (x ExhibitorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExhibitorType) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[6].Descriptor()
}

func (ExhibitorType) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[6]
}

func (x ExhibitorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExhibitorType.Descriptor instead.
func (ExhibitorType) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{6}
}

type CommentContentType int32

const (
	CommentContentType_COMMENT_CONTENT_TYPE_UNKNOWN      CommentContentType = 0 //未知
	CommentContentType_COMMENT_CONTENT_TYPE_GENERAL      CommentContentType = 1 //普通
	CommentContentType_COMMENT_CONTENT_TYPE_BIND         CommentContentType = 2 //2绑定实盘
	CommentContentType_COMMENT_CONTENT_TYPE_FIXED        CommentContentType = 3 //3固定评价
	CommentContentType_COMMENT_CONTENT_TYPE_BOOK         CommentContentType = 4 //4预约演讲
	CommentContentType_COMMENT_CONTENT_TYPE_REGISTRATION CommentContentType = 5 //5报名通知
)

// Enum value maps for CommentContentType.
var (
	CommentContentType_name = map[int32]string{
		0: "COMMENT_CONTENT_TYPE_UNKNOWN",
		1: "COMMENT_CONTENT_TYPE_GENERAL",
		2: "COMMENT_CONTENT_TYPE_BIND",
		3: "COMMENT_CONTENT_TYPE_FIXED",
		4: "COMMENT_CONTENT_TYPE_BOOK",
		5: "COMMENT_CONTENT_TYPE_REGISTRATION",
	}
	CommentContentType_value = map[string]int32{
		"COMMENT_CONTENT_TYPE_UNKNOWN":      0,
		"COMMENT_CONTENT_TYPE_GENERAL":      1,
		"COMMENT_CONTENT_TYPE_BIND":         2,
		"COMMENT_CONTENT_TYPE_FIXED":        3,
		"COMMENT_CONTENT_TYPE_BOOK":         4,
		"COMMENT_CONTENT_TYPE_REGISTRATION": 5,
	}
)

func (x CommentContentType) Enum() *CommentContentType {
	p := new(CommentContentType)
	*p = x
	return p
}

func (x CommentContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommentContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_expo_v1_models_proto_enumTypes[7].Descriptor()
}

func (CommentContentType) Type() protoreflect.EnumType {
	return &file_expo_v1_models_proto_enumTypes[7]
}

func (x CommentContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommentContentType.Descriptor instead.
func (CommentContentType) EnumDescriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{7}
}

// 人脸矩形位置
type FaceRect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X      int32 `protobuf:"varint,1,opt,name=x,json=x,proto3" json:"x"`                // 左上角X坐标
	Y      int32 `protobuf:"varint,2,opt,name=y,json=y,proto3" json:"y"`                // 左上角Y坐标
	Width  int32 `protobuf:"varint,3,opt,name=width,json=width,proto3" json:"width"`    // 宽度
	Height int32 `protobuf:"varint,4,opt,name=height,json=height,proto3" json:"height"` // 高度
}

func (x *FaceRect) Reset() {
	*x = FaceRect{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FaceRect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceRect) ProtoMessage() {}

func (x *FaceRect) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceRect.ProtoReflect.Descriptor instead.
func (*FaceRect) Descriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{0}
}

func (x *FaceRect) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *FaceRect) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *FaceRect) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *FaceRect) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type SponsorLevelItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level SponsorLevel `protobuf:"varint,1,opt,name=level,json=level,proto3,enum=api.expo.v1.SponsorLevel" json:"level"`
	Icon  string       `protobuf:"bytes,2,opt,name=icon,json=icon,proto3" json:"icon"`
	Name  string       `protobuf:"bytes,3,opt,name=name,json=name,proto3" json:"name"`
	Key   string       `protobuf:"bytes,4,opt,name=key,json=key,proto3" json:"key"`
}

func (x *SponsorLevelItem) Reset() {
	*x = SponsorLevelItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_expo_v1_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SponsorLevelItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SponsorLevelItem) ProtoMessage() {}

func (x *SponsorLevelItem) ProtoReflect() protoreflect.Message {
	mi := &file_expo_v1_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SponsorLevelItem.ProtoReflect.Descriptor instead.
func (*SponsorLevelItem) Descriptor() ([]byte, []int) {
	return file_expo_v1_models_proto_rawDescGZIP(), []int{1}
}

func (x *SponsorLevelItem) GetLevel() SponsorLevel {
	if x != nil {
		return x.Level
	}
	return SponsorLevel_SponsorLevel_NONE
}

func (x *SponsorLevelItem) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *SponsorLevelItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SponsorLevelItem) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

var File_expo_v1_models_proto protoreflect.FileDescriptor

var file_expo_v1_models_proto_rawDesc = []byte{
	0x0a, 0x14, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f,
	0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70,
	0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x54, 0x0a, 0x08, 0x46, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x63, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78,
	0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x7d, 0x0a, 0x10,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x2f, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x70,
	0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x2a, 0xa4, 0x01, 0x0a, 0x0c,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x4e, 0x4f, 0x4e,
	0x45, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x47, 0x4f, 0x4c,
	0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x49, 0x4e, 0x55, 0x4d, 0x10, 0x03, 0x12, 0x18,
	0x0a, 0x14, 0x53, 0x70, 0x6f, 0x6e, 0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x44,
	0x49, 0x41, 0x4d, 0x4f, 0x4e, 0x44, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x70, 0x6f, 0x6e,
	0x73, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x47, 0x4f, 0x4c, 0x4f, 0x42, 0x41, 0x4c,
	0x10, 0x05, 0x2a, 0x73, 0x0a, 0x08, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59,
	0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x44, 0x55,
	0x53, 0x54, 0x52, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f,
	0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54, 0x4f, 0x10,
	0x03, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x46, 0x49,
	0x4e, 0x54, 0x45, 0x43, 0x48, 0x10, 0x04, 0x2a, 0x7d, 0x0a, 0x08, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12, 0x15,
	0x0a, 0x11, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53,
	0x54, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44,
	0x45, 0x52, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x4b, 0x4f, 0x4c, 0x10, 0x04, 0x2a, 0xa5, 0x02, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x17, 0x0a, 0x12, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59,
	0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x10, 0x91, 0x4e, 0x12, 0x23, 0x0a, 0x1d, 0x53, 0x55, 0x42,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x10, 0xb1, 0xea, 0x01, 0x12, 0x1a,
	0x0a, 0x14, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x46,
	0x49, 0x4e, 0x54, 0x45, 0x43, 0x48, 0x10, 0xb2, 0xea, 0x01, 0x12, 0x19, 0x0a, 0x13, 0x53, 0x55,
	0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x52, 0x59, 0x50, 0x54,
	0x4f, 0x10, 0xb3, 0xea, 0x01, 0x12, 0x1d, 0x0a, 0x17, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x42,
	0x10, 0xb4, 0xea, 0x01, 0x12, 0x1b, 0x0a, 0x15, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x10, 0xb5, 0xea,
	0x01, 0x12, 0x19, 0x0a, 0x13, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54,
	0x59, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x52, 0x10, 0xb6, 0xea, 0x01, 0x12, 0x18, 0x0a, 0x12,
	0x53, 0x55, 0x42, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x10, 0xb7, 0xea, 0x01, 0x12, 0x16, 0x0a, 0x10, 0x53, 0x55, 0x42, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x4b, 0x4f, 0x4c, 0x10, 0xc1, 0xb8, 0x02, 0x2a, 0x69,
	0x0a, 0x0a, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x41, 0x75, 0x64, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x13,
	0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x41,
	0x55, 0x44, 0x49, 0x54, 0x5f, 0x57, 0x61, 0x69, 0x74, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x41,
	0x50, 0x50, 0x4c, 0x59, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x10,
	0x02, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x5f, 0x41, 0x55, 0x44, 0x49, 0x54,
	0x5f, 0x4e, 0x6f, 0x50, 0x61, 0x73, 0x73, 0x10, 0x03, 0x2a, 0x8c, 0x01, 0x0a, 0x11, 0x41, 0x70,
	0x70, 0x6c, 0x79, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x02, 0x12, 0x12,
	0x0a, 0x0e, 0x49, 0x4e, 0x44, 0x55, 0x53, 0x54, 0x52, 0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e,
	0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x4a, 0x4f, 0x42,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x06, 0x2a, 0x62, 0x0a, 0x0d, 0x45, 0x78, 0x68, 0x69,
	0x62, 0x69, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x45, 0x58, 0x48,
	0x49, 0x42, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x58, 0x48, 0x49, 0x42, 0x49, 0x54,
	0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x52, 0x4f, 0x4b, 0x45, 0x52, 0x10, 0x01,
	0x12, 0x1a, 0x0a, 0x16, 0x45, 0x58, 0x48, 0x49, 0x42, 0x49, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x4f, 0x52, 0x10, 0x03, 0x2a, 0xdd, 0x01, 0x0a,
	0x12, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x45,
	0x4e, 0x45, 0x52, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x42, 0x49, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46,
	0x49, 0x58, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42,
	0x4f, 0x4f, 0x4b, 0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45,
	0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x42, 0x10, 0x5a, 0x0e,
	0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_expo_v1_models_proto_rawDescOnce sync.Once
	file_expo_v1_models_proto_rawDescData = file_expo_v1_models_proto_rawDesc
)

func file_expo_v1_models_proto_rawDescGZIP() []byte {
	file_expo_v1_models_proto_rawDescOnce.Do(func() {
		file_expo_v1_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_expo_v1_models_proto_rawDescData)
	})
	return file_expo_v1_models_proto_rawDescData
}

var file_expo_v1_models_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_expo_v1_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_expo_v1_models_proto_goTypes = []interface{}{
	(SponsorLevel)(0),        // 0: api.expo.v1.SponsorLevel
	(Industry)(0),            // 1: api.expo.v1.Industry
	(Identity)(0),            // 2: api.expo.v1.Identity
	(SubIdentity)(0),         // 3: api.expo.v1.SubIdentity
	(ApplyAudit)(0),          // 4: api.expo.v1.ApplyAudit
	(ApplyRefuseReason)(0),   // 5: api.expo.v1.ApplyRefuseReason
	(ExhibitorType)(0),       // 6: api.expo.v1.ExhibitorType
	(CommentContentType)(0),  // 7: api.expo.v1.CommentContentType
	(*FaceRect)(nil),         // 8: api.expo.v1.FaceRect
	(*SponsorLevelItem)(nil), // 9: api.expo.v1.SponsorLevelItem
}
var file_expo_v1_models_proto_depIdxs = []int32{
	0, // 0: api.expo.v1.SponsorLevelItem.level:type_name -> api.expo.v1.SponsorLevel
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_expo_v1_models_proto_init() }
func file_expo_v1_models_proto_init() {
	if File_expo_v1_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_expo_v1_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FaceRect); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_expo_v1_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SponsorLevelItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_expo_v1_models_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_expo_v1_models_proto_goTypes,
		DependencyIndexes: file_expo_v1_models_proto_depIdxs,
		EnumInfos:         file_expo_v1_models_proto_enumTypes,
		MessageInfos:      file_expo_v1_models_proto_msgTypes,
	}.Build()
	File_expo_v1_models_proto = out.File
	file_expo_v1_models_proto_rawDesc = nil
	file_expo_v1_models_proto_goTypes = nil
	file_expo_v1_models_proto_depIdxs = nil
}
