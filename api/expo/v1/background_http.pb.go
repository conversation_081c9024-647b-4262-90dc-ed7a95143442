// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.25.3
// source: expo/v1/background.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	common "gold_store/api/common"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationBackgroundAddExpoCommunity = "/api.expo.v1.Background/AddExpoCommunity"
const OperationBackgroundAddExpoExhibitor = "/api.expo.v1.Background/AddExpoExhibitor"
const OperationBackgroundAddExpoExhibitorMember = "/api.expo.v1.Background/AddExpoExhibitorMember"
const OperationBackgroundAddExpoGuest = "/api.expo.v1.Background/AddExpoGuest"
const OperationBackgroundAddExpoGuide = "/api.expo.v1.Background/AddExpoGuide"
const OperationBackgroundAddExpoLive = "/api.expo.v1.Background/AddExpoLive"
const OperationBackgroundAddExpoPartner = "/api.expo.v1.Background/AddExpoPartner"
const OperationBackgroundAddExpoReview = "/api.expo.v1.Background/AddExpoReview"
const OperationBackgroundAddExpoSchedule = "/api.expo.v1.Background/AddExpoSchedule"
const OperationBackgroundAddGuest = "/api.expo.v1.Background/AddGuest"
const OperationBackgroundAddHall = "/api.expo.v1.Background/AddHall"
const OperationBackgroundApplyAudit = "/api.expo.v1.Background/ApplyAudit"
const OperationBackgroundApplyRefuseInfo = "/api.expo.v1.Background/ApplyRefuseInfo"
const OperationBackgroundDeleteExhibitorMember = "/api.expo.v1.Background/DeleteExhibitorMember"
const OperationBackgroundDeleteExpoCommunity = "/api.expo.v1.Background/DeleteExpoCommunity"
const OperationBackgroundDeleteExpoExhibitor = "/api.expo.v1.Background/DeleteExpoExhibitor"
const OperationBackgroundDeleteExpoGuest = "/api.expo.v1.Background/DeleteExpoGuest"
const OperationBackgroundDeleteExpoGuide = "/api.expo.v1.Background/DeleteExpoGuide"
const OperationBackgroundDeleteExpoLive = "/api.expo.v1.Background/DeleteExpoLive"
const OperationBackgroundDeleteExpoPartner = "/api.expo.v1.Background/DeleteExpoPartner"
const OperationBackgroundDeleteExpoReview = "/api.expo.v1.Background/DeleteExpoReview"
const OperationBackgroundDeleteExpoSchedule = "/api.expo.v1.Background/DeleteExpoSchedule"
const OperationBackgroundDeleteGuest = "/api.expo.v1.Background/DeleteGuest"
const OperationBackgroundDeleteHall = "/api.expo.v1.Background/DeleteHall"
const OperationBackgroundExhibitorApplyList = "/api.expo.v1.Background/ExhibitorApplyList"
const OperationBackgroundExpoExhibitorExport = "/api.expo.v1.Background/ExpoExhibitorExport"
const OperationBackgroundExpoExhibitorImport = "/api.expo.v1.Background/ExpoExhibitorImport"
const OperationBackgroundExportExpoGuest = "/api.expo.v1.Background/ExportExpoGuest"
const OperationBackgroundExportExpoPartner = "/api.expo.v1.Background/ExportExpoPartner"
const OperationBackgroundExportExpoSchedule = "/api.expo.v1.Background/ExportExpoSchedule"
const OperationBackgroundFaceGroupCreate = "/api.expo.v1.Background/FaceGroupCreate"
const OperationBackgroundFaceGroupList = "/api.expo.v1.Background/FaceGroupList"
const OperationBackgroundFaceGroupsByExpo = "/api.expo.v1.Background/FaceGroupsByExpo"
const OperationBackgroundFacePhotoUpload = "/api.expo.v1.Background/FacePhotoUpload"
const OperationBackgroundFacesPhotoList = "/api.expo.v1.Background/FacesPhotoList"
const OperationBackgroundGetExpoCommunity = "/api.expo.v1.Background/GetExpoCommunity"
const OperationBackgroundGetExpoExhibitor = "/api.expo.v1.Background/GetExpoExhibitor"
const OperationBackgroundGetExpoGuest = "/api.expo.v1.Background/GetExpoGuest"
const OperationBackgroundGetExpoGuide = "/api.expo.v1.Background/GetExpoGuide"
const OperationBackgroundGetExpoLive = "/api.expo.v1.Background/GetExpoLive"
const OperationBackgroundGetExpoPartner = "/api.expo.v1.Background/GetExpoPartner"
const OperationBackgroundGetExpoPartnerType = "/api.expo.v1.Background/GetExpoPartnerType"
const OperationBackgroundGetExpoReview = "/api.expo.v1.Background/GetExpoReview"
const OperationBackgroundGetExpoSchedule = "/api.expo.v1.Background/GetExpoSchedule"
const OperationBackgroundGetFaceGroupInfo = "/api.expo.v1.Background/GetFaceGroupInfo"
const OperationBackgroundGetGuest = "/api.expo.v1.Background/GetGuest"
const OperationBackgroundGetHall = "/api.expo.v1.Background/GetHall"
const OperationBackgroundGetSponsorLevel = "/api.expo.v1.Background/GetSponsorLevel"
const OperationBackgroundGetSyncStatus = "/api.expo.v1.Background/GetSyncStatus"
const OperationBackgroundImportExpoPartner = "/api.expo.v1.Background/ImportExpoPartner"
const OperationBackgroundImportExpoSchedule = "/api.expo.v1.Background/ImportExpoSchedule"
const OperationBackgroundListExpoExhibitor = "/api.expo.v1.Background/ListExpoExhibitor"
const OperationBackgroundListExpoGuest = "/api.expo.v1.Background/ListExpoGuest"
const OperationBackgroundListExpoGuide = "/api.expo.v1.Background/ListExpoGuide"
const OperationBackgroundListExpoLive = "/api.expo.v1.Background/ListExpoLive"
const OperationBackgroundListExpoPartner = "/api.expo.v1.Background/ListExpoPartner"
const OperationBackgroundListExpoReview = "/api.expo.v1.Background/ListExpoReview"
const OperationBackgroundListExpoSchedule = "/api.expo.v1.Background/ListExpoSchedule"
const OperationBackgroundListGuest = "/api.expo.v1.Background/ListGuest"
const OperationBackgroundListGuestsNotInExpo = "/api.expo.v1.Background/ListGuestsNotInExpo"
const OperationBackgroundListHall = "/api.expo.v1.Background/ListHall"
const OperationBackgroundListLiveImage = "/api.expo.v1.Background/ListLiveImage"
const OperationBackgroundRefuseSetting = "/api.expo.v1.Background/RefuseSetting"
const OperationBackgroundSearchExhibitorMember = "/api.expo.v1.Background/SearchExhibitorMember"
const OperationBackgroundSendMessageEmailsAPI = "/api.expo.v1.Background/SendMessageEmailsAPI"
const OperationBackgroundSetExhibitorApplyStatus = "/api.expo.v1.Background/SetExhibitorApplyStatus"
const OperationBackgroundSetExpoCommunityEnable = "/api.expo.v1.Background/SetExpoCommunityEnable"
const OperationBackgroundSetExpoExhibitorEmployeeEnable = "/api.expo.v1.Background/SetExpoExhibitorEmployeeEnable"
const OperationBackgroundSetExpoExhibitorEnable = "/api.expo.v1.Background/SetExpoExhibitorEnable"
const OperationBackgroundSetExpoGuestEnable = "/api.expo.v1.Background/SetExpoGuestEnable"
const OperationBackgroundSetExpoGuideEnable = "/api.expo.v1.Background/SetExpoGuideEnable"
const OperationBackgroundSetExpoLiveEnable = "/api.expo.v1.Background/SetExpoLiveEnable"
const OperationBackgroundSetExpoPartnerEnable = "/api.expo.v1.Background/SetExpoPartnerEnable"
const OperationBackgroundSetExpoReviewEnable = "/api.expo.v1.Background/SetExpoReviewEnable"
const OperationBackgroundSetExpoScheduleEnable = "/api.expo.v1.Background/SetExpoScheduleEnable"
const OperationBackgroundSetGuestEnable = "/api.expo.v1.Background/SetGuestEnable"
const OperationBackgroundSetHallEnable = "/api.expo.v1.Background/SetHallEnable"
const OperationBackgroundSyncFace = "/api.expo.v1.Background/SyncFace"
const OperationBackgroundSyncLiveImage = "/api.expo.v1.Background/SyncLiveImage"
const OperationBackgroundUpdateExpoCommunity = "/api.expo.v1.Background/UpdateExpoCommunity"
const OperationBackgroundUpdateExpoExhibitor = "/api.expo.v1.Background/UpdateExpoExhibitor"
const OperationBackgroundUpdateExpoGuide = "/api.expo.v1.Background/UpdateExpoGuide"
const OperationBackgroundUpdateExpoLive = "/api.expo.v1.Background/UpdateExpoLive"
const OperationBackgroundUpdateExpoPartner = "/api.expo.v1.Background/UpdateExpoPartner"
const OperationBackgroundUpdateExpoReview = "/api.expo.v1.Background/UpdateExpoReview"
const OperationBackgroundUpdateExpoSchedule = "/api.expo.v1.Background/UpdateExpoSchedule"
const OperationBackgroundUpdateGuest = "/api.expo.v1.Background/UpdateGuest"
const OperationBackgroundUpdateHall = "/api.expo.v1.Background/UpdateHall"

type BackgroundHTTPServer interface {
	// AddExpoCommunity ===========================================================
	// =========================== 展会社区 =======================
	// ===========================================================
	AddExpoCommunity(context.Context, *ExpoCommunity) (*common.EmptyReply, error)
	AddExpoExhibitor(context.Context, *ExpoExhibitorInfo) (*AddExpoExhibitorReply, error)
	AddExpoExhibitorMember(context.Context, *ExpoExhibitorMemberRequest) (*AddExpoExhibitorMemberReply, error)
	// AddExpoGuest ===========================================================
	// =========================== 展会嘉宾 =======================
	// ===========================================================
	AddExpoGuest(context.Context, *AddExpoGuestRequest) (*AddExpoGuestReply, error)
	// AddExpoGuide ===========================================================
	// =========================== 展会指南 =======================
	// ===========================================================
	AddExpoGuide(context.Context, *ExpoGuideInfo) (*AddExpoGuideReply, error)
	// AddExpoLive ===========================================================
	// =========================== 展会直播 =======================
	// ===========================================================
	AddExpoLive(context.Context, *ExpoLive) (*AddExpoLiveReply, error)
	AddExpoPartner(context.Context, *ExpoPartnerInfo) (*AddExpoPartnerReply, error)
	// AddExpoReview ===========================================================
	// =========================== 展会回顾 =======================
	// ===========================================================
	AddExpoReview(context.Context, *ExpoReviewInfo) (*AddExpoReviewReply, error)
	// AddExpoSchedule ===========================================================
	// =========================== 展会议程 =======================
	// ===========================================================
	AddExpoSchedule(context.Context, *ExpoScheduleInfo) (*AddExpoScheduleReply, error)
	// AddGuest ===========================================================
	// =========================== 嘉宾 ===========================
	// ===========================================================
	AddGuest(context.Context, *Guest) (*AddGuestReply, error)
	// AddHall ===========================================================
	// =========================== 展会会场 =======================
	// ===========================================================
	AddHall(context.Context, *ExpoHall) (*AddGuestReply, error)
	// ApplyAudit报名审核
	ApplyAudit(context.Context, *ApplyAuditRequest) (*ApplyAuditReply, error)
	// ApplyRefuseInfo展示拒绝理由
	ApplyRefuseInfo(context.Context, *ApplyRefuseInfoRequest) (*ApplyRefuseInfoReply, error)
	DeleteExhibitorMember(context.Context, *DeleteExhibitorMemberRequest) (*common.EmptyReply, error)
	DeleteExpoCommunity(context.Context, *DeleteExpoCommunityRequest) (*common.EmptyReply, error)
	DeleteExpoExhibitor(context.Context, *DeleteExpoExhibitorRequest) (*common.EmptyReply, error)
	DeleteExpoGuest(context.Context, *DeleteExpoGuestRequest) (*common.EmptyReply, error)
	DeleteExpoGuide(context.Context, *DeleteExpoGuideRequest) (*common.EmptyReply, error)
	DeleteExpoLive(context.Context, *DeleteExpoLiveRequest) (*common.EmptyReply, error)
	DeleteExpoPartner(context.Context, *DeleteExpoPartnerRequest) (*common.EmptyReply, error)
	DeleteExpoReview(context.Context, *DeleteExpoReviewRequest) (*common.EmptyReply, error)
	DeleteExpoSchedule(context.Context, *DeleteExpoScheduleRequest) (*common.EmptyReply, error)
	DeleteGuest(context.Context, *DeleteGuestRequest) (*common.EmptyReply, error)
	DeleteHall(context.Context, *DeleteHallRequest) (*common.EmptyReply, error)
	// ExhibitorApplyList ===========================================================
	// =========================== 报名参展商 ======================
	// ===========================================================
	ExhibitorApplyList(context.Context, *ExhibitorApplyListRequest) (*ExhibitorApplyListReply, error)
	ExpoExhibitorExport(context.Context, *ExpoExhibitorExportRequest) (*ExpoExhibitorExportReply, error)
	ExpoExhibitorImport(context.Context, *ExpoExhibitorImportRequest) (*common.EmptyReply, error)
	ExportExpoGuest(context.Context, *ExportExpoGuestRequest) (*ExportExpoGuestReply, error)
	ExportExpoPartner(context.Context, *ExportExpoPartnerRequest) (*ExportExpoPartnerReply, error)
	ExportExpoSchedule(context.Context, *ExportExpoScheduleRequest) (*ExportExpoScheduleReply, error)
	// FaceGroupCreate 创建人员库
	FaceGroupCreate(context.Context, *FaceGroupCreateRequest) (*FaceGroupCreateReply, error)
	// FaceGroupList 获取人员库列表
	FaceGroupList(context.Context, *FaceGroupListRequest) (*FaceGroupListReply, error)
	// FaceGroupsByExpo 根据展会ID获取人员库
	FaceGroupsByExpo(context.Context, *FaceGroupsByExpoRequest) (*FaceGroupInfo, error)
	// FacePhotoUpload 展会照片上传与人脸识别
	FacePhotoUpload(context.Context, *FacePhotoUploadRequest) (*FacePhotoUploadReply, error)
	// FacesPhotoList 照片列表
	FacesPhotoList(context.Context, *PhotoFacesRequest) (*PhotoFacesReply, error)
	GetExpoCommunity(context.Context, *GetExpoCommunityRequest) (*ExpoCommunity, error)
	GetExpoExhibitor(context.Context, *GetExpoExhibitorRequest) (*ExpoExhibitorInfo, error)
	GetExpoGuest(context.Context, *GetExpoGuestRequest) (*GetExpoGuestReply, error)
	GetExpoGuide(context.Context, *GetExpoGuideRequest) (*ExpoGuideInfo, error)
	GetExpoLive(context.Context, *GetExpoLiveRequest) (*ExpoLive, error)
	GetExpoPartner(context.Context, *GetExpoPartnerRequest) (*ExpoPartnerInfo, error)
	// GetExpoPartnerType ===========================================================
	// =========================== 合作伙伴 =======================
	// ===========================================================
	GetExpoPartnerType(context.Context, *GetExpoPartnerTypeRequest) (*GetExpoPartnerTypeReply, error)
	GetExpoReview(context.Context, *GetExpoReviewRequest) (*ExpoReviewInfo, error)
	GetExpoSchedule(context.Context, *GetExpoScheduleRequest) (*ExpoScheduleInfo, error)
	// GetFaceGroupInfo 获取人员库信息
	GetFaceGroupInfo(context.Context, *FaceGroupInfoRequest) (*FaceGroupInfo, error)
	GetGuest(context.Context, *GetGuestRequest) (*Guest, error)
	GetHall(context.Context, *GetHallRequest) (*ExpoHall, error)
	// GetSponsorLevel ===========================================================
	// =========================== 展会参展商 ======================
	// ===========================================================
	GetSponsorLevel(context.Context, *common.EmptyRequest) (*GetSponsorLevelReply, error)
	// GetSyncStatus 查询展会图片同步状态
	GetSyncStatus(context.Context, *GetSyncStatusRequest) (*GetSyncStatusReply, error)
	ImportExpoPartner(context.Context, *ImportExpoPartnerRequest) (*common.EmptyReply, error)
	ImportExpoSchedule(context.Context, *ImportExpoScheduleRequest) (*common.EmptyReply, error)
	ListExpoExhibitor(context.Context, *ListExpoExhibitorRequest) (*ListExpoExhibitorReply, error)
	ListExpoGuest(context.Context, *ListExpoGuestRequest) (*ListExpoGuestReply, error)
	ListExpoGuide(context.Context, *ListExpoGuideRequest) (*ListExpoGuideReply, error)
	ListExpoLive(context.Context, *ListExpoLiveRequest) (*ListExpoLiveReply, error)
	ListExpoPartner(context.Context, *ListExpoPartnerRequest) (*ListExpoPartnerReply, error)
	ListExpoReview(context.Context, *ListExpoReviewRequest) (*ListExpoReviewReply, error)
	ListExpoSchedule(context.Context, *ListExpoScheduleRequest) (*ListExpoScheduleReply, error)
	ListGuest(context.Context, *ListGuestRequest) (*ListGuestReply, error)
	ListGuestsNotInExpo(context.Context, *ListGuestsNotInExpoRequest) (*ListGuestsNotInExpoReply, error)
	ListHall(context.Context, *ListHallRequest) (*ListHallReply, error)
	// ListLiveImage ===========================================================
	// =========================== 图片直播 =======================
	// ===========================================================
	ListLiveImage(context.Context, *ListLiveImageRequest) (*ListLiveImageReply, error)
	// RefuseSetting拒绝理由配置
	RefuseSetting(context.Context, *RefuseSettingRequest) (*RefuseSettingReply, error)
	SearchExhibitorMember(context.Context, *SearchExhibitorMemberRequest) (*SearchExhibitorMemberReply, error)
	// SendMessageEmailsAPI ===========================================================
	// =========================== 邮件发送 =======================
	// ===========================================================
	// 发送展会邮件
	SendMessageEmailsAPI(context.Context, *SendMessageEmailsRequest) (*SendMessageEmailsReply, error)
	SetExhibitorApplyStatus(context.Context, *SetExhibitorApplyStatusRequest) (*common.EmptyReply, error)
	SetExpoCommunityEnable(context.Context, *SetExpoCommunityEnableRequest) (*common.EmptyReply, error)
	SetExpoExhibitorEmployeeEnable(context.Context, *SetExpoExhibitorEmployeeEnableRequest) (*common.EmptyReply, error)
	SetExpoExhibitorEnable(context.Context, *SetExpoExhibitorEnableRequest) (*common.EmptyReply, error)
	SetExpoGuestEnable(context.Context, *SetExpoGuestEnableRequest) (*common.EmptyReply, error)
	SetExpoGuideEnable(context.Context, *SetExpoGuideEnableRequest) (*common.EmptyReply, error)
	SetExpoLiveEnable(context.Context, *SetExpoLiveEnableRequest) (*common.EmptyReply, error)
	SetExpoPartnerEnable(context.Context, *SetExpoPartnerEnableRequest) (*common.EmptyReply, error)
	SetExpoReviewEnable(context.Context, *SetExpoReviewEnableRequest) (*common.EmptyReply, error)
	SetExpoScheduleEnable(context.Context, *SetExpoScheduleEnableRequest) (*common.EmptyReply, error)
	SetGuestEnable(context.Context, *SetGuestEnableRequest) (*common.EmptyReply, error)
	SetHallEnable(context.Context, *SetHallEnableRequest) (*common.EmptyReply, error)
	SyncFace(context.Context, *SyncFaceRequest) (*SyncFaceReply, error)
	SyncLiveImage(context.Context, *SyncLiveImageRequest) (*SyncLiveImageReply, error)
	UpdateExpoCommunity(context.Context, *ExpoCommunity) (*common.EmptyReply, error)
	UpdateExpoExhibitor(context.Context, *ExpoExhibitorInfo) (*common.EmptyReply, error)
	UpdateExpoGuide(context.Context, *ExpoGuideInfo) (*common.EmptyReply, error)
	UpdateExpoLive(context.Context, *ExpoLive) (*common.EmptyReply, error)
	UpdateExpoPartner(context.Context, *ExpoPartnerInfo) (*common.EmptyReply, error)
	UpdateExpoReview(context.Context, *ExpoReviewInfo) (*common.EmptyReply, error)
	UpdateExpoSchedule(context.Context, *ExpoScheduleInfo) (*common.EmptyReply, error)
	UpdateGuest(context.Context, *Guest) (*common.EmptyReply, error)
	UpdateHall(context.Context, *ExpoHall) (*common.EmptyReply, error)
}

func RegisterBackgroundHTTPServer(s *http.Server, srv BackgroundHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/admin/image/live/list", _Background_ListLiveImage0_HTTP_Handler(srv))
	r.POST("/v1/admin/image/live/sync", _Background_SyncLiveImage0_HTTP_Handler(srv))
	r.POST("/v1/admin/sync/face", _Background_SyncFace0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/sync-status", _Background_GetSyncStatus0_HTTP_Handler(srv))
	r.POST("/v1/admin/guest/add", _Background_AddGuest0_HTTP_Handler(srv))
	r.GET("/v1/admin/guest/detail", _Background_GetGuest0_HTTP_Handler(srv))
	r.PUT("/v1/admin/guest/update", _Background_UpdateGuest0_HTTP_Handler(srv))
	r.PUT("/v1/admin/guest/enable", _Background_SetGuestEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/guest/list", _Background_ListGuest0_HTTP_Handler(srv))
	r.GET("/v1/admin/guest/list-not-in-expo", _Background_ListGuestsNotInExpo0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/guest/delete", _Background_DeleteGuest0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/community/add", _Background_AddExpoCommunity0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/community/detail", _Background_GetExpoCommunity0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/community/update", _Background_UpdateExpoCommunity0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/community/enable", _Background_SetExpoCommunityEnable0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/community/delete", _Background_DeleteExpoCommunity0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/hall/add", _Background_AddHall0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/hall/detail", _Background_GetHall0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/hall/update", _Background_UpdateHall0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/hall/enable", _Background_SetHallEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/hall/list", _Background_ListHall0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/hall/delete", _Background_DeleteHall0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/guest/add", _Background_AddExpoGuest0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/guest/detail", _Background_GetExpoGuest0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/guest/list", _Background_ListExpoGuest0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/guest/delete", _Background_DeleteExpoGuest0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/guest/enable", _Background_SetExpoGuestEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/guest/export", _Background_ExportExpoGuest0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/schedule/add", _Background_AddExpoSchedule0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/schedule/detail", _Background_GetExpoSchedule0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/schedule/update", _Background_UpdateExpoSchedule0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/schedule/enable", _Background_SetExpoScheduleEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/schedule/list", _Background_ListExpoSchedule0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/schedule/import", _Background_ImportExpoSchedule0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/schedule/export", _Background_ExportExpoSchedule0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/schedule/delete", _Background_DeleteExpoSchedule0_HTTP_Handler(srv))
	r.POST("/v1/admin/exhibitor/apply/list", _Background_ExhibitorApplyList0_HTTP_Handler(srv))
	r.PUT("/v1/admin/exhibitor/apply/status", _Background_SetExhibitorApplyStatus0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/sponsor/level", _Background_GetSponsorLevel0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/exhibitor/employee/enable", _Background_SetExpoExhibitorEmployeeEnable0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/exhibitor/add", _Background_AddExpoExhibitor0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/exhibitor/detail", _Background_GetExpoExhibitor0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/exhibitor/update", _Background_UpdateExpoExhibitor0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/exhibitor/enable", _Background_SetExpoExhibitorEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/exhibitor", _Background_ListExpoExhibitor0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/exhibitor/delete", _Background_DeleteExpoExhibitor0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/exhibitor/import", _Background_ExpoExhibitorImport0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/exhibitor/export", _Background_ExpoExhibitorExport0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/exhibitor/member", _Background_SearchExhibitorMember0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/exhibitor/member/add", _Background_AddExpoExhibitorMember0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/exhibitor/member/delete", _Background_DeleteExhibitorMember0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/guide/add", _Background_AddExpoGuide0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/guide/detail", _Background_GetExpoGuide0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/guide/update", _Background_UpdateExpoGuide0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/guide/enable", _Background_SetExpoGuideEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/guide/list", _Background_ListExpoGuide0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/guide/delete", _Background_DeleteExpoGuide0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/partner/type", _Background_GetExpoPartnerType0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/partner/add", _Background_AddExpoPartner0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/partner/detail", _Background_GetExpoPartner0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/partner/update", _Background_UpdateExpoPartner0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/partner/enable", _Background_SetExpoPartnerEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/partner/list", _Background_ListExpoPartner0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/partner/delete", _Background_DeleteExpoPartner0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/partner/import", _Background_ImportExpoPartner0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/partner/export", _Background_ExportExpoPartner0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/review/add", _Background_AddExpoReview0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/review/detail", _Background_GetExpoReview0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/review/update", _Background_UpdateExpoReview0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/review/enable", _Background_SetExpoReviewEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/review/list", _Background_ListExpoReview0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/review/delete", _Background_DeleteExpoReview0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/live/add", _Background_AddExpoLive0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/live/detail", _Background_GetExpoLive0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/live/update", _Background_UpdateExpoLive0_HTTP_Handler(srv))
	r.PUT("/v1/admin/expo/live/enable", _Background_SetExpoLiveEnable0_HTTP_Handler(srv))
	r.GET("/v1/admin/expo/live/list", _Background_ListExpoLive0_HTTP_Handler(srv))
	r.DELETE("/v1/admin/expo/live/delete", _Background_DeleteExpoLive0_HTTP_Handler(srv))
	r.POST("/v1/face/photosUpload", _Background_FacePhotoUpload0_HTTP_Handler(srv))
	r.POST("/v1/face/createGroups", _Background_FaceGroupCreate0_HTTP_Handler(srv))
	r.POST("/v1/face/groupsInfo", _Background_GetFaceGroupInfo0_HTTP_Handler(srv))
	r.POST("/v1/face/groupsList", _Background_FaceGroupList0_HTTP_Handler(srv))
	r.POST("/v1/face/groupsByExpo", _Background_FaceGroupsByExpo0_HTTP_Handler(srv))
	r.POST("/v1/face/PhotoList", _Background_FacesPhotoList0_HTTP_Handler(srv))
	r.POST("/v1/admin/expo/email/send", _Background_SendMessageEmailsAPI0_HTTP_Handler(srv))
	r.POST("/v1/admin/registration/applyAudit", _Background_ApplyAudit0_HTTP_Handler(srv))
	r.GET("/v1/admin/registration/applyRefuseInfo", _Background_ApplyRefuseInfo0_HTTP_Handler(srv))
	r.GET("/v1/admin/registration/refuseSetting", _Background_RefuseSetting0_HTTP_Handler(srv))
}

func _Background_ListLiveImage0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListLiveImageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListLiveImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListLiveImage(ctx, req.(*ListLiveImageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListLiveImageReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SyncLiveImage0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncLiveImageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSyncLiveImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncLiveImage(ctx, req.(*SyncLiveImageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncLiveImageReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SyncFace0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncFaceRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSyncFace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SyncFace(ctx, req.(*SyncFaceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncFaceReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetSyncStatus0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSyncStatusRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetSyncStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSyncStatus(ctx, req.(*GetSyncStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSyncStatusReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Guest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddGuest(ctx, req.(*Guest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGuest(ctx, req.(*GetGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Guest)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Guest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateGuest(ctx, req.(*Guest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetGuestEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetGuestEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetGuestEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetGuestEnable(ctx, req.(*SetGuestEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGuest(ctx, req.(*ListGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListGuestsNotInExpo0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListGuestsNotInExpoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListGuestsNotInExpo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListGuestsNotInExpo(ctx, req.(*ListGuestsNotInExpoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListGuestsNotInExpoReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteGuest(ctx, req.(*DeleteGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoCommunity0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoCommunity
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoCommunity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoCommunity(ctx, req.(*ExpoCommunity))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoCommunity0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoCommunityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoCommunity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoCommunity(ctx, req.(*GetExpoCommunityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoCommunity)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoCommunity0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoCommunity
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoCommunity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoCommunity(ctx, req.(*ExpoCommunity))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoCommunityEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoCommunityEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoCommunityEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoCommunityEnable(ctx, req.(*SetExpoCommunityEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoCommunity0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoCommunityRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoCommunity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoCommunity(ctx, req.(*DeleteExpoCommunityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddHall0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoHall
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddHall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddHall(ctx, req.(*ExpoHall))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetHall0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetHallRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetHall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetHall(ctx, req.(*GetHallRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoHall)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateHall0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoHall
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateHall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateHall(ctx, req.(*ExpoHall))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetHallEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetHallEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetHallEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetHallEnable(ctx, req.(*SetHallEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListHall0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListHallRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListHall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHall(ctx, req.(*ListHallRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListHallReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteHall0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteHallRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteHall)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteHall(ctx, req.(*DeleteHallRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddExpoGuestRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoGuest(ctx, req.(*AddExpoGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoGuest(ctx, req.(*GetExpoGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExpoGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoGuest(ctx, req.(*ListExpoGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoGuest(ctx, req.(*DeleteExpoGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoGuestEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoGuestEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoGuestEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoGuestEnable(ctx, req.(*SetExpoGuestEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ExportExpoGuest0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExportExpoGuestRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundExportExpoGuest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportExpoGuest(ctx, req.(*ExportExpoGuestRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExportExpoGuestReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoScheduleInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoSchedule(ctx, req.(*ExpoScheduleInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoSchedule(ctx, req.(*GetExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoScheduleInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoScheduleInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoSchedule(ctx, req.(*ExpoScheduleInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoScheduleEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoScheduleEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoScheduleEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoScheduleEnable(ctx, req.(*SetExpoScheduleEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoSchedule(ctx, req.(*ListExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ImportExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ImportExpoScheduleRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundImportExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ImportExpoSchedule(ctx, req.(*ImportExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ExportExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExportExpoScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundExportExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportExpoSchedule(ctx, req.(*ExportExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExportExpoScheduleReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoSchedule0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoScheduleRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoSchedule)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoSchedule(ctx, req.(*DeleteExpoScheduleRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ExhibitorApplyList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExhibitorApplyListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundExhibitorApplyList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExhibitorApplyList(ctx, req.(*ExhibitorApplyListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExhibitorApplyListReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExhibitorApplyStatus0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExhibitorApplyStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExhibitorApplyStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExhibitorApplyStatus(ctx, req.(*SetExhibitorApplyStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetSponsorLevel0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in common.EmptyRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetSponsorLevel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSponsorLevel(ctx, req.(*common.EmptyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSponsorLevelReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoExhibitorEmployeeEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoExhibitorEmployeeEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoExhibitorEmployeeEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoExhibitorEmployeeEnable(ctx, req.(*SetExpoExhibitorEmployeeEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoExhibitor0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoExhibitorInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoExhibitor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoExhibitor(ctx, req.(*ExpoExhibitorInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoExhibitorReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoExhibitor0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoExhibitorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoExhibitor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoExhibitor(ctx, req.(*GetExpoExhibitorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoExhibitorInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoExhibitor0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoExhibitorInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoExhibitor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoExhibitor(ctx, req.(*ExpoExhibitorInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoExhibitorEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoExhibitorEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoExhibitorEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoExhibitorEnable(ctx, req.(*SetExpoExhibitorEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoExhibitor0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoExhibitorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoExhibitor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoExhibitor(ctx, req.(*ListExpoExhibitorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoExhibitorReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoExhibitor0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoExhibitorRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoExhibitor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoExhibitor(ctx, req.(*DeleteExpoExhibitorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ExpoExhibitorImport0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoExhibitorImportRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundExpoExhibitorImport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoExhibitorImport(ctx, req.(*ExpoExhibitorImportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ExpoExhibitorExport0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoExhibitorExportRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundExpoExhibitorExport)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExpoExhibitorExport(ctx, req.(*ExpoExhibitorExportRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoExhibitorExportReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SearchExhibitorMember0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchExhibitorMemberRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSearchExhibitorMember)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchExhibitorMember(ctx, req.(*SearchExhibitorMemberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchExhibitorMemberReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoExhibitorMember0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoExhibitorMemberRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoExhibitorMember)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoExhibitorMember(ctx, req.(*ExpoExhibitorMemberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoExhibitorMemberReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExhibitorMember0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExhibitorMemberRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExhibitorMember)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExhibitorMember(ctx, req.(*DeleteExhibitorMemberRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoGuide0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoGuideInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoGuide(ctx, req.(*ExpoGuideInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoGuideReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoGuide0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoGuideRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoGuide(ctx, req.(*GetExpoGuideRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoGuideInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoGuide0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoGuideInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoGuide(ctx, req.(*ExpoGuideInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoGuideEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoGuideEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoGuideEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoGuideEnable(ctx, req.(*SetExpoGuideEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoGuide0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoGuideRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoGuide(ctx, req.(*ListExpoGuideRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoGuideReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoGuide0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoGuideRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoGuide(ctx, req.(*DeleteExpoGuideRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoPartnerType0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoPartnerTypeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoPartnerType)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoPartnerType(ctx, req.(*GetExpoPartnerTypeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExpoPartnerTypeReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoPartnerInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoPartner(ctx, req.(*ExpoPartnerInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoPartnerReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoPartnerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoPartner(ctx, req.(*GetExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoPartnerInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoPartnerInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoPartner(ctx, req.(*ExpoPartnerInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoPartnerEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoPartnerEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoPartnerEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoPartnerEnable(ctx, req.(*SetExpoPartnerEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoPartnerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoPartner(ctx, req.(*ListExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoPartnerReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoPartnerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoPartner(ctx, req.(*DeleteExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ImportExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ImportExpoPartnerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundImportExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ImportExpoPartner(ctx, req.(*ImportExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ExportExpoPartner0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExportExpoPartnerRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundExportExpoPartner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ExportExpoPartner(ctx, req.(*ExportExpoPartnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExportExpoPartnerReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoReview0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoReviewInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoReview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoReview(ctx, req.(*ExpoReviewInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoReviewReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoReview0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoReviewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoReview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoReview(ctx, req.(*GetExpoReviewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoReviewInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoReview0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoReviewInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoReview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoReview(ctx, req.(*ExpoReviewInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoReviewEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoReviewEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoReviewEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoReviewEnable(ctx, req.(*SetExpoReviewEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoReview0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoReviewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoReview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoReview(ctx, req.(*ListExpoReviewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoReviewReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoReview0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoReviewRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoReview)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoReview(ctx, req.(*DeleteExpoReviewRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_AddExpoLive0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoLive
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundAddExpoLive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddExpoLive(ctx, req.(*ExpoLive))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddExpoLiveReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetExpoLive0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExpoLiveRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetExpoLive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExpoLive(ctx, req.(*GetExpoLiveRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ExpoLive)
		return ctx.Result(200, reply)
	}
}

func _Background_UpdateExpoLive0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ExpoLive
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundUpdateExpoLive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateExpoLive(ctx, req.(*ExpoLive))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SetExpoLiveEnable0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetExpoLiveEnableRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSetExpoLiveEnable)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetExpoLiveEnable(ctx, req.(*SetExpoLiveEnableRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ListExpoLive0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExpoLiveRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundListExpoLive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExpoLive(ctx, req.(*ListExpoLiveRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExpoLiveReply)
		return ctx.Result(200, reply)
	}
}

func _Background_DeleteExpoLive0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteExpoLiveRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundDeleteExpoLive)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteExpoLive(ctx, req.(*DeleteExpoLiveRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*common.EmptyReply)
		return ctx.Result(200, reply)
	}
}

func _Background_FacePhotoUpload0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FacePhotoUploadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundFacePhotoUpload)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FacePhotoUpload(ctx, req.(*FacePhotoUploadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FacePhotoUploadReply)
		return ctx.Result(200, reply)
	}
}

func _Background_FaceGroupCreate0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FaceGroupCreateRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundFaceGroupCreate)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FaceGroupCreate(ctx, req.(*FaceGroupCreateRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FaceGroupCreateReply)
		return ctx.Result(200, reply)
	}
}

func _Background_GetFaceGroupInfo0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FaceGroupInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundGetFaceGroupInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFaceGroupInfo(ctx, req.(*FaceGroupInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FaceGroupInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_FaceGroupList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FaceGroupListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundFaceGroupList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FaceGroupList(ctx, req.(*FaceGroupListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FaceGroupListReply)
		return ctx.Result(200, reply)
	}
}

func _Background_FaceGroupsByExpo0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FaceGroupsByExpoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundFaceGroupsByExpo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FaceGroupsByExpo(ctx, req.(*FaceGroupsByExpoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FaceGroupInfo)
		return ctx.Result(200, reply)
	}
}

func _Background_FacesPhotoList0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PhotoFacesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundFacesPhotoList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FacesPhotoList(ctx, req.(*PhotoFacesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PhotoFacesReply)
		return ctx.Result(200, reply)
	}
}

func _Background_SendMessageEmailsAPI0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SendMessageEmailsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundSendMessageEmailsAPI)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendMessageEmailsAPI(ctx, req.(*SendMessageEmailsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SendMessageEmailsReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ApplyAudit0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ApplyAuditRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundApplyAudit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ApplyAudit(ctx, req.(*ApplyAuditRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ApplyAuditReply)
		return ctx.Result(200, reply)
	}
}

func _Background_ApplyRefuseInfo0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ApplyRefuseInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundApplyRefuseInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ApplyRefuseInfo(ctx, req.(*ApplyRefuseInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ApplyRefuseInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Background_RefuseSetting0_HTTP_Handler(srv BackgroundHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RefuseSettingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationBackgroundRefuseSetting)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RefuseSetting(ctx, req.(*RefuseSettingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RefuseSettingReply)
		return ctx.Result(200, reply)
	}
}

type BackgroundHTTPClient interface {
	AddExpoCommunity(ctx context.Context, req *ExpoCommunity, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	AddExpoExhibitor(ctx context.Context, req *ExpoExhibitorInfo, opts ...http.CallOption) (rsp *AddExpoExhibitorReply, err error)
	AddExpoExhibitorMember(ctx context.Context, req *ExpoExhibitorMemberRequest, opts ...http.CallOption) (rsp *AddExpoExhibitorMemberReply, err error)
	AddExpoGuest(ctx context.Context, req *AddExpoGuestRequest, opts ...http.CallOption) (rsp *AddExpoGuestReply, err error)
	AddExpoGuide(ctx context.Context, req *ExpoGuideInfo, opts ...http.CallOption) (rsp *AddExpoGuideReply, err error)
	AddExpoLive(ctx context.Context, req *ExpoLive, opts ...http.CallOption) (rsp *AddExpoLiveReply, err error)
	AddExpoPartner(ctx context.Context, req *ExpoPartnerInfo, opts ...http.CallOption) (rsp *AddExpoPartnerReply, err error)
	AddExpoReview(ctx context.Context, req *ExpoReviewInfo, opts ...http.CallOption) (rsp *AddExpoReviewReply, err error)
	AddExpoSchedule(ctx context.Context, req *ExpoScheduleInfo, opts ...http.CallOption) (rsp *AddExpoScheduleReply, err error)
	AddGuest(ctx context.Context, req *Guest, opts ...http.CallOption) (rsp *AddGuestReply, err error)
	AddHall(ctx context.Context, req *ExpoHall, opts ...http.CallOption) (rsp *AddGuestReply, err error)
	ApplyAudit(ctx context.Context, req *ApplyAuditRequest, opts ...http.CallOption) (rsp *ApplyAuditReply, err error)
	ApplyRefuseInfo(ctx context.Context, req *ApplyRefuseInfoRequest, opts ...http.CallOption) (rsp *ApplyRefuseInfoReply, err error)
	DeleteExhibitorMember(ctx context.Context, req *DeleteExhibitorMemberRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoCommunity(ctx context.Context, req *DeleteExpoCommunityRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoExhibitor(ctx context.Context, req *DeleteExpoExhibitorRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoGuest(ctx context.Context, req *DeleteExpoGuestRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoGuide(ctx context.Context, req *DeleteExpoGuideRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoLive(ctx context.Context, req *DeleteExpoLiveRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoPartner(ctx context.Context, req *DeleteExpoPartnerRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoReview(ctx context.Context, req *DeleteExpoReviewRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteExpoSchedule(ctx context.Context, req *DeleteExpoScheduleRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteGuest(ctx context.Context, req *DeleteGuestRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	DeleteHall(ctx context.Context, req *DeleteHallRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	ExhibitorApplyList(ctx context.Context, req *ExhibitorApplyListRequest, opts ...http.CallOption) (rsp *ExhibitorApplyListReply, err error)
	ExpoExhibitorExport(ctx context.Context, req *ExpoExhibitorExportRequest, opts ...http.CallOption) (rsp *ExpoExhibitorExportReply, err error)
	ExpoExhibitorImport(ctx context.Context, req *ExpoExhibitorImportRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	ExportExpoGuest(ctx context.Context, req *ExportExpoGuestRequest, opts ...http.CallOption) (rsp *ExportExpoGuestReply, err error)
	ExportExpoPartner(ctx context.Context, req *ExportExpoPartnerRequest, opts ...http.CallOption) (rsp *ExportExpoPartnerReply, err error)
	ExportExpoSchedule(ctx context.Context, req *ExportExpoScheduleRequest, opts ...http.CallOption) (rsp *ExportExpoScheduleReply, err error)
	FaceGroupCreate(ctx context.Context, req *FaceGroupCreateRequest, opts ...http.CallOption) (rsp *FaceGroupCreateReply, err error)
	FaceGroupList(ctx context.Context, req *FaceGroupListRequest, opts ...http.CallOption) (rsp *FaceGroupListReply, err error)
	FaceGroupsByExpo(ctx context.Context, req *FaceGroupsByExpoRequest, opts ...http.CallOption) (rsp *FaceGroupInfo, err error)
	FacePhotoUpload(ctx context.Context, req *FacePhotoUploadRequest, opts ...http.CallOption) (rsp *FacePhotoUploadReply, err error)
	FacesPhotoList(ctx context.Context, req *PhotoFacesRequest, opts ...http.CallOption) (rsp *PhotoFacesReply, err error)
	GetExpoCommunity(ctx context.Context, req *GetExpoCommunityRequest, opts ...http.CallOption) (rsp *ExpoCommunity, err error)
	GetExpoExhibitor(ctx context.Context, req *GetExpoExhibitorRequest, opts ...http.CallOption) (rsp *ExpoExhibitorInfo, err error)
	GetExpoGuest(ctx context.Context, req *GetExpoGuestRequest, opts ...http.CallOption) (rsp *GetExpoGuestReply, err error)
	GetExpoGuide(ctx context.Context, req *GetExpoGuideRequest, opts ...http.CallOption) (rsp *ExpoGuideInfo, err error)
	GetExpoLive(ctx context.Context, req *GetExpoLiveRequest, opts ...http.CallOption) (rsp *ExpoLive, err error)
	GetExpoPartner(ctx context.Context, req *GetExpoPartnerRequest, opts ...http.CallOption) (rsp *ExpoPartnerInfo, err error)
	GetExpoPartnerType(ctx context.Context, req *GetExpoPartnerTypeRequest, opts ...http.CallOption) (rsp *GetExpoPartnerTypeReply, err error)
	GetExpoReview(ctx context.Context, req *GetExpoReviewRequest, opts ...http.CallOption) (rsp *ExpoReviewInfo, err error)
	GetExpoSchedule(ctx context.Context, req *GetExpoScheduleRequest, opts ...http.CallOption) (rsp *ExpoScheduleInfo, err error)
	GetFaceGroupInfo(ctx context.Context, req *FaceGroupInfoRequest, opts ...http.CallOption) (rsp *FaceGroupInfo, err error)
	GetGuest(ctx context.Context, req *GetGuestRequest, opts ...http.CallOption) (rsp *Guest, err error)
	GetHall(ctx context.Context, req *GetHallRequest, opts ...http.CallOption) (rsp *ExpoHall, err error)
	GetSponsorLevel(ctx context.Context, req *common.EmptyRequest, opts ...http.CallOption) (rsp *GetSponsorLevelReply, err error)
	GetSyncStatus(ctx context.Context, req *GetSyncStatusRequest, opts ...http.CallOption) (rsp *GetSyncStatusReply, err error)
	ImportExpoPartner(ctx context.Context, req *ImportExpoPartnerRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	ImportExpoSchedule(ctx context.Context, req *ImportExpoScheduleRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	ListExpoExhibitor(ctx context.Context, req *ListExpoExhibitorRequest, opts ...http.CallOption) (rsp *ListExpoExhibitorReply, err error)
	ListExpoGuest(ctx context.Context, req *ListExpoGuestRequest, opts ...http.CallOption) (rsp *ListExpoGuestReply, err error)
	ListExpoGuide(ctx context.Context, req *ListExpoGuideRequest, opts ...http.CallOption) (rsp *ListExpoGuideReply, err error)
	ListExpoLive(ctx context.Context, req *ListExpoLiveRequest, opts ...http.CallOption) (rsp *ListExpoLiveReply, err error)
	ListExpoPartner(ctx context.Context, req *ListExpoPartnerRequest, opts ...http.CallOption) (rsp *ListExpoPartnerReply, err error)
	ListExpoReview(ctx context.Context, req *ListExpoReviewRequest, opts ...http.CallOption) (rsp *ListExpoReviewReply, err error)
	ListExpoSchedule(ctx context.Context, req *ListExpoScheduleRequest, opts ...http.CallOption) (rsp *ListExpoScheduleReply, err error)
	ListGuest(ctx context.Context, req *ListGuestRequest, opts ...http.CallOption) (rsp *ListGuestReply, err error)
	ListGuestsNotInExpo(ctx context.Context, req *ListGuestsNotInExpoRequest, opts ...http.CallOption) (rsp *ListGuestsNotInExpoReply, err error)
	ListHall(ctx context.Context, req *ListHallRequest, opts ...http.CallOption) (rsp *ListHallReply, err error)
	ListLiveImage(ctx context.Context, req *ListLiveImageRequest, opts ...http.CallOption) (rsp *ListLiveImageReply, err error)
	RefuseSetting(ctx context.Context, req *RefuseSettingRequest, opts ...http.CallOption) (rsp *RefuseSettingReply, err error)
	SearchExhibitorMember(ctx context.Context, req *SearchExhibitorMemberRequest, opts ...http.CallOption) (rsp *SearchExhibitorMemberReply, err error)
	SendMessageEmailsAPI(ctx context.Context, req *SendMessageEmailsRequest, opts ...http.CallOption) (rsp *SendMessageEmailsReply, err error)
	SetExhibitorApplyStatus(ctx context.Context, req *SetExhibitorApplyStatusRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoCommunityEnable(ctx context.Context, req *SetExpoCommunityEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoExhibitorEmployeeEnable(ctx context.Context, req *SetExpoExhibitorEmployeeEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoExhibitorEnable(ctx context.Context, req *SetExpoExhibitorEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoGuestEnable(ctx context.Context, req *SetExpoGuestEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoGuideEnable(ctx context.Context, req *SetExpoGuideEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoLiveEnable(ctx context.Context, req *SetExpoLiveEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoPartnerEnable(ctx context.Context, req *SetExpoPartnerEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoReviewEnable(ctx context.Context, req *SetExpoReviewEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetExpoScheduleEnable(ctx context.Context, req *SetExpoScheduleEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetGuestEnable(ctx context.Context, req *SetGuestEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SetHallEnable(ctx context.Context, req *SetHallEnableRequest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	SyncFace(ctx context.Context, req *SyncFaceRequest, opts ...http.CallOption) (rsp *SyncFaceReply, err error)
	SyncLiveImage(ctx context.Context, req *SyncLiveImageRequest, opts ...http.CallOption) (rsp *SyncLiveImageReply, err error)
	UpdateExpoCommunity(ctx context.Context, req *ExpoCommunity, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateExpoExhibitor(ctx context.Context, req *ExpoExhibitorInfo, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateExpoGuide(ctx context.Context, req *ExpoGuideInfo, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateExpoLive(ctx context.Context, req *ExpoLive, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateExpoPartner(ctx context.Context, req *ExpoPartnerInfo, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateExpoReview(ctx context.Context, req *ExpoReviewInfo, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateExpoSchedule(ctx context.Context, req *ExpoScheduleInfo, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateGuest(ctx context.Context, req *Guest, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
	UpdateHall(ctx context.Context, req *ExpoHall, opts ...http.CallOption) (rsp *common.EmptyReply, err error)
}

type BackgroundHTTPClientImpl struct {
	cc *http.Client
}

func NewBackgroundHTTPClient(client *http.Client) BackgroundHTTPClient {
	return &BackgroundHTTPClientImpl{client}
}

func (c *BackgroundHTTPClientImpl) AddExpoCommunity(ctx context.Context, in *ExpoCommunity, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/community/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoCommunity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoExhibitor(ctx context.Context, in *ExpoExhibitorInfo, opts ...http.CallOption) (*AddExpoExhibitorReply, error) {
	var out AddExpoExhibitorReply
	pattern := "/v1/admin/expo/exhibitor/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoExhibitor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoExhibitorMember(ctx context.Context, in *ExpoExhibitorMemberRequest, opts ...http.CallOption) (*AddExpoExhibitorMemberReply, error) {
	var out AddExpoExhibitorMemberReply
	pattern := "/v1/admin/expo/exhibitor/member/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoExhibitorMember))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoGuest(ctx context.Context, in *AddExpoGuestRequest, opts ...http.CallOption) (*AddExpoGuestReply, error) {
	var out AddExpoGuestReply
	pattern := "/v1/admin/expo/guest/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoGuide(ctx context.Context, in *ExpoGuideInfo, opts ...http.CallOption) (*AddExpoGuideReply, error) {
	var out AddExpoGuideReply
	pattern := "/v1/admin/expo/guide/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoLive(ctx context.Context, in *ExpoLive, opts ...http.CallOption) (*AddExpoLiveReply, error) {
	var out AddExpoLiveReply
	pattern := "/v1/admin/expo/live/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoLive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoPartner(ctx context.Context, in *ExpoPartnerInfo, opts ...http.CallOption) (*AddExpoPartnerReply, error) {
	var out AddExpoPartnerReply
	pattern := "/v1/admin/expo/partner/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoReview(ctx context.Context, in *ExpoReviewInfo, opts ...http.CallOption) (*AddExpoReviewReply, error) {
	var out AddExpoReviewReply
	pattern := "/v1/admin/expo/review/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoReview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddExpoSchedule(ctx context.Context, in *ExpoScheduleInfo, opts ...http.CallOption) (*AddExpoScheduleReply, error) {
	var out AddExpoScheduleReply
	pattern := "/v1/admin/expo/schedule/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddGuest(ctx context.Context, in *Guest, opts ...http.CallOption) (*AddGuestReply, error) {
	var out AddGuestReply
	pattern := "/v1/admin/guest/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) AddHall(ctx context.Context, in *ExpoHall, opts ...http.CallOption) (*AddGuestReply, error) {
	var out AddGuestReply
	pattern := "/v1/admin/expo/hall/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundAddHall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ApplyAudit(ctx context.Context, in *ApplyAuditRequest, opts ...http.CallOption) (*ApplyAuditReply, error) {
	var out ApplyAuditReply
	pattern := "/v1/admin/registration/applyAudit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundApplyAudit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ApplyRefuseInfo(ctx context.Context, in *ApplyRefuseInfoRequest, opts ...http.CallOption) (*ApplyRefuseInfoReply, error) {
	var out ApplyRefuseInfoReply
	pattern := "/v1/admin/registration/applyRefuseInfo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundApplyRefuseInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExhibitorMember(ctx context.Context, in *DeleteExhibitorMemberRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/exhibitor/member/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExhibitorMember))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoCommunity(ctx context.Context, in *DeleteExpoCommunityRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/community/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoCommunity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoExhibitor(ctx context.Context, in *DeleteExpoExhibitorRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/exhibitor/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoExhibitor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoGuest(ctx context.Context, in *DeleteExpoGuestRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/guest/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoGuide(ctx context.Context, in *DeleteExpoGuideRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/guide/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoLive(ctx context.Context, in *DeleteExpoLiveRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/live/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoLive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoPartner(ctx context.Context, in *DeleteExpoPartnerRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/partner/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoReview(ctx context.Context, in *DeleteExpoReviewRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/review/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoReview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteExpoSchedule(ctx context.Context, in *DeleteExpoScheduleRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/schedule/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteGuest(ctx context.Context, in *DeleteGuestRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/guest/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) DeleteHall(ctx context.Context, in *DeleteHallRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/hall/delete"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundDeleteHall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ExhibitorApplyList(ctx context.Context, in *ExhibitorApplyListRequest, opts ...http.CallOption) (*ExhibitorApplyListReply, error) {
	var out ExhibitorApplyListReply
	pattern := "/v1/admin/exhibitor/apply/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundExhibitorApplyList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ExpoExhibitorExport(ctx context.Context, in *ExpoExhibitorExportRequest, opts ...http.CallOption) (*ExpoExhibitorExportReply, error) {
	var out ExpoExhibitorExportReply
	pattern := "/v1/admin/expo/exhibitor/export"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundExpoExhibitorExport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ExpoExhibitorImport(ctx context.Context, in *ExpoExhibitorImportRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/exhibitor/import"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundExpoExhibitorImport))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ExportExpoGuest(ctx context.Context, in *ExportExpoGuestRequest, opts ...http.CallOption) (*ExportExpoGuestReply, error) {
	var out ExportExpoGuestReply
	pattern := "/v1/admin/expo/guest/export"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundExportExpoGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ExportExpoPartner(ctx context.Context, in *ExportExpoPartnerRequest, opts ...http.CallOption) (*ExportExpoPartnerReply, error) {
	var out ExportExpoPartnerReply
	pattern := "/v1/admin/expo/partner/export"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundExportExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ExportExpoSchedule(ctx context.Context, in *ExportExpoScheduleRequest, opts ...http.CallOption) (*ExportExpoScheduleReply, error) {
	var out ExportExpoScheduleReply
	pattern := "/v1/admin/expo/schedule/export"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundExportExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) FaceGroupCreate(ctx context.Context, in *FaceGroupCreateRequest, opts ...http.CallOption) (*FaceGroupCreateReply, error) {
	var out FaceGroupCreateReply
	pattern := "/v1/face/createGroups"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundFaceGroupCreate))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) FaceGroupList(ctx context.Context, in *FaceGroupListRequest, opts ...http.CallOption) (*FaceGroupListReply, error) {
	var out FaceGroupListReply
	pattern := "/v1/face/groupsList"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundFaceGroupList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) FaceGroupsByExpo(ctx context.Context, in *FaceGroupsByExpoRequest, opts ...http.CallOption) (*FaceGroupInfo, error) {
	var out FaceGroupInfo
	pattern := "/v1/face/groupsByExpo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundFaceGroupsByExpo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) FacePhotoUpload(ctx context.Context, in *FacePhotoUploadRequest, opts ...http.CallOption) (*FacePhotoUploadReply, error) {
	var out FacePhotoUploadReply
	pattern := "/v1/face/photosUpload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundFacePhotoUpload))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) FacesPhotoList(ctx context.Context, in *PhotoFacesRequest, opts ...http.CallOption) (*PhotoFacesReply, error) {
	var out PhotoFacesReply
	pattern := "/v1/face/PhotoList"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundFacesPhotoList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoCommunity(ctx context.Context, in *GetExpoCommunityRequest, opts ...http.CallOption) (*ExpoCommunity, error) {
	var out ExpoCommunity
	pattern := "/v1/admin/expo/community/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoCommunity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoExhibitor(ctx context.Context, in *GetExpoExhibitorRequest, opts ...http.CallOption) (*ExpoExhibitorInfo, error) {
	var out ExpoExhibitorInfo
	pattern := "/v1/admin/expo/exhibitor/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoExhibitor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoGuest(ctx context.Context, in *GetExpoGuestRequest, opts ...http.CallOption) (*GetExpoGuestReply, error) {
	var out GetExpoGuestReply
	pattern := "/v1/admin/expo/guest/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoGuide(ctx context.Context, in *GetExpoGuideRequest, opts ...http.CallOption) (*ExpoGuideInfo, error) {
	var out ExpoGuideInfo
	pattern := "/v1/admin/expo/guide/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoLive(ctx context.Context, in *GetExpoLiveRequest, opts ...http.CallOption) (*ExpoLive, error) {
	var out ExpoLive
	pattern := "/v1/admin/expo/live/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoLive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoPartner(ctx context.Context, in *GetExpoPartnerRequest, opts ...http.CallOption) (*ExpoPartnerInfo, error) {
	var out ExpoPartnerInfo
	pattern := "/v1/admin/expo/partner/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoPartnerType(ctx context.Context, in *GetExpoPartnerTypeRequest, opts ...http.CallOption) (*GetExpoPartnerTypeReply, error) {
	var out GetExpoPartnerTypeReply
	pattern := "/v1/admin/expo/partner/type"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoPartnerType))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoReview(ctx context.Context, in *GetExpoReviewRequest, opts ...http.CallOption) (*ExpoReviewInfo, error) {
	var out ExpoReviewInfo
	pattern := "/v1/admin/expo/review/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoReview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetExpoSchedule(ctx context.Context, in *GetExpoScheduleRequest, opts ...http.CallOption) (*ExpoScheduleInfo, error) {
	var out ExpoScheduleInfo
	pattern := "/v1/admin/expo/schedule/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetFaceGroupInfo(ctx context.Context, in *FaceGroupInfoRequest, opts ...http.CallOption) (*FaceGroupInfo, error) {
	var out FaceGroupInfo
	pattern := "/v1/face/groupsInfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundGetFaceGroupInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetGuest(ctx context.Context, in *GetGuestRequest, opts ...http.CallOption) (*Guest, error) {
	var out Guest
	pattern := "/v1/admin/guest/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetHall(ctx context.Context, in *GetHallRequest, opts ...http.CallOption) (*ExpoHall, error) {
	var out ExpoHall
	pattern := "/v1/admin/expo/hall/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetHall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetSponsorLevel(ctx context.Context, in *common.EmptyRequest, opts ...http.CallOption) (*GetSponsorLevelReply, error) {
	var out GetSponsorLevelReply
	pattern := "/v1/admin/expo/sponsor/level"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetSponsorLevel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) GetSyncStatus(ctx context.Context, in *GetSyncStatusRequest, opts ...http.CallOption) (*GetSyncStatusReply, error) {
	var out GetSyncStatusReply
	pattern := "/v1/admin/expo/sync-status"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundGetSyncStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ImportExpoPartner(ctx context.Context, in *ImportExpoPartnerRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/partner/import"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundImportExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ImportExpoSchedule(ctx context.Context, in *ImportExpoScheduleRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/schedule/import"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundImportExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoExhibitor(ctx context.Context, in *ListExpoExhibitorRequest, opts ...http.CallOption) (*ListExpoExhibitorReply, error) {
	var out ListExpoExhibitorReply
	pattern := "/v1/admin/expo/exhibitor"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoExhibitor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoGuest(ctx context.Context, in *ListExpoGuestRequest, opts ...http.CallOption) (*ListExpoGuestReply, error) {
	var out ListExpoGuestReply
	pattern := "/v1/admin/expo/guest/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoGuide(ctx context.Context, in *ListExpoGuideRequest, opts ...http.CallOption) (*ListExpoGuideReply, error) {
	var out ListExpoGuideReply
	pattern := "/v1/admin/expo/guide/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoLive(ctx context.Context, in *ListExpoLiveRequest, opts ...http.CallOption) (*ListExpoLiveReply, error) {
	var out ListExpoLiveReply
	pattern := "/v1/admin/expo/live/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoLive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoPartner(ctx context.Context, in *ListExpoPartnerRequest, opts ...http.CallOption) (*ListExpoPartnerReply, error) {
	var out ListExpoPartnerReply
	pattern := "/v1/admin/expo/partner/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoReview(ctx context.Context, in *ListExpoReviewRequest, opts ...http.CallOption) (*ListExpoReviewReply, error) {
	var out ListExpoReviewReply
	pattern := "/v1/admin/expo/review/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoReview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListExpoSchedule(ctx context.Context, in *ListExpoScheduleRequest, opts ...http.CallOption) (*ListExpoScheduleReply, error) {
	var out ListExpoScheduleReply
	pattern := "/v1/admin/expo/schedule/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListGuest(ctx context.Context, in *ListGuestRequest, opts ...http.CallOption) (*ListGuestReply, error) {
	var out ListGuestReply
	pattern := "/v1/admin/guest/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListGuestsNotInExpo(ctx context.Context, in *ListGuestsNotInExpoRequest, opts ...http.CallOption) (*ListGuestsNotInExpoReply, error) {
	var out ListGuestsNotInExpoReply
	pattern := "/v1/admin/guest/list-not-in-expo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListGuestsNotInExpo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListHall(ctx context.Context, in *ListHallRequest, opts ...http.CallOption) (*ListHallReply, error) {
	var out ListHallReply
	pattern := "/v1/admin/expo/hall/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListHall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) ListLiveImage(ctx context.Context, in *ListLiveImageRequest, opts ...http.CallOption) (*ListLiveImageReply, error) {
	var out ListLiveImageReply
	pattern := "/v1/admin/image/live/list"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundListLiveImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) RefuseSetting(ctx context.Context, in *RefuseSettingRequest, opts ...http.CallOption) (*RefuseSettingReply, error) {
	var out RefuseSettingReply
	pattern := "/v1/admin/registration/refuseSetting"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundRefuseSetting))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SearchExhibitorMember(ctx context.Context, in *SearchExhibitorMemberRequest, opts ...http.CallOption) (*SearchExhibitorMemberReply, error) {
	var out SearchExhibitorMemberReply
	pattern := "/v1/admin/expo/exhibitor/member"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationBackgroundSearchExhibitorMember))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SendMessageEmailsAPI(ctx context.Context, in *SendMessageEmailsRequest, opts ...http.CallOption) (*SendMessageEmailsReply, error) {
	var out SendMessageEmailsReply
	pattern := "/v1/admin/expo/email/send"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSendMessageEmailsAPI))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExhibitorApplyStatus(ctx context.Context, in *SetExhibitorApplyStatusRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/exhibitor/apply/status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExhibitorApplyStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoCommunityEnable(ctx context.Context, in *SetExpoCommunityEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/community/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoCommunityEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoExhibitorEmployeeEnable(ctx context.Context, in *SetExpoExhibitorEmployeeEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/exhibitor/employee/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoExhibitorEmployeeEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoExhibitorEnable(ctx context.Context, in *SetExpoExhibitorEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/exhibitor/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoExhibitorEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoGuestEnable(ctx context.Context, in *SetExpoGuestEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/guest/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoGuestEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoGuideEnable(ctx context.Context, in *SetExpoGuideEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/guide/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoGuideEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoLiveEnable(ctx context.Context, in *SetExpoLiveEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/live/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoLiveEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoPartnerEnable(ctx context.Context, in *SetExpoPartnerEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/partner/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoPartnerEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoReviewEnable(ctx context.Context, in *SetExpoReviewEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/review/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoReviewEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetExpoScheduleEnable(ctx context.Context, in *SetExpoScheduleEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/schedule/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetExpoScheduleEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetGuestEnable(ctx context.Context, in *SetGuestEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/guest/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetGuestEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SetHallEnable(ctx context.Context, in *SetHallEnableRequest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/hall/enable"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSetHallEnable))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SyncFace(ctx context.Context, in *SyncFaceRequest, opts ...http.CallOption) (*SyncFaceReply, error) {
	var out SyncFaceReply
	pattern := "/v1/admin/sync/face"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSyncFace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) SyncLiveImage(ctx context.Context, in *SyncLiveImageRequest, opts ...http.CallOption) (*SyncLiveImageReply, error) {
	var out SyncLiveImageReply
	pattern := "/v1/admin/image/live/sync"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundSyncLiveImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoCommunity(ctx context.Context, in *ExpoCommunity, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/community/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoCommunity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoExhibitor(ctx context.Context, in *ExpoExhibitorInfo, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/exhibitor/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoExhibitor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoGuide(ctx context.Context, in *ExpoGuideInfo, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/guide/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoLive(ctx context.Context, in *ExpoLive, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/live/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoLive))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoPartner(ctx context.Context, in *ExpoPartnerInfo, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/partner/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoPartner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoReview(ctx context.Context, in *ExpoReviewInfo, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/review/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoReview))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateExpoSchedule(ctx context.Context, in *ExpoScheduleInfo, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/schedule/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateExpoSchedule))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateGuest(ctx context.Context, in *Guest, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/guest/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateGuest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *BackgroundHTTPClientImpl) UpdateHall(ctx context.Context, in *ExpoHall, opts ...http.CallOption) (*common.EmptyReply, error) {
	var out common.EmptyReply
	pattern := "/v1/admin/expo/hall/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationBackgroundUpdateHall))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
