// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.25.3
// source: article/ArticlePush.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RelationTraderType int32

const (
	RelationTraderType_RelationTraderType_None            RelationTraderType = 0 // 未知
	RelationTraderType_RelationTraderType_Trader          RelationTraderType = 1 // 交易商
	RelationTraderType_RelationTraderType_Agent           RelationTraderType = 3 // 代理商
	RelationTraderType_RelationTraderType_Dealer          RelationTraderType = 4 // 受评方
	RelationTraderType_RelationTraderType_ServiceProvider RelationTraderType = 5 // 服务商
	RelationTraderType_RelationTraderType_Stock           RelationTraderType = 6 // 券商
)

// Enum value maps for RelationTraderType.
var (
	RelationTraderType_name = map[int32]string{
		0: "RelationTraderType_None",
		1: "RelationTraderType_Trader",
		3: "RelationTraderType_Agent",
		4: "RelationTraderType_Dealer",
		5: "RelationTraderType_ServiceProvider",
		6: "RelationTraderType_Stock",
	}
	RelationTraderType_value = map[string]int32{
		"RelationTraderType_None":            0,
		"RelationTraderType_Trader":          1,
		"RelationTraderType_Agent":           3,
		"RelationTraderType_Dealer":          4,
		"RelationTraderType_ServiceProvider": 5,
		"RelationTraderType_Stock":           6,
	}
)

func (x RelationTraderType) Enum() *RelationTraderType {
	p := new(RelationTraderType)
	*p = x
	return p
}

func (x RelationTraderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationTraderType) Descriptor() protoreflect.EnumDescriptor {
	return file_article_ArticlePush_proto_enumTypes[0].Descriptor()
}

func (RelationTraderType) Type() protoreflect.EnumType {
	return &file_article_ArticlePush_proto_enumTypes[0]
}

func (x RelationTraderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationTraderType.Descriptor instead.
func (RelationTraderType) EnumDescriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{0}
}

type RelationType int32

const (
	RelationType_RelationType_None            RelationType = 0 // 未知
	RelationType_RelationType_BelongTo        RelationType = 1 // 文章所属交易商
	RelationType_RelationType_Related         RelationType = 2 // 文章关联交易商/代理商
	RelationType_RelationType_ServiceProvider RelationType = 3 // 文章关联服务商
)

// Enum value maps for RelationType.
var (
	RelationType_name = map[int32]string{
		0: "RelationType_None",
		1: "RelationType_BelongTo",
		2: "RelationType_Related",
		3: "RelationType_ServiceProvider",
	}
	RelationType_value = map[string]int32{
		"RelationType_None":            0,
		"RelationType_BelongTo":        1,
		"RelationType_Related":         2,
		"RelationType_ServiceProvider": 3,
	}
)

func (x RelationType) Enum() *RelationType {
	p := new(RelationType)
	*p = x
	return p
}

func (x RelationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationType) Descriptor() protoreflect.EnumDescriptor {
	return file_article_ArticlePush_proto_enumTypes[1].Descriptor()
}

func (RelationType) Type() protoreflect.EnumType {
	return &file_article_ArticlePush_proto_enumTypes[1]
}

func (x RelationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationType.Descriptor instead.
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{1}
}

type ArticleBelongToCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code         string             `protobuf:"bytes,1,opt,name=Code,json=Code,proto3" json:"Code"`                                                                                                         // 所属交易商或服务商code
	RelationType RelationTraderType `protobuf:"varint,2,opt,name=RelationType,json=RelationType,proto3,enum=ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationTraderType" json:"RelationType"` // 相关类型，交易商或服务商
	CateName     string             `protobuf:"bytes,3,opt,name=CateName,json=CateName,proto3" json:"CateName"`                                                                                             // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
}

func (x *ArticleBelongToCodeResult) Reset() {
	*x = ArticleBelongToCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleBelongToCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleBelongToCodeResult) ProtoMessage() {}

func (x *ArticleBelongToCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleBelongToCodeResult.ProtoReflect.Descriptor instead.
func (*ArticleBelongToCodeResult) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{0}
}

func (x *ArticleBelongToCodeResult) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ArticleBelongToCodeResult) GetRelationType() RelationTraderType {
	if x != nil {
		return x.RelationType
	}
	return RelationTraderType_RelationTraderType_None
}

func (x *ArticleBelongToCodeResult) GetCateName() string {
	if x != nil {
		return x.CateName
	}
	return ""
}

type ArticleByCodesQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtCodes                 []string `protobuf:"bytes,1,rep,name=ArtCodes,json=ArtCodes,proto3" json:"ArtCodes"`
	IsRequiredRelationTrader bool     `protobuf:"varint,2,opt,name=IsRequiredRelationTrader,json=IsRequiredRelationTrader,proto3" json:"IsRequiredRelationTrader"` // 是否需要相关交易商
}

func (x *ArticleByCodesQuery) Reset() {
	*x = ArticleByCodesQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleByCodesQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleByCodesQuery) ProtoMessage() {}

func (x *ArticleByCodesQuery) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleByCodesQuery.ProtoReflect.Descriptor instead.
func (*ArticleByCodesQuery) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{1}
}

func (x *ArticleByCodesQuery) GetArtCodes() []string {
	if x != nil {
		return x.ArtCodes
	}
	return nil
}

func (x *ArticleByCodesQuery) GetIsRequiredRelationTrader() bool {
	if x != nil {
		return x.IsRequiredRelationTrader
	}
	return false
}

type ArticleByCodesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Articles []*ArticleList `protobuf:"bytes,1,rep,name=Articles,json=Articles,proto3" json:"Articles"`
}

func (x *ArticleByCodesResult) Reset() {
	*x = ArticleByCodesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleByCodesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleByCodesResult) ProtoMessage() {}

func (x *ArticleByCodesResult) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleByCodesResult.ProtoReflect.Descriptor instead.
func (*ArticleByCodesResult) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{2}
}

func (x *ArticleByCodesResult) GetArticles() []*ArticleList {
	if x != nil {
		return x.Articles
	}
	return nil
}

type ArticleDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtCode             string             `protobuf:"bytes,1,opt,name=ArtCode,json=ArtCode,proto3" json:"ArtCode"`                                      // 文章Code
	CategoryId          string             `protobuf:"bytes,2,opt,name=CategoryId,json=CategoryId,proto3" json:"CategoryId"`                             // 文章分类编码
	Title               string             `protobuf:"bytes,3,opt,name=Title,json=Title,proto3" json:"Title"`                                            // 标题
	Summary             string             `protobuf:"bytes,4,opt,name=Summary,json=Summary,proto3" json:"Summary"`                                      // 摘要
	Content             string             `protobuf:"bytes,5,opt,name=Content,json=Content,proto3" json:"Content"`                                      // 正文
	LanguageId          string             `protobuf:"bytes,6,opt,name=LanguageId,json=LanguageId,proto3" json:"LanguageId"`                             // 文章语言
	Tag                 string             `protobuf:"bytes,7,opt,name=Tag,json=Tag,proto3" json:"Tag"`                                                  // tag
	Icon                string             `protobuf:"bytes,8,opt,name=Icon,json=Icon,proto3" json:"Icon"`                                               // icon
	Share               string             `protobuf:"bytes,9,opt,name=Share,json=Share,proto3" json:"Share"`                                            // 文章分享页地址
	Timestamp           int64              `protobuf:"varint,10,opt,name=Timestamp,json=Timestamp,proto3" json:"Timestamp"`                              // 显示日期时间戳
	ArticleBelongToCode string             `protobuf:"bytes,11,opt,name=ArticleBelongToCode,json=ArticleBelongToCode,proto3" json:"ArticleBelongToCode"` // 文章所属交易商
	Associations        []*ArticleRelation `protobuf:"bytes,12,rep,name=Associations,json=Associations,proto3" json:"Associations"`
	IsWhiteList         bool               `protobuf:"varint,13,opt,name=IsWhiteList,json=IsWhiteList,proto3" json:"IsWhiteList"` // 是否白名单
	ArtKeyword          string             `protobuf:"bytes,14,opt,name=ArtKeyword,json=ArtKeyword,proto3" json:"ArtKeyword"`     // 关键词
	ArtImageUrl         string             `protobuf:"bytes,15,opt,name=ArtImageUrl,json=ArtImageUrl,proto3" json:"ArtImageUrl"`  // 头图
	IsShow              bool               `protobuf:"varint,16,opt,name=IsShow,json=IsShow,proto3" json:"IsShow"`                // 是否显示
}

func (x *ArticleDetail) Reset() {
	*x = ArticleDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetail) ProtoMessage() {}

func (x *ArticleDetail) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetail.ProtoReflect.Descriptor instead.
func (*ArticleDetail) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{3}
}

func (x *ArticleDetail) GetArtCode() string {
	if x != nil {
		return x.ArtCode
	}
	return ""
}

func (x *ArticleDetail) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *ArticleDetail) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleDetail) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *ArticleDetail) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ArticleDetail) GetLanguageId() string {
	if x != nil {
		return x.LanguageId
	}
	return ""
}

func (x *ArticleDetail) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *ArticleDetail) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *ArticleDetail) GetShare() string {
	if x != nil {
		return x.Share
	}
	return ""
}

func (x *ArticleDetail) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ArticleDetail) GetArticleBelongToCode() string {
	if x != nil {
		return x.ArticleBelongToCode
	}
	return ""
}

func (x *ArticleDetail) GetAssociations() []*ArticleRelation {
	if x != nil {
		return x.Associations
	}
	return nil
}

func (x *ArticleDetail) GetIsWhiteList() bool {
	if x != nil {
		return x.IsWhiteList
	}
	return false
}

func (x *ArticleDetail) GetArtKeyword() string {
	if x != nil {
		return x.ArtKeyword
	}
	return ""
}

func (x *ArticleDetail) GetArtImageUrl() string {
	if x != nil {
		return x.ArtImageUrl
	}
	return ""
}

func (x *ArticleDetail) GetIsShow() bool {
	if x != nil {
		return x.IsShow
	}
	return false
}

type ArticleDetailQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtCode string `protobuf:"bytes,1,opt,name=ArtCode,json=ArtCode,proto3" json:"ArtCode"` // 文章Code
}

func (x *ArticleDetailQuery) Reset() {
	*x = ArticleDetailQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleDetailQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleDetailQuery) ProtoMessage() {}

func (x *ArticleDetailQuery) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleDetailQuery.ProtoReflect.Descriptor instead.
func (*ArticleDetailQuery) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{4}
}

func (x *ArticleDetailQuery) GetArtCode() string {
	if x != nil {
		return x.ArtCode
	}
	return ""
}

type ArticleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtCode             string     `protobuf:"bytes,1,opt,name=ArtCode,json=ArtCode,proto3" json:"ArtCode"`                                     // 文章Code
	Title               string     `protobuf:"bytes,2,opt,name=Title,json=Title,proto3" json:"Title"`                                           // 标题
	Banner              *ImageInfo `protobuf:"bytes,3,opt,name=Banner,json=Banner,proto3" json:"Banner"`                                        // 头图
	Timestamp           int64      `protobuf:"varint,4,opt,name=Timestamp,json=Timestamp,proto3" json:"Timestamp"`                              // 显示日期时间戳
	LanguageId          string     `protobuf:"bytes,5,opt,name=LanguageId,json=LanguageId,proto3" json:"LanguageId"`                            // 文章语言
	FirstImage          *ImageInfo `protobuf:"bytes,6,opt,name=FirstImage,json=FirstImage,proto3" json:"FirstImage"`                            // 内容里面的第一张图
	CategoryId          string     `protobuf:"bytes,7,opt,name=CategoryId,json=CategoryId,proto3" json:"CategoryId"`                            // 文章类型
	ArticleBelongToCode string     `protobuf:"bytes,8,opt,name=ArticleBelongToCode,json=ArticleBelongToCode,proto3" json:"ArticleBelongToCode"` // 文章所属交易商Code
	RelationCodes       []string   `protobuf:"bytes,9,rep,name=RelationCodes,json=RelationCodes,proto3" json:"RelationCodes"`
	Summary             string     `protobuf:"bytes,10,opt,name=Summary,json=Summary,proto3" json:"Summary"`    // 摘要
	TextPart            string     `protobuf:"bytes,11,opt,name=TextPart,json=TextPart,proto3" json:"TextPart"` // 部分正文内容，暂时取120个字，web端使用
	HitsInfo            string     `protobuf:"bytes,12,opt,name=HitsInfo,json=HitsInfo,proto3" json:"HitsInfo"` // 统计信息
}

func (x *ArticleList) Reset() {
	*x = ArticleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleList) ProtoMessage() {}

func (x *ArticleList) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleList.ProtoReflect.Descriptor instead.
func (*ArticleList) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{5}
}

func (x *ArticleList) GetArtCode() string {
	if x != nil {
		return x.ArtCode
	}
	return ""
}

func (x *ArticleList) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleList) GetBanner() *ImageInfo {
	if x != nil {
		return x.Banner
	}
	return nil
}

func (x *ArticleList) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ArticleList) GetLanguageId() string {
	if x != nil {
		return x.LanguageId
	}
	return ""
}

func (x *ArticleList) GetFirstImage() *ImageInfo {
	if x != nil {
		return x.FirstImage
	}
	return nil
}

func (x *ArticleList) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *ArticleList) GetArticleBelongToCode() string {
	if x != nil {
		return x.ArticleBelongToCode
	}
	return ""
}

func (x *ArticleList) GetRelationCodes() []string {
	if x != nil {
		return x.RelationCodes
	}
	return nil
}

func (x *ArticleList) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *ArticleList) GetTextPart() string {
	if x != nil {
		return x.TextPart
	}
	return ""
}

func (x *ArticleList) GetHitsInfo() string {
	if x != nil {
		return x.HitsInfo
	}
	return ""
}

type ArticleQuery100Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CateName                 string `protobuf:"bytes,1,opt,name=CateName,json=CateName,proto3" json:"CateName"`                                                  // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
	IsRequiredRelationTrader bool   `protobuf:"varint,2,opt,name=IsRequiredRelationTrader,json=IsRequiredRelationTrader,proto3" json:"IsRequiredRelationTrader"` // 是否需要相关交易商
}

func (x *ArticleQuery100Request) Reset() {
	*x = ArticleQuery100Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleQuery100Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleQuery100Request) ProtoMessage() {}

func (x *ArticleQuery100Request) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleQuery100Request.ProtoReflect.Descriptor instead.
func (*ArticleQuery100Request) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{6}
}

func (x *ArticleQuery100Request) GetCateName() string {
	if x != nil {
		return x.CateName
	}
	return ""
}

func (x *ArticleQuery100Request) GetIsRequiredRelationTrader() bool {
	if x != nil {
		return x.IsRequiredRelationTrader
	}
	return false
}

type ArticleQueryCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CateName   string `protobuf:"bytes,1,opt,name=CateName,json=CateName,proto3" json:"CateName"`        // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
	IsHomePage bool   `protobuf:"varint,2,opt,name=IsHomePage,json=IsHomePage,proto3" json:"IsHomePage"` // 是否首页
}

func (x *ArticleQueryCountRequest) Reset() {
	*x = ArticleQueryCountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleQueryCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleQueryCountRequest) ProtoMessage() {}

func (x *ArticleQueryCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleQueryCountRequest.ProtoReflect.Descriptor instead.
func (*ArticleQueryCountRequest) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{7}
}

func (x *ArticleQueryCountRequest) GetCateName() string {
	if x != nil {
		return x.CateName
	}
	return ""
}

func (x *ArticleQueryCountRequest) GetIsHomePage() bool {
	if x != nil {
		return x.IsHomePage
	}
	return false
}

type ArticleQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CateName                 string `protobuf:"bytes,1,opt,name=CateName,json=CateName,proto3" json:"CateName"`                                                  // 查询类型,Trader:交易商，Provider:服务商，WikiFx：wikifx新闻
	Pageindex                int32  `protobuf:"varint,2,opt,name=Pageindex,json=Pageindex,proto3" json:"Pageindex"`                                              // 页数
	PageSize                 int32  `protobuf:"varint,3,opt,name=PageSize,json=PageSize,proto3" json:"PageSize"`                                                 // 页大小
	IsRequiredRelationTrader bool   `protobuf:"varint,4,opt,name=IsRequiredRelationTrader,json=IsRequiredRelationTrader,proto3" json:"IsRequiredRelationTrader"` // 是否需要相关交易商
	IsHomePage               bool   `protobuf:"varint,5,opt,name=IsHomePage,json=IsHomePage,proto3" json:"IsHomePage"`                                           // 是否首页
}

func (x *ArticleQueryRequest) Reset() {
	*x = ArticleQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleQueryRequest) ProtoMessage() {}

func (x *ArticleQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleQueryRequest.ProtoReflect.Descriptor instead.
func (*ArticleQueryRequest) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{8}
}

func (x *ArticleQueryRequest) GetCateName() string {
	if x != nil {
		return x.CateName
	}
	return ""
}

func (x *ArticleQueryRequest) GetPageindex() int32 {
	if x != nil {
		return x.Pageindex
	}
	return 0
}

func (x *ArticleQueryRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ArticleQueryRequest) GetIsRequiredRelationTrader() bool {
	if x != nil {
		return x.IsRequiredRelationTrader
	}
	return false
}

func (x *ArticleQueryRequest) GetIsHomePage() bool {
	if x != nil {
		return x.IsHomePage
	}
	return false
}

type ArticleRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtRelRelationType RelationType       `protobuf:"varint,1,opt,name=ArtRelRelationType,json=ArtRelRelationType,proto3,enum=ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationType" json:"ArtRelRelationType"` // 文章关联类型（所属或相关）
	Code               string             `protobuf:"bytes,2,opt,name=Code,json=Code,proto3" json:"Code"`                                                                                                                     // 关联交易商或代理商id
	ArtRelTraderType   RelationTraderType `protobuf:"varint,3,opt,name=ArtRelTraderType,json=ArtRelTraderType,proto3,enum=ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationTraderType" json:"ArtRelTraderType"` // 关联机构类型（平台，交易商，代理商）
}

func (x *ArticleRelation) Reset() {
	*x = ArticleRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArticleRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleRelation) ProtoMessage() {}

func (x *ArticleRelation) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleRelation.ProtoReflect.Descriptor instead.
func (*ArticleRelation) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{9}
}

func (x *ArticleRelation) GetArtRelRelationType() RelationType {
	if x != nil {
		return x.ArtRelRelationType
	}
	return RelationType_RelationType_None
}

func (x *ArticleRelation) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ArticleRelation) GetArtRelTraderType() RelationTraderType {
	if x != nil {
		return x.ArtRelTraderType
	}
	return RelationTraderType_RelationTraderType_None
}

type ContainerData_ArticleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64          `protobuf:"varint,1,opt,name=Total,json=Total,proto3" json:"Total"`
	Items []*ArticleList `protobuf:"bytes,2,rep,name=Items,json=Items,proto3" json:"Items"`
}

func (x *ContainerData_ArticleList) Reset() {
	*x = ContainerData_ArticleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContainerData_ArticleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContainerData_ArticleList) ProtoMessage() {}

func (x *ContainerData_ArticleList) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContainerData_ArticleList.ProtoReflect.Descriptor instead.
func (*ContainerData_ArticleList) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{10}
}

func (x *ContainerData_ArticleList) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ContainerData_ArticleList) GetItems() []*ArticleList {
	if x != nil {
		return x.Items
	}
	return nil
}

type ImageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url       string `protobuf:"bytes,1,opt,name=Url,json=Url,proto3" json:"Url"`                    // 图片url
	Width     int32  `protobuf:"varint,2,opt,name=Width,json=Width,proto3" json:"Width"`             // 宽
	Height    int32  `protobuf:"varint,3,opt,name=Height,json=Height,proto3" json:"Height"`          // 高
	ImageType int32  `protobuf:"varint,4,opt,name=ImageType,json=ImageType,proto3" json:"ImageType"` // 图片类型
}

func (x *ImageInfo) Reset() {
	*x = ImageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageInfo) ProtoMessage() {}

func (x *ImageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageInfo.ProtoReflect.Descriptor instead.
func (*ImageInfo) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{11}
}

func (x *ImageInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ImageInfo) GetWidth() int32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *ImageInfo) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *ImageInfo) GetImageType() int32 {
	if x != nil {
		return x.ImageType
	}
	return 0
}

type QueryBelongToCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ArtCode string `protobuf:"bytes,1,opt,name=ArtCode,json=ArtCode,proto3" json:"ArtCode"` // 文章Code
}

func (x *QueryBelongToCodeRequest) Reset() {
	*x = QueryBelongToCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryBelongToCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryBelongToCodeRequest) ProtoMessage() {}

func (x *QueryBelongToCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryBelongToCodeRequest.ProtoReflect.Descriptor instead.
func (*QueryBelongToCodeRequest) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{12}
}

func (x *QueryBelongToCodeRequest) GetArtCode() string {
	if x != nil {
		return x.ArtCode
	}
	return ""
}

type UnityReply_ArticleBelongToCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                       `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string                     `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *ArticleBelongToCodeResult `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_ArticleBelongToCodeResult) Reset() {
	*x = UnityReply_ArticleBelongToCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_ArticleBelongToCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_ArticleBelongToCodeResult) ProtoMessage() {}

func (x *UnityReply_ArticleBelongToCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_ArticleBelongToCodeResult.ProtoReflect.Descriptor instead.
func (*UnityReply_ArticleBelongToCodeResult) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{13}
}

func (x *UnityReply_ArticleBelongToCodeResult) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_ArticleBelongToCodeResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_ArticleBelongToCodeResult) GetResult() *ArticleBelongToCodeResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_ArticleByCodesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                  `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string                `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *ArticleByCodesResult `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_ArticleByCodesResult) Reset() {
	*x = UnityReply_ArticleByCodesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_ArticleByCodesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_ArticleByCodesResult) ProtoMessage() {}

func (x *UnityReply_ArticleByCodesResult) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_ArticleByCodesResult.ProtoReflect.Descriptor instead.
func (*UnityReply_ArticleByCodesResult) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{14}
}

func (x *UnityReply_ArticleByCodesResult) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_ArticleByCodesResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_ArticleByCodesResult) GetResult() *ArticleByCodesResult {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_ArticleDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool           `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string         `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *ArticleDetail `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_ArticleDetail) Reset() {
	*x = UnityReply_ArticleDetail{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_ArticleDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_ArticleDetail) ProtoMessage() {}

func (x *UnityReply_ArticleDetail) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_ArticleDetail.ProtoReflect.Descriptor instead.
func (*UnityReply_ArticleDetail) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{15}
}

func (x *UnityReply_ArticleDetail) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_ArticleDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_ArticleDetail) GetResult() *ArticleDetail {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_ContainerData_ArticleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool                       `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string                     `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    *ContainerData_ArticleList `protobuf:"bytes,3,opt,name=Result,json=Result,proto3" json:"Result"`           // 返回结果
}

func (x *UnityReply_ContainerData_ArticleList) Reset() {
	*x = UnityReply_ContainerData_ArticleList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_ContainerData_ArticleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_ContainerData_ArticleList) ProtoMessage() {}

func (x *UnityReply_ContainerData_ArticleList) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_ContainerData_ArticleList.ProtoReflect.Descriptor instead.
func (*UnityReply_ContainerData_ArticleList) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{16}
}

func (x *UnityReply_ContainerData_ArticleList) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_ContainerData_ArticleList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_ContainerData_ArticleList) GetResult() *ContainerData_ArticleList {
	if x != nil {
		return x.Result
	}
	return nil
}

type UnityReply_Int32 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsSuccess bool   `protobuf:"varint,1,opt,name=IsSuccess,json=IsSuccess,proto3" json:"IsSuccess"` // 是否成功
	Message   string `protobuf:"bytes,2,opt,name=Message,json=Message,proto3" json:"Message"`        // 错误信息
	Result    int32  `protobuf:"varint,3,opt,name=Result,json=Result,proto3" json:"Result"`          // 返回结果
}

func (x *UnityReply_Int32) Reset() {
	*x = UnityReply_Int32{}
	if protoimpl.UnsafeEnabled {
		mi := &file_article_ArticlePush_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnityReply_Int32) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnityReply_Int32) ProtoMessage() {}

func (x *UnityReply_Int32) ProtoReflect() protoreflect.Message {
	mi := &file_article_ArticlePush_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnityReply_Int32.ProtoReflect.Descriptor instead.
func (*UnityReply_Int32) Descriptor() ([]byte, []int) {
	return file_article_ArticlePush_proto_rawDescGZIP(), []int{17}
}

func (x *UnityReply_Int32) GetIsSuccess() bool {
	if x != nil {
		return x.IsSuccess
	}
	return false
}

func (x *UnityReply_Int32) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UnityReply_Int32) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

var File_article_ArticlePush_proto protoreflect.FileDescriptor

var file_article_ArticlePush_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x2f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x36, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69,
	0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x22, 0xbb, 0x01, 0x0a, 0x19, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42,
	0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x6e, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4a, 0x2e, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57,
	0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x6d, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x41, 0x72, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x18, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x22, 0x77, 0x0a, 0x14, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5f, 0x0a, 0x08, 0x41, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69,
	0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x73, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x08, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x73, 0x22, 0xa8, 0x04, 0x0a, 0x0d, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x41,
	0x72, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x72,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x54, 0x61,
	0x67, 0x12, 0x12, 0x0a, 0x04, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x53, 0x68, 0x61, 0x72, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x30, 0x0a, 0x13, 0x41, 0x72, 0x74,
	0x69, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42,
	0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x6b, 0x0a, 0x0c, 0x41,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x47, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x49, 0x73, 0x57, 0x68,
	0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x49,
	0x73, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x41, 0x72,
	0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x41, 0x72, 0x74, 0x4b, 0x65, 0x79, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x72,
	0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x41, 0x72, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06,
	0x49, 0x73, 0x53, 0x68, 0x6f, 0x77, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x49, 0x73,
	0x53, 0x68, 0x6f, 0x77, 0x22, 0x2e, 0x0a, 0x12, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x72,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x72, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x83, 0x04, 0x0a, 0x0b, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x72, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x72, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x59, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62,
	0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1e, 0x0a,
	0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x61, 0x0a,
	0x0a, 0x46, 0x69, 0x72, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x41, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x46, 0x69, 0x72, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64,
	0x12, 0x30, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x6c, 0x6f, 0x6e,
	0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61, 0x72, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x54, 0x65, 0x78, 0x74, 0x50, 0x61, 0x72, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x48, 0x69, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x48, 0x69, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x70, 0x0a, 0x16, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x31, 0x30, 0x30, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3a, 0x0a, 0x18, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x18, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x22, 0x56, 0x0a, 0x18,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x61, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x61, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x48, 0x6f, 0x6d, 0x65,
	0x50, 0x61, 0x67, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x13, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x43, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x43, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x61, 0x67, 0x65,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x50, 0x61, 0x67,
	0x65, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x50, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x3a, 0x0a, 0x18, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x49, 0x73, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e,
	0x0a, 0x0a, 0x49, 0x73, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x49, 0x73, 0x48, 0x6f, 0x6d, 0x65, 0x50, 0x61, 0x67, 0x65, 0x22, 0x93,
	0x02, 0x0a, 0x0f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x74, 0x0a, 0x12, 0x41, 0x72, 0x74, 0x52, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x44,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70,
	0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x41, 0x72, 0x74, 0x52, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x76, 0x0a, 0x10,
	0x41, 0x72, 0x74, 0x52, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4a, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78,
	0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x41, 0x72, 0x74, 0x52, 0x65, 0x6c, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x59, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x05, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x69, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55,
	0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x57, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x34,
	0x0a, 0x18, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x72,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x72, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0xc9, 0x01, 0x0a, 0x24, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x6c, 0x6f, 0x6e,
	0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x69, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41,
	0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0xbf, 0x01, 0x0a, 0x1f, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f,
	0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x64, 0x0a, 0x06,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e,
	0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0xb1, 0x01, 0x0a, 0x18, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x5d, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xc9, 0x01, 0x0a, 0x24, 0x55, 0x6e, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44,
	0x61, 0x74, 0x61, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x69, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x62, 0x0a, 0x10, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x5f, 0x49, 0x6e, 0x74, 0x33, 0x32, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x53, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2a, 0xd3, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x44, 0x65,
	0x61, 0x6c, 0x65, 0x72, 0x10, 0x04, 0x12, 0x26, 0x0a, 0x22, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x10, 0x05, 0x12, 0x1c,
	0x0a, 0x18, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x72, 0x61, 0x64, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x10, 0x06, 0x2a, 0x7c, 0x0a, 0x0c,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x6f, 0x6e,
	0x65, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x10, 0x03, 0x32, 0x82, 0x09, 0x0a, 0x12, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0xac, 0x01, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x12, 0x4a, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e,
	0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x41, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x50,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70,
	0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0xbb, 0x01, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x4b, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63,
	0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69,
	0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x73, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x1a, 0x57, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41,
	0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55,
	0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0xaf,
	0x01, 0x0a, 0x11, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x50, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62,
	0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x41, 0x72,
	0x74, 0x69, 0x63, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65,
	0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78,
	0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e,
	0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x49, 0x6e, 0x74, 0x33, 0x32,
	0x12, 0xbd, 0x01, 0x0a, 0x10, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41,
	0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x41,
	0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x5c, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e,
	0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0xbd, 0x01, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x30, 0x12, 0x4e, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x31, 0x30, 0x30,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x57, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c,
	0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46,
	0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73,
	0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x41, 0x72, 0x74, 0x69,
	0x63, 0x6c, 0x65, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0xcc, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67,
	0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x79, 0x41, 0x72, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x50, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43, 0x6f, 0x72,
	0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x72,
	0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x42, 0x65,
	0x6c, 0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x5c, 0x2e, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x41, 0x62, 0x70, 0x2e, 0x43,
	0x6f, 0x72, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x47, 0x72, 0x70, 0x63, 0x2e, 0x57, 0x69, 0x6b, 0x69, 0x46, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x5f, 0x41, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x42, 0x65, 0x6c,
	0x6f, 0x6e, 0x67, 0x54, 0x6f, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42,
	0x17, 0x5a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x72, 0x74, 0x69, 0x63, 0x6c, 0x65, 0x70, 0x75,
	0x73, 0x68, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_article_ArticlePush_proto_rawDescOnce sync.Once
	file_article_ArticlePush_proto_rawDescData = file_article_ArticlePush_proto_rawDesc
)

func file_article_ArticlePush_proto_rawDescGZIP() []byte {
	file_article_ArticlePush_proto_rawDescOnce.Do(func() {
		file_article_ArticlePush_proto_rawDescData = protoimpl.X.CompressGZIP(file_article_ArticlePush_proto_rawDescData)
	})
	return file_article_ArticlePush_proto_rawDescData
}

var file_article_ArticlePush_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_article_ArticlePush_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_article_ArticlePush_proto_goTypes = []interface{}{
	(RelationTraderType)(0),                      // 0: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationTraderType
	(RelationType)(0),                            // 1: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationType
	(*ArticleBelongToCodeResult)(nil),            // 2: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleBelongToCodeResult
	(*ArticleByCodesQuery)(nil),                  // 3: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleByCodesQuery
	(*ArticleByCodesResult)(nil),                 // 4: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleByCodesResult
	(*ArticleDetail)(nil),                        // 5: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleDetail
	(*ArticleDetailQuery)(nil),                   // 6: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleDetailQuery
	(*ArticleList)(nil),                          // 7: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleList
	(*ArticleQuery100Request)(nil),               // 8: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleQuery100Request
	(*ArticleQueryCountRequest)(nil),             // 9: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleQueryCountRequest
	(*ArticleQueryRequest)(nil),                  // 10: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleQueryRequest
	(*ArticleRelation)(nil),                      // 11: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleRelation
	(*ContainerData_ArticleList)(nil),            // 12: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ContainerData_ArticleList
	(*ImageInfo)(nil),                            // 13: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ImageInfo
	(*QueryBelongToCodeRequest)(nil),             // 14: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.QueryBelongToCodeRequest
	(*UnityReply_ArticleBelongToCodeResult)(nil), // 15: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleBelongToCodeResult
	(*UnityReply_ArticleByCodesResult)(nil),      // 16: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleByCodesResult
	(*UnityReply_ArticleDetail)(nil),             // 17: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleDetail
	(*UnityReply_ContainerData_ArticleList)(nil), // 18: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ContainerData_ArticleList
	(*UnityReply_Int32)(nil),                     // 19: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_Int32
}
var file_article_ArticlePush_proto_depIdxs = []int32{
	0,  // 0: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleBelongToCodeResult.RelationType:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationTraderType
	7,  // 1: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleByCodesResult.Articles:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleList
	11, // 2: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleDetail.Associations:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleRelation
	13, // 3: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleList.Banner:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ImageInfo
	13, // 4: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleList.FirstImage:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ImageInfo
	1,  // 5: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleRelation.ArtRelRelationType:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationType
	0,  // 6: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleRelation.ArtRelTraderType:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.RelationTraderType
	7,  // 7: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ContainerData_ArticleList.Items:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleList
	2,  // 8: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleBelongToCodeResult.Result:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleBelongToCodeResult
	4,  // 9: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleByCodesResult.Result:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleByCodesResult
	5,  // 10: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleDetail.Result:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleDetail
	12, // 11: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ContainerData_ArticleList.Result:type_name -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ContainerData_ArticleList
	6,  // 12: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticle:input_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleDetailQuery
	3,  // 13: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleByCodes:input_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleByCodesQuery
	9,  // 14: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleCount:input_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleQueryCountRequest
	10, // 15: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleList:input_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleQueryRequest
	8,  // 16: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleTop100:input_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticleQuery100Request
	14, // 17: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryBelongToCodeByArtCode:input_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.QueryBelongToCodeRequest
	17, // 18: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticle:output_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleDetail
	16, // 19: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleByCodes:output_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleByCodesResult
	19, // 20: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleCount:output_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_Int32
	18, // 21: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleList:output_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ContainerData_ArticleList
	16, // 22: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryArticleTop100:output_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleByCodesResult
	15, // 23: ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService.QueryBelongToCodeByArtCode:output_type -> ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.UnityReply_ArticleBelongToCodeResult
	18, // [18:24] is the sub-list for method output_type
	12, // [12:18] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_article_ArticlePush_proto_init() }
func file_article_ArticlePush_proto_init() {
	if File_article_ArticlePush_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_article_ArticlePush_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleBelongToCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleByCodesQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleByCodesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleDetailQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleQuery100Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleQueryCountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArticleRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContainerData_ArticleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryBelongToCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_ArticleBelongToCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_ArticleByCodesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_ArticleDetail); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_ContainerData_ArticleList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_article_ArticlePush_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnityReply_Int32); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_article_ArticlePush_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_article_ArticlePush_proto_goTypes,
		DependencyIndexes: file_article_ArticlePush_proto_depIdxs,
		EnumInfos:         file_article_ArticlePush_proto_enumTypes,
		MessageInfos:      file_article_ArticlePush_proto_msgTypes,
	}.Build()
	File_article_ArticlePush_proto = out.File
	file_article_ArticlePush_proto_rawDesc = nil
	file_article_ArticlePush_proto_goTypes = nil
	file_article_ArticlePush_proto_depIdxs = nil
}
