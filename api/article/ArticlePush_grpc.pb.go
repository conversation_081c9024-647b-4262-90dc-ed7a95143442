// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.3
// source: article/ArticlePush.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ArticlePushService_QueryArticle_FullMethodName               = "/ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService/QueryArticle"
	ArticlePushService_QueryArticleByCodes_FullMethodName        = "/ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService/QueryArticleByCodes"
	ArticlePushService_QueryArticleCount_FullMethodName          = "/ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService/QueryArticleCount"
	ArticlePushService_QueryArticleList_FullMethodName           = "/ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService/QueryArticleList"
	ArticlePushService_QueryArticleTop100_FullMethodName         = "/ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService/QueryArticleTop100"
	ArticlePushService_QueryBelongToCodeByArtCode_FullMethodName = "/ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService/QueryBelongToCodeByArtCode"
)

// ArticlePushServiceClient is the client API for ArticlePushService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ArticlePushServiceClient interface {
	QueryArticle(ctx context.Context, in *ArticleDetailQuery, opts ...grpc.CallOption) (*UnityReply_ArticleDetail, error)
	QueryArticleByCodes(ctx context.Context, in *ArticleByCodesQuery, opts ...grpc.CallOption) (*UnityReply_ArticleByCodesResult, error)
	QueryArticleCount(ctx context.Context, in *ArticleQueryCountRequest, opts ...grpc.CallOption) (*UnityReply_Int32, error)
	QueryArticleList(ctx context.Context, in *ArticleQueryRequest, opts ...grpc.CallOption) (*UnityReply_ContainerData_ArticleList, error)
	QueryArticleTop100(ctx context.Context, in *ArticleQuery100Request, opts ...grpc.CallOption) (*UnityReply_ArticleByCodesResult, error)
	QueryBelongToCodeByArtCode(ctx context.Context, in *QueryBelongToCodeRequest, opts ...grpc.CallOption) (*UnityReply_ArticleBelongToCodeResult, error)
}

type articlePushServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewArticlePushServiceClient(cc grpc.ClientConnInterface) ArticlePushServiceClient {
	return &articlePushServiceClient{cc}
}

func (c *articlePushServiceClient) QueryArticle(ctx context.Context, in *ArticleDetailQuery, opts ...grpc.CallOption) (*UnityReply_ArticleDetail, error) {
	out := new(UnityReply_ArticleDetail)
	err := c.cc.Invoke(ctx, ArticlePushService_QueryArticle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *articlePushServiceClient) QueryArticleByCodes(ctx context.Context, in *ArticleByCodesQuery, opts ...grpc.CallOption) (*UnityReply_ArticleByCodesResult, error) {
	out := new(UnityReply_ArticleByCodesResult)
	err := c.cc.Invoke(ctx, ArticlePushService_QueryArticleByCodes_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *articlePushServiceClient) QueryArticleCount(ctx context.Context, in *ArticleQueryCountRequest, opts ...grpc.CallOption) (*UnityReply_Int32, error) {
	out := new(UnityReply_Int32)
	err := c.cc.Invoke(ctx, ArticlePushService_QueryArticleCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *articlePushServiceClient) QueryArticleList(ctx context.Context, in *ArticleQueryRequest, opts ...grpc.CallOption) (*UnityReply_ContainerData_ArticleList, error) {
	out := new(UnityReply_ContainerData_ArticleList)
	err := c.cc.Invoke(ctx, ArticlePushService_QueryArticleList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *articlePushServiceClient) QueryArticleTop100(ctx context.Context, in *ArticleQuery100Request, opts ...grpc.CallOption) (*UnityReply_ArticleByCodesResult, error) {
	out := new(UnityReply_ArticleByCodesResult)
	err := c.cc.Invoke(ctx, ArticlePushService_QueryArticleTop100_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *articlePushServiceClient) QueryBelongToCodeByArtCode(ctx context.Context, in *QueryBelongToCodeRequest, opts ...grpc.CallOption) (*UnityReply_ArticleBelongToCodeResult, error) {
	out := new(UnityReply_ArticleBelongToCodeResult)
	err := c.cc.Invoke(ctx, ArticlePushService_QueryBelongToCodeByArtCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ArticlePushServiceServer is the server API for ArticlePushService service.
// All implementations must embed UnimplementedArticlePushServiceServer
// for forward compatibility
type ArticlePushServiceServer interface {
	QueryArticle(context.Context, *ArticleDetailQuery) (*UnityReply_ArticleDetail, error)
	QueryArticleByCodes(context.Context, *ArticleByCodesQuery) (*UnityReply_ArticleByCodesResult, error)
	QueryArticleCount(context.Context, *ArticleQueryCountRequest) (*UnityReply_Int32, error)
	QueryArticleList(context.Context, *ArticleQueryRequest) (*UnityReply_ContainerData_ArticleList, error)
	QueryArticleTop100(context.Context, *ArticleQuery100Request) (*UnityReply_ArticleByCodesResult, error)
	QueryBelongToCodeByArtCode(context.Context, *QueryBelongToCodeRequest) (*UnityReply_ArticleBelongToCodeResult, error)
	mustEmbedUnimplementedArticlePushServiceServer()
}

// UnimplementedArticlePushServiceServer must be embedded to have forward compatible implementations.
type UnimplementedArticlePushServiceServer struct {
}

func (UnimplementedArticlePushServiceServer) QueryArticle(context.Context, *ArticleDetailQuery) (*UnityReply_ArticleDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryArticle not implemented")
}
func (UnimplementedArticlePushServiceServer) QueryArticleByCodes(context.Context, *ArticleByCodesQuery) (*UnityReply_ArticleByCodesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryArticleByCodes not implemented")
}
func (UnimplementedArticlePushServiceServer) QueryArticleCount(context.Context, *ArticleQueryCountRequest) (*UnityReply_Int32, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryArticleCount not implemented")
}
func (UnimplementedArticlePushServiceServer) QueryArticleList(context.Context, *ArticleQueryRequest) (*UnityReply_ContainerData_ArticleList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryArticleList not implemented")
}
func (UnimplementedArticlePushServiceServer) QueryArticleTop100(context.Context, *ArticleQuery100Request) (*UnityReply_ArticleByCodesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryArticleTop100 not implemented")
}
func (UnimplementedArticlePushServiceServer) QueryBelongToCodeByArtCode(context.Context, *QueryBelongToCodeRequest) (*UnityReply_ArticleBelongToCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryBelongToCodeByArtCode not implemented")
}
func (UnimplementedArticlePushServiceServer) mustEmbedUnimplementedArticlePushServiceServer() {}

// UnsafeArticlePushServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ArticlePushServiceServer will
// result in compilation errors.
type UnsafeArticlePushServiceServer interface {
	mustEmbedUnimplementedArticlePushServiceServer()
}

func RegisterArticlePushServiceServer(s grpc.ServiceRegistrar, srv ArticlePushServiceServer) {
	s.RegisterService(&ArticlePushService_ServiceDesc, srv)
}

func _ArticlePushService_QueryArticle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleDetailQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArticlePushServiceServer).QueryArticle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArticlePushService_QueryArticle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArticlePushServiceServer).QueryArticle(ctx, req.(*ArticleDetailQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArticlePushService_QueryArticleByCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleByCodesQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArticlePushServiceServer).QueryArticleByCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArticlePushService_QueryArticleByCodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArticlePushServiceServer).QueryArticleByCodes(ctx, req.(*ArticleByCodesQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArticlePushService_QueryArticleCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleQueryCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArticlePushServiceServer).QueryArticleCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArticlePushService_QueryArticleCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArticlePushServiceServer).QueryArticleCount(ctx, req.(*ArticleQueryCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArticlePushService_QueryArticleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArticlePushServiceServer).QueryArticleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArticlePushService_QueryArticleList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArticlePushServiceServer).QueryArticleList(ctx, req.(*ArticleQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArticlePushService_QueryArticleTop100_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArticleQuery100Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArticlePushServiceServer).QueryArticleTop100(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArticlePushService_QueryArticleTop100_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArticlePushServiceServer).QueryArticleTop100(ctx, req.(*ArticleQuery100Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _ArticlePushService_QueryBelongToCodeByArtCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryBelongToCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArticlePushServiceServer).QueryBelongToCodeByArtCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ArticlePushService_QueryBelongToCodeByArtCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArticlePushServiceServer).QueryBelongToCodeByArtCode(ctx, req.(*QueryBelongToCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ArticlePushService_ServiceDesc is the grpc.ServiceDesc for ArticlePushService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ArticlePushService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ArticleAbp.Core.Application.Grpc.WikiFx.ProtoContracts.ArticlePushService",
	HandlerType: (*ArticlePushServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryArticle",
			Handler:    _ArticlePushService_QueryArticle_Handler,
		},
		{
			MethodName: "QueryArticleByCodes",
			Handler:    _ArticlePushService_QueryArticleByCodes_Handler,
		},
		{
			MethodName: "QueryArticleCount",
			Handler:    _ArticlePushService_QueryArticleCount_Handler,
		},
		{
			MethodName: "QueryArticleList",
			Handler:    _ArticlePushService_QueryArticleList_Handler,
		},
		{
			MethodName: "QueryArticleTop100",
			Handler:    _ArticlePushService_QueryArticleTop100_Handler,
		},
		{
			MethodName: "QueryBelongToCodeByArtCode",
			Handler:    _ArticlePushService_QueryBelongToCodeByArtCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "article/ArticlePush.proto",
}
