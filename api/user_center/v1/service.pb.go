// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.24.3
// source: user_center/v1/service.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	common "gold_store/api/common"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_user_center_v1_service_proto protoreflect.FileDescriptor

var file_user_center_v1_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0xa8, 0x0b, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x47,
	0x0a, 0x07, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x10, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0a, 0x12, 0x08, 0x2f,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x7a, 0x12, 0xaf, 0x05, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xd1, 0x04, 0x92, 0x41, 0xb8, 0x04, 0x0a, 0x06, 0xe6, 0x8e,
	0xa8, 0xe8, 0x8d, 0x90, 0x12, 0x28, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x95, 0x86, 0xe4,
	0xb8, 0x9a, 0x2f, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0xe7, 0x9a, 0x84, 0xe7, 0x83, 0xad, 0xe9,
	0x97, 0xa8, 0xe6, 0x88, 0x96, 0xe8, 0x80, 0x85, 0xe6, 0x9c, 0x80, 0xe6, 0x96, 0xb0, 0x72, 0x83,
	0x04, 0x0a, 0x16, 0x0a, 0x07, 0x58, 0x2d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x07, 0x58, 0x2d,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x28, 0x01, 0x0a, 0x40, 0x0a, 0x09, 0x58, 0x2d, 0x52,
	0x65, 0x61, 0x6c, 0x2d, 0x49, 0x70, 0x12, 0x31, 0xe5, 0xae, 0xa2, 0xe6, 0x88, 0xb7, 0xe7, 0xab,
	0xaf, 0x49, 0x50, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4,
	0xbb, 0x8e, 0x20, 0x58, 0x2d, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x2d, 0x46,
	0x6f, 0x72, 0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x18, 0x01, 0x0a, 0x17, 0x0a, 0x09, 0x58,
	0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7,
	0x49, 0x44, 0x18, 0x01, 0x0a, 0x4b, 0x0a, 0x0c, 0x58, 0x2d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2d, 0x49, 0x64, 0x12, 0x39, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbb, 0x8e, 0x20,
	0x57, 0x69, 0x6b, 0x69, 0x64, 0x61, 0x74, 0x61, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2d, 0x49, 0x64, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x18,
	0x01, 0x0a, 0x40, 0x0a, 0x0e, 0x58, 0x2d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x2d, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x2c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65, 0xef,
	0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbb, 0x8e, 0x20, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0x18, 0x01, 0x0a, 0x48, 0x0a, 0x0f, 0x58, 0x2d, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x2d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x33, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8, 0xaf,
	0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c,
	0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbb, 0x8e, 0x20, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x63, 0x6f, 0x64, 0x65, 0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x18, 0x01, 0x0a, 0x19, 0x0a,
	0x09, 0x42, 0x61, 0x73, 0x69, 0x63, 0x64, 0x61, 0x74, 0x61, 0x12, 0x0a, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x20, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x0a, 0x57, 0x0a, 0x45, 0x58, 0x2d, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x2d, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x2d, 0x43, 0x6f, 0x64, 0x65, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97,
	0xb6, 0xe4, 0xbb, 0x8e, 0x20, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x6c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0x12, 0x0c, 0xe5, 0x81, 0x8f, 0xe5, 0xa5, 0xbd, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x18,
	0x01, 0x0a, 0x41, 0x0a, 0x0b, 0x58, 0x2d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x49, 0x64,
	0x12, 0x30, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0xef, 0xbc,
	0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbc, 0x9a, 0xe4, 0xbb, 0x8e,
	0x42, 0x61, 0x73, 0x69, 0x63, 0x64, 0x61, 0x74, 0x61, 0xe4, 0xb8, 0xad, 0xe8, 0xa7, 0xa3, 0xe6,
	0x9e, 0x90, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x12, 0x0d, 0x2f, 0x76, 0x31, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0xa1, 0x05, 0x0a, 0x0b, 0x53, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xd4, 0x04, 0x92, 0x41, 0xb8, 0x04, 0x0a, 0x06, 0xe6,
	0x8e, 0xa8, 0xe8, 0x8d, 0x90, 0x12, 0x28, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0xe5, 0x95, 0x86,
	0xe4, 0xb8, 0x9a, 0x2f, 0xe5, 0x8a, 0xa8, 0xe6, 0x80, 0x81, 0xe7, 0x9a, 0x84, 0xe7, 0x83, 0xad,
	0xe9, 0x97, 0xa8, 0xe6, 0x88, 0x96, 0xe8, 0x80, 0x85, 0xe6, 0x9c, 0x80, 0xe6, 0x96, 0xb0, 0x72,
	0x83, 0x04, 0x0a, 0x16, 0x0a, 0x07, 0x58, 0x2d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x07, 0x58,
	0x2d, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x28, 0x01, 0x0a, 0x40, 0x0a, 0x09, 0x58, 0x2d,
	0x52, 0x65, 0x61, 0x6c, 0x2d, 0x49, 0x70, 0x12, 0x31, 0xe5, 0xae, 0xa2, 0xe6, 0x88, 0xb7, 0xe7,
	0xab, 0xaf, 0x49, 0x50, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6,
	0xe4, 0xbb, 0x8e, 0x20, 0x58, 0x2d, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x2d,
	0x46, 0x6f, 0x72, 0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x18, 0x01, 0x0a, 0x17, 0x0a, 0x09,
	0x58, 0x2d, 0x55, 0x73, 0x65, 0x72, 0x2d, 0x49, 0x64, 0x12, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x44, 0x18, 0x01, 0x0a, 0x4b, 0x0a, 0x0c, 0x58, 0x2d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2d, 0x49, 0x64, 0x12, 0x39, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbb, 0x8e,
	0x20, 0x57, 0x69, 0x6b, 0x69, 0x64, 0x61, 0x74, 0x61, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2d, 0x49, 0x64, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96,
	0x18, 0x01, 0x0a, 0x40, 0x0a, 0x0e, 0x58, 0x2d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x2d,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0xe5, 0x9b, 0xbd, 0xe5, 0xae, 0xb6, 0x63, 0x6f, 0x64, 0x65,
	0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbb, 0x8e, 0x20,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x63, 0x6f, 0x64, 0x65, 0x20, 0xe8, 0x8e, 0xb7, 0xe5,
	0x8f, 0x96, 0x18, 0x01, 0x0a, 0x48, 0x0a, 0x0f, 0x58, 0x2d, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x2d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x33, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8,
	0xaf, 0xad, 0xe8, 0xa8, 0x80, 0x63, 0x6f, 0x64, 0x65, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6,
	0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbb, 0x8e, 0x20, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x63, 0x6f, 0x64, 0x65, 0x20, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f, 0x96, 0x18, 0x01, 0x0a, 0x19,
	0x0a, 0x09, 0x42, 0x61, 0x73, 0x69, 0x63, 0x64, 0x61, 0x74, 0x61, 0x12, 0x0a, 0x62, 0x61, 0x73,
	0x69, 0x63, 0x20, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x0a, 0x57, 0x0a, 0x45, 0x58, 0x2d, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x2d, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x2d, 0x43, 0x6f, 0x64, 0x65, 0xef, 0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6,
	0x97, 0xb6, 0xe4, 0xbb, 0x8e, 0x20, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x6c,
	0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x63, 0x6f, 0x64, 0x65, 0x20, 0xe8, 0x8e, 0xb7, 0xe5,
	0x8f, 0x96, 0x12, 0x0c, 0xe5, 0x81, 0x8f, 0xe5, 0xa5, 0xbd, 0xe8, 0xaf, 0xad, 0xe8, 0xa8, 0x80,
	0x18, 0x01, 0x0a, 0x41, 0x0a, 0x0b, 0x58, 0x2d, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2d, 0x49,
	0x64, 0x12, 0x30, 0xe8, 0xae, 0xbe, 0xe5, 0xa4, 0x87, 0xe6, 0xa0, 0x87, 0xe8, 0xaf, 0x86, 0xef,
	0xbc, 0x8c, 0xe6, 0xb2, 0xa1, 0xe6, 0x9c, 0x89, 0xe6, 0x97, 0xb6, 0xe4, 0xbc, 0x9a, 0xe4, 0xbb,
	0x8e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x64, 0x61, 0x74, 0x61, 0xe4, 0xb8, 0xad, 0xe8, 0xa7, 0xa3,
	0xe6, 0x9e, 0x90, 0x18, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x12, 0x10, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x17, 0x5a,
	0x15, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_user_center_v1_service_proto_goTypes = []interface{}{
	(*common.EmptyRequest)(nil), // 0: common.EmptyRequest
	(*GetUserInfoRequest)(nil),  // 1: api.user_center.v1.GetUserInfoRequest
	(*StringReplyRequest)(nil),  // 2: api.user_center.v1.StringReplyRequest
	(*common.HealthyReply)(nil), // 3: common.HealthyReply
	(*GetUserInfoReply)(nil),    // 4: api.user_center.v1.GetUserInfoReply
	(*common.StringReply)(nil),  // 5: common.StringReply
}
var file_user_center_v1_service_proto_depIdxs = []int32{
	0, // 0: api.user_center.v1.Service.Healthy:input_type -> common.EmptyRequest
	1, // 1: api.user_center.v1.Service.GetUserInfo:input_type -> api.user_center.v1.GetUserInfoRequest
	2, // 2: api.user_center.v1.Service.StringReply:input_type -> api.user_center.v1.StringReplyRequest
	3, // 3: api.user_center.v1.Service.Healthy:output_type -> common.HealthyReply
	4, // 4: api.user_center.v1.Service.GetUserInfo:output_type -> api.user_center.v1.GetUserInfoReply
	5, // 5: api.user_center.v1.Service.StringReply:output_type -> common.StringReply
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_user_center_v1_service_proto_init() }
func file_user_center_v1_service_proto_init() {
	if File_user_center_v1_service_proto != nil {
		return
	}
	file_user_center_v1_models_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_center_v1_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_center_v1_service_proto_goTypes,
		DependencyIndexes: file_user_center_v1_service_proto_depIdxs,
	}.Build()
	File_user_center_v1_service_proto = out.File
	file_user_center_v1_service_proto_rawDesc = nil
	file_user_center_v1_service_proto_goTypes = nil
	file_user_center_v1_service_proto_depIdxs = nil
}
