// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.24.3
// source: user_center/v1/growth_center.proto

package v1

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IdentityRuleBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string               `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Badge         string               `protobuf:"bytes,2,opt,name=badge,json=badge,proto3" json:"badge"`
	IdentityTips  string               `protobuf:"bytes,3,opt,name=identity_tips,json=identity_tips,proto3" json:"identity_tips"`
	IdentityDesc  string               `protobuf:"bytes,4,opt,name=identity_desc,json=identity_desc,proto3" json:"identity_desc"`
	IdentityGrade []*IdentityGradeInfo `protobuf:"bytes,5,rep,name=identity_grade,json=identity_grade,proto3" json:"identity_grade"`
}

func (x *IdentityRuleBaseInfo) Reset() {
	*x = IdentityRuleBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityRuleBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityRuleBaseInfo) ProtoMessage() {}

func (x *IdentityRuleBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityRuleBaseInfo.ProtoReflect.Descriptor instead.
func (*IdentityRuleBaseInfo) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{0}
}

func (x *IdentityRuleBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetBadge() string {
	if x != nil {
		return x.Badge
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetIdentityTips() string {
	if x != nil {
		return x.IdentityTips
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetIdentityDesc() string {
	if x != nil {
		return x.IdentityDesc
	}
	return ""
}

func (x *IdentityRuleBaseInfo) GetIdentityGrade() []*IdentityGradeInfo {
	if x != nil {
		return x.IdentityGrade
	}
	return nil
}

type IdentityGradeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GradeName          string               `protobuf:"bytes,1,opt,name=grade_name,json=grade_name,proto3" json:"grade_name"`
	GradeDesc          string               `protobuf:"bytes,2,opt,name=grade_desc,json=grade_desc,proto3" json:"grade_desc"`
	IsAcquire          bool                 `protobuf:"varint,3,opt,name=is_acquire,json=is_acquire,proto3" json:"is_acquire"`
	Badge              string               `protobuf:"bytes,4,opt,name=badge,json=badge,proto3" json:"badge"`
	BgColor            string               `protobuf:"bytes,5,opt,name=bg_color,json=bg_color,proto3" json:"bg_color"`
	GradeUpgradeTitle  string               `protobuf:"bytes,6,opt,name=grade_upgrade_title,json=grade_upgrade_title,proto3" json:"grade_upgrade_title"`
	GradeTask          []*IdentityGradeTask `protobuf:"bytes,7,rep,name=grade_task,json=grade_task,proto3" json:"grade_task"`
	UpgradeSuccessTips []string             `protobuf:"bytes,8,rep,name=upgrade_success_tips,json=upgrade_success_tips,proto3" json:"upgrade_success_tips"`
}

func (x *IdentityGradeInfo) Reset() {
	*x = IdentityGradeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityGradeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityGradeInfo) ProtoMessage() {}

func (x *IdentityGradeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityGradeInfo.ProtoReflect.Descriptor instead.
func (*IdentityGradeInfo) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{1}
}

func (x *IdentityGradeInfo) GetGradeName() string {
	if x != nil {
		return x.GradeName
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeDesc() string {
	if x != nil {
		return x.GradeDesc
	}
	return ""
}

func (x *IdentityGradeInfo) GetIsAcquire() bool {
	if x != nil {
		return x.IsAcquire
	}
	return false
}

func (x *IdentityGradeInfo) GetBadge() string {
	if x != nil {
		return x.Badge
	}
	return ""
}

func (x *IdentityGradeInfo) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeUpgradeTitle() string {
	if x != nil {
		return x.GradeUpgradeTitle
	}
	return ""
}

func (x *IdentityGradeInfo) GetGradeTask() []*IdentityGradeTask {
	if x != nil {
		return x.GradeTask
	}
	return nil
}

func (x *IdentityGradeInfo) GetUpgradeSuccessTips() []string {
	if x != nil {
		return x.UpgradeSuccessTips
	}
	return nil
}

// 身份等级任务
type IdentityGradeTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskName             string `protobuf:"bytes,1,opt,name=task_name,json=task_name,proto3" json:"task_name"`
	TaskContent          string `protobuf:"bytes,2,opt,name=task_content,json=task_content,proto3" json:"task_content"`
	TaskType             int32  `protobuf:"varint,3,opt,name=task_type,json=task_type,proto3" json:"task_type"`
	TaskNumericalTarget  int32  `protobuf:"varint,4,opt,name=task_numerical_target,json=task_numerical_target,proto3" json:"task_numerical_target"`
	TaskNumericalCurrent int32  `protobuf:"varint,5,opt,name=task_numerical_current,json=task_numerical_current,proto3" json:"task_numerical_current"`
	IsFinish             bool   `protobuf:"varint,6,opt,name=is_finish,json=is_finish,proto3" json:"is_finish"`
	TaskStatusColor      string `protobuf:"bytes,7,opt,name=task_status_color,json=task_finish_color,proto3" json:"task_status_color"`
	TaskIco              string `protobuf:"bytes,8,opt,name=task_ico,json=task_ico,proto3" json:"task_ico"`
}

func (x *IdentityGradeTask) Reset() {
	*x = IdentityGradeTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityGradeTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityGradeTask) ProtoMessage() {}

func (x *IdentityGradeTask) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityGradeTask.ProtoReflect.Descriptor instead.
func (*IdentityGradeTask) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{2}
}

func (x *IdentityGradeTask) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskContent() string {
	if x != nil {
		return x.TaskContent
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskType() int32 {
	if x != nil {
		return x.TaskType
	}
	return 0
}

func (x *IdentityGradeTask) GetTaskNumericalTarget() int32 {
	if x != nil {
		return x.TaskNumericalTarget
	}
	return 0
}

func (x *IdentityGradeTask) GetTaskNumericalCurrent() int32 {
	if x != nil {
		return x.TaskNumericalCurrent
	}
	return 0
}

func (x *IdentityGradeTask) GetIsFinish() bool {
	if x != nil {
		return x.IsFinish
	}
	return false
}

func (x *IdentityGradeTask) GetTaskStatusColor() string {
	if x != nil {
		return x.TaskStatusColor
	}
	return ""
}

func (x *IdentityGradeTask) GetTaskIco() string {
	if x != nil {
		return x.TaskIco
	}
	return ""
}

type GetIdentityRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetIdentityRuleRequest) Reset() {
	*x = GetIdentityRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityRuleRequest) ProtoMessage() {}

func (x *GetIdentityRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityRuleRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityRuleRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{3}
}

func (x *GetIdentityRuleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetIdentityRuleReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rules []*IdentityRuleBaseInfo `protobuf:"bytes,1,rep,name=rules,json=rules,proto3" json:"rules"`
	Order int32                   `protobuf:"varint,2,opt,name=order,json=order,proto3" json:"order"`
}

func (x *GetIdentityRuleReply) Reset() {
	*x = GetIdentityRuleReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityRuleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityRuleReply) ProtoMessage() {}

func (x *GetIdentityRuleReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityRuleReply.ProtoReflect.Descriptor instead.
func (*GetIdentityRuleReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{4}
}

func (x *GetIdentityRuleReply) GetRules() []*IdentityRuleBaseInfo {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *GetIdentityRuleReply) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

type GetUserGrowthDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetUserGrowthDetailRequest) Reset() {
	*x = GetUserGrowthDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGrowthDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGrowthDetailRequest) ProtoMessage() {}

func (x *GetUserGrowthDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGrowthDetailRequest.ProtoReflect.Descriptor instead.
func (*GetUserGrowthDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserGrowthDetailRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserGrowthDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId    string               `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	Name      string               `protobuf:"bytes,2,opt,name=name,json=name,proto3" json:"name"`
	Avatar    string               `protobuf:"bytes,3,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	GradeInfo []*IdentityGradeInfo `protobuf:"bytes,4,rep,name=grade_info,json=grade_info,proto3" json:"grade_info"`
}

func (x *GetUserGrowthDetailReply) Reset() {
	*x = GetUserGrowthDetailReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserGrowthDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGrowthDetailReply) ProtoMessage() {}

func (x *GetUserGrowthDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGrowthDetailReply.ProtoReflect.Descriptor instead.
func (*GetUserGrowthDetailReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserGrowthDetailReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserGrowthDetailReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserGrowthDetailReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetUserGrowthDetailReply) GetGradeInfo() []*IdentityGradeInfo {
	if x != nil {
		return x.GradeInfo
	}
	return nil
}

type GetGrowthCenterEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetGrowthCenterEntryRequest) Reset() {
	*x = GetGrowthCenterEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrowthCenterEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrowthCenterEntryRequest) ProtoMessage() {}

func (x *GetGrowthCenterEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrowthCenterEntryRequest.ProtoReflect.Descriptor instead.
func (*GetGrowthCenterEntryRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{7}
}

type GetGrowthCenterEntryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsShowGrowthCenter bool `protobuf:"varint,1,opt,name=is_show_growth_center,json=is_show_growth_center,proto3" json:"is_show_growth_center"`
	IsShowGradeRule    bool `protobuf:"varint,2,opt,name=is_show_grade_rule,json=is_show_grade_rule,proto3" json:"is_show_grade_rule"`
}

func (x *GetGrowthCenterEntryReply) Reset() {
	*x = GetGrowthCenterEntryReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrowthCenterEntryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrowthCenterEntryReply) ProtoMessage() {}

func (x *GetGrowthCenterEntryReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrowthCenterEntryReply.ProtoReflect.Descriptor instead.
func (*GetGrowthCenterEntryReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{8}
}

func (x *GetGrowthCenterEntryReply) GetIsShowGrowthCenter() bool {
	if x != nil {
		return x.IsShowGrowthCenter
	}
	return false
}

func (x *GetGrowthCenterEntryReply) GetIsShowGradeRule() bool {
	if x != nil {
		return x.IsShowGradeRule
	}
	return false
}

type GetIdentityCarouselRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetIdentityCarouselRequest) Reset() {
	*x = GetIdentityCarouselRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityCarouselRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityCarouselRequest) ProtoMessage() {}

func (x *GetIdentityCarouselRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityCarouselRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityCarouselRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{9}
}

func (x *GetIdentityCarouselRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetIdentityCarouselReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Banners  []*CarouselInfo `protobuf:"bytes,1,rep,name=banners,json=banner,proto3" json:"banners"`
	TimeSpan int64           `protobuf:"varint,2,opt,name=time_span,json=time_span,proto3" json:"time_span"`
}

func (x *GetIdentityCarouselReply) Reset() {
	*x = GetIdentityCarouselReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityCarouselReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityCarouselReply) ProtoMessage() {}

func (x *GetIdentityCarouselReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityCarouselReply.ProtoReflect.Descriptor instead.
func (*GetIdentityCarouselReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{10}
}

func (x *GetIdentityCarouselReply) GetBanners() []*CarouselInfo {
	if x != nil {
		return x.Banners
	}
	return nil
}

func (x *GetIdentityCarouselReply) GetTimeSpan() int64 {
	if x != nil {
		return x.TimeSpan
	}
	return 0
}

type CarouselInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,json=name,proto3" json:"name"`
	Img     string `protobuf:"bytes,2,opt,name=img,json=img,proto3" json:"img"`
	JumpUrl string `protobuf:"bytes,3,opt,name=jump_url,json=jump_url,proto3" json:"jump_url"`
}

func (x *CarouselInfo) Reset() {
	*x = CarouselInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CarouselInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CarouselInfo) ProtoMessage() {}

func (x *CarouselInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CarouselInfo.ProtoReflect.Descriptor instead.
func (*CarouselInfo) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{11}
}

func (x *CarouselInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CarouselInfo) GetImg() string {
	if x != nil {
		return x.Img
	}
	return ""
}

func (x *CarouselInfo) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

type GetIdentityShareRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
}

func (x *GetIdentityShareRequest) Reset() {
	*x = GetIdentityShareRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityShareRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityShareRequest) ProtoMessage() {}

func (x *GetIdentityShareRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityShareRequest.ProtoReflect.Descriptor instead.
func (*GetIdentityShareRequest) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{12}
}

func (x *GetIdentityShareRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetIdentityShareReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId      string   `protobuf:"bytes,1,opt,name=user_id,json=user_id,proto3" json:"user_id"`
	NickName    string   `protobuf:"bytes,2,opt,name=nick_name,json=nick_name,proto3" json:"nick_name"`
	Avatar      string   `protobuf:"bytes,3,opt,name=avatar,json=avatar,proto3" json:"avatar"`
	Badge       string   `protobuf:"bytes,4,opt,name=badge,json=badge,proto3" json:"badge"`
	Identity    string   `protobuf:"bytes,5,opt,name=identity,json=identity,proto3" json:"identity"`
	AcquireTime int64    `protobuf:"varint,6,opt,name=acquire_time,json=acquire_time,proto3" json:"acquire_time"`
	Content     []string `protobuf:"bytes,7,rep,name=content,json=content,proto3" json:"content"`
}

func (x *GetIdentityShareReply) Reset() {
	*x = GetIdentityShareReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_center_v1_growth_center_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetIdentityShareReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIdentityShareReply) ProtoMessage() {}

func (x *GetIdentityShareReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_center_v1_growth_center_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIdentityShareReply.ProtoReflect.Descriptor instead.
func (*GetIdentityShareReply) Descriptor() ([]byte, []int) {
	return file_user_center_v1_growth_center_proto_rawDescGZIP(), []int{13}
}

func (x *GetIdentityShareReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetIdentityShareReply) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *GetIdentityShareReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetIdentityShareReply) GetBadge() string {
	if x != nil {
		return x.Badge
	}
	return ""
}

func (x *GetIdentityShareReply) GetIdentity() string {
	if x != nil {
		return x.Identity
	}
	return ""
}

func (x *GetIdentityShareReply) GetAcquireTime() int64 {
	if x != nil {
		return x.AcquireTime
	}
	return 0
}

func (x *GetIdentityShareReply) GetContent() []string {
	if x != nil {
		return x.Content
	}
	return nil
}

var File_user_center_v1_growth_center_proto protoreflect.FileDescriptor

var file_user_center_v1_growth_center_proto_rawDesc = []byte{
	0x0a, 0x22, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x2d, 0x67, 0x65, 0x6e, 0x2d, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x76, 0x32, 0x2f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc9, 0x02, 0x0a, 0x14, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0x90, 0x8d, 0xe7,
	0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x05, 0x62, 0x61, 0x64, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xba,
	0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0x52, 0x05, 0x62, 0x61, 0x64, 0x67,
	0x65, 0x12, 0x40, 0x0a, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x69,
	0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15, 0xe8,
	0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x8f, 0x90, 0xe7, 0xa4,
	0xba, 0xe8, 0xaf, 0x8d, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x74,
	0x69, 0x70, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a,
	0x12, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x8f, 0x8f,
	0xe8, 0xbf, 0xb0, 0x52, 0x0d, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x12, 0x60, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe7, 0xad,
	0x89, 0xe7, 0xba, 0xa7, 0x52, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x22, 0xf6, 0x03, 0x0a, 0x11, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0a, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x90, 0x8d, 0xe7, 0xa7,
	0xb0, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a,
	0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe5, 0x8d,
	0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x8f, 0x8f, 0xe8, 0xbf, 0xb0, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x12, 0x31, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x61, 0x63, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe8, 0x8e, 0xb7, 0xe5, 0xbe, 0x97, 0x52, 0x0a, 0x69,
	0x73, 0x5f, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x62, 0x61, 0x64,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5,
	0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0x52, 0x05, 0x62, 0x61, 0x64, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e,
	0x92, 0x41, 0x0b, 0x2a, 0x09, 0xe8, 0x83, 0x8c, 0xe6, 0x99, 0xaf, 0xe8, 0x89, 0xb2, 0x52, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x49, 0x0a, 0x13, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12, 0xe7, 0xad, 0x89, 0xe7,
	0xba, 0xa7, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0xa0, 0x87, 0xe9, 0xa2, 0x98, 0x52, 0x13,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x58, 0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x11,
	0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a,
	0xa1, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x4e, 0x0a,
	0x14, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x69, 0x70, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17,
	0x2a, 0x15, 0xe5, 0x8d, 0x87, 0xe7, 0xba, 0xa7, 0xe6, 0x88, 0x90, 0xe5, 0x8a, 0x9f, 0xe6, 0x8f,
	0x90, 0xe7, 0xa4, 0xba, 0xe8, 0xaf, 0x8d, 0x52, 0x14, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x22, 0xa4, 0x04,
	0x0a, 0x11, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x61, 0x64, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe4, 0xbb, 0xbb,
	0xe5, 0x8a, 0xa1, 0xe5, 0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a,
	0x0c, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0x86, 0x85, 0xe5, 0xae, 0xb9, 0x52, 0x0c, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x2d,
	0x92, 0x41, 0x2a, 0x2a, 0x28, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e,
	0x8b, 0x3a, 0x31, 0x20, 0x62, 0x6f, 0x6f, 0x6c, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x2c, 0x32,
	0x20, 0xe6, 0x95, 0xb0, 0xe5, 0x80, 0xbc, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0x52, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x15, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe6, 0x95,
	0xb0, 0xe5, 0x80, 0xbc, 0xe7, 0xb1, 0xbb, 0xe5, 0x9e, 0x8b, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1,
	0xe7, 0x9b, 0xae, 0xe6, 0xa0, 0x87, 0xe5, 0x80, 0xbc, 0x52, 0x15, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x12, 0x5b, 0x0a, 0x16, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63,
	0x61, 0x6c, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x95, 0xb0, 0xe5, 0x80, 0xbc, 0xe7, 0xb1, 0xbb,
	0xe5, 0x9e, 0x8b, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe5, 0xbd, 0x93, 0xe5, 0x89, 0x8d, 0xe8,
	0xbf, 0x9b, 0xe5, 0xba, 0xa6, 0x52, 0x16, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x65,
	0x72, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xae, 0x8c,
	0xe6, 0x88, 0x90, 0x52, 0x09, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x12, 0x48,
	0x0a, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0x92, 0x41, 0x17, 0x2a, 0x15,
	0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0xe7, 0x8a, 0xb6, 0xe6, 0x80, 0x81, 0xe8, 0x83, 0x8c, 0xe6,
	0x99, 0xaf, 0xe8, 0x89, 0xb2, 0x52, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x66, 0x69, 0x6e, 0x69,
	0x73, 0x68, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2a, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x63, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0e, 0x92, 0x41, 0x0b, 0x2a,
	0x09, 0xe4, 0xbb, 0xbb, 0xe5, 0x8a, 0xa1, 0x69, 0x63, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x63, 0x6f, 0x22, 0x41, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x51, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x75, 0x6c,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe8, 0xa7, 0x84, 0xe5, 0x88, 0x99, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x05, 0x72, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0x92, 0x41, 0x07, 0x2a, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x22, 0x45, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88,
	0xb7, 0x49, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xef, 0x01, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x69, 0x64, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe5, 0x90, 0x8d,
	0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06,
	0xe5, 0xa4, 0xb4, 0xe5, 0x83, 0x8f, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x5e,
	0x0a, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x17, 0x92, 0x41, 0x14, 0x2a, 0x12,
	0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe7, 0xad, 0x89, 0xe7, 0xba, 0xa7, 0xe8, 0xaf, 0xa6, 0xe6,
	0x83, 0x85, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x1d,
	0x0a, 0x1b, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xcb, 0x01,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x59, 0x0a, 0x15, 0x69,
	0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a,
	0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5, 0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe8, 0xba, 0xab,
	0xe4, 0xbb, 0xbd, 0xe6, 0x88, 0x90, 0xe9, 0x95, 0xbf, 0xe4, 0xb8, 0xad, 0xe5, 0xbf, 0x83, 0x52,
	0x15, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f,
	0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f,
	0x77, 0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x42, 0x23, 0x92, 0x41, 0x20, 0x2a, 0x1e, 0xe6, 0x98, 0xaf, 0xe5, 0x90, 0xa6, 0xe5,
	0xb1, 0x95, 0xe7, 0xa4, 0xba, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe7, 0xad, 0x89, 0xe7, 0xba,
	0xa7, 0xe8, 0xa7, 0x84, 0xe5, 0x88, 0x99, 0x52, 0x12, 0x69, 0x73, 0x5f, 0x73, 0x68, 0x6f, 0x77,
	0x5f, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x22, 0x45, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a, 0x2a,
	0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x4f, 0x0a, 0x07, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad, 0xe5,
	0x9b, 0xbe, 0xe5, 0x88, 0x97, 0xe8, 0xa1, 0xa8, 0x52, 0x06, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x12, 0x3e, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x20, 0x92, 0x41, 0x1d, 0x2a, 0x1b, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad,
	0xe5, 0x9b, 0xbe, 0xe5, 0x88, 0x87, 0xe6, 0x8d, 0xa2, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0xe9,
	0x97, 0xb4, 0xe9, 0x9a, 0x94, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x6e,
	0x22, 0x8c, 0x01, 0x0a, 0x0c, 0x43, 0x61, 0x72, 0x6f, 0x75, 0x73, 0x65, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x28, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x14, 0x92, 0x41, 0x11, 0x2a, 0x0f, 0xe8, 0xbd, 0xae, 0xe6, 0x92, 0xad, 0xe5, 0x9b, 0xbe, 0xe5,
	0x90, 0x8d, 0xe7, 0xa7, 0xb0, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x03, 0x69,
	0x6d, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8,
	0xbd, 0xae, 0xe6, 0x92, 0xad, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x03, 0x69, 0x6d, 0x67,
	0x12, 0x2d, 0x0a, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0xb7, 0xb3, 0xe8, 0xbd, 0xac, 0xe5,
	0x9c, 0xb0, 0xe5, 0x9d, 0x80, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x75, 0x72, 0x6c, 0x22,
	0x42, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x68,
	0x61, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0x92, 0x41, 0x0a,
	0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x22, 0xc6, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x27, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d,
	0x92, 0x41, 0x0a, 0x2a, 0x08, 0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0x49, 0x44, 0x52, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x12, 0x2f, 0x0a, 0x09, 0x6e, 0x69, 0x63, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c,
	0xe7, 0x94, 0xa8, 0xe6, 0x88, 0xb7, 0xe6, 0x98, 0xb5, 0xe7, 0xa7, 0xb0, 0x52, 0x09, 0x6e, 0x69,
	0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe5, 0xa4,
	0xb4, 0xe5, 0x9b, 0xbe, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x05,
	0x62, 0x61, 0x64, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x11, 0x92, 0x41, 0x0e,
	0x2a, 0x0c, 0xe8, 0xba, 0xab, 0xe4, 0xbb, 0xbd, 0xe5, 0xbe, 0xbd, 0xe7, 0xab, 0xa0, 0x52, 0x05,
	0x62, 0x61, 0x64, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe8, 0xba,
	0xab, 0xe4, 0xbb, 0xbd, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x35,
	0x0a, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x11, 0x92, 0x41, 0x0e, 0x2a, 0x0c, 0xe8, 0x8e, 0xb7, 0xe5, 0x8f,
	0x96, 0xe6, 0x97, 0xb6, 0xe9, 0x97, 0xb4, 0x52, 0x0c, 0x61, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0b, 0x92, 0x41, 0x08, 0x2a, 0x06, 0xe6, 0x96, 0x87,
	0xe6, 0xa1, 0x88, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x17, 0x5a, 0x15,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_center_v1_growth_center_proto_rawDescOnce sync.Once
	file_user_center_v1_growth_center_proto_rawDescData = file_user_center_v1_growth_center_proto_rawDesc
)

func file_user_center_v1_growth_center_proto_rawDescGZIP() []byte {
	file_user_center_v1_growth_center_proto_rawDescOnce.Do(func() {
		file_user_center_v1_growth_center_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_center_v1_growth_center_proto_rawDescData)
	})
	return file_user_center_v1_growth_center_proto_rawDescData
}

var file_user_center_v1_growth_center_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_user_center_v1_growth_center_proto_goTypes = []interface{}{
	(*IdentityRuleBaseInfo)(nil),        // 0: api.user_center.v1.IdentityRuleBaseInfo
	(*IdentityGradeInfo)(nil),           // 1: api.user_center.v1.IdentityGradeInfo
	(*IdentityGradeTask)(nil),           // 2: api.user_center.v1.IdentityGradeTask
	(*GetIdentityRuleRequest)(nil),      // 3: api.user_center.v1.GetIdentityRuleRequest
	(*GetIdentityRuleReply)(nil),        // 4: api.user_center.v1.GetIdentityRuleReply
	(*GetUserGrowthDetailRequest)(nil),  // 5: api.user_center.v1.GetUserGrowthDetailRequest
	(*GetUserGrowthDetailReply)(nil),    // 6: api.user_center.v1.GetUserGrowthDetailReply
	(*GetGrowthCenterEntryRequest)(nil), // 7: api.user_center.v1.GetGrowthCenterEntryRequest
	(*GetGrowthCenterEntryReply)(nil),   // 8: api.user_center.v1.GetGrowthCenterEntryReply
	(*GetIdentityCarouselRequest)(nil),  // 9: api.user_center.v1.GetIdentityCarouselRequest
	(*GetIdentityCarouselReply)(nil),    // 10: api.user_center.v1.GetIdentityCarouselReply
	(*CarouselInfo)(nil),                // 11: api.user_center.v1.CarouselInfo
	(*GetIdentityShareRequest)(nil),     // 12: api.user_center.v1.GetIdentityShareRequest
	(*GetIdentityShareReply)(nil),       // 13: api.user_center.v1.GetIdentityShareReply
}
var file_user_center_v1_growth_center_proto_depIdxs = []int32{
	1,  // 0: api.user_center.v1.IdentityRuleBaseInfo.identity_grade:type_name -> api.user_center.v1.IdentityGradeInfo
	2,  // 1: api.user_center.v1.IdentityGradeInfo.grade_task:type_name -> api.user_center.v1.IdentityGradeTask
	0,  // 2: api.user_center.v1.GetIdentityRuleReply.rules:type_name -> api.user_center.v1.IdentityRuleBaseInfo
	1,  // 3: api.user_center.v1.GetUserGrowthDetailReply.grade_info:type_name -> api.user_center.v1.IdentityGradeInfo
	11, // 4: api.user_center.v1.GetIdentityCarouselReply.banners:type_name -> api.user_center.v1.CarouselInfo
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_user_center_v1_growth_center_proto_init() }
func file_user_center_v1_growth_center_proto_init() {
	if File_user_center_v1_growth_center_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_center_v1_growth_center_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityRuleBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityGradeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityGradeTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityRuleReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGrowthDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserGrowthDetailReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrowthCenterEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrowthCenterEntryReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityCarouselRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityCarouselReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CarouselInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityShareRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_center_v1_growth_center_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetIdentityShareReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_center_v1_growth_center_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_center_v1_growth_center_proto_goTypes,
		DependencyIndexes: file_user_center_v1_growth_center_proto_depIdxs,
		MessageInfos:      file_user_center_v1_growth_center_proto_msgTypes,
	}.Build()
	File_user_center_v1_growth_center_proto = out.File
	file_user_center_v1_growth_center_proto_rawDesc = nil
	file_user_center_v1_growth_center_proto_goTypes = nil
	file_user_center_v1_growth_center_proto_depIdxs = nil
}
