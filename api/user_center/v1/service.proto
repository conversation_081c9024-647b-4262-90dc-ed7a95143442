syntax = "proto3";

package api.user_center.v1;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "common/common.proto";
import "user_center/v1/models.proto";

option go_package = "api/user_center/v1;v1";

service Service {
  rpc Healthy(common.EmptyRequest) returns (common.HealthyReply) {
    option (google.api.http) = {get: "/healthz"};
  }
  rpc GetUserInfo(GetUserInfoRequest) returns (GetUserInfoReply){
    option (google.api.http) = {get: "/v1/user_info"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取商业/动态的热门或者最新",tags: ["推荐"],
      parameters: {
        headers: [
          {
            name: "X-Token"
            type: STRING,
            description: "X-Token"
            required: true
          },
          {
            name: "X-Real-Ip"
            type: STRING,
            description: "客户端IP，没有时从 X-Forwarded-For 获取"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "X-Country-Code"
            type: STRING,
            description: "国家code，没有时从 Countrycode 获取"
          },
          {
            name: "X-Language-Code"
            type: STRING,
            description: "当前语言code，没有时从 Languagecode 获取"
          },
          {
            name: "Basicdata"
            type: STRING,
            description: "basic data"
          },
          {
            name: "X-Preferred-Language-Code，没有时从 Preferredlanguagecode 获取"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
  rpc StringReply(StringReplyRequest) returns (common.StringReply){
    option (google.api.http) = {get: "/v1/string_reply"};
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {summary: "获取商业/动态的热门或者最新",tags: ["推荐"],
      parameters: {
        headers: [
          {
            name: "X-Token"
            type: STRING,
            description: "X-Token"
            required: true
          },
          {
            name: "X-Real-Ip"
            type: STRING,
            description: "客户端IP，没有时从 X-Forwarded-For 获取"
          },
          {
            name: "X-User-Id"
            type: STRING,
            description: "用户ID"
          },
          {
            name: "X-Request-Id"
            type: STRING,
            description: "request_id，没有时从 Wikidatacenter-Request-Id获取"
          },
          {
            name: "X-Country-Code"
            type: STRING,
            description: "国家code，没有时从 Countrycode 获取"
          },
          {
            name: "X-Language-Code"
            type: STRING,
            description: "当前语言code，没有时从 Languagecode 获取"
          },
          {
            name: "Basicdata"
            type: STRING,
            description: "basic data"
          },
          {
            name: "X-Preferred-Language-Code，没有时从 Preferredlanguagecode 获取"
            type: STRING,
            description: "偏好语言"
          },
          {
            name: "X-Device-Id"
            type: STRING,
            description: "设备标识，没有时会从Basicdata中解析"
          }
        ]
      };
    };
  }
}
