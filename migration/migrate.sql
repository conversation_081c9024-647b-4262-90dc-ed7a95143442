CREATE TABLE `user_address` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户ID',
    `country_code` varchar(64) NOT NULL DEFAULT '' COMMENT '国家code',
    `country_name` varchar(64) NOT NULL DEFAULT '' COMMENT '国家名称',
    `user_name` varchar(128) NOT NULL DEFAULT '' COMMENT '用户名称',
    `phone_country_code` varchar(64) NOT NULL DEFAULT '' COMMENT '手机国家code',
    `phone_area_code` varchar(64) NOT NULL DEFAULT '' COMMENT '手机区号',
    `phone` varchar(128) NOT NULL DEFAULT '' COMMENT '联系电话',
    `province_name` varchar(64) NOT NULL DEFAULT '' COMMENT '省份名称',
    `city_name` varchar(64) NOT NULL DEFAULT '' COMMENT '城市名称',
    `street_address` varchar(256) NOT NULL DEFAULT '' COMMENT '街道地址',
    `building_unit_address` varchar(256) NOT NULL DEFAULT '' COMMENT '单元地址',
    `postal_code`           varchar(64)  NOT NULL DEFAULT '' COMMENT '邮编',
    `is_default`            tinyint      NOT NULL DEFAULT 0 COMMENT '是否默认地址',
    `created_at`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`            datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`            datetime COMMENT '删除时间',
    KEY                     `idx_user_id_deleted_at` (`user_id`,`deleted_at`,`updated_at`)
) AUTO_INCREMENT=100000  COMMENT='用户地址表';

CREATE TABLE `goods`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `goods_id`       varchar(64)    NOT NULL DEFAULT '' COMMENT '商品ID',
    `category`       tinyint        NOT NULL DEFAULT 0 COMMENT '商品类型：0：实物商品；1：VIP；2：VPS',
    `name`           varchar(64)    NOT NULL DEFAULT '' COMMENT '商品名称',
    `base_price`     decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '商品基础价格',
    `use_base_price` tinyint        NOT NULL DEFAULT 0 COMMENT '是否使用基础价格',
    `selected_price` int            NOT NULL DEFAULT 0 COMMENT '选中的sku价格',
    `description`    text COMMENT '商品描述',
    `labels`         text comment '商品标签',
    `image`          text COMMENT '商品主图',
    `status`         tinyint        NOT NULL DEFAULT 0 COMMENT '商品状态：0：下架；1：上架',
    `free_shipping`  tinyint        NOT NULL DEFAULT 0 COMMENT '是否免运费',
    `carousels`      text COMMENT '商品轮播图',
    `details`        text COMMENT '商品详情',
    `specs`          text COMMENT '商品规格',
    `translate`      text COMMENT '商品翻译',
    `extra`          text COMMENT '扩展字段',
    `created_at`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`     datetime COMMENT '删除时间',
    UNIQUE KEY `idx_goods_id` (`goods_id`),
    KEY              `idx_created_at` (`created_at`),
    KEY              `idx_status_created_at` (`status`,`created_at`)
) AUTO_INCREMENT=100000  COMMENT='商品信息表';

CREATE TABLE `goods_statistics`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `goods_id`      varchar(64) NOT NULL DEFAULT '' COMMENT '商品ID',
    `status`        tinyint     NOT NULL DEFAULT 0 COMMENT '商品状态：0：下架；1：上架',
    `sales`         int         NOT NULL DEFAULT 0 COMMENT '实际销量',
    `virtual_sales` int         NOT NULL DEFAULT 0 COMMENT '虚拟销量',
    `total_sales`   int         NOT NULL DEFAULT 0 COMMENT '总销量',
    `views`         int         NOT NULL DEFAULT 0 COMMENT '总浏览量',
    `created_at`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`    datetime COMMENT '删除时间',
    UNIQUE KEY `idx_goods_id` (`goods_id`),
    KEY             `idx_status_sales` (`status`,`sales`),
    KEY             `idx_status_total_sales_goods_id`(`status`,`total_sales`,`goods_id`)
) AUTO_INCREMENT=100000  COMMENT='商品统计表';

CREATE TABLE `sku`
(
    `id`            bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `goods_id`      varchar(64)    NOT NULL DEFAULT '' COMMENT '商品ID',
    `sku_id`        varchar(64)    NOT NULL DEFAULT '' COMMENT 'sku_id',
    `specs`         text COMMENT '商品规格',
    `price`         decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '商品价格',
    `stock`         int            NOT NULL DEFAULT 0 COMMENT '商品库存',
    `virtual_sales` int            NOT NULL DEFAULT 0 COMMENT '虚拟销量',
    `sales`         int            NOT NULL DEFAULT 0 COMMENT '商品销量',
    `disable`       tinyint        NOT NULL DEFAULT 0 COMMENT '商品状态：0：禁用；1：启用',
    `created_at`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`    datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`    datetime COMMENT '删除时间',
    UNIQUE KEY `idx_sku_id` (`sku_id`),
    KEY             `idx_goods_id_deleted_at` (`goods_id`,`deleted_at`)
) AUTO_INCREMENT=100000  COMMENT='商品sku表';

CREATE TABLE `goods_snapshot`
(
    `id`         bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `goods_id`   varchar(64) NOT NULL DEFAULT '' COMMENT '商品ID',
    `version`    int         NOT NULL DEFAULT 0 COMMENT '快照版本号',
    `snapshot`   longtext COMMENT '快照内容',
    `created_at` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` datetime COMMENT '删除时间',
    UNIQUE KEY `idx_goods_version` (`goods_id`,`version`)
) AUTO_INCREMENT=100000  COMMENT='商品快照表';

CREATE TABLE `order`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `order_no`                varchar(64)    NOT NULL DEFAULT '' COMMENT '订单号',
    `source`                  tinyint        NOT NULL DEFAULT 0 COMMENT '订单来源：0：金币商城；1：VIP；2：VPS；3：报告；4：展会；5：EA',
    `payment_method`          tinyint        NOT NULL DEFAULT 0 COMMENT '下单时选择的支付方式：0：金币；1：礼品卡；2：用户任务;3：支付宝；4：微信；5：苹果内购；6：google内购',
    `platform`                tinyint        NOT NULL DEFAULT 0 COMMENT '下单平台:0:苹果；1：安卓；2：PC；3：web',
    `user_id`                 varchar(64)    NOT NULL DEFAULT '' COMMENT '用户ID',
    `client_ip`               varchar(64)    NOT NULL DEFAULT '' COMMENT '下单时的IP地址',
    `language_code`           varchar(64)    NOT NULL DEFAULT '' COMMENT '用户下单时的语言',
    `preferred_language_code` varchar(64)    NOT NULL DEFAULT '' COMMENT '用户下单时的偏好语言',
    `country_code`            varchar(64)    NOT NULL DEFAULT '' COMMENT '用户下单时的国家代码',
    `currency_symbol`         varchar(64)    NOT NULL DEFAULT '' COMMENT '用户下单时的货币',
    `basic_data`              varchar(128)   NOT NULL DEFAULT '' COMMENT '用户下单时的basic_data',
    `address_id`              bigint         NOT NULL DEFAULT 0 COMMENT '地址ID',
    `address`                 text COMMENT '地址快照',
    `quantity`                int            NOT NULL DEFAULT 0 COMMENT '商品数量',
    `estimated_shipping_time` datetime                default null COMMENT '预计发货时间',
    `total_amount`            decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '订单总金额',
    `shipping_fee`            decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '运费',
    `status`                  tinyint        NOT NULL DEFAULT 0 COMMENT '订单状态：0：待支付；1：已取消；2：已支付；3：已完成',
    `goods_name`              varchar(64)    NOT NULL DEFAULT '' COMMENT '商品名称(冗余字段，后台搜索订单使用)',
    `tracking_no`             varchar(64)    NOT NULL DEFAULT '' COMMENT '物流单号,冗余字段',
    `extra`                   text COMMENT '扩展字段',
    `created_at`              datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`              datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`              datetime COMMENT '删除时间',
    UNIQUE KEY `idx_order_no` (`order_no`),
    KEY                       `idx_user_id_status_created_at` (`user_id`,`status`,`created_at`),
    KEY                        `idx_user_id_created_at` (`user_id`,`created_at`)
) AUTO_INCREMENT=100000  COMMENT='订单表';
alter table `order` add index `idx_source_created_at`(`source`,`created_at`);


CREATE TABLE `order_item`
(
    `id`                bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `order_no`          varchar(64)    NOT NULL DEFAULT '' COMMENT '订单号',
    `goods_id`          varchar(64)    NOT NULL DEFAULT '' COMMENT '商品ID',
    `sku_id`            varchar(64)    NOT NULL DEFAULT '' COMMENT 'sku_id',
    `price`             int            NOT NULL DEFAULT 0.00 COMMENT '商品价格',
    `price_unit`        varchar(64)    NOT NULL DEFAULT '' COMMENT '价格单位',
    `goods_snapshot_id` bigint         NOT NULL DEFAULT 0 COMMENT '商品快照ID',
    `quantity`          int            NOT NULL DEFAULT 0 COMMENT '商品数量',
    `total_amount`      decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '当前商品总金额',
    `shipping_fee`      decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '运费',
    `goods_name`        varchar(128)   NOT NULL DEFAULT '' COMMENT '商品名称',
    `spec_desc`         varchar(128)   NOT NULL DEFAULT '' COMMENT '商品规格描述',
    `specs`             text COMMENT '选中的商品规格',
    `created_at`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`        datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`        datetime COMMENT '删除时间',
    KEY                 `idx_order_no` (`order_no`),
    KEY                 `idx_goods_id_sku_id` (`goods_id`,`sku_id`)
) AUTO_INCREMENT=100000  COMMENT='订单物品表';

CREATE TABLE `payment`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `payment_no`   varchar(64)    NOT NULL DEFAULT '' COMMENT '支付单号',
    `order_no`     varchar(64)    NOT NULL DEFAULT '' COMMENT '订单号',
    `operation_no` varchar(64)    NOT NULL DEFAULT '' COMMENT '支付侧业务ID',
    `total_amount` decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '当前商品总金额',
    `paid_total`   decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '支付总金额',
    `status`       tinyint        NOT NULL DEFAULT 0 COMMENT '支付状态：0：待支付；1：已支付；2：已取消；3：待退款；4：已退款',
    `paid_method`  tinyint        NOT NULL DEFAULT 0 COMMENT '支付方式：0：金币；1：礼品卡；2：用户任务;3：支付宝；4：微信；5：苹果内购；6：google内购',
    `paid_time`    datetime COMMENT '支付时间',
    `extra`        text COMMENT '扩展字段',
    `created_at`   datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`   datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`   datetime COMMENT '删除时间',
    UNIQUE KEY `idx_payment_no` (`payment_no`),
    KEY            `idx_order_no` (`order_no`)
) AUTO_INCREMENT=100000  COMMENT='支付单表';


CREATE TABLE `logistics`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `order_no`       varchar(64)    NOT NULL DEFAULT '' COMMENT '订单号',
    `tracking_no`    varchar(64)    NOT NULL DEFAULT '' COMMENT '物流单号',
    `shipping_fee`   decimal(20, 2) NOT NULL DEFAULT 0 COMMENT '运费',
    `status`         tinyint        NOT NULL DEFAULT 0 COMMENT '物流状态：0：已下单；1：仓库处理中；2：已发货；3：已揽件；4：运输中；5：派件中；6：已签收',
    `shipping_notes` varchar(64)    NOT NULL DEFAULT '' COMMENT '物流备注',
    `shipping_time`  datetime COMMENT '发货时间',
    `delivery_time`  datetime COMMENT '送达时间',
    `sign_time`      datetime COMMENT '签收时间',
    `carrier_code`   varchar(64)    NOT NULL DEFAULT '' COMMENT '物流公司编号',
    `carrier_name`   varchar(64)    NOT NULL DEFAULT '' COMMENT '物流公司名称',
    `created_at`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`     datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`     datetime COMMENT '删除时间',
    KEY              `idx_tracking_no` (`tracking_no`),
    KEY              `idx_order_no` (`order_no`)
) AUTO_INCREMENT=100000  COMMENT='物流信息表';

CREATE TABLE `logistics_detail`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `order_no`    varchar(64) NOT NULL DEFAULT '' COMMENT '订单号',
    `tracking_no` varchar(64) NOT NULL DEFAULT '' COMMENT '物流单号',
    `delivery_id` varchar(64) NOT NULL DEFAULT '' COMMENT '物流流水ID',
    `status`      tinyint     NOT NULL DEFAULT 0 COMMENT '物流状态：0：已下单；1：仓库处理中；2：已发货；3：已揽件；4：运输中；5：派件中；6：已签收',
    `detail`      text COMMENT '物流备注',
    `event_time`  datetime COMMENT '事件时间',
    `created_at`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at`  datetime COMMENT '删除时间',
    KEY           `idx_tracking_no_event_time` (`tracking_no`,`event_time`),
    KEY           `idx_order_no` (`order_no`,`event_time`)
) AUTO_INCREMENT=100000  COMMENT='物流详情表';

CREATE TABLE `task_config`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键（自增）',
    `task_type`      tinyint     NOT NULL COMMENT '任务类型（1=新手任务，2=日常任务）',
    `task_enum_code` varchar(50) NOT NULL DEFAULT '' COMMENT '任务枚举表Id',
    `complete_times` int         NOT NULL COMMENT '完成次数（如发布动态5次）',
    `task_icon`      varchar(255)         DEFAULT NULL COMMENT '任务图标路径（上传后的存储路径或URL）',
    `reward_icon`    varchar(255)         DEFAULT NULL COMMENT '奖励图标路径（上传后的存储路径或URL）',
    `task_title`     varchar(100)         DEFAULT NULL COMMENT '任务标题',
    `min_version`    varchar(32)          DEFAULT NULL COMMENT '最低版本号（限制可参与的客户端版本，如"1.2.3"）',
    `show_project`   varchar(50) NOT NULL COMMENT '展示项目（WikiFX/web端/pwa等）',
    `reward_type`    tinyint     NOT NULL COMMENT '奖励类型（1=金币，2=实物，3=虚拟）',
    `reward_config`  json        NOT NULL COMMENT '奖励配置内容（奖励数值等）',
    `task_config`    json        NOT NULL COMMENT '任务额外配置',
    `i18n`           json        NOT NULL COMMENT '多语言配置',
    `sort_order`     int                  DEFAULT '0' COMMENT '排序值，越大越靠前',
    `config_version` varchar(32)          DEFAULT NULL COMMENT '配置版本号',
    `visible_users`  varchar(50)          DEFAULT NULL COMMENT '可见用户范围（如"全部用户"、"新手用户"等）',
    `modifier`       varchar(50)          DEFAULT NULL COMMENT '最后修改人（用户Id或操作账号）',
    `status`         tinyint     NOT NULL DEFAULT '0' COMMENT '状态（1=开启，2=关闭）',
    `created_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间（UTC）',
    `updated_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（UTC）',
    PRIMARY KEY (`id`),
    KEY              `idx_status` (`status`) COMMENT '状态索引',
    KEY              `idx_config_version` (`config_version`) COMMENT '配置版本索引',
    KEY              `idx_task_type` (`task_type`) COMMENT '任务类型索引'
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='任务配置表';

CREATE TABLE `task_progress`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键（自增）',
    `user_id`        varchar(50) NOT NULL COMMENT '用户Id（如用户唯一标识）',
    `task_config_id` int unsigned NOT NULL COMMENT '任务配置Id',
    `user_progress`  int         NOT NULL DEFAULT '0' COMMENT '用户完成进度（如浏览次数）',
    `status`         tinyint     NOT NULL DEFAULT '1' COMMENT '任务状态（1=进行中，2=已完成未领取，3=已完成已领取，4=已过期）',
    `expire_time`    datetime    NOT NULL COMMENT '过期时间（UTC）',
    `receive_time`   datetime             DEFAULT NULL COMMENT '奖励领取时间（UTC）',
    `user_timezone`  varchar(50)          DEFAULT 'UTC' COMMENT '用户所在时区（如"Asia/Shanghai"）',
    `created_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（UTC）',
    `updated_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（UTC）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_task` (`user_id`,`task_config_id`,`expire_time`),
    KEY              `idx_status_expire` (`status`,`expire_time`) COMMENT '状态+过期时间索引',
    KEY              `idx_user_id` (`user_id`) COMMENT '用户Id索引'
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='用户任务进度表';

CREATE TABLE `task_reward_issue`
(
    `id`               int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键（自增）',
    `user_id`          varchar(50) NOT NULL COMMENT '用户Id（如用户唯一标识）',
    `task_config_id`   int unsigned NOT NULL COMMENT '任务配置Id',
    `task_progress_id` int unsigned NOT NULL COMMENT '任务进度Id',
    `reward_type`      tinyint     NOT NULL COMMENT '奖励类型（1=金币，2=实物，3=虚拟）',
    `reward_config`    json        NOT NULL COMMENT '奖励配置内容（奖励数值等）',
    `status`           tinyint     NOT NULL DEFAULT '0' COMMENT '发放状态（0=待领取，1=已发放，2=已过期，3=发放中，4=发放失败）',
    `fail_reason`      varchar(255)         DEFAULT NULL COMMENT '失败原因',
    `operation_id`     varchar(64) NOT NULL COMMENT '操作唯一标识',
    `created_at`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（UTC）',
    `updated_at`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（UTC）',
    PRIMARY KEY (`id`),
    KEY                `idx_user_status` (`user_id`,`status`) COMMENT '用户+状态索引',
    KEY                `idx_task_config_id` (`task_config_id`) COMMENT '任务配置Id索引'
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='奖励发放记录表';

CREATE TABLE `sign_reward_log`
(
    `id`           int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键（自增）',
    `user_id`      varchar(64) NOT NULL COMMENT '用户ID（唯一标识）',
    `record_id`    int unsigned NOT NULL COMMENT '签到配置表id',
    `sign_id`      int unsigned NOT NULL COMMENT '来源关联ID（签到记录ID）',
    `source_type`  tinyint     NOT NULL DEFAULT '1' COMMENT '来源类型（1=签到送，2=签到满送）',
    `gold_coins`   int         NOT NULL DEFAULT '0' COMMENT '奖励数量（如金币数量）',
    `status`       tinyint     NOT NULL DEFAULT '0' COMMENT '发放状态（0=处理中，1=成功，2=失败）',
    `error_msg`    varchar(255)         DEFAULT NULL COMMENT '失败原因（仅status=2时有效）',
    `operation_id` varchar(64) NOT NULL COMMENT '操作唯一标识（防重复）',
    `updated_at`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（UTC）',
    `created_at`   datetime    NOT NULL COMMENT '操作时间（UTC）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_operation_id` (`operation_id`) COMMENT '操作唯一标识唯一索引',
    KEY            `idx_user_status` (`user_id`,`status`) COMMENT '用户+状态联合索引'
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='奖励流水表';

CREATE TABLE `sign_config`
(
    `id`           int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键（自增）',
    `config_key`   varchar(50) NOT NULL COMMENT '配置标识（如sign_in_config）',
    `config_value` json        NOT NULL COMMENT '配置内容（签到相关配置）可能包含多段奖励规则',
    `status`       tinyint     NOT NULL DEFAULT '1' COMMENT '状态（1=生效，2=失效）',
    `cycle_days`   tinyint     NOT NULL DEFAULT '7' COMMENT '签到周期天数（默认7天）',
    `updated_by`   varchar(50)          DEFAULT NULL COMMENT '更新人',
    `updated_at`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（UTC）',
    `created_at`   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`) COMMENT '配置标识唯一索引',
    KEY            `idx_status` (`status`) COMMENT '状态普通索引'
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='任务配置表';

CREATE TABLE `sign_record`
(
    `id`               int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键（自增）',
    `user_id`          varchar(64) NOT NULL COMMENT '用户ID（唯一标识）',
    `task_id`          int unsigned NOT NULL COMMENT '签到配置表id',
    `sign_date`        timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '签到日期（服务器UTC时间存储）',
    `consecutive_days` int         NOT NULL DEFAULT '1' COMMENT '当前连续签到天数',
    `reward_json`      json        NOT NULL COMMENT '奖励详情JSON，包含普通奖励和连续签到奖励',
    `user_timezone`    varchar(50)          DEFAULT 'UTC' COMMENT '用户所在时区（如"Asia/Shanghai"）',
    `updated_at`       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间（UTC）',
    `created_at`       datetime    NOT NULL DEFAULT (now()) COMMENT '记录时间（UTC）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_date_timezone` (`user_id`,`sign_date`) COMMENT '用户+日期 唯一索引',
    KEY                `idx_user_id` (`user_id`) COMMENT '用户ID普通索引'
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='用户签到记录表';

CREATE TABLE `express_query_log`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT,
    `tracking_no`  varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '快递单号',
    `carrier_code` varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '快递公司编码',
    `carrier_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '快递公司名称',
    `status`       tinyint                                 NOT NULL DEFAULT '0' COMMENT '快递状态',
    `query_time`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
    `last_update`  timestamp NULL DEFAULT NULL COMMENT '最后更新时间',
    `is_completed` tinyint NOT NULL DEFAULT '0' COMMENT '是否已完成',
    `raw_response` json                                             DEFAULT NULL COMMENT '原始响应数据',
    `phone`        varchar(20) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '查询时使用的手机号',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `deleted_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY            `idx_tracking_no` (`tracking_no`),
    KEY            `idx_carrier_code` (`carrier_code`),
    KEY            `idx_query_time` (`query_time`)
) ENGINE=InnoDB AUTO_INCREMENT=10000  COMMENT='快递查询日志表';

