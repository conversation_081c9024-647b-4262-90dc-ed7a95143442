alter table `expo` add index `idx_start_end_type` (`start`, `end`, `type`);
alter table `expo` add column `registrants` int not null default '0' comment '报名人数';
alter table `expo` add column `baidu_map` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '百度地图地址';
alter table `expo` add column `google_map` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'google地图地址';
alter table `participant` add index `idx_expo_id_sub_identify_code` (`expo_id`, `sub_identity_code`);
alter table `participant` add index `idx_expo_id_user_id` (`expo_id`, `user_id`);
alter table `participant` add index `idx_code_created_at` (`code`,`created_at`);
alter table `order` add index `idx_order_no`(`order_no`);
alter table `ticket_type` add index `idx_expo_id_price` (`expo_id`,`price`);
alter table `participant` add index `idx_user_id_ticket_status` (`user_id`, `ticket_status`);
alter table participant add is_employee tinyint default 0 not null comment '是否是企业员工：0-否;1-是';

CREATE TABLE `expo_image` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `image_url` VARCHAR(1000) NOT NULL DEFAULT '' COMMENT '图片地址',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_created_at` (`expo_id`, `created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会图片';

CREATE TABLE `expo_live` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `url` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '直播地址',
    `cover` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '直播封面',
    `app_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'app_id',
    `room_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '房间号',
    `user_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '用户ID',
    `level` INT NOT NULL DEFAULT '0' COMMENT '直播等级',
    `enable` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用;1-启用',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_created_at` (`expo_id`, `created_at`),
    KEY `uk_expo_id_url` (`expo_id`, `level`)
) AUTO_INCREMENT = 10000 COMMENT = '展会直播';

CREATE TABLE `guest` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `avatar` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '嘉宾头像',
    `name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '嘉宾名称',
    `user_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '用户ID',
    `wiki_number` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'Wiki ID',
    `phone_area_code` VARCHAR(8) NOT NULL DEFAULT '' COMMENT '手机号区号',
    `phone` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '手机号',
    `email` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '邮箱地址',
    `wechat` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '微信号',
    `whats_app` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'whats_app',
    `facebook` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'facebook',
    `twitter` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'twitter',
    `linkedin` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'linkedin',
    `instagram` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'instagram',
    `reddit` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'reddit',
    `youtube` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'youtube',
    `telegram` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'telegram',
    `tiktok` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'tiktok',
    `is_register` TINYINT NOT NULL DEFAULT '0' COMMENT '是否注册：0-否;1-是',
    `enable` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用;1-启用',
    `extra` TEXT COMMENT '扩展字段(json数据)',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `country_flag` VARCHAR(200) NULL DEFAULT '' COMMENT '国旗',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_created_at` (`created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '嘉宾表';

CREATE TABLE `expo_guest` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `guest_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '嘉宾id',
    `role` TINYINT NOT NULL DEFAULT '0' COMMENT '角色：0-演讲嘉宾;1-主持人',
    `enable` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用;1-启用',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_created_at` (`expo_id`, `created_at`,`guest_id`)
) AUTO_INCREMENT = 10000 COMMENT = '展会嘉宾表';

CREATE TABLE `expo_community` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `start` TIMESTAMP DEFAULT NULL COMMENT '开始时间(冗余字段)',
    `end` TIMESTAMP DEFAULT NULL COMMENT '结束时间(冗余字段)',
    `description` TEXT COMMENT '展会介绍',
    `enable` tinyint NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用;1-启用',
    `topic_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '话题ID',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_created_at` (`expo_id`,`enable`, `created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会嘉宾表';

CREATE TABLE `expo_hall` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `type` TINYINT NOT NULL DEFAULT '0' COMMENT '会场类型：0-主会场;1-分会场',
    `name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '会场名称',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_created_at` (`expo_id`,`enable`, `created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会会场表';


CREATE TABLE `expo_schedule` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `hall_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '会场ID',
    `type` TINYINT NOT NULL DEFAULT '0' COMMENT '日程类型',
    `theme` TEXT COMMENT '演讲主题',
    `host_id` BIGINT UNSIGNED NOT NULL COMMENT '主持人ID(guest表主键)',
    `date` VARCHAR(10) NOT NULL DEFAULT '' COMMENT '演讲日期(2025-07-07)',
    `start` TIMESTAMP DEFAULT NULL COMMENT '演讲开始时间',
    `end` TIMESTAMP DEFAULT NULL COMMENT '演讲结束时间',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_start` (`expo_id`, `start`),
    KEY `idx_start_enable` (`start`,`enable`)
) AUTO_INCREMENT = 10000 COMMENT = '展会议程表';

CREATE TABLE `expo_schedule_guest` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `schedule_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '议程ID',
    `guest_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '演讲嘉宾ID(guest表主键)',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_schedule_id_speaker_id` (`schedule_id`, `speaker_id`),
    KEY `idx_expo_id_deleted_at` (`expo_id`,`deleted_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会议程嘉宾表';

CREATE TABLE `expo_exhibitor_apply` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `user_id` varchar(20) NOT NULL DEFAULT '' COMMENT '用户ID',
    `booth` varchar(100) NOT NULL DEFAULT '' COMMENT '展位大小',
    `company` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '公司名称',
    `website` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '官方网址',
    `contact` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '联系人',
    `phone_area_code` VARCHAR(8) NOT NULL DEFAULT '' COMMENT '手机号区号',
    `phone` VARCHAR(16) NOT NULL DEFAULT '' COMMENT '手机号',
    `email` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '邮箱地址',
    `status` TINYINT NOT NULL DEFAULT '0' COMMENT '状态：0-待审核;1-审核通过;2-审核未通过',
    `reason`  VARCHAR(128) NOT NULL DEFAULT '' COMMENT '审核未通过原因',
    `client_ip` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '报名时的IP地址',
    `language_code` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '报名时的语言',
    `preferred_language_code` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '报名时的偏好语言',
    `country_code` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '报名时的国家代码',
    `basic_data` VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'basic data',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_created_at` (`expo_id`, `created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '参展商报名记录表';

CREATE TABLE `expo_exhibitor` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL  DEFAULT '0'  COMMENT '展会ID',
    `hall_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '会场ID',
    `trader_code` VARCHAR(24) NOT NULL DEFAULT '' COMMENT '关联的交易商code',
    'exhibitor_type' TINYINT NOT NULL DEFAULT '0' COMMENT '参展商类型：1：服务商 3：交易商',
    `trader_name` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '参展商名称',
    `logo` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'logo图片地址',
    `min_logo` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'logo缩略图地址',
    `sponsor_level` TINYINT NOT NULL DEFAULT '0' COMMENT '赞助等级：0-未赞助；1-白银；2-黄金；3-铂金；4-钻石；5-全球',
    `booth_length` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '展位长度',
    `booth_width` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '展位宽度',
    `booth_height` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '展位高度',
    `booth` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '展位区域',
    `contact` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '联系人',
    `phone_area_code` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '电话区域',
    `phone` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '电话',
    `email` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '邮箱',
    `rank` INT NOT NULL DEFAULT '0' COMMENT '排序',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_sponsor_level` (`expo_id`, `sponsor_level`),
    KEY `idx_expo_id_rank_created_at` (`expo_id`, `rank`,`created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会参展商表';

CREATE TABLE `expo_exhibitor_employee` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL  DEFAULT '0'  COMMENT '展会ID',
    `exhibitor_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '参展商(expo_exhibitor主键)',
    `employee_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '员工ID(票夹ID)',
    `type` TINYINT NOT NULL DEFAULT '0' COMMENT '员工类型：0-报名员工；1-团队成员',
    `user_id` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '用户ID',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_exhibitor_id_enable` (`expo_id`, `exhibitor_id`,`enable`),
    KEY `idx_expo_id_exhibitor_user_id` (`expo_id`, `exhibitor_id`,`user_id`)
) AUTO_INCREMENT = 10000 COMMENT = '展会参展商员工表';

CREATE TABLE `expo_guide` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL  DEFAULT '0'  COMMENT '展会ID',
    `map_url` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '展会指南地图',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_enable` (`expo_id`, `enable`)
) AUTO_INCREMENT = 10000 COMMENT = '展会地图表';

CREATE TABLE `expo_partner` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL  DEFAULT '0'  COMMENT '展会ID',
    `name` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '名称',
    `logo` VARCHAR(128) NOT NULL DEFAULT '' COMMENT 'logo',
    `website` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '网址',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `type` TINYINT NOT NULL DEFAULT '0' COMMENT '类型：0:区块链和数字资产合作伙伴;1:WEB3.0、METAVERSE和游戏合作伙伴;2:金融科技、人工智能、外汇和支付合作伙伴;3:VCS,金融服务合作伙伴;4:社区合作伙伴(加密货币和外汇',
    `rank` INT NOT NULL DEFAULT '0' COMMENT '排序',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_enable_rank_created_at` (`expo_id`,`enable`, `rank`,`created_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会合作伙伴表';

CREATE TABLE `expo_review` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL  DEFAULT '0'  COMMENT '展会ID',
    `type` TINYINT NOT NULL DEFAULT '0' COMMENT '类型：0:商品；1：图片',
    `url` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '视频地址；图片地址在extra中',
    `count` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '文件数量',
    `cover` VARCHAR(128) NOT NULL DEFAULT '' COMMENT '封面地址',
    `enable` TINYINT NOT NULL DEFAULT '0' COMMENT '是否启用：0-不启用；1-启用',
    `extra` LONGTEXT COMMENT '扩展字段',
    `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_expo_id_enable` (`expo_id`, `enable`)
) AUTO_INCREMENT = 10000 COMMENT = '展会回顾表';

alter table participant
    add column `apply_status` int  NOT NULL DEFAULT '0' COMMENT '报名状态：0未知， 1-待审核;2-审核通过;3-审核未通过',
    add column  int  NOT NULL DEFAULT 0 COMMENT '拒绝原因',
    add column `apply_userid` varchar(20)  NOT NULL DEFAULT '' COMMENT '报名userid',
    add column is_resister tinyint NOT NULL DEFAULT '0' COMMENT '是否注册：0-未注册;1-已注册',
    add column `audit_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '审核时间',
    add column `audit_user` varchar(50)  COMMENT '审核人',
    add column `is_comment_notice`  tinyint NOT NULL DEFAULT '0' COMMENT '是否评论通知：0-否;1-是',
    add column `for_other`  tinyint NOT NULL DEFAULT '0' COMMENT '是否提他人报名：0-否；1-是',
    add column `country_code` varchar(10)  NOT NULL DEFAULT '0'  COMMENT '国家代码',
    add column `ip` varchar(50) NOT NULL DEFAULT ''   COMMENT 'ip',
    add column `language_code` varchar(10) NOT NULL DEFAULT ''   COMMENT '语言code',


CREATE TABLE `expo_schedule_reserve` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `user_id` VARCHAR(32) NOT NULL DEFAULT '0' COMMENT '用户ID',
    `schedule_guest_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会议程嘉宾id',
    `language_code` VARCHAR(32) NOT NULL DEFAULT '' COMMENT '语言代码',
    `notify` INT NOT NULL DEFAULT '0' COMMENT '是否通知',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_user_id_expo_id,schedule_guest_id` (`user_id`,`expo_id`, `schedule_guest_id`),
    KEY `idx_expo_id_notify_deleted_at` (`expo_id`, `notify`, `deleted_at`)
) AUTO_INCREMENT = 10000 COMMENT = '展会议程预约';