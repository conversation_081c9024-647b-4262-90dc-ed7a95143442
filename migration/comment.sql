
CREATE TABLE `expo_comment` (
    `id` varchar(50) NOT NULL  PRIMARY KEY COMMENT '主键ID',
    `expo_id` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '展会ID',
    `user_id` varchar(50)  NOT NULL DEFAULT '' COMMENT '用户ID',
    `status` int NOT NULL DEFAULT '100' COMMENT '100 未审核 200 通过 401 拒绝',
    `content` text NOT NULL DEFAULT '' COMMENT '评论内容',
    `content_type` tinyint NOT NULL DEFAULT '0' COMMENT '评价类型 1普通 2绑定实盘 3固定评价 4预约演讲 5报名通知',
    `content_desc` varchar(500) NOT NULL DEFAULT 0 COMMENT '补充字段',
    `root_id` varchar(50) NOT NULL DEFAULT''   COMMENT '根Id',
    `nickname` varchar(50) NOT NULL DEFAULT''   COMMENT '发帖用户昵称',
    `reply_nickname` varchar(50) NOT NULL DEFAULT''   COMMENT '回复用户昵称',
    `parent_id` varchar(50) NOT NULL DEFAULT '' COMMENT '父id',
    `language_code` varchar(10) NOT NULL DEFAULT '' COMMENT '语言',
    `country_code` varchar(10) NOT NULL DEFAULT '' COMMENT '国家code',
    `ip` varchar(255) NOT NULL DEFAULT '' COMMENT 'ip',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
     `like_count`  int NOT NULL DEFAULT '0' COMMENT '点赞总数',
     `deleted_at` DATETIME COMMENT '删除时间',
     `audit_at` DATETIME COMMENT '审核时间',
     `audit_name` varchar(50) NOT NULL DEFAULT '' COMMENT '审核用户',
     `update_name` varchar(50) NOT NULL DEFAULT '' COMMENT '修改用户',
     `refuse_reason` varchar(50) NOT NULL DEFAULT '' COMMENT '拒绝理由',
    KEY `idx_created_at` (`created_at`),
    KEY `idx_expo_id` (`expo_id`),
    KEY `idx_root_id` (`root_id`)
) AUTO_INCREMENT = 10000 COMMENT = '展会评论';

CREATE TABLE `expo_comment_image` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `comment_id` varchar(50) NOT NULL DEFAULT '' COMMENT '评论ID',
    `image_url` VARCHAR(500) NOT NULL DEFAULT '' COMMENT '图片地址',
    `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_comment_id` (`comment_id`)
) AUTO_INCREMENT = 10000 COMMENT = '评价图片';


CREATE TABLE `expo_comment_at` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `comment_id` varchar(50) NOT NULL DEFAULT '' COMMENT '评论ID',
    `user_id` varchar(20) NOT NULL DEFAULT '' NOT NULL,
    `nickname` varchar(100) NOT NULL DEFAULT '' NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `deleted_at` DATETIME COMMENT '删除时间',
    KEY `idx_comment_id` (`comment_id`)
)AUTO_INCREMENT = 10000 COMMENT = '评价@表';


CREATE TABLE `expo_comment_like` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `comment_id` varchar(50) NOT NULL DEFAULT '' COMMENT '评论ID',
    `user_id` varchar(20) NOT NULL DEFAULT '' NOT NULL,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    KEY `idx_comment_id` (`comment_id`)
)AUTO_INCREMENT = 10000 COMMENT = '评价点赞表';

Create Table expo_comment_trans (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `comment_id` varchar(50) NOT NULL DEFAULT '' COMMENT '评论ID',
    `language_code` varchar(10) NOT NULL DEFAULT '' COMMENT '语言',
    `content` text NOT NULL DEFAULT '' COMMENT '评论内容',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
    `trans_user` varchar(20) NOT NULL DEFAULT '' COMMENT '翻译用户',
    KEY `idx_comment_id` (`comment_id`)
    ) AUTO_INCREMENT = 10000 COMMENT = '翻译表';