-- 1. 人员库表 (face_groups)
CREATE TABLE `face_groups` (
    `id` VARCHAR(64) NOT NULL PRIMARY KEY COMMENT '人员库ID（对应腾讯云GroupId）',
    `name` VARCHAR(60) NOT NULL COMMENT '人员库名称',
    `description` VARCHAR(200) NOT NULL DEFAULT '' COMMENT '人员库描述',
    `tag` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '人员库标签（对应腾讯云Tag字段）',
    `face_model_version` VARCHAR(10) NOT NULL DEFAULT '3.0' COMMENT '算法模型版本',
    `max_faces` INT NOT NULL DEFAULT 50000 COMMENT '最大人脸数量',
    `estimated_face_count` INT NOT NULL DEFAULT 0 COMMENT '预估当前人脸数量',
    `creation_timestamp` BIGINT DEFAULT 0 COMMENT '腾讯云返回的创建时间戳',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1=正常 2=删除中 3=已删除',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) COMMENT = '人员库表-存储腾讯云人员库信息';

-- 2. 人员库场景关联表 (face_group_scenes)
CREATE TABLE `face_group_scenes` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `group_id` VARCHAR(64) NOT NULL COMMENT '人员库ID',
    `expo_id` VARCHAR(64) NOT NULL COMMENT '场景ID（如展会ID、活动ID）',
    `scene_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '场景名称',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1=正常 2=已删除',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_group_scene` (`group_id`, `expo_id`),
    INDEX `idx_scene` (`expo_id`),
    INDEX `idx_status` (`status`)
) AUTO_INCREMENT = 10000 COMMENT = '人员库场景关联表-关联人员库与业务场景';

-- 3. 人脸照片关联表 (face_photo_relations)
CREATE TABLE `face_photo_relations` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `face_business_id` VARCHAR(64) NOT NULL COMMENT '业务人脸ID（自定义UUID）',
    `tencent_person_id` VARCHAR(64) NOT NULL COMMENT '腾讯云PersonId（每个人脸对应一个独立Person）',
    `group_id` VARCHAR(64) NOT NULL COMMENT '人员库ID',
    `original_photo_url` TEXT NOT NULL COMMENT '原始照片URL',
    `face_rect_x` INT NOT NULL DEFAULT 0 COMMENT '人脸矩形左上角X坐标',
    `face_rect_y` INT NOT NULL DEFAULT 0 COMMENT '人脸矩形左上角Y坐标',
    `face_rect_width` INT NOT NULL DEFAULT 0 COMMENT '人脸矩形宽度',
    `face_rect_height` INT NOT NULL DEFAULT 0 COMMENT '人脸矩形高度',
    `quality_score` DECIMAL(5,2) DEFAULT 0.00 COMMENT '人脸质量分数（0-100）',
    `gender` TINYINT DEFAULT 0 COMMENT '性别：0=未知 1=男 2=女',
    `age` TINYINT DEFAULT 0 COMMENT '估算年龄',
    `beauty` DECIMAL(5,2) DEFAULT 0.00 COMMENT '颜值分数（0-100）',
    `tencent_face_id` VARCHAR(64) DEFAULT '' COMMENT '腾讯云返回的FaceId',
    `upload_user_id` VARCHAR(64) DEFAULT '' COMMENT '上传用户ID',
    `upload_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1=正常 2=已删除',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_face_business_id` (`face_business_id`),
    UNIQUE KEY `uk_tencent_person_id` (`tencent_person_id`),
    INDEX `idx_group_status` (`group_id`, `status`),
    INDEX `idx_upload_user_id` (`upload_user_id`),
    INDEX `idx_upload_time` (`upload_time`)
) AUTO_INCREMENT = 10000 COMMENT = '人脸照片关联表-存储人脸与照片的关联关系';
