CREATE TABLE `user_gift_card` (
                       `id` bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                       `setting_id` bigint NOT NULL  COMMENT '礼品卡配置Id',
                       `user_id` varchar(64) NOT NULL DEFAULT '' COMMENT '用户id',
                       `goods_id` varchar(64) NOT NULL DEFAULT '' COMMENT '产品Id',
                       `order_id` varchar(64)    COMMENT '订单号',
                       `is_pinkage` tinyint NOT NULL DEFAULT 0 COMMENT '是否包邮 0不包邮 1包邮',
                       `status` tinyint NOT NULL DEFAULT 0 COMMENT '商品状态：0：未使用；1：已使用',
                       `remark` varchar(64)    COMMENT '修改备注',
                       `show_notice` tinyint NOT NULL DEFAULT 0 COMMENT '提醒状态 0 未提醒 1已提醒',
                       `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
                       `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
                       `start_time` datetime NOT NULL  COMMENT '有效期开始时间',
                       `end_time` datetime NOT NULL  COMMENT '有效期结束时间',
                       `used_time` datetime  NULL  COMMENT '使用时间',
                       KEY `idx_user_id_status_start_end_created_at` (`user_id`,`status`,`start_time`,`end_time`,`created_at`),
                       KEY `idx_user_id_created_at` (`user_id`,`created_at`),
                       KEY `idx_setting_id`(`setting_id`)
) AUTO_INCREMENT=100000  COMMENT='用户兑换';

CREATE TABLE `gift_card_setting` (
                                 `id` bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                 `goods_id` varchar(64) NOT NULL DEFAULT '' COMMENT '产品Id',
                                 `is_pinkage` tinyint NOT NULL DEFAULT 0 COMMENT '是否包邮 0不包邮 1包邮',
                                 `in_date` tinyint NOT NULL DEFAULT 0 COMMENT '有效期类型 0固定 1相对',
                                 `in_date_days` int NOT NULL DEFAULT 0 COMMENT '有效期天数 相对时有值',
                                 `start_time` datetime  NULL  COMMENT '有效期开始时间',
                                 `end_time` datetime  NULL  COMMENT '有效期结束时间',
                                 `use_times` int NOT NULL DEFAULT 0 COMMENT '使用次数',
                                 `promotion_channel` varchar(100) NOT NULL DEFAULT '' COMMENT '推广渠道',
                                 `proposer` varchar(100) NOT NULL DEFAULT '' COMMENT '申请人',
                                 `created_user` varchar(200) NOT NULL DEFAULT '' COMMENT '申请人',
                                 `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '创建时间',
                                 `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '最后一次更新时间',
                                 `image` varchar(1000) NOT NULL DEFAULT '' COMMENT '领取弹框图片'
) AUTO_INCREMENT=100000  COMMENT='礼品卡配置表';

