package immessagecenter

import (
	"fmt"
	"strings"
	"time"
)

// CustomTime 自定义时间类型，用于解析不含时区的时间格式
type CustomTime time.Time

// UnmarshalJSON 自定义 JSON 解析方法
func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	// 移除引号
	str := strings.Trim(string(data), `"`)
	if str == "null" || str == "" {
		return nil
	}

	// 尝试解析不含时区的时间格式
	t, err := time.Parse("2006-01-02T15:04:05", str)
	if err != nil {
		return err
	}

	*ct = CustomTime(t)
	return nil
}

// MarshalJSON 自定义 JSON 序列化方法
func (ct CustomTime) MarshalJSON() ([]byte, error) {
	t := time.Time(ct)
	if t.<PERSON><PERSON>ero() {
		return []byte("null"), nil
	}
	return []byte(`"` + t.Format("2006-01-02T15:04:05") + `"`), nil
}

// Time 获取 time.Time 类型
func (ct CustomTime) Time() time.Time {
	return time.Time(ct)
}

// 实现常用的时间比较方法
func (ct CustomTime) After(u time.Time) bool {
	return time.Time(ct).After(u)
}

func (ct CustomTime) Before(u time.Time) bool {
	return time.Time(ct).Before(u)
}

func (ct CustomTime) Equal(u time.Time) bool {
	return time.Time(ct).Equal(u)
}

// 支持与 CustomTime 类型比较
func (ct CustomTime) AfterCustom(u CustomTime) bool {
	return time.Time(ct).After(time.Time(u))
}

func (ct CustomTime) BeforeCustom(u CustomTime) bool {
	return time.Time(ct).Before(time.Time(u))
}

func (ct CustomTime) EqualCustom(u CustomTime) bool {
	return time.Time(ct).Equal(time.Time(u))
}

// 实现其他常用的时间方法
func (ct CustomTime) IsZero() bool {
	return time.Time(ct).IsZero()
}

func (ct CustomTime) Format(layout string) string {
	return time.Time(ct).Format(layout)
}

type CheckResponse interface {
	Check() error
}

type CommonResponse struct {
	Code      int    `json:"code"`
	Succeed   bool   `json:"succeed"`
	Error     string `json:"error"`
	Message   string `json:"message"`
	Timestamp int64  `json:"timestamp"`
}

func (s CommonResponse) Check() error {
	if !s.Succeed {
		if s.Error != "" {
			return fmt.Errorf("%s", s.Error)
		}
		if s.Message != "" {
			return fmt.Errorf("%s", s.Message)
		}
		return fmt.Errorf("request failed with code: %d", s.Code)
	}
	return nil
}

// ExhibitionRecordListRequest 展会记录列表请求
type ExhibitionRecordListRequest struct {
	ExhibitionId string `json:"exhibitionId" form:"exhibitionId" binding:"required"` // 展会ID
}

// ExhibitionRecord 展会记录
type ExhibitionRecord struct {
	UserId   string     `json:"userId"`   // 用户ID
	TargetId string     `json:"targetId"` // 目标ID
	SendDate CustomTime `json:"sendDate"` // 发送时间
}

// ExhibitionRecordListReply 展会记录列表响应
type ExhibitionRecordListReply struct {
	CommonResponse
	Result []ExhibitionRecord `json:"result"` // 记录列表
}
