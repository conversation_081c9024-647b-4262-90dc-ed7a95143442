package immessagecenter

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-resty/resty/v2"
)

type Client struct {
	client  *resty.Client
	BaseURL string
}

func NewClient(baseURL string) (*Client, error) {
	if baseURL == "" {
		return nil, fmt.Errorf("baseURL is empty")
	}

	return &Client{
		client: resty.New().
			SetHeader("Connection", "keep-alive").
			SetTransport(&http.Transport{
				MaxIdleConnsPerHost: 10,               // 每个主机的最大空闲连接数
				IdleConnTimeout:     10 * time.Minute, // 空闲连接超时时间
			}).
			SetTimeout(time.Second * 5).
			OnBeforeRequest(BeforeRequest).
			OnAfterResponse(AfterResponse),
		BaseURL: baseURL,
	}, nil
}

func NewClientWithConfig(config *Config) (*Client, error) {
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}
	return NewClient(config.BaseURL)
}

func BeforeRequest(_ *resty.Client, request *resty.Request) error {
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Accept", "text/plain")
	return nil
}

func AfterResponse(_ *resty.Client, response *resty.Response) error {
	if response.StatusCode() != http.StatusOK {
		return fmt.Errorf("response code got %d expected 200", response.StatusCode())
	}

	ret := response.Result()
	if check, ok := ret.(CheckResponse); ok {
		return check.Check()
	}
	return nil
}

func headerFromContext(ctx context.Context) map[string]string {
	var (
		basicData, _    = icontext.BasicDataFrom(ctx)
		clientIP, _     = icontext.ClientIPFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		requestId, _    = icontext.RequestIdFrom(ctx)
		userId, _       = icontext.UserIdFrom(ctx)
	)
	return map[string]string{
		"BasicData":     basicData,
		"x-forword-for": clientIP,
		"CountryCode":   countryCode,
		"LanguageCode":  languageCode,
		"X-Request-Id":  requestId,
		"X-User-Id":     userId,
		"request-from":  "api-client",
	}
}
