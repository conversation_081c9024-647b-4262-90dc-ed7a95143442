package immessagecenter

import (
	"context"
	"fmt"
)

// GetExhibitionRecordList 获取展会记录列表
func (c *Client) GetExhibitionRecordList(ctx context.Context, req *ExhibitionRecordListRequest) (*ExhibitionRecordListReply, error) {
	var resp ExhibitionRecordListReply
	_, err := c.client.R().
		SetHeaders(headerFromContext(ctx)).
		SetResult(&resp).
		SetQueryParam("exhibitionId", req.ExhibitionId).
		Get(fmt.Sprintf("%s/immessagecenter/exhibition/recordlist", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return &resp, nil
}
