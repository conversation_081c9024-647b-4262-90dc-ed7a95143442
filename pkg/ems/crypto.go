package ems

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"fmt"

	"github.com/tjfoc/gmsm/sm4"
)

// SM4CryptoTool SM4加密工具
type SM4CryptoTool struct {
	key []byte
}

// NewSM4CryptoTool 创建SM4加密工具
func NewSM4CryptoTool(key string) (*SM4CryptoTool, error) {
	var keyBytes []byte
	var err error

	// 尝试base64解码
	if keyBytes, err = base64.StdEncoding.DecodeString(key); err != nil {
		// 如果base64解码失败，尝试hex解码
		if keyBytes, err = hex.DecodeString(key); err != nil {
			// 如果都失败，直接使用字符串字节
			keyBytes = []byte(key)
		}
	}

	// SM4密钥长度必须是16字节
	if len(keyBytes) != 16 {
		if len(keyBytes) < 16 {
			// 如果密钥长度不足16字节，用0补充
			padding := make([]byte, 16-len(keyBytes))
			keyBytes = append(keyBytes, padding...)
		} else {
			// 如果密钥长度超过16字节，截取前16字节
			keyBytes = keyBytes[:16]
		}
	}

	return &SM4CryptoTool{
		key: keyBytes,
	}, nil
}

// Encrypt SM4加密
func (s *SM4CryptoTool) Encrypt(plaintext string) (string, error) {
	// SM4 ECB模式加密
	ciphertext, err := sm4.Sm4Ecb(s.key, []byte(plaintext), true)
	if err != nil {
		return "", fmt.Errorf("SM4加密失败: %w", err)
	}

	// base64编码并加上前缀
	encoded := base64.StdEncoding.EncodeToString(ciphertext)
	return "|$4|" + encoded, nil
}

// Decrypt SM4解密
func (s *SM4CryptoTool) Decrypt(ciphertext string) (string, error) {
	// 移除前缀
	if len(ciphertext) < 4 || ciphertext[:4] != "|$4|" {
		return "", fmt.Errorf("密文格式错误，缺少|$4|前缀")
	}

	encoded := ciphertext[4:]

	// base64解码
	encrypted, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %w", err)
	}

	// SM4 ECB模式解密
	plaintext, err := sm4.Sm4Ecb(s.key, encrypted, false)
	if err != nil {
		return "", fmt.Errorf("SM4解密失败: %w", err)
	}

	return string(plaintext), nil
}

// GenerateKey 生成随机SM4密钥
func GenerateKey() (string, error) {
	key := make([]byte, 16)
	_, err := rand.Read(key)
	if err != nil {
		return "", fmt.Errorf("生成密钥失败: %w", err)
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

// SignData 对数据进行签名（SM4加密）
// 按照EMS官方文档要求：业务报文 + 密钥(base64字符串)
func (s *SM4CryptoTool) SignData(data, keyString string) (string, error) {
	// 将数据与密钥字符串拼接（密钥直接使用传入的base64字符串）
	signData := data + keyString

	// SM4加密
	return s.Encrypt(signData)
}

// VerifySign 验证签名
func (s *SM4CryptoTool) VerifySign(data, signature, keyString string) (bool, error) {
	// 解密签名
	decrypted, err := s.Decrypt(signature)
	if err != nil {
		return false, err
	}

	// 检查是否以数据开头并以密钥字符串结尾
	expectedData := data + keyString

	return decrypted == expectedData, nil
}
