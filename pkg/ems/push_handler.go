package ems

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// PushHandler 推送通知处理器
type PushHandler struct {
	client   *Client
	handlers map[string]PushEventHandler
}

// PushEventHandler 推送事件处理器接口
type PushEventHandler interface {
	Handle(ctx context.Context, data *PushBusinessData) error
}

// PushEventHandlerFunc 推送事件处理器函数类型
type PushEventHandlerFunc func(ctx context.Context, data *PushBusinessData) error

// Handle 实现PushEventHandler接口
func (f PushEventHandlerFunc) Handle(ctx context.Context, data *PushBusinessData) error {
	return f(ctx, data)
}

// NewPushHandler 创建推送通知处理器
func NewPushHandler(client *Client) *PushHandler {
	return &PushHandler{
		client:   client,
		handlers: make(map[string]PushEventHandler),
	}
}

// RegisterHandler 注册推送事件处理器
func (h *PushHandler) RegisterHandler(status string, handler PushEventHandler) {
	h.handlers[status] = handler
}

// RegisterHandlerFunc 注册推送事件处理器函数
func (h *PushHandler) RegisterHandlerFunc(status string, handlerFunc PushEventHandlerFunc) {
	h.handlers[status] = handlerFunc
}

// HandlePushNotification 处理推送通知
func (h *PushHandler) HandlePushNotification(ctx context.Context, pushData *PushNotificationData) (*PushNotificationResponse, error) {
	// 验证推送数据
	if err := h.client.verifyPushSignature(pushData); err != nil {
		return h.buildErrorResponse(pushData.SerialNo, "S00001", fmt.Sprintf("签名验证失败: %v", err)), nil
	}

	// 解密推送数据
	decryptedData, err := h.client.decryptPushData(pushData.LogitcsInterface)
	if err != nil {
		return h.buildErrorResponse(pushData.SerialNo, "S00001", fmt.Sprintf("解密失败: %v", err)), nil
	}

	// 解析业务数据
	var businessData PushBusinessData
	if err := json.Unmarshal([]byte(decryptedData), &businessData); err != nil {
		return h.buildErrorResponse(pushData.SerialNo, "S00001", fmt.Sprintf("解析业务数据失败: %v", err)), nil
	}

	// 记录推送日志
	log.Printf("收到EMS推送通知: 运单号=%s, 跟踪号=%s, 状态=%s", 
		businessData.WaybillNo, businessData.TrackingNo, businessData.Status)

	// 查找并执行对应的处理器
	if handler, exists := h.handlers[businessData.Status]; exists {
		if err := handler.Handle(ctx, &businessData); err != nil {
			log.Printf("处理推送通知失败: %v", err)
			return h.buildErrorResponse(pushData.SerialNo, "S00001", fmt.Sprintf("处理失败: %v", err)), nil
		}
	} else {
		// 如果没有找到特定状态的处理器，使用默认处理器
		if defaultHandler, exists := h.handlers["default"]; exists {
			if err := defaultHandler.Handle(ctx, &businessData); err != nil {
				log.Printf("默认处理器处理推送通知失败: %v", err)
				return h.buildErrorResponse(pushData.SerialNo, "S00001", fmt.Sprintf("默认处理失败: %v", err)), nil
			}
		} else {
			log.Printf("未找到状态 %s 的处理器，且无默认处理器", businessData.Status)
		}
	}

	// 返回成功响应
	return &PushNotificationResponse{
		SerialNo:    pushData.SerialNo,
		Code:        "00000",
		CodeMessage: "推送处理成功",
		SenderNo:    h.client.config.SenderNo,
	}, nil
}

// buildErrorResponse 构建错误响应
func (h *PushHandler) buildErrorResponse(serialNo, code, message string) *PushNotificationResponse {
	return &PushNotificationResponse{
		SerialNo:    serialNo,
		Code:        code,
		CodeMessage: message,
		SenderNo:    h.client.config.SenderNo,
	}
}

// DefaultTrackingHandler 默认的物流轨迹处理器
type DefaultTrackingHandler struct{}

// Handle 处理物流轨迹更新
func (h *DefaultTrackingHandler) Handle(ctx context.Context, data *PushBusinessData) error {
	log.Printf("默认处理器: 运单号=%s, 跟踪号=%s, 状态=%s, 描述=%s, 更新时间=%s",
		data.WaybillNo, data.TrackingNo, data.Status, data.StatusDesc, data.UpdateTime)
	
	// 这里可以添加默认的处理逻辑，比如：
	// 1. 更新数据库中的物流状态
	// 2. 发送通知给用户
	// 3. 触发其他业务流程
	
	return nil
}

// PushNotificationServer 推送通知服务器
type PushNotificationServer struct {
	handler *PushHandler
}

// NewPushNotificationServer 创建推送通知服务器
func NewPushNotificationServer(client *Client) *PushNotificationServer {
	handler := NewPushHandler(client)
	
	// 注册默认处理器
	handler.RegisterHandler("default", &DefaultTrackingHandler{})
	
	return &PushNotificationServer{
		handler: handler,
	}
}

// RegisterStatusHandler 注册状态处理器
func (s *PushNotificationServer) RegisterStatusHandler(status string, handler PushEventHandler) {
	s.handler.RegisterHandler(status, handler)
}

// RegisterStatusHandlerFunc 注册状态处理器函数
func (s *PushNotificationServer) RegisterStatusHandlerFunc(status string, handlerFunc PushEventHandlerFunc) {
	s.handler.RegisterHandlerFunc(status, handlerFunc)
}

// HandlePush 处理推送请求
func (s *PushNotificationServer) HandlePush(ctx context.Context, pushData *PushNotificationData) (*PushNotificationResponse, error) {
	return s.handler.HandlePushNotification(ctx, pushData)
}

// 常见的物流状态常量
const (
	StatusAccepted    = "ACCEPTED"    // 已收寄
	StatusInTransit   = "IN_TRANSIT"  // 运输中
	StatusOutDelivery = "OUT_DELIVERY" // 派送中
	StatusDelivered   = "DELIVERED"   // 已签收
	StatusException   = "EXCEPTION"   // 异常
	StatusReturned    = "RETURNED"    // 退回
)

// ExamplePushHandlerUsage 推送处理器使用示例
func ExamplePushHandlerUsage() {
	// 创建客户端
	config, _ := NewSandboxConfig("test_sender", "test_auth", "test_key")
	client, _ := NewClient(config)
	
	// 创建推送通知服务器
	server := NewPushNotificationServer(client)
	
	// 注册不同状态的处理器
	server.RegisterStatusHandlerFunc(StatusDelivered, func(ctx context.Context, data *PushBusinessData) error {
		log.Printf("包裹已签收: %s", data.TrackingNo)
		// 这里可以发送签收通知给用户
		return nil
	})
	
	server.RegisterStatusHandlerFunc(StatusException, func(ctx context.Context, data *PushBusinessData) error {
		log.Printf("包裹异常: %s, 原因: %s", data.TrackingNo, data.StatusDesc)
		// 这里可以发送异常通知给用户
		return nil
	})
	
	// 模拟处理推送通知
	ctx := context.Background()
	pushData := &PushNotificationData{
		Method:           ApiCodePushNotify,
		SenderNo:         "test_sender",
		MsgType:          "0",
		TimeStamp:        time.Now().Format("2006-01-02 15:04:05"),
		Version:          "V1.0.0",
		LogitcsInterface: "|$4|encrypted_data_here",
		SerialNo:         "test_serial_123",
	}
	
	response, err := server.HandlePush(ctx, pushData)
	if err != nil {
		log.Printf("处理推送失败: %v", err)
		return
	}
	
	log.Printf("推送处理结果: %+v", response)
}
