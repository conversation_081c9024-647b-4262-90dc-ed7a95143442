package ems

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-resty/resty/v2"
)

const (
	// 环境配置
	ProductionURL = "https://api.ems.com.cn/amp-prod-api/f/amp/api/open"
	SandboxURL    = "https://api.ems.com.cn/amp-prod-api/f/amp/api/test"

	// 默认配置
	DefaultVersion = "V1.0.0"
	DefaultMsgType = "0" // 0-json, 1-xml

	// API接口代码 - 根据EMS官方文档
	ApiCodeQueryTrack    = "040001" // 查询物流轨迹
	ApiCodePushNotify    = "040002" // 推送通知
	ApiCodeCreateWaybill = "010001" // 创建运单
	ApiCodeQueryWaybill  = "020001" // 查询运单
	ApiCodeQueryFee      = "030001" // 查询费用
	ApiCodeCancelWaybill = "050001" // 取消运单
)

// Config EMS客户端配置
type Config struct {
	BaseURL       string `json:"base_url"`      // API基础URL
	SenderNo      string `json:"sender_no"`     // 客户代码
	Authorization string `json:"authorization"` // 授权码
	SecretKey     string `json:"secret_key"`    // 加密密钥
	UserCode      string `json:"user_code"`     // 用户编码
	Version       string `json:"version"`       // 版本号
	MsgType       string `json:"msg_type"`      // 消息类型
	Timeout       int    `json:"timeout"`       // 超时时间(秒)
}

// Client EMS客户端
type Client struct {
	config    *Config
	client    *resty.Client
	crypto    *SM4CryptoTool
	secretKey string // 保存原始密钥字符串，用于签名
}

// CheckResponse 响应检查接口
type CheckResponse interface {
	Check() error
}

// NewClient 创建EMS客户端
func NewClient(config *Config) (*Client, error) {
	if config == nil {
		return nil, fmt.Errorf("config不能为空")
	}

	if config.BaseURL == "" {
		return nil, fmt.Errorf("base_url不能为空")
	}

	if config.SenderNo == "" {
		return nil, fmt.Errorf("sender_no不能为空")
	}

	if config.Authorization == "" {
		return nil, fmt.Errorf("authorization不能为空")
	}

	if config.SecretKey == "" {
		return nil, fmt.Errorf("secret_key不能为空")
	}

	// 设置默认值
	if config.Version == "" {
		config.Version = DefaultVersion
	}
	if config.MsgType == "" {
		config.MsgType = DefaultMsgType
	}
	if config.Timeout == 0 {
		config.Timeout = 30
	}

	// 创建SM4加密工具
	crypto, err := NewSM4CryptoTool(config.SecretKey)
	if err != nil {
		return nil, fmt.Errorf("创建SM4加密工具失败: %w", err)
	}

	// 创建HTTP客户端
	client := resty.New().
		SetHeader("Connection", "keep-alive").
		SetHeader("Content-Type", "application/json").
		SetHeader("Accept", "application/json").
		SetTransport(&http.Transport{
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     10 * time.Minute,
		}).
		SetTimeout(time.Duration(config.Timeout) * time.Second).
		OnBeforeRequest(beforeRequest).
		OnAfterResponse(afterResponse)

	return &Client{
		config:    config,
		client:    client,
		crypto:    crypto,
		secretKey: config.SecretKey, // 保存原始密钥字符串
	}, nil
}

// beforeRequest 请求前置处理
func beforeRequest(_ *resty.Client, request *resty.Request) error {
	// 调试: 打印请求信息
	fmt.Printf("🔍 DEBUG - Request URL: %s\n", request.URL)
	fmt.Printf("🔍 DEBUG - Request Method: %s\n", request.Method)

	// 尝试序列化请求体为JSON查看格式
	if request.Body != nil {
		if jsonBytes, err := json.Marshal(request.Body); err == nil {
			fmt.Printf("🔍 DEBUG - Request Body JSON: %s\n", string(jsonBytes))
		}
	}

	// 可以在这里添加请求日志
	return nil
}

// afterResponse 响应后置处理
func afterResponse(_ *resty.Client, response *resty.Response) error {
	// 调试: 打印原始响应
	fmt.Printf("🔍 DEBUG - HTTP Status: %d\n", response.StatusCode())
	fmt.Printf("🔍 DEBUG - Response Body: %s\n", string(response.Body()))

	// 可以在这里添加响应日志
	if response.StatusCode() != http.StatusOK {
		return fmt.Errorf("HTTP状态码错误: %d", response.StatusCode())
	}

	ret := response.Result()
	if check, ok := ret.(CheckResponse); ok {
		return check.Check()
	}

	return nil
}

// buildRequest 构建请求
func (c *Client) buildRequest(apiCode string, logitcsInterface interface{}) (*CommonRequest, error) {
	// 将业务数据序列化为JSON
	jsonData, err := json.Marshal(logitcsInterface)
	if err != nil {
		return nil, fmt.Errorf("序列化业务数据失败: %w", err)
	}

	// 对业务数据进行签名
	signature, err := c.crypto.SignData(string(jsonData), c.secretKey)
	if err != nil {
		return nil, fmt.Errorf("签名失败: %w", err)
	}

	return &CommonRequest{
		ApiCode:          apiCode,
		SenderNo:         c.config.SenderNo,
		Authorization:    c.config.Authorization,
		MsgType:          c.config.MsgType,
		TimeStamp:        time.Now().Format("2006-01-02 15:04:05"),
		Version:          c.config.Version,
		LogitcsInterface: signature,
		UserCode:         c.config.UserCode,
	}, nil
}

// sendRequest 发送请求
func (c *Client) sendRequest(_ context.Context, req *CommonRequest, response interface{}) error {
	// 根据EMS官方文档，需要使用form-urlencoded格式
	formData := map[string]string{
		"apiCode":          req.ApiCode,
		"senderNo":         req.SenderNo,
		"authorization":    req.Authorization,
		"msgType":          req.MsgType,
		"timeStamp":        req.TimeStamp,
		"version":          req.Version,
		"logitcsInterface": req.LogitcsInterface.(string),
	}

	// 如果有userCode，添加到表单数据中
	if req.UserCode != "" {
		formData["userCode"] = req.UserCode
	}

	resp, err := c.client.R().
		SetHeader("Content-Type", "application/x-www-form-urlencoded").
		SetFormData(formData).
		SetResult(response).
		Post(c.config.BaseURL)

	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}

	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode(), resp.String())
	}

	return nil
}

// headerFromContext 从上下文中获取请求头
func (c *Client) headerFromContext(ctx context.Context) map[string]string {
	var (
		basicData, _    = icontext.BasicDataFrom(ctx)
		clientIP, _     = icontext.ClientIPFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		requestId, _    = icontext.RequestIdFrom(ctx)
	)
	return map[string]string{
		"BasicData":       basicData,
		"X-Forwarded-For": clientIP,
		"CountryCode":     countryCode,
		"LanguageCode":    languageCode,
		"X-Request-Id":    requestId,
		"User-Agent":      "EMS-Go-Client/1.0",
	}
}

// QueryWaybill 查询运单
func (c *Client) QueryWaybill(ctx context.Context, req *QueryWaybillRequest) ([]QueryWaybillResponse, error) {
	// 构建请求
	commonReq, err := c.buildRequest(ApiCodeQueryWaybill, req)
	if err != nil {
		return nil, err
	}

	// 发送请求，使用动态响应处理
	var response DynamicResponse
	if err := c.sendRequest(ctx, commonReq, &response); err != nil {
		return nil, err
	}

	if err := response.Check(); err != nil {
		return nil, err
	}

	// 解析RetBody
	var result []QueryWaybillResponse
	if err := response.ParseRetBody(&result); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}

	return result, nil
}

// QueryTrack 查询物流轨迹
func (c *Client) QueryTrack(ctx context.Context, req *QueryTrackRequest) ([]QueryTrackResponse, error) {
	// 构建请求
	commonReq, err := c.buildRequest(ApiCodeQueryTrack, req)
	if err != nil {
		return nil, err
	}

	// 发送请求，使用动态响应处理
	var response DynamicResponse
	if err := c.sendRequest(ctx, commonReq, &response); err != nil {
		return nil, err
	}

	if err := response.Check(); err != nil {
		return nil, err
	}

	// 解析RetBody
	var result []QueryTrackResponse
	if err := response.ParseRetBody(&result); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}

	return result, nil
}

// CreateWaybill 创建运单
func (c *Client) CreateWaybill(ctx context.Context, req *CreateWaybillRequest) (*CreateWaybillResponse, error) {
	// 构建请求
	commonReq, err := c.buildRequest(ApiCodeCreateWaybill, req)
	if err != nil {
		return nil, err
	}

	// 发送请求，使用动态响应处理
	var response DynamicResponse
	if err := c.sendRequest(ctx, commonReq, &response); err != nil {
		return nil, err
	}

	if err := response.Check(); err != nil {
		return nil, err
	}

	// 解析RetBody
	var result CreateWaybillResponse
	if err := response.ParseRetBody(&result); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}

	return &result, nil
}

// QueryFee 查询费用
func (c *Client) QueryFee(ctx context.Context, req *QueryFeeRequest) (*QueryFeeResponse, error) {
	// 构建请求
	commonReq, err := c.buildRequest(ApiCodeQueryFee, req)
	if err != nil {
		return nil, err
	}

	// 发送请求，使用动态响应处理
	var response DynamicResponse
	if err := c.sendRequest(ctx, commonReq, &response); err != nil {
		return nil, err
	}

	if err := response.Check(); err != nil {
		return nil, err
	}

	// 解析RetBody
	var result QueryFeeResponse
	if err := response.ParseRetBody(&result); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}

	return &result, nil
}

// CancelWaybill 取消运单
func (c *Client) CancelWaybill(ctx context.Context, req *CancelWaybillRequest) ([]CancelWaybillResponse, error) {
	// 构建请求
	commonReq, err := c.buildRequest(ApiCodeCancelWaybill, req)
	if err != nil {
		return nil, err
	}

	// 发送请求，使用动态响应处理
	var response DynamicResponse
	if err := c.sendRequest(ctx, commonReq, &response); err != nil {
		return nil, err
	}

	if err := response.Check(); err != nil {
		return nil, err
	}

	// 解析RetBody
	var result []CancelWaybillResponse
	if err := response.ParseRetBody(&result); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}

	return result, nil
}

// HandlePushNotification 处理推送通知 - 用于接收EMS推送的物流状态更新
func (c *Client) HandlePushNotification(ctx context.Context, pushData *PushNotificationData) (*PushNotificationResponse, error) {
	// 验证推送数据的签名
	if err := c.verifyPushSignature(pushData); err != nil {
		return nil, fmt.Errorf("推送数据签名验证失败: %w", err)
	}

	// 解密推送数据
	decryptedData, err := c.decryptPushData(pushData.LogitcsInterface)
	if err != nil {
		return nil, fmt.Errorf("解密推送数据失败: %w", err)
	}

	// 解析业务数据
	var businessData PushBusinessData
	if err := json.Unmarshal([]byte(decryptedData), &businessData); err != nil {
		return nil, fmt.Errorf("解析推送业务数据失败: %w", err)
	}

	// 构建响应
	response := &PushNotificationResponse{
		SerialNo:    pushData.SerialNo,
		Code:        "00000", // 成功
		CodeMessage: "推送处理成功",
		SenderNo:    c.config.SenderNo,
	}

	return response, nil
}

// TestAPICode 测试指定的API代码 - 仅用于调试和测试
func (c *Client) TestAPICode(ctx context.Context, apiCode string, businessData interface{}) (*DynamicResponse, error) {
	// 构建请求
	commonReq, err := c.buildRequest(apiCode, businessData)
	if err != nil {
		return nil, err
	}

	// 发送请求，使用动态响应处理
	var response DynamicResponse
	if err := c.sendRequest(ctx, commonReq, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// verifyPushSignature 验证推送数据签名
func (c *Client) verifyPushSignature(pushData *PushNotificationData) error {
	// 验证必要字段是否存在
	if pushData.Method != ApiCodePushNotify {
		return fmt.Errorf("推送方法代码错误，期望: %s, 实际: %s", ApiCodePushNotify, pushData.Method)
	}

	if pushData.SenderNo != c.config.SenderNo {
		return fmt.Errorf("客户代码不匹配，期望: %s, 实际: %s", c.config.SenderNo, pushData.SenderNo)
	}

	// 这里可以根据实际需要添加更复杂的签名验证逻辑
	// 构建验证数据：method + senderNo + timeStamp + version + logitcsInterface
	// verifyData := pushData.Method + pushData.SenderNo + pushData.TimeStamp + pushData.Version + pushData.LogitcsInterface

	return nil
}

// decryptPushData 解密推送数据
func (c *Client) decryptPushData(encryptedData string) (string, error) {
	if encryptedData == "" {
		return "", fmt.Errorf("推送数据为空")
	}

	// 使用SM4解密
	decrypted, err := c.crypto.Decrypt(encryptedData)
	if err != nil {
		return "", fmt.Errorf("解密失败: %w", err)
	}

	// 解密后的数据格式是：业务数据 + 密钥字符串
	// 需要移除末尾的密钥字符串
	keyString := c.config.SecretKey
	if len(decrypted) > len(keyString) && decrypted[len(decrypted)-len(keyString):] == keyString {
		// 移除末尾的密钥字符串
		businessData := decrypted[:len(decrypted)-len(keyString)]
		return businessData, nil
	}

	// 如果没有找到密钥字符串，直接返回解密数据
	return decrypted, nil
}
