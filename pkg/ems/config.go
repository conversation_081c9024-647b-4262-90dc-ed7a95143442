package ems

import (
	"fmt"
	"time"
)

// Environment 环境类型
type Environment string

const (
	// EnvironmentProduction 生产环境
	EnvironmentProduction Environment = "production"
	// EnvironmentSandbox 沙箱环境
	EnvironmentSandbox Environment = "sandbox"
)

// DefaultConfig 默认配置
type DefaultConfig struct {
	// 生产环境配置
	Production struct {
		BaseURL string
	}
	// 沙箱环境配置
	Sandbox struct {
		BaseURL string
	}
	// 默认值
	Defaults struct {
		Version string
		MsgType string
		Timeout time.Duration
	}
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *DefaultConfig {
	return &DefaultConfig{
		Production: struct{ BaseURL string }{
			BaseURL: ProductionURL,
		},
		Sandbox: struct{ BaseURL string }{
			BaseURL: SandboxURL,
		},
		Defaults: struct {
			Version string
			MsgType string
			Timeout time.Duration
		}{
			Version: DefaultVersion,
			MsgType: DefaultMsgType,
			Timeout: 30 * time.Second,
		},
	}
}

// ConfigBuilder 配置构建器
type ConfigBuilder struct {
	config *Config
}

// NewConfigBuilder 创建配置构建器
func NewConfigBuilder() *ConfigBuilder {
	defaultConfig := GetDefaultConfig()
	return &ConfigBuilder{
		config: &Config{
			Version: defaultConfig.Defaults.Version,
			MsgType: defaultConfig.Defaults.MsgType,
			Timeout: int(defaultConfig.Defaults.Timeout.Seconds()),
		},
	}
}

// Environment 设置环境
func (b *ConfigBuilder) Environment(env Environment) *ConfigBuilder {
	defaultConfig := GetDefaultConfig()
	switch env {
	case EnvironmentProduction:
		b.config.BaseURL = defaultConfig.Production.BaseURL
	case EnvironmentSandbox:
		b.config.BaseURL = defaultConfig.Sandbox.BaseURL
	default:
		// 默认使用沙箱环境
		b.config.BaseURL = defaultConfig.Sandbox.BaseURL
	}
	return b
}

// SenderNo 设置客户代码
func (b *ConfigBuilder) SenderNo(senderNo string) *ConfigBuilder {
	b.config.SenderNo = senderNo
	return b
}

// Authorization 设置授权码
func (b *ConfigBuilder) Authorization(authorization string) *ConfigBuilder {
	b.config.Authorization = authorization
	return b
}

// SecretKey 设置加密密钥
func (b *ConfigBuilder) SecretKey(secretKey string) *ConfigBuilder {
	b.config.SecretKey = secretKey
	return b
}

// UserCode 设置用户编码
func (b *ConfigBuilder) UserCode(userCode string) *ConfigBuilder {
	b.config.UserCode = userCode
	return b
}

// Version 设置版本号
func (b *ConfigBuilder) Version(version string) *ConfigBuilder {
	b.config.Version = version
	return b
}

// MsgType 设置消息类型
func (b *ConfigBuilder) MsgType(msgType string) *ConfigBuilder {
	b.config.MsgType = msgType
	return b
}

// Timeout 设置超时时间（秒）
func (b *ConfigBuilder) Timeout(timeout int) *ConfigBuilder {
	b.config.Timeout = timeout
	return b
}

// Build 构建配置
func (b *ConfigBuilder) Build() (*Config, error) {
	// 验证必要字段
	if b.config.BaseURL == "" {
		return nil, fmt.Errorf("base_url不能为空")
	}
	if b.config.SenderNo == "" {
		return nil, fmt.Errorf("sender_no不能为空")
	}
	if b.config.Authorization == "" {
		return nil, fmt.Errorf("authorization不能为空")
	}
	if b.config.SecretKey == "" {
		return nil, fmt.Errorf("secret_key不能为空")
	}

	// 返回配置副本
	return &Config{
		BaseURL:       b.config.BaseURL,
		SenderNo:      b.config.SenderNo,
		Authorization: b.config.Authorization,
		SecretKey:     b.config.SecretKey,
		UserCode:      b.config.UserCode,
		Version:       b.config.Version,
		MsgType:       b.config.MsgType,
		Timeout:       b.config.Timeout,
	}, nil
}

// NewProductionConfig 创建生产环境配置
func NewProductionConfig(senderNo, authorization, secretKey string) (*Config, error) {
	return NewConfigBuilder().
		Environment(EnvironmentProduction).
		SenderNo(senderNo).
		Authorization(authorization).
		SecretKey(secretKey).
		Build()
}

// NewSandboxConfig 创建沙箱环境配置
func NewSandboxConfig(senderNo, authorization, secretKey string) (*Config, error) {
	return NewConfigBuilder().
		Environment(EnvironmentSandbox).
		SenderNo(senderNo).
		Authorization(authorization).
		SecretKey(secretKey).
		Build()
}

// NewProductionConfigWithUserCode 创建带用户编码的生产环境配置
func NewProductionConfigWithUserCode(senderNo, authorization, secretKey, userCode string) (*Config, error) {
	return NewConfigBuilder().
		Environment(EnvironmentProduction).
		SenderNo(senderNo).
		Authorization(authorization).
		SecretKey(secretKey).
		UserCode(userCode).
		Build()
}

// NewSandboxConfigWithUserCode 创建带用户编码的沙箱环境配置
func NewSandboxConfigWithUserCode(senderNo, authorization, secretKey, userCode string) (*Config, error) {
	return NewConfigBuilder().
		Environment(EnvironmentSandbox).
		SenderNo(senderNo).
		Authorization(authorization).
		SecretKey(secretKey).
		UserCode(userCode).
		Build()
}

// ValidateConfig 验证配置
func ValidateConfig(config *Config) error {
	if config == nil {
		return fmt.Errorf("config不能为空")
	}
	if config.BaseURL == "" {
		return fmt.Errorf("base_url不能为空")
	}
	if config.SenderNo == "" {
		return fmt.Errorf("sender_no不能为空")
	}
	if config.Authorization == "" {
		return fmt.Errorf("authorization不能为空")
	}
	if config.SecretKey == "" {
		return fmt.Errorf("secret_key不能为空")
	}
	return nil
}

// EMSConfigData 外部EMS配置数据结构
type EMSConfigData struct {
	Environment   string `json:"environment"`   // production | sandbox
	SenderNo      string `json:"sender_no"`     // 客户代码
	Authorization string `json:"authorization"` // 授权码
	SecretKey     string `json:"secret_key"`    // SM4加密密钥
	UserCode      string `json:"user_code"`     // 用户编码
	Timeout       int    `json:"timeout"`       // 超时时间
}

// NewClientFromConfig 从外部配置创建EMS客户端
func NewClientFromConfig(configData *EMSConfigData) (*Client, error) {
	if configData == nil {
		return nil, fmt.Errorf("配置数据不能为空")
	}

	// 验证必要字段
	if configData.SenderNo == "" {
		return nil, fmt.Errorf("sender_no不能为空")
	}
	if configData.Authorization == "" {
		return nil, fmt.Errorf("authorization不能为空")
	}
	if configData.SecretKey == "" {
		return nil, fmt.Errorf("secret_key不能为空")
	}

	// 设置默认值
	timeout := configData.Timeout
	if timeout == 0 {
		timeout = 30 // 默认30秒超时
	}

	// 根据环境创建配置
	var config *Config
	var err error

	switch configData.Environment {
	case "production":
		config, err = NewProductionConfig(
			configData.SenderNo,
			configData.Authorization,
			configData.SecretKey,
		)
	case "sandbox", "":
		// 默认使用沙箱环境
		if configData.UserCode != "" {
			config, err = NewSandboxConfigWithUserCode(
				configData.SenderNo,
				configData.Authorization,
				configData.SecretKey,
				configData.UserCode,
			)
		} else {
			config, err = NewSandboxConfig(
				configData.SenderNo,
				configData.Authorization,
				configData.SecretKey,
			)
		}
	default:
		return nil, fmt.Errorf("不支持的环境类型: %s", configData.Environment)
	}

	if err != nil {
		return nil, fmt.Errorf("创建EMS配置失败: %v", err)
	}

	// 设置超时时间
	config.Timeout = timeout

	// 创建客户端
	return NewClient(config)
}
