package ems

import (
	"encoding/json"
	"time"
)

// EMSTime 自定义时间类型，用于处理EMS可能返回空字符串的时间字段
type EMSTime struct {
	time.Time
}

// UnmarshalJSON 自定义JSON解析，处理空字符串时间
func (t *EMSTime) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	// 如果是空字符串，使用零值
	if s == "" {
		t.Time = time.Time{}
		return nil
	}

	// 尝试多种时间格式
	formats := []string{
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02T15:04:05Z",
	}

	for _, format := range formats {
		if parsed, err := time.Parse(format, s); err == nil {
			t.Time = parsed
			return nil
		}
	}

	// 如果都解析失败，使用零值
	t.Time = time.Time{}
	return nil
}

// 公共请求参数
type CommonRequest struct {
	ApiCode          string      `json:"apiCode"`            // 接口代码
	SenderNo         string      `json:"senderNo"`           // 客户代码
	Authorization    string      `json:"authorization"`      // 授权码（区分测试和生产）
	MsgType          string      `json:"msgType,omitempty"`  // 0 - json、1 - xml，默认为json
	TimeStamp        string      `json:"timeStamp"`          // 请求时间，格式为yyyy-MM-dd HH:mm:ss
	Version          string      `json:"version,omitempty"`  // 版本号，默认V1.0.0
	LogitcsInterface interface{} `json:"logitcsInterface"`   // 请求消息体
	UserCode         string      `json:"userCode,omitempty"` // 用户编码
}

// 公共返回参数
type CommonResponse struct {
	SerialNo string      `json:"serialNo"` // 每次请求的唯一编码，用于查询日志使用
	RetCode  string      `json:"retCode"`  // 00000表示成功；S00001表示系统级错误；B00001表示接口级错误
	RetMsg   string      `json:"retMsg"`   // 错误信息
	RetBody  interface{} `json:"retBody"`  // 各接口返回的具体业务参数报文
	RetDate  EMSTime     `json:"retDate"`  // 各接口返回的时间
}

// CheckResponse 用于检查响应结果
func (r *CommonResponse) Check() error {
	if r.RetCode != "00000" {
		return &EMSError{
			Code:    r.RetCode,
			Message: r.RetMsg,
		}
	}
	return nil
}

// EMSError EMS API错误类型
type EMSError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

func (e *EMSError) Error() string {
	return e.Message
}

// 公共推送参数
type CommonPushRequest struct {
	Method           string      `json:"method"`                     // 接口代码
	SenderNo         string      `json:"senderNo"`                   // 客户代码
	MsgType          string      `json:"msgType"`                    // 0 - json、1 - xml，默认为json
	TimeStamp        string      `json:"timeStamp,omitempty"`        // 请求时间，格式为yyyy-MM-dd HH:mm:ss
	Version          string      `json:"version"`                    // 版本号，默认V1.0.0
	LogitcsInterface interface{} `json:"logitcsInterface,omitempty"` // 推送消息体
}

// 公共推送返回参数
type CommonPushResponse struct {
	SerialNo    string `json:"serialNo"`    // 每次请求的唯一编码，用于查询日志使用
	Code        string `json:"code"`        // 00000表示成功；S00001表示推送失败
	CodeMessage string `json:"codeMessage"` // 具体错误原因
	SenderNo    string `json:"senderNo"`    // 客户代码
}

// 寄件人信息
type SenderInfo struct {
	Name        string `json:"name"`        // 寄件人姓名
	Company     string `json:"company"`     // 寄件人公司
	Phone       string `json:"phone"`       // 寄件人电话
	Mobile      string `json:"mobile"`      // 寄件人手机
	Province    string `json:"province"`    // 寄件人省份
	City        string `json:"city"`        // 寄件人城市
	County      string `json:"county"`      // 寄件人区县
	Address     string `json:"address"`     // 寄件人详细地址
	PostCode    string `json:"postCode"`    // 寄件人邮编
	CountryCode string `json:"countryCode"` // 寄件人国家代码
}

// 收件人信息
type ReceiverInfo struct {
	Name        string `json:"name"`        // 收件人姓名
	Company     string `json:"company"`     // 收件人公司
	Phone       string `json:"phone"`       // 收件人电话
	Mobile      string `json:"mobile"`      // 收件人手机
	Province    string `json:"province"`    // 收件人省份
	City        string `json:"city"`        // 收件人城市
	County      string `json:"county"`      // 收件人区县
	Address     string `json:"address"`     // 收件人详细地址
	PostCode    string `json:"postCode"`    // 收件人邮编
	CountryCode string `json:"countryCode"` // 收件人国家代码
}

// 货物信息
type CargoInfo struct {
	Name        string `json:"name"`        // 货物名称
	Quantity    int    `json:"quantity"`    // 数量
	Weight      int    `json:"weight"`      // 重量(克)
	Value       int    `json:"value"`       // 价值(分)
	Currency    string `json:"currency"`    // 币种
	Origin      string `json:"origin"`      // 原产地
	Description string `json:"description"` // 货物描述
}

// 运单信息
type WaybillInfo struct {
	CustomerOrderNo string       `json:"customerOrderNo"` // 客户订单号
	OrderChannel    string       `json:"orderChannel"`    // 订单渠道
	ServiceType     string       `json:"serviceType"`     // 服务类型
	Sender          SenderInfo   `json:"sender"`          // 寄件人信息
	Receiver        ReceiverInfo `json:"receiver"`        // 收件人信息
	Cargo           []CargoInfo  `json:"cargo"`           // 货物信息
	Remark          string       `json:"remark"`          // 备注
}

// 运单创建请求
type CreateWaybillRequest struct {
	Language    string      `json:"language"`    // 语言代码
	WaybillInfo WaybillInfo `json:"waybillInfo"` // 运单信息
}

// 运单创建响应
type CreateWaybillResponse struct {
	WaybillNo       string `json:"waybillNo"`       // 运单号
	TrackingNo      string `json:"trackingNo"`      // 跟踪号
	CustomerOrderNo string `json:"customerOrderNo"` // 客户订单号
	TotalFee        int    `json:"totalFee"`        // 总费用(分)
	Currency        string `json:"currency"`        // 币种
}

// 运单查询请求
type QueryWaybillRequest struct {
	Language    string   `json:"language"`    // 语言代码
	WaybillNos  []string `json:"waybillNos"`  // 运单号列表
	TrackingNos []string `json:"trackingNos"` // 跟踪号列表
}

// 运单查询响应
type QueryWaybillResponse struct {
	WaybillNo  string `json:"waybillNo"`  // 运单号
	TrackingNo string `json:"trackingNo"` // 跟踪号
	Status     string `json:"status"`     // 状态
	StatusDesc string `json:"statusDesc"` // 状态描述
	UpdateTime string `json:"updateTime"` // 更新时间
}

// 物流轨迹信息
type TrackInfo struct {
	TrackTime   string `json:"trackTime"`   // 轨迹时间
	TrackStatus string `json:"trackStatus"` // 轨迹状态
	TrackDesc   string `json:"trackDesc"`   // 轨迹描述
	Location    string `json:"location"`    // 位置
}

// 物流轨迹查询请求
type QueryTrackRequest struct {
	Language    string   `json:"language"`    // 语言代码
	WaybillNos  []string `json:"waybillNos"`  // 运单号列表
	TrackingNos []string `json:"trackingNos"` // 跟踪号列表
}

// 物流轨迹查询响应
type QueryTrackResponse struct {
	WaybillNo  string      `json:"waybillNo"`  // 运单号
	TrackingNo string      `json:"trackingNo"` // 跟踪号
	Status     string      `json:"status"`     // 状态
	StatusDesc string      `json:"statusDesc"` // 状态描述
	Tracks     []TrackInfo `json:"tracks"`     // 轨迹信息
}

// 费用查询请求
type QueryFeeRequest struct {
	Language        string      `json:"language"`        // 语言代码
	ServiceType     string      `json:"serviceType"`     // 服务类型
	SenderCountry   string      `json:"senderCountry"`   // 寄件国家
	ReceiverCountry string      `json:"receiverCountry"` // 收件国家
	Weight          int         `json:"weight"`          // 重量(克)
	Cargo           []CargoInfo `json:"cargo"`           // 货物信息
}

// 费用查询响应
type QueryFeeResponse struct {
	ServiceType   string `json:"serviceType"`   // 服务类型
	ServiceDesc   string `json:"serviceDesc"`   // 服务描述
	TotalFee      int    `json:"totalFee"`      // 总费用(分)
	Currency      string `json:"currency"`      // 币种
	EstimatedDays int    `json:"estimatedDays"` // 预计天数
}

// 取消运单请求
type CancelWaybillRequest struct {
	Language    string   `json:"language"`    // 语言代码
	WaybillNos  []string `json:"waybillNos"`  // 运单号列表
	TrackingNos []string `json:"trackingNos"` // 跟踪号列表
	Reason      string   `json:"reason"`      // 取消原因
}

// 取消运单响应
type CancelWaybillResponse struct {
	WaybillNo  string `json:"waybillNo"`  // 运单号
	TrackingNo string `json:"trackingNo"` // 跟踪号
	Status     string `json:"status"`     // 状态
	StatusDesc string `json:"statusDesc"` // 状态描述
	CancelTime string `json:"cancelTime"` // 取消时间
}

// DynamicResponse 动态响应结构，用于处理retBody的不同类型
type DynamicResponse struct {
	SerialNo string          `json:"serialNo"` // 每次请求的唯一编码，用于查询日志使用
	RetCode  string          `json:"retCode"`  // 00000表示成功；S00001表示系统级错误；B00001表示接口级错误
	RetMsg   string          `json:"retMsg"`   // 错误信息
	RetBody  json.RawMessage `json:"retBody"`  // 使用RawMessage来处理动态类型
	RetDate  EMSTime         `json:"retDate"`  // 各接口返回的时间
	Success  bool            `json:"success"`  // 是否成功
}

// Check 检查响应是否成功
func (r *DynamicResponse) Check() error {
	if r.RetCode != "00000" {
		return &EMSError{
			Code:    r.RetCode,
			Message: r.RetMsg,
		}
	}
	return nil
}

// ParseRetBody 解析RetBody到指定的结构体
func (r *DynamicResponse) ParseRetBody(target interface{}) error {
	// 如果RetBody是空字符串或空，直接返回
	if len(r.RetBody) == 0 || string(r.RetBody) == `""` || string(r.RetBody) == `null` {
		return nil
	}

	// 尝试解析JSON
	return json.Unmarshal(r.RetBody, target)
}

// GetRetBodyAsString 获取RetBody的字符串值（用于错误消息）
func (r *DynamicResponse) GetRetBodyAsString() string {
	if len(r.RetBody) == 0 {
		return ""
	}

	// 如果是字符串类型，去掉引号
	var str string
	if err := json.Unmarshal(r.RetBody, &str); err == nil {
		return str
	}

	// 否则返回原始JSON
	return string(r.RetBody)
}

// 推送通知相关结构

// PushNotificationData 推送通知数据结构
type PushNotificationData struct {
	Method           string      `json:"method"`                     // 接口代码，固定为040002
	SenderNo         string      `json:"senderNo"`                   // 客户代码
	MsgType          string      `json:"msgType"`                    // 0 - json、1 - xml，默认为json
	TimeStamp        string      `json:"timeStamp,omitempty"`        // 请求时间，格式为yyyy-MM-dd HH:mm:ss
	Version          string      `json:"version"`                    // 版本号，默认V1.0.0
	LogitcsInterface string      `json:"logitcsInterface,omitempty"` // 推送消息体（加密后的数据）
	SerialNo         string      `json:"serialNo"`                   // 每次请求的唯一编码
}

// PushNotificationResponse 推送通知响应结构
type PushNotificationResponse struct {
	SerialNo    string `json:"serialNo"`    // 每次请求的唯一编码，用于查询日志使用
	Code        string `json:"code"`        // 00000表示成功；S00001表示推送失败
	CodeMessage string `json:"codeMessage"` // 具体错误原因
	SenderNo    string `json:"senderNo"`    // 客户代码
}

// PushBusinessData 推送业务数据结构
type PushBusinessData struct {
	WaybillNo  string      `json:"waybillNo"`  // 运单号
	TrackingNo string      `json:"trackingNo"` // 跟踪号
	Status     string      `json:"status"`     // 状态
	StatusDesc string      `json:"statusDesc"` // 状态描述
	Tracks     []TrackInfo `json:"tracks"`     // 轨迹信息
	UpdateTime string      `json:"updateTime"` // 更新时间
}

// PushTrackInfo 推送轨迹信息
type PushTrackInfo struct {
	TrackTime   string `json:"trackTime"`   // 轨迹时间
	TrackStatus string `json:"trackStatus"` // 轨迹状态
	TrackDesc   string `json:"trackDesc"`   // 轨迹描述
	Location    string `json:"location"`    // 位置
	Operator    string `json:"operator"`    // 操作员
}
