package vgoods

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-resty/resty/v2"
)

type Config struct {
	VPSDomain    string `json:"vps_domain"`
	VIPDomain    string `json:"vip_domain"`
	ShadowDomain string `json:"shadow_domain"`
	BrokerDomain string `json:"broker_domain"`
	ReportDomain string `json:"report_domain"`
}

type Client struct {
	client    *resty.Client
	vpsURL    string
	vipURL    string
	shadowURL string
	brokerURL string
	reportURL string
}

func NewClient(c *Config) (*Client, error) {
	if c == nil {
		return nil, fmt.Errorf("empty virtual_goods")
	}

	if c.VPSDomain == "" {
		return nil, fmt.Errorf("vgoods vps_domain is empty")
	}

	if c.VIPDomain == "" {
		return nil, fmt.Errorf("vgoods vip_domain is empty")
	}

	if c.ShadowDomain == "" {
		return nil, fmt.Errorf("vgoods shadow_domain is empty")
	}

	if c.<PERSON>roker<PERSON>omain == "" {
		return nil, fmt.Errorf("vgoods broker_domain is empty")
	}

	if c.ReportDomain == "" {
		return nil, fmt.Errorf("vgoods report_domain is empty")
	}

	return &Client{
		client: resty.New().
			SetHeader("Connection", "keep-alive").
			SetTransport(&http.Transport{
				MaxIdleConnsPerHost: 10,               // 每个主机的最大空闲连接数
				IdleConnTimeout:     10 * time.Minute, // 空闲连接超时时间
			}).
			SetTimeout(time.Minute * 1).
			OnBeforeRequest(BeforeRequest).
			OnAfterResponse(AfterResponse),
		vpsURL:    c.VPSDomain,
		vipURL:    c.VIPDomain,
		shadowURL: c.ShadowDomain,
		brokerURL: c.BrokerDomain,
		reportURL: c.ReportDomain,
	}, nil
}

func BeforeRequest(_ *resty.Client, request *resty.Request) error {
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Accept", "application/json")
	return nil
}

func AfterResponse(_ *resty.Client, response *resty.Response) error {
	//println("原始数据：", string(response.Body()))
	if response.StatusCode() != http.StatusOK {
		return fmt.Errorf("response code is not is %d", response.StatusCode())
	}

	ret := response.Result()
	if check, ok := ret.(CheckResponse); ok {
		return check.Check()
	}
	return nil
}

func headerFromContext(ctx context.Context) map[string]string {
	var (
		basicData, _    = icontext.BasicDataFrom(ctx)
		clientIP, _     = icontext.ClientIPFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		requestId, _    = icontext.RequestIdFrom(ctx)
	)
	return map[string]string{
		"BasicData":       basicData,
		"X-Forwarded-For": clientIP,
		"CountryCode":     countryCode,
		"LanguageCode":    languageCode,
		"X-Request-Id":    requestId,
	}
}
