package vgoods

import (
	"context"
	"fmt"
	"net/url"
)

func (c *Client) GiveVPS(ctx context.Context, req *GiveVPSRequest) (*GiveVPSReply, error) {
	resp := &GiveVPSReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/mt4Os/uhost/CreateUHostInstance", c.vpsURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) GiveVIP(ctx context.Context, req *GiveVIPRequest) (*GiveVIPReply, error) {
	resp := &GiveVIPReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/usercenter/directcompletionorder/v3", c.vipURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) GetUserVPSInfo(ctx context.Context, req *GetUserVPSInfoRequest) (*GetUserVPSInfoReply, error) {
	resp := &GetUserVPSInfoReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/mt4Os/uhost/power", c.vpsURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) CheckShadow(ctx context.Context, req *CheckShadowRequest) (*CheckShadowReply, error) {
	resp := &CheckShadowReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Get(fmt.Sprintf("%s/api/shieldcenterV2/wikifx/get?point=%s", c.shadowURL, req.Point))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) GetBroker(ctx context.Context, req *GetBrokerRequest) (*GetBrokerReply, error) {
	resp := &GetBrokerReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/wikicore/getMultiple/v2", c.brokerURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) GetReportDetail(ctx context.Context, req *GetReportDetailRequest) (*GetReportDetailReply, error) {
	resp := &GetReportDetailReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Get(fmt.Sprintf("%s/api/wikifx/orderdetail?orderId=%s", c.reportURL, req.OrderNo))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) FindReportList(ctx context.Context, req *FindReportListRequest) (*FindReportListReply, error) {
	resp := &FindReportListReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Get(fmt.Sprintf("%s/api/wikifx/pullorder?time=%s&pageSize=%d", c.reportURL, url.QueryEscape(req.CreateTime), req.PageSize))
	if err != nil {
		return nil, err
	}
	return resp, nil
}
