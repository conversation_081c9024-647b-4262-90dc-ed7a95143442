package vgoods

import "fmt"

type CheckResponse interface {
	Check() error
}

type CommonResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	ErrorCode int    `json:"errorCode"`
	IsSuccess bool   `json:"isSuccess"`
}

func (s CommonResponse) Check() error {
	if !s.IsSuccess {
		return fmt.Errorf(s.Message)
	}
	return nil
}

type GiveVPSRequest struct {
	UserId   string `json:"UserId"`
	Zone     string `json:"Zone"`
	Language int    `json:"Language"`
	Config   int    `json:"Config"`
	Level    string `json:"Level"`
}

type GiveVPSReply struct {
	ErrorCode   int    `json:"ErrorCode"`
	Data        string `json:"Data"`
	Description string `json:"Description"`
	RequestId   string `json:"requestId"`
}

func (s *GiveVPSReply) Check() error {
	if s.ErrorCode != 200 {
		return fmt.Errorf("%s[%s]", s.Description, s.RequestId)
	}
	return nil
}

type GiveVIPRequest struct {
	OrderNo    string `json:"orderNo"`
	UserId     string `json:"userId"`
	Year       int    `json:"year"`
	Month      int    `json:"month"`
	Day        int    `json:"day"`
	PaidAt     string `json:"paidAt"`
	PaidAmount int    `json:"paidAmount"`
}

type GiveVIPReply struct {
	Succeed bool   `json:"succeed"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

func (s *GiveVIPReply) Check() error {
	if !s.Succeed {
		return fmt.Errorf(s.Message)
	}
	return nil
}

type GetUserVPSInfoRequest struct {
	UserId string `json:"user_id"`
}

type GetUserVPSInfoReply struct {
	ErrorCode int `json:"ErrorCode"`
	Data      struct {
		HasUhost    bool `json:"has_uhost"`
		IsBlackuser bool `json:"is_blackuser"`
	} `json:"Data"`
	Description string `json:"Description"`
	RequestId   string `json:"requestId"`
}

func (s *GetUserVPSInfoReply) Check() error {
	if s.ErrorCode != 200 {
		return fmt.Errorf("%s[%s]", s.Description, s.RequestId)
	}
	return nil
}

type CheckShadowRequest struct {
	Point string
}

type CheckShadowReply struct {
	Code   int `json:"code"`
	Result struct {
		Point int    `json:"point"`
		Value int    `json:"value"`
		Ip    string `json:"ip"`
	} `json:"result"`
	Succeed   bool        `json:"succeed"`
	Error     interface{} `json:"error"`
	Message   interface{} `json:"message"`
	Timestamp int64       `json:"timestamp"`
}

type GetBrokerRequest struct {
	Codes       []string `json:"codes"`
	Country     string   `json:"country"`
	HasDisabled bool     `json:"hasDisabled"`
	Language    string   `json:"language"`
}

type GetBrokerReply struct {
	Result []struct {
		Code       string  `json:"code"`
		Score      float64 `json:"score"`
		TextScore  string  `json:"text_score"`
		ScoreLevel int     `json:"scoreLevel"`
		Color      string  `json:"color"`
		Annotation string  `json:"annotation"`
		Labels     []struct {
			Type int `json:"type"`
			Data []struct {
				LabelName string `json:"labelName"`
			} `json:"Data"`
		} `json:"labels"`
		SpecialtyLabels []struct {
			LabelType       int    `json:"labelType"`
			Icon            string `json:"icon"`
			Image           string `json:"image"`
			Content         string `json:"content"`
			BackgroundColor string `json:"backgroundColor"`
			FontColor       string `json:"fontColor"`
			ImageWidth      int    `json:"imageWidth"`
			ImageHeight     int    `json:"imageHeight"`
		} `json:"specialtyLabels"`
		Flag                 string      `json:"flag"`
		Logo                 string      `json:"logo"`
		Ico                  string      `json:"ico"`
		Hico                 string      `json:"hico"`
		Project              int         `json:"project"`
		Type                 int         `json:"type"`
		Regnumber            interface{} `json:"regnumber"`
		LocalShortName       string      `json:"localShortName"`
		LocalFullName        string      `json:"localFullName"`
		EnglishShortName     string      `json:"englishShortName"`
		EnglishFullName      string      `json:"englishFullName"`
		ShowName             string      `json:"showName"`
		EnterpriseName       string      `json:"enterpriseName"`
		IsVr                 bool        `json:"isVr"`
		AgentMember          int         `json:"agentMember"`
		AgentLabel           interface{} `json:"agentLabel"`
		AgentBadge           interface{} `json:"agentBadge"`
		AgentTraderInfo      interface{} `json:"agentTraderInfo"`
		IsEpc                bool        `json:"isEpc"`
		UltimateType         int         `json:"ultimateType"`
		UrlName              string      `json:"urlName"`
		Seal                 string      `json:"seal"`
		CreatedDate          string      `json:"createdDate"`
		IsFake               bool        `json:"isFake"`
		Kbscore              string      `json:"kbscore"`
		ServiceProviderLabel interface{} `json:"serviceProviderLabel"`
		IsWhite              bool        `json:"isWhite"`
		PropTraderFeeLabel   string      `json:"propTraderFeeLabel"`
		Icon                 string      `json:"icon"`
		ForbidExpiredAt      string      `json:"forbidExpiredAt"`
		RegisterCountry      string      `json:"registerCountry"`
		Status               int         `json:"status"`
		Dimension            []struct {
			Code     string  `json:"code"`
			Category string  `json:"category"`
			Score    float64 `json:"score"`
		} `json:"dimension"`
		Investment []struct {
			Type    int    `json:"type"`
			Contact string `json:"contact"`
		} `json:"investment"`
		Category struct {
			Color string `json:"Color"`
			Name  string `json:"Name"`
		} `json:"category"`
		StaffCount       int           `json:"staffCount"`
		CooperationCount int           `json:"cooperationCount"`
		Website          []string      `json:"website"`
		AccountWebsite   []interface{} `json:"accountWebsite"`
	} `json:"result"`
	Succeed   bool   `json:"succeed"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
}

func (s *GetBrokerReply) Check() error {
	if !s.Succeed {
		return fmt.Errorf("%s[%s]", s.Message, s.RequestId)
	}
	return nil
}

type GetReportDetailRequest struct {
	OrderNo string `json:"order_no"`
}

type GetReportDetailReply struct {
	Statuscode int `json:"statuscode"`
	Result     struct {
		Id                    int     `json:"id"`
		OrderId               string  `json:"orderId"`
		Uid                   string  `json:"uid"`
		Email                 string  `json:"email"`
		Status                string  `json:"status"`
		OrderColor            string  `json:"orderColor"`
		SengEmailStatus       string  `json:"sengEmailStatus"`
		SengEmailColor        string  `json:"sengEmailColor"`
		CreateStatus          string  `json:"createStatus"`
		CreateColor           string  `json:"createColor"`
		Tag                   string  `json:"tag"`
		PdfSeeUrl             string  `json:"pdfSeeUrl"`
		IsClick               bool    `json:"isClick"`
		OrderDate             string  `json:"orderDate"`
		PayMent               float64 `json:"payMent"`
		PurchaseBody          string  `json:"purchaseBody"`
		Language              string  `json:"language"`
		LanguageType          int     `json:"languageType"`
		CreateDate            string  `json:"createDate"`
		UpdateDate            string  `json:"updateDate"`
		OrderDateUtcTimestamp string  `json:"orderDateUtcTimestamp"`
	} `json:"result"`
	Succeed   bool        `json:"succeed"`
	Message   interface{} `json:"message"`
	Timestamp int64       `json:"timestamp"`
}

func (s *GetReportDetailReply) Check() error {
	if !s.Succeed {
		return fmt.Errorf("%s", s.Message)
	}
	return nil
}

type FindReportListRequest struct {
	PageSize   int    `json:"pageSize"`
	CreateTime string `json:"CreateTime"`
}

type FindReportListReply struct {
	Statuscode int `json:"statuscode"`
	Result     struct {
		Items []struct {
			UserId         string  `json:"userId"`
			OrderId        string  `json:"orderId"`
			TraderCode     string  `json:"traderCode"`
			ReportType     int32   `json:"reportType"`
			OrderTime      string  `json:"orderTime"`
			Price          float32 `json:"price"`
			SymbolType     int32   `json:"symbolType"`
			ReportLanguage int32   `json:"reportLanguage"`
			PayType        int32   `json:"payType"`
		} `json:"items"`
		NextTime string `json:"nextTime"`
	} `json:"result"`
	Succeed   bool        `json:"succeed"`
	Message   interface{} `json:"message"`
	Timestamp int64       `json:"timestamp"`
}

func (s *FindReportListReply) Check() error {
	if !s.Succeed {
		return fmt.Errorf("%s", s.Message)
	}
	return nil
}
