package vgoods

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/stretchr/testify/assert"
)

var (
	cli *Client
	ctx context.Context
)

func TestMain(m *testing.M) {
	var err error
	cli, err = NewClient(&Config{
		VPSDomain:    "http://cloudvpsapi.fxeyeinterface.com",
		VIPDomain:    "http://wikiapi.fxeyeinterface.com",
		ShadowDomain: "http://shieldcenterapi.fxeyeinterface.com",
		BrokerDomain: "http://wikibitsearch.fxeyeinterface.com",
		ReportDomain: "http://bitfxpdfapi.fxeyeinterface.com",
	})
	if err != nil {
		panic(err)
	}
	ctx = context.Background()
	ctx = icontext.WithBasicData(ctx, "1,31,3,250,0,c105f1288fe88ab3,0")
	os.Exit(m.Run())
}

func TestClient_GiveVPS(t *testing.T) {
	res, err := cli.GiveVPS(ctx, &GiveVPSRequest{
		UserId:   "3004325208",
		Zone:     "cn-bj2-05",
		Language: 1,
		Config:   1, // 1:1C1G;2:2C2G;3:3C3G;
	})
	assert.Nil(t, err)
	assert.NotEqual(t, "", res.RequestId)
}

func TestClient_GiveVIP(t *testing.T) {
	res, err := cli.GiveVIP(ctx, &GiveVIPRequest{
		OrderNo:    "20250606213902384890",
		UserId:     "3004325208",
		Day:        10,
		PaidAt:     time.Now().Format(time.RFC3339),
		PaidAmount: 10,
	})
	assert.Nil(t, err)
	assert.Equal(t, "", res.Error)
}

func TestClient_GetUserVPSInfo(t *testing.T) {
	res, err := cli.GetUserVPSInfo(ctx, &GetUserVPSInfoRequest{
		UserId: "110",
	})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}

func TestClient_CheckShadow(t *testing.T) {
	ctx = icontext.WithCountryCode(ctx, "764")
	res, err := cli.CheckShadow(ctx, &CheckShadowRequest{
		Point: "12005",
	})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}

func TestClient_GetBroker(t *testing.T) {
	res, err := cli.GetBroker(ctx, &GetBrokerRequest{
		Codes:    []string{"3351410785"},
		Language: "en",
	})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}

func TestClient_GetReportDetail(t *testing.T) {
	res, err := cli.GetReportDetail(ctx, &GetReportDetailRequest{
		OrderNo: "3351410785",
	})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}

func TestClient_FindReportList(t *testing.T) {
	res, err := cli.FindReportList(ctx, &FindReportListRequest{
		PageSize:   10,
		CreateTime: "2019-09-06 16:01:53",
	})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}
