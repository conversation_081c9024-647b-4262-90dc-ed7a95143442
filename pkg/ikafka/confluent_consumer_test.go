package ikafka

import (
	"fmt"
	"testing"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/stretchr/testify/assert"
)

func ConfluentConsumerFunc(c *kafka.Consumer, msg *kafka.Message) {
	defer c.CommitMessage(msg)

	fmt.Println("数据：", string(msg.Value))
}

func TestNewConfluentConsumer(t *testing.T) {
	consumer, err := NewConfluentConsumer(&ConsumerConfig{
		Brokers:      []string{"192.168.19.15:9092"},
		Topics:       []string{"trader-flow-machine"},
		GroupId:      "wiki_netwon11",
		OffsetOldest: true,
	})
	assert.Nil(t, err)
	defer consumer.Close()

	consumer.Start(ConfluentConsumerFunc)
	assert.Nil(t, nil)
	select {}
	return
}
