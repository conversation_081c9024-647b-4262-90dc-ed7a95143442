package ikafka

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/IBM/sarama"
	"github.com/go-kratos/kratos/v2/log"
)

type ConsumerConfig struct {
	Brokers           []string      `json:"brokers" yaml:"brokers"`
	Topics            []string      `json:"topics" yaml:"topics"`
	GroupId           string        `json:"group_id" yaml:"group_id"`
	Username          string        `json:"username" yaml:"username"`
	Password          string        `json:"password" yaml:"password"`
	OffsetOldest      bool          `json:"offset_oldest" yaml:"offset_oldest"`
	EnableSASL        bool          `json:"enable_sasl" yaml:"enable_sasl"`
	EnableTLS         bool          `json:"enable_tls" yaml:"enable_tls"`
	Version           string        `json:"version" yaml:"version"`
	AutoCommit        bool          `json:"auto_commit" yaml:"auto_commit"`
	MaxProcessingTime time.Duration `json:"max_processing_time" yaml:"max_processing_time"`
	DialTimeout       time.Duration `json:"dial_timeout" yaml:"dial_timeout"`           // 连接超时
	ReadTimeout       time.Duration `json:"read_timeout" yaml:"read_timeout"`           // 读取超时
	WriteTimeout      time.Duration `json:"write_timeout" yaml:"write_timeout"`         // 写入超时
	KeepAlive         time.Duration `json:"keep_alive" yaml:"keep_alive"`               // TCP keepalive间隔
	MaxRetries        int           `json:"max_retries" yaml:"max_retries"`             // 最大重试次数
	RetryBackoff      time.Duration `json:"retry_backoff" yaml:"retry_backoff"`         // 重试间隔
	ConnectionsRetry  bool          `json:"connections_retry" yaml:"connections_retry"` // 是否自动重连
}

func (c *ConsumerConfig) Copy() *ConsumerConfig {
	return &ConsumerConfig{
		Brokers:           c.Brokers,
		Topics:            c.Topics,
		GroupId:           c.GroupId,
		Username:          c.Username,
		Password:          c.Password,
		OffsetOldest:      c.OffsetOldest,
		EnableSASL:        c.EnableSASL,
		EnableTLS:         c.EnableTLS,
		Version:           c.Version,
		AutoCommit:        c.AutoCommit,
		MaxProcessingTime: c.MaxProcessingTime,
		DialTimeout:       c.DialTimeout,
		ReadTimeout:       c.ReadTimeout,
		WriteTimeout:      c.WriteTimeout,
		KeepAlive:         c.KeepAlive,
		MaxRetries:        c.MaxRetries,
		RetryBackoff:      c.RetryBackoff,
		ConnectionsRetry:  c.ConnectionsRetry,
	}
}

type Do func(session sarama.ConsumerGroupSession, message *sarama.ConsumerMessage)

type Consumer struct {
	cfg    *ConsumerConfig
	ready  chan bool
	wg     *sync.WaitGroup
	client sarama.ConsumerGroup
	ctx    context.Context
	cancel context.CancelFunc
	do     Do
	atomic atomic.Int32
}

func NewConsumer(cfg *ConsumerConfig) (*Consumer, error) {
	version, err := sarama.ParseKafkaVersion(cfg.Version)
	if err != nil {
		return nil, err
	}

	config := sarama.NewConfig()
	config.Version = version
	config.Net.SASL.Enable = cfg.EnableSASL
	config.Net.TLS.Enable = cfg.EnableTLS
	config.Consumer.MaxWaitTime = 500 * time.Millisecond
	config.Consumer.Group.Rebalance.Timeout = 60 * time.Second
	config.Consumer.Group.Session.Timeout = 60 * time.Second
	//config.Consumer.IsolationLevel = sarama.ReadCommitted

	// 配置网络相关超时设置
	if cfg.DialTimeout > 0 {
		config.Net.DialTimeout = cfg.DialTimeout
	} else {
		config.Net.DialTimeout = 30 * time.Second // 默认30秒连接超时
	}

	if cfg.ReadTimeout > 0 {
		config.Net.ReadTimeout = cfg.ReadTimeout
	} else {
		config.Net.ReadTimeout = 30 * time.Second // 默认30秒读取超时
	}

	if cfg.WriteTimeout > 0 {
		config.Net.WriteTimeout = cfg.WriteTimeout
	} else {
		config.Net.WriteTimeout = 30 * time.Second // 默认30秒写入超时
	}

	if cfg.KeepAlive > 0 {
		config.Net.KeepAlive = cfg.KeepAlive
	} else {
		config.Net.KeepAlive = 60 * time.Second // 默认60秒keepalive
	}

	// 配置重试策略
	if cfg.MaxRetries > 0 {
		config.Metadata.Retry.Max = cfg.MaxRetries
		config.Producer.Retry.Max = cfg.MaxRetries
	} else {
		config.Metadata.Retry.Max = 5 // 默认最多重试5次
		config.Producer.Retry.Max = 5
	}

	if cfg.RetryBackoff > 0 {
		config.Metadata.Retry.Backoff = cfg.RetryBackoff
		config.Producer.Retry.Backoff = cfg.RetryBackoff
	} else {
		config.Metadata.Retry.Backoff = 5 * time.Second // 默认重试间隔5秒
		config.Producer.Retry.Backoff = 5 * time.Second
	}

	if cfg.Username != "" {
		config.Net.SASL.User = cfg.Username
		config.Net.SASL.Password = cfg.Password
		config.Net.TLS.Config = &tls.Config{
			InsecureSkipVerify: true,
		}
	}

	if cfg.OffsetOldest {
		config.Consumer.Offsets.Initial = sarama.OffsetOldest
	}

	if cfg.MaxProcessingTime > 0 {
		config.Consumer.MaxProcessingTime = cfg.MaxProcessingTime
	}

	config.Consumer.Offsets.AutoCommit.Enable = false
	//config.Consumer.Group.Rebalance.GroupStrategies = []sarama.BalanceStrategy{sarama.NewBalanceStrategyRoundRobin()}
	//sarama.Logger = &Log{}

	err = config.Validate()
	if err != nil {
		return nil, err
	}

	client, err := sarama.NewConsumerGroup(cfg.Brokers, cfg.GroupId, config)
	if err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(context.Background())
	return &Consumer{
		cfg:    cfg,
		ready:  make(chan bool),
		wg:     &sync.WaitGroup{},
		client: client,
		ctx:    ctx,
		cancel: cancel,
	}, nil
}

func (s *Consumer) Start(do Do) error {
	if value := s.atomic.Load(); value == 1 {
		return errors.New("start func can be executed only once")
	}

	s.atomic.Add(1)

	if do == nil {
		return errors.New("empty do func")
	}

	s.do = do
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		for {
			if err := s.client.Consume(s.ctx, s.cfg.Topics, s); err != nil {
				if errors.Is(err, sarama.ErrClosedConsumerGroup) {
					return
				}

				log.Errorf("消费者错误: [%s]%v，将在5秒后尝试重新连接", s.cfg.Topics, err)
				// 如果配置了自动重连或未明确禁用，等待5秒后继续尝试
				if s.ctx.Err() == nil && (s.cfg.ConnectionsRetry || s.cfg.ConnectionsRetry == false) { // 默认或显式开启
					time.Sleep(5 * time.Second)
					continue
				}
				// 严重错误，无法恢复，直接终止程序
				panic(fmt.Sprintf("Consumer Consume Err:[%s]%v", s.cfg.Topics, err))
			}
			log.Infof("%s consumer restart", s.cfg.Topics)

			if s.ctx.Err() != nil {
				return
			}
			s.ready = make(chan bool)
		}
	}()

	go func() {
		<-s.ready
		log.Info("consumer running")
	}()
	return nil
}

func (s *Consumer) Close() error {
	s.cancel()
	s.wg.Wait()
	return s.client.Close()
}

func (s *Consumer) Setup(sarama.ConsumerGroupSession) error {
	close(s.ready)
	return nil
}

func (s *Consumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

func (s *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				log.Infof("%v message channel was closed", s.cfg.Topics)
				return nil
			}
			session.MarkMessage(message, "")
			s.do(session, message)
		case <-session.Context().Done():
			return nil
		}
	}
}
