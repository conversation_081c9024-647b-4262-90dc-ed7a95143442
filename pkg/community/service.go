package community

import (
	"context"
	"fmt"
)

func (c *Client) GetJoinActivityByPostsID(ctx context.Context, req *GetJoinActivityByPostsIDRequest) (*CommunityActivityResponse, error) {
	resp := &CommunityActivityResponse{}

	apiURL := fmt.Sprintf("%s/posts/getjoinactivitybypostsidforgoldstore?postsId=%s", c.BaseURL, req.PostsId)

	_, err := c.client.R().
		SetContext(ctx).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Get(apiURL)

	if err != nil {
		return nil, fmt.Errorf("failed to get join activity by posts ID from community API: %w", err)
	}

	return resp, nil
}
