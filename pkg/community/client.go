package community

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-resty/resty/v2"
)

const (
	// defaultBaseURL is the default base URL for the community API.
	defaultBaseURL = "http://wikicommunity.fxeyeinterface.com/api"
	// defaultTimeout is the default timeout for requests.
	defaultTimeout = 5 * time.Second
)

type Client struct {
	client  *resty.Client
	BaseURL string
}

func NewClient(baseURL string) (*Client, error) {
	if baseURL == "" {
		baseURL = defaultBaseURL
	}

	return &Client{
		client: resty.New().
			SetHeader("Connection", "keep-alive").
			SetTransport(&http.Transport{
				MaxIdleConnsPerHost: 10,               // 每个主机的最大空闲连接数
				IdleConnTimeout:     10 * time.Minute, // 空闲连接超时时间
			}).
			SetTimeout(defaultTimeout).
			OnBeforeRequest(BeforeRequest).
			OnAfterResponse(AfterResponse),
		BaseURL: baseURL,
	}, nil
}

func BeforeRequest(_ *resty.Client, request *resty.Request) error {
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Accept", "application/json")
	// Add any other common headers needed for community service
	return nil
}

func AfterResponse(_ *resty.Client, response *resty.Response) error {
	// fmt.Println("原始数据：", string(response.Body())) // For debugging, consider using a logger
	if response.StatusCode() != http.StatusOK {
		return fmt.Errorf("response code got %d expected %d, body: %s", response.StatusCode(), http.StatusOK, string(response.Body()))
	}

	ret := response.Result()
	if check, ok := ret.(CheckResponse); ok { // Ensure CheckResponse is defined in community/types.go
		return check.Check()
	}
	return nil
}

// headerFromContext extracts relevant headers from the context.
// This can be customized based on what the community API expects.
func headerFromContext(ctx context.Context) map[string]string {
	var (
		basicData, _    = icontext.BasicDataFrom(ctx)
		clientIP, _     = icontext.ClientIPFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		requestId, _    = icontext.RequestIdFrom(ctx)
	)
	// Customize these headers as per community API requirements
	headers := make(map[string]string)
	if basicData != "" {
		headers["BasicData"] = basicData
	}
	if clientIP != "" {
		headers["X-Forwarded-For"] = clientIP
	}
	if countryCode != "" {
		headers["CountryCode"] = countryCode
	}
	if languageCode != "" {
		headers["LanguageCode"] = languageCode
	}
	if requestId != "" {
		headers["X-Request-Id"] = requestId
	}
	return headers
}
