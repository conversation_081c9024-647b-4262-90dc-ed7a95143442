package community

import (
	"fmt"
)

// GetJoinActivityByPostsIDRequest defines the request structure for getting join activity by posts ID.
type GetJoinActivityByPostsIDRequest struct {
	PostsId string
}

type ActivityResult struct {
	UserID     *string `json:"userId"`
	ActivityID *string `json:"activityId"`
	IsExist    bool    `json:"isExist"`
}

// CommunityActivityResponse defines the overall structure of the API response.
type CommunityActivityResponse struct {
	StatusCode int            `json:"statusCode"`
	Result     ActivityResult `json:"result"`
	Succeed    bool           `json:"succeed"`
	Message    *string        `json:"message"`
	Extra      *string        `json:"extra"`
	Timestamp  int64          `json:"timestamp"`
}

// Check implements a check for the response, similar to gold.CheckResponse.
func (r *CommunityActivityResponse) Check() error {
	if !r.Succeed {
		if r.Message != nil {
			return fmt.Errorf("community API request failed: %s (statusCode: %d)", *r.Message, r.StatusCode)
		}
		return fmt.Errorf("community API request failed with statusCode: %d", r.StatusCode)
	}
	// Optionally, you could also check r.StatusCode == http.StatusOK here if Succeed alone isn't sufficient.
	return nil
}

// CheckResponse is an interface for response checking, similar to the one in the gold package.
// This is defined for compatibility with the AfterResponse pattern.
type CheckResponse interface {
	Check() error
}
