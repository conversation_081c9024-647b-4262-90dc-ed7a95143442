package tencentAi

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	iai "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/iai/v20200303"
)

// Client 腾讯云人脸识别客户端
type Client struct {
	config *Config
	client *iai.Client
	mu     sync.RWMutex
	closed bool
}

// NewClient 创建新的腾讯云人脸识别客户端
func NewClient(config *Config) (*Client, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// 创建腾讯云凭证
	credential := common.NewCredential(config.SecretId, config.SecretKey)

	// 创建客户端配置
	clientProfile := profile.NewClientProfile()
	clientProfile.HttpProfile.Endpoint = config.Endpoint

	// 使用配置文件中的超时时间，如果没有配置则默认60秒
	timeout := 60
	if config.TimeOutSec > 0 {
		timeout = config.TimeOutSec
	}
	clientProfile.HttpProfile.ReqTimeout = timeout

	// 创建IAI客户端
	iaiClient, err := iai.NewClient(credential, config.Region, clientProfile)
	if err != nil {
		return nil, fmt.Errorf("failed to create iai client: %w", err)
	}

	// 创建客户端实例
	client := &Client{
		config: config,
		client: iaiClient,
	}

	return client, nil
}

// Close 关闭客户端
func (c *Client) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return ErrClientClosed
	}

	c.closed = true
	return nil
}

// IsHealthy 检查客户端健康状态
func (c *Client) IsHealthy() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return !c.closed && c.client != nil
}

// DetectFace 人脸检测
func (c *Client) DetectFace(ctx context.Context, req *DetectFaceRequest) (*DetectFaceResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil {
		return nil, ErrInvalidRequest
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)

	l.Debugf("人脸检测请求: image_size=%d, url=%s", len(req.Image), req.Url)

	// 创建腾讯云请求
	request := iai.NewDetectFaceRequest()

	// 设置图片参数
	if req.Image != "" {
		request.Image = common.StringPtr(req.Image)
	} else if req.Url != "" {
		request.Url = common.StringPtr(req.Url)
	} else {
		return nil, ErrImageEmpty
	}

	// 设置其他参数，使用配置的默认值
	if req.MaxFaceNum > 0 {
		request.MaxFaceNum = common.Uint64Ptr(uint64(req.MaxFaceNum))
	} else if c.config.SearchConfig.MaxFaceNum > 0 {
		request.MaxFaceNum = common.Uint64Ptr(uint64(c.config.SearchConfig.MaxFaceNum))
	}
	if req.MinFaceSize > 0 {
		request.MinFaceSize = common.Uint64Ptr(uint64(req.MinFaceSize))
	} else if c.config.SearchConfig.MinFaceSize > 0 {
		request.MinFaceSize = common.Uint64Ptr(uint64(c.config.SearchConfig.MinFaceSize))
	}
	if req.NeedFaceAttributes {
		request.NeedFaceAttributes = common.Uint64Ptr(1)
	}
	if req.NeedQualityDetection {
		request.NeedQualityDetection = common.Uint64Ptr(1)
	}
	if req.NeedRotateDetection {
		request.NeedRotateDetection = common.Uint64Ptr(1)
	}
	if req.FaceModelVersion != "" {
		request.FaceModelVersion = common.StringPtr(req.FaceModelVersion)
	} else {
		request.FaceModelVersion = common.StringPtr(c.config.FaceModelVersion)
	}

	// 执行请求
	response, err := c.client.DetectFaceWithContext(ctx, request)
	if err != nil {
		l.Errorf("人脸检测失败: %v, 耗时: %v", err, time.Since(startTime))
		return nil, c.wrapTencentError("DetectFace", err)
	}

	duration := time.Since(startTime)

	// 转换响应
	detectResult := c.convertDetectFaceResponse(response)

	l.Infof("人脸检测成功: 检测到%d个人脸, 耗时: %v, 请求ID: %s",
		len(detectResult.FaceInfos), duration, detectResult.RequestId)

	return detectResult, nil
}

// SearchFaces 人脸搜索
func (c *Client) SearchFaces(ctx context.Context, req *SearchFacesRequest) (*SearchFacesResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil {
		return nil, ErrInvalidRequest
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)

	l.Debugf("人脸搜索请求: group_count=%d, image_size=%d", len(req.GroupIds), len(req.Image))

	// 创建腾讯云请求
	request := iai.NewSearchFacesRequest()

	// 设置人员库列表
	if len(req.GroupIds) == 0 {
		return nil, ErrGroupIdEmpty
	}
	groupIds := make([]*string, len(req.GroupIds))
	for i, id := range req.GroupIds {
		groupIds[i] = common.StringPtr(id)
	}
	request.GroupIds = groupIds

	// 设置图片参数
	if req.Image != "" {
		request.Image = common.StringPtr(req.Image)
	} else if req.Url != "" {
		request.Url = common.StringPtr(req.Url)
	} else {
		return nil, ErrImageEmpty
	}

	// 设置其他参数，使用配置的默认值
	if req.MaxFaceNum > 0 {
		request.MaxFaceNum = common.Uint64Ptr(uint64(req.MaxFaceNum))
	} else if c.config.SearchConfig.MaxFaceNum > 0 {
		request.MaxFaceNum = common.Uint64Ptr(uint64(c.config.SearchConfig.MaxFaceNum))
	}
	if req.MinFaceSize > 0 {
		request.MinFaceSize = common.Uint64Ptr(uint64(req.MinFaceSize))
	} else if c.config.SearchConfig.MinFaceSize > 0 {
		request.MinFaceSize = common.Uint64Ptr(uint64(c.config.SearchConfig.MinFaceSize))
	}
	if req.MaxPersonNum > 0 {
		request.MaxPersonNum = common.Uint64Ptr(uint64(req.MaxPersonNum))
	} else if c.config.SearchConfig.MaxPersonNum > 0 {
		request.MaxPersonNum = common.Uint64Ptr(uint64(c.config.SearchConfig.MaxPersonNum))
	}
	if req.QualityControl > 0 {
		request.QualityControl = common.Uint64Ptr(uint64(req.QualityControl))
	}
	if req.FaceMatchThreshold > 0 {
		request.FaceMatchThreshold = common.Float64Ptr(req.FaceMatchThreshold)
	} else if c.config.SearchConfig.DefaultMatchThreshold > 0 {
		request.FaceMatchThreshold = common.Float64Ptr(c.config.SearchConfig.DefaultMatchThreshold)
	}
	if req.NeedPersonInfo {
		request.NeedPersonInfo = common.Int64Ptr(1)
	}
	if req.NeedRotateDetection {
		request.NeedRotateDetection = common.Uint64Ptr(1)
	}

	// 执行请求
	response, err := c.client.SearchFacesWithContext(ctx, request)
	if err != nil {
		l.Errorf("人脸搜索失败: %v, 耗时: %v", err, time.Since(startTime))
		return nil, c.wrapTencentError("SearchFaces", err)
	}

	duration := time.Since(startTime)

	// 转换响应
	searchResult := c.convertSearchFacesResponse(response)

	l.Infof("人脸搜索成功: 结果数量=%d, 耗时: %v, 请求ID: %s",
		len(searchResult.Results), duration, searchResult.RequestId)

	return searchResult, nil
}

// CreateGroup 创建人员库
func (c *Client) CreateGroup(ctx context.Context, req *CreateGroupRequest) (*CreateGroupResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil {
		return nil, ErrInvalidRequest
	}

	if req.GroupId == "" || req.GroupName == "" {
		return nil, ErrGroupIdEmpty
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)

	l.Debugf("创建人员库请求: group_id=%s, group_name=%s", req.GroupId, req.GroupName)

	// 创建腾讯云请求
	request := iai.NewCreateGroupRequest()
	request.GroupId = common.StringPtr(req.GroupId)
	request.GroupName = common.StringPtr(req.GroupName)

	if len(req.GroupExDescriptions) > 0 {
		descriptions := make([]*string, len(req.GroupExDescriptions))
		for i, desc := range req.GroupExDescriptions {
			descriptions[i] = common.StringPtr(desc)
		}
		request.GroupExDescriptions = descriptions
	}

	if req.Tag != "" {
		request.Tag = common.StringPtr(req.Tag)
	}

	if req.FaceModelVersion != "" {
		request.FaceModelVersion = common.StringPtr(req.FaceModelVersion)
	} else {
		request.FaceModelVersion = common.StringPtr(c.config.FaceModelVersion)
	}

	// 执行请求
	response, err := c.client.CreateGroupWithContext(ctx, request)
	if err != nil {
		l.Errorf("创建人员库失败: %v, 耗时: %v", err, time.Since(startTime))
		return nil, c.wrapTencentError("CreateGroup", err)
	}

	duration := time.Since(startTime)

	createResult := &CreateGroupResponse{
		RequestId: *response.Response.RequestId,
	}
	if response.Response.FaceModelVersion != nil {
		createResult.FaceModelVersion = *response.Response.FaceModelVersion
	}

	l.Infof("创建人员库成功: group_id=%s, 耗时: %v, 请求ID: %s",
		req.GroupId, duration, createResult.RequestId)

	return createResult, nil
}

// GetGroupInfo 获取人员库信息
func (c *Client) GetGroupInfo(ctx context.Context, req *GetGroupInfoRequest) (*GetGroupInfoResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil || req.GroupId == "" {
		return nil, ErrGroupIdEmpty
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)

	l.Debugf("获取人员库信息: group_id=%s", req.GroupId)

	// 创建腾讯云请求
	request := iai.NewGetGroupInfoRequest()
	request.GroupId = common.StringPtr(req.GroupId)

	// 执行请求
	response, err := c.client.GetGroupInfoWithContext(ctx, request)
	if err != nil {
		l.Errorf("获取人员库信息失败: %v, 耗时: %v", err, time.Since(startTime))
		return nil, c.wrapTencentError("GetGroupInfo", err)
	}

	duration := time.Since(startTime)

	groupResult := &GetGroupInfoResponse{
		RequestId: *response.Response.RequestId,
	}

	// 转换人员库信息
	groupResult.GroupInfo = c.convertGroupInfo(response.Response)

	l.Infof("获取人员库信息成功: group_id=%s, 耗时: %v, 请求ID: %s",
		req.GroupId, duration, groupResult.RequestId)

	return groupResult, nil
}

// DeleteGroup 删除人员库
func (c *Client) DeleteGroup(ctx context.Context, req *DeleteGroupRequest) (*DeleteGroupResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil || req.GroupId == "" {
		return nil, ErrGroupIdEmpty
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)
	l.Debugf("删除人员库请求: group_id=%s", req.GroupId)

	// 创建腾讯云请求
	request := iai.NewDeleteGroupRequest()
	request.GroupId = common.StringPtr(req.GroupId)

	// 执行请求
	response, err := c.client.DeleteGroupWithContext(ctx, request)
	if err != nil {
		l.Errorf("删除人员库失败: %v, 耗时: %v", err, time.Since(startTime))
		return nil, c.wrapTencentError("DeleteGroup", err)
	}

	duration := time.Since(startTime)

	deleteResult := &DeleteGroupResponse{
		RequestId: *response.Response.RequestId,
	}

	l.Infof("删除人员库成功: group_id=%s, 耗时: %v, 请求ID: %s",
		req.GroupId, duration, deleteResult.RequestId)

	return deleteResult, nil
}

// CreatePerson 创建人员
func (c *Client) CreatePerson(ctx context.Context, req *CreatePersonRequest) (*CreatePersonResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil {
		return nil, ErrInvalidRequest
	}

	if req.GroupId == "" || req.PersonId == "" || req.PersonName == "" {
		return nil, ErrInvalidRequest
	}

	if req.Image == "" && req.Url == "" {
		return nil, ErrImageEmpty
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)
	l.Debugf("create person request",
		"group_id", req.GroupId,
		"person_id", req.PersonId,
		"person_name", req.PersonName)

	// 创建腾讯云请求
	request := iai.NewCreatePersonRequest()
	request.GroupId = common.StringPtr(req.GroupId)
	request.PersonId = common.StringPtr(req.PersonId)
	request.PersonName = common.StringPtr(req.PersonName)

	// 设置图片参数
	if req.Image != "" {
		request.Image = common.StringPtr(req.Image)
	} else if req.Url != "" {
		request.Url = common.StringPtr(req.Url)
	}

	// 设置其他参数
	if req.UniquePersonControl > 0 {
		request.UniquePersonControl = common.Uint64Ptr(uint64(req.UniquePersonControl))
	}
	if req.QualityControl > 0 {
		request.QualityControl = common.Uint64Ptr(uint64(req.QualityControl))
	}
	if req.NeedRotateDetection {
		request.NeedRotateDetection = common.Uint64Ptr(1)
	}

	// 执行请求
	response, err := c.client.CreatePersonWithContext(ctx, request)
	if err != nil {
		l.Errorf("创建人员失败: %v, 耗时: %v", err, time.Since(startTime))
		return nil, c.wrapTencentError("CreatePerson", err)
	}

	duration := time.Since(startTime)

	// 转换响应
	createResult := c.convertCreatePersonResponse(response)

	l.Infof("create person success",
		"group_id", req.GroupId,
		"person_id", req.PersonId,
		"face_id", createResult.FaceId,
		"duration", duration,
		"request_id", createResult.RequestId)

	return createResult, nil
}

// DeletePerson 删除人员
func (c *Client) DeletePerson(ctx context.Context, req *DeletePersonRequest) (*DeletePersonResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil || req.GroupId == "" || req.PersonId == "" {
		return nil, ErrInvalidRequest
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)
	l.Debugf("delete person request", "group_id", req.GroupId, "person_id", req.PersonId)

	// 创建腾讯云请求
	request := iai.NewDeletePersonRequest()
	request.PersonId = common.StringPtr(req.PersonId)

	// 执行请求
	response, err := c.client.DeletePersonWithContext(ctx, request)
	if err != nil {
		l.Errorf("delete person failed", "error", err, "duration", time.Since(startTime))
		return nil, c.wrapTencentError("DeletePerson", err)
	}

	duration := time.Since(startTime)

	deleteResult := &DeletePersonResponse{
		RequestId: *response.Response.RequestId,
	}

	l.Infof("delete person success",
		"group_id", req.GroupId,
		"person_id", req.PersonId,
		"duration", duration,
		"request_id", deleteResult.RequestId)

	return deleteResult, nil
}

// AddFace 添加人脸
func (c *Client) AddFace(ctx context.Context, req *AddFaceRequest) (*AddFaceResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil || req.PersonId == "" {
		return nil, ErrInvalidRequest
	}

	if len(req.Images) == 0 && len(req.Urls) == 0 {
		return nil, ErrImageEmpty
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)
	l.Debugf("add face request",
		"person_id", req.PersonId,
		"image_count", len(req.Images),
		"url_count", len(req.Urls))

	// 创建腾讯云请求
	request := iai.NewCreateFaceRequest()
	request.PersonId = common.StringPtr(req.PersonId)

	// 设置图片参数
	if len(req.Images) > 0 {
		images := make([]*string, len(req.Images))
		for i, img := range req.Images {
			images[i] = common.StringPtr(img)
		}
		request.Images = images
	} else if len(req.Urls) > 0 {
		urls := make([]*string, len(req.Urls))
		for i, url := range req.Urls {
			urls[i] = common.StringPtr(url)
		}
		request.Urls = urls
	}

	// 设置其他参数
	if req.FaceMatchThreshold > 0 {
		request.FaceMatchThreshold = common.Float64Ptr(req.FaceMatchThreshold)
	}
	if req.QualityControl > 0 {
		request.QualityControl = common.Uint64Ptr(uint64(req.QualityControl))
	}
	if req.NeedRotateDetection {
		request.NeedRotateDetection = common.Uint64Ptr(1)
	}

	// 执行请求
	response, err := c.client.CreateFaceWithContext(ctx, request)
	if err != nil {
		l.Errorf("add face failed", "error", err, "duration", time.Since(startTime))
		return nil, c.wrapTencentError("AddFace", err)
	}

	duration := time.Since(startTime)

	// 转换响应
	addResult := c.convertAddFaceResponse(response)

	l.Infof("add face success",
		"person_id", req.PersonId,
		"suc_face_num", addResult.SucFaceNum,
		"duration", duration,
		"request_id", addResult.RequestId)

	return addResult, nil
}

// DeleteFace 删除人脸
func (c *Client) DeleteFace(ctx context.Context, req *DeleteFaceRequest) (*DeleteFaceResponse, error) {
	if err := c.checkClient(); err != nil {
		return nil, err
	}

	if req == nil || req.PersonId == "" || len(req.FaceIds) == 0 {
		return nil, ErrInvalidRequest
	}

	var (
		l         = log.Context(ctx)
		startTime = time.Now()
	)

	// 创建腾讯云请求
	request := iai.NewDeleteFaceRequest()
	request.PersonId = common.StringPtr(req.PersonId)

	faceIds := make([]*string, len(req.FaceIds))
	for i, id := range req.FaceIds {
		faceIds[i] = common.StringPtr(id)
	}
	request.FaceIds = faceIds

	// 执行请求
	response, err := c.client.DeleteFaceWithContext(ctx, request)
	if err != nil {
		l.Errorf("delete face failed", "error", err, "duration", time.Since(startTime))
		return nil, c.wrapTencentError("DeleteFace", err)
	}

	duration := time.Since(startTime)

	// 转换响应
	deleteResult := c.convertDeleteFaceResponse(response)

	l.Infof("delete face success",
		"person_id", req.PersonId,
		"suc_deleted_num", deleteResult.SucDeletedNum,
		"duration", duration,
		"request_id", deleteResult.RequestId)

	return deleteResult, nil
}

// BatchCreatePersons 批量创建人员
func (c *Client) BatchCreatePersons(ctx context.Context, requests []*CreatePersonRequest) ([]*CreatePersonResponse, []error) {
	if len(requests) == 0 {
		return nil, []error{ErrInvalidRequest}
	}

	l := log.Context(ctx)
	l.Infof("批量创建人员开始: 数量=%d", len(requests))

	responses := make([]*CreatePersonResponse, len(requests))
	errors := make([]error, len(requests))

	// 并发处理多个请求
	var wg sync.WaitGroup
	var mu sync.Mutex

	for i, req := range requests {
		wg.Add(1)
		go func(index int, request *CreatePersonRequest) {
			defer wg.Done()

			resp, err := c.CreatePerson(ctx, request)

			mu.Lock()
			responses[index] = resp
			errors[index] = err
			mu.Unlock()
		}(i, req)
	}

	wg.Wait()

	// 统计成功和失败数量
	successCount := 0
	for _, err := range errors {
		if err == nil {
			successCount++
		}
	}

	l.Infof("批量创建人员完成: 总数=%d, 成功=%d, 失败=%d",
		len(requests), successCount, len(requests)-successCount)

	return responses, errors
}

// BatchAddFaces 批量添加人脸
func (c *Client) BatchAddFaces(ctx context.Context, requests []*AddFaceRequest) ([]*AddFaceResponse, []error) {
	if len(requests) == 0 {
		return nil, []error{ErrInvalidRequest}
	}

	l := log.Context(ctx)
	l.Infof("批量添加人脸开始: 数量=%d", len(requests))

	responses := make([]*AddFaceResponse, len(requests))
	errors := make([]error, len(requests))

	// 并发处理多个请求
	var wg sync.WaitGroup
	var mu sync.Mutex

	for i, req := range requests {
		wg.Add(1)
		go func(index int, request *AddFaceRequest) {
			defer wg.Done()

			resp, err := c.AddFace(ctx, request)

			mu.Lock()
			responses[index] = resp
			errors[index] = err
			mu.Unlock()
		}(i, req)
	}

	wg.Wait()

	// 统计成功和失败数量
	successCount := 0
	for _, err := range errors {
		if err == nil {
			successCount++
		}
	}

	l.Infof("批量添加人脸完成: 总数=%d, 成功=%d, 失败=%d",
		len(requests), successCount, len(requests)-successCount)

	return responses, errors
}

// GetConfig 获取当前配置的副本
func (c *Client) GetConfig() *Config {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 返回配置的副本以避免外部修改
	configCopy := *c.config

	return &configCopy
}

// checkClient 检查客户端状态
func (c *Client) checkClient() error {
	c.mu.RLock()
	defer c.mu.RUnlock()

	if c.closed {
		return ErrClientClosed
	}
	if c.client == nil {
		return ErrClientNotInitialized
	}
	return nil
}

// wrapTencentError 包装腾讯云错误
func (c *Client) wrapTencentError(operation string, err error) error {
	if err == nil {
		return nil
	}

	// 尝试解析腾讯云SDK错误
	errStr := err.Error()

	// 解析腾讯云SDK错误格式：[TencentCloudSDKError] Code=xxx, Message=xxx, RequestId=xxx
	var code, message, requestId string

	// 简单的字符串解析
	if strings.Contains(errStr, "[TencentCloudSDKError]") &&
		strings.Contains(errStr, "Code=") &&
		strings.Contains(errStr, "Message=") &&
		strings.Contains(errStr, "RequestId=") {

		// 提取Code
		if codeStart := strings.Index(errStr, "Code="); codeStart != -1 {
			codeStart += 5 // "Code="的长度
			if codeEnd := strings.Index(errStr[codeStart:], ","); codeEnd != -1 {
				code = strings.TrimSpace(errStr[codeStart : codeStart+codeEnd])
			} else {
				code = strings.TrimSpace(errStr[codeStart:])
			}
		}

		// 提取Message
		if msgStart := strings.Index(errStr, "Message="); msgStart != -1 {
			msgStart += 8 // "Message="的长度
			if msgEnd := strings.Index(errStr[msgStart:], ", RequestId="); msgEnd != -1 {
				message = strings.TrimSpace(errStr[msgStart : msgStart+msgEnd])
			} else {
				message = strings.TrimSpace(errStr[msgStart:])
			}
		}

		// 提取RequestId
		if reqStart := strings.Index(errStr, "RequestId="); reqStart != -1 {
			reqStart += 10 // "RequestId="的长度
			if reqEnd := strings.Index(errStr[reqStart:], ","); reqEnd != -1 {
				requestId = strings.TrimSpace(errStr[reqStart : reqStart+reqEnd])
			} else {
				requestId = strings.TrimSpace(errStr[reqStart:])
			}
		}
	}

	// 如果解析失败，使用原始错误信息
	if code == "" {
		code = "Unknown"
	}
	if message == "" {
		message = errStr
	}

	return NewTencentCloudError(operation, code, message, requestId)
}

// convertDetectFaceResponse 转换人脸检测响应
func (c *Client) convertDetectFaceResponse(resp *iai.DetectFaceResponse) *DetectFaceResponse {
	result := &DetectFaceResponse{
		RequestId: *resp.Response.RequestId,
	}

	if resp.Response.ImageWidth != nil {
		result.ImageWidth = int(*resp.Response.ImageWidth)
	}
	if resp.Response.ImageHeight != nil {
		result.ImageHeight = int(*resp.Response.ImageHeight)
	}
	if resp.Response.FaceModelVersion != nil {
		result.FaceModelVersion = *resp.Response.FaceModelVersion
	}

	// 转换人脸信息
	if resp.Response.FaceInfos != nil {
		result.FaceInfos = make([]FaceInfo, len(resp.Response.FaceInfos))
		for i, faceInfo := range resp.Response.FaceInfos {
			result.FaceInfos[i] = c.convertFaceInfo(faceInfo)
		}
	}

	return result
}

// convertSearchFacesResponse 转换人脸搜索响应
func (c *Client) convertSearchFacesResponse(resp *iai.SearchFacesResponse) *SearchFacesResponse {
	result := &SearchFacesResponse{
		RequestId: *resp.Response.RequestId,
	}

	if resp.Response.FaceNum != nil {
		result.FaceNum = int(*resp.Response.FaceNum)
	}
	if resp.Response.FaceModelVersion != nil {
		result.FaceModelVersion = *resp.Response.FaceModelVersion
	}

	// 转换搜索结果
	if resp.Response.Results != nil {
		result.Results = make([]SearchResult, len(resp.Response.Results))
		for i, searchResult := range resp.Response.Results {
			result.Results[i] = c.convertSearchResult(searchResult)
		}
	}

	return result
}

// convertFaceInfo 转换人脸信息
func (c *Client) convertFaceInfo(faceInfo *iai.FaceInfo) FaceInfo {
	result := FaceInfo{}

	// 转换人脸矩形框
	if faceInfo.X != nil && faceInfo.Y != nil && faceInfo.Width != nil && faceInfo.Height != nil {
		result.FaceRect = FaceRect{
			X:      int(*faceInfo.X),
			Y:      int(*faceInfo.Y),
			Width:  int(*faceInfo.Width),
			Height: int(*faceInfo.Height),
		}
	}

	// 转换人脸质量信息
	if faceInfo.FaceQualityInfo != nil {
		result.Quality = &FaceQuality{}
		if faceInfo.FaceQualityInfo.Score != nil {
			result.Quality.Score = float64(*faceInfo.FaceQualityInfo.Score)
		}
		if faceInfo.FaceQualityInfo.Sharpness != nil {
			result.Quality.Sharpness = float64(*faceInfo.FaceQualityInfo.Sharpness)
		}
		if faceInfo.FaceQualityInfo.Brightness != nil {
			result.Quality.Brightness = float64(*faceInfo.FaceQualityInfo.Brightness)
		}
	}

	// 转换人脸属性信息
	if faceInfo.FaceAttributesInfo != nil {
		result.Attributes = &FaceAttributes{}
		if faceInfo.FaceAttributesInfo.Gender != nil {
			result.Attributes.Gender = int(*faceInfo.FaceAttributesInfo.Gender)
		}
		if faceInfo.FaceAttributesInfo.Age != nil {
			result.Attributes.Age = int(*faceInfo.FaceAttributesInfo.Age)
		}
		if faceInfo.FaceAttributesInfo.Expression != nil {
			result.Attributes.Expression = int(*faceInfo.FaceAttributesInfo.Expression)
		}
		if faceInfo.FaceAttributesInfo.Beauty != nil {
			result.Attributes.Beauty = float64(*faceInfo.FaceAttributesInfo.Beauty)
		}
		if faceInfo.FaceAttributesInfo.Hat != nil {
			result.Attributes.Hat = *faceInfo.FaceAttributesInfo.Hat
		}
		if faceInfo.FaceAttributesInfo.Mask != nil {
			result.Attributes.Mask = *faceInfo.FaceAttributesInfo.Mask
		}
		if faceInfo.FaceAttributesInfo.Glass != nil {
			result.Attributes.Glass = *faceInfo.FaceAttributesInfo.Glass
		}
	}

	return result
}

// convertSearchResult 转换搜索结果
func (c *Client) convertSearchResult(searchResult *iai.Result) SearchResult {
	result := SearchResult{}

	// 转换人脸矩形框
	if searchResult.FaceRect != nil {
		result.FaceRect = FaceRect{
			X:      int(*searchResult.FaceRect.X),
			Y:      int(*searchResult.FaceRect.Y),
			Width:  int(*searchResult.FaceRect.Width),
			Height: int(*searchResult.FaceRect.Height),
		}
	}

	// 转换候选人列表
	if searchResult.Candidates != nil {
		result.Candidates = make([]SearchCandidate, len(searchResult.Candidates))
		for i, candidate := range searchResult.Candidates {
			result.Candidates[i] = SearchCandidate{
				PersonId: *candidate.PersonId,
				Score:    float64(*candidate.Score),
			}
			if candidate.PersonName != nil {
				result.Candidates[i].PersonName = *candidate.PersonName
			}
			if candidate.FaceId != nil {
				result.Candidates[i].FaceId = *candidate.FaceId
			}
		}
	}

	if searchResult.RetCode != nil {
		result.RetCode = int(*searchResult.RetCode)
	}

	return result
}

// convertGroupInfo 转换人员库信息
func (c *Client) convertGroupInfo(resp *iai.GetGroupInfoResponseParams) GroupInfo {
	info := GroupInfo{}

	if resp.GroupId != nil {
		info.GroupId = *resp.GroupId
	}
	if resp.GroupName != nil {
		info.GroupName = *resp.GroupName
	}
	if resp.Tag != nil {
		info.Tag = *resp.Tag
	}
	if resp.FaceModelVersion != nil {
		info.FaceModelVersion = *resp.FaceModelVersion
	}
	if resp.CreationTimestamp != nil {
		info.CreationTimestamp = int64(*resp.CreationTimestamp)
		info.CreatedAt = time.Unix(int64(*resp.CreationTimestamp), 0)
	}

	return info
}

// convertCreatePersonResponse 转换创建人员响应
func (c *Client) convertCreatePersonResponse(resp *iai.CreatePersonResponse) *CreatePersonResponse {
	result := &CreatePersonResponse{
		RequestId: *resp.Response.RequestId,
	}

	if resp.Response.FaceId != nil {
		result.FaceId = *resp.Response.FaceId
	}
	if resp.Response.FaceRect != nil {
		result.FaceRect = FaceRect{
			X:      int(*resp.Response.FaceRect.X),
			Y:      int(*resp.Response.FaceRect.Y),
			Width:  int(*resp.Response.FaceRect.Width),
			Height: int(*resp.Response.FaceRect.Height),
		}
	}
	if resp.Response.SimilarPersonId != nil {
		result.SimilarPersonId = *resp.Response.SimilarPersonId
	}
	if resp.Response.FaceModelVersion != nil {
		result.FaceModelVersion = *resp.Response.FaceModelVersion
	}

	return result
}

// convertAddFaceResponse 转换添加人脸响应
func (c *Client) convertAddFaceResponse(resp *iai.CreateFaceResponse) *AddFaceResponse {
	result := &AddFaceResponse{
		RequestId: *resp.Response.RequestId,
	}

	if resp.Response.SucFaceNum != nil {
		result.SucFaceNum = int(*resp.Response.SucFaceNum)
	}
	if resp.Response.SucFaceIds != nil {
		result.SucFaceIds = make([]string, len(resp.Response.SucFaceIds))
		for i, id := range resp.Response.SucFaceIds {
			result.SucFaceIds[i] = *id
		}
	}
	if resp.Response.RetCode != nil {
		result.RetCode = make([]int, len(resp.Response.RetCode))
		for i, code := range resp.Response.RetCode {
			result.RetCode[i] = int(*code)
		}
	}
	if resp.Response.SucIndexes != nil {
		result.SucIndexes = make([]int, len(resp.Response.SucIndexes))
		for i, idx := range resp.Response.SucIndexes {
			result.SucIndexes[i] = int(*idx)
		}
	}
	if resp.Response.SucFaceRects != nil {
		result.SucFaceRects = make([]FaceRect, len(resp.Response.SucFaceRects))
		for i, rect := range resp.Response.SucFaceRects {
			result.SucFaceRects[i] = FaceRect{
				X:      int(*rect.X),
				Y:      int(*rect.Y),
				Width:  int(*rect.Width),
				Height: int(*rect.Height),
			}
		}
	}
	if resp.Response.FaceModelVersion != nil {
		result.FaceModelVersion = *resp.Response.FaceModelVersion
	}

	return result
}

// convertDeleteFaceResponse 转换删除人脸响应
func (c *Client) convertDeleteFaceResponse(resp *iai.DeleteFaceResponse) *DeleteFaceResponse {
	result := &DeleteFaceResponse{
		RequestId: *resp.Response.RequestId,
	}

	if resp.Response.SucDeletedNum != nil {
		result.SucDeletedNum = int(*resp.Response.SucDeletedNum)
	}
	if resp.Response.SucFaceIds != nil {
		result.SucFaceIds = make([]string, len(resp.Response.SucFaceIds))
		for i, id := range resp.Response.SucFaceIds {
			result.SucFaceIds[i] = *id
		}
	}

	return result
}
