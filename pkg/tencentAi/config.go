package tencentAi

// Config 腾讯云IAI服务配置
type Config struct {
	// 腾讯云基础配置
	SecretId  string `json:"secret_id" yaml:"secret_id"`   // 腾讯云SecretId
	SecretKey string `json:"secret_key" yaml:"secret_key"` // 腾讯云SecretKey
	Region    string `json:"region" yaml:"region"`         // 地域，如 ap-beijing

	// IAI服务配置
	Endpoint         string `json:"endpoint" yaml:"endpoint"`                     // IAI服务端点，默认为 iai.tencentcloudapi.com
	FaceModelVersion string `json:"face_model_version" yaml:"face_model_version"` // 人脸识别算法模型版本，默认3.0

	TimeOutSec int `json:"timeout_sec" yaml:"timeout_sec"`

	// 人脸搜索配置
	SearchConfig SearchConfig `json:"search_config" yaml:"search_config"`

	// 频率限制配置
	RateLimitConfig RateLimitConfig `json:"rate_limit_config" yaml:"rate_limit_config"`

	// 缓存配置
	CacheConfig CacheConfig `json:"cache_config" yaml:"cache_config"`
}

// SearchConfig 人脸搜索配置 - 仅保留实际使用的字段
type SearchConfig struct {
	DefaultMatchThreshold float64 `json:"default_match_threshold" yaml:"default_match_threshold"` // 默认人脸匹配阈值
	MaxFaceNum            int     `json:"max_face_num" yaml:"max_face_num"`                       // 最多识别人脸数
	MaxPersonNum          int     `json:"max_person_num" yaml:"max_person_num"`                   // 返回最相似人员数
	MinFaceSize           int     `json:"min_face_size" yaml:"min_face_size"`                     // 最小人脸尺寸
}

// RateLimitConfig 频率限制配置
type RateLimitConfig struct {
	UserLimitPerMin int `json:"user_limit_per_min" yaml:"user_limit_per_min"` // 用户每分钟限制次数
	UserLimitPerDay int `json:"user_limit_per_day" yaml:"user_limit_per_day"` // 用户每日限制次数
	IPLimitPerMin   int `json:"ip_limit_per_min" yaml:"ip_limit_per_min"`     // IP每分钟限制次数
	AlertThreshold  int `json:"alert_threshold" yaml:"alert_threshold"`       // 报警阈值（百分比）
}

// CacheConfig 缓存配置
type CacheConfig struct {
	TTL int `json:"ttl" yaml:"ttl"` // 缓存TTL（秒）
}
