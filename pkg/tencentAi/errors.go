package tencentAi

import (
	"errors"
	"fmt"
	"strings"
)

// 配置错误
var (
	ErrInvalidSecretId  = errors.New("tencentAI: invalid secret_id")
	ErrInvalidSecretKey = errors.New("tencentAI: invalid secret_key")
	ErrInvalidRegion    = errors.New("tencentAI: invalid region")
	ErrInvalidEndpoint  = errors.New("tencentAI: invalid endpoint")
)

// 客户端错误
var (
	ErrClientNotInitialized = errors.New("tencentAI: client not initialized")
	ErrClientClosed         = errors.New("tencentAI: client is closed")
	ErrInvalidRequest       = errors.New("tencentAI: invalid request parameters")
	ErrEmptyResponse        = errors.New("tencentAI: empty response")
)

// 人员库错误
var (
	ErrGroupNotFound     = errors.New("tencentAI: group not found")
	ErrGroupAlreadyExist = errors.New("tencentAI: group already exists")
	ErrGroupIdEmpty      = errors.New("tencentAI: group id is empty")
	ErrGroupNameEmpty    = errors.New("tencentAI: group name is empty")
	ErrGroupFull         = errors.New("tencentAI: group is full")
)

// 人员错误
var (
	ErrPersonNotFound     = errors.New("tencentAI: person not found")
	ErrPersonAlreadyExist = errors.New("tencentAI: person already exists")
	ErrPersonIdEmpty      = errors.New("tencentAI: person id is empty")
	ErrPersonNameEmpty    = errors.New("tencentAI: person name is empty")
)

// 人脸错误
var (
	ErrFaceNotFound       = errors.New("tencentAI: face not found")
	ErrFaceAlreadyExist   = errors.New("tencentAI: face already exists")
	ErrFaceIdEmpty        = errors.New("tencentAI: face id is empty")
	ErrNoFaceDetected     = errors.New("tencentAI: no face detected in image")
	ErrFaceQualityTooLow  = errors.New("tencentAI: face quality too low")
	ErrFaceSizeTooSmall   = errors.New("tencentAI: face size too small")
	ErrMultipleFacesFound = errors.New("tencentAI: multiple faces found in image")
)

// 图片错误
var (
	ErrImageEmpty         = errors.New("tencentAI: image is empty")
	ErrImageTooLarge      = errors.New("tencentAI: image size too large")
	ErrImageFormatInvalid = errors.New("tencentAI: invalid image format")
	ErrImageDecodeFailed  = errors.New("tencentAI: image decode failed")
	ErrImageUrlInvalid    = errors.New("tencentAI: invalid image url")
)

// 搜索错误
var (
	ErrSearchTimeout          = errors.New("tencentAI: search timeout")
	ErrSearchNoResult         = errors.New("tencentAI: no search result")
	ErrSearchThresholdInvalid = errors.New("tencentAI: invalid search threshold")
)

// 重试错误
var (
	ErrMaxRetriesExceeded = errors.New("tencentAI: max retries exceeded")
	ErrRetryTimeout       = errors.New("tencentAI: retry timeout")
)

// 网络错误
var (
	ErrNetworkTimeout     = errors.New("tencentAI: network timeout")
	ErrNetworkUnavailable = errors.New("tencentAI: network unavailable")
	ErrRateLimitExceeded  = errors.New("tencentAI: rate limit exceeded")
)

// TencentCloudError 腾讯云API错误包装
type TencentCloudError struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"request_id"`
	Operation string `json:"operation"`
}

func (e *TencentCloudError) Error() string {
	return fmt.Sprintf("tencentAI: operation=%s, code=%s, message=%s, request_id=%s",
		e.Operation, e.Code, e.Message, e.RequestId)
}

// NewTencentCloudError 创建腾讯云API错误
func NewTencentCloudError(operation, code, message, requestId string) *TencentCloudError {
	return &TencentCloudError{
		Code:      code,
		Message:   message,
		RequestId: requestId,
		Operation: operation,
	}
}

// IsRetryableError 判断是否为可重试错误
func IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	// 网络相关错误可重试
	switch err {
	case ErrNetworkTimeout, ErrNetworkUnavailable:
		return true
	}

	// 腾讯云API错误码判断
	if tcErr, ok := err.(*TencentCloudError); ok {
		switch tcErr.Code {
		case "RequestLimitExceeded", "InternalError", "RequestTimeout":
			return true
		case "ResourceUnavailable", "ThrottlingException":
			return true
		case "ServerError", "ServiceUnavailable":
			return true
		}
	}

	return false
}

// IsFatalError 判断是否为致命错误（不应重试）
func IsFatalError(err error) bool {
	if err == nil {
		return false
	}

	// 配置错误不应重试
	switch err {
	case ErrInvalidSecretId, ErrInvalidSecretKey, ErrInvalidRegion:
		return true
	case ErrInvalidRequest, ErrImageFormatInvalid:
		return true
	}

	// 腾讯云API错误码判断
	if tcErr, ok := err.(*TencentCloudError); ok {
		switch tcErr.Code {
		case "AuthFailure", "UnauthorizedOperation", "InvalidParameter":
			return true
		case "LimitExceeded", "ResourceNotFound", "InvalidParameterValue":
			return true
		}
	}

	return false
}

// IsSkippableError 判断是否为应该跳过的错误（不算真正的失败）
func IsSkippableError(err error) bool {
	if err == nil {
		return false
	}

	// 直接错误类型判断
	switch err {
	case ErrNoFaceDetected, ErrFaceSizeTooSmall, ErrFaceQualityTooLow:
		return true
	}

	// 检查错误字符串中是否包含可跳过的错误码
	errStr := err.Error()

	// 检查腾讯云API错误码
	skippableCodes := []string{
		"InvalidParameterValue.NoFaceInPhoto",
		"FailedOperation.FaceSizeTooSmall",
		"InvalidParameterValue.FaceQualityNotQualified",
		"FailedOperation.ImageFaceNotFound",
	}

	for _, code := range skippableCodes {
		if strings.Contains(errStr, code) {
			return true
		}
	}

	// 腾讯云API错误结构判断
	if tcErr, ok := err.(*TencentCloudError); ok {
		switch tcErr.Code {
		case "InvalidParameterValue.NoFaceInPhoto":
			// 图片中没有人脸
			return true
		case "FailedOperation.FaceSizeTooSmall":
			// 人脸框大小小于MinFaceSize设置
			return true
		case "InvalidParameterValue.FaceQualityNotQualified":
			// 人脸质量不合格
			return true
		case "FailedOperation.ImageFaceNotFound":
			// 图片中未检测到人脸
			return true
		}
	}

	return false
}

// WrapError 包装错误并添加上下文
func WrapError(err error, operation string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("tencentAI: %s failed: %w", operation, err)
}
