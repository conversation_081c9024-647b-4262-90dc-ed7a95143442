package tencentAi

import (
	"time"
)

// FaceRect 人脸矩形框
type FaceRect struct {
	X      int `json:"x"`      // 左上角X坐标
	Y      int `json:"y"`      // 左上角Y坐标
	Width  int `json:"width"`  // 宽度
	Height int `json:"height"` // 高度
}

// FaceQuality 人脸质量信息
type FaceQuality struct {
	Score      float64 `json:"score"`      // 质量分数 (0-100)
	Sharpness  float64 `json:"sharpness"`  // 清晰度 (0-100)
	Brightness float64 `json:"brightness"` // 光照度 (0-100)
}

// FaceAttributes 人脸属性信息
type FaceAttributes struct {
	Gender     int     `json:"gender"`     // 性别：0=未知, 1=男, 2=女
	Age        int     `json:"age"`        // 年龄
	Expression int     `json:"expression"` // 表情
	Beauty     float64 `json:"beauty"`     // 颜值分数 (0-100)
	Hat        bool    `json:"hat"`        // 是否戴帽子
	Mask       bool    `json:"mask"`       // 是否戴口罩
	Glass      bool    `json:"glass"`      // 是否戴眼镜
}

// FaceInfo 人脸信息
type FaceInfo struct {
	FaceId     string          `json:"face_id"`    // 人脸ID
	FaceRect   FaceRect        `json:"face_rect"`  // 人脸矩形框
	Quality    *FaceQuality    `json:"quality"`    // 人脸质量
	Attributes *FaceAttributes `json:"attributes"` // 人脸属性
}

// DetectFaceRequest 人脸检测请求
type DetectFaceRequest struct {
	Image                string `json:"image,omitempty"`              // base64编码的图片
	Url                  string `json:"url,omitempty"`                // 图片URL
	MaxFaceNum           int    `json:"max_face_num"`                 // 最多检测人脸数，默认1
	MinFaceSize          int    `json:"min_face_size"`                // 最小人脸尺寸，默认34
	NeedFaceAttributes   bool   `json:"need_face_attributes"`         // 是否需要人脸属性
	NeedQualityDetection bool   `json:"need_quality_detection"`       // 是否需要质量检测
	NeedRotateDetection  bool   `json:"need_rotate_detection"`        // 是否开启旋转检测
	FaceModelVersion     string `json:"face_model_version,omitempty"` // 人脸识别算法版本
}

// DetectFaceResponse 人脸检测响应
type DetectFaceResponse struct {
	ImageWidth       int        `json:"image_width"`        // 图片宽度
	ImageHeight      int        `json:"image_height"`       // 图片高度
	FaceInfos        []FaceInfo `json:"face_infos"`         // 人脸信息列表
	FaceModelVersion string     `json:"face_model_version"` // 算法模型版本
	RequestId        string     `json:"request_id"`         // 请求ID
}

// GroupInfo 人员库信息
type GroupInfo struct {
	GroupId           string    `json:"group_id"`           // 人员库ID
	GroupName         string    `json:"group_name"`         // 人员库名称
	GroupDescription  string    `json:"group_description"`  // 人员库描述
	Tag               string    `json:"tag"`                // 标签
	FaceModelVersion  string    `json:"face_model_version"` // 算法模型版本
	CreationTimestamp int64     `json:"creation_timestamp"` // 创建时间戳
	CreatedAt         time.Time `json:"created_at"`         // 创建时间
	UpdatedAt         time.Time `json:"updated_at"`         // 更新时间
}

// CreateGroupRequest 创建人员库请求
type CreateGroupRequest struct {
	GroupId             string   `json:"group_id"`                        // 人员库ID
	GroupName           string   `json:"group_name"`                      // 人员库名称
	GroupExDescriptions []string `json:"group_ex_descriptions,omitempty"` // 自定义描述字段
	Tag                 string   `json:"tag,omitempty"`                   // 标签
	FaceModelVersion    string   `json:"face_model_version,omitempty"`    // 算法模型版本
}

// CreateGroupResponse 创建人员库响应
type CreateGroupResponse struct {
	FaceModelVersion string `json:"face_model_version"` // 算法模型版本
	RequestId        string `json:"request_id"`         // 请求ID
}

// GetGroupInfoRequest 获取人员库信息请求
type GetGroupInfoRequest struct {
	GroupId string `json:"group_id"` // 人员库ID
}

// GetGroupInfoResponse 获取人员库信息响应
type GetGroupInfoResponse struct {
	GroupInfo
	RequestId string `json:"request_id"` // 请求ID
}

// DeleteGroupRequest 删除人员库请求
type DeleteGroupRequest struct {
	GroupId string `json:"group_id"` // 人员库ID
}

// DeleteGroupResponse 删除人员库响应
type DeleteGroupResponse struct {
	RequestId string `json:"request_id"` // 请求ID
}

// PersonInfo 人员信息
type PersonInfo struct {
	PersonId     string            `json:"person_id"`     // 人员ID
	PersonName   string            `json:"person_name"`   // 人员名称
	Gender       int               `json:"gender"`        // 性别
	PersonExtras map[string]string `json:"person_extras"` // 自定义描述字段
	FaceIds      []string          `json:"face_ids"`      // 人脸ID列表
	CreatedAt    time.Time         `json:"created_at"`    // 创建时间
	UpdatedAt    time.Time         `json:"updated_at"`    // 更新时间
}

// CreatePersonRequest 创建人员请求
type CreatePersonRequest struct {
	GroupId             string            `json:"group_id"`                        // 人员库ID
	PersonId            string            `json:"person_id"`                       // 人员ID
	PersonName          string            `json:"person_name"`                     // 人员名称
	Image               string            `json:"image,omitempty"`                 // base64编码的图片
	Url                 string            `json:"url,omitempty"`                   // 图片URL
	PersonExtras        map[string]string `json:"person_extras,omitempty"`         // 自定义描述字段
	UniquePersonControl int               `json:"unique_person_control,omitempty"` // 同一人判断控制
	QualityControl      int               `json:"quality_control,omitempty"`       // 质量控制
	NeedRotateDetection bool              `json:"need_rotate_detection,omitempty"` // 是否开启旋转检测
}

// CreatePersonResponse 创建人员响应
type CreatePersonResponse struct {
	FaceId           string   `json:"face_id"`            // 人脸ID
	FaceRect         FaceRect `json:"face_rect"`          // 人脸矩形框
	SimilarPersonId  string   `json:"similar_person_id"`  // 疑似同一人的PersonId
	FaceModelVersion string   `json:"face_model_version"` // 算法模型版本
	RequestId        string   `json:"request_id"`         // 请求ID
}

// DeletePersonRequest 删除人员请求
type DeletePersonRequest struct {
	GroupId  string `json:"group_id"`  // 人员库ID
	PersonId string `json:"person_id"` // 人员ID
}

// DeletePersonResponse 删除人员响应
type DeletePersonResponse struct {
	RequestId string `json:"request_id"` // 请求ID
}

// AddFaceRequest 添加人脸请求
type AddFaceRequest struct {
	PersonId            string   `json:"person_id"`                       // 人员ID
	Images              []string `json:"images,omitempty"`                // base64编码的图片列表
	Urls                []string `json:"urls,omitempty"`                  // 图片URL列表
	FaceMatchThreshold  float64  `json:"face_match_threshold,omitempty"`  // 人脸相似度阈值
	QualityControl      int      `json:"quality_control,omitempty"`       // 质量控制
	NeedRotateDetection bool     `json:"need_rotate_detection,omitempty"` // 是否开启旋转检测
}

// AddFaceResponse 添加人脸响应
type AddFaceResponse struct {
	SucFaceNum       int        `json:"suc_face_num"`       // 成功添加的人脸数量
	SucFaceIds       []string   `json:"suc_face_ids"`       // 成功添加的人脸ID列表
	RetCode          []int      `json:"ret_code"`           // 每张图片的返回码
	SucIndexes       []int      `json:"suc_indexes"`        // 成功的索引
	SucFaceRects     []FaceRect `json:"suc_face_rects"`     // 成功的人脸框
	FaceModelVersion string     `json:"face_model_version"` // 算法模型版本
	RequestId        string     `json:"request_id"`         // 请求ID
}

// DeleteFaceRequest 删除人脸请求
type DeleteFaceRequest struct {
	PersonId string   `json:"person_id"` // 人员ID
	FaceIds  []string `json:"face_ids"`  // 人脸ID列表
}

// DeleteFaceResponse 删除人脸响应
type DeleteFaceResponse struct {
	SucDeletedNum int      `json:"suc_deleted_num"` // 成功删除的人脸数量
	SucFaceIds    []string `json:"suc_face_ids"`    // 成功删除的人脸ID列表
	RequestId     string   `json:"request_id"`      // 请求ID
}

// Metrics 性能指标
type Metrics struct {
	RequestCount int64         `json:"request_count"` // 请求总数
	SuccessCount int64         `json:"success_count"` // 成功请求数
	ErrorCount   int64         `json:"error_count"`   // 错误请求数
	RetryCount   int64         `json:"retry_count"`   // 重试次数
	AvgLatency   time.Duration `json:"avg_latency"`   // 平均延迟
	MaxLatency   time.Duration `json:"max_latency"`   // 最大延迟
	MinLatency   time.Duration `json:"min_latency"`   // 最小延迟
}

// SearchCandidate 搜索候选人
type SearchCandidate struct {
	PersonId   string  `json:"person_id"`   // 人员ID
	PersonName string  `json:"person_name"` // 人员名称
	FaceId     string  `json:"face_id"`     // 人脸ID
	Score      float64 `json:"score"`       // 相似度分数
}

// SearchResult 搜索结果
type SearchResult struct {
	FaceRect   FaceRect          `json:"face_rect"`  // 人脸矩形框
	Candidates []SearchCandidate `json:"candidates"` // 候选人列表
	RetCode    int               `json:"ret_code"`   // 返回码
}

// SearchFacesRequest 人脸搜索请求
type SearchFacesRequest struct {
	GroupIds            []string `json:"group_ids"`             // 人员库ID列表
	Image               string   `json:"image,omitempty"`       // base64编码的图片
	Url                 string   `json:"url,omitempty"`         // 图片URL
	MaxFaceNum          int      `json:"max_face_num"`          // 最多检测人脸数，默认1
	MinFaceSize         int      `json:"min_face_size"`         // 最小人脸尺寸，默认34
	MaxPersonNum        int      `json:"max_person_num"`        // 最多返回的最相似人员数量，默认5
	QualityControl      int      `json:"quality_control"`       // 质量控制，默认0
	FaceMatchThreshold  float64  `json:"face_match_threshold"`  // 人脸相似度阈值
	NeedPersonInfo      bool     `json:"need_person_info"`      // 是否需要返回人员具体信息
	NeedRotateDetection bool     `json:"need_rotate_detection"` // 是否开启旋转检测
}

// SearchFacesResponse 人脸搜索响应
type SearchFacesResponse struct {
	FaceNum          int            `json:"face_num"`           // 识别出的人脸数量
	Results          []SearchResult `json:"results"`            // 搜索结果列表
	FaceModelVersion string         `json:"face_model_version"` // 算法模型版本
	RequestId        string         `json:"request_id"`         // 请求ID
}
