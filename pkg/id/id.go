package id

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"io"
	"strconv"
	"sync/atomic"
	"time"
)

type Business string

const (
	BusinessGoods   Business = "G"
	BusinessSKU     Business = "S"
	BusinessOrder   Business = "O"
	BusinessPayment Business = "P"
)

var (
	processID        = processUnique()
	goodsIDCounter   = readRandomUint32()
	skuIDCounter     = readRandomUint32()
	orderIDCounter   = readRandomUint32()
	paymentIDCounter = readRandomUint32()
)

func processUnique() string {
	var buf [5]byte
	_, err := rand.Read(buf[:])
	if err != nil {
		panic(err)
	}
	num := binary.BigEndian.Uint16(buf[:]) % 10000
	return fmt.Sprintf("%04d", num)
}

func readRandomUint32() uint32 {
	var b [4]byte
	_, err := io.ReadFull(rand.Reader, b[:])
	if err != nil {
		panic(fmt.Errorf("cannot initialize objectid package with crypto.rand.Reader: %w", err))
	}
	return (uint32(b[0]) << 0) | (uint32(b[1]) << 8) | (uint32(b[2]) << 16) | (uint32(b[3]) << 24)
}

func GenerateId(business Business, opts ...Option) string {
	o := &option{
		channel: "0",
	}

	for _, opt := range opts {
		opt(o)
	}

	var (
		now        = time.Now().UTC()
		counterStr = strconv.Itoa(int(now.UnixMilli()))
	)

	switch business {
	case BusinessGoods:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&goodsIDCounter, 1))
	case BusinessSKU:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&skuIDCounter, 1))
	case BusinessOrder:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&orderIDCounter, 1))
	case BusinessPayment:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&paymentIDCounter, 1))
	}
	return now.Format("20060102") + string(business) + o.channel + processID + counterStr
}
