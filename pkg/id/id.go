package id

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"io"
	"strconv"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
)

type Business string

const (
	BusinessFace   Business = "F"  // 人脸相关业务
	BusinessUpload Business = "U"  // 上传批次
	BusinessSearch Business = "SR" // 搜索批次
)

var (
	processID       = processUnique()
	faceIDCounter   = readRandomUint32()
	uploadIDCounter = readRandomUint32()
	searchIDCounter = readRandomUint32()
)

func processUnique() string {
	var buf [5]byte
	_, err := rand.Read(buf[:])
	if err != nil {
		panic(err)
	}
	num := binary.BigEndian.Uint16(buf[:]) % 10000
	return fmt.Sprintf("%04d", num)
}

func readRandomUint32() uint32 {
	var b [4]byte
	_, err := io.ReadFull(rand.Reader, b[:])
	if err != nil {
		panic(fmt.Errorf("cannot initialize objectid package with crypto.rand.Reader: %w", err))
	}
	return (uint32(b[0]) << 0) | (uint32(b[1]) << 8) | (uint32(b[2]) << 16) | (uint32(b[3]) << 24)
}

func GenerateId(business Business, opts ...Option) string {
	o := &option{
		channel: "0",
	}

	for _, opt := range opts {
		opt(o)
	}

	var (
		now        = time.Now().UTC()
		counterStr = strconv.Itoa(int(now.UnixMilli()))
	)

	switch business {
	case BusinessFace:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&faceIDCounter, 1))
	case BusinessUpload:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&uploadIDCounter, 1))
	case BusinessSearch:
		counterStr = fmt.Sprintf("%010d", atomic.AddUint32(&searchIDCounter, 1))
	}
	return now.Format("20060102") + string(business) + o.channel + processID + counterStr
}

// 人脸识别相关ID生成方法

// NewFaceID 生成人脸业务ID
func NewFaceID() string {
	return GenerateId(BusinessFace)
}

// NewUploadID 生成上传批次ID
func NewUploadID() string {
	return GenerateId(BusinessUpload)
}

// NewSearchID 生成搜索批次ID
func NewSearchID() string {
	return GenerateId(BusinessSearch)
}

// NewTencentPersonID 生成腾讯云PersonID（使用UUID确保全局唯一）
func NewTencentPersonID() string {
	return uuid.New().String()
}

func CreatePrimaryId(prefix string) string {
	now := time.Now().UTC()
	counterStr := fmt.Sprintf("%010d", atomic.AddUint32(&searchIDCounter, 1))
	return prefix + now.Format("20060102") + processID + counterStr
}
