package express

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// Client 快递100客户端
type Client struct {
	config     *Config
	httpClient *http.Client
}

// NewClient 创建快递100客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = DefaultConfig()
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// QueryRequest 快递查询请求参数 - 与官方demo保持一致
type QueryRequest struct {
	Num      string `json:"num"`                // 快递单号
	Com      string `json:"com"`                // 快递公司编号
	Phone    string `json:"phone,omitempty"`    // 收件人或寄件人手机号（顺丰必填）
	From     string `json:"from,omitempty"`     // 出发地城市
	To       string `json:"to,omitempty"`       // 目的地城市
	ResultV2 string `json:"resultv2,omitempty"` // 开启行政区域解析：1=基础，4=高级（推荐），8=预测
	Show     string `json:"show,omitempty"`     // 返回格式：0=json（默认），1=xml，2=html，3=text
	Order    string `json:"order,omitempty"`    // 返回结果排序：desc=降序（默认），asc=升序
	Lang     string `json:"lang,omitempty"`     // 返回结果语言：zh=中文，en=英文
}

// QueryResponse 快递查询响应 - 根据官方文档完善字段
type QueryResponse struct {
	Message        string               `json:"message"`        // 消息体
	Nu             string               `json:"nu"`             // 单号
	IsCheck        string               `json:"ischeck"`        // 是否签收标记：0未签收，1已签收
	Condition      string               `json:"condition"`      // 快递单明细状态标记
	Com            string               `json:"com"`            // 快递公司编码
	Status         string               `json:"status"`         // 通讯状态：200成功，其他失败
	State          string               `json:"state"`          // 快递单当前状态：0在途，1揽收，2疑难，3签收，4退签，5派件，6退回，7转投，8清关，14拒签
	Data           []ExpressData        `json:"data"`           // 物流信息（倒序）
	RouteInfo      RouteInfo            `json:"routeInfo"`      // 路由信息
	IsLoop         bool                 `json:"isLoop"`         // 是否循环查询
	ArrivalTime    string               `json:"arrivalTime"`    // 预计到达时间（resultv2=8时返回）
	TotalTime      string               `json:"totalTime"`      // 平均耗时（resultv2=8时返回）
	RemainTime     string               `json:"remainTime"`     // 到达还需时间（resultv2=8时返回）
	PredictedRoute []PredictedRouteNode `json:"predictedRoute"` // 物流节点数据（resultv2=8时返回）

	// 错误时的响应字段
	Result     bool   `json:"result"`     // 查询结果
	ReturnCode string `json:"returnCode"` // 错误码
}

// ExpressData 物流信息 - 根据官方文档完善字段
type ExpressData struct {
	Time       string `json:"time"`       // 时间（原始格式）
	Ftime      string `json:"ftime"`      // 格式化时间
	Context    string `json:"context"`    // 物流信息描述
	Location   string `json:"location"`   // 当前地点（resultv2=4时返回）
	Status     string `json:"status"`     // 物流状态名称（resultv2=1或4时返回）
	StatusCode string `json:"statusCode"` // 高级物流状态值（resultv2=4时返回）
	AreaCode   string `json:"areaCode"`   // 行政区域编码（resultv2=1或4时返回）
	AreaName   string `json:"areaName"`   // 行政区域名称（resultv2=1或4时返回）
	AreaCenter string `json:"areaCenter"` // 行政区域经纬度（resultv2=4时返回）
	AreaPinYin string `json:"areaPinYin"` // 行政区域拼音（resultv2=4时返回）
}

// RouteInfo 路由信息 - 根据官方文档调整
type RouteInfo struct {
	From RouteNode `json:"from"` // 出发地
	Cur  RouteNode `json:"cur"`  // 当前地点
	To   RouteNode `json:"to"`   // 目的地
}

// RouteNode 路由节点信息
type RouteNode struct {
	Number string `json:"number"` // 区域编号
	Name   string `json:"name"`   // 区域名称
}

// PredictedRouteNode 预测路由节点（resultv2=8时返回）
type PredictedRouteNode struct {
	ArriveTime string `json:"arriveTime"` // 到达节点时间
	LeaveTime  string `json:"leaveTime"`  // 离开节点时间
	Province   string `json:"province"`   // 节点所在省
	City       string `json:"city"`       // 节点所在市
	District   string `json:"district"`   // 节点所在区
	Name       string `json:"name"`       // 节点名称
	State      string `json:"state"`      // 当前节点状态：已经过节点、当前停留节点、预估途径节点
	Type       string `json:"type"`       // 当前节点类型：转运中心、网点
}

// Query 查询快递信息
func (c *Client) Query(ctx context.Context, req *QueryRequest) (*QueryResponse, error) {
	// 构建参数对象，使用与官方demo一致的字段名
	param := map[string]interface{}{
		"com": req.Com,
		"num": req.Num,
	}

	// 可选参数
	if req.Phone != "" {
		param["phone"] = req.Phone
	}
	if req.From != "" {
		param["from"] = req.From
	}
	if req.To != "" {
		param["to"] = req.To
	}
	if req.ResultV2 != "" {
		param["resultv2"] = req.ResultV2
	} else {
		// 默认开启高级行政区域解析
		param["resultv2"] = "4"
	}
	if req.Show != "" {
		param["show"] = req.Show
	} else {
		param["show"] = "0" // 默认json格式
	}
	if req.Order != "" {
		param["order"] = req.Order
	} else {
		param["order"] = "desc" // 默认降序
	}
	if req.Lang != "" {
		param["lang"] = req.Lang
	} else {
		param["lang"] = "zh" // 默认中文
	}

	// 序列化参数
	paramStr, err := json.Marshal(param)
	if err != nil {
		log.Errorf("快递查询参数序列化失败: %v", err)
		return nil, err
	}

	// 按照官方文档的顺序生成签名: param + key + customer
	sign := c.generateSign(string(paramStr))

	// 构建form表单数据
	formData := url.Values{}
	formData.Add("customer", c.config.Customer)
	formData.Add("sign", sign)
	formData.Add("param", string(paramStr))

	// 添加调试信息
	log.Context(ctx).Infof("快递查询请求URL: %s", c.config.BaseURL+"/poll/query.do")
	log.Context(ctx).Infof("请求参数: customer=%s, param=%s, sign=%s", c.config.Customer, string(paramStr), sign)

	// 发送HTTP请求
	resp, err := c.httpClient.PostForm(c.config.BaseURL+"/poll/query.do", formData)
	if err != nil {
		log.Errorf("快递查询请求失败: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Context(ctx).Errorf("读取快递查询响应失败: %v", err)
		return nil, err
	}

	log.Context(ctx).Infof("快递查询响应: %s", string(body))

	// 解析响应
	var result QueryResponse
	if err := json.Unmarshal(body, &result); err != nil {
		log.Errorf("解析快递查询响应失败: %v, body: %s", err, string(body))
		return nil, err
	}

	return &result, nil
}

// generateSign 生成签名 - 按照官方文档: param + key + customer 顺序MD5加密并转大写
func (c *Client) generateSign(param string) string {
	signStr := param + c.config.Key + c.config.Customer
	hash := md5.Sum([]byte(signStr))
	return strings.ToUpper(fmt.Sprintf("%x", hash))
}

// GetCompanyCode 根据快递公司名称获取编码
func GetCompanyCode(companyName string) string {
	companyMap := map[string]string{
		"顺丰速运":  "shunfeng",
		"申通快递":  "shentong",
		"圆通速递":  "yuantong",
		"中通快递":  "zhongtong",
		"韵达速递":  "yunda",
		"百世快递":  "baishihy",
		"京东快递":  "jd",
		"邮政EMS": "ems",
		"德邦快递":  "debangwuliu",
		"天天快递":  "tiantian",
		"宅急送":   "zhaijisong",
		"全峰快递":  "quanfengkuaidi",
		"国通快递":  "guotongkuaidi",
		"优速快递":  "youshuwuliu",
		"中国邮政":  "youzhengguonei",
		"菜鸟网络":  "cainiao",
		"速尔快递":  "suer",
		"安能快递":  "annengwuliu",
		"EMS":   "ems",
		"国际快递":  "ems",
		"国际EMS": "ems",
		"EMS国际": "ems",
	}

	if code, exists := companyMap[companyName]; exists {
		return code
	}

	// 如果没有找到，默认使用EMS
	return "ems"
}
