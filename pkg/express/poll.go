package express

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"io"
	"net/url"
	"strings"
)

// PollParameters 订阅参数
type PollParameters struct {
	Callbackurl        string `json:"callbackurl"`                  // 回调接口地址
	Salt               string `json:"salt,omitempty"`               // 签名用随机字符串
	Phone              string `json:"phone,omitempty"`              // 收件人或寄件人手机号
	Resultv2           string `json:"resultv2,omitempty"`           // 开启行政区域解析和状态增强
	AutoCom            string `json:"autoCom,omitempty"`            // 智能判断快递公司
	InterCom           string `json:"interCom,omitempty"`           // 开启国际版
	DepartureCountry   string `json:"departureCountry,omitempty"`   // 出发国家编码
	DepartureCom       string `json:"departureCom,omitempty"`       // 出发快递公司编码
	DestinationCountry string `json:"destinationCountry,omitempty"` // 目的国家编码
	DestinationCom     string `json:"destinationCom,omitempty"`     // 目的快递公司编码
}

// PollRequest 订阅请求参数 - 根据官方文档
type PollRequest struct {
	Company    string         `json:"company"`    // 快递公司编码（小写）
	Number     string         `json:"number"`     // 快递单号
	From       string         `json:"from"`       // 出发地
	To         string         `json:"to"`         // 目的地
	Key        string         `json:"key"`        // 授权码
	Parameters PollParameters `json:"parameters"` // 附加参数
}

// PollResponse 订阅响应
type PollResponse struct {
	Result     bool   `json:"result"`     // true表示成功，false表示失败
	ReturnCode string `json:"returnCode"` // 返回代码
	Message    string `json:"message"`    // 返回消息
}

// CallbackData 推送回调数据结构
type CallbackData struct {
	Status     string      `json:"status"`     // 监控状态：polling/shutdown/abort/updateall
	BillStatus string      `json:"billstatus"` // 包裹状态（已弃用）
	Message    string      `json:"message"`    // 监控状态相关消息
	AutoCheck  string      `json:"autoCheck"`  // 快递公司编码是否出错
	ComOld     string      `json:"comOld"`     // 原始快递公司编码
	ComNew     string      `json:"comNew"`     // 纠正后快递公司编码
	LastResult *LastResult `json:"lastResult"` // 最新查询结果
	DestResult *DestResult `json:"destResult"` // 目的国查询结果（国际版）

	// 预测相关字段（resultv2=8时返回）
	ArrivalTime    string          `json:"arrivalTime"`    // 预计到达时间
	TotalTime      string          `json:"totalTime"`      // 平均耗时
	RemainTime     string          `json:"remainTime"`     // 剩余时间
	PredictedRoute []PredictedNode `json:"predictedRoute"` // 预估路径节点
}

// LastResult 最新查询结果
type LastResult struct {
	Message   string      `json:"message"`   // 消息体
	State     string      `json:"state"`     // 快递单当前状态
	Status    string      `json:"status"`    // 通讯状态
	Condition string      `json:"condition"` // 快递单明细状态标记
	IsCheck   string      `json:"ischeck"`   // 是否签收标记
	Com       string      `json:"com"`       // 快递公司编码
	Nu        string      `json:"nu"`        // 单号
	Data      []TrackData `json:"data"`      // 物流轨迹数据
	RouteInfo *RouteInfo  `json:"routeInfo"` // 路由信息
	IsLoop    bool        `json:"isLoop"`    // 是否环路
}

// DestResult 目的国查询结果（国际版）
type DestResult struct {
	Message   string      `json:"message"`   // 消息体
	State     string      `json:"state"`     // 快递单当前状态
	Status    string      `json:"status"`    // 通讯状态
	Condition string      `json:"condition"` // 快递单明细状态标记
	IsCheck   string      `json:"ischeck"`   // 是否签收标记
	Com       string      `json:"com"`       // 快递公司编码
	Nu        string      `json:"nu"`        // 单号
	Data      []TrackData `json:"data"`      // 物流轨迹数据
}

// TrackData 物流轨迹数据
type TrackData struct {
	Context    string `json:"context"`              // 物流描述
	Time       string `json:"time"`                 // 时间（原始格式）
	FTime      string `json:"ftime"`                // 格式化时间
	Status     string `json:"status,omitempty"`     // 物流状态名称
	StatusCode string `json:"statusCode,omitempty"` // 高级物流状态值
	AreaCode   string `json:"areaCode,omitempty"`   // 行政区域编码
	AreaName   string `json:"areaName,omitempty"`   // 行政区域名称
	AreaCenter string `json:"areaCenter,omitempty"` // 行政区域经纬度
	Location   string `json:"location,omitempty"`   // 快件当前位置
	AreaPinYin string `json:"areaPinYin,omitempty"` // 行政区域拼音
}

// PredictedNode 预估路径节点
type PredictedNode struct {
	ArriveTime string `json:"arriveTime"` // 到达时间
	LeaveTime  string `json:"leaveTime"`  // 离开时间
	Province   string `json:"province"`   // 省份
	City       string `json:"city"`       // 城市
	District   string `json:"district"`   // 区县
	Name       string `json:"name"`       // 节点名称
	State      string `json:"state"`      // 节点状态
	Type       string `json:"type"`       // 节点类型
}

// CallbackRequest 推送回调请求结构
type CallbackRequest struct {
	Sign  string `form:"sign"`  // 签名
	Param string `form:"param"` // JSON参数
}

// CallbackResponse 推送回调响应结构
type CallbackResponse struct {
	Result     bool   `json:"result"`     // true表示成功，false表示失败
	ReturnCode string `json:"returnCode"` // 200表示成功，其他为错误码
	Message    string `json:"message"`    // 响应消息
}

// SubscribeExpress 订阅快递推送
func (c *Client) SubscribeExpress(ctx context.Context, req *PollRequest) (*PollResponse, error) {
	// 验证必填参数
	if req.Company == "" {
		return nil, fmt.Errorf("快递公司编码不能为空")
	}
	if req.Number == "" {
		return nil, fmt.Errorf("快递单号不能为空")
	}
	if req.Key == "" {
		req.Key = c.config.Key
	}
	if req.Parameters.Callbackurl == "" {
		return nil, fmt.Errorf("回调地址不能为空")
	}

	// 将参数转换为JSON字符串
	paramBytes, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("参数序列化失败: %w", err)
	}
	paramStr := string(paramBytes)

	// 构建请求参数
	data := url.Values{}
	data.Set("schema", "json")
	data.Set("param", paramStr)

	log.Context(ctx).Infof("SubscribeExpress data: %v", data)
	// 发送请求
	pollURL := c.config.BaseURL + "/poll"
	resp, err := c.httpClient.PostForm(pollURL, data)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	var pollResp PollResponse
	if err := json.Unmarshal(body, &pollResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &pollResp, nil
}

// ValidateCallback 验证推送回调的签名
func (c *Client) ValidateCallback(req *CallbackRequest) (*CallbackData, error) {
	if req.Param == "" {
		return nil, fmt.Errorf("ValidateCallback 回调参数不能为空")
	}

	// 解析回调数据
	var callbackData CallbackData
	if err := json.Unmarshal([]byte(req.Param), &callbackData); err != nil {
		return nil, fmt.Errorf("ValidateCallback 解析回调数据失败: %w", err)
	}

	// 如果提供了签名，则验证签名

	// 快递100推送回调签名算法：md5(param + key + customer + secret)
	// 参考: https://api.kuaidi100.com/document/5f0ffa8f2977d50a94e1023c
	signString := req.Param + c.config.Secret
	hash := md5.Sum([]byte(signString))
	expectedSign := strings.ToUpper(fmt.Sprintf("%x", hash))

	if req.Sign != expectedSign {
		return nil, fmt.Errorf("签名验证失败: 期望 %s, 实际 %s", expectedSign, req.Sign)
	}

	return &callbackData, nil
}

// ExpressStatusCode 快递状态码常量
type ExpressStatusCode struct {
	// 基础状态
	InTransit  string // 0 - 在途
	Collected  string // 1 - 揽收
	Difficult  string // 2 - 疑难
	Signed     string // 3 - 签收
	Returned   string // 4 - 退签
	Delivering string // 5 - 派件
	Rejected   string // 14 - 拒签
	Customs    string // 8 - 清关

	// 高级状态示例
	OrderPlaced       string // 101 - 已下单
	WaitingCollection string // 102 - 待揽收
	Collected103      string // 103 - 已揽收
	ArrivedCity       string // 1001 - 到达派件城市
	MainLine          string // 1002 - 干线
	Transfer          string // 1003 - 转递
	DeliveredToLocker string // 501 - 投柜或驿站
	SelfSigned        string // 301 - 本人签收
	ProxySigned       string // 303 - 代签
	LockerSigned      string // 304 - 投柜或站签收
}

// NewExpressStatusCode 获取快递状态码常量
func NewExpressStatusCode() *ExpressStatusCode {
	return &ExpressStatusCode{
		InTransit:         "0",
		Collected:         "1",
		Difficult:         "2",
		Signed:            "3",
		Returned:          "4",
		Delivering:        "5",
		Rejected:          "14",
		Customs:           "8",
		OrderPlaced:       "101",
		WaitingCollection: "102",
		Collected103:      "103",
		ArrivedCity:       "1001",
		MainLine:          "1002",
		Transfer:          "1003",
		DeliveredToLocker: "501",
		SelfSigned:        "301",
		ProxySigned:       "303",
		LockerSigned:      "304",
	}
}

// GetStatusDescription 获取状态描述
func GetStatusDescription(statusCode string) string {
	statusMap := map[string]string{
		"0":    "在途",
		"1":    "揽收",
		"2":    "疑难",
		"3":    "签收",
		"4":    "退签",
		"5":    "派件",
		"6":    "退回",
		"7":    "转投",
		"8":    "清关",
		"10":   "待清关",
		"11":   "清关中",
		"12":   "已清关",
		"13":   "清关异常",
		"14":   "拒签",
		"101":  "已下单",
		"102":  "待揽收",
		"103":  "已揽收",
		"1001": "到达派件城市",
		"1002": "干线",
		"1003": "转递",
		"301":  "本人签收",
		"302":  "派件异常后签收",
		"303":  "代签",
		"304":  "投柜或站签收",
		"401":  "已销单",
		"501":  "投柜或驿站",
		"201":  "超时未签收",
		"202":  "超时未更新",
		"203":  "拒收",
		"204":  "派件异常",
		"205":  "柜或驿站超时未取",
		"206":  "无法联系",
		"207":  "超区",
		"208":  "滞留",
		"209":  "破损",
		"210":  "销单",
	}

	if desc, ok := statusMap[statusCode]; ok {
		return desc
	}
	return "未知状态"
}
