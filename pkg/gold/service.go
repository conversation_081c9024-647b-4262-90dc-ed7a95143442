package gold

import (
	"context"
	"fmt"
)

// Distribute 金币发放
func (c *Client) Distribute(ctx context.Context, req *DistributeRequest) (*DistributeReply, error) {
	resp := &DistributeReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/api/UserPoints/signin_dotasks/give", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UserBalance 用户余额
func (c *Client) UserBalance(ctx context.Context, req *UserBalanceRequest) (*UserBalanceReply, error) {
	var resp UserBalanceReply
	_, err := c.client.R().
		SetHeaders(headerFromContext(ctx)).
		SetResult(&resp).
		Get(fmt.Sprintf("%s/api/UserPoints/balance?UserId=%s", c.BaseURL, req.UserId))
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// Cost 金币消费
func (c *Client) Cost(ctx context.Context, req *CostRequest) (*CostReply, error) {
	resp := &CostReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/api/UserPoints/shopping/cost", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// Rollback 消费回滚
func (c *Client) Rollback(ctx context.Context, req *RollbackRequest) (*RollbackReply, error) {
	resp := &RollbackReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/api/UserPoints/shopping/rollback", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ===================== 用户积分 =====================

// PointsDistribute 积分发放
func (c *Client) PointsDistribute(ctx context.Context, req *PointsDistributeRequest) (*PointsDistributeReply, error) {
	resp := &PointsDistributeReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/api/UserPoints/signin_dotasks/give", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UserPoints 获取用户积分
func (c *Client) UserPoints(ctx context.Context, req *UserPointsRequest) (*UserPointsReply, error) {
	var resp UserPointsReply
	_, err := c.client.R().
		SetHeaders(headerFromContext(ctx)).
		SetResult(&resp).
		Get(fmt.Sprintf("%s/api/UserPoints/total?UserId=%s", c.BaseURL, req.UserId))
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// PointsCost 积分消费
func (c *Client) PointsCost(ctx context.Context, req *PointsCostRequest) (*PointsCostReply, error) {
	resp := &PointsCostReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/api/UserPoints/shopping/cost", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// PointsRollback 消费回滚
func (c *Client) PointsRollback(ctx context.Context, req *PointsRollbackRequest) (*PointsRollbackReply, error) {
	resp := &PointsRollbackReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/api/UserPoints/shopping/rollback", c.BaseURL))
	if err != nil {
		return nil, err
	}
	return resp, nil
}
