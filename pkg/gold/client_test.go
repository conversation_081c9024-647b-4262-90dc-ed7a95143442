package gold

import (
	"context"
	"os"
	"testing"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/stretchr/testify/assert"
)

var (
	cli *Client
	ctx context.Context
)

func TestMain(m *testing.M) {
	var err error
	cli, err = NewClient("http://fxeyegold.fxeyeinterface.com")
	if err != nil {
		panic(err)
	}
	ctx = context.Background()
	ctx = icontext.WithBasicData(ctx, "1,31,3,250,0,c105f1288fe88ab3,0")
	os.Exit(m.Run())
}

func TestClient_UserBalance(t *testing.T) {
	ctx = icontext.WithUserId(ctx, "4154576401")
	res, err := cli.UserPoints(ctx, &UserPointsRequest{
		UserId: "4154576401",
	})
	assert.Nil(t, err)
	assert.Equal(t, 201, res.Data.Points)
}

func TestClient_Distribute(t *testing.T) {
	res, err := cli.PointsDistribute(ctx, &PointsDistributeRequest{
		UserId:        "3004325208",
		ProductName:   "测试",
		EnumOrderType: 8,
		Points:        100000000,
		Remark:        "测试",
		OrderNo:       "10102001000",
		ProductImgUrl: "https://www.baidu.com",
	})
	assert.Nil(t, err)
	assert.Equal(t, true, res.Data)
}

func TestClient_Cost(t *testing.T) {
	res, err := cli.PointsCost(ctx, &PointsCostRequest{
		UserId:               "3004325208",
		ProductName:          "测试商品",
		Points:               100,
		Remark:               "remark",
		OrderNo:              "11000",
		ProductImgUrl:        "http://www.baidu.com",
		TranslateProductName: "",
	})
	assert.Nil(t, err)
	assert.NotEqual(t, "", res.Data)
}

func TestClient_Rollback(t *testing.T) {
	res, err := cli.PointsRollback(ctx, &PointsRollbackRequest{
		Points:      100,
		OperationId: "6458dfc9-7407-45bc-829c-ebd93b9ecaeb",
	})
	assert.Nil(t, err)
	assert.NotEqual(t, "", res.Data)
}
