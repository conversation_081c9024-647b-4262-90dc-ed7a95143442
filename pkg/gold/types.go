package gold

import "fmt"

type CheckResponse interface {
	Check() error
}

type CommonResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	ErrorCode int    `json:"errorCode"`
	IsSuccess bool   `json:"isSuccess"`
}

func (s CommonResponse) Check() error {
	if !s.IsSuccess {
		return fmt.Errorf(s.Message)
	}
	return nil
}

type DistributeRequest struct {
	UserId               string `json:"userId"`               // 用户Id
	ProductName          string `json:"productName"`          // 产品名（如：签到，做任务）
	EnumOrderType        int    `json:"enumOrderType"`        // 签到：3 做任务：4
	Points               int    `json:"points"`               // 金币
	Remark               string `json:"remark"`               // 备注
	OrderNo              string `json:"orderNo,omitempty"`    // 订单号
	ProductImgUrl        string `json:"productImgUrl"`        // 产品图片地址
	TranslateProductName string `json:"translateProductName"` //多言语的标题描述
}

type DistributeReply struct {
	CommonResponse
	Data bool `json:"data"`
}

type CostOrderType int

// EnumOrderType
const (
	EnumOrderTypeSign CostOrderType = 7 // 签到
	EnumOrderTypeTask CostOrderType = 8 // 做任务
	EnumOrderTypeCost CostOrderType = 9 // 消费
)

type CostRequest struct {
	UserId               string        `json:"userId"`
	ProductName          string        `json:"productName"`
	EnumOrderType        CostOrderType `json:"enumOrderType"`
	Points               int           `json:"points"`
	Remark               string        `json:"remark"`
	OrderNo              string        `json:"orderNo"`
	ProductImgUrl        string        `json:"ProductImgUrl"`
	TranslateProductName string        `json:"TranslateProductName"`
}

type CostReply struct {
	CommonResponse
	Data struct {
		ID string `json:"id"`
	} `json:"data"`
}

type UserBalanceRequest struct {
	UserId string `json:"userId"`
}

type UserBalanceReply struct {
	CommonResponse
	Data struct {
		Balance int `json:"balanceGold"`
	} `json:"data"`
}

type RollbackRequest struct {
	OperationId          string `json:"id"`
	Points               int    `json:"points"`
	TranslateProductName string `json:"TranslateProductName"`
}

type RollbackReply struct {
	CommonResponse
	Data struct {
		Balance int `json:"balanceGold"`
	} `json:"data"`
}

// ================================== 积分 ==================================
type PointsDistributeRequest struct {
	UserId               string `json:"userId"`               // 用户Id
	ProductName          string `json:"productName"`          // 产品名（如：签到，做任务）
	EnumOrderType        int    `json:"enumOrderType"`        // 7代表签到，8代表做任务
	Points               int    `json:"Points"`               // 积分
	Remark               string `json:"remark"`               // 备注
	OrderNo              string `json:"orderNo,omitempty"`    // 订单号
	ProductImgUrl        string `json:"productImgUrl"`        // 产品图片地址
	TranslateProductName string `json:"translateProductName"` //多言语的标题描述
}

type PointsDistributeReply struct {
	CommonResponse
	Data bool `json:"data"`
}

type UserPointsRequest struct {
	UserId string `json:"userId"`
}

type UserPointsReply struct {
	CommonResponse
	Data struct {
		Points int `json:"totalPoints"`
	} `json:"data"`
}

type PointsCostRequest struct {
	UserId               string        `json:"userId"`
	ProductName          string        `json:"productName"`
	EnumOrderType        CostOrderType `json:"enumOrderType"`
	Points               int           `json:"Points"`
	Remark               string        `json:"remark"`
	OrderNo              string        `json:"orderNo"`
	ProductImgUrl        string        `json:"ProductImgUrl"`
	TranslateProductName string        `json:"TranslateProductName"`
}

type PointsCostReply struct {
	CommonResponse
	Data struct {
		ID string `json:"id"`
	} `json:"data"`
}

type PointsRollbackRequest struct {
	OperationId          string `json:"id"`
	Points               int    `json:"Points"`
	TranslateProductName string `json:"TranslateProductName"`
}

type PointsRollbackReply struct {
	CommonResponse
	Data struct {
		Balance int `json:"Points"`
	} `json:"data"`
}
