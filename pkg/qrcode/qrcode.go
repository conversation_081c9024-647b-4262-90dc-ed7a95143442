package qrcode

import (
	"bytes"
	"image"
	"image/draw"
	"image/png"
	"os"

	"github.com/skip2/go-qrcode"
)

var (
	logoFile  *os.File
	logoImage image.Image
)

func init() {
	logoPath := os.Getenv("LOGO_FILE_PATH")
	if logoPath != "" {
		var err error
		logoFile, err = os.Open(logoPath)
		if err != nil {
			panic(err)
		}

		logoImage, _, err = image.Decode(logoFile)
		if err != nil {
			panic(err)
		}
	}

}

func GenerateQRCodeWithLogo(content string, level qrcode.RecoveryLevel, size int) ([]byte, error) {
	code, err := qrcode.New(content, level)
	if err != nil {
		return nil, err
	}

	var (
		qrcodeImg = code.Image(size)
		outImg    = image.NewRGBA(qrcodeImg.Bounds())
	)

	// logo 和二维码拼接
	draw.Draw(outImg, outImg.Bounds(), qrcodeImg, image.Pt(0, 0), draw.Over)
	offset := image.Pt((outImg.Bounds().Max.X-logoImage.Bounds().Max.X)/2, (outImg.Bounds().Max.Y-logoImage.Bounds().Max.Y)/2)
	draw.Draw(outImg, outImg.Bounds().Add(offset), logoImage, image.Pt(0, 0), draw.Over)
	buf := bytes.NewBuffer(nil)
	err = png.Encode(buf, outImg)
	return buf.Bytes(), err
}
