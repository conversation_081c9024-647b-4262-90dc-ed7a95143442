package rabbit

import (
	"github.com/streadway/amqp"
)

type Config struct {
	URL          string `json:"url"`
	Exchange     string `json:"exchange"`
	ExchangeKind string `json:"exchange_kind"`
	Queue        string `json:"queue"`
	RoutingKey   string `json:"routing_key"`
	BindingKey   string `json:"binding_key"`
	New          bool   `json:"new"`
}

type Rabbit struct {
	conn         *amqp.Connection
	channel      *amqp.Channel
	exchangeName string
	queueName    string
	routingKey   string
	bindingKey   string
}

func NewRabbit(c *Config) (*Rabbit, error) {
	conn, err := amqp.Dial(c.URL)
	if err != nil {
		return nil, err
	}

	channel, err := conn.Channel()
	if err != nil {
		return nil, err
	}

	if c.New {
		// 定义队列
		_, err = channel.QueueDeclare(c.Queue, true, false, false, false, nil)
		if err != nil {
			return nil, err
		}

		// 定义交换机
		err = channel.ExchangeDeclare(c.Exchange, c.<PERSON>Kind, true, false, false, false, nil)
		if err != nil {
			return nil, err
		}

		// 绑定
		err = channel.QueueBind(c.Queue, c.Binding<PERSON>ey, c.Exchange, true, nil)
		if err != nil {
			return nil, err
		}
	}

	return &Rabbit{
		conn:         conn,
		channel:      channel,
		exchangeName: c.Exchange,
		queueName:    c.Queue,
		routingKey:   c.RoutingKey,
		bindingKey:   c.BindingKey,
	}, nil
}

func (p *Rabbit) Publish(body []byte) error {
	return p.channel.Publish(p.exchangeName, p.routingKey, false, false, amqp.Publishing{
		Body: body,
	})
}

func (p *Rabbit) Consume(handler func(msg amqp.Delivery)) error {
	channel, err := p.channel.Consume(p.queueName, "", true, false, false, true, nil)
	if err != nil {
		return err
	}

	go func() {
		for message := range channel {
			handler(message)
		}
	}()
	return nil
}

func (p *Rabbit) Close() {
	_ = p.channel.Close()
	_ = p.conn.Close()
}
