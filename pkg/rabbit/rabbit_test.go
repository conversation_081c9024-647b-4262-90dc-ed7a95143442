package rabbit

import (
	"fmt"
	"testing"

	"github.com/streadway/amqp"
	"github.com/stretchr/testify/assert"
)

func TestNewRabbitPublish(t *testing.T) {
	shRabbit, err := NewRabbit(&Config{
		URL:          "amqp://fxeye:<EMAIL>:5672/netwon",
		Exchange:     "web_log_exchange",
		ExchangeKind: "topic",
		Queue:        "web_log",
		R<PERSON><PERSON><PERSON><PERSON>:   "web_log.messagesync",
		BindingKey:   "web_log.messagesync",
	})
	assert.Nil(t, err)
	defer shRabbit.Close()

	// 上海收
	err = shRabbit.Consume(func(msg amqp.Delivery) {
		fmt.Println(string(msg.Body))
	})
	assert.Nil(t, err)
	select {}
}
