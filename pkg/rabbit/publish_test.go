package rabbit

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewRabbitPublish2(t *testing.T) {
	sgRabbit, err := NewRabbit(&Config{
		URL:          "amqp://fxeye:<EMAIL>:5672/netwon",
		Exchange:     "web_log_exchange",
		ExchangeKind: "topic",
		Queue:        "web_log",
		Routing<PERSON>ey:   "web_log.messagesync",
		Binding<PERSON>ey:   "web_log.messagesync",
	})
	assert.Nil(t, err)
	defer sgRabbit.Close()

	// 新加坡发
	for i := 0; i < 100; i++ {
		err = sgRabbit.Publish([]byte(fmt.Sprintf("内容%v", i)))
		assert.Nil(t, err)
	}
}
