package emailsender

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Config 邮件发送配置
type Config struct {
	BaseURL    string `json:"base_url"`    // 邮件服务基础URL
	TimeoutSec int    `json:"timeout_sec"` // 请求超时时间(秒)
}

// EmailSender 邮件发送器
type EmailSender struct {
	config *Config
	client *http.Client
}

// New 创建邮件发送器
func New(config *Config) *EmailSender {
	timeout := time.Duration(config.TimeoutSec) * time.Second
	if timeout == 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	return &EmailSender{
		config: config,
		client: &http.Client{
			Timeout: timeout,
		},
	}
}

// SendEmailRequest 发送邮件请求参数
type SendEmailRequest struct {
	UserId       string `form:"userId"`       // 用户邮箱
	LanguageCode string `form:"languageCode"` // 语言代码
	MailBody     string `form:"mailBody"`     // 邮件内容(HTML)
	MailSubject  string `form:"mailSubject"`  // 邮件主题
	FromEmail    string `form:"fromEmail"`    // 发件人邮箱
}

// SendEmailResponse 发送邮件响应
type SendEmailResponse struct {
	Result    bool   `json:"result"`    // 是否成功
	Succeed   bool   `json:"succeed"`   // 是否成功
	Message   string `json:"message"`   // 响应消息
	RequestId string `json:"requestId"` // 请求ID
}

// Send 发送邮件
func (e *EmailSender) Send(ctx context.Context, req *SendEmailRequest) (*SendEmailResponse, error) {
	// 构建请求URL
	sendURL := fmt.Sprintf("%s/email/send", strings.TrimRight(e.config.BaseURL, "/"))

	// 构建form数据
	formData := url.Values{}
	formData.Set("userId", req.FromEmail)
	formData.Set("languageCode", req.LanguageCode)
	formData.Set("mailBody", req.MailBody)
	formData.Set("mailSubject", req.MailSubject)

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", sendURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	httpReq.Header.Set("Accept", "application/x-www-form-urlencoded")

	// 发送请求
	resp, err := e.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("邮件服务返回错误状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var emailResp SendEmailResponse
	if err := json.NewDecoder(resp.Body).Decode(&emailResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	return &emailResp, nil
}

// SendEmailTemplate 邮件模板参数
type SendEmailTemplate struct {
	UserId       string // 用户ID
	UserEmail    string // 用户邮箱
	LanguageCode string // 语言代码
	Subject      string // 邮件主题
	HTMLContent  string // HTML邮件内容
}

// SendTemplateEmail 发送模板邮件
func (e *EmailSender) SendTemplateEmail(ctx context.Context, template *SendEmailTemplate) (*SendEmailResponse, error) {
	req := &SendEmailRequest{
		UserId:       template.UserId,
		LanguageCode: template.LanguageCode,
		MailBody:     template.HTMLContent,
		MailSubject:  template.Subject,
		FromEmail:    template.UserEmail,
	}

	return e.Send(ctx, req)
}
