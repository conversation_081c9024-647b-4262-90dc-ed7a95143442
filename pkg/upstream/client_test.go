package upstream

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/stretchr/testify/assert"
)

var (
	cli *Client
	ctx context.Context
)

func TestMain(m *testing.M) {
	var err error
	cli, err = NewClient(&Config{
		TraderDomain:         "http://wikibitsearch.fxeyeinterface.com",
		ContestDomain:        "http://tradeContest.fxeyeinterface.com",
		WikienterpriseDomain: "http://wikienterprise.fxeyeinterface.com",
	})
	if err != nil {
		panic(err)
	}
	ctx = context.Background()
	ctx = icontext.WithBasicData(ctx, "1,31,3,250,0,c105f1288fe88ab3,0")
	os.Exit(m.Run())
}

func TestClient_GetBroker(t *testing.T) {
	res, err := cli.GetTraders(ctx, &GetTraderRequest{
		Codes: []string{"**********"},
	})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}

func TestClient_HasRealAccount(t *testing.T) {
	res, err := cli.HasRealAccount(ctx, &UserRealAccountRequest{UserId: "**********"})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}

func TestClient_IsUnAuthenticatedService(t *testing.T) {
	res, err := cli.IsUnAuthenticatedService(ctx, &UserStatusRequest{UserId: "**********"})
	assert.Nil(t, err)
	str, _ := json.Marshal(res)
	println(string(str))
}
