package upstream

import (
	"context"
	"crypto/md5"
	"fmt"
)

const (
	TransToken  = "2mNpJU4tND"
	TransPrefix = "Wikiglobal"
	TransSuffix = "Translation"
)

func (c *Client) GetTraders(ctx context.Context, req *GetTraderRequest) (*GetTraderReply, error) {
	resp := &GetTraderReply{}
	_, err := c.client.R().
		SetBody(req).
		SetResult(resp).
		SetHeaders(headerFromContext(ctx)).
		Post(fmt.Sprintf("%s/wikicore/getMultiple/v2", c.conf.TraderDomain))
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *Client) HasRealAccount(ctx context.Context, req *UserRealAccountRequest) (bool, error) {
	resp := &UserRealAccountReply{}
	url := fmt.Sprintf("%s/saas/accountList", c.conf.ContestDomain)

	_, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetBody(req).
		SetResult(resp).
		Post(url)
	if err != nil {
		return false, err
	}

	if len(resp.Data.AccountList) > 0 {
		return true, nil
	} else {
		return false, nil
	}
}

func (c *Client) IsUnAuthenticatedService(ctx context.Context, req *UserStatusRequest) (bool, error) {
	resp := &UserStatusReply{}
	url := fmt.Sprintf("%s/api/staff/statussimpleinfo?userId=%s", c.conf.WikienterpriseDomain, req.UserId)

	_, err := c.client.R().
		SetHeader("Content-Type", "application/json").
		SetResult(resp).
		Get(url)
	if err != nil {
		return false, err
	}

	if resp.Succeed && resp.Result.IdentityType == 2 && resp.Result.IsAuthenticated == false {
		return true, nil
	} else {
		return false, nil
	}
}

// GeetTestVerify 极验
func (c *Client) GeetTestVerify(ctx context.Context, lot_number, captcha_output, pass_token, gen_time string) (bool, error) {
	resp := &GeetTestReply{}
	var domain = fmt.Sprintf("%s/geettest/validate4", c.conf.ThirdDomain)
	_, err := c.client.R().
		SetResult(resp).
		Get(domain + "?lot_number=" + lot_number + "&captcha_output=" + captcha_output + "&pass_token=" + pass_token + "&gen_time=" + gen_time)

	if err != nil {

		return false, err
	}
	return resp.Result, nil
}

func (c *Client) Translate(content, languageCode string) (TransOut, error) {
	if len(content) == 0 {
		return TransOut{
			Data:     "",
			Language: "",
		}, nil
	}
	var out ResultBase[TransOut]
	_, err := c.client.R().SetBody(map[string]string{
		"content": content,
		"target":  languageCode,
		"token":   TransToken,
		"md5":     fmt.Sprintf("%x", md5.Sum([]byte(TransPrefix+content+TransSuffix))),
	}).SetResult(&out).Post(c.conf.GoogleTransDomain)
	if err != nil {
		return TransOut{}, err
	}
	return out.Result, nil
}
