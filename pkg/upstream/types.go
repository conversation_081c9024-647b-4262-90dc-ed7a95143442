package upstream

import "fmt"

type CheckResponse interface {
	Check() error
}

type CommonResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	ErrorCode int    `json:"errorCode"`
	IsSuccess bool   `json:"isSuccess"`
}

func (s CommonResponse) Check() error {
	if !s.IsSuccess {
		return fmt.Errorf("[%s]%d", s.Message, s.Code)
	}
	return nil
}

type GetTraderRequest struct {
	Codes       []string `json:"codes"`
	Country     string   `json:"country"`
	HasDisabled bool     `json:"hasDisabled"`
	Language    string   `json:"language"`
}

type TraderInfo struct {
	Code       string  `json:"code"`
	Score      float64 `json:"score"`
	TextScore  string  `json:"text_score"`
	ScoreLevel float32 `json:"scoreLevel"`
	Color      string  `json:"color"`
	Annotation string  `json:"annotation"`
	Labels     []struct {
		Type int `json:"type"`
		Data []struct {
			LabelName string `json:"labelName"`
		} `json:"Data"`
	} `json:"labels"`
	SpecialtyLabels []struct {
		LabelType       int    `json:"labelType"`
		Icon            string `json:"icon"`
		Image           string `json:"image"`
		Content         string `json:"content"`
		BackgroundColor string `json:"backgroundColor"`
		FontColor       string `json:"fontColor"`
		ImageWidth      int    `json:"imageWidth"`
		ImageHeight     int    `json:"imageHeight"`
	} `json:"specialtyLabels"`
	Flag                 string      `json:"flag"`
	Logo                 string      `json:"logo"`
	Ico                  string      `json:"ico"`
	Hico                 string      `json:"hico"`
	Project              int         `json:"project"`
	Type                 int         `json:"type"`
	Regnumber            interface{} `json:"regnumber"`
	LocalShortName       string      `json:"localShortName"`
	LocalFullName        string      `json:"localFullName"`
	EnglishShortName     string      `json:"englishShortName"`
	EnglishFullName      string      `json:"englishFullName"`
	ShowName             string      `json:"showName"`
	EnterpriseName       string      `json:"enterpriseName"`
	IsVr                 bool        `json:"isVr"`
	AgentMember          int         `json:"agentMember"`
	AgentLabel           interface{} `json:"agentLabel"`
	AgentBadge           interface{} `json:"agentBadge"`
	AgentTraderInfo      interface{} `json:"agentTraderInfo"`
	IsEpc                bool        `json:"isEpc"`
	UltimateType         int         `json:"ultimateType"`
	UrlName              string      `json:"urlName"`
	Seal                 string      `json:"seal"`
	CreatedDate          string      `json:"createdDate"`
	IsFake               bool        `json:"isFake"`
	Kbscore              string      `json:"kbscore"`
	ServiceProviderLabel interface{} `json:"serviceProviderLabel"`
	IsWhite              bool        `json:"isWhite"`
	PropTraderFeeLabel   string      `json:"propTraderFeeLabel"`
	Icon                 string      `json:"icon"`
	ForbidExpiredAt      string      `json:"forbidExpiredAt"`
	RegisterCountry      string      `json:"registerCountry"`
	Status               int         `json:"status"`
	Dimension            []struct {
		Code     string  `json:"code"`
		Category string  `json:"category"`
		Score    float64 `json:"score"`
	} `json:"dimension"`
	Investment []struct {
		Type    int    `json:"type"`
		Contact string `json:"contact"`
	} `json:"investment"`
	Category struct {
		Color string `json:"Color"`
		Name  string `json:"Name"`
	} `json:"category"`
	StaffCount       int           `json:"staffCount"`
	CooperationCount int           `json:"cooperationCount"`
	Website          []string      `json:"website"`
	AccountWebsite   []interface{} `json:"accountWebsite"`
}

type GetTraderReply struct {
	Result    []*TraderInfo `json:"result"`
	Succeed   bool          `json:"succeed"`
	Message   string        `json:"message"`
	RequestId string        `json:"requestId"`
}

func (s *GetTraderReply) Check() error {
	if !s.Succeed {
		return fmt.Errorf("%s[%s]", s.Message, s.RequestId)
	}
	return nil
}

type UserRealAccountRequest struct {
	UserId string `json:"user_id"`
}

type AccountInfo struct {
	Mt4Account   string `json:"mtaccount"`
	TradeAccount string `json:"AccountID"`
	BrokerId     string `json:"brokerId"`
	Server       string `json:"server"`
	Status       int    `json:"status"`
}

type DataResult struct {
	AccountList []AccountInfo `json:"accountList"`
}

type UserRealAccountReply struct {
	ErrorCode int        `json:"ErrorCode"`
	Data      DataResult `json:"Data"`
	RequestId string     `json:"requestId"`
}

type GeetTestReply struct {
	Result  bool `json:"result"`
	Succeed bool `json:"succeed"`
}

type UserStatusRequest struct {
	UserId string `json:"user_id"`
}

type UserStatus struct {
	UserId          string `json:"userId"`
	Level           int    `json:"level"`
	Code            string `json:"code"`
	IdentityType    int    `json:"identityType"`
	IsSelected      bool   `json:"isSelected"`
	IsAuthenticated bool   `json:"isAuthenticated"`
}

type UserStatusReply struct {
	Code    int        `json:"code"`
	Result  UserStatus `json:"result"`
	Succeed bool       `json:"succeed"`
	Message string     `json:"message"`
}

type ResultBase[T any] struct {
	Code    int    `json:"code"`
	Result  T      `json:"result"`
	Succeed bool   `json:"succeed"`
	Message string `json:"message"`
}
type TransOut struct {
	Data     string `json:"data"`
	Language string `json:"language"`
}
