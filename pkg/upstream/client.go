package upstream

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/airunny/wiki-go-tools/icontext"
	"github.com/go-resty/resty/v2"
)

type Config struct {
	TraderDomain         string `json:"trader_domain"`
	ContestDomain        string `json:"contest_domain"`
	ThirdDomain          string `json:"third_domain"`
	WikienterpriseDomain string `json:"wikienterprise_domain"`
	GoogleTransDomain    string `json:"google_trans_domain"`
}

type Client struct {
	client *resty.Client
	conf   *Config
}

func NewClient(c *Config) (*Client, error) {
	if c == nil {
		return nil, fmt.Errorf("empty virtual_goods")
	}

	return &Client{
		client: resty.New().
			SetHeader("Connection", "keep-alive").
			SetTransport(&http.Transport{
				MaxIdleConnsPerHost: 10,               // 每个主机的最大空闲连接数
				IdleConnTimeout:     10 * time.Minute, // 空闲连接超时时间
			}).
			SetTimeout(time.Minute * 1).
			OnBeforeRequest(BeforeRequest).
			OnAfterResponse(AfterResponse),
		conf: c,
	}, nil
}

func BeforeRequest(_ *resty.Client, request *resty.Request) error {
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Accept", "application/json")
	return nil
}

func AfterResponse(_ *resty.Client, response *resty.Response) error {
	println("原始数据：", string(response.Body()))
	if response.StatusCode() != http.StatusOK {
		return fmt.Errorf("response code is %d", response.StatusCode())
	}

	ret := response.Result()
	if check, ok := ret.(CheckResponse); ok {
		return check.Check()
	}
	return nil
}

func headerFromContext(ctx context.Context) map[string]string {
	var (
		basicData, _    = icontext.BasicDataFrom(ctx)
		clientIP, _     = icontext.ClientIPFrom(ctx)
		countryCode, _  = icontext.CountryCodeFrom(ctx)
		languageCode, _ = icontext.LanguageCodeFrom(ctx)
		requestId, _    = icontext.RequestIdFrom(ctx)
	)
	return map[string]string{
		"BasicData":       basicData,
		"X-Forwarded-For": clientIP,
		"CountryCode":     countryCode,
		"LanguageCode":    languageCode,
		"X-Request-Id":    requestId,
	}
}
