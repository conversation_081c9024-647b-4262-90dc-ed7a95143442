package message_parser

import (
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
)

// 提取出来的CDC消息解析模块
// 用于处理数据库变更捕获(CDC)类型的消息，通常用于解析Kafka中的数据变更事件

// ParseCDCMessage 解析CDC格式的消息并提取关键字段
// 参数:
//   - messageValue: 原始消息内容
//   - fields: 需要提取的字段名称列表，例如["PostsId", "Status"]
//
// 返回:
//   - map[string]interface{}: 包含所有提取字段的映射
//   - map[string]interface{}: 包含旧值的映射(如果有)
//   - error: 解析过程中的错误，如果没有错误则为nil
func ParseCDCMessage(messageValue []byte, fields []string) (map[string]interface{}, map[string]interface{}, error) {
	// 尝试直接解析为CDC数据结构
	var cdcData map[string]interface{}
	if err := json.Unmarshal(messageValue, &cdcData); err != nil {
		// 如果直接解析失败，尝试解析嵌套结构
		return parseNestedMessage(messageValue, fields)
	}

	// 检查是否有data字段，判断是否为CDC数据
	data, ok := cdcData["data"].(map[string]interface{})
	if !ok {
		// 如果没有data字段，则尝试解析嵌套结构
		return parseNestedMessage(messageValue, fields)
	}

	// 提取指定的字段
	result := make(map[string]interface{})
	for _, field := range fields {
		if val, exists := data[field]; exists {
			result[field] = val
		}
	}

	// 提取old部分(可能不存在)
	var oldData map[string]interface{}
	if old, ok := cdcData["old"].(map[string]interface{}); ok {
		oldData = make(map[string]interface{})
		for _, field := range fields {
			if val, exists := old[field]; exists {
				oldData[field] = val
			}
		}
	}

	return result, oldData, nil
}

func ParseFlatMessage(messageValue []byte, fields []string) (map[string]interface{}, error) {
	// 直接解析为 map
	var data map[string]interface{}
	if err := json.Unmarshal(messageValue, &data); err != nil {
		return nil, err
	}

	// 提取指定字段
	result := make(map[string]interface{})
	for _, field := range fields {
		if val, exists := data[field]; exists {
			result[field] = val
		}
	}

	return result, nil
}

// parseNestedMessage 解析嵌套在msg字段中的CDC消息
// 用于处理嵌套在日志格式中的CDC消息
func parseNestedMessage(messageValue []byte, fields []string) (map[string]interface{}, map[string]interface{}, error) {
	var msg map[string]interface{}
	if err := json.Unmarshal(messageValue, &msg); err != nil {
		log.Errorf("Failed to unmarshal message: %v", err)
		return nil, nil, err
	}

	// 提取msg字段中的实际消息内容
	msgContent, ok := msg["msg"].(string)
	if !ok {
		log.Warnf("Message doesn't contain 'msg' field or it's not a string")
		return nil, nil, nil
	}

	// 解析msg字段中的CDC数据
	var cdcData map[string]interface{}
	if err := json.Unmarshal([]byte(msgContent), &cdcData); err != nil {
		log.Errorf("Failed to unmarshal CDC data from msg field: %v", err)
		return nil, nil, err
	}

	// 提取data部分
	data, ok := cdcData["data"].(map[string]interface{})
	if !ok {
		log.Warnf("CDC message doesn't contain 'data' field or it's not an object")
		return nil, nil, nil
	}

	// 提取old部分(可能不存在)
	var oldData map[string]interface{}
	if old, ok := cdcData["old"].(map[string]interface{}); ok {
		oldData = make(map[string]interface{})
		for _, field := range fields {
			if val, exists := old[field]; exists {
				oldData[field] = val
			}
		}
	}

	// 提取指定的字段
	result := make(map[string]interface{})
	for _, field := range fields {
		if val, exists := data[field]; exists {
			result[field] = val
		}
	}

	return result, oldData, nil
}

// GetIntField 安全地获取int类型字段值
func GetIntField(data map[string]interface{}, field string) (int, bool) {
	if val, exists := data[field]; exists {
		switch v := val.(type) {
		case float64:
			// JSON解析通常会将数字解析为float64
			return int(v), true
		case int:
			return v, true
		case int64:
			return int(v), true
		case json.Number:
			// 处理json.Number类型
			if i, err := v.Int64(); err == nil {
				return int(i), true
			}
		case string:
			// 尝试将字符串转换为整数
			var i int
			_, err := fmt.Sscanf(v, "%d", &i)
			if err == nil {
				return i, true
			}
		}
	}
	return 0, false
}

// GetStringField 安全地获取string类型字段值
func GetStringField(data map[string]interface{}, field string) (string, bool) {
	if val, exists := data[field]; exists {
		switch v := val.(type) {
		case string:
			return v, true
		case json.Number, float64, int, int64, bool:
			// 将其他基本类型转换为字符串
			return fmt.Sprintf("%v", v), true
		}
	}
	return "", false
}

// GetFloat64Field 安全地获取float64类型字段值
func GetFloat64Field(data map[string]interface{}, field string) (float64, bool) {
	if val, exists := data[field]; exists {
		switch v := val.(type) {
		case float64:
			return v, true
		case int:
			return float64(v), true
		case int64:
			return float64(v), true
		case json.Number:
			if f, err := v.Float64(); err == nil {
				return f, true
			}
		case string:
			// 尝试将字符串转换为浮点数
			var f float64
			_, err := fmt.Sscanf(v, "%f", &f)
			if err == nil {
				return f, true
			}
		}
	}
	return 0, false
}

// GetBoolField 安全地获取bool类型字段值
func GetBoolField(data map[string]interface{}, field string) (bool, bool) {
	if val, exists := data[field]; exists {
		switch v := val.(type) {
		case bool:
			return v, true
		case string:
			// 尝试将字符串转换为布尔值
			if v == "true" || v == "True" || v == "TRUE" || v == "1" {
				return true, true
			} else if v == "false" || v == "False" || v == "FALSE" || v == "0" {
				return false, true
			}
		case float64, int, int64:
			// 将数字转换为布尔值（0为false，非0为true）
			numVal, _ := GetFloat64Field(data, field)
			return numVal != 0, true
		}
	}
	return false, false
}
