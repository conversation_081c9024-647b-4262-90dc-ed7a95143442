// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"gold_store/api/common"
	"gold_store/internal/conf"
	"gold_store/internal/dao"
	"gold_store/internal/server"
	"gold_store/internal/service"
)

import (
	_ "github.com/airunny/wiki-go-tools/env"
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(serverConfig *common.ServerConfig, data *conf.Data, business *conf.Business, logger log.Logger) (*kratos.App, func(), error) {
	client, cleanup, err := dao.NewRedis(data, logger)
	if err != nil {
		return nil, nil, err
	}
	db, cleanup2, err := dao.NewMySQL(data, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	goods := dao.NewGoods(db)
	goodsStatistics := dao.NewGoodsStatistics(db)
	goodsSnapshot := dao.NewGoodsSnapshot(db)
	order := dao.NewOrder(db)
	orderItem := dao.NewOrderItem(db)
	payment := dao.NewPayment(db)
	sku := dao.NewSKU(db)
	address := dao.NewAddress(db)
	staticDB, cleanup3, err := dao.NewStaticDB(data, logger)
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	country := dao.NewCountry(staticDB)
	countryTranslate := dao.NewCountryTranslate(staticDB)
	logistics := dao.NewLogistics(db)
	logisticsDetail := dao.NewLogisticsDetail(db)
	expressQuery := dao.NewExpressQuery(db)
	signRecord := dao.NewSignRecord(db, logger)
	signConfig := dao.NewSignTaskConfig(db, logger)
	signRewardLog := dao.NewSignRewardLog(db, logger)
	taskConfig := dao.NewTaskConfig(db)
	taskProgress := dao.NewTaskProgress(db)
	taskRewardIssue := dao.NewRewardIssue(db)
	userGiftCard := dao.NewUserGiftCard(db)
	giftCardUserRemind := dao.NewGiftCardUserRemind(db)
	giftCardSetting := dao.NewGiftCardSetting(db)
	eaDB, cleanup4, err := dao.NewEaDB(data, logger)
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	ea := dao.NewEA(eaDB)
	vpsDB, cleanup5, err := dao.NewVpsDB(data, logger)
	if err != nil {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	vps := dao.NewVPS(vpsDB)
	vipDB, cleanup6, err := dao.NewVipDB(data, logger)
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	vip := dao.NewVIP(vipDB)
	exhibitionDB, cleanup7, err := dao.NewExhibitionDB(data, logger)
	if err != nil {
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	exhibition := dao.NewExhibition(exhibitionDB)
	serviceService, cleanup8 := service.NewService(business, client, goods, goodsStatistics, goodsSnapshot, order, orderItem, payment, sku, address, country, countryTranslate, logistics, logisticsDetail, expressQuery, signRecord, signConfig, signRewardLog, taskConfig, taskProgress, taskRewardIssue, userGiftCard, giftCardUserRemind, giftCardSetting, ea, vps, vip, exhibition)
	grpcServer := server.NewGRPCServer(serverConfig, serviceService, logger)
	httpServer := server.NewHTTPServer(serverConfig, serviceService, logger)
	app := newApp(logger, grpcServer, httpServer)
	return app, func() {
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
