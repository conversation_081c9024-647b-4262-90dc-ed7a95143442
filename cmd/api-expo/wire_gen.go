// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"api-expo/api/common"
	"api-expo/internal/conf"
	"api-expo/internal/dao"
	"api-expo/internal/server"
	"api-expo/internal/service"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "github.com/airunny/wiki-go-tools/env"
	_ "github.com/airunny/wiki-go-tools/geo"
	_ "github.com/airunny/wiki-go-tools/language"
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(serverConfig *common.ServerConfig, dataConfig *common.DataConfig, business *conf.Business, logger log.Logger) (*kratos.App, func(), error) {
	client, cleanup, err := dao.NewRedis(dataConfig, logger)
	if err != nil {
		return nil, nil, err
	}
	db, cleanup2, err := dao.NewMySQL(dataConfig, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	expo := dao.NewExpo(db)
	expoCommunity := dao.NewExpoCommunity(db)
	expoExhibitor := dao.NewExpoExhibitor(db)
	expoExhibitorEmployee := dao.NewExpoExhibitorEmployee(db)
	expoExhibitorApply := dao.NewExpoExhibitorApply(db)
	expoGuest := dao.NewExpoGuest(db)
	expoGuide := dao.NewExpoGuide(db)
	expoHall := dao.NewExpoHall(db)
	expoImage := dao.NewExpoImage(db)
	expoLive := dao.NewExpoLive(db)
	expoPartner := dao.NewExpoPartner(db)
	expoReview := dao.NewExpoReview(db)
	expoSchedule := dao.NewExpoSchedule(db)
	expoScheduleGuest := dao.NewExpoScheduleGuest(db)
	expoScheduleReserve := dao.NewExpoScheduleReserve(db)
	guest := dao.NewGuest(db)
	participant := dao.NewParticipant(db)
	faceGroup := dao.NewFaceGroup(db)
	facePhotoRelation := dao.NewFacePhotoRelation(db)
	faceGroupExpo := dao.NewFaceGroupExpo(db)
	comment := dao.NewComment(db)
	commentAt := dao.NewCommentAt(db)
	commentImage := dao.NewCommentImage(db)
	commentLike := dao.NewCommentLike(db)
	commentTrans := dao.NewCommentTrans(db)
	config := dao.NewConfig(db)
	serviceService := service.NewGreeterService(business, client, expo, expoCommunity, expoExhibitor, expoExhibitorEmployee, expoExhibitorApply, expoGuest, expoGuide, expoHall, expoImage, expoLive, expoPartner, expoReview, expoSchedule, expoScheduleGuest, expoScheduleReserve, guest, participant, faceGroup, facePhotoRelation, faceGroupExpo, comment, commentAt, commentImage, commentLike, commentTrans, config)
	grpcServer := server.NewGRPCServer(serverConfig, serviceService, logger)
	httpServer := server.NewHTTPServer(serverConfig, serviceService, logger)
	app := newApp(logger, grpcServer, httpServer)
	return app, func() {
		cleanup2()
		cleanup()
	}, nil
}
