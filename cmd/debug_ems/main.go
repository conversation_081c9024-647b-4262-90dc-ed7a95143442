package main

import (
	"encoding/json"
	"fmt"
	"log"

	"gold_store/pkg/ems"
)

func main() {
	// 从你提供的日志中提取的加密数据
	encryptedData := "|$4|r/Xs4Q148tFXjYVJlqiP2CEkV+LCRIf8bF7Po5OGb1MHqHKcjJNP+vjmpqbnODbsIxJL/w4noDQy0gew1jqqDtvI2mG7Ar/E2zWPxogvkc528yh+x7A68kFCyfIMtEP99NUTgsMkmGQn7LxAgZRixw=="
	
	// 配置中的密钥
	secretKey := "U2ZPSGY5VU4weVI2NUtOUw=="
	
	// 创建SM4加密工具
	crypto, err := ems.NewSM4CryptoTool(secretKey)
	if err != nil {
		log.Fatalf("创建SM4加密工具失败: %v", err)
	}
	
	// 解密数据
	decrypted, err := crypto.Decrypt(encryptedData)
	if err != nil {
		log.Fatalf("解密失败: %v", err)
	}
	
	fmt.Printf("解密后的完整数据: %s\n", decrypted)
	
	// 尝试移除末尾的密钥字符串
	if len(decrypted) > len(secretKey) && decrypted[len(decrypted)-len(secretKey):] == secretKey {
		businessData := decrypted[:len(decrypted)-len(secretKey)]
		fmt.Printf("业务数据: %s\n", businessData)
		
		// 尝试解析为JSON
		var jsonData interface{}
		if err := json.Unmarshal([]byte(businessData), &jsonData); err == nil {
			prettyJSON, _ := json.MarshalIndent(jsonData, "", "  ")
			fmt.Printf("格式化的业务数据:\n%s\n", string(prettyJSON))
		}
	} else {
		fmt.Printf("未找到密钥字符串后缀，原始解密数据: %s\n", decrypted)
	}
	
	// 测试重新加密是否一致
	testData := `{"language":"zh","waybillNos":["9808148684586"],"trackingNos":[]}`
	encrypted, err := crypto.SignData(testData, secretKey)
	if err != nil {
		log.Printf("重新加密失败: %v", err)
	} else {
		fmt.Printf("测试数据重新加密结果: %s\n", encrypted)
		fmt.Printf("是否与原始加密数据一致: %t\n", encrypted == encryptedData)
	}
}
