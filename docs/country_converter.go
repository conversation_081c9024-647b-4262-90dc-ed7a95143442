package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
)

// 输入JSON的结构 - 使用map[string]interface{}来处理动态结构
type InputCountryData map[string][]InputCountry

type InputCountry struct {
	CountryCode string `json:"countryCode"`
	TwoCharCode string `json:"twoCharCode"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Flag        string `json:"flag"`
	Currency    string `json:"currency,omitempty"`
	Symbol      string `json:"symbol,omitempty"`
}

// 输出结构体定义
type Country struct {
	TwoCharCode   string                   `json:"two_char_code"`
	ThreeCharCode string                   `json:"three_char_code"`
	CountryCode   string                   `json:"country_code"`
	FlagURL       string                   `json:"flag_url"`
	NameMap       map[LangCode]CountryName `json:"name_map"`
}

type LangCode string

type CountryName string

const (
	LangCodeEn LangCode = "en"
	LangCodeZh LangCode = "zh"
)

func main() {
	// 读取输入JSON文件
	inputFile := "country.json"
	data, err := ioutil.ReadFile(inputFile)
	if err != nil {
		log.Fatalf("读取文件失败: %v", err)
	}

	// 解析输入JSON
	var inputData InputCountryData
	if err := json.Unmarshal(data, &inputData); err != nil {
		log.Fatalf("解析JSON失败: %v", err)
	}

	// 创建国家映射，用于合并英文和中文数据
	countryMap := make(map[string]*Country)

	// 处理英文数据
	for _, enCountry := range inputData.En {
		country := &Country{
			TwoCharCode:   enCountry.TwoCharCode,
			ThreeCharCode: "", // 输入数据中没有三字符代码，留空
			CountryCode:   enCountry.CountryCode,
			FlagURL:       enCountry.Flag,
			NameMap:       make(map[LangCode]CountryName),
		}
		country.NameMap[LangCodeEn] = CountryName(enCountry.Name)
		countryMap[enCountry.TwoCharCode] = country
	}

	// 处理中文数据，添加到对应的国家记录中
	for _, zhCountry := range inputData.Zh {
		if country, exists := countryMap[zhCountry.TwoCharCode]; exists {
			country.NameMap[LangCodeZh] = CountryName(zhCountry.Name)
		} else {
			// 如果英文数据中没有对应记录，创建新记录
			country := &Country{
				TwoCharCode:   zhCountry.TwoCharCode,
				ThreeCharCode: "",
				CountryCode:   zhCountry.CountryCode,
				FlagURL:       zhCountry.Flag,
				NameMap:       make(map[LangCode]CountryName),
			}
			country.NameMap[LangCodeZh] = CountryName(zhCountry.Name)
			countryMap[zhCountry.TwoCharCode] = country
		}
	}

	// 转换为切片
	var countries []Country
	for _, country := range countryMap {
		countries = append(countries, *country)
	}

	// 生成输出JSON
	outputData, err := json.MarshalIndent(countries, "", "  ")
	if err != nil {
		log.Fatalf("生成JSON失败: %v", err)
	}

	// 写入输出文件
	outputFile := "country_converted.json"
	if err := ioutil.WriteFile(outputFile, outputData, 0644); err != nil {
		log.Fatalf("写入文件失败: %v", err)
	}

	fmt.Printf("转换完成！输出文件: %s\n", outputFile)
	fmt.Printf("共转换了 %d 个国家记录\n", len(countries))
}
